#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#

#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#

#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#

#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
Li3001 = Start Date Cannot Be [{}]
LI3002 = Main account product type cannot be empty
Li3003 = Please Enter The Correct Amount [{}]
Li3004 = Missing Required Input Parameter [{}]
Li3005 = Product [{}] Event [{}] Interest Calculation Type [{}] Does Not Have A Corresponding Interest Rate Type
Li3006 = Period Type [{}] Does Not Exist!
LI3007 = account primary key internalKey must not be empty
LI3008 = Operation type operator cannot be empty
LI3009 = [{}] is an unknown operation type
LI3010 = Interest type intClass must not be empty
Li3011 = Interest Rate Effective Date Is blank
Li3012 = The Interest Rate Information To Be Added Already Exists
LI3013 = Interest rate information does not exist
LI3014 = Interest rate type modification is not supported yet
Li3015 = Wrong Amount Format!
LI3016 = The account key value [{}] corresponding to the account split information does not exist!
Li3017 = Start Time [{}] Is Greater Than End Time [{}]
Li3018 = The Interest Rate Type Corresponding To Product [{}] And Event Type [{}] Is Not Configured!
Li3019 = Effective Date Must Not Be Less Than Transaction Date
Li3020 = [{}] Rule Configuration Information Does Not Exist
Li3021 = Rule File Generation Exception
Li3022 = There Is No Next Interest Rate Change Period
Li3023 = Please Check The Product Type [{}] And Interest Rate Activation Method [{}]!
Li3024 = No Data In Segmented Interest Rate Table!
Li3025 = When The Interest Rate Activation Method Is [{}], The Interest Rate Activation Method Is R, And The Interest Rate Change Period Must Be Entered!
Li3026 = There Is No Interest Settlement Information In The Account Yet
Li3027 = The Product Information Corresponding To Product [{}] Does Not Exist!
Li3029 = Exchange Rate Information Does Not Exist!
Li3030 = Currency Pair Price Information Does Not Exist!
Li3031 = Exchange Rate Type [{}] Does Not Exist!
Li3032 = Unknown Banknote Type!
Li3055 = Amount Expression Does Not Exist!
Li3067 = Time Period Mismatch!
Li3068 = Amount Type Mismatch!
Li3083 = Customer Information Does Not Exist!
Li3084 = [{}] When The Interest Rate Change Method Is S, Irlpreacctint Must Be Sent Or Irlacctint Must Exist
Li3101 = The Interest Rate Information Corresponding To Interest Rate Type [{}] Does Not Exist!
Li3102 = No Corresponding Equivalent Amount
Li3103 = The Segment Information Corresponding To The Account Key Value [{}] And Interest Calculation Type [{}] Does Not Exist
LI3104 = Account key value [{}] and interest accrual type [{}] correspond to account details that do not exist!
LI3105 = The interest information for product [{}] and event [{}] does not exist.
Li3106 = The Offset Amount Array Cannot Be blank!
Li3107 = The Reversal End Date [{}] Cannot Be Greater Than The Current Accounting Day [{}]!
Li3108 = Interest Rate Floating Constraint Type Is blank!
Li3109 = Period Type [{}] Or Period Value [{}] Cannot Be blank!
Li3110 = The Exchange Rate Information Corresponding To Source Currency [{}] Target Currency [{}] Exchange Rate Type [{}] Branch [{}] Effective Date [{}] Effective Time [{}] Does Not Exist!
Li3111 = Rule Type [{}] Product Group [{}] Product Id [{}] Cannot Be blank!
LI3112 = The exchange rate information corresponding to currency [{}] does not exist!
Li3114 = Deadbank Number Does Not Exist!
Li3121 = Base Rate Mismatch!
LI3123 = The effective date cannot exceed the account validity period!
LI3124 = Non-agreement deposits are only allowed to maintain contract interest rates
Li3175 = Account Key Value Does Not Match Account Number
Li3176 = Please Check The Start Date [{}] And End Date [{}]
Li3177 = Year Base Days Is blank
Li3178 = Default Branch Is blank!
Li3179 = The Internal Flat Exchange Rate Type For Foreign Exchange Settlement And Sales Is blank!
Li3180 = Interest Rate Activation Method Does Not Exist!
Li3181 = The Buying Currency Amount And Selling Currency Amount Cannot Be blank At The Same Time!
Li3182 = Only One Of The Buying Currency Amount And Selling Currency Amount Can Be Entered!
Li3183 = Fee Type [{}] Does Not Configure Fee Ladder Information
Li3184 = Exchange Rate Conversion Error.
Li3185 = Charge Type [{}] Is Not Configured
Li3186 = Customer Number Does Not Exist[{}]
LI3187 = Trading institution does not exist [{}]
LI3188 = The account opening institution does not exist [{}]
Li3189 = Product Interest Rate Information Corresponding To Product [{}] Event [{}] Interest Rate Type [{}] Does Not Exist!
Li3190 = Start Date [{}] Cannot Be Greater Than End Date [{}]!
LI3191 = Error in calculating [{}] fee, account identifier is [{}]
LI3192 = The account key [{}] is not a TD account!
LI3193 = The account key [{}] has been closed!
LI3194 = Provision adjustment cannot be made on the account opening date!
LI3195 = An error occurred when updating the next processing day of the fee package, package code [{}]!
LI3196 = Error updating fee package status, package code [{}]!
I3197 = Product [{}] Does Not Exist!
Li3198 = Monthly Basis Cannot Be blank!
Li3199 = Interest Calculation Method Cannot Be blank!
Li3200 = Start Date Cannot Be blank!
Li3201 = End Date Cannot Be blank!
Li3202 = Please Submit The Base Number Of Basis Days:Year!
Li3203 = The Corresponding Exchange Rate Information Does Not Exist!
Li3204 = Remote Procedure Call Exception, Please Check The Accounting System
LI3205 = Missing interest rate segment data!
Li3206 = Wrong Calculation Of Accrual Days!
Li3207 = The Exchange Rate Type [{}] Is The Currency Pair Exchange Rate, And The Buying Currency And Selling Currency Cannot Be The Same Currency!
LI3208 = System ID must not be empty
Li3209 = The Current System Does Not Configure The Previous Working Day For The Current Date [{}]!
Li3210 = The Current System Does Not Configure The Next Working Day For The Current Date [{}]!
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
Li4007 = Account Key Cannot Be blank!
Li4008 = The Interest Rate Ladder Corresponding To The Branch [{}] Currency [{}] Interest Rate Type [{}] Does Not Exist!
Li4010 = The Interest Rate Ladder Corresponding To The Branch [{}] Currency [{}] Interest Rate Type [{}] Effective Time [{}] Does Not Exist!
Li4009 = The Basic Interest Rate Information Corresponding To Branch [{}] Currency [{}] Interest Rate Type [{}] Does Not Exist!
#LI4009 = 利率基础信息不存在！
Li4016 = Month Basis Day Is Wrong, Please Check The Input Period Value!
LI4017 = The number of interest accrual days is empty or the value is incorrect!
Li4018 = The Account Key Value Does Not Match The Account Number!
Li4019 = Period Frequency Does Not Exist!
Li4020 = Effective Date Cannot Be Earlier Than Transaction Date
Li4021 = [{}] The Interest Calculation Method Is Ab Or Bs And The Recalculation Method Is Not Allowed To Be Configured As H Or I.
Li4022 = No Data In Segmented Rate Table
Li4023 = Account Primary Key Cannot Be blank
Li4024 = Operation Type Must Not Be blank
Li4025 = The Operation Type Can Only Be Save, Update, Delete[{}]
Li4026 = The Interest Rate Information To Be Added Already Exists
Li4027 = Service Is Not Configured Or Is Misconfigured
Li4028 = Product Group Cannot Be blank!
Li4029 = Rule Configuration Information Does Not Exist!
Li4030 = Start Date Cannot Be blank
Li4031 = No Corresponding Product Type Information Found!
Li4032 = Product Interest Rate Information Not Configured!
LI4033 = The change information has taken effect and cannot be deleted
Li4034 = Error In Batch Processing Of Interest Accrual!
Li4035 = The Sub-Interest Rate Type Of Fortune Bao Products Cannot Be blank!
Li4036 = Time Format Conversion Error!
Li4037 = This Interface Only Applies To The Product Method And Subtraction Method!
Li4038 = Product Interest Rate Information Does Not Exist!
Li4039 = Segment History Information Was Not Obtained!
Li4040 = Batch Rate Change Error!
Li4041 = The Interest Rate Change Method Of S Model Can Only Be N!
Li4042 = There Is An Error In Synchronizing The Account Amount Information!
LI4043 = The interest rate change information [{}] did not find the corresponding household information!
Li4044 = There Is A Problem With The Configuration Of Product Parameters (Change Frequency And Change Date)!
Li4045 = Error In Batch Processing Synchronization Account Interest Accrual Type Information!
Li4046 = Neither The Account Key Nor The System Flag Can Be blank!
Li4047 = The Interest Rate Change Period And Change Date Must Be Lost!
Li4048 = Error In Batch Account Cancellation Processing!
Li4049 = Batch Account Data Synchronization Error!
Li4050 = Synchronization Error In Batch Processing Account Details!
Li4051 = Synchronization Error In Batch Processing Of Account Tiered Interest Rates!
Li4052 = Error In Batch New User Update!
Li4053 = The Rebate Cannot Be Issued Beyond The Interest Settlement Date!
Li4054 = Recovery And Reversal Cannot Cross The Interest Settlement Date!
Li4055 = The Term Manual Interest Settlement Date [{}] Cannot Exceed The Maturity Date [{}]!
Li4056 = Periodic Manual Interest Settlement Recalculation Method Can Only Be H
Li4057 = The Event Type [{}] Corresponding To The Periodic Manual Interest Settlement Is Incorrect!
Li4058 = The Sum Of Principal [{}] And Interest [{}] Cannot Be Less Than The Balance Of Prepaid Interest (Interest Settlement) [{}]!
Li4059 = [{}] Partial Early Withdrawal Cannot Be Made Using The Interest Calculation Method!
Li4060 = [{}]The Corresponding Interest Rate Information Does Not Exist!
Li4061 = [{}]The Corresponding Tax Rate Information Does Not Exist!
Li4062 = [{}]The Corresponding Account Tiered Interest Rate Does Not Exist!
Li4063 = The Interest Settlement Tiered Interest Rate Corresponding To The Key Value [{}] Interest Calculation Type [{}] Interest Settlement Date [{}] Does Not Exist
Li4064 = The Segmented Interest Rate Corresponding To The Interest Calculation Type [{}] Of The Key Value [{}] Does Not Exist
Li4065 = The Historical Segmented Interest Rate Corresponding To The Key Value [{}] Interest Calculation Type [{}] Does Not Exist
Li4066 = The Interest Settlement Information Corresponding To The Key Value [{}] And Interest Calculation Type [{}] Does Not Exist!
Li4067 = Segment Information Does Not Exist!
Li4068 = The Actual Amount [{}] Is Greater Than The Accumulated Withdrawal Amount [{}] Of The Account!
LI4069 = Fee package [{}] does not exist!
Li4070 = Product Segment Routing File Is Undefined [{}]
Li4071 = Error In Next Interest Rate Change Date!
Li4072 = Segment Type Is Undefined, Can Only Be A Or P!
LI4073 = The interest rate effective date cannot be earlier than the last change date!
Li4074 = Missing Interest Segment Rate Data!
Li4075 = Amount Not Sent!
Li4076 = No Interest Value In Segmented Data!
Li4077 = No Matching Parameters!
Li4078 = Please Check Whether To Upload Product Interest Rate Information!
Li4079 = Please Check Whether The Interest Accrual Amount Is Sent!
Li4080 = Please Check Whether The Deposit Amount Has Been Uploaded!
Li4081 = Please Check Whether The Interest Rate Effective Date Is Sent!
Li4082 = Please Check Whether To Send Product Type!
Li4083 = Please Check Whether To Send Event Type!
Li4084 = Please Check Whether To Submit The Interest Calculation Type!
LI4085 = No records returned from query!
LI4086 = End date cannot be earlier than start date!
Li4087 = The Interest Rate Segment Information For The Date Range [{}]-[{}] Does Not Exist!
Li4088 = Term Account Cannot Be Reversed Every Other Day!
LI4089 = The effective date of the interest rate already exists and cannot be changed repeatedly!
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#

#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#

#------------------------------------6------------end-----------------------------#
#------------------------------------7------------begin---------------------------#
Li7001 = Resource [{}] Is In Use, Please Try Again Later
#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#

#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#

#------------------------------------9------------end-----------------------------#