#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#

#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#

#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#

#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
LI3001 = 起始日期不能为[{}]
LI3002 = 产品类型不能为空
LI3003 = 请输入正确的金额[{}]
LI3004 = 缺少必须的入参[{}]
LI3005 = 产品[{}]事件[{}]计息类型[{}]没有对应的利率类型
LI3006 = 周期类型[{}]不存在！
LI3007 = 账户主键internalKey不得为空
LI3008 = 操作类型operator不得为空
LI3009 = [{}]为未知的操作类型
LI3010 = 利息类型intClass不得为空
LI3011 = 利率生效日为空
LI3012 = 所要添加的利率信息已存在
LI3013 = 利率信息不存在
LI3014 = 暂不支持利率类型修改
LI3015 = 金额格式错误！
LI3016 = 账户键值[{}]对应分户信息不存在！
LI3017 = 开始时间[{}]大于结束时间[{}]
LI3018 = 产品[{}]与事件类型[{}]对应的利率类型未配置！
LI3019 = 生效日期不得小于交易日期
LI3020 = [{}]规则配置信息不存在
LI3021 = 规则文件生成异常
LI3022 = 不存在下一利率变更周期
LI3023 = 请检查产品类型[{}]和利率启用方式[{}]！
LI3024 = 分段利率表无数据！
LI3025 = 利率启用方式为[{}]时，利率启用方式为R，必须输入利率变动周期！
LI3026 = 账户暂无已结息信息
LI3027 = 产品[{}]对应产品信息不存在！
LI3029 = 汇率牌价信息不存在！
LI3030 = 货币对牌价信息不存在！
LI3031 = 汇率类型[{}]不存在！
LI3032 = 未知的钞汇类型！
LI3055 = 金额表达式不存在！
LI3067 = 时间周期不匹配！
LI3068 = 金额类型不匹配！
LI3083 = 客户信息不存在！
LI3084 = [{}]利率变更方式为S时，必须上送irlPreAcctInt或存在 irlAcctInt
LI3101 = 利率类型[{}]对应利率信息不存在！
LI3102 = 没有对应的等值金额
LI3103 = 账户键值[{}]计息类型[{}]对应分段信息不存在
LI3104 = 账户键值[{}]计息类型[{}]对应分户明细信息不存在！
LI3105 = 产品[{}]事件[{}]对应产品利率信息不存在！
LI3106 = 冲正金额数组不能为空！
LI3107 = 冲正结束日期[{}]不能大于当前会计日[{}]！
LI3108 = 利率浮动约束类型为空！
LI3109 = 周期类型[{}]或周期值[{}]不能为空！
LI3110 = 源币种[{}]目标币种[{}]汇率类型[{}]机构[{}]生效日期[{}]生效时间[{}]对应汇率信息不存在！
LI3111 = 规则类型[{}]产品组[{}]产品ID[{}]不能为空！
LI3112 = 币种[{}]对应汇率牌价信息不存在！
LI3114 = 期限天数不存在！
LI3121 = 基准利率不匹配！
LI3123 = 生效日期不能超过账户有效期！
LI3124 = 非协议存款只允许维护合同利率
LI3175 = 账户键值与账户号不匹配
LI3176 = 请检查起始日期[{}]和结束日期[{}]
LI3177 = 年基准天数为空
LI3178 = 默认机构为空！
LI3179 = 结售汇内部平盘汇率类型为空！
LI3180 = 利率启用方式不存在！
LI3181 = 买入币种金额和卖出币种金额不能同时为空!
LI3182 = 买入币种金额和卖出币种金额只能输入其中一个!
LI3183 = 费用类型[{}]未配置费用阶梯信息
LI3184 = 汇率换算出现错误
LI3185 = 费用类型[{}]未配置
LI3186 = 客户号不存在[{}]
LI3187 = 交易机构不存在[{}]
LI3188 = 开户机构不存在[{}]
LI3189 = 产品[{}]事件[{}]利率类型[{}]对应产品利率信息不存在！
LI3190 = 开始日期[{}]不能大于结束日期[{}]！
LI3191 = 计算[{}]费用出错,账户标识符为[{}]
LI3192 = 账户键值[{}]账户不是定期账户！
LI3193 = 账户键值[{}]账户已经销户！
LI3194 = 开户日不能做计提调整！
LI3195 = 更新费用套餐下一处理日出错,套餐代码[{}]！
LI3196 = 更新费用套餐状态出错,套餐代码[{}]！
LI3197 = 产品[{}]不存在！
LI3198 = 月基准不能为空！
LI3199 = 计息方式不能为空！
LI3200 = 起始日期不能为空！
LI3201 = 终止日期不能为空！
LI3202 = 请上送年基准天数！
LI3203 = 对应汇率信息不存在！
LI3204 = 远程过程调用异常，请检查核算系统
LI3205 = 缺少利率分段数据!
LI3206 = 计提天数计算错误！
LI3207 = 汇率类型[{}]为货币对汇率，买入币种与卖出币种不能为同一币种！
LI3208 = 系统ID不得为空
LI3209 = 当前系统未给当前日期[{}]配置上一工作日！
LI3210 = 当前系统未给当前日期[{}]配置下一工作日！
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
LI4007 = 账户键值不得为空！
LI4008 = 机构[{}]币种[{}]利率类型[{}]对应利率阶梯不存在!
LI4010 = 机构[{}]币种[{}]利率类型[{}]生效时间[{}]对应利率阶梯不存在!
LI4009 = 机构[{}]币种[{}]利率类型[{}]对应的利率基础信息不存在！
#LI4009 = 利率基础信息不存在！
LI4016 = 月基数错误，请检查输入周期值 ！
LI4017 = 计息天数为空 或 值不正确！
LI4018 = 账户键值与账号不匹配！
LI4019 = 周期频率不存在！
LI4020 = 生效日期不得比交易日期早
LI4021 = [{}]利息计算方法为AB或BS不允许配置重算方法为H或I
LI4022 = 分段利率表中无数据
LI4023 = 账户主键不得为空
LI4024 = 操作类型不得为空
LI4025 = 操作类型只可为SAVE,UPDATE,DELETE[{}]
LI4026 = 所要添加的利率信息已存在
LI4027 = 服务未配置或配置错误
LI4028 = 产品组不能为空！
LI4029 = 规则配置信息不存在！
LI4030 = 开始日期不能为空
LI4031 = 未查询到相应的产品类型信息！
LI4032 = 未配置产品利率信息！
LI4033 = 变更信息已生效不可删除
LI4034 = 批处理计提结息出错！
LI4035 = 财富宝产品子利率类型不能为空！
LI4036 = 时间格式转化错误！
LI4037 = 该接口只适用于积数法和差减法！
LI4038 = 产品利率信息不存在！
LI4039 = 没有获取到分段历史信息！
LI4040 = 批处理利率变更出错！
LI4041 = S模型利率变更方式只能为N！
LI4042 = 同步分户金额信息出错！
LI4043 = 利率变更信息[{}]没有找的对应的分户信息！
LI4044 = 产品参数（变更频率和变更日期）配置有问题！
LI4045 = 批处理同步分户计息类型信息出错！
LI4046 = 账户键值和系统标示都不能为空！
LI4047 = 利率变更周期和变更日期必输！
LI4048 = 批处理销户处理出错！
LI4049 = 批处理分户数据同步出错！
LI4050 = 批处理分户明细同步出错！
LI4051 = 批处理分户阶梯利率同步出错！
LI4052 = 批处理新建用户更新出错！
LI4053 = 发放冲正不能跨结息日！
LI4054 = 回收冲正不能跨结息日！
LI4055 = 定期手工结息日[{}]不能超过到期日[{}]！
LI4056 = 定期手工结息重算方式只能为H
LI4057 = 定期手工结息对应事件类型[{}]不正确！
LI4058 = 本金[{}]与利息[{}]之和不能小于前付息（结息）余额[{}]！
LI4059 = [{}]计息法不能做部分提前支取！
LI4060 = [{}]对应利率信息不存在！
LI4061 = [{}]对应税率信息不存在！
LI4062 = [{}]对应分户阶梯利率不存在！
LI4063 = 键值[{}]计息类型[{}]结息日期[{}]对应结息阶梯利率不存在
LI4064 = 键值[{}]计息类型[{}]对应分段阶梯利率不存在
LI4065 = 键值[{}]计息类型[{}]对应历史分段阶梯利率不存在
LI4066 = 键值[{}]计息类型[{}]对应结息信息不存在！
LI4067 = 分段信息不存在！
LI4068 = 实际金额[{}]大于账户累计计提金额[{}]！
LI4069 = 费用套餐[{}]不存在！
LI4070 = 产品分段路由文件未定义[{}]
LI4071 = 下一个利率变更日期出错！
LI4072 = 分段类型未定义，只能为A或P！
LI4073 = 利率生效日期不能早于上一变更日！
LI4074 = 缺少利息分段利率数据！
LI4075 = 金额未上送！
LI4076 = 分段数据中没有利息值！
LI4077 = 没有匹配的参数！
LI4078 = 请检查是否上送产品利率信息！
LI4079 = 请检查是否上送计息金额！
LI4080 = 请检查是否上送靠档金额！
LI4081 = 请检查是否上送利率生效日！
LI4082 = 请检查是否上送产品类型！
LI4083 = 请检查是否上送事件类型！
LI4084 = 请检查是否上送计息类型！
LI4085 = 查询无返回记录！
LI4086 = 终止日期不能早于起始日期！
LI4087 = 日期区间[{}]-[{}]的利率分段信息不存在！
LI4088 = 定期账户不能隔日冲正！
LI4089 = 利率生效日期已存在,不能重复变更
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#

#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#

#------------------------------------6------------end-----------------------------#
#------------------------------------7------------begin---------------------------#
LI7001 = 资源[{}]正在使用中，请稍后再试
#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#

#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#

#------------------------------------9------------end-----------------------------#