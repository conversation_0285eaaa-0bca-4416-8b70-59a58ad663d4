#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#

#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
RB4343 = 账号[{}]不是活期账户！
RB7128 = 该卡号未开通无卡自助消费!
UC4056 = 限额参数[{}]信息尚未设置！
UC4057 = 交易金额大于单笔限额！
UC4058 = 无原始交易信息！
UC4059 = 银联延迟转账原纪录不存在！
UC4060 = 交易金额大于单日免密限额！
UC4063 = 匹配内部户使用的交易类型为空！






