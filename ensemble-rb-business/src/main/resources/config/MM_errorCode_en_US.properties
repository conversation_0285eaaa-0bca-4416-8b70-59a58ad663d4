#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD,MM）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#
#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
MM3001=Product type cannot be empty!
MM3002=The loading and unloading direction cannot be empty!
MM3003=Internal and external identifiers cannot be empty!
MM3004=Counterparty cannot be empty!
MM3005=The currency cannot be empty!
MM3006=Basic interest rate cannot be empty!
MM3007=The capital spread cannot be empty!
MM3008=Value date cannot be empty!
MM3009=Expiration date cannot be empty!
MM3010=Settlement information cannot be empty!
MM3011=The settlement account cannot be empty!
MM3012=The payment flag cannot be empty!
MM3013=The account of the fund transfer party cannot be empty!
MM3014=The account of the party withdrawing funds cannot be empty!
MM3015=Transaction number cannot be empty!
MM3016=Expiration date cannot be empty!
Mm3017=The Internal Serial Number Cannot Be blank!
MM3018=The payment transaction number cannot be empty!
Mm3019=The Deposit And Withdrawal Account Is Not Maintained!
Mm3020=Query Parameters Cannot Be blank!
Mm3021=Reversal Application Can Only Be Initiated By The Lending Initiating Bank!
Mm3022=The Reversal Application Can Only Be Initiated By The Lending Initiating Teller!
Mm3023= This Intra-Bank Loan Has Expired And No Cancellation Is Allowed!
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
MM4001=The value date cannot be earlier than the system date!
MM4002=The value date is a currency holiday!
MM4003=The expiration date cannot be earlier than the system date!
MM4004=Maturity date is a currency holiday!
Mm4005=The Settlement Information Can Only Be One Charge And One Payment!
MM4006=[{}] account settlement information not found!
Mm4007=The Collection And Payment Account Cannot Be The Same Account!
Mm4008=Cannot Be A Collection Or Payment Account At The Same Time!
MM4009=Incorrect removal flag!
Mm4010=The Opponent'S Default Settlement Information Does Not Exist!
MM4011=Lending information does not exist!
MM4012=Interbank loan repayment information does not exist!
MM4013=The lending and reversal information does not exist!
Mm4014=The Lending Status Is Not Posted, And Early Repayment Is Not Allowed!
Mm4015=Early Repayment Is Not Allowed After Maturity!
Mm4016=The Expiration Date Cannot Be Earlier Than The Current System Date And Cannot Be Later Than The Original Expiration Date!
MM4017=The customer number must be the internal customer number of the current operation line!
Mm4018=Interbank Lending Early Repayment Application Can Only Be Initiated By The Lending Party!
MM4019=The early repayment information does not exist or has been approved!
Mm4020=The Application For Early Repayment Of This Loan Already Exists!
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
#------------------------------------6------------end-----------------------------#
Mm6001=The Account Information Is Not Unique!
#------------------------------------7------------begin---------------------------#
MM7001=Transaction has expired!
MM7002=Transaction has been posted!
MM7003=The transaction has been reversed!
MM7004=Transaction deleted!
MM7005=The opponent's transaction has been reviewed and cannot be deleted!
MM7006=Transaction has been reviewed!
MM7007=Transaction not posted!
MM7008=Transaction not reviewed!
MM7009=Tellers can only reverse their own transactions!
#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#
#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#
#------------------------------------9------------end-----------------------------#

