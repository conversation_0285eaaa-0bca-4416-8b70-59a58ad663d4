#错误码定义6位
#前两位模块（PD,FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#
#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
PD4001=Product validity check is illegal!
PD4002=The client type is inconsistent with the product definition!
PD4003=client flag is inconsistent with product definition!
PD4004=The client's offshore flag is inconsistent with the product definition!
PD4005=Deposit term information must be specified when depositing regular products!
PD4006=Periodic deposit information is illegal!
PD4007=Joint accounts are not allowed for independent accounts!
PD4008=The repayment method is not within the value range!
PD4009=Inverted value date exceeds the maximum allowable number of days [{}] days!
PD4010=The withdrawal amount is not within the definition!
PD4011=The main account record balance sign cannot be empty!
PD4012=The main account record balance management information cannot be empty!
PD4013=The voucher type is not within the range of values!
PD4014=The automatic account cancellation flag cannot be empty!
PD4015=The account status is [{}], this transaction is not allowed!
PD4016=Inactive days are not within the scope of the product factory configuration!
PD4017=The balance is not within the scope of the product factory configuration!
PD4018=The auto-turn to sleep flag cannot be empty!
PD4019=The principal needs to be submitted for withdrawal!
PD4020=The number of transfers must be defined during automatic transfer!
PD4021=Currency [{}] and product definition currency [{}] are inconsistent!
PD4022=Partial principal transfer is not allowed during automatic transfer!
PD4023=No advance withdrawal is allowed!
PD4024=No additional principal is allowed during automatic transfer!
PD4025=Account attribute [{}] is not within the scope of product definition [{}]!
PD4026=Cross month parameter cannot be empty!
PD4027=Cross month is not allowed!
PD4028=Multi client joint account information cannot be empty!
PD4029=The account identifier in the joint name information cannot be empty!
PD4030=Client No. in joint name information cannot be empty!
PD4031=The account balance is insufficient and cannot be overdrawn!
PD4032=The withdrawal method or times in advance are not defined!
PD4033=Incorrect withdrawal password!
PD4034=Interest settlement cycle information must be defined for periodic products!
PD4035=Periodic interest settlement cycle information is illegal!
PD4036=The number of missing storage submitted cannot be greater than the number of missing storage defined by the product!
PD4037=Continuous deduction flag must be configured!
PD4038=Transaction amount [{}][{}] does not achieve the minimum deposit amount! [{}][{}]！
PD4039=The amount after deposit [{}][{}] exceeds the maximum amount set in the account [{}][{}]!
PD4040=The retained amount after withdrawal is insufficient, it can be withdrawn in full or retained no less than: [{}][{}]!
PD4041=The single transaction amount is not within the product definition range [{}][{}]!
PD4042=The agent is not an in-house client, so it is not allowed to do it!
PD4043=Non cash transactions are not allowed!
PD4044=Transfer between different clients is not allowed!
PD4045=Transfer between non related clients is not allowed!
PD4046=Transaction is not allowed for non transfer business!
PD4047=The debit account is not allowed to be redeemed!
PD4048=Universal deposit transaction is not allowed for credit account!
PD4049=This transaction type does not exist!
PD4050=Module type cannot be empty!
PD4051=Account subtype cannot be empty!
PD4052=Profit center does not exist!
PD4053=Account identifier flag cannot be empty!
PD4054=There are uncollected expenses in the account, and the account cannot be closed!
PD4055=The account has contract information that has not been cancelled. You cannot cancel the account!
PD4056=Voucher type [{}] is inconsistent with product configuration [{}]!
PD4057=Only one basic account can be opened for a company client!
PD4058=The basic account and the general account under the company client cannot be opened in the same bank!
PD4059=The [{}] product under this account can no longer open a sub account!
PD4060=The product types are inconsistent. Operation is not allowed!
PD4061=This product is not allowed to reverse value!
PD4062=The far value date exceeds the maximum number of days allowed for far value by [{}] days!
PD4063=This product does not allow remote value!
PD4064=Value interest cannot cross year!
PD4065=Voucher No. cannot be empty!
PD4066=Voucher update failed, please check the submitted voucher information!
PD4067=The amount type is inconsistent with the amount calculation configuration. Please check the configuration [{}]!
PD4068=The product parameter [{}] configuration should be within the range of [{}]!
PD4069=The number of early withdrawals has exceeded the number of product definitions. No more early withdrawals are allowed!
PD4070=You need to define the partial withdrawal times of the product!
PD4071=Partial withdrawal times have exceeded the product definition times. Partial withdrawal is not allowed!
PD4072=Partial withdrawal is not allowed!
PD4073=The sum of transaction amount [{}] and cumulative amount [{}] exceeds the limit of the current day [{}]!
PD4074=Whether to conduct cross free trade zone inspection? The ID cannot be empty!
PD4075=Free Trade Zone accounts can only be traded in the free trade zone, and non free trade zone accounts can only be traded in non free trade zone branch!
PD4076=No operating mechanism information!
PD4077=When opening a multi currency product, you must first open a primary account in functional currency! Functional currency: [{}], opening currency: [{}].
PD4078=The product parameter [{}] configuration should be configured in the parameter value definition!
PD4079=The product [{}] indicator [{}] is incorrectly configured, and the value definition of parameter [{}] is missing. The transaction is terminated!
PD4080=The funds transferred from class II/III accounts must be bound to class I accounts. Class I account information has not been bound yet!
PD4081=The funds transferred from class II/III accounts must be bound to class I accounts!
PD4082=Class II/III accounts are bound to class I accounts as non bank accounts, which is not supported at the moment!
PD4083=Voucher type is not configured for product type [{}]!
PD4084=Product type [{}] is configured with multiple voucher types!
PD4085=Transaction currency of FT account and domestic general account must be CNY!
PD4086=[{}] the client's certificate has expired. Please maintain the client certificate information first!
PD4089=Transaction prohibited by channel [{}]
PD4090=The client has opened this class I account in the bank or the opening of such account has reached the upper limit!
PD4091=Product [{}] balance direction is not defined!
PD4092=Product [{}] is not defined!
PD4093=Account opening quantity control. The client account opening quantity exceeds the limit. Control element: [{}]. Authorization is required!
PD4094=Account opening quantity control: the client account opening quantity exceeds the limit. Control element: [{}], account opening is rejected!
PD4095=Account opening quantity control: the client account opening quantity exceeds the limit. Control element: [{}], prompt whether to continue!
PD4096=Universal cash withdrawal is not allowed for credit account!
PD4097=Debit account cannot be redeemed!
PD4098=Universal deposit is not allowed for debit account!
PD4099=The current client type does not match the account attribute!
PD4100=Limit start date cannot be empty!
PD4101=Unable to find the record account parameters!
PD4102=Account primary key cannot be empty!
PD4103=The account expiration date cannot be blank or check the product parameter configuration!
PD4104=There are restrictions on the account!
PD4105=This account is a remittance account and cannot be used for cash credit!
PD4106=The product value date verification is illegal!
PD4107=Product status verification is illegal!
PD4108=Cash account cannot be transferred to remittance account!
PD4109=The primary sub account opening ID is illegal. The value range is [m, s]!
PD4110=Product type cannot be empty!
PD4111=The number of early withdrawals has exceeded the allowed number of periods. No further early withdrawals are allowed!
PD4112=Saving account cannot be maintained for current account!
PD4113=Account cumulative accrual is negative, interest settlement is not allowed!
PD4114=Direct transfer is not allowed between accounts in different free trade zones!
PD4115=No transaction can be conducted between free trade zone accounts and non free trade zone accounts!
PD4116=Non transfer business is not allowed in the free trade zone!
PD4117=Free Trade Zone accounts can only be traded in this free trade zone!
PD4118=Transactions are not allowed in different regions!
PD4119=The number of accounts with product type [{}] exceeds the limit, and authorization is required!
PD4120=The number of accounts with product type [{}] exceeds the limit!
PD4121=The number of acounts with product type [{}] exceeds the limit. You will be prompted whether to continue!
PD4122=The number of accounts with an account attribute of [{}] exceeds the limit. Authorization is required!
PD4123=The number of accounts with account attribute [{}] exceeds the limit!
PD4124=The number of accounts with an account attribute of [{}] exceeds the limit. You will be prompted whether to continue!
PD4125=The number of accounts with voucher type [{}] under the client name exceeds the limit. Authorization is required!
PD4126=The number of accounts with voucher type [{}] exceeds the limit
PD4127=The number of accounts with voucher type [{}] exceeds the limit. You will be prompted whether to continue!
PD4128=The number of accounts with account category [{}] exceeds the limit. Authorization is required!
PD4129=The number of accounts with account category [{}] exceeds the limit!
PD4130=The number of accounts with account category [{}] exceeds the limit. You will be prompted whether to continue!
PD4131=The number of such accounts exceeds the limit. Authorization is required!
PD4132=The number of such accounts exceeds the limit, and account opening is refused!
PD4133=If the number of such accounts exceeds the limit, you will be prompted whether to continue!
PD4134=Transaction amount [{}][{}] cannot be greater than the maximum purchase amount! [{}][{}]！
PD4135=Face to face signing is not allowed for this electronic account product!
PD4202=Product [{}] is not a deposit product!
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
#------------------------------------6------------end-----------------------------#
PD6001=The product definition does not support the [{}] business function!
PD6002=Indicator [{}] in event [{}] has no instance configured!
PD6003=The indicator [{}] attribute configuration in event [{}] is abnormal. Please check whether the indicator instance and indicator parameter definitions are consistent!
PD6004=The attribute [{}] definition does not exist. Please configure the attribute in the attribute type definition table!
PD6005=The indicator [{}] type in the event [{}] is not defined. Please configure the indicator in the indicator type definition table!
PD6006=The attribute definition of indicator [{}] in event [{}] is incomplete. Please check the indicator parameter definition!
PD6007=The product [{}] definition is incomplete, and the [{}] attribute in the basic product [{}] is missing. Please complete the necessary information of the product!
PD6008=The basic product [{}] does not exist. Please check whether the basic product configuration of the product [{}] is correct or whether the basic product is defined!
PD6009=The product [{}] default event [{}] does not exist. The product configuration is incorrect. Please check the corresponding configuration of the product!
PD6010=The property [{}] value is empty, please configure the property value!
PD6011=Product [{}] is not configured with whitelist support type!
PD6012=No matching product information found!
PD6013=The submitted interest rate type does not match the product configuration!

#------------------------------------7------------begin---------------------------#
#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#
#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#
#------------------------------------9------------end-----------------------------#

