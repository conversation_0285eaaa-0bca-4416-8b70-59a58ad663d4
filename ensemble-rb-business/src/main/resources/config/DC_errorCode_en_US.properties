#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#
#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
DC3001=The operation type cannot be empty!
DC3002=Insufficient available balance!
DC3003=The reservation or subscription information does not exist or has been cancelled!
DC3004=Customer information does not exist!
DC3006=account information does not exist!
DC3007=The deposit reservation/subscription information has been canceled and cannot be canceled repeatedly.
DC3008=The account has been opened and cannot be cancelled!
DC3009=Failed to open account and cannot re-do!
DC3010=Please enter one of the account number/reservation number/period date to query.
DC3011=The quota reduction parameter is empty!
DC3012=The issue has expired and trading is not allowed!
DC3013=Insufficient available quota for large certificate of deposit configuration, transaction is not allowed!
DC3014=The issue number cannot be empty
DC3015=Organization number cannot be empty!
DC0316=No records returned from query!
DC3018=Those who have signed up for card financing cannot make reservations for subscription!
DC3019=account number and reservation number is mandatory!
DC3020=The transfer of corporate large-denomination certificates of deposit must be carried out in this institution!
DC3021=The principal and interest account in the transfer information cannot be the original interest account when purchasing large certificates of deposit!
DC3023=Issue [{}] does not exist, please check the issue definition!
DC3024=The voucher number cannot be empty!
DC3026=Transaction cannot modify the annual quota before the current year!
DC3027=The transferred account types need to be consistent!
ST3201=The main account is empty!
ST3202=The settlement account is empty!
ST3204=The new associated income account and structured deposit account must be the same customer!"
DC3030=The new period code already exists, please modify it!
DC3031=The annual quota does not exist, please set up the annual issuance plan first!
DC3032=The period allocation quota is greater than the annual remaining quota, please modify it!
DC3033=The period information does not exist!
DC3034=The annual quota does not exist!
DC3035=The adjustment amount is greater than the annual remaining amount, please modify it!
DC3036=Only whitelist users of this issue can make reservations and subscriptions!
DC3037=The issue code cannot be empty!
DC3038=The period information does not exist!
DC3039=The current organization does not have an organization level configured!
DC3040=The current branch has not allocated a quota, please allocate a quota from the head office first!
DC3041=The branch does not have operation authority!
DC3042=The current quota is auctioned, and branches and branches are not allowed to configure it!
DC3043=The current branch has no quota information!
DC3044=When making a reservation for a large certificate of deposit, the execution interest rate must be submitted!
DC3045=Channel type is undefined , or channel classification is undefined !
DC3046=Insufficient available period quota!
DC3047=This institution has no available quota!
DC3048=The period reservation quota does not exist!
DC3049=The electronic channel for this issue does not exist!
DC3051=The channel quota does not exist!
DC3052=The institution's quota does not exist!
DC3053=The equipment quota of this organization does not exist!
DC3054=The branch institution quota has not been allocated, and it is not allowed to set quotas for branches [{}]!
DC3055=The institutional quota allocation of branch [{}] exceeds the allocable quota [{}]!
DC3056=The period has been sold or reserved, and modification or deletion is not allowed!
DC3058=Deletion is not allowed for this issue!
DC3059=Please set the quota configuration method for the current period!
DC3060=The operating mechanism cannot be empty!
DC3061=The cumulative distribution amount cannot be less than 0!
DC3062=The cumulative distribution quota and remaining quota cannot be less than 0!
DC3063=The card account is reported as lost and it is not allowed to open a personal large-denomination certificate of deposit under the card!
DC3065=The total issuance quota after modification cannot be less than the allocated quota!
DC3066=The reservation information has failed to open an account and no cancellation is allowed!
DC3067=The reservation information has been successfully opened and no cancellation is allowed!
DC3068=This period has passed the reservation start time and is not allowed to be deleted!
DC3069=The legal person information of the operating organization cannot be empty!
DC3070=Pending order start date cannot be empty!
DC3071=Pending order end date cannot be empty!
DC3072=The query time interval cannot be greater than one year!
DC3073=When the interest rate indicator is immediate interest rate, this transaction is not allowed!
DC3074=The expiration date of the certificate is earlier than the interest date of the large-denomination certificate of deposit product purchased, and this transaction is not allowed!
DC3075=The subscription has been successfully opened and cannot be modified!
DC3076=A branch available for sale is set up for this period. The institution to which the subscription account belongs does not match the branch available for sale. Purchase is not allowed!
DC3077=The "Whether the fund source is allowed to be an internal account identification" for this period is "N". Internal accounts are not allowed to be used. Open a large-denomination certificate of deposit!
DC3078 = "Whether to specify interest collection" for this period is "N", and the customer numbers of the subscription account and the interest collection account must be consistent!
DC3079=For private purposes, the interest-collecting account must be a first-class account!
DC3080=Internal account and regular one-stop service must be at the same outlet!
DC3081=When interest is paid according to frequency, the interest-collecting account will lose!
DC3082=The interest rate mark is the interest rate at the end of the fundraising period, and this transaction is not allowed!
DC3083=The issue has been issued and deletion is not allowed!
DC3084=The issue number or issue description cannot be empty!
DC3085=Sellable institutions cannot be empty!
DC3088=The quota occupation application does not exist!
DC3089=Cancellation is not allowed in non-application status!
DC3093=The sellable institution is inconsistent with the quota application institution, please check!
DC3096=The issue end date must be greater than the issue start date!
DC3097=The subscription has been successfully opened and cannot be canceled!
DC3098=The internal account subscribed must be an internal account with the function of pending account cancellation!
DC3099=The issue date cannot be less than the accounting date!
DC3100=The Limit Has Been Used And No Modification Is Allowed!
DC3207=Already Assigned To Other Channels, Redistribution To All Channels Is Not Allowed!
DC3208=The Expiration Date Of The Certificate Is Earlier Than The Expiration Date Of The Large-Denomination Certificate Of Deposit Product Purchased, And This Transaction Is Not Allowed!




#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
DC4001=The parameters of the issue code [{}] in the issue information table are not configured or the parameter configuration is incomplete. Please check the parameter configuration of the issue information table!
DC4002=Please configure the large deposit certificate restriction type parameter!
DC4003=期次已过发行期，不允许交易！
DC4004=Insufficient available quota for large-denomination certificate of deposit configuration, transaction is not allowed!
DC4005=The issue number cannot be empty!
DC4006=The trading institution number, issue number, and channel cannot be empty.
DC4007=The organization information table does not contain configuration information for this organization.
DC4008=Organization number cannot be empty!
DC4009=Channel cannot be empty!
DC4010=机构信息表没有本机构配置信息
DC4011=配置类型、年度、期次代码、产品类型、币种不能为空！
DC4012=年度剩余额度不足！
DC4013=新的期次发行额度不能小于已分配额度和已使用额度之和！
DC4014=[{}]配置数据不满足：发行额度=已分配额度+已用额度+剩余额度！
DC4015=本期次已有有“ALL-所有渠道”配置，[{}]不能新增！
DC4016=渠道[{}]未配置，不能进行修改！
DC4017=渠道[{}]配置发行额度总额过大！
DC4018=“ALL-所有渠道”新增过多！
DC4019=原有“ALL-所有渠道”再不能有其它渠道！
DC4020=渠道信息数组不能为空！
DC4021=本期次[{}]销售方式不是配额销售，不允许设置配额！
DC4022=机构[{}]额度配置已经存在，不允许新增！
DC4023=机构[{}]未配置，不能进行修改！
DC4024=分行配置发行额度总额过大！
DC4025=提交的分行信息数组不能为空！
DC4026=支行配置发行额度总额过大！
DC4027=分行额度配置信息未找到,只能在已查找到的分行中选择，否则先配置分行配额！
DC4028=提交的分行信息数组不能为空！
DC4029=The transfer-out account must be a current settlement account for the same customer!
DC4030=本年度额度未配置！
DC4031=The customer type does not match the product!
DC4032=The products of this annual period have been redeemed!
DC4034=This issue has not been released!
DC4035=The registration amount cannot be 0!
DC4036=Year and registration quota cannot be empty!
DC4037=The subscription time is not within the defined time range of the period!
DC4038=The pre-order amount does not meet the minimum deposit amount!
DC4039=For Individual Accounts, You Can Only Renew The Large-Deposit Sub-Account Under The Card! !
DC4040=The period information does not exist!
DC4041=This account is not a current settlement account!
DC4042=Large-denomination certificates of deposit can only handle [{}]  business!
DC4043=The principal and interest account must be a current settlement account!
DC4044=The Product Type For Opening A Main And Sub-Account Does Not Meet The Requirements!
DC4045=Private customers can only reserve/subscribe large deposit sub-accounts under the card! !
DC4046=When a free trade zone institution subscribes/reserves a large certificate of deposit account, the fund source account must be a free trade zone account! !
DC4047=When recognizing/reserving a large certificate of deposit in the Free Trade Zone, the fund source account must be the same Free Trade Zone account! !
DC4048=Reservation/subscription can only be reserved and subscribed for unissued issues!
DC4049=This issue has been deleted and operations are not allowed!
DC4050=The adjustment amount is too small!
DC4051=The quota has been used and deletion is not allowed!
DC4052=The allocated quota exceeds the allocated quota!
DC4053=The reservation quota must be configured with the reservation start time and reservation end time!
DC4054=The period has started to be reserved, and the reservation quota is not allowed to be deleted!
DC4055=The current date has exceeded the issue start date, and it is prohibited to modify the reservation amount!
DC4056=Reservation subscription must be within the reservation time period
DC4057=This period is proportional allotment, and there is already a reservation record, and the reservation quota is not allowed to be reduced!
DC4058=This account holds deposit certificates and automatic settlement is not allowed!
DC4059=There is already a reservation record for this period, and the reservation quota is not allowed to be reduced!
DC4060=There is already a reservation record for this period, and the reservation quota is not allowed to be deleted!
DC4061=Pending order end date cannot be empty!
DC4062=The beneficiary customer number cannot be empty!
DC4063=The Transfer Status, Transfer Number, And Transferee Customer Number Cannot All Be Empty!
DC4064=[{}][{}]The adjusted total quota cannot be lower than the allocated quota!
DC4065=[{}][{}]No available quota!
DC4066=[{}][{}]Insufficient available quota!
DC4067=No unit quota is allocated for the current year!
DC4068=Cannot allocate quota to past years!
DC4069=User error, customer number and issue do not match!
DC4070=The account has been closed and redemption is not allowed!
DC4071=You Must Lose One Of The Beneficiary Customer Number And Transfer Password!
DC4072=Please Enter The Subscription Amount!
DC4073=The number of months until maturity must be a positive integer within the range of 1 to 12!
DC4074=The large-denomination certificate of deposit has expired and the subscription failed!
DC4075=The subscription customer group type identifier does not match the period configuration, and the subscription failed!DC4076=开户金额不允许小于期次单笔最低购买金额，申购失败！
DC4076=The account opening amount is not allowed to be less than the minimum single purchase amount of the period, and the subscription failed!
DC4077=The Account Opening Amount Is Not Allowed To Exceed The Maximum Single Purchase Amount Of The Period, And The Subscription Fails!
DC4078=Insufficient available quota for the period, subscription failed!
DC4079=The total number of large-denomination certificates of deposit accounts that this customer has purchased for this period + the account opening amount of newly opened large-denomination certificates of deposit exceeds the set purchase limit for this customer group, and the subscription failed!
DC4080=The total number of large-denomination certificates of deposit accounts set by the customer group that have been purchased by this customer + the account opening amount of the newly opened large-denomination certificates exceeds the set purchase limit for the customer group, and the subscription failed!
DC4081=The interest due due cannot be 0!
DC4082=The Customer Type And The Account Group Customer Type Are Inconsistent!
DC4083=The Contract Deposit Account Group Cannot Be Empty!
DC4084=Effective Contract Information Does Not Allow Modification Of The Effective Start Date

ST4100=Private customers can only subscribe for structured deposit sub-accounts under the card!
ST4101=This account is not a current settlement account!
ST4102=This account must be an active account!
ST4103=This issue does not exist!
ST4104=Product type cannot be empty!
ST4105=The issue code cannot be empty!
ST4106=Product name cannot be empty!
ST4107=Product description cannot be empty!
ST4108=The currency cannot be empty!
ST4109=Risk level cannot be empty!
ST4110=The investment period cannot be empty!
ST4111=Term type cannot be empty!
ST4112=The linked subject cannot be empty!
ST4113=Issue size cannot be empty!
ST4114=The minimum amount for period establishment cannot be empty!
ST4115=The minimum subscription deposit amount cannot be empty!
ST4116=Amount unit cannot be empty!

ST4117=The Maximum Amount Of A Single Subscription Cannot Be Empty!

ST4118=The subscription start date cannot be empty!
ST4119=Subscription termination date cannot be empty!
ST4120=Value date cannot be empty
ST4121=The expiration date cannot be empty!
ST4122=The accrual interest rate rule cannot be empty!
ST4123=Early withdrawal interest rate cannot be empty!
ST4124=Touch type cannot be empty!
ST4125=Obstacle (trigger) percentage cannot be empty!
ST4126=Whether the trigger event terminates the product flag cannot be empty!
ST4127=Observation start date cannot be empty!
ST4128=Observation end date cannot be empty!
ST4129=Rule description cannot be empty!
ST4130=Low-gear interest rate cannot be empty!
ST4131=High-gear interest rate cannot be empty!
ST4132=Annualized performance parameter cannot be empty!
ST4133=Whether to set the observation day flag cannot be empty!
ST4134=Observation day information cannot be empty!

ST4135=Minimum Price Cannot Be Empty!
ST4136=Maximum Price Cannot Be Empty!

ST4137=The subscription start date must be less than or equal to the subscription end date!
ST4138=The interest value date must be greater than or equal to the subscription termination date!
ST4139=The end date of the observation interval must be greater than or equal to the start date of the observation interval!
ST4140=The starting date of the observation interval must be greater than or equal to the interest rate date!
ST4141=The end date of the observation interval must be less than the expiration date!
ST4142=The observation day is not within the range!
ST4143=The minimum amount for product establishment must be less than or equal to the issuance scale!
ST4144=The minimum subscription amount must be less than or equal to the issuance size!
ST4145=The amount unit must be less than or equal to the subscription deposit amount!
ST4146=The maximum amount of a single subscription must be less than or equal to the issuance size!
ST4147=The minimum deposit amount for subscription must be less than or equal to the maximum amount for a single subscription!

ST4148=Threshold 1 must be less than or equal to threshold 2!
ST4149=The annualized performance benchmark (low) must be less than the annualized performance benchmark (high)!
ST4150=The execution interest rate cannot be empty!

ST4151=The Price Must Be Between 0-200!

ST4152=The annualized rate of return must be one of the annualized performance benchmark (low) and the annualized performance benchmark (high)!
ST4153=The subscription start date must be after the current system date!
ST4154=This period has been terminated!
ST4155=Can only be modified before the subscription period!
ST4156=Structured deposits cannot be changed in terms of deposit period!

ST4157=Structured Deposit [{}] Does Not Allow Reversal!

ST4158=Provision adjustment is not allowed for structured deposits!
ST4159=Structured deposits do not allow regular account holder changes!
ST4160=Structured deposits do not allow regular withdrawals!

ST4161=Structured Deposits Do Not Allow Interest Rate Changes!

ST4162=This transaction is not allowed for non-structured deposits!
ST4163=The account has payment restrictions and does not allow this transaction!
ST4164=The first purchase should meet the subsequent minimum purchase requirements!
ST4165=The additional quota should be an integral multiple of the unit quota!
ST4166=The transaction amount is not allowed to be greater than the remaining balance of the period!

ST4167=You Can Only Purchase Issues In The Subscription Period!

ST4168=Undo operations must maintain "summary" information!
ST4169=Branch Cannot Be Empty!

ST4170=The subscription time is not within the defined time range of the period!
ST4171=The period information does not exist!
ST4172=There is no additional scene for subscription!
ST4173=The subscription amount cannot be greater than the maximum single amount [{}]!
ST4174=The obstacle percentage should be greater than 100 when touching upward and not touching upward!
ST4175=The obstacle percentage should be less than 100 when touching down and not touching down!
ST4176=Trigger percentage should be greater than 100 when bullish!
ST4177=Trigger percentage should be less than 100 when bearish!
ST4178=The valid business day should be [{}]!
ST4179=Annualized rate of return cannot be empty!
ST4180=secondary card s cannot subscribe to structured deposit accounts!
ST4181=Structured deposit accounts cannot be subject to forced deductions!
ST4182=Subscription can only subscribe for the issues within the issuance period!
ST4183=This issue has been deleted!
ST4184=The subscription amount cannot be empty!
ST4185=The investment period does not match the maturity date and value date!
ST4186=The subscription amount cannot be less than the minimum deposit amount [{}]!
ST4187=The deposit period cannot be changed with one million profits!

ST4188=The Interest Classification Parameters Were Uploaded Incorrectly!

ST4189=The status of this issue is [{}], it is not in the issuance status and does not allow deletion or modification!

ST4192=The Reservation Start Time Must Be Less Than The Reservation End Time!
ST4191=The Number Of Liquidation Days Cannot Be Empty!
ST4190=Liquidation Expiration Date Cannot Be Empty!
ST4193=The Subscription Account And The Expiry Credit Account Must Be The Same Customer!
ST4194=The End Date Must Be Greater Than Or Equal To The Start Date!
ST4195=The Query Interval Between The End Date And The Start Date Cannot Exceed 1 Year!
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
DC6001=The Reduced Filing Amount Cannot Be Greater Than The Remaining Annual Amount!
DC6002=The Length Of The Entered Or Adjusted Filing Amount Cannot Be Greater Than [{}] Digits!
DC6003=The Subscription Amount Cannot Be Greater Than The Remaining Amount Of This Period [{}]!
DC6004=No records match the search conditions. Procedure！
DC6005=Customer number, customer ID information, account number three must lose one
DC6006=Account Information Inquiries Are Not Allowed For Non-Account-Opening Banks!
DC6007=The Customer Status Is [{}], The Status Is Incorrect!
DC6008=This Account Has Not Been Opened!
DC6009=Incorrect interest rate information on account opening！
DC6010=Product Type Cannot Be Empty!
DC6011=Unit periodic confirmation is not allowed to print across agencies!
DC6012=The Interest Credit Account And The Maturity Settlement Account Must Be The Same Account!
DC6013=The Fund Source Account And The Due Settlement Account Must Be The Same Account!
DC6014=The Fund Source Account Number And The Interest Payment Account Number Must Be The Same Account Number!
DC6015=The Purchase Account And The Settlement Account Must Be The Same Account!
DC6016=The Purchase Account And The Interest Deposit Account Must Be The Same Account!
DC6017=The Purchase Account And The Fund Source Account Must Be The Same Account!
DC6018=The account has credential information
DC6019=There are restrictions on this account that do not allow printing

RB3219=Insufficient available balance in account
DC6020=The booking customer [{}] and the funding source customer [{}] must be the same customer
DC6021=Pending transfer of CDS is not allowed during the cooling-off period
DC6022=A Paper Deposit Receipt Has Been Issued For This Account And Transfer Is Not Allowed!
DC6023=There are restrictions on this account, no transfer is allowed!
DC6024=Transfer rate does not comply with the rules, not allowed to transfer!
DC6025=The products of the annual period are being redeemed, not allowed to transfer!
DC6026=Transfer amount is less than the minimum deposit amount, not allowed to transfer!
DC6027=The account has opened a period of assets certificate, not allowed to transfer!
DC6028=The current CDS have expired, no transfer application is allowed!
DC6029=This Large Deposit Certificate Is Not Transferable For This Period!
DC6030=The Transfer Application Has Been Cancelled!
DC6031=Transfer application has been effective, repeat application is not allowed!
DC6032=Only the current whitelist users can make transactions!
DC6033=Product period is not in the sale period, not allowed to trade!
DC6034=Deposit amount has reached the maximum amount of this period, not allowed to deposit!
DC6035=All transfer must transfer all principal!
DC6036=Different types of customers are not allowed to transfer!
DC6037=The transfer type is inconsistent with the application, and the transaction cannot be processed!
DC6038=Insufficient balance remaining after transfer: unable to continue with the transfer!
DC6039=The remaining balance after transfer is less than the minimum required balance, partial transfer is not allowed!
DC6040=The transferred principal amount does not match the registered amount: please check!
DC6041=The recipient and the transferor cannot be the same customer!
DC6042=The starting deposit amount [{}] must be greater than or equal to the product's set minimum deposit amount [{}]!
DC6043=The minimum remaining balance amount [{}] for the period must be greater than or equal to the product's set minimum remaining balance [{}]!
DC6044=his product for this period does not allow the establishment of an automatically settled account!
DC6045=Corporate accounts are not allowed to print this voucher!
DC6046=Printing is not allowed during redemption of large-denomination certificates of deposit!
DC6047=The interest income account has restrictions on credit operations and does not allow deposits!
DC6048=This account has judicial freeze, automatic settlement is not allowed!
DC6049=This account has been pledged and automatic settlement is not allowed!
DC6050=This Account Is A Paper Deposit Receipt And Does Not Allow Automatic Settlement!
DC6051=There is no redemption anomaly in the interest entry account!
DC6052=The Large Certificate Of Deposit Account Does Not Exist!
DC6053=The Transferee'S Customer Number Is Inconsistent With The Registered Beneficiary Of The Targeted Transfer, And The Transaction Is Not Allowed!
DC6054=The account has issued vouchers are not allowed to redeem!
DC6055=The Transfer Type Is Incorrect, Please Check The Information!
DC6056=The Status Of This Issue Is Not For Sale!
DC6057=The Transfer Maturity Date Must Be Less Than The Maturity Date Of The Large Deposit Certificate!
DC6058=The Transfer Amount Is Greater Than The Available Amount Of The Deposit Certificate, And The Transfer Is Not Allowed!
DC6059=The Transfer Amount Does Not Comply With The Rules And The Transfer Is Not Allowed. The Balance After The Transfer Is Not Allowed To Be Less Than The Retained Amount Of The Period!
DC6060=There Are Pledge Restrictions On The Account And No Transfer Is Allowed!
DC6061=The Account Is Subject To Judicial Freeze And No Transfer Is Allowed!
DC6062=The Withdrawal Amount Does Not Comply With The Rules And Is Not Allowed To Be Withdrawn. The Balance After Withdrawal Is Not Allowed To Be Less Than The Retained Amount Of The Period!
DC6063=The System Is Recalculating Transfer Prices In Batch Processing, Please Try Again Later!
DC6064=This account has already issued a certificate and redemption is not allowed!
DC6065=Invalid query type!
DC6066=The transfer application date is greater than the due date of the large-denomination certificate minus the transfer deadline, transfer is not allowed!
DC6067=This Session Is Not In Reservation Status!
#------------------------------------6------------end-----------------------------#
#------------------------------------7------------begin---------------------------#
DC7001=The redemption of this issue is not allowed!
DC7002=The large-denomination certificate of deposit account information for this issue does not exist!
DC7003=Multiple account information exists for this issue, redemption is not allowed!
DC7004=The branch number belonging to this issue is inconsistent with the uploaded branch number, redemption is not possible!
DC7005=The customer does not match the issue, redemption is not allowed!
DC7006=The large-denomination certificate of deposit account is frozen (non-pledged), redemption application is denied!
DC7007=The automatically settled account has restrictions on receiving and paying, redemption application is denied!
DC7008=When the pledge flag is Y, the deposit account information cannot be empty!
DC7009=This issue has already been redeemed!
DC7010=A redemption application record already exists for this issue!
DC7011=The redemption application record for this issue does not exist or has already been redeemed!
DC7012=The deposit account in the redemption application does not exist!
DC7013=The redemption date should be greater than the current system date or less than the expiration date of the issue!
DC7014=The Redemption Branch Is Inconsistent With The Branch To Which The Large-Denomination Certificate Of Deposit Belongs, So Registration Is Refused!
DC7015=The Corporate Large-Denomination Certificate Of Deposit Period Has Not Been Closed And Is Not Allowed To Be Transferred!
DC7016=The Redemption Date Should Be Greater Than The Current Date Of The System And Less Than The Issuance End Date Of The Issue!
DC7017=Please Remove The Issue Before Applying For Redemption!
#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#
DC8001=Both customer group and maximum purchase limit must be present!
DC8003=The end date must be greater than or equal to the start date.
DC8004=The transfer status, transferor customer number, assignee customer number, and large-denomination certificate of deposit account number cannot all be empty!
DC8005=Product type and issue code cannot both be empty!
DC8006=The transfer principal cannot be empty!
DC8007=Account/card number cannot be empty!
DC8008=Account sequence number cannot be empty!
DC8009=The transfer listing deadline must be a positive integer.
DC8010=The transfer deadline must be a positive integer.
DC8011=When the transfer flag is Y, the account classification can only be input as 1.
DC8013=The current time has reached the issue product's release start date. Only the "issue status" field can be modified, and it can only be changed from "in release" to "invalid".
DC8014=Customer group cannot be empty!
DC8015=The actual interest rate must not exceed the maximum interest rate value configured for the product interest rate type!
DC8016=The total transfer consideration cannot be empty!
DC8017=The issuance year cannot be empty!
DC7026=The number of early withdrawals [0] has exceeded the number defined for the product issue [0], and no further early withdrawals are allowed!
DC8018=The Start Time Of Pending Orders Must Be Greater Than The Account Opening Time!
DC8019=The order closing time must be less than the expiration time!
DC8020=The transfer of CDS is not allowed to offset
DC8021=The Current Time [{}] Must Be Less Than Or Equal To The Pending Order Start Time [{}]!!
DC8022=The Current Time [{}] Must Be Greater Than Or Equal To The Account Opening Time [{}]!
DC8023=The Current Time [{}] Must Be Less Than Or Equal To The Expiration Time [{}]!
DC8024=The start date of order hanging must be greater than the value date!
DC8025=The English description of the issue cannot be empty!
DC8026=The account opening date cannot be earlier than the system date!
DC8027=The Effective Date Cannot Be Less Than The System Date!
DC3005=Customer Certificate Information Does Not Exist!
DC3017=Cross-Branch Are Not Allowed For Public Subscription Subscription And Cancellation!
DC3022=Redemption Can Only Be Done On Issued And Unexpired Large Certificates Of Deposit!
DC3050=The Limit For This Device Does Not Exist!
DC3057=Only The Total Issuance Limit Is Allowed To Be Increased Between The Issuance Start Date And The Issuance End Date!
DC3064=When Purchasing Large-Denomination Certificates Of Deposit For Individuals, The Fund Source Account Cannot Be A Second-Type Account!
DC3086=The Issue Has Been Issued And No Modification Is Allowed!
DC3087=There Is Already Application Information For This Approval Order Number!
DC3090=The Issuance Limit Of The Issue Is Inconsistent With The Application Limit, Please Check!
DC3091=The Approval Order Number Is Not Registered!
DC3092=The Issue Code Is Inconsistent With The Application Information, Please Check!
DC3094=Invalid Approval Order Number!
DC3095=Issue Year, Product Type, And Issue Code Cannot All Be blank!
DC3101=The End-Of-Sale Time Must Be Greater Than The Start-Of-Sale Time!
DC3102=Please Enter The Reservation Number!
DC3103=The Settlement Account And The Large Deposit Certificate Opening Customer Must Be The Same Customer!
DC3104=The Format Of The Start Time/End Time Is Incorrect, For Example: 08:00!
DC3105=The Interest Settlement Date And Maturity Date Of The Large Certificate Of Deposit Account Are Not Allowed To Be Modified!
DC3106=The Current Balance Is Insufficient, Re-Enter The Modified Amount!
DC3107=The Subscribed Internal Account Is An Internal Account That Does Not Have The Function Of Canceling Accounts, And The Pending Account Data Should Be blank!
DC3108=The Whitelist Array Cannot Be blank!
DC3109=Channel Array Cannot Be blank!
DC3110=The Remaining Limit Of The Period Is Insufficient!
DC3111=Counter Channels Or Omni-Channels Must Allocate Limits First, And Limits Cannot Be Allocated To Branches!
DC3112=The Sub-Branch Array Cannot Be blank!
DC3113=The Remaining Limit Of Counter Channels Is Insufficient!
DC3114=The Expiration Date Of The Certificate Is Earlier Than The Issuance End Date Of The Large Deposit Certificate Purchased, So This Transaction Is Not Allowed!
DC3115=The Account Status Of The Subscription Account Must Be Active!
DC3116=The Voucher Status Of The Subscription Account Is Abnormal!
DC3117=The Password Status Of The Subscription Account Is Abnormal!
DC3118=There Is A Full Payment Stop Limit On The Subscription Account!
DC3119=The Subscription Account Is Subject To Judicial Freezing Restrictions!
DC3120=Only Whitelisted Users In This Period Can Subscribe!
DC3200=Limit Type [{}] Is Illegal!
DC3201=Insufficient Remaining Limit In The Whitelist!
DC3202=The Remaining Limit Of The Branch Is Insufficient!
DC3203=The Remaining Limit Of This Channel Is Insufficient!
DC3204=The Customer Does Not Match The Issue Type!
DC3205=The Customer Number And Certificate Information Do Not Match!
DC3206=All Channels Have Been Allocated And No Other Channels Are Allowed To Be Allocated!

#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#
#------------------------------------9------------end-----------------------------#