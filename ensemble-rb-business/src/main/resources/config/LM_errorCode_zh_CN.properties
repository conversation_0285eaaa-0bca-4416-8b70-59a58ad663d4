#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
LM3101=操作类型不能为空
LM3103=控制类型不能为空
LM3104=限额ID不能为空
LM3106=账户信息不能为空
LM3107=证件信息不能为空
LM3108=当前柜员无{0}币种/凭证类型信息
#4：检查类提示：数据有效性检查
#（如格式、存在、不存在、不在有效值范围等）
LM4101=限额类型[{}]未定义!
LM4102=限额代号[{}]对应的限额信息不存在
LM4103=账户主键[{}]对应的账户已设定交易限额[{}]
LM4104=客户[{}]已设定交易限额[{}]
LM4105=账户号[{}]，账户序号[{}]对应的账户已设定交易限额[{}]
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查
#（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查
#（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
LM6101=限额明细数据不存在
LM6102=限额明细数据不合法
LM6103=限额明细数据重复
LM6104=限额产品正在被使用不能删除或修改
LM6105=机构[{}]不属于本机构[{}]的下属机构
LM6106=限额信息为[{}]
LM6107=尾箱[{}]为现金尾箱,不能建立凭证限额关系
LM6108=尾箱[{}]为凭证尾箱,不能建立现金限额关系
LM6109=尾箱[{}]不存在
LM6110=只有总行机构可为自身建立/撤销限额关系
LM6111=查询无记录
LM6112=客户名下非面签III类户交易额度超限，请尽快到柜面进行客户信息确认。
#7：检查类提示：业务流程合法性检查
LM6113=限额类别不能为空！
#（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
LM6114={}