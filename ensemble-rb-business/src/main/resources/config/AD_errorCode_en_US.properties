#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#

#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
AD4002 = The loss report number does not exist!
AD4101 = No record found
AD4102 = Bills  does not exist!
AD4103 = The bill status is wrong, only un-reserved bills can be returned for un-reserved funds!
AD4104 = No bank's acceptance bill information 
AD4105 = Operation type cannot be empty! !
AD4106 = Wrong operation type!
AD4107 = Incorrect loss report type!
AD4108 = Bills information does not exist!
AD4109 = The loss report type should be 1-announce loss report or 2-flitigation
AD4110 = Loss report type should be 2 - litigation!
AD4114 = The person who reported the loss is not the original person who reported the loss, and the type of loss report is not allowed to be modified!
AD4115 = The loss report number cannot be empty!
AD4116 = The loss report information does not exist!
AD4127 = The available balance of this account is insufficient and cannot be frozen!
AD4137 = The bill is not in reserve status and cannot be redeemed!
AD4147= The payment institution is undefined  in our bank, please confirm!
AD4154 = The bank acceptance bill replacement operation can only be performed on the day the bank acceptance bill is accepted!
AD4178 = No bills information found!
AD5020 = Cancellation of acceptance can only be operated on the day of acceptance!
AD5046 = Only paper billss can be used for bills replacement!
AD5047 = The bank acceptance bill must be in acceptance status before the bank acceptance bill can be replaced!
AD6001 = The message sent is empty!
AD6002 = Please check the parameters sent!
AD6003 = Please enter the correct operation type!
AD6004 = Please submit the correct bills media type!
AD6005 = Please submit the receipt type when issuing paper billss!
AD6006 = Please check the uploaded payment account information!
AD6008 = Please check the deposit account information!
AD6009 = Please check parameter configuration 'BAD_RESTRAINT_TYPE'!
AD6010 = Restriction type [{}] does not exist!
AD6011 = The banking agreement information corresponding to the sending account serial number does not exist!
AD6012 = The uploaded reserve account array is empty!
AD6013 = There is no corresponding reserve account for this transactions serial number!
AD6015 = The submitted bills information does not exist!
AD6018 = Insufficient available balance in margin account!
AD6019 = The billing serial number cannot be empty!
AD6020 = The acceptance bill information corresponding to the issuing serial number does not exist!
AD6021 = There is no available bill information under the billing serial number!
AD6026 = The amount of parameters sent is empty!
AD6027 = The invoice amount is empty!
AD6028 = The acceptance amount must be greater than 0!
AD6029 = The face value must be greater than 0!
AD6030 = The invoice amount cannot be greater than the total amount!
AD6031 = Personal accounts are not allowed to issue bank acceptance drafts!
AD6032 = The payment account and margin account must from the same institution!
AD6033 = The acceptance information cannot be found or the acceptance has been canceled!
AD6034 = Multiple margin accounts are not allowed to exist in one outgoing account number!
AD6036 = A regular margin account can only correspond to one account serial number!
AD6037 = The bills has been issued and cannot be re-issued
AD6038 = There is no qualified voucher under the teller
AD6039 = The TD account is empty or the frozen information does not exist
AD6040 = The TD account reserve amount is 0!
AD6041 = Payer account cannot be empty
AD6042 = The remittance account payable cannot be empty
AD6043 = Reserve data is empty or reserve amount is empty
AD6044 = The current margin account is empty or the frozen information does not exist
AD6045 = The reserve amount of the current margin account is 0!
AD6046 = Current settlement account is empty
AD6047 = The reserve amount of current settlement account is 0
AD6048 = Channel type cannot be empty
AD6049 = Product type cannot be empty
AD6050 = rbFinEbMapFrontList did not find the accounting rule for this operation scenario
AD6051 = rbFinEbMapDistinctList did not find the accounting rule for this operation scenario
AD6052 = rbFinEbMapOptionList did not find the accounting rule for this operation scenario
AD6053 = rbFinEbMapAllList did not find the accounting rule for this operation scenario
AD6054 = Parameter account type cannot be empty
AD6055 = Parameter account type does not match or upload is missing
AD6056 = Unable to determine discount and premium
AD6057 = Wrong parameter product type
AD6058 = Buyer/Seller interest payment loan imbalance
AD6059 = Unremoved media cannot be pledged!
AD6060 = The deposit reserve was successful but the advance failed, please reserve a second time!
AD6061 = Acceptance agreement number cannot be empty!
AD6062 = The bill status does not support trial calculation!
AD6063 = Business dates are inconsistent [{}]!
AD6064 = Either the totalAmt, ccy, billType, billAmt, billSignDate, billAcceptDate or billMaturityDate can not be null when option is equal to 01!
#------------------------------------6------------end---------------------------#









