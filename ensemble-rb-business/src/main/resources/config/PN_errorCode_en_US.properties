#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD,PN）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#
#------------------------------------0------------end-----------------------------#

#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#

#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#

#------------------------------------3------------begin---------------------------#
#------------------------------------3------------end-----------------------------#

#------------------------------------4------------begin---------------------------#
PN4001=When issuing cash, the [{}] input cannot be empty!
PN4002=When issuing a transfer, the [{}] input cannot be empty!
PN4003=When issuing a debit account, the [{}] input cannot be empty!
PN4004=[{}]The input cannot be empty!
PN4005=No record for query!
PN4006=Incorrect bills key input!
PN4007=No bill issuance information was found based on the transaction serial number [{}]!
PN4008=Invalid operation type!
PN4009=bills number [{}] does not exist!
PN4010=No bills issuance information was found according to the bills number [{}]!
PN4011=The transaction deadline must be greater than the transaction start date!
PN4012=When querying based on bills number, the bills number cannot be empty!
PN4013=When querying based on the serial number, the serial number cannot be empty!
PN4014=When querying bill register information, the operation type cannot be empty!
PN4015=No data that meets the conditions!
PN4016=The accounting transaction type is not recognized according to the current bill transaction scenario!
Pn4017=The Account Number [{}] Does Not Exist!
Pn4018=The Pending Account Balance Corresponding To The Pending Account Number [{}] Has Been Processed And Cannot Be Written Off!
Pn4019=The Write-Off Amount Cannot Be Greater Than The Pending Account Balance
PN4020=The input [{}] is inconsistent with the bills issuance information!
PN4021=The entered bill issuance amount is inconsistent with the original issuance amount!
PN4022=The original bills number [{}] does not exist!
PN4023=The bills loss report information was not found and cannot be released!
PN4024=No bills issuance information was found according to the bills number [{}]!
PN4025=Reprint new bills number input cannot be empty!
PN4026=The key input for reprinting the bills cannot be empty!
PN4027=The loss reporting/freezing end date must be greater than the system running date!
PN4028=The loss reporting/freezing start date must be greater than or equal to the system running date!
PN4029=The loss reporting/freezing start date must be less than or equal to the end date!
PN4030=Issuance start date must be less than or equal to the issuance deadline!
PN4031=The expiration start date must be less than or equal to the expiry end date!
PN4032=The minimum issuance amount must be less than or equal to the maximum issuance amount!
PN4033=The minimum issuance amount must be greater than 0!
PN4034=The maximum issuance amount must be greater than 0!
PN4035=This bill of exchange is issued in cash. When partial payment is made, the excess funds will be transferred back to the account. The input cannot be empty!
PN4036=This bill of exchange is issued on a closed account. When partial payment is made, the excess funds transferred back to the account input cannot be empty!
Pn4040=There Is No Corresponding Limit Definition Information For This Voucher/Bill Type!
Pn4041=No Limit Information Found!
Pn4042=The Entered Maximum Issuance Amount Must Be Greater Than Or Equal To The Minimum Issuance Amount!
#------------------------------------4------------end-----------------------------#

#------------------------------------5------------begin---------------------------#
PN5001=The operating teller is not the bill issuance entry teller, and modification or deletion operations are not allowed!
#------------------------------------5------------end-----------------------------#

#------------------------------------6------------begin---------------------------#
Pn6001=The Unblocking Time Exceeds The Payment Prompt Date
Pn6002=The Time Of Reporting Loss Exceeds The Payment Reminder Date
PN6003=The bill has been paid or returned and no further loss reporting is allowed!
PN6004=The bills has been reported lost/frozen and re-execution is not allowed!
Pn6005=The Input Notification Number [{}] Is Inconsistent With The Loss Report Register And The Court Is Not Allowed To Perform The Operation!
PN6006=The bills is not issued/reported as lost and reprinting is not allowed!
PN6007=The court execution notice number input cannot be empty!
PN6008=The court-executed bill must be in court-frozen status!
PN6009=Prompt payment date/due date must be greater than the current system date!
PN6010=The bill issuance date must be less than the payment prompt/expiration date!
Pn6011=The Issuance Amount [{}] Exceeds The Upper Limit Of The Branch’S Promissory Note Issuance Amount [{}]!
PN6012=Please enter the accounting method when the court executes!
PN6013=Please enter the account information when the court executes!
#------------------------------------6------------end-----------------------------#

#------------------------------------7------------begin---------------------------#
PN7001=The bill is not issued and is not allowed to be redeemed!
PN7002=The bill is not issued and is not allowed to be modified or deleted!
PN7003=The bills is not reported as lost/frozen and cannot be released!
PN7004=The bill is not issued and is not allowed to be returned!
PN7005=The returned account information does not match the original issuing account information, and return is not allowed!
PN7006=This bill of exchange is not in entry status and review operations are not allowed!
PN7007=The account name of the payee entered in this bill of exchange is inconsistent with the account name of the payee for review, and the review operation is not allowed!
Pn7008=The Amount Entered In This Bill Of Exchange Is Inconsistent With The Review Amount, And The Review Operation Is Not Allowed!
PN7009=The teller who entered the issuance of this bill and the teller who reviewed it are the same teller, and review operations are not allowed!
PN7010=The account name of the applicant entered in this bill of exchange is inconsistent with the account name of the review applicant, and the review operation is not allowed!
PN7011=Inter-agency review is not allowed!
PN7012=The cashier's check must be paid in full!
PN7013=The settlement/redemption amount cannot be greater than the bill issuance amount!
Pn7014=Cash Drafts Are Not Allowed To Be Redeemed Across Branch!
Pn7015=The Original Transaction Information Was Not Found And The Deletion Operation Cannot Be Performed!
PN7016=This bill {} is no longer allowed to be paid!
PN7017=The bill is not expired and the payment operation is not allowed!
PN7018=This money order is not allowed to be returned across institutions!
PN7019=Cash drafts can only be paid in cash!
PN7020=Cash payment is not allowed for transfer orders!
PN7021=Cash drafts can only be issued in cash!
PN7022=Transfer bills are not allowed to be issued in cash!
PN7023=The return method of this money order must be consistent with the original issuance method!
PN7024=The bill is more than two years old from the issuance date and needs to be enforced through court execution!
PN7025=This bill is an expired bill, please pay the expired bill first!
#------------------------------------7------------end-----------------------------#
