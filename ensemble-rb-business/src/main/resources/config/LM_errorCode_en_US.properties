#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
LM3101=Operation type cannot be null
LM3103=Control type cannot be null
LM3104=Limit ID cannot be empty
LM3106=Account information cannot be empty
LM3107=ID information cannot be empty
LM3108=The current teller has no {} currency/voucher type information
LM4101=Limit type [{}] is undefined!
LM4102=The limit information corresponding to the limit code [{}] does not exist
LM4103=The account internal key [{}] corresponding to the account has set a transaction limit [{1}]
LM4104=The client [{}] has set a transaction limit [{1}]
LM4105=Account number [{}], the account corresponding to the account serial number [{}] has set transaction limit [{}]
LM6101=Limit detail data does not exist
LM6102=Limit detail data is illegal
LM6103=Duplicate limit details data
LM6104=The limit product is in use and cannot be deleted or modified
LM6105=Branch [{}] is not a subsidiary of this branch [{}]
LM6106=The limit information is [{}]
LM6107=The trail box [{}] is a cash trail box, and the certificate limit relationship cannot be established
LM6108=The trail box [{}] is the trail box of the voucher, and the cash limit relationship cannot be established
LM6109=Trailbox [{}] does not exist
LM6110=Only the head branch can establish/cancel the limit relationship for itself
LM6111=No record
LM6112=If the transaction quota for non-face-to-face III accounts under the client name exceeds the limit, please go to the counter to confirm client information as soon as possible.
LM6113=The limit category cannot be empty!