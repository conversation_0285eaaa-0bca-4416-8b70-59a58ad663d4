#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#

#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
AD4002 = 挂失编号不存在哦！
AD4101 = 查询无记录!
AD4102 = 票据不存在!
AD4103 = 票据状态不对,只有未备款票据可办理未备款退回!
AD4104 = 无银承信息！
AD4105 = 操作类型不能为空！！
AD4106 = 操作类型有误！
AD4107 = 挂失类型不正确！
AD4108 = 票据信息不存在！
AD4109 = 挂失类型应该为1-公告挂失 或 2-提起诉讼！
AD4110 = 挂失类型应该为 2-提起诉讼！
AD4111 = 承兑编号[{}]，不是承兑状态无法进行退回！
AD4114 = 该挂失人不是原挂失人，不允许修改挂失类型!
AD4115 = 挂失编号不能为空!
AD4116 = 挂失信息不存在！
AD4117 = 本票已挂失！
AD4118 = 该票据已完成预登记取消，不可再次进行预登记取消！
AD4119 = 该票据已承兑，不可进行预登记取消！
AD4120 = 该票据不为预登记状态，不能进行预登记取消！
AD4121 = 付款人账户不是结算账户类型！
AD4122 = 付款人账户已关闭！
AD4123 = 付款人账户不能是保证金账户！
AD4124 = 收款行在我行没有定义或被关闭！
AD4125 = 该票据已失效！
AD4126 = 该账户不是时保证金账户！
AD4127 = 该账户可用余额不足，无法冻结！
AD4128 = 担保金额之和不能大于出票金额！
AD4129 = 保证金数组不能为空！
AD4130 = 保证金账户序号不能为空！
AD4131 = 保证金账户产品类型不能为空！
AD4132 = 保证金账户币种不能为空！
AD4133 = 持票人账户不是结算账户！
AD4134 = 持票人账户已关闭！
AD4135 = 兑付账号不能是操作柜员的账号！
AD4136 = 该票据已兑付！
AD4137 = 该票据不是完成备款状态，不可进行兑付！
AD4138 = 票面金额不足，不可进行兑付！
AD4139 = 不能跨分行兑付！
AD4140 = 该汇票已挂失，不可进行兑付！
AD4141 = 该汇票未到期，不可进行兑付！
AD4142 = 兑付与兑付取消柜员不属于同一操作者！
AD4143 = 兑付取消必须为兑付当日！
AD4144 = 票据不为兑付状态，不可进行兑付取消操作！
AD4145 = 持票人非本行账户，不可进行兑付取消！
AD4146 = 开出本票总账账户不存在!
AD4147=  付款机构在我行未定义，请确认！
AD4148 = 票据非预登记状态,不允许承兑!
AD4149 = 票据非承兑状态,不允许做承兑取消!
AD4150 = 挂失只允许在承兑机构操作,请确认承兑机构!
AD4151 = 承兑取消票据与承兑票据号码不一致!
AD4152 = 票据状态有误，不允许进行此交易!
AD4153 = 银行承兑汇票票据更换的员必须与承兑柜员一致!
AD4154 = 只有银行承兑汇票承兑当天才能做银行承兑汇票票据更换操作!
AD4155 = 总账账户未开立！
AD4156 = 票据非预登记状态,不允许挂失!
AD4157 = 服务费数组输入错误！
AD4158 = 承兑交易不能在“统一冲正”进行冲正！
AD4159 = 已挂失票据不可做未到期退回！
AD4160 = 柜员不允许操作自己的账户！
AD4161 = 该机构没有配置对应的银承备款应解汇款账号！
AD4162 = 银承备款必须在承兑机构进行备款！ 
AD4163 = 电子银行承兑汇票需在电子商业汇票系统进行该操作！ 
AD4164 = 备款交易不能进行冲正！ 
AD4165 = 票据密押输入错误!
AD4166 = 出票日期与录入出票日期不符！
AD4167 = 票据金额与录入录入票据金额不符！
AD4168 = 票据密押未录入或不存在！
AD4169 = 出票日期未录入或不存在！
AD4170 = 票据金额与未录入或不存在！
AD4171 = 票据非签发或者解挂状态！
AD4172 = 票据超出提示付款期！
AD4174 = 票据机构不允许为空！
AD4175 = 银行本票号码不符!
AD4176 = 非本行票据!
AD4177 = 银行本票已挂失!
AD4178 = 未查询到票据信息！
AD4179 = 票据金额不能为空！
AD4243=不存在托收编号[{}]的委托收款的登记信息，不允许兑付！
AD4244=托收编号[{}]登记的金额与此票据金额不一致,不允许兑付！
AD4245=托收编号[{}]登记的票据号与此兑付的票据号不一致，不允许兑付！
AD4246=银承汇票承兑时，担保方式不能为空！
AD4247=当前汇票状态为非备款或者非承兑状态，不允许进行挂失！
AD4248=持票人账号必须和出票人账号一致！
AD4249=退回日期大于或等于系统日期，不能做未到期退回！
AD4230=改票据已经挂失，不能进行备款操作！
AD4231=进行丧失兑付，票据状态必须为挂失状态！
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#
AD5001 = 银承预登记时,付款人账号不允许为空！
AD5002 = 银承预登记时,付款人账户序号不允许为空！
AD5003 = 银承预登记时,付款人账户产品类型不允许为空！
AD5004 = 银承预登记时,付款人账户币种不允许为空！
AD5005 = 银承预登记时,出票通知书号不允许为空！
AD5006 = 银承预登记时,承兑协议编号不允许为空！
AD5007 = 银承预登记时,借据号不允许为空！
AD5008 = 银承预登记时,出票金额不允许为空！
AD5009 = 银承预登记时,担保方式不允许为空！
AD5010 = 保证金账户[{}]的担保金额必须大于0！
AD5011 = 出票通知书号[{}]已登记！
AD5012 = 银承（电票）预登记时,票据类型、票据号码必输！
AD5013 = 银承（电票）预登记时,承兑协议编号必输！
AD5014 = 银承（电票）预登记时,到期日必输！
AD5015 = 银承（电票）预登记取消时,票据状态必须为出票状态！
AD5016 = 工本费费用类型必输！
AD5017 = 票据已经挂失，不可重复挂失！
AD5018 = 当前票据状态为已兑付，不允许挂失！
AD5019 = 承兑取消的操作柜员必须与承兑操作柜员一致！
AD5020 = 承兑取消只能在承兑当天才能操作！
AD5021 = 承兑只能在预登记当天才能操作！
AD5023 = 该票据已挂失，不可做承兑取消操作！
AD5024 = 该票据已挂失，交易类型请选择[T121-银承丧失兑付]！
AD5025 = 票据状态正常，交易类型请选择[T115-银承正常兑付]！
AD5026 = 该票据状态为非已备款状态，不能进行兑付！
AD5027 = 该票据承兑机构与预登记机构不一致！
AD5028 = 票据未到期退回必须在原承兑机构进行！
AD5029 = 非同一挂失人！
AD5030 = 票据状态为挂失止挂，只能做公示催告挂失或提起诉讼！
AD5031 = 挂失出现异常！
AD5032 = 票据未挂失！
AD5033 = 解挂出现异常！
AD5034 = 开始日期不能大于结束日期！
AD5035 = 没有满足条件的记录！
AD5036 = 银承到期退回,票据状态必须为正常!
AD5037 = 请通过4402-银行承兑备款交易做备款！
AD5038 = 请通过4415-银行承兑汇票集中备款交易做备款！
AD5039 = 票据[{}]的付款人保证金和结算账户不存在！
AD5040 = 本票挂失人不能为本行员工
AD5041 = 本票解挂人不能为本行员工
AD5042 = 票据[{}]承兑申请人结算账户不存在,无法完成备款!
AD5043 = 保证金账户[{}]可用余额不足,无法完成备款!
AD5044 = 挂失编号不能为空！！
AD5045 = 起始日期或者终止日期不能为空！
AD5046 = 纸票才能够进行票据更换！
AD5047 = 银行承兑汇票必须是承兑状态才能做银行承兑汇票的票据更换！
#------------------------------------5------------end-----------------------------#

#------------------------------------6------------begin---------------------------#
AD6001 = 上送报文为空！
AD6002 = 请检查上送参数！
AD6003 = 请输入正确操作类型！
AD6004 = 请上送正确的票据介质类型！
AD6005 = 纸票签发时请上送票据凭证类型！
AD6006 = 请检查上送付款账户信息！
AD6007 = 请检查上送收款账户信息！
AD6008 = 请检查上送保证金账户信息！
AD6009 = 请检查参数配置‘BAD_RESTRAINT_TYPE’！
AD6010 = 限制类型[{}]不存在！
AD6011 = 上送出账流水号对应的银承协议信息不存在！
AD6012 = 上送备款账户数组为空！
AD6013 = 该出账流水号没有对应的备款账户！
AD6014 = 上送账户数组与出账流水号对应备款账户数组不一致！
AD6015 = 上送票据信息不存在！
AD6016 = 票据已存在保证金冻结信息，不允许新增！
AD6017 = 票据不存在保证金冻结信息，不允许修改、解限、冲正！
AD6018 = 保证金账户可用余额不足！
AD6019 = 出账流水号不能为空！
AD6020 = 出账流水号对应的承兑汇票信息不存在！
AD6021 = 出账流水号下没有可用票据信息！
AD6022 = 垫款标志必须上送!
AD6023 = 备款账户可用余额不足，备款失败!
AD6024 = 备款内部账户不存在!
AD6025 = 备款内部账户不可用!
AD6026 = 上送参数金额为空!
AD6027 = 出票金额为空!
AD6028 = 承兑金额必须大于0!
AD6029 = 票面金额必须大于0!
AD6030 = 出票金额不能大于总金额!
AD6031 = 个人账户不允许签发银行承兑汇票!
AD6032 = 付款账户与保证金账户必须为同一机构!
AD6033 = 找不到该承兑信息或已进行承兑取消!
AD6034 = 一笔出账流水号不允许存在多个保证金账户!
AD6035 = 定期保证金账户,票据到期日必须大于等于定期到期日!
AD6036 = 一个定期保证金账户只能对应一个出账流水号!
AD6037 = 该票已有无法重复签发
AD6038 = 柜员下无符合条件的凭证
AD6039 = 定期账户为空或者冻结信息不存在
AD6040 = 定期账户备款金额为0!
AD6041 = 付款人账户不可为空
AD6042 = 应付应解汇款账户不可为空
AD6043 = 备款数据为空或备款金额为空
AD6044 = 活期保证金账户为空或者冻结信息不存在
AD6045 = 活期保证金账户备款金额为0!
AD6046 = 活期结算账户为空
AD6047 = 活期结算户备款金额为0
AD6048 = 渠道类型不能为空
AD6049 = 产品类型不能为空
AD6050 = rbFinEbMapFrontList没有找到该操作场景的核算规则
AD6051 = rbFinEbMapDistinctList没有找到该操作场景的核算规则
AD6052 = rbFinEbMapOptionList没有找到该操作场景的核算规则
AD6053 = rbFinEbMapAllList没有找到该操作场景的核算规则
AD6054 = 参数账户类型不能为空
AD6055 = 参数账户类型不匹配或上送缺失
AD6056 = 无法判断折溢价
AD6057 = 参数产品类型有误
AD6058 = 买方/卖方付息借贷不平衡
AD6059 = 未去介质无法质押！
AD6060 = 存款备款成功但是垫款失败,请二次备款！
AD6061 = 承兑协议编号不能为空!
AD6062 = 票据状态不支持试算!
AD6063 = 营业日期不一致[{}]!
AD6064 = 操作类型为01时，总金额、币种、票据类型、票面余额、票据登记日期、票据承兑日期、票据到期日期中的其中任意一个都不能为空！
#------------------------------------6------------end---------------------------#









