#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#
#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
CD3001=Pre-authorization information does not exist
CD3002=Old card number or card replacement flag with the same number cannot be empty
CD3003=Amount Cannot Be Empty!
CD3004=Incorrect Completion Amount
CD3005=Card status is incorrect
CD3006=Card number cannot be empty
CD3007=[{}] The card number does not exist!
CD3008=The card number has been canceled!
CD3009=Card product name cannot be empty!
CD3010=Card printing quantity cannot be empty!
CD3012=Product definition cannot change cards with the same number!
CD3013=New card number cannot be empty!
CD3014=The original card number and the new card number cannot be the same!
CD3015=[{}] is not an additional card!
CD3016=Exceeded the maximum number of card printing applications, maximum number: [{}], actual quantity: [{}]!
CD3017=The maximum number of card application quantities is missing, please add product configuration! parameter name:[{}]
CD3018=The query condition cannot be empty!
CD3019=The account information associated with the unit settlement card cannot be empty!
CD3020=The default settlement account of the unit settlement card cannot be empty!
CD3021=The service level of the card product issued cannot be higher than the customer service level [{}]!
CD3022=Your Customer Service Level Is [{}]. Please Upgrade To A Higher Level Card Product. You Will Be Prompted Whether To Continue!
CD3030=The default settlement account flag of the unit settlement card cannot be empty!
CD3035=[{}] account attributes do not match!
CD3040=[{}] Duplicated input account!
CD3041=The loss report number cannot be empty!
CD3042=Certificate type cannot be empty!
CD3043=Certificate number cannot be empty!
CD3044=Issuing country cannot be empty!
CD3045=The reason for reporting loss cannot be empty!
CD3046=The starting reissue date cannot be empty!
CD3047=The voucher type cannot be empty!
CD3048=The voucher number cannot be empty!
CD3049=Certificate status cannot be empty!
CD3050=[{}] of [{}] after limit adjustment cannot be greater than the defined limit [{}]!
CD3051=Reset/unlock must be done at the account opening bank!
CD3052=The settlement card product type is empty!
CD3053=When changing the password, the original password is mandatory!
CD3054=When changing the password, you must input the new password!
CD3055=You must enter the new password when resetting the password!
CD3056=The password  must input when unlocking it!
CD3057=You must enter the new password when activating the card!
CD3058=The agent is not allowed to reset the password!
CD3059=There is only one link account that does not require collection. Please modify the automatic collection function first!
CD3060=Less than the minimum number for card printing application, minimum number: [{}], actual quantity: [{}]!
CD3061=This batch is not a lucky card, no allocation transaction is required!
CD3062=This batch of auspicious cards has not been signed for and is not allowed to be distributed!
CD3063=The card [{}] has been put into inventory and is not allowed to be allocated again!
CD3064=The card [{}] has been issued and is not allowed to be distributed again!
CD3065=This batch of non-auspicious cards does not require a signature transaction!
CD3066=This batch has been signed for, no further signing transaction is required!
CD3067=The Number Of Digits Is Too Long, The Application Number Cannot Exceed 7 Digits!
CD3068=This batch of cards has not yet been printed. Please sign for it after the card printing is completed!
CD3069=The card [{}] has not been signed for and is not allowed to be distributed!
CD3070=The number selection type must input
CD3071=When querying the number selection application, the institution number, application start date, and application deadline must be entered.
CD3072=When querying the selected number and card printing, one of the institution number, card number, and card printing date must be entered.
CD3073=Product type and voucher type do not match, please check
CD3074=The minimum certificate [{}] and the maximum certificate [{}] are not within the certificate range of the application number and cannot be stored in the database, please check!
CD3075=The Start Date Cannot Be Greater Than The End Date!
CD3076=Only One Type Of Query Condition Can Be Entered (Account Number, Card Number, Customer Number, Certificate Information)!
CD3077=Insufficient Digits In The Number Segment, The Application Number Cannot Be Less Than 7 Digits!
CD3078=The settlement card of an organization that signed a contract with one account and sub-accounts is not allowed to be charged to the account as a handling fee!
CD3079=Jixiang Card Signing Is Only Allowed At The Head Office!
CD3080=Jixiang Card Allocation Is Only Allowed By The Head Office!
CD3081=Registration Type Cannot Be blank
CD3082=If the card production status corresponding to the application number is neither "pending inventory" nor "partially inventoried," warehousing is not allowed.
CD3083=Application number does not exist, please check!
#------------------------------------3------------end-----------------------------#
CD4001=[{}] status does not exist!
CD4002=No record returned from query!
CD4003=The card printing file generation path is set incorrectly!
CD4004=Card product type [{}] is undefined!
CD4005=Card product type [{}] is undefined  [{}]!
CD4006=The Validity Period Of The Card Bin Certificate Is Not Defined!
CD4007=Card number [{}] already exists!
CD4020=[{}] card is not a corporate settlement card!
CD4021=The account status of the settlement account is incorrect!
CD4022=The unblocking mechanism must be consistent with the loss reporting mechanism!
CD4023=When Adding A New Associated Account, It Needs To Be Processed At The Account Opening Office Of The Associated Account!
CD4024=Please Enter The Correct Company Settlement Card!
CD4025=Lock type does not exist!
CD4026=Open flag does not exist!
CD4027=When using common land locks, common province information cannot be empty!
CD4028=Must be processed at the card issuer!
CD4029=The primary and secondary card marks are empty, please confirm the correctness of the card number!
CD4030=No card printing file/list to be generated, please check!
CD4031=Card printing exception, please try again later!
CD4032=The Password Of This Account [{}] Is Abnormal, Please Handle It!
CD4033=The card is not a self-selected card number or replace the card with the same number
CD4034=The account book number has been used!
CD4035=This account already has a reconciliation book!
CD4036=The internal number length of the self-selected card number is inconsistent with the system parameters.
CD4037=The card status does not allow cancellation of card production application
CD4038=The interval between the start and end dates of number selection application inquiry cannot exceed one year.
CD4039=There is no reconciliation book for this account!
CD4040=Wrong registration type upload!
CD4041=Whether to merge the printing logo and send it is wrong!
CD4045=[{}]Date Format Error!
#------------------------------------4------------begin---------------------------#
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
#------------------------------------6------------end-----------------------------#
#------------------------------------7------------begin---------------------------#
#------------------------------------7------------end-----------------------------#
CD6001=The unit settlement card must have one and only one default settlement account!
CD6002=Signed the One-account Unit Settlement Card Agreement, sub-accounts are not allowed to be maintained!
CD7001=[{}] Card making...
CD7002=[{}] The card has been canceled!
CD7003=[{}] The card has been locked due to CVN error!
CD7004=[{}] The card has been locked because the number of incorrect passwords for the day reached the upper limit!
CD7005=[{}] The card has been locked due to the accumulated number of incorrect passwords reaching the upper limit!
CD7006=[{}] The card password has expired!
CD7007=Failed to update file generation start time!
CD7008=[{}] file already exists!
CD7009=[{}] Card not issued!
CD7010=secondary card  [{}] does not allow this transaction!
CD7011=Inactive card [{}] does not allow this transaction!
CD7012=Supplementary Card [{}] Is Not Issued On Behalf Of Others!
CD7013=This product [{}] does not allow the issuance of secondary card s
CD7014=The number of secondary card s under this card [{}] has exceeded the upper limit [{}]!
CD7015=This transaction is not allowed for non-affiliated card [{}]!
CD7016=Card [{}] Lost Report Status Is Incorrect!
CD7017=Card product withdrawal range (WITHDRAW_RANGE) configuration should be CARD!
CD7018=[{}] Card Has Expired!
CD7019=[{}] Card Is Not Valid!
CD7020=[{}] The card has been reported lost in writing!
CD7021=[{}] The card has been reported lost verbally!
CD7022=[{}] Card Information Does Not Exist!
CD7023=[{}] The Card Effective Date Is blank!
CD7024=[{}] The Card Expiry Date Is blank!
CD7025=Card Number Information Cannot Be Found!
CD7026=The additional card limit [{}] set amount is less than the [{}] set amount!
CD7027=The card issuing institution must be the account opening institution of the company's settlement account!
CD7028=The unit settlement account is unclaimed 
CD7029=The unit settlement account is dormant
CD7030=The unit settlement account is pre-opened!
CD7031=The unit settlement account has been closed
CD7032=CD7032=The Number Of Corporate Settlement Cards Held By The Cardholder Has Exceeded The Limit!
CD7033=[{}]This card is not an organizational settlement card or a canceled card!
CD7034=The Company Card [{}] Does Not Match The Settlement Account [{}]!
CD7035=[{}]The card is voided!
CD7036=[{}]This card is not allowed to perform [{}]!
CD7037=When the card is canceled normally or damaged, the card status must be normal!
CD7038=When reporting loss or canceling a card, the loss report number cannot be empty!
CD7039=Card loss reporting is not allowed!
CD7040=The loss report number is invalid!
CD7041=The card cancelling nstitution and the card issuing institution must be the same institution!
CD7042=The Card Number And Certificate Information Are Not The Same Customer Information!
CD7043=Written report lost existed, no verbal report lost is allowed. 
CD7044=The Card Number And Certificate Information Are Not The Same Customer Information!
CD7045=The Loss Report Number And Certificate Information Are Not The Same Customer Information!
CD7046=The card number and loss report number are not the same customer information!
CD7047=Cardholder ID information does not exist!
CD7048=The currency of the unit's settlement card and the currency of the settlement account must be consistent!
CD7049=The currency of the unit settlement card must be local ccy [[}] !
CD7050=The Settlement Account Must Be A Basic Account Or A General Account!
CD7051=This settlement account is not a settlement account!
CD7052=This settlement account was not opened by our bank!
CD7053=This product is no longer valid!
CD7054=The detachment does not support agent handling!
CD7055=If the card is reported lost, please choose to report it lost or cancel it.
CD7056=This account has been suspended for a long time!
CD7057=The newly added account is different from the settlement account customer of the unit settlement card!
CD7058=The Cardholder Is A Blacklisted User And Is Not Allowed To Make Transactions!
CD7059=When selecting automatic collection, the automatic collection sequence cannot be empty!
CD7060=There is only one associated unit settlement account, and there is no need to set up automatic collection!
CD7061=The number of primary cards of the corporate settlement card held by this customer has exceeded the limit!
CD7062=The number of secondary card s of the corporate settlement card held by this customer has exceeded the limit!
CD7063=The Validity Period Of The Capital Verification Account Cannot Be blank!
CD7064=The default settlement account of the unit settlement card does not need to set the collection serial number!
CD7065=link accounts and non-current settlement accounts do not need to set collection serial numbers!
CD7066=The Main Card Number Is Not The Customer’S Commercial Card Number, Please Check!
CD7067=The main card number has been deactivated!
CD7068=The main cardholder’s ID type is incorrectly entered, please check!
CD7069=The main cardholder’s ID number is entered incorrectly, please check!
CD7070=The main cardholder name is entered incorrectly, please check!
CD7071=The contact number of the main cardholder is entered incorrectly, please check!
CD7072=When opening a secondary card, the ID type of the main cardholder cannot be empty!
CD7073=When opening a secondary card, the ID number of the primary cardholder cannot be empty!
CD7074=When opening a secondary card, the name of the main cardholder cannot be empty!
CD7075=When opening a secondary card, the contact number of the primary cardholder cannot be empty!
CD7076=The default account of this card is a general account and no withdrawal is allowed!
CD7077=The number of digits in the ID number is incorrect, please check!
CD7078=All 15 digits of the ID card number should be numeric; all 18 digits except the last digit should be numeric!
CD7079=The card number is incorrect, please check!
CD7080=The main card account has been canceled!
CD7081=The primary and secondary cards are not under the same customer number!
CD7082=This card is not a commercial card, please check!
CD7083=For the secondary card [{}], the single transaction limit [{}] cannot exceed the single transaction limit of the main card!
CD7084=For the secondary card [{}], the daily cumulative limit [{}] cannot exceed the daily cumulative limit of the main card!
CD7085=For the secondary card [{}], the monthly cumulative limit [{}] cannot exceed the monthly cumulative limit of the main card!
CD7086=Disabled commercial cards are not allowed to be reported as lost!
CD7087=Disabled commercial cards are not allowed to be unlinked!
CD7088=The default account is not allowed to be a personal account!
CD7089=Cardholder changes for commercial cards are only allowed at the account opening institution!
CD7090=The card number has been deactivated and this business cannot be processed!
CD7091=When selecting automatic collection, the collection sequence number cannot be empty!
CD7092=The collection sequence number cannot be negative!
CD7093=Please fill in the collection sequence number in the order of 1, 2, 3...!
CD7094=Please fill in the smallest collection sequence number starting from 1!
CD7095=[{}] The main card has been reported as lost and a secondary card cannot be issued!
CD7096=The default account cannot be a card number, please confirm!
CD7097=The primary and secondary cards cannot be the same cardholder!
CD7098=When opening a main card, the cardholder information must be consistent with the cardholder information of the main card!
CD7099=There is an uncancelled secondary card under the main card, and the card cannot be canceled!
CD7100=[{}]The card has not expired and cannot be replaced after expiration!
CD7101=This business needs to be handled by the original card issuer!
CD7102=The card has expired!
CD7103=[{}] The card has been officially reported lost!
CD7104=The transferred account has been transferred for a long time!
CD7105=Customer certificate has expired!
CD7106=The card number is already a multi-card account and cannot be associated with the account!
CD7107=This account is already in one-card multi-account status and no more cards can be opened!
CD7108=The entered new and old card numbers cannot be empty at the same time!
CD7109=Margin accounts are not allowed to open commercial cards!
CD7110=Internal suspense accounts are not allowed to open commercial cards!
CD7111=[{}]This card is controlled by the main card's authority and is not allowed to perform [{}]!
CD7112=When opening a secondary card, the main card number cannot be empty!
CD7113=Card printing information does not exist!
CD7114=Virtual card does not allow this transaction!
CD7115=Private customers are not allowed to issue corporate settlement cards!
CD7116=Customer Id Information Does Not Exist!
CD7117=The primary card has been deactivated and the secondary card is not allowed to be updated!
CD7118=The presiding cardholder does not enter the correct customer or certificate information incorrectly!
CD7119=The cardholder is not a customer in the bank or the certificate information is entered incorrectly!
CD7120=This unit card is the main card and cannot be replaced with a secondary card!
CD7121=The unit card default account number parameter [{}] is not configured!
CD7122=The number of default accounts for unit cards exceeds the upper limit of parameter configuration [{}]!
CD7123=The counterparty for unit card transfer is not the default account!
CD7124=The password registration book information does not exist!
CD7125=The number of unit settlement cards held by the same person exceeds the limit!
CD7126=Old password cannot be empty!
CD7127=New password cannot be empty!
CD7128=The signing of the One-account Self-owned Fund account Agreement does not allow the issuance of corporate settlement cards!
CD7129=The company card that signed the One-account Sub-account Agreement does not allow the issuance of secondary card s!

CD7150=The Card Printing File With Voucher Type [{}] Does Not Exist Under Branch [{}]
CD7151=The Voucher Number Segment [{}][{}] Does Not Exist Or Is Insufficient In Quantity Or Is Not In The Card-Printed Status
CD7152=The Number Of Files Does Not Match!
CD7154=The Voucher Number Segment [{}][{}] Does Not Exist In The [{}] Batch!
CD7130=[{}]File Rewriting Failed!
#------------------------------------8------------begin---------------------------#
#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#
#------------------------------------9------------end-----------------------------#

