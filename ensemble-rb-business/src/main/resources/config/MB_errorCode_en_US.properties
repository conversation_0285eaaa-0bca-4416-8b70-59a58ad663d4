#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#
MB0001=Business implementation object not found!
MB0002=Business data logging is abnormal!
MB2001=Mandatory authorization required!
MB3001=[{}]The product does not define the [{}] event!
MB3002=Please enter a trading account!
MB3003=Please enter the transaction currency!
MB3004=Balance type is empty or undefined balance type priority!
MB3005=Please enter the voucher type!
MB3006=The balance of the transfer-out account [{}] is insufficient!
MB3007=Please enter the transaction amount!
MB3008=Please enter the transaction type!
MB3009=The deposited amount cannot be negative!
MB3010=The deposited amount is not equal to the principal amount!
MB3011=Please enter the branch number!
MB3016=Interest rate marketization returns abnormal!
MB3017=The transaction amount cannot be zero!
MB3018=Interest rate marketization returns abnormally, no return information!
MB3019=Contact cannot be empty!
MB3020=Client ID cannot be empty!
MB3021=Account internal key cannot be empty!
MB3022=Protocol type cannot be null!
MB3023=The actual amount received in the service fee cannot be negative!
MB3024=The asset class balance [{}] is insufficient [{}], and credit and bookkeeping processing is not allowed!
MB3025=The balance of liabilities [{}] is insufficient [{}], and debit accounting is not allowed!
MB3026=The account number in line [{}] cannot be empty!
MB3027=The register type of line [{}] cannot be empty!
MB3028=The operation mode of line [{}] cannot be empty!
MB3029=The currency of the debit and credit account must be the same!
MB3030=PROD_TYPE cannot be empty in the business model!
MB3031=Please enter the account serial number (issue number)!
MB3032=The agreement number cannot be empty!
MB3033=Client ID and client ID information cannot be empty!
MB3034=The business module does not support this account, please re-enter!
MB3035=The account type is inreverse, please re-enter!
MB3036=This transaction cannot be selected for sub-account operation!
MB3037=The main account cannot be selected for this transaction!
MB3038=The account has no available balance and cannot be operated!
MB3039=The account has available balance and cannot be operated!
MB3040=Account has wrong public-private flag, please re-enter!
MB3041=Account status does not match, please re-enter!
MB3042=Account number does not meet the query conditions for this transaction, please re-enter!
MB3043=Please enter product type!
MB3044=The main transaction event of the transaction flow is empty, and reversal processing is not allowed!
MB3045=The transaction reference number cannot be empty!
MB3046=Wealth management products cannot be withdrawn regularly!
MB3047=Current event [{}] The main event of the transaction is empty, no transaction is allowed, please add the main event type in the FLOW layer!
MB3048=The serial information corresponding to the serial number [{}] was not found!
MB3049=The balance direction corresponding to the account is empty, and it cannot be reversed!
MB3050=The account [{}] is not bound to a Class I account and the balance is not 0, and the account cannot be cancelled. Please bind a Class I account first!
MB3051=The account [{}] is not bound to an I-type account and there is unsettled information, and the account cannot be cancelled. Please bind the I-type account first!
MB3052=The interest rate change information array cannot be empty!
MB3053=Transactions that report loss and withdrawal are not allowed to be reversed!
MB3054=Pre-open account can only do account information maintenance!
MB3055=Public accounts cannot be closed across branch!
MB3056=It is not allowed to report the loss and reissuance of private certificates!
MB3057=The certificate of cancellation and re-deposit can only be processed at the issuing branch!
MB3058=Amount type cannot be empty!
MB3059=Regular sub-accounts are not allowed to transfer across branch!
MB3117=Please configure the restriction type parameter!
MB3118=Cross-banking is not allowed for non-deposit deposit and withdrawal accounts!
MB3119=The client number of the newly opened regular account cannot be empty!
MB3120=The principal amount of a newly opened regular account must be greater than zero!
MB3121=The product type of the newly opened regular account cannot be empty!
MB3122=It is not allowed to issue a certificate of deposit across banks!
MB3123=This account has no expiration date!
MB3135=Operation type cannot be empty!
MB3136=The loan is already in interest-bearing status without compound interest!
MB3137=The loan is already in interest suspension status, no interest suspension required!
MB3201=Product [{}] does not allow cash withdrawals!
MB3202=The expiration date of the regular account is [{}], and the product [{}] does not allow early withdrawal!
MB3203=Product [{}] does not allow partial withdrawals!
MB3204=Product [{}] is not allowed to exchange!
MB3205=The product to which the account belongs is defined as compound interest, and partial withdrawal is not allowed!
MB3206=The happiness deposit product [{}] does not allow cash to be issued!
MB3207=Exceeded the maximum saleable amount of the current product [{}]!
MB3208=The client age is less than the applicable age of the product!
MB3209=The client age is greater than the applicable age of the product!
MB3210=This product is not allowed to be sold at this establishment!
MB3211=Protocol type cannot be empty!
MB3212=The balance of the regular account account [{}] is not 0, and the account cannot be cancelled. Please cancel the account from the regular withdrawal transaction!
MB3213=There are multiple sub-accounts under the reported loss account, and account cancellation is not allowed!
MB3214=Cancellation of accounts in multiple currencies is not allowed!
MB3215=Accounts of different currencies cannot be eliminated at the same time!
MB3216=The transaction flow [{}] has been reversed, and it is not allowed to reverse processing!
MB3217=The reversal type corresponding to the transaction type [{}] is not allowed to be empty!
MB3218=The account number is less than 6, please check!
MB3219=
MB3220=Debit and credit business is not allowed before the Type II/III account is verified!
MB3221=III types of accounts do not allow cash deposits!
MB3222=III types of accounts do not allow cash withdrawals!
MB3223=Transfer in and out of account cannot be the same!
MB3224=The unverified array of category III households cannot be empty!
MB3225=Account [{}] balance is not 0!
MB3226=Incorrect operation type!
MB3227=[01, 03, 04, 05, 06] Type verification must fill in the disposal method!
MB3228=When the verification status is 06, the reason cannot be verified Required!
MB4047=The deductible amount or the number of transactions in the fee package is insufficient!
MB4048=The currency of the income account must be the same as that of the contracted package definition currency!
MB4049=The effective date cannot be later than the end date defined by the plan
MB4050=There is no cost plan with plan code [{}]
MB4051=There is no fee package contract with agreement number [{}]!
MB4052=[{}]The package has been terminated, this transaction is not allowed!
MB4053=client information does not exist!
MB4054=The client has arrears, please make up!
RB4100=The client type does not match the client type defined by the fee plan
MB4101=Fee plan for plan code [{}] is not available!
MB4102=The effective date cannot be earlier than the current system date!
MB4103=The effective date must be before the expiry date!
MB4104=The expiration time of the contracted package cannot exceed the expiration time in the package parameter definition table!
MB4105=The number of cycles calculated based on the expiration date must be an integer multiple of the package frequency in the cost package!
MB4106=The deduction account must be a valid current settlement account under the same client name!
MB3311=The beneficiary account must be a valid settlement account under the same client name!
MB3312=The currency of the debit account is inconsistent with the currency of the package!
MB3313=Repeat signing package [{}]!
MB3314=There are duplicate fee types within the contracted package!
MB3315=Fee type [{}] does not exist!
MB3316=One of the certificate information, client number, and certificate information must be uploaded!
MB3317=Execution rate cannot be empty!
MB3318=Account code cannot be empty!
MB3319=Document information is incomplete!
MB3320=Cash transactions are not allowed to reverse every other day!
MB3321=Client information is not shared among legal entities, and transactions are not allowed!
MB3322=Account serial number cannot be empty!
MB3323=The account with the flag 'Suspend and Write-off' is 'Y' does not support internal account bookkeeping.
MB3324=Source of funds does not support fix deposit account.
MB3325=Source of funds does not support general deposit account.
MB3326=Regular withdrawal is not allowed for internal account bookkeeping.
MB3334=The transaction [{}] has not been reversed, and further corrections are not allowed.
MB4001=The [{}] line information already exists!
MB4002=The [{}] line information has not been entered yet!
MB4003=The transaction client must be a corporateorate client!
MB4004=Invalid account number or account status!
MB4005=The [{}] transaction type is invalid!
MB4006=Client information does not exist!
MB4007=Event type does not exist!
MB4008=The long-suspended accounting status does not exist!
MB4009=This approval already exists!
MB4010=The custom account has been used, please select again!
MB4011=When customizing an account, the account information cannot be empty!
MB4012=The approval document must be entered in the process of opening an account in public foreign currency!
MB4013=The client information corresponding to the approval document is inconsistent with the client information!
MB4014=Approval information [{}] does not exist!
MB4015=Approval information [{}] has expired!
MB4016=Unable to find the account, please check the four elements of the account!
MB4017=Non-scheduled products cannot do this transaction!
MB4018=The query returns no records!
MB4019=Client [{}] has not signed a temporary no-fee agreement!
MB4020=Open an education savings account, the principal amount cannot exceed 20,000 yuan!
MB4021=The customized account can only contain numbers and letters, the length cannot be less than 15 digits, and the last two digits must be digits!
MB4022=Client [{}] did not sign up for a package with fee [{}]!
MB4023=The repayment method of the loan product [{}] is incorrectly configured and cannot be multiple methods!
MB4024=The new printing page number cannot be greater than the maximum printing page number [{}] of the passbook!
MB4025=The new printing line number of the current page cannot be greater than the maximum printing line number [{}] of the passbook!
MB4026=Account [{}] is not associated with a passbook!
MB4027=Please enter a valid page row value!
MB4028=Account [{}] does not exist or does not support this transaction!
MB4029=Transaction type [{}] borrowing direction [{}] is inconsistent with event [{}]!
MB4030=After adjusting the accrued interest, the total interest [{}] is less than 0, please re-enter the adjusted interest!
MB4031=The voucher already exists in the check refund registration!
MB4032=The minimum deposit period corporate for education savings is "month", please check the deposit period type!
MB4033=The principal amount for opening an education savings account must be less than or equal to [{}] yuan!
MB4034=The restriction level corresponding to the transaction type [{}] is inreversely defined!
MB4035=[{}]This transaction is not allowed for accounts with no own funds!
MB4036=[{}] The account is not an all-in-one account or an all-in-one account has expired!
MB4037=[{}]The all-in-one account settlement mode does not allow this transaction!
MB4038=The [{}] account has opened an all-in-one account!
MB4039=The transaction flow with the serial number [{}] has been adjusted and cannot be adjusted again!
MB4040=The [{}] one-to-one account false and real sign does not exist!
MB4041=For real accounts, please go to the current transfer transaction for transfer processing!!
MB4042=For non-FTZ corporateorate accounts, when the nature of the account is under the capital account, the account approval number must be lost!!
MB4043=For a corporateorate RMB account in the Free Trade Zone, when the nature of the account is under the capital account, the account approval number must be lost!!
MB4044=This transaction is only applicable to the smart deposit ladder type use!
MB4045=The function parameter cannot be empty!
MB4046=This product is not allowed to change the term!
MB4055=Account [{}] has not signed up for all-in-one account !
MB4056=The new printing line number of the current page cannot be less than the minimum printing line number [{}] of the passbook!
MB4057=[{}], the card account mark [{}], there is no current settlement account!
MB4058=The number of account arrays for a single account is inconsistent with the number of transaction model arrays or is empty!
MB4059=The item [{}] of the account array of a single account is empty!
MB4060=The item [{}] of the transaction array for one account is empty!
MB4061=Rate type does not exist! intClass: [{}], intType [{}]
MB4062=The set limit exceeds [{}]!
MB4063=Adjustment between sub-accounts must be sub-accounts of the same level, account [{}] and account [{}] do not meet the requirements!
MB4064=The adjustment between sub-accounts must be the same as the upper-level account. Account [{}] and account [{}] do not meet the requirements!
MB4065=It is impossible to know whether the client type is to be checked for approval!
MB4066=New approval documents across legal entities are not allowed!
MB4067=The number of pages to be adjusted cannot be greater than the maximum number of printed pages of the account book!
MB4068=The number of adjustment lines cannot be greater than the maximum number of printed lines in the reconciliation book!
MB4069=Adjust the number of pages to send error!
MB4076=The account has not signed a temporary fee free agreement!
MB5001=The transaction exceeds the maximum forward days. The maximum number of days allowed for this transaction is [{}]!
MB5002=The transaction exceeds the maximum expiration days. The maximum number of days allowed for this transaction is [{}]!
MB5003=This number has already been used, and the main limit currency cannot be modified!
MB5004=This number is already in use and cannot be deleted!
MB5005=This number has already been used, and the client number cannot be modified!
MB5006=This number has already been used, and the sub-limit currency cannot be modified!
MB5007=The sub-limit amount cannot be greater than the approval limit!
MB6001=Account withdrawal password is wrong!
MB6002=The current accounting date is not within the validity period!
MB6003=The notification withdrawal date is less than the current date of the system!
MB6004=The sub-limit currency of the approval document is the same as the main limit currency!
MB6005=Line [{}] notification date cannot be less than the account opening date!
MB6006=The notification amount on line [{}] cannot be less than the minimum withdrawal amount!
MB6007=This product is not allowed to deposit!
MB6008=This product does not allow currency exchange!
MB6009=Cash transactions cannot be charged every other day!
MB6010=The correspondence between the ticket information and the account number is illegal!
MB6011=The ticket reminds that the date of issuance is more than 10 days, and no transaction is allowed!
MB6012=Prompt ticket date cannot be empty!
MB6013=The currency in the bill transaction must be RMB!
MB6014=The interest has been settled on that day, and the interest cannot be repeated!
MB6015=The account [{}] is not a Class I account!
MB6016=Class II or Class III accounts are not linked to Class I accounts, and account opening is not allowed!
MB6017=Channel [{}] trading prohibited
MB6018=Product [{}] channel [{}] single-day limit cannot be greater than [{}]
MB6019=Product [{}] channel [{}] transactions have reached the maximum limit of the day
MB6020=Trading agency [{}] prohibits trading of this product [{}]
MB6021=Product [{}] account opening must submit sub-account information, please check the interface data, the transaction is terminated!
MB6022=Account opening for products with non-zero balance, transaction information must be sent, please check the interface data, transaction termination
MB6023=Type of service not provided
MB6024=The account [{}] has no settlement, please settle it first!
MB6025=The product [{}] is missing interest information when opening an account, please check the uploaded data!
MB6026=Sub product [{}] is not defined in the combined product [{}]!
MB6027=Open account with non-zero balance, please enter the transaction type and other information
MB6028=Debit account does not exist
MB6029=Credit account does not exist
MB6030=Effective date cannot be empty
MB6031=Account opening date cannot be empty
MB6032=Account property cannot be empty
MB6033=Inactive and newly created accounts are not allowed to be upgraded!
MB6034=If the verification status of the II/III account is not verified or the verification fails, it is not allowed to be promoted or demoted!
MB6035=Transfer currency and transfer account currency are inconsistent!
MB6036=Failed to get the opType of the settlement and sale transaction type [{}], please check the transaction type configuration!
MB6037=Failed to obtain [{}] counterparty transaction type, please check the transaction type configuration!
MB6038=Process routing failed, please check!
MB6039=The ticket date cannot be greater than the current date!
MB6040=The current account signed with the financial management agreement is not allowed to be corrected!
MB6041=A portion of the arrears has been collected, and no reversal are allowed. The fee type is [{}]!
MB6042=One of the arrears has been reversed, and it is not allowed to reverse again. The fee type is [{}]!
MB6043=The ticket date cannot be greater than the current date!
MB6044=The terms does not match the terms of the product!
MB6045=Product [{}] is not allowed to be transferred, please modify the transfer flag!
MB6046=This account has account restrictions and cannot sell vouchers!
MB6047=This account has been downgraded and cannot be upgraded!
MB6048=The transaction has crossed the settlement date and is not allowed to be reversed!
MB6049=The lump sum withdrawal and education savings cannot be reversed!
MB6050=Card products can only open a personal settlement account!
MB6051=Account upgrades are only available for Class II/III accounts!
MB6052=Type II accounts can only be upgraded to Type I accounts!
MB6053=Type III accounts can only be upgraded to Type II/I accounts!
MB6054=Only Type II/III accounts can be maintained!
MB6055=When the operation type is new or modified, the bound account information must be entered!
MB6056=The restraints corresponding to the restraints number [{}] has expired or has been lifted!
MB6057=The main account information does not exist!
MB6058=The term does not match the product term!
MB6059=Cash transactions are not configured with balance increase and decrease signs!
MB6060=The current account [{}] has a fixed account that has not been cancelled under the card, and it cannot be extended for a long time!
MB6061=The deposit account [{}] is not active and internal billing is not allowed!
MB6062=[{}] is an internal current account, and no interest rate is allowed!
MB6063=Regular account [{}] does not allow reversal every other day!
MB6065=The value date must be greater than or equal to the account opening date!
MB6066=The withdrawal amount of the reverse value interest must be less than the daily EOD balance of the account!
MB6067=The debit or credit flag cannot be empty!
MB6068=Inverted value and interest reversal, the effective date must be greater than or equal to the account opening date and less than or equal to the current date!
MB6069=For reverse value and reversal, the amount of reversal withdrawal must be less than the end-of-day balance of the account!
MB6070=The effective date of the correction transaction must not be less than the value date or the account opening date!
MB6071=Correction transaction effective date cannot be empty!
MB6072=The settlement account of the free trade zone account must be a free trade zone account!
MB6073=corporateorate clients belonging to the Free Trade Zone can only open accounts at branch in the Free Trade Zone, and corporateorate clients outside the Free Trade Zone can only open accounts at branch outside the Free Trade Zone!
MB6074=The settlement account of the free trade zone account must be the same free trade zone account!
MB6075=For non-type III accounts, non-binding accounts are not allowed to deposit funds and sign contracts!
MB6076=The account has not been verified, and no non-binding account deposit contract is allowed!
MB6077=The account has been verified face-to-face, and there is no need to sign an unbound account deposit!
MB6078=The account is not transferred from the bound account, and the contract is not allowed!
MB6079=The client non-face-to-face verification that the account has been signed and the number of unbound accounts to deposit funds exceeds the limit, and no signing is allowed!
MB6080=The client has verified that the number of unsigned accounts exceeds the limit, and no signing is allowed!
MB6081=The account has not signed an unbound account deposit and cannot be terminated!
MB6082=For non-type III accounts, category III account identity verification is not allowed!
MB6083=Account is not verified, this transaction is not allowed!
MB6084=The account is already signed, no need to sign up again!
MB6085=Type III accounts opened over the counter do not need to sign an unbound account deposit!
MB6086=It has been verified that Type III accounts are not allowed to sign contracts for unbound account deposits!
MB6087=client ID cannot be empty!
MB6088=The Type I account bound to the Type II/III account must be a settlement account!
MB6089=Account verification status cannot be empty!
MB6090=Cross company inquiries are not allowed!
MB6091=Cross company transactions are not allowed!
MB6092=This product type does not allow cross-legal queries!
MB6093=The current transaction only supports cross-legal operations!
MB6094=The current transaction only supports the cancellation of the original teller in the original branch!
MB6095=The currency of the settlement account must be the same as that of the II/III account!
MB6096=[{}]The contract product is not allowed to cancel the contract midway!
MB6097=[{}]The agreement has come into effect, no modification is allowed!
MB6098=The secondary value-added frequency cannot be less than the value-added frequency!
MB6099=Account does not allow overdraft!
MB6100=The renewal deposit amount does not match the number of missed deposits!
MB6101=The debit account cannot be empty!
MB6102=The credit account cannot be empty!
MB6103=The account opening date does not match the transaction date!
MB6104=There is contract information in the account, downgrade is not allowed! !
MB6105=Account [{}] is an interest credit account, downgrade is not allowed!
MB6106=Account [{}] is the account where the principal is deposited, and no downgrade is allowed!
MB6107=Account [{}] is a bound account, downgrade is not allowed!
MB6108=Binding an account and opening an account are not the same person
MB6109=The account is cancelled or has been suspended for a long time, and binding is not allowed!
MB6110=The account has been opened with a deposit certificate, and binding is not allowed!
MB6111=The [{}] rate approval information does not exist!
MB6112=The account has an interest rate approval form and cannot be added!
MB6113=Date conversion exception!
MB6114=The account does not have a historical interest rate approval form and cannot be continued!
MB6115=[{}]The tracking number already exists!
MB6116=Channel [{}] does not exist!
MB6117=This channel [{}] cannot be used for account activation!
MB6118=Corporates issued in batches cannot be activated at {}!
MB7001=Account [{}] and currency [{}] must correspond to a unique clearing account!
MB7002=The settlement account corresponding to the branch [{}] has not been opened, so this transaction cannot be operated!
MB7003=The long and short payment has been processed, and the long and short payment cannot be reversed
MB7004=This branch cannot make this transaction!
MB7005=The current account [{}] does not have its own funds account, and interest cannot be credited!
MB7006=The current account [{}] does not have its own funds account, and the difference interest cannot be credited!
MB7007=Main account and sub account clients must be the same!
MB7008=Main account and sub account clients must be different!
MB7009=The execution interest rate of the sub-account cannot be higher than the execution interest rate of the superior account!
MB7010=This one-person account [{}] has opened its own funds sub-account!
MB7011=Please open a self-funded sub-account of [{}]!
MB7012=The current account [{}] has no largest account, and the difference interest cannot be credited!
MB7013=The current account [{}] has no minimum account, and the difference interest cannot be credited!
MB7014=The current account [{}] is a virtual account, and no manual settlement is allowed!
MB7015=Sub-accounts opened do not allow interest calculation!
MB7016=The parent branch [{}] for liquidation is not configured!
MB7017=The settlement account corresponding to the branch [{}] is not configured!
MB7018=Supplementary cards cannot do this transaction!
MB7019=The corporate settlement card cannot do this transaction!
MB7020=The corporate billing card [{}] does not allow this transaction!
MB7021=[{}] is a non-trading branch and this transaction is not allowed!
MB7101=Non-corporateorate settlement accounts are prohibited from signing contracts!
MB7102=Account [{}] is signed up!
MB7103=Operator [{}] does not exist!
MB7104=The operator password is wrong!
MB7105=Please enter the manager's ID number or phone number!
MB7106=Commercial cards cannot do this transaction!
MB7107=There is a commercial card [{}] associated with this account!
MB7108=The client has a margin account [{}][{}][{}][{}]!
MB7109=The account [{}][{}][{}][{}] has uncollected transactions!
MB7110=The current branch [{}] did not find the interest rate configuration information!
MB7111=The password-free function has been activated for this card, and it is forbidden to activate it again!
MB7112=The card has turned off the password-free function, and it is forbidden to turn it off again!
MB7113=The password-free function of this card has been closed, please enable this function first!
MB7114=The daily password-free quota must be greater than or equal to the single password-free quota!
MB7115=[{}]This card has no small-amount password-free signing information!
MB9000=Interest rate marketization returns abnormally, please contact the system administrator!
MB9001=[{}] Event has no implementation defined, please contact system administrator!
MB9011=Non-one account or account does not exist!
MB9012=No master account information!
MB9013=Check for no eligible transaction pipeline!
MB9014=There is no master account associated with the inquiry account!
MB9015=Serial number: [{}] Non AIO account transaction history!
MB9016=No transaction history!
MB9017=Serial number: Account in [{}]: [{}] is inconsistent with query account: [{}]!
MB9018=Account Mode does not exist!
MB9019=There is a sub-account at the lower level, and account cancellation is not allowed!
MB9020=There is no master agreement for this account!
MB9021=This transaction cannot be done on a sub-account of One Account!
MB9022=The self-owned funds sub-account also has accounts at the same level, please do account cancellation for other accounts at the same level first!
MB9023=The account cannot be cancelled here, please check the account level and go to the corresponding cancellation transaction to cancel the account!
MB9024=The transaction flow is not allowed to be reversed!
MB9025=[{}]Related transaction flow is not allowed to be reversed!
MB9026=Overdue [{}] is not allowed to reverse!
MB9027=corporateorate accounts cannot handle this business across branch!
MB9028=The total amount transferred out is not equal to the total amount transferred in!
MB9029=The information about the balance to be adjusted does not exist!
MB9030=Account [{}] is not a sub-account of [{}]!
MB9031=There cannot be more than one transfer-out account and transfer-in account at the same time!
MB9032=The account [{}] has not signed a contract for one account or the agreement has expired!
MB9033=Account [{}] has no credential information!
MB9034=There are multiple vouchers for the account, please submit the voucher information!
MB9035=The submitted voucher information is incorrect, please check!
MB9036=The corresponding registration information was not found, please check the submitted information!
MB9037=Found multiple registration information, please check the submitted information!
MB9038=Media has been removed, deletion is not allowed!
MB9039=Operation type cannot be empty!
MB9040=Account cannot be empty!
MB9041=No appointment registration record found!
MB9042=The outgoing account and the incoming account cannot be the same!
MB9043=The transfer account cannot be repeated!
MB9044=The transfer to the account cannot be repeated!
MB9045=Contract type does not exist!
MB9046=The medium has been removed, and repeated registration is not allowed!
MB9047=The settlement frequency has not been uploaded!
MB9048=No deposit rate found!
MB9049=Transfer transactions are not allowed to be corrected!
MB9050=The withdrawal amount cannot be empty!
MB9051=Account balance is insufficient!
MB9052=It is not allowed to create sub-accounts in the sub-account of Yihutong's own funds!
MB9053=The transaction flow [{}] that has been reversed is not allowed to be reversed again!
MB9062=Account risk level is not undefined and do not open an account！
MB9304=The binding account customer number [{}] and the maintenance account customer number [{}] are not the same customer!
MB9306=The account with Account Number [{}] and Account Sequence Number [{}] has already been associated with a seal card!
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
#------------------------------------6------------end-----------------------------#
#------------------------------------7------------begin---------------------------#
