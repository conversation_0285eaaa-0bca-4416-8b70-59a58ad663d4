#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
TB3101=Tail Box Array Cannot Be blank
TB3102=Tail Box Id Cannot Be blank
TB3103=The Branch To Which The Tail Box Belongs Cannot Be blank.
TB3104=Tail Box Attribute Cannot Be blank
TB3105=The Tail Box Allocation Plan Array Cannot Be blank
TB3106=Start Date Cannot Be blank
TB3107=Plan Id Cannot Be blank
TB3108=Illegal Operation Type
TB3109=The Accessory Item Information Array Cannot Be blank
TB3110=The Name Of The Accessory Item Cannot Be blank
TB3111=Accessory Item Id Cannot Be blank
TB3112=Plan Details Array Cannot Be blank
TB3113=Teller Id Cannot Be blank
TB3114=Tail Box Id And Teller Id Cannot Be blank At The Same Time
TB3115=The Query Date Cannot Be blank
TB3116=Currency Cannot Be blank
TB3401=Warehousing Teller Cannot Be blank
TB3402=The Other Party'S Branch Type Cannot Be blank
TB3403=Tracking Number Cannot Be blank
TB3404=Outbound Teller Cannot Be blank
TB3405=The Outgoing Branch Cannot Be blank
TB3406=The Warehousing Branch Cannot Be blank
TB3407=Teller Cannot Be blank
TB3408=The Reservation Number Cannot Be blank
TB3409=Reservation Date Cannot Be blank
TB3410=Reservation Agency Cannot Be blank
TB3411=Reservation Teller Cannot Be blank
TB3412=The Voucher Information Array Cannot Be blank
TB3413=The Other Branch Cannot Be blank
TB3414=Certificate Information Query, Branch Parameters Cannot Be blank
TB3415=Voucher Information Query, The Trunk Number And Teller Number Cannot Be blank At The Same Time
TB3416=Voucher Transfer Type Cannot Be blank
TB3417=Organization number cannot be empty
TB3418=The voucher type cannot be empty!
TB3419=The Associated Key Value Cannot Be blank
TB3420=Whether To Review Cannot Be blank
TB3421=The Reconciliation Id Cannot Be blank
TB3422=The starting number of the voucher cannot be emptyTB3423=凭证终止号码不能为空
TB3424=Certificate Change Status Cannot Be blank
TB3501=Cash Array Is blank
TB3502=The Other Party’S Customer Number Cannot Be blank
TB3503=The Ticket Type Cannot Be blank;
TB3504=Cash Transfer Tracking Number Cannot Be blank
TB3505=Cash Dispensing Teller Cannot Be blank
TB3506=Account/Card Number Cannot Be blank
TB3507=Balance Type Cannot Be blank
TB3508=Account Type Cannot Be blank
TB3509=Account Currency Cannot Be blank
TB3510=The Other Party Cannot Be The Applicant Branch
TB3511=No Corresponding Query Result
TB3512=Reservation Line [{}] Does Not Define The Counterparty Branch
TB3513=The Account Number Cannot Be blank
TB3514=Unit Information Cannot Be blank
TB3515=The Account Number In Line [{}] Of Unit Information Cannot Be blank.
TB3516=The Unit Information Account Name Cannot Be blank
TB3517=The Account Type In Line [{}] Of Unit Information Cannot Be blank.
TB3518=The Account Currency In Line [{}] Of Unit Information Cannot Be blank.
TB3519=Unit Information Branch Code Cannot Be blank
TB3520=Unit Information Already Exists
TB3521=Unit Information Does Not Exist
TB3522=Setting Type Cannot Be blank
TB3523=Setting Information Cannot Be blank
TB3524=The Line Code In Line [{}] Of The Setting Information Cannot Be blank.
TB3525=The Line Name In Line [{}] Of The Setting Information Cannot Be blank.
TB3526=The Line Name In The [{}] Line Of The Setting Information Cannot Be blank. Please Set The Cash Car Route With The Line Code [{}] First.
TB3527=The Branch Number In Line [{}] Of The Setting Information Cannot Be blank.
TB3528=The Branch Name In Line [{}] Of The Setting Information Cannot Be blank.
TB3529=The Branch Line Of [{}] Already Exists
TB3530=The Line Code Of Line [{}] Does Not Exist
TB3531=The Branch Line Of [{}] Does Not Exist
TB3532=Query Type Cannot Be blank
TB3533=There Is An Branch Under The Line [{}] Of The Setting Information. Please Delete The Branch Under The Line.
TB3534=Income Details Cannot Be blank
TB3535=The Credit Coupon Information Cannot Be blank
TB3536=The Door-To-Door Collection Appointment Has Been Entered
TB3537=The Actual Amount Entered Cannot Be blank
TB3538=Customer Reservation Amount Cannot Be blank
TB3539=The Reviewed Amount Does Not Match The Entered Amount;
TB3540=Cleaning Information Cannot Be blank
TB3541=The Banknote Information Cannot Be blank
TB3542=The Amount Of Money Added Cannot Be blank
TB3543=The Current Branch Cannot Force Itself To Close Its Doors.
TB3544=The Forced Closing Mechanism Cannot Be In The Closed State
TB3545=The Branch Forced To Close Must Be A Subordinate Branch Of The Current Branch
TB3546=The [{}] Line Of Unit Information Does Not Exist Or The Door-To-Door Collection Unit Account Cannot Be Modified.
TB3547=Branch Type [{}] Is Not Defined
TB3548=The Voucher Status Cannot Be blank!
TB3549=The Seal Card Information Cannot Be blank!
TB3550=The Following Signature Cards Contain Borrowed Signature Cards.
TB3551=Undefined For Private Client Type
TB3552=The Certificate Has Been Lent And Cannot Be Destroyed.
TB3553=Cash Deposit Details Cannot Be blank
TB3554=The Branch And Teller Information Do Not Match!
#4：检查类提示：数据有效性检查
#（如格式、存在、不存在、不在有效值范围等）
TB4103=Tail Box Id[{}] Already Exists
TB4105=The Branch Already Has An Branch Tailbox [{}]
TB4106=Tail Box Id[{}] Does Not Exist
TB4107=Cannot Modify Or Delete Operations, There Is No Tail Box Allocation Plan For The Day
TB4108=No Corresponding Trunk Allocation Plan
TB4109=Tail Box Status Is Not Unused
TB4110=The Status Of The Tail Box [{}] Is Not In Use.
TB4111=Teller [{}] Has No Corresponding Trunk
TB4112=This Mechanism Does Not Have A Corresponding Tail Box
TB4113=No Corresponding Tail Box
TB4001=Language [{}] Parameter Group [{}] Parameter [{}] Does Not Have A Corresponding Parameter Setting Value
TB4002=Voucher Type [{}] Does Not Exist
TB4401=Invalid Operation Type [{}]
TB4402=Teller [{}] Has No Corresponding Trunk Box Of This Branch.
TB4403=Teller[{}] Tail Box Type Must Be Branchal Tail Box
TB4404=The Voucher Transfer Information Corresponding To The Voucher Tracking Number [{}] Does Not Exist
TB4405=The Number Of Inbound And Outbound Vouchers Is Not Equal
TB4406=Voucher Type [{}] Is Not Defined
TB4407=The Voucher Type [{}] Is Not Within The Valid Date Range, And The Accounting Date Is [{}]
TB4408=Voucher [{}] Face Value Is Invalid
TB4409=Voucher [{}] Face Value Does Not Exist
TB4410=Invalid certificate [{}] start number [{}] and end number [{}]
TB4411=Certificate [{}] Requires A Prefix
TB4412=Credentials[{}] Undefined Prefix
TB4413=The Length Of The Voucher Number [{}][{}] Exceeds The Length Defined By The Voucher Parameters[{}]
TB4414=The Information Corresponding To The Voucher Reservation Number [{}] Does Not Exist
TB4415=The Reservation Voucher [{}] Is Inconsistent With The Quantity Of The Delivery Voucher
TB4416=Credential [{}] Is Not In The Branch Trunk Or Has A Status Of Unavailable
TB4417=Teller[{}] Trunk Type Must Be Teller Trunk Type
TB4418=Please Use The Vouchers In Order, The Current Minimum Number Is [{}]
TB4419=Certificate [{}] Is Not Valid
TB4420=Certificate [{}] Has Expired
TB4421=Invalid Reconciliation Type [{}]
TB4422=Invalid Reservation Type [{}]
TB4423=The Other Party'S Branch Type [{}] Is Invalid
TB4424=The voucher corresponding to the voucher type [{}] prefix [{}] voucher number [{}] does not exist
TB4425=The Voucher Start Number And End Number Are Entered Incorrectly
TB4426=Start Number [{}] End Number [{}] There Are Already Stored Vouchers In This Voucher.
TB4427=The Voucher Information Corresponding To The Voucher Start Number [{}] To The End Number [{}] Of The Voucher Type [{}] Prefix [{}] Is Duplicated
TB4428=The Voucher Information Corresponding To The Voucher Start Number [{}] To The End Number [{}] Of The Voucher Type [{}] Prefix [{}] Does Not Exist
TB4429=The Voucher Status Of Voucher Type [{}] Prefix [{}] Voucher Number [{}] Status [{}] Is Incorrect.
TB4430=The Voucher Transfer Details Corresponding To The Voucher Tracking Number [{}] Do Not Exist
TB4431=The Voucher Data Corresponding To The Tail Box Number [{}] Does Not Exist
TB4432=No Corresponding Cash Truck Line
TB4433=[{}]The Credentials Are Not Used In Order, Please Authorize!
TB4441=[{}]This card is an auspicious card number and requires authorization!
TB4501=Teller [{}] Is Not The Warehouse Clerk Of This Branch
TB4502=Branch [{}] Cash Trunk Does Not Exist
TB4503=Branch [{}] Cash Trunk Is Not Unique
TB4504=The Record Corresponding To The Reservation Number [{}] Does Not Exist Or Is Not Confirmed
TB4505=The Reservation Number [{}] Cash And Outgoing Cash Information Are Inconsistent
TB4506=The Reservation Date Has Expired
TB4507=Cash Tail Box [{}] Does Not Have The Amount Corresponding To The Currency Of [{}];
TB4508=The Amount Corresponding To The [{}] Currency In The Cash Trunk [{}] Is Insufficient;
TB4509=Ticket Number [{}] Does Not Exist In TBparvalue
TB4510=The Coupon Type [{}] Currency [{}] Data In The Cash Tail Box Balance List Does Not Exist;
TB4511=The Record Corresponding To The Reservation Number [{}] Does Not Exist Or Has Not Been Shipped.
TB4512=The Cash Transfer Tracking Number [{}] Does Not Match The Entered [{}]
TB4513=The Record Corresponding To The Cash Transfer Tracking Number Does Not Exist
TB4514=The Cash Information Does Not Match The Cash Information Corresponding To The Transfer Tracking Number
TB4515=Teller [{}] Is A Warehouse Clerk Or There Is No Teller Cash Box
TB4516=Invalid Mark For Loading And Unloading Banknotes
TB4517=The Parameter Table TB_Ref_Code Is Missing An Item With Domain [{}]
TB4518=Invalid Branch Type
TB4519=The Corresponding Record For Long And Short Payment [{}] Does Not Exist
TB4520=Long And Short Payment Have Been Processed
TB4521=The Cash Trunk Data Corresponding To The Trunk Number [{}] Does Not Exist
TB4522=Branch [{}] Has No Tail Box With Tail Box Number [{}]
TB4523=Invalid Loan Indicator
TB4524=The Entered Residual Coin Identification Information [{}] Does Not Match The Actual [{}]
TB4525=The Total Amount Of Complete Coins Entered [{}] Is Greater Than The Total Amount Of Complete Coins In The Tail Box [{}]
TB4526=Teller [{}] Has More Than One Cash Trunk
TB4527=The Reconciliation Type Cannot Be blank
TB4528=Branchal Cash Tail Box [{}] Currency Information Does Not Exist
TB4529=The Currency Information Of Teller Cash Trunk [{}] Does Not Exist
TB4530=Cash Reservation Number Does Not Exist
TB4531=The Current Branch Does Not Match The Reserved Branch
TB4532=The Pboc Cash Deposit Is Not Configured In The TB_Ref_Code Parameter Trantype
TB4533=Pbc Interbank Cash Outflow Confirmation Is Not Configured In TB_Ref_Code Parameter Trantype
TB4534=The Long And Short Watch Does Not Define The Parameter [{}] Of Processing Type [{}]
TB4535=The Door-To-Door Collection Record Corresponding To The Reservation Number [{}] Does Not Exist Or The Collection Has Been Reviewed
TB4536=The Check Teller Cannot Be The Entry Teller
TB4537=Door-To-Door Collection Is Not Configured In TB_Ref_Code Parameter Trantype
TB4538=Teller [{}] Is Not A Virtual Teller
TB4539=Clear The Trunk [{}] Without Confirmation Of Adding Money.
TB4540=Atm[{}] Has Entered The Process Of Adding Money And Cannot Add Money Anymore.
TB4541=Tail Box [{}] Did Not Add Money.
TB4542=Insufficient Data Of Coupon Type [{}] Currency [{}] In The Cash Trunk;
TB4543=Branch [{}] Has No Tail Box
TB4544=The Modified Teller Must Be The Entered Teller
TB4545=The Adjusted Amount Is Inconsistent With The Trunk
TB4546=The Unprocessed Amount Of Long And Short Payment Is Insufficient.
TB4547=Teller[{}] Voucher Trunk Does Not Exist
TB4548=Tail Box Id[{}] Is Not A Voucher Tail Box
TB4459=HUB [{}]'s notification has been sent, please do not send it again!
TB4459=Hub [{}]'S Notification Has Been Sent, Please Do Not Send It Again!
TB4550=The Account Currency Does Not Match The Transaction Currency
TB4551=Door-To-Door Collection And Accounting Transactions Must Be Completed On The Same Day
TB4552=Insufficient Number Of [{}] Vouchers In The Teller'S Trunk
TB4553=Teller[{}] Cash Trunk Is Not Unique
TB4554=Teller [{}] Voucher Trunk Is Not Unique
TB4556=The Coupon Type [{}] Currency [{}] Data In The Cash Tail Box Balance List Does Not Exist
TB4557=The Record Corresponding To The Reservation Number [{}] Does Not Exist Or Has Been Confirmed
TB4558=The Record Corresponding To The Reservation Number [{}] Does Not Exist Or Is No Longer In The Application Status
TB4559=The Record Corresponding To The Cash Transfer Tracking Number Does Not Exist Or Has Been Confirmed
TB4560=The Deleted Teller Must Be The Entered Teller
TB4561=The [{}] Corresponding To The Transfer Number Cannot Be Returned In Cash!
TB4562=Credential information does not exist!
TB4563=%D% Cash Tail Box [{}] Has No Amount Corresponding To The Currency Of [{}]
TB4564=The Amount Corresponding To The [{}] Currency In The %D% Cash Trunk [{}] Is Insufficient.
TB4565=Cash Trunk Information Exists [{}]
TB4566=Teller[{}] Voucher Trunk Does Not Exist
TB4567=The Item Name Cannot Exceed 30 Characters!
TB4568=This Item Subtype Has Already Been Registered And Cannot Be Registered Again!
TB4569=No Corresponding Information!
TB4570=The Appointment Date Is Not Within The Validity Period Managed By The Door-To-Door Collection Unit!
TB4571=The Appointment With The Other Branch Is Wrong!
TB4572=The Change Information Already Exists, Waiting For Eod Processing!
TB4573=Teller[{}] Cash Trunk Does Not Exist
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查
#（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
TB5002=There Are [{}] Pieces Of Cash Information In Transit That The Other Branch Has Withdrawn From The Warehouse But Has Not Been Put Into The Warehouse, And The Door Is Not Allowed To Be Closed!
TB5003=There Are [{}] Pieces Of In-Transit Voucher Information That This Branch Has Shipped Out Of The Warehouse But Has Not Been Put Into The Warehouse By The Other Branch, And The Door Is Not Allowed To Be Closed!
TB5101=Teller [{}] Did Not Check The Cash Before Officially Checking Out!
TB5102=Teller [{}] Did Not Check The Voucher Before Officially Checking Out!
TB5103=There Are [{}] Pieces Of Cash Data In Transit, And The Door Cannot Be Closed!
TB5104=There Are [{}] Pieces Of Voucher Data In Transit, And The Door Cannot Be Closed!
TB5105=Please Carry Out Branchal Cash Deposit Before Officially Signing Out!
TB5106=Please Check Your Branchal Credentials Before Officially Signing Out!
TB5401=The Information Of Non-Bank Warehousing Branches Does Not Match! The Voucher Type Is [{}], The Head Office Branch Is [{}], And The Participating Branch Is [{}]
TB5402=The Warehousing Agency [{}] Does Not Allow The Use Of This Voucher Type [{}]
TB5403=Voucher [{}] Has Duplicate Outgoing Information
TB5404=The Inbound Voucher [{}] Does Not Have A Matching Outbound Voucher
TB5405=The Voucher [{}] Has Been Stored In The Reconciliation
TB5406=The Reservation Status Of Voucher [{}] Is Not Confirmed
TB5407=The Transfer Indicator Of Voucher [{}] Is Not Allowed To Transfer
TB5408=The Change Of Certificate [{}] From [{}] Status To [{}] Status Is Illegal.
TB5409=Only The Warehouse Administrator Can Make The Voucher To Be Destroyed Or Destroyed.
TB5410=The Warehouse Type Does Not Match The Teller Trunk Type. The Warehouse Type Is [{}] And The Teller Trunk Type Is [{}]
TB5411=Failed To Access The Reconciliation. The Voucher Input Information Does Not Match The Voucher Information In The Trunk.
TB5412=Failed To Access The Reconciliation, The Voucher Types Are Not Equal! The Input Voucher Type Is [{}], And The System Records The Voucher Type As [{}]
TB5413=The Voucher Reservation Date [{}] Must Be Greater Than The Current Accounting Date [{}]
TB5414=To Cancel The Voucher Reservation, The Teller Must Apply For The Teller Reservation.
TB5415=When The Voucher Reservation Information Is Not In Application Status, It Cannot Be Cancelled.
TB5416=The Teller Who Confirms The Appointment With The Voucher Cannot Apply For A Teller For The Appointment.
TB5417=When The Voucher Reservation Information Is Not In The Application Status, It Cannot Be Confirmed.
TB5418=The Appointment Date Has Passed And Cannot Be Confirmed
TB5419=The Voucher Status Has Not Changed
TB5420=The Transaction Teller [{}] Is Not The Teller To Whom The Voucher Belongs [{}]
TB5421=The Reconciliation Access Failed, The Currency Quantity Does Not Match! The Input Currency Quantity Is [{}], And The System Records The Currency Quantity As [{}]
TB5422=Failed To Touch The Warehouse, The Cash Trunk Is Uneven When It Touches The Warehouse.
TB5423=The Reconciliation Is Touched At The Beginning And End Of The Day And Needs To Be Reviewed.
TB5424 = Check The Bank At The Beginning Of The Day And The End Of The Day, And The Check Teller Cannot Be blank.
TB5425=There Are [{}] Unreviewed Door-To-Door Collections On That Day, Please Review!
TB5426=The Reconciliation Is Touched At The Beginning Of The Day And Needs To Be Reviewed.
TB5427=The Voucher Is Not Issued (Act) Status
TB5428=Credential is not associated with this account
TB5501=Cross-Branch Operations Are Not Allowed
TB5502=The Error-Causing Branch [{}] Is Inconsistent With This Branch [{}]
TB5503=The Authorizing Agency [{}] Is Not The Superior Agency Of The Authorized Agency [{}] And Cannot Authorize!
TB5504=Branch [{}] Has Not Been Authorized By The Superior Branch And Cannot Perform Branchal Sign-In!
TB5505=The Seal Card Has Been Borrowed, Please Confirm!
TB5506=The Outlet Has Checked Out Or The Account Is Not In Balance And Cannot Be Checked Out!
TB5507=Cannot Register The Same Card Repeatedly
TB5508=The Current Teller Is Not A Collection Teller And Cannot Destroy The Card.
TB5510=This Account Is A Non-Cancellation Account And Cannot Be Cancelled.
TB5511=The Card Is Not Registered With This Branch And Cannot Be Returned Across Branches.
TB5512=The Card Has Been Registered And Is Currently Pending Destruction!
TB5513=There Is No Corresponding Information For The Query!
TB5514=There Are [{}] Pieces Of Cash Data In Transit From Other Branches To This Branch, And The Shutdown Is Not Allowed!
TB5515=There Are [{}] Pieces Of Cash Data In Transit For This Branch And Cannot Be Closed!
TB5516=There Are [{}] Pieces Of Voucher Data In Transit From Other Branchs To This Branch, And The Shutdown Is Not Allowed!
TB5517=There Are [{}] Pieces Of Voucher Data In Transit For This Branch And Cannot Be Closed!
TB5518=There Are [{}] Pieces Of Data That Have Not Been Written Off Due To Expiration Of The Branch'S Accounts, And The Closure Is Not Allowed!
TB5519=Please Check Your Voucher Before Officially Checking Out!
TB5520=Please Make Cash Deposit Before Officially Checking Out!
TB5521=The Removal Mechanism Must Be Closed!
TB5522=The Amount Of The Tail Box [{}] Is Not 0!
TB5523=Not All Vouchers Have Been Handed In!
TB5524=There Is A Subordinate Branch [{}] In The Detached Branch, Please Process It!
TB5525=The Withdrawal Branch Is A Liquidation Branch, Please Process It!
TB5533=The Removed Mechanism Is A Switching Mechanism, Please Process It!
TB5526=There Is A Loan Signature Card With The Voucher Number [{}], Please Process It!
TB5527=There Are Important Items In The Disassembly Mechanism, Please Process Them!
TB5528=There Are Items For Safekeeping In The Removal Branch, Please Process It!
TB5529=There Are Unprocessed Cards To Be Destroyed In The Removal Mechanism, Please Process Them!
TB5530=There Are Counterfeit Coins That Have Not Been Handed Over To The Withdrawal Agency, Please Process Them!
TB5531=Not All Accessory Items Have Been Turned In!
TB5532=The Withdrawal Branch Is A Ticket Exchange Branch In The Same City, Please Process It!
#6：检查类提示：业务逻辑合法性/一致性/完整性检查
#（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
TB6101=Tail Box Status Is Not Unused
TB6107=The Tail Box Is In Use And Cannot Be Modified Or Sealed.
TB6108=There Is Cash In The Trunk And Cannot Be Modified Or Sealed.
TB6109=There Is A Voucher In The Trunk And Cannot Be Modified Or Sealed.
TB6110=The Trunk Is Not Sealed And Cannot Be Enabled.
TB6111=This Teller Already Has A Trunk In Use
TB6112=The Trunk Id Does Not Match The Teller Id, And The Trunk Is Not Under The Teller'S Name.
TB6113=There Are Attached Items In The Trunk And Cannot Be Modified Or Sealed.
TB6501=The Branch And Teller Must Enter One Item
TB6502=Branch And Teller Must Be Enter One Item
TB6503=Cash In Transit Cannot Be Closed
TB6504=This Service Cannot Be Performed If There Is {0} In The Trunk.
TB6505=The Warehouse Manager Is Not Allowed To Operate This Transaction
TB6506=The Current Teller Is Not A Warehouse Clerk And Cannot Operate This Transaction.
TB6507=This Teller Does Not Have A Corresponding Trunk
TB6508=The Voucher To Be Destroyed Cannot Be Sent To The Subordinate Branch.
TB6509=The Corresponding Tail Box Was Not Found!
TB6510=Please Make Cash Deposit Before Officially Checking Out!
TB6511=Please Check Your Voucher Before Officially Checking Out!
TB6512=Teller [{}] Is Not The Voucher Clerk
TB6513=Teller [{}] Is Not A Cash Clerk
TB6514=Teller [{}] Is A Warehouse Clerk Or Has No Teller Voucher.
TB6515=Teller[{}] Is Not A Warehouse Clerk
TB6516=There Are [{}] Intra-Bank Loan Early Repayment Applications That Have Not Been Processed By This Branch And Are Not Allowed To Be Closed!
TB6517=There Are [{}] Intra-Bank Lending And Reversal Applications That Have Not Been Processed By This Branch And Are Not Allowed To Be Closed!
TB6519=The Status Of The Old And New Vouchers Has Not Changed!


#7：检查类提示：业务流程合法性检查
#（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
TB7001=There Are [{}] Loans That Have Not Been Entered Into The Loan Repayment Plan!
TB7002=There Are [{}] Unreviewed Loans Under The Branch, Please Review!
TB7003=There Are [{}] Loans That Require Re-Entering The Loan Repayment Plan After Early Repayment!
TB7004=The Input Amount Of Banknotes Is Inconsistent With The Actual Amount Of Banknotes!
TB7005=[{}]The account is already associated with this voucher type!
TB7006=There Are [{}] Unresolved Asset Transfer Information Under The Branch, Please Review!
TB7007=This institution is a non-trading bank and tellers are not allowed to log in!
TB4681=Cannot find [{}].[{}] organization check definition class parameters, please confirm!
TB4682=Business date cannot be empty
TB4683=The cut date information does not exist
TB4678=Teller [{}] Did Not Receive The Ordinary Teller Cash Trunk Or Combined Trunk
TB6564=It Is Illegal For Teller [{}] To Use The Trunk
TB6565=Teller [{}] Did Not Receive The Branchal Voucher Or Combined Warehouse Box
TB6547=Branch [{}] Has No Branch Credentials Or Combined Storage Box
TB6577=It Is Illegal For Branch [{}] To Own The Warehouse Box
TB4680=Teller [{}] Did Not Receive The Ordinary Teller Certificate Trunk Or Combined Trunk
TB4835=Teller[{}] Is Not A Warehouse Clerk
TB4836=Teller [{}] Did Not Receive The Branch’S Cash Or Combined Warehouse Box
TB4574=Organization [{}] does not exist
TB4589=This institution is a non-trading bank and tellers are not allowed to log in!
TB4619=Organization [{}] has been merged and tellers are not allowed to log in!
TB5556=This organization has already signed in and is not allowed to sign in again.
TB5998=Teller [{}] cannot open the door to [{}] institution
TB7008=The current institution [{}] is open and batch processing is not allowed.
TB3559=Teller number cannot be empty
TB3560=Teller[{}] does not exist
TB3916=The organization number cannot be empty when querying virtual tellers
TB4616=Organization [{}] has been merged
TB3601=Teller type cannot be empty
TB6522=Only the teller information of this organization and its affiliated organizations can be maintained.
TB4577=Teller[{}] does not exist!
TB4576=TB4576=Teller[{}] already exists!
TB3602=Teller details cannot be empty
TB4662=The teller details are entered incorrectly, please confirm.
TB4669=This teller category does not allow automatic allocation of trunks
TB4670=This teller category must automatically allocate trunks
TB4671=The certificate type and certificate number must both have values.
TB4672=The certificate already exists in the teller information, duplicate certificates are not allowed, please confirm!
TB3599=When automatically assigning and creating a tail box, the tail box attribute is required.
TB4663=This teller is not allowed to create this type of trunk, please confirm!
TB5552=Error message [{}]
TB4575=Teller[{}] has been deleted
TB4578=Teller[{}] Tail Box Exists!
TB6576=There Is [{}] In The Trunk [{}] In Transit [{}], And The Storage, Handover, And Handover Operations Cannot Be Performed!
TB6580=Tail Box [{}] Did Not Perform {}{} Reconciliation!
TB9907=Please Complete The Daily Inventory Of The Trunk [{}] First!
TB6583=The Trunk Handover Teller Branch [{}] Is Inconsistent With The Current Branch [{}], And The Operation Is Not Allowed!
TB6543=Branch [{}] Has No Branchal Cash Or Combination Box
TB4685=This transaction does not allow cross-legal entities, please check!
TB6518=The organization code is not allowed to be empty!
TB3596=Organization [{}] does not have transaction permissions for the corresponding currency [{}]TB4456=警告信息{},拒绝信息{}
TB4457=Warning Message{}
TB4458=Rejected Message{}