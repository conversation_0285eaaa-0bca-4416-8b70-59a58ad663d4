#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#

#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#

#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#

#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
PC3001 = Operation type cannot be empty!
PC3002 = account group ID and main account number cannot be empty at the same time
PC3003 = account group name cannot be empty
PC3004 = account group currency cannot be empty
PC3005 = Income and expenditure attributes cannot be empty
PC3007 = The Account Group Has Signed Up For The Cash Pool, And The Effective Date Must Be Entered When Adding A New Sub-Account.
PC3008 = Fund Pool Agreement Has Been Terminated
PC3009 = The account group has not signed a fund pool agreement or the agreement has expired
PC3010 = account group ID cannot be empty
PC3011 = Whether To Activate The Limit Cannot Be blank
PC3012 = Start date cannot be empty
PC3013 = End date cannot be empty
PC3014 = Whether To Activate The Limit Cannot Be blank
PC3015 = Contract cash pool account group cannot be empty and can only have one
PC3016 = Main account product type cannot be empty
PC3017 = Main account currency cannot be empty
PC3018 = Master account serial number cannot be empty
PC3019 = The interest settlement method is not configured for the product
PC3020 = The status of this account is exception and maintenance is not allowed. Please confirm!
PC3021 = The protocol number cannot be empty!
PC3022 = Subsidiary serial number cannot be empty
PC3023 = Internal pricing is not enabled for this account!
PC3024 = The main account or sub-account of this contract account has the restriction type set by the product, and the contract is not allowed!
PC3025 = The account attributes of the main account or sub-account are not among the account attributes allowed by the capital pool product, and the contract is not allowed!
PC3026 = This account has a periodic mandatory deduction agreement, and suspension and recovery are not allowed.
PC3027 = There Are Multiple Sub-Accounts In The Account Group, And The Priority Of The Sub-Account Must Be Entered.
PC3028 = The sub-account information of the main account of the account group cannot be empty.
PC3029 = Wrong operation type
PC3030 = Agreement number does not match master account information
PC3031 = account group [{}] has not signed a fund pool agreement or the agreement has expired
PC3032 = The agreement number [{}] is incorrect, or the corresponding fund pool agreement has been canceled or suspended.
PC3033 = Protocol number [{}] does not match account number [{}]
PC3034 = An Overdraft Agreement Has Been Signed. A Virtual Account With A Single Account Is Not Allowed To Be Added To This Type Of Account.
PC3035 = The Capital Pool Has Been Signed, And The Virtual And Real Accounts Of One Account Are Not Allowed To Be Added To This Type Of Account.
PC3036 = The Account Group Has Not Signed A Fund Pool Agreement Or The Agreement Status Is Not Signed, Manually Suspended, Automatically Suspended Or Canceled.
PC3037 = When Whether To Enable Internal Pricing Is Y-Yes, The Internal Pricing Mode And Pricing Method Must Be Lost.


PC3101 = Contract Number Cannot Be blank
PC3102 = Cross-Border Capital Pool Type Cannot Be blank
PC3103 = Fund Pool Sub-Account Cannot Be blank!
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
PC4001 = account group names cannot be repeated
PC4002 = The main account of the account group cannot be a margin account
PC4003 = The closed sub-account cannot perform this transaction
PC4004 = account group sub-account cannot be a margin account
PC4005 = The entered sub-account of the account group cannot be the same account as the main account of the account group.
PC4006 = When there are multiple sub-accounts, the priority of the sub-account must be entered.
PC4007 = The entered account number and account group ID do not match
PC4008 = account groups cannot repeatedly sign up for the same type of fund pool products
PC4009 = Subaccount's account status is incorrect
PC4010 = The effective date of the new sub-account must be later than the system date
PC4011 = When The Collection Strategy Is [{}] Full Collection, The Reserve Amount And Fixed Amount Cannot Be Entered.
PC4012 = Termination date cannot be later than current date
PC4013 = When The Collection Strategy Is [{}] Limit Collection, The Limit Amount Cannot Be blank
PC4014 = When The Collection Method Is Scheduled Collection, The Collection Frequency, Collection Day, And Collection Time Cannot Be blank.
PC4015 = The account balance attribute is income, and allocation is not allowed.
PC4016 = The account has no subordinate sub-accounts and the transaction is not allowed.
PC4017 = The account has not signed a fund pool agreement or the agreement has expired
PC4030 = When The Allocation Strategy Is [L03] Fixed Amount Allocation, The Fixed Amount Cannot Be blank
PC4031 = When The Allocation Strategy Is [L04] Full Allocation, The Fixed Amount Does Not Need To Be Entered.
PC4032 = The account has not signed a fund pool agreement and the transaction is not allowed.
PC4033 = When The Dialing Method Is Scheduled Dialing, The Dialing Frequency, Dialing Date, And Dialing Time Cannot Be blank.
PC4034 = The account income and expenditure attribute is expenditure, and aggregation is not allowed.
PC4035 = The account is not a subordinate sub-account in the account group, the transaction is not allowed
PC4036 = The account has no superior account and the transaction is not allowed.
PC4037 = The Sub-Account Is blank, Please Enter The Sub-Account
PC4038 = The account has not signed an account group and cannot sign a fund pool agreement.
PC4039 = Please enter the correct group ID or account information
PC4040 = account status is incorrect
PC4041 = The main account must be a current settlement account
PC4042 = The agreement has taken effect and cannot be deleted
PC4043 = Priorities cannot be repeated when there are multiple sub-accounts
PC4044 = The contracted pool does not allow the maintenance of restricted subaccounts
PC4045 = The account attributes of the sub-account are not among the account attributes allowed by the fund pool product
PC4046 = This account does not belong to the account group or has not signed the fund pool agreement or the agreement has expired
PC4047 = The allocated amount cannot be empty
PC4048 = The actual amount collected is insufficient, no need to collect or manually enter the amount to be collected!
PC4049 = There are restrictions on the account and fund pool signing is not allowed.
PC4050 = The same account exists in the entered account group information, please check; the duplicate account is [{}]
PC4051 = Scheduled allocation, the payment method must be CQ-day overdraft payment
PC4052 = Real-time payment, the payment method must be RQ-real-time payment
PC4053 = The protocol number already exists, please re-enter it.
PC4054 = When the collection strategy is to collect stop payment, the collection amount must be entered
PC4055 = The actual amount transferred is insufficient, no need to transfer or manually enter the amount transferred
PC4056 = There are more than two levels of hierarchical relationships in this fund pool, and it is not allowed to maintain designated interest collection accounts or merge interest settlement identifiers.
PC4057 = This capital pool has designated an interest collection account or whether to consolidate interest settlement. The capital pool signing does not allow the selection of internal pricing.
PC4058 = When selecting consolidated interest settlement, the interest collection account must be specified
PC4060 = Fund pool sub-account [{}] is a designated interest-collecting account and cannot be deleted!
PC4062 = This account is a designated interest-collecting account and no termination is allowed.
PC4063 = The Account [{}] Has Suspended The Fund Pool Protocol And Cannot Be Maintained!
PC4064 = The Account [{}] Is A Designated Interest-Collecting Account And Is Not Allowed To Be Deleted!
PC4065 = For Deferred Interest Payment Maintenance, Please Use The Deferred Interest Payment Maintenance Interface!
PC4066 = The Deferred Interest Payment Array Cannot Be blank!
PC4067 = The Fund Pool Account Number Or Account Sub-Serial Number Cannot Be blank!
PC4068 = The Deferred Interest Payment Account Number Or Account Sub-Serial Number Cannot Be blank!
PC4069 = The Designated Interest Collection Account Or Account Sub-Serial Number For Deferred Interest Payment Cannot Be blank!
PC4070 = The Deferred Interest Payment Account Number [{}] And Account Sub-Serial Number [{}] Are Not Account Information In The Fund Pool Account Group!
PC4071 = The Deferred Interest Payment Designated Interest Collection Account Number [{}] And Account Sub-Serial Number [{}] Are Not Account Information In The Fund Pool Account Group!
PC4072 = The Deferred Interest Payment Account [{}] Already Has Deferred Interest Payment Contract Information!
PC4073 = The Fund Pool Deferred Interest Payment Account Signing Does Not Support Fixed Accounts!
PC4074 = The Customer Number [{}] Has Not Signed An Account Group!

#------------------------------------4----,--------end-----------------------------#
#------------------------------------5------------begin---------------------------#
PC5001 = There are already sub-accounts under the account group and deletion is not allowed.
PC5002 = The account group has signed a cash pool product and is not allowed to be deleted.
PC5003 = Deleted account group cannot be deleted repeatedly
PC5004 = The account has been closed
PC5005 = The main account currency must be consistent with the account group currency
PC5006 = account groups cannot be created for sub-accounts with two lines of income and expenditure.
PC5007 = When a sub-account is already a sub-account under another account group, it can no longer be used as a sub-account.
PC5008 = Income And Expense Account Information Must Be Entered At The Same Time Under The Same Customer
PC5009 = The serial numbers corresponding to the income/expense accounts under the same customer must be consistent.
PC5010 = Sub-Accounts With The Same Serial Number Must Have The Same Customer Number
PC5011 = The revenue and expenditure flag in the sub-account information must be revenue and expenditure integrated
PC5012 = The revenue and expenditure mark in the sub-account information must be two lines of revenue and expenditure
PC5013 = Effective date cannot be earlier than transaction date
PC5014 = The account must be a corporate settlement account
PC5015 = The Main Account Is Already A Sub-Account Of The Three-Tier Account Group, And No More Account Groups Are Allowed To Be Created.
PC5016 = The Sub-Account Must Be A Corporate Settlement Account
PC5017 = The account group has signed a contract with the cash pool, and the effective date must be entered when adding a new sub-account.
PC5018 = The hierarchical relationship of this sub-account has reached the maximum
PC5019 = An income account and an expense account must be entered at the same time under the same customer
PC5020 = The two lines of income and expenditure must be one income account and one expenditure account.
PC5021 = Sub-accounts that have not been terminated after signing the contract are not allowed to be deleted.
PC5022 = When the sub-account of the deleted account group is the main account of another account group, deletion is not allowed.
PC5023 = When there are two lines of income and expenditure, the income account and expenditure account under the same customer must be deleted at the same time.
PC5024 = Sequences for different subsidiaries cannot be the same
PC5025 = There Is No Open Limit For This Account Group
PC5026 = There Is No Opening Limit For This Account Group
PC5027 = The customer number of all sub-accounts cannot be the same
PC5028 = The income account and expenditure account under the two lines of income and expenditure cannot be the same account
PC5029 = The priorities of the income/expense accounts under the same customer must be consistent.
PC5030 = The sum of transaction amount [{}] and accumulated amount [{}] exceeds the current week limit [{}]!
PC5031 = The sum of transaction amount [{}] and accumulated amount [{}] exceeds the monthly limit [{}]!
PC5032 = The sum of transaction amount [{}] and accumulated amount [{}] exceeds the custom limit [{}]!
PC5033 = The sum of transaction amount [{}] and accumulated amount [{}] exceeds the daily limit [{}]!
PC5034 = The sum of the transaction amount [{}] and the accumulated amount [{}] exceeds the single limit [{}]!
PC5035 = The amount allocated [{}] is greater than the available limit [{}]. The account cannot be allocated and the business operation failed!
PC5036 = Insufficient account available balance
PC5037 = The income account and the expenditure account must be deleted at the same time, not just one.
PC5038 = All sub-accounts cannot be the same
PC5039 = Subaccount must be a current settlement account
PC5040 = The Main Agreement Contains Sub-Accounts That Have Not Been Terminated. Termination Of The Main Agreement Is Not Allowed.
PC5041 = The master account's hierarchical relationship has reached the maximum
PC5042 = After maintenance, the hierarchical relationship of the account group will be greater than the maximum hierarchical relationship, and maintenance is not allowed.
PC5043 = When a sub-account has created multiple account groups, it cannot be used as a sub-account.
PC5044 = The income account and the expense account must be [{}] at the same time
PC5045 = Protocol custom limit cannot be set at the same time as weekly limit and monthly limit.
PC5046 = Start date and end date cannot be empty
PC5047 = Limit type cannot be empty
PC5048 = When defining protocol limits, only one of each type of limit can be set
PC5049 = Limit start date cannot be earlier than effective date
PC5050 = Limit end date cannot be earlier than start date
PC5051 = The single transaction limit value cannot be greater than the daily limit, weekly limit or monthly limit, please confirm!
PC5052 = The daily limit cannot be greater than the weekly limit or monthly limit, please confirm!
PC5053 = Weekly limit cannot be greater than monthly limit, please confirm!
PC5054 = account does not have an account group created as a master account
PC5055 = The custom limit has expired, please reset the custom limit effective time!
PC5056 = Weekly limit and monthly limit restriction types cannot be set at the same time, please confirm!
PC5057 = Weekly limit and custom limit limit types cannot be set at the same time, please confirm!
PC5058 = Monthly limit and custom limit limit types cannot be set at the same time, please confirm!
PC5059 = Sub-account must be selected when there is a sub-account!
PC5060 = The current trading account has triggered a day overdraft request, the balance of the superior account is insufficient, and the transaction failed!
PC5101 = The Transfer Amount Is Less Than The Latest Execution Limit
PC5102 = Account [{}] Is The Main Account Of Other Fund Pool Account Groups And Is Not Allowed To Be Used As A Sub-Account Of This Account Group!
PC5103 = Account [{}] Is A Sub-Account Of Another Account Group And Cannot Create Another Account Group!
PC5104 = Account [{}] Is Not A Fund Pool Sub-Account
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
PC6001=account [{}] has signed a fund pool agreement, please terminate the contract first!
PC6002=account [{}] has not signed or has terminated the fund pool agreement!
PC6003=The collection amount is a required field and cannot be empty!
PC6004=account [{}] has been suspended or the fund pool agreement has been terminated!
PC6005=This account [{}] has unclaimed interest and is not allowed to terminate the contract!
PC6101=This Account [{}] Has Signed A Cross-Border Fund Pool Agreement, Please Terminate The Agreement First!
PC6102=Contract Number [{}] Already Exists!
PC6103=Cross-Border Capital Pool Type Cannot Be Modified!
PC6104=The Contract Number Cannot Be Modified!
PC6105=Contract Number [{}] Does Not Exist!
PC6106=Cross-Border Cash Pool Type Is Undefined!
PC6107=This Customer Does Not Have A Qualified Xinghui Deposit Account.
PC6108=The Entered Customer Number And Account Group Id Cannot Be blank At The Same Time!
PC6109=The Account [{}] Is In Abnormal Status And Cannot Sign A Contract!
#------------------------------------6------------end-----------------------------#
#------------------------------------7------------begin---------------------------#
PC7101=The Main Account [{}] Of The Contracted Cross-Border Capital Pool Cannot Be Transferred To Accounts Other Than Sub-Accounts.
PC7102=The Main Account [{}] Of The Contracted Cross-Border Capital Pool Cannot Be Transferred To Accounts Other Than Sub-Accounts.
PC7103=Transfers Are Not Allowed Between Sub-Accounts Of The Contracted Cross-Border Capital Pool
PC7104=The Account Information Of The Fund Pool Sub-Account Array Has Not Been Uploaded!
PC7105=The Designated Interest Collection Account Was Not Found!

#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#

#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#

#------------------------------------9------------end-----------------------------#


