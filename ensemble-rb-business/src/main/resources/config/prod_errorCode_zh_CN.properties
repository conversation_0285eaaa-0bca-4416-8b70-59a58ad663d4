#错误码定义6位
#前两位模块（PD,FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#
#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#
#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#
#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
PD4001=产品有效期校验不合法！
PD4002=客户类型与产品定义不一致！
PD4003=客户标志与产品定义不一致！
PD4004=客户离岸标识与产品定义不一致！
PD4005=存款定期产品定义时必须指定存期信息！
PD4006=定期存期信息不合法！
PD4007=独立账户不允许联名!
PD4008=还款方式不在取值范围内!
PD4009=倒起息日超过倒起息允许最大天数[{}]天!
PD4010=支取金额不在定义范围之内！
PD4011=主账户记录余额标志不能为空！
PD4012=主账户记录余额管理信息不能为空！
PD4013=凭证类型不在取值范围内！
PD4014=自动销户标识不能为空！
PD4015=账户状态不在产品定义范围之内！账户[{}]-状态[{}],产品定义状态[{}]
PD4016=不活动天数不在产品工厂配置范围内！
PD4017=余额不在产品工厂配置范围内！
PD4018=自动转睡眠标识不能为空！
PD4019=本金需要全部提前支取！
PD4020=自动转存时转存次数必须定义!
PD4021=币种[{}]和产品定义币种[{}]不一致！
PD4022=自动转存时不允许转存部分本金！
PD4023=不允许提前支取!
PD4024=自动转存时不允许新增本金！
PD4025=账户属性[{}]不在产品定义[{}]范围内！
PD4026=跨月参数不能为空！
PD4027=不允许跨月！
PD4028=多客户联名账号信息不能为空！
PD4029=联名信息中账号标识符不能为空！
PD4030=联名信息中客户号不能为空！
PD4031=账户余额不足无法透支！
PD4032=提前支取方式或支取次数没有定义!
PD4033=支取密码不正确！
PD4034=定期产品时结息周期信息必须定义！
PD4035=定期结息周期信息不合法！
PD4036=上送的漏存次数不能大于产品定义的漏存次数！
PD4037=必须配置持续扣款标志！
PD4038=交易金额[{}][{}]不满足起存金额！[{}][{}]！
PD4039=存入后金额[{}][{}]超出账户设置的最大金额[{}][{}]！
PD4040=支取后留存金额不足，可以全部支取或留存不小于:[{}][{}]！
PD4041=单次交易金额不在产品定义范围之内[{}][{}]！
PD4042=该代办人不是行内客户，不允许代办！
PD4043=非现金业务不允许交易！
PD4044=不同客户之间不允许转账！
PD4045=非关系客户之间之间不允许转账！
PD4046=非转账业务不允许交易！
PD4047=借方账户不允许通兑交易！
PD4048=贷方账户不允许通存交易！
PD4049=没有该交易类型！
PD4050=模块类型不能为空！
PD4051=账户子类型不能为空！
PD4052=利润中心不存在！
PD4053=账号标识符不能为空！
PD4054=账户存在未收费用，不能销户！
PD4055=账户存在未解除的签约信息，不能销户！
PD4056=凭证类型[{}]与产品配置[{}]不一致！
PD4057=单位客户只允许开立一个基本户！
PD4058=单位客户下基本户与一般户不允许在同一开户行！
PD4059=该账号下[{}]产品不能再续开子账户！
PD4060=产品类型不一致，不允许进行操作！
PD4061=该产品不允许倒起息！
PD4062=远起息日超过远起息允许最大天数[{}]天!
PD4063=该产品不允许远起息！
PD4064=起息不允许跨年!
PD4065=凭证号不能为空！
PD4066=凭证更新失败,请检查上送凭证信息！
PD4067=金额类型与金额计算配置不一致，请检查配置[{}]！
PD4068=产品参数[{}]配置应该在[{}]范围内！
PD4069=提前支取次数已大于产品定义次数，不允许再进行提前支取！
PD4070=需定义产品的部分支取次数！
PD4071=部分支取次数已大于产品定义次数，不允许再进行部分支取！
PD4072=不允许部分支取！
PD4073=交易金额[{}]与累计金额[{}]的和超出当日限额[{}]！
PD4074=是否进行跨自贸区检查标识不能为空！
PD4075=自贸区账户只能在自贸区交易，非自贸区账户只能在非自贸区机构交易！！
PD4076=无操作机构信息！
PD4077=多币种产品开立时，必须首先开立本币主账户！本币：[{}],开户币种：[{}]。
PD4078=产品参数[{}]配置应该在参数值定义中配置！
PD4079=产品[{}]指标[{}]配置错误,缺少参数[{}]的值定义,交易终止!
PD4080=II/III类账户转入资金来源必须为绑定I类账户，尚未绑定I类账户信息！
PD4081=II/III类账户转入资金来源必须为绑定I类账户!
PD4082=II/III类账户绑定I类账户为行外账户，赞不支持!
PD4083=产品类型[{}]未配置凭证类型!
PD4084=产品类型[{}]配置有多种凭证类型!
PD4085=FT账户和境内一般户交易币种必须是人名币!
PD4086=[{}]客户证件已过期，请先维护客户证件信息!
PD4089=渠道[{}]禁止交易
PD4090=该客户在本行已开立此I类账户或此类账户开立已达到上限！
PD4091=产品[{}]余额方向未定义！
PD4092=产品[{}]未定义！
PD4093=开户数量控制，该客户开户数量超限，控制要素：[{}]，需要授权操作！
PD4094=开户数量控制，该客户开户数量超限，控制要素：[{}]，拒绝开户！
PD4095=开户数量控制，该客户开户数量超限，控制要素：[{}]，提示是否继续！
PD4096=贷方账户不允许通兑！
PD4097=借方账户不允许通兑！
PD4098=借方账户不允许通存！
PD4099=当前客户类型与账户属性不匹配！
PD4100=限制起始日期不能为空！
PD4101=无法找到备案类账户参数！
PD4102=账户主键不能为空！
PD4103=账户到期日不能为空或检查产品参数配置！
PD4104=账户存在限制！
PD4105=该账户为汇户不允许进行现金贷记业务！
PD4106=产品起息日校验不合法！
PD4107=产品状态校验不合法！
PD4108=钞户不能转账给汇户！
PD4109=主子账户开立标识不合法,取值范围为[M,S]！
PD4110=产品类型不能为空！
PD4111=提前支取次数已大于期次允许次数，不允许再进行提前支取！
PD4112=结算户不允许维护为储蓄户！
PD4113=账户累计计提为负，不允许结息！
PD4114=不同自贸区账户之间不允许直接转账！
PD4115=自贸区账户与非自贸区账户之间不能进行交易！
PD4116=自贸区不允许非转账业务！
PD4117=自贸区账户只能在本自贸区交易！
PD4118=不同区域不允许做交易！
PD4119=该客户名下，产品类型为[{}]的账户数量超限，需要授权操作！
PD4120=该客户名下，产品类型为[{}]的账户数量超限！
PD4121=该客户名下，产品类型为[{}]的账户数量超限，提示是否继续！
PD4122=该客户名下，账户属性为[{}]的账户数量超限，需要授权操作！
PD4123=该客户名下，账户属性为[{}]的账户数量超限！
PD4124=该客户名下，账户属性为[{}]的账户数量超限，提示是否继续！
PD4125=该客户名下，凭证类型为[{}]的账户数量超限，需要授权操作！
PD4126=该客户名下，凭证类型为[{}]的账户数量超限！
PD4127=该客户名下，凭证类型为[{}]的账户数量超限，提示是否继续！
PD4128=该客户名下，账户类别为[{}]的账户数量超限，需要授权操作！
PD4129=该客户名下，账户类别为[{}]的账户数量超限！
PD4130=该客户名下，账户类别为[{}]的账户数量超限，提示是否继续！
PD4131=该客户名下，此类账户数量超限，需要授权操作！
PD4132=该客户名下，此类账户数量超限，拒绝开户！
PD4133=该客户名下，此类账户数量超限，提示是否继续！
PD4134=交易金额[{}][{}]不能大于最大购买金额！[{}][{}]！
PD4135=此电子账户产品不允许面签！
PD4201=账户存在司法冻结！
PD4202=产品[{}]不是存款产品！
PD4203=单次交易不允许超过最大交易金额[{}][{}]!
PD4204=单次支取不允许小于最小支取金额[{}][{}]!
PD4205=交易机构不一致，不允许通兑交易！
PD4206=无凭证待激活账户不允许做入账！
PD4207=账户[{}]可用余额[{}]不足[{}]!
PD4208=汇户不允许现金借记业务！
PD4209=产品[{}]不允许倒起息！
PD4210=产品[{}]不允许晚起息！
PD4211=倒起息日超过倒起息允许最大天数[{}]天！
PD4212=晚起息日超过倒起息允许最大天数[{}]天！
PD4213=卡下不允许开立三类户
#------------------------------------4------------end-----------------------------#
#------------------------------------5------------begin---------------------------#
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
#------------------------------------6------------end-----------------------------#
PD6001=该产品定义不支持[{}]业务功能！
PD6002=事件[{}]中指标[{}]未配置实例 ！
PD6003=事件[{}]中指标[{}]属性配置异常,请检查该指标实例和指标参数定义是否一致！
PD6004=属性[{}]定义不存在，请在属性类型定义表中配置该属性！
PD6005=事件[{}]中指标[{}]类型未定义，请在指标类型定义表中配置该指标！
PD6006=事件[{}]中指标[{}]的属性定义不完整，请检查指标参数定义！
PD6007=产品[{}]定义不完整，缺少基础产品[{}]中的[{}]属性,请将该产品必要信息补充完整！
PD6008=基础产品[{}]不存在，请检查产品[{}]的基础产品配置是否正确,或基础产品是否定义！
PD6009=产品[{}]默认事件[{}]不存在，产品配置错误，请检查该产品相应配置！
PD6010=属性[{}]值为空，请配置该属性值！
PD6011=产品[{}]未配置白名单支持类型！
PD6012=未找到符合条件的产品信息！
PD6013=上送的利率类型与产品配置不一匹配！
PD6014=试算后不满足留存金额，可以全部试算或留存不小于:[{}][{}]！
#------------------------------------7------------begin---------------------------#
#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#
#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#
#------------------------------------9------------end-----------------------------#

