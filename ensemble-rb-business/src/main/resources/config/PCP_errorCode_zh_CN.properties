#错误码定义6位
#前两位模块（FM,CI,RB,CL,TB,CD）
#后四位数字（第一位按以下含义定义，其他三位顺序标号）
#第一位数字含义分类
#0：预留（系统/模块相关）
#1：确认类提示
#2：授权类提示
#3：检查类提示：数据为空、不为空检查
#4：检查类提示：数据有效性检查（如格式、存在、不存在、不在有效值范围等）
#5：检查类提示：用户权限/柜员操作权限/客户权限相关检查（如不允许操作交易、跨分行检查、用户不允许XXX、该客户不能开立下列子产品类型账户XXX等）
#6：检查类提示：业务逻辑合法性/一致性/完整性检查（如日期比较、金额比较、账户限制、限额相关、不能关闭余额不等于0的账户、RB账户和交易账户的货币符号不同等）
#7：检查类提示：业务流程合法性检查（如已冲正不能交易、已销户XXX、未复核不能后续交易、数据已被更改XXX、交易必须要先核准才能XXX、未到期XXX等）
#8：预留/待以上扩展
#9：预留/待以上扩展
#------------------------------------0------------begin---------------------------#

#------------------------------------0------------end-----------------------------#
#------------------------------------1------------begin---------------------------#

#------------------------------------1------------end-----------------------------#
#------------------------------------2------------begin---------------------------#

#------------------------------------2------------end-----------------------------#
#------------------------------------3------------begin---------------------------#
PC3001 = 操作类型不能为空!
PC3002 = 账户组ID和主账户账号不能同时为空
PC3003 = 账户组名称不能为空
PC3004 = 账户组币种不能为空
PC3005 = 收支属性不能为空
PC3006 = 主账户不能为空
PC3007 = 账户组已签约现金池，新增子账户时生效日期必须输入
PC3008 = 资金池协议已经解除
PC3009 = 账户组未签约资金池协议或者协议已经失效
PC3010 = 账户组ID不能为空
PC3011 = 是否开通额度不能为空
PC3012 = 起始日期不能为空
PC3013 = 终止日期不能为空
PC3014 = 是否开通限额不能为空
PC3015 = 签约现金池账户组不能为空且只能有一个
PC3016 = 主账户产品类型不能为空
PC3017 = 主账户币种不能为空
PC3018 = 主账户序列号不能为空
PC3019 = 产品未配置结息方式
PC3020 = 该账户的状态为非正常状态，不允许进行维护，请确认！
PC3021 = 协议编号不能为空
PC3022 = 子公司序号不能为空
PC3023 = 该账户未开通内部计价！
PC3024 = 该签约账户的主账户或子账户存在产品设置的限制类型，不允许签约！
PC3025 = 主账户或子账户的账户属性不在资金池产品允许的账户属性内，不允许签约！
PC3026 = 该账户存在周期性强制扣划协议，不允许进行暂停恢复。
PC3027 = 账户组存在多个子账户，子账户的优先级必须输入
PC3028 = 账户组主账户的子账户信息不能为空
PC3029 = 操作类型错误
PC3030 = 协议号与主账户信息不匹配
PC3031 = 账户组[{}]未签约资金池协议或者协议已经失效
PC3032 = 协议号[{}]不正确，或对应的资金池协议已经解除或暂停
PC3033 = 协议号[{}]与账号[{}]不匹配
PC3034 = 已签约法人透支协议，一户通主虚子实账户不允许加挂此类账户
PC3035 = 已签约资金池，一户通主虚子实账户不允许加挂此类账户
PC3036 = 账户组未签约资金池协议或者协议状态不为已签约、手动暂停、自动暂停和已解约
PC3037 = 当是否开通内部计价为Y-是时，内部计价模式和计价方式必输


PC3101 = 签约编号不能为空
PC3102 = 跨境资金池类型不能为空
PC3103 = 资金池子账户不能为空！
#------------------------------------3------------end-----------------------------#
#------------------------------------4------------begin---------------------------#
PC4001 = 账户组名称不能重复
PC4002 = 账户组主账户不能为保证金账户
PC4003 = 已销户的子账户不能做此交易
PC4004 = 账户组子账号不能为保证金账户
PC4005 = 录入的账户组子账户不能和账户组主账户为同一个账号
PC4006 = 有多个子账户时必须输入子账户的优先级
PC4007 = 输入的账号和账户组ID不匹配
PC4008 = 账户组不能重复签约同一类型资金池产品
PC4009 = 归集策略为[{}]固定留存金额归集时，留底金额不能为空
PC4010 = 新增子账户的生效日期必须晚于系统日期
PC4011 = 归集策略为[{}]全额归集时,留底金额和固定金额不可输入
PC4012 = 终止日期不能晚于当前日期
PC4013 = 归集策略为[{}]限额归集时,限额金额不能为空
PC4014 = 归集方法为定时归集时，归集频率、归集日、归集时点不能为空
PC4015 = 账户收支属性为收入,不允许进行下拨
PC4016 = 账户无下级子账户,不允许进行该交易
PC4017 = 账户未签约资金池协议或者协议已经失效
PC4030 = 下拨策略为[L03]固定金额下拨时，固定金额不能为空
PC4031 = 下拨策略为[L04]全额下拨时，固定金额不需要输入
PC4032 = 账户未签约资金池协议,不允许进行该交易
PC4033 = 下拨方法为定时下拨时，下拨频率、下拨日、下拨时点不能为空
PC4034 = 账户收支属性为支出,不允许进行归集
PC4035 = 账户不是账户组内下级子账户,不允许进行该交易
PC4036 = 账户无上级账户,不允许进行该交易
PC4037 = 子账户为空，请录入子账户
PC4038 = 账户未签约账户组，不能签约资金池协议
PC4039 = 请录入正确的组ID或账户信息
PC4040 = 账户[{}]状态不正确
PC4041 = 主账户必须是活期结算账户
PC4042 = 协议已经生效不能被删除
PC4043 = 有多个子账户时优先级不能重复
PC4044 = 已签约的资金池不允许维护有限制的子账户
PC4045 = 子账户的账户属性不在资金池产品允许的账户属性内
PC4046 = 此账户不属于该账户组或未签约资金池协议或者协议已经失效
PC4047 = 下拨金额不能为空
PC4048 = 归集实际金额不足,无需归集或手动输入归集金额！
PC4049 = 账户存在限制，不允许进行资金池签约
PC4050 = 输入的账户组信息中存在相同账户，请检查;重复账户为[{}]
PC4051 = 定时下拨，请款方式必须CQ-日间透支请款
PC4052 = 实时下拨，请款方式必须RQ-实时请款
PC4053 = 协议号已存在，请重新输入
PC4054 = 归集策略为止付归集时，归集金额必须输入
PC4055 = 下拨实际金额不足,无需下拨或手动输入下拨金额
PC4056 = 该资金池存在两层以上的层级关系，不允许维护指定收息账户或者合并结息标识
PC4057 = 该资金池已指定收息账户或是否合并结息，资金池签约不允许选择内部计价
PC4058 = 选择合并结息时，必须指定收息账户
PC4060 = 资金池子账户[{}]为指定收息账户，不可删除！
PC4062 = 该账户是指定收息账户，不允许解约!
PC4063 = 该账户[{}]已暂停资金池池协议，不可以维护！
PC4064 = 该账户[{}]是指定收息账户，不允许删除!
PC4065 = 延期付息维护请使用延期付息维护接口！
PC4066 = 延期付息数组不能为空！
PC4067 = 资金池账号或账户子序号不能为空！
PC4068 = 延期付息账号或账户子序号不能为空！
PC4069 = 延期付息指定收息账号或账户子序号不能为空！
PC4070 = 该延期付息账号[{}]和账户子序号[{}]非资金池账户组中账户信息！
PC4071 = 该延期付息指定收息账号[{}]和账户子序号[{}]非资金池账户组中账户信息！
PC4072 = 该延期付息账号[{}]已存在延期付息签约信息！
PC4073 = 资金池延期付息账户签约不支持定期账户！
PC4074 = 该客户号[{}]未签约账户组！

#------------------------------------4----,--------end-----------------------------#
#------------------------------------5------------begin---------------------------#
PC5001 = 账户组下已经有子账户，不允许删除
PC5002 = 账户组已经签约现金池产品，不允许删除
PC5003 = 已删除的账户组不能重复删除
PC5004 = 该账户已销户
PC5005 = 主账户币种必须和账户组币种一致
PC5006 = 收支两条线的子账户不能再建立账户组
PC5007 = 子账户已经是别的账户组下的子账户时，不能再作为子账户
PC5008 = 同一个客户下必须同时录入收入和支出账户信息
PC5009 = 同一客户下的收入/支出账号对应的序号必须一致
PC5010 = 序号相同的子账户客户号必须相同
PC5011 = 子账户信息中的收支标志必须为收支一体
PC5012 = 子账户信息中的收支标志必须为收支两条线
PC5013 = 生效日期不能早于交易日期
PC5014 = 账户必须为对公结算户
PC5015 = 该主账户已是三层账户组的子账户，不允许再建账户组
PC5016 = 子账户必须为对公结算户
PC5017 = 账户组已签约现金池，新增子账户时生效日期必须输入
PC5018 = 该子账户的层级关系已达到最大
PC5019 = 同一个客户下必须同时录入一条收入账户和一条支出账户
PC5020 = 收支两条线下必须是一个收入账号一个支出账号
PC5021 = 签约后未解约的子账户不允许删除
PC5022 = 删除的账户组子账号为其他账户组的主账号时，不允许删除
PC5023 = 收支两条线时，同一客户下的收入账号和支出账号必须同时删除
PC5024 = 不同子公司的序列不能相同
PC5025 = 该账户组没有开通额度
PC5026 = 该账户组没有开通限额
PC5027 = 所有的子账户的客户号不能相同
PC5028 = 收支两条线下的收入帐号和支出账号不能为同一账号
PC5029 = 同一客户下的收入/支出账号对应的优先级必须一致
PC5030 = 交易金额[{}]与累计金额[{}]的和超出当周限额[{}]！
PC5031 = 交易金额[{}]与累计金额[{}]的和超出当月限额[{}]！
PC5032 = 交易金额[{}]与累计金额[{}]的和超出自定义限额[{}]！
PC5033 = 交易金额[{}]与累计金额[{}]的和超出当日限额[{}]！
PC5034 = 交易金额[{}]与累计金额[{}]的和超出单笔限额[{}]！
PC5035 = 下拨金额[{}]大于可用额度[{}]无法给该账户下拨，业务操作失败！
PC5036 = 账户可用额度不足
PC5037 = 收入账号和支出账号必须同时删除，不可只删除一个
PC5038 = 所有的子账号不能相同
PC5039 = 子账户必须是活期结算账户
PC5040 = 主协议中包含未解约子账户，不允许解约主协议
PC5041 = 该主账户的层级关系已达到最大
PC5042 = 维护后账户组的层级关系会大于最大层级关系，不允许维护
PC5043 = 子账户已经建立过多个账户组时，不能被作为子账户
PC5044 = 收入账号和支出账号必须同时[{}]
PC5045 = 协议自定义限额不能与周限额，月限额同时设定
PC5046 = 开始日期和结束日期不能为空
PC5047 = 限额类型不能为空
PC5048 = 定义协议限额时，每种限额只能设置一个
PC5049 = 限额起始日期不能早于生效日期
PC5050 = 限额结束日期不能早于起始日期
PC5051 = 单笔限额值不能大于日限额、周限额或者月限额，请确认！
PC5052 = 日限额不能大于周限额或者月限额，请确认!
PC5053 = 周限额不能大于月限额，请确认！
PC5054 = 账户没有作为主账户建立账户组
PC5055 = 自定义限额已失效，请重新设置自定义限额生效时间!
PC5056 = 周限额和月限额限制类型不能同时设定，请确认！
PC5057 = 周限额和自定义限额限制类型不能同时设定，请确认！
PC5058 = 月限额和自定义限额限制类型不能同时设定，请确认！
PC5059 = 有子账户时必须选择子账户！
PC5060 = 当前交易账户已触发日间透支请款，上级账户余额不足，交易失败！
PC5101 = 划拨金额小于最新执行额度
PC5102 = 账户[{}]为其他资金池账户组主账户，不允许使用作为该账户组的子账号！
PC5103 = 账户[{}]为其他账户组子账户不能再建立账户组！
PC5104 = 账户[{}]不为资金池子账户
#------------------------------------5------------end-----------------------------#
#------------------------------------6------------begin---------------------------#
PC6001=账户[{}]已签署资金池协议，请先解约！
PC6002=账户[{}]未签署或已解约资金池协议！
PC6003=归集金额为必输字段，不能为空！
PC6004=账户[{}]已暂停或已解约资金池协议！
PC6005=该账户[{}]存在未追缴的利息，不允许解约！
PC6101=该账户[{}]已签署跨境资金池协议，请先解约！
PC6102=签约编号[{}]已存在！
PC6103=跨境资金池类型不可修改!
PC6104=签约编号不可修改!
PC6105=签约编号[{}]不存在！
PC6106=跨境资金池类型未定义!
PC6107=该客户下没有符合条件的签约兴惠存账户
PC6108=输入的客户号和账户组ID不能同时为空！
PC6109=该账户[{}]非正常状态，不能签约！
#------------------------------------6------------end-----------------------------#
#------------------------------------7------------begin---------------------------#
PC7101=签约跨境资金池主账户[{}]不可向子账户以外的账户转出
PC7102=签约跨境资金池主账户[{}]不可向子账户以外的账户转入
PC7103=签约跨境资金池子账户间不允许转账
PC7104=该资金池子账户数组未上送账号信息！
PC7105=未查到指定收息账户！
#------------------------------------7------------end-----------------------------#
#------------------------------------8------------begin---------------------------#

#------------------------------------8------------end-----------------------------#
#------------------------------------9------------begin---------------------------#

#------------------------------------9------------end-----------------------------#


