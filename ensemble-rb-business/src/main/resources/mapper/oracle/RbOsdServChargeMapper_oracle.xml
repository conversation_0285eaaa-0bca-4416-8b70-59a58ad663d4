<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge">

  <select id="selectByChargeInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="java.lang.String" databaseId="oracle">
    select
    <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE where charge_to_internal_key = #{internalKey} and fee_amt > 0 and
    (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')
    <if test="startDate != null ">
      and to_char(tran_date,'yyyymmdd') <![CDATA[>= ]]> #{startDate,jdbcType=DATE}
    </if>
    <if test="endDate != null ">
      and to_char(tran_date,'yyyymmdd') <![CDATA[<= ]]> #{endDate,jdbcType=DATE}
    </if>
    <if test="feeType != null and feeType.length() > 0" >
      AND fee_type =#{feeType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="java.lang.String" databaseId="oracle">
    select
    <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE where charge_to_internal_key = #{internalKey} and fee_amt > 0 and
    (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')
    <if test="startDate != null ">
      and to_char(tran_date,'yyyymmdd') <![CDATA[>= ]]> #{startDate,jdbcType=DATE}
    </if>
    <if test="endDate != null ">
      and to_char(tran_date,'yyyymmdd') <![CDATA[<= ]]> #{endDate,jdbcType=DATE}
    </if>
    <if test="feeType != null and feeType.length() > 0" >
      AND fee_type =#{feeType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByClientNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="java.util.Map" databaseId="oracle">
    select
    <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE where client_no = #{clientNo} and fee_amt > 0 and (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')
    <if test="startDate != null ">
      and to_char(tran_date,'yyyymmdd') <![CDATA[>= ]]> #{startDate,jdbcType=DATE}
    </if>
    <if test="endDate != null ">
      and to_char(tran_date,'yyyymmdd') <![CDATA[<= ]]> #{endDate,jdbcType=DATE}
    </if>
    <if test="feeType != null and feeType.length() > 0" >
      AND fee_type =#{feeType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
