<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
                "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDropdownParameter">

        <!-- 下拉列表查询专用-->
        <select id="selectPkList" parameterType="java.util.Map" resultType="java.util.LinkedHashMap" databaseId="oracle">
                SELECT DISTINCT
                TO_CHAR (keyc) "keyc",
                TO_CHAR (valuec) "valuec"
                FROM (
                SELECT
                ${column} keyc,
                ${columnDesc} valuec
                FROM
                ${tableName}
                <if test="condition != null and  condition != '' ">
                        where  ${condition}
                </if>
                )
                ORDER BY
                "keyc"
        </select>

</mapper>