<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary">
  <select id="getIBUncEntriesForSumUnc" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary" databaseId="oracle">
    SELECT
    nvl(REVERSAL_DATE, TRAN_DATE) AS TRAN_DATE,
    TRAN_BRANCH AS BRANCH,
    SELL_BUY_IND,
    nvl2(REVERSAL_DATE, 'Y', 'N') AS REVERSAL,
    SELL_CCY,
    BUY_CCY,
    DECODE(SELL_BUY_IND, 'E', TRAN_TYPE, '') AS TRAN_TYPE,
    NVL(UNC_STATUS, 'N') AS STATUS,
    SUM(BUY_AMOUNT) AS AMT_BUY,
    SUM(SELL_AMOUNT) AS AMT_SELL ,
    COUNT(1) AS CTR_TRAN,
    #{nodeId} AS NODE_ID,
    sum(nvl(FCY_CTRL_IBUNC_AMT, 0)) AS UNC_LCY_AMT
    FROM
    RB_EXCHANGE_TRAN_HIST
    WHERE (unc_status in ('N', 'D') OR unc_status IS NULL)
    AND ((REVERSAL_DATE IS NULL AND TRAN_DATE BETWEEN #{lastRunDate,jdbcType=DATE} AND #{yesterday,jdbcType=DATE})
    OR (REVERSAL_DATE IS NOT NULL AND REVERSAL_DATE != TRAN_DATE AND REVERSAL_DATE BETWEEN #{lastRunDate,jdbcType=DATE} AND #{yesterday,jdbcType=DATE}))
    AND seq_no BETWEEN #{startKey} AND #{endKey}
    GROUP BY nvl(REVERSAL_DATE, TRAN_DATE),
    TRAN_BRANCH,
    SELL_BUY_IND,
    nvl2(REVERSAL_DATE, 'Y', 'N'),
    SELL_CCY,
    BUY_CCY,
    DECODE(SELL_BUY_IND, 'E', TRAN_TYPE, ''),
    NVL(UNC_STATUS, 'N')
  </select>
  <select id="getBranchSumEntryForSumObunc" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary" databaseId="oracle">
    SELECT TRAN_DATE,
    BRANCH,
    DECODE(SELL_BUY_IND, 'B', BUY_CCY, SELL_CCY) AS BUY_CCY,
    SUM(DECODE(SELL_BUY_IND, 'B', DECODE(REVERSAL, 'N', AMT_BUY, -1*AMT_BUY), DECODE(REVERSAL, 'N', -1*AMT_SELL, AMT_SELL))) AS AMT_BUY,
    SUM(DECODE(SELL_BUY_IND, 'B', DECODE(REVERSAL, 'N', -1*UNC_LCY_AMT, UNC_LCY_AMT), DECODE(REVERSAL, 'N', UNC_LCY_AMT, -1*UNC_LCY_AMT))) AS AMT_SELL
    FROM RB_EX_TRAN_SUMMARY
    WHERE STATUS IN ('D', 'S')
    AND SELL_BUY_IND != 'E'
    AND TRAN_DATE between #{lastRunDate,jdbcType=DATE} and #{yesterday,jdbcType=DATE}
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    GROUP BY TRAN_DATE, BRANCH, DECODE(SELL_BUY_IND, 'B', BUY_CCY, SELL_CCY)
  </select>

</mapper>
