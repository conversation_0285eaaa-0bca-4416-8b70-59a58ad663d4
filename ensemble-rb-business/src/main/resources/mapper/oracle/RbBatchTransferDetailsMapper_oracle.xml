<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">

  <select id="getSumRevservalDetailsBytranType" parameterType="java.util.Map" resultType="java.math.BigDecimal" databaseId="oracle">
    select SUM(decode(REC_AMT_CTRL, 'N', ACT_TRAN_AMT, TRAN_AMT))
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
    <if test="tranType!=null">
      AND TRAN_TYPE=#{tranType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND BATCH_STATUS = 'S'
    AND REVERSAL_FLAG = 'Y'
  </select>

</mapper>
