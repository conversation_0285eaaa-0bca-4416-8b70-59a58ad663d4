<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetail">
    <update id="updateAccruedPrevDetailForEod" parameterType="java.util.Map" databaseId="oracle">
        UPDATE rb_acct_int_detail a
        SET int_accrued_prev = int_accrued,
        ADV_UPD_LAST_DATE = #{runDate}
        WHERE NVL (int_accrued, 0) <![CDATA[ <> ]]>  NVL (int_accrued_prev, 0)
        AND A.adv_upd_last_date <![CDATA[ < ]]> #{runDate}
        AND EXISTS
        (SELECT 1
        FROM rb_acct b
        WHERE a.internal_key = b.internal_key
        and a.CLIENT_NO =b.CLIENT_NO
        AND b.acct_status <![CDATA[ <> ]]>'C'
        AND b.internal_key BETWEEN #{startKey} AND #{endKey} )
    </update>
    <update id="updateDriPrevDetailForEod" parameterType="java.util.Map" databaseId="oracle">
        UPDATE RB_ACCT_INT_DETAIL
        SET
        DISCNT_RETAIN_INT_PREV = DISCNT_RETAIN_INT,
        DRI_UPD_LAST_DATE = #{runDate}
        WHERE INTERNAL_KEY BETWEEN #{startKey} AND #{endKey}
        AND (DRI_UPD_LAST_DATE IS NULL OR DRI_UPD_LAST_DATE <![CDATA[ < ]]> #{runDate} )
        AND NVL(DISCNT_RETAIN_INT, 0) <![CDATA[ <> ]]> NVL(DISCNT_RETAIN_INT_PREV, 0)
    </update>
    <update id="updateAdvPrevDetailForEod" parameterType="java.util.Map" databaseId="oracle">
        UPDATE RB_ACCT_INT_DETAIL
        SET
        DISCNT_INT_PREV = DISCNT_INT
        <!-- ,ADV_UPD_LAST_DATE = #{runDate} -->
        WHERE INTERNAL_KEY BETWEEN #{startKey,jdbcType=BIGINT} AND #{endKey,jdbcType=BIGINT}
        AND (ADV_UPD_LAST_DATE IS NULL OR ADV_UPD_LAST_DATE <![CDATA[ < ]]> #{runDate} )
        AND NVL(DISCNT_INT, 0) <![CDATA[ <> ]]> NVL(DISCNT_INT_PREV, 0)
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="updateAdjPrevDetailForEod" parameterType="java.util.Map" databaseId="oracle">
        UPDATE RB_ACCT_INT_DETAIL
        SET
        INT_ADJ_PREV = INT_ADJ,
        ADJ_UPD_LAST_DATE = #{runDate}
        WHERE INTERNAL_KEY BETWEEN #{startKey,jdbcType=BIGINT} AND #{endKey,jdbcType=BIGINT}
        AND (ADJ_UPD_LAST_DATE IS NULL OR ADJ_UPD_LAST_DATE <![CDATA[ < ]]> #{runDate} )
        AND NVL(INT_ADJ, 0) <![CDATA[ <> ]]> NVL(INT_ADJ_PREV, 0)
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </update>
    <update id="updatePrevDetailForEod" parameterType="java.util.Map" databaseId="oracle">
        update RB_ACCT_INT_DETAIL
        set
        INT_ADJ_PREV = INT_ADJ,
        INT_ACCRUED_PREV = INT_ACCRUED,
        DISCNT_INT_PREV = DISCNT_INT,
        last_change_date = #{runDate}
        where internal_key BETWEEN #{startKey} and #{endKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND ( NVL(INT_ADJ,0)  <![CDATA[ <> ]]> NVL(INT_ADJ_PREV,0)
        OR NVL(INT_ACCRUED,0) <![CDATA[ <> ]]> NVL(INT_ACCRUED_PREV,0)
        OR NVL(DISCNT_INT,0) <![CDATA[ <> ]]> NVL(DISCNT_INT_PREV,0))
    </update>
    <select id="selectRbIntSummary" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetail" databaseId="oracle">
        select
        int_class,
        SUM(nvl ( int_accrued_prev, 0 ) + nvl ( int_posted_ctd, 0 ) + nvl ( int_adj_prev, 0 ) - nvl ( DISCNT_INT, 0 ) ) int_Amt
        from RB_ACCT_INT_DETAIL
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND  CLIENT_NO = #{clientNo}
        AND nvl ( int_accrued_prev, 0 ) + nvl ( int_posted_ctd, 0 ) + nvl ( int_adj_prev, 0 ) - nvl ( DISCNT_INT, 0 ) <![CDATA[<>]]> 0
        GROUP BY int_class
    </select>

</mapper>
