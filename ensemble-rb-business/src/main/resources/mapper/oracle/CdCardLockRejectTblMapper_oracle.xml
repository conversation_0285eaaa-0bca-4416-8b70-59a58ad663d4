<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardLockRejectTbl">
	<!--多法人改造 by LIYUANV-->
	<select id="getCdCardLockRejectInfos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardLockRejectTbl" databaseId="oracle">
		select
		<include refid="Base_Column"/>
		from
		<include refid="Table_Name" />
		<where>
		<if test="beginDate != null ">
			and to_char(TRAN_DATE,'yyyymmdd') &gt;= #{beginDate}
		</if>
		<if test="endDate != null ">
			and to_char(TRAN_DATE,'yyyymmdd') &lt;= #{endDate}
		</if>
		<if test="cardNo != null and  cardNo != '' ">
			and CARD_NO = #{cardNo}
		</if>
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>

</mapper>