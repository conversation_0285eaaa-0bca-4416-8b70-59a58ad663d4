<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">

  <select id="getExTranHistListbyUncStatusAndDate" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist" databaseId="oracle">
    select <include refid="Base_Column"/>
    from RB_EXCHANGE_TRAN_HIST
    where
    TRAN_DATE   = to_date(#{tranDate},'yyyymmdd')
    <if test="uncStatus != null and uncStatus.length() > 0">
      and NVL(UNC_STATUS,'N') = #{uncStatus}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getObuncEntriesByBrnFcy" parameterType="java.util.Map"
          resultType="java.util.Map" databaseId="oracle">
    SELECT TRAN_BRANCH,
    SELL_BUY_IND,
    FCY_CCY,
    SUM(NVL(DECODE(AA.IS_REVERSAL,
    'Y',
    -1 * AA.FCY_CTRL_IBUNC_AMT,
    AA.FCY_CTRL_IBUNC_AMT),
    0)) FCY_BRN_OBUNCAMT
    FROM (SELECT A.TRAN_DATE,
    A.TRAN_BRANCH,
    A.SELL_BUY_IND,
    DECODE(A.SELL_BUY_IND, 'B', A.BUY_CCY, A.SELL_CCY) FCY_CCY,
    NVL2(A.REVERSAL_TRAN_TYPE, 'Y', 'N') IS_REVERSAL,
    A.UNC_STATUS,
    A.IBUNC_REFERENCE,
    A.OBUNC_REFERENCE,
    A.FCY_CTRL_IBUNC_AMT
    FROM RB_EXCHANGE_TRAN_HIST A
    WHERE A.TRAN_DATE between  #{lastRunDate,jdbcType=DATE} and #{yesterday,jdbcType=DATE}
    AND A.UNC_STATUS = 'P'
    ) AA
    GROUP BY AA.TRAN_BRANCH, AA.SELL_BUY_IND, AA.FCY_CCY
  </select>
  <select id="getIBUncEntriesOnSum" parameterType="java.util.Map"
          resultType="java.util.Map" databaseId="oracle">
    select A.TRAN_BRANCH,
    A.SELL_BUY_IND,
    a.rate_type,
    nvl2(a.reversal_tran_type, 'Y', 'N') IS_REVERSAL,
    decode(a.sell_buy_ind, 'B', A.BUY_CCY, A.SELL_CCY) FCY_CCY,
    SUM(a.buy_amount) AMT_BUY,
    sum(a.sell_amount) AMT_SELL
    from rb_exchange_tran_hist a
    WHERE (a.unc_status = 'R')
    AND A.TRAN_DATE=#{tranDate}
    group by A.TRAN_BRANCH,
    A.SELL_BUY_IND,
    a.rate_type,
    nvl2(a.reversal_tran_type, 'Y', 'N'),
    decode(a.sell_buy_ind, 'B', A.BUY_CCY, A.SELL_CCY)
  </select>
</mapper>
