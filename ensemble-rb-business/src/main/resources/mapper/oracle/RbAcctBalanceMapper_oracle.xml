<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
    <select id="selectRbBalanceSummary" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance" useCache="false" databaseId="oracle">
        SELECT
        SUM(nvl(total_amount_prev,0)) total_amount,LAST_BAL_UPD_DATE
        FROM RB_ACCT_BALANCE
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CLIENT_NO = #{clientNo}
        group by LAST_BAL_UPD_DATE
    </select>
    <update id="updateFinRegAmount" parameterType="java.util.Map" databaseId="oracle">
        update RB_ACCT_BALANCE set FINREG_AMOUNT =  nvl(FINREG_AMOUNT,0) + #{finRegAmount}, LAST_CHANGE_DATE  = #{runDate}
        where internal_key = #{internalKey} and CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="selectRbBalanceSummary1"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.acct.RbAcctBalanceTotalModel"  useCache="false" databaseId="oracle">
        <if test="_databaseId == 'oracle'">
            select SUM(case b.ACCT_STOP_PAY WHEN 'Y' THEN 0  ELSE nvl((-1)*total_amount,0)+nvl(-1*finreg_amount,0)-nvl(pld_amount,0)+nvl(-1*dos_amount,0) END) as  total_amount,
            prod_type as prod_type ,
            acct_ccy as acct_ccy,
            base_acct_no as base_acct_no
            from rb_acct_balance a,  rb_acct b
            where a.internal_key =b.internal_key
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            and b.base_acct_no =#{baseAcctNo}
            and b.client_no = #{clientNo}
            and b.acct_real_flag='Y'
        </if>
        <if test="_databaseId == 'oracle'">
            select SUM(case b.ACCT_STOP_PAY  WHEN 'Y' THEN 0  ELSE nvl((-1)*total_amount,0)+nvl(-1*finreg_amount,0)-nvl(pld_amount,0)+nvl(-1*dos_amount,0) END) as  total_amount,
            prod_type as prod_type ,
            acct_ccy as acct_ccy,
            b.base_Acct_No  as base_acct_no
            from  rb_acct_balance a
            LEFT JOIN RB_ACCT B ON A.INTERNAL_KEY = B.INTERNAL_KEY
            where a.client_no=b.client_no and  b.base_acct_no =#{baseAcctNo}
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            and b.client_no = #{clientNo}
            and b.acct_real_flag='Y'
            group by b.prod_type ,b.acct_ccy,b.base_acct_no
        </if>
    </select>

</mapper>
