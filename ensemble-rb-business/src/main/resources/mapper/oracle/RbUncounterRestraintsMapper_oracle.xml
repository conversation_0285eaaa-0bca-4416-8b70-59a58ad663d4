<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">

	<select id="selectJZuncounter" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints" databaseId="oracle">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE
		<if test="clientNo != null and  clientNo != '' ">
			CLIENT_NO = #{clientNo}  AND
		</if>
		<if test="documentType != null and  documentType != '' ">
			DOCUMENT_TYPE = #{documentType}  AND
		</if>
		<if test="docunmentId != null and  docunmentId != '' ">
			DOCUMENT_ID = #{docunmentId}  AND
		</if>
		UNCOUNTER_RESTRAINT_TYPE = '0008'
		and UNCOUNTER_RESTRAINT_STATUS = '1'
		AND #{runDate} between to_char(start_effect_date,'yyyymmdd')  and  to_char(end_effect_date,'yyyymmdd')
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	
</mapper>
