<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
    <select id="getNoCalcBalTranBySeqNo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran" databaseId="oracle">
        select
        <include refid="Base_Column"/>
        from RB_UPD_BALANCE_TRAN
        where BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        AND CLIENT_NO = #{clientNo}
        <if test="timesSeqNo != null">
            AND TIMES_SEQ_NO >= #{timesSeqNo}
        </if>
        <if test="internalKey != null">
            AND internal_Key =#{internalKey}
        </if>
        AND tran_date =#{runDate,jdbcType=DATE}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="tranTimeStamp != null">
            AND #{tranTimeStamp} > TRAN_TIMESTAMP
        </if>
        <if test="singleCount != null">
            AND    #{singleCount} > rownum
        </if>
    </select>

    <select id="getNoCalcBalTranMaxSeqNo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran" databaseId="oracle">
        SELECT MAX (TIMES_SEQ_NO) AS TIMES_SEQ_NO
        FROM RB_UPD_BALANCE_TRAN
        WHERE BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        AND tran_date =#{runDate,jdbcType=DATE}
        AND internal_Key =#{internalKey}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO =#{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="asynstatus != null and asynstatus.length() > 0">
            AND TRAN_TIMESTAMP <![CDATA[ <= ]]> #{tranTimeStamp}
        </if>
    </select>
    <select id="getNoCalcBalTranByMaxSeqNo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran" databaseId="oracle">
        select * from(
        select
        <include refid="Base_Column"/>
        from RB_UPD_BALANCE_TRAN
        where BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        AND CLIENT_NO = #{clientNo}
        <if test="timesSeqNo != null">
            AND TIMES_SEQ_NO <![CDATA[ <= ]]> #{timesSeqNo}
        </if>
        <if test="internalKey != null">
            AND internal_Key =#{internalKey}
        </if>
        AND tran_date =#{runDate,jdbcType=DATE}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY TIMES_SEQ_NO ASC
        ) where rownum <![CDATA[ <= ]]> ${singleCount}
    </select>
    <select id="getBalTranBySeqNoMax" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran" databaseId="oracle">
        SELECT MAX(TIMES_SEQ_NO+0) AS timesSeqNo
        FROM RB_UPD_BALANCE_TRAN
        WHERE internal_key = #{internalKey}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        AND tran_date =#{runDate,jdbcType=DATE}
        AND TIMES_SEQ_NO <![CDATA[  < ]]> #{timesSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="batchUpdateRbUpdBalanceSegment" parameterType="java.util.HashMap" databaseId="oracle">
        update RB_UPD_BALANCE_TRAN
        set BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        where client_no = #{clientNo, jdbcType=VARCHAR}
        <if test="internalKey != null">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        AND tran_date =#{tranDate,jdbcType=DATE}
        <if test="cometStart != null and cometEnd != null and cometEnd > 0">
            and  TIMES_SEQ_NO  BETWEEN #{cometStart} and #{cometEnd}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <delete id="deleteRbUpdBalanceTranDbList" parameterType="java.util.HashMap" databaseId="oracle">
        delete from RB_UPD_BALANCE_TRAN
        where client_no = #{clientNo, jdbcType=VARCHAR}
        and internal_key = #{internalKey}
        and TIMES_SEQ_NO BETWEEN #{minSeqNo} and #{maxSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <update id="batchUpdateRbUpdBalanceTran" parameterType="java.util.HashMap" databaseId="oracle">
        update RB_UPD_BALANCE_TRAN
        set BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        where client_no = #{clientNo, jdbcType=VARCHAR}
        and TO_NUMBER(seq_no) BETWEEN #{minSeqNo} and #{maxSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
</mapper>
