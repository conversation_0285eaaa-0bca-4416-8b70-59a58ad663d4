<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctFinancialCheck">

	<select id="selectBalList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.accounting.AccountingCheckModel" databaseId="oracle">
		select
		a.INTERNAL_KEY,
		a.BASE_ACCT_NO,
		a.ACCT_BRANCH as BRANCH,
		a.ACCT_CCY as CCY,
		a.PROD_TYPE as BUSI_PROD,
		a.ACCOUNTING_STATUS,
		'BAL' as AMT_TYPE,
		ABS(b.TOTAL_AMOUNT_PREV) as BALANCE,
		'RB' as SYSTEM_ID,
		#{yesterday} as TRAN_DATE
		from
		RB_ACCT a,RB_ACCT_BALANCE b
		where
		a.CLIENT_NO = b.CLIENT_NO
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND a.COMPANY = #{company}
		</if>
		AND
		a.INTERNAL_KEY in
		<foreach collection="internalKey" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND
		(a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
		AND
		a.INTERNAL_KEY = b.INTERNAL_KEY
		AND
		a.SOURCE_MODULE != 'CL'
		AND
		NVL(b.TOTAL_AMOUNT_PREV,0) <![CDATA[ <> ]]> 0
		AND
		a.ACCT_REAL_FLAG = 'Y'
	</select>
	<select id="selectIntList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.accounting.AccountingCheckModel" databaseId="oracle">
		select
		a.INTERNAL_KEY,
		a.BASE_ACCT_NO,
		a.ACCT_BRANCH as BRANCH,
		a.ACCT_CCY as CCY,
		a.PROD_TYPE as BUSI_PROD,
		a.ACCOUNTING_STATUS,
		b.INT_CLASS as AMT_TYPE,
		NVL(b.INT_ACCRUED_PREV, 0)+NVL(b.INT_ADJ_PREV, 0)-NVL(b.DISCNT_INT_PREV, 0) as BALANCE,
		'RB' as SYSTEM_ID,
		#{yesterday} as TRAN_DATE
		from
		RB_ACCT a,RB_ACCT_INT_DETAIL b
		where
		a.CLIENT_NO = b.CLIENT_NO
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND a.COMPANY = #{company}
		</if>
		AND
		a.INTERNAL_KEY in
		<foreach collection="internalKey" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND
		(a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
		AND
		a.INTERNAL_KEY = b.INTERNAL_KEY
		AND
		NVL(b.INT_ACCRUED_PREV, 0)+NVL(b.INT_ADJ_PREV, 0)-NVL(b.DISCNT_INT_PREV, 0) <![CDATA[ <> ]]> 0
		AND
		a.ACCT_REAL_FLAG = 'Y'
	</select>
	<select id="selectFinBalList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.accounting.AccountingCheckModel" databaseId="oracle">
		select
		BASE_ACCT_NO,
		TRAN_BRANCH as BRANCH,
		ACCT_CCY as CCY,
		PROD_TYPE as BUSI_PROD,
		'ZHC' as ACCOUNTING_STATUS,
		'BAL' as AMT_TYPE,
		TOTAL_AMOUNT_PREV as BALANCE,
		'RB' as SYSTEM_ID,
		#{yesterday} as TRAN_DATE
		from
		RB_FINANCIAL_ACCT_REGISTER
		where
		INTERNAL_KEY in
		<foreach collection="internalKey" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND
		(ACCT_STATUS != 'C' OR (ACCT_STATUS = 'C' AND ACCT_CLOSE_DATE = #{runDate}))
		AND
		NVL(TOTAL_AMOUNT_PREV, 0) <![CDATA[ <> ]]> 0
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectFinIntList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.accounting.AccountingCheckModel" databaseId="oracle">
		select
		BASE_ACCT_NO,
		TRAN_BRANCH as BRANCH,
		ACCT_CCY as CCY,
		PROD_TYPE as BUSI_PROD,
		'ZHC' as ACCOUNTING_STATUS,
		INT_CLASS as AMT_TYPE,
		NVL(INT_ACCRUED_PREV, 0)+NVL(INT_ADJ_PREV, 0) as BALANCE,
		'RB' as SYSTEM_ID,
		#{yesterday} as TRAN_DATE
		from
		RB_FINANCIAL_ACCT_REGISTER
		where
		INTERNAL_KEY in
		<foreach collection="internalKey" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND
		(ACCT_STATUS != 'C' OR (ACCT_STATUS = 'C' AND ACCT_CLOSE_DATE = #{runDate}))
		AND
		NVL(INT_ACCRUED_PREV, 0)+NVL(INT_ADJ_PREV, 0) <![CDATA[ <> ]]> 0
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>




</mapper>