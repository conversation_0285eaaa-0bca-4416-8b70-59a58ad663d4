<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWechat">
	<select id="selectCosWechatSignInfo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWechat" databaseId="oracle">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_WECHAT
		where
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		to_char(TRAN_TIMESTAMP,'yyyymmdd') = to_char(#{tranTimestamp},'yyyymmdd')
	</select>
</mapper>
