<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg">
	<select id="selectDossList" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg" databaseId="oracle">
		SELECT <include refid="Base_Column"/>
		FROM RB_ACCT_DOSS_REG
		WHERE DOSS_STATUS in ('WS','S','WO','O')
		<if test="baseAcctNo != null and baseAcctNo !=''">
			AND BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="dossStatus != null and dossStatus !=''">
			AND DOSS_STATUS = #{dossStatus}
		</if>
		<if test="isIndividualFlag != null and isIndividualFlag !=''">
			AND IS_INDIVIDUAL_FLAG = #{isIndividualFlag}
		</if>
		<if test="nonTransplantFlag != null and nonTransplantFlag !=''">
			AND NVL(NON_TRANSPLANT_FLAG,'N') = #{nonTransplantFlag}
		</if>
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY INTERNAL_KEY DESC
	</select>
	<select id="selectDossAcctList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctAndDossRegModel" databaseId="oracle">
		SELECT a.*,
		b.document_id documentId,
		b.document_Type documentType,
		b.card_No cardNo
		FROM RB_ACCT_DOSS_REG a
		LEFT JOIN RB_ACCT b
		ON a.INTERNAL_KEY = b.INTERNAL_KEY AND a.CLIENT_NO = b.CLIENT_NO
		WHERE a.DOSS_STATUS in ('WS','S','WO','O')
		AND NVL(b.ACCT_BRANCH, a.DOSS_BRANCH) = #{branch}
		<if test="nonTransplantFlag != null and nonTransplantFlag !=''">
			AND NVL(a.NON_TRANSPLANT_FLAG,'N') = #{nonTransplantFlag}
		</if>
		<if test="baseAcctNo != null and baseAcctNo !=''">
			AND a.BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="dossStatus != null and dossStatus !='' and dossStatus == 'WS'">
			AND a.DOSS_STATUS = #{dossStatus} and b.acct_status not in ('A','C')
		</if>
		<if test="dossStatus != null and dossStatus !=''">
			AND a.DOSS_STATUS = #{dossStatus}
		</if>
		<if test="isIndividualFlag != null and isIndividualFlag !=''">
			AND a.IS_INDIVIDUAL_FLAG = #{isIndividualFlag}
		</if>
		<if test="clientNo != null and clientNo !=''">
			AND a.CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>

		ORDER BY a.INTERNAL_KEY DESC
	</select>
</mapper>
