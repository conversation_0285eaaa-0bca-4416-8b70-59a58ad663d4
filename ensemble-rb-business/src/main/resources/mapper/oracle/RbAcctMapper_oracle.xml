<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct">
    <!--查询柜面渠道存取标记为Y的内部账户 -->
    <select id="queryCounterDepWtdAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct" databaseId="oracle">
        SELECT ma.client_no,ma.base_acct_no
        FROM RB_ACCT ma, rb_acct_attach B
        WHERE ma.INTERNAL_KEY = B.INTERNAL_KEY
        AND ma.CLIENT_NO = B.CLIENT_NO
        AND ma.acct_status != 'C'
        AND (B.COUNTER_DEP_FLAG = 'Y'
        OR B.COUNTER_DEBT_FLAG = 'Y')
        AND ma.ACCT_BRANCH = #{branch}
        and ma.source_module = 'GL'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>
    <select id="getOneClassBaseAcc" parameterType="java.util.Map" resultType="java.lang.String" databaseId="oracle">
        SELECT distinct BASE_ACCT_NO
        FROM RB_ACCT
        WHERE CLIENT_NO =#{clientNo}
        AND acct_class = '1' and acct_status not in('C','S') and rownum = 1
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbAcctByAcctSeqNoClient" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct" databaseId="oracle">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT
        WHERE acct_status != 'C' and base_acct_no = #{baseAcctNo}
        <if test="acctSeqNo != null and acctSeqNo.length()> 0">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND ROWNUM = 1
    </select>
    <select id="getMbAcctByBaseAcctNoAndClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct" databaseId="oracle">
        SELECT <include refid="Base_Column"/>
        FROM RB_ACCT
        WHERE acct_type='C'
        <if test = "baseAcctNo !=null and baseAcctNo !=''">
            and  (base_acct_no =#{baseAcctNo} or card_no =#{baseAcctNo})
        </if>
        <if test = "clientNo !=null and clientNo !=''">
            AND CLIENT_NO = #{clientNo}
        </if>

        AND rownum =1
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAllAcctByBaseAcctNoOrCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct" databaseId="oracle">
        SELECT <include refid="Base_Column"/>
        FROM RB_ACCT
        WHERE card_no =#{baseAcctNo} OR base_acct_no =#{baseAcctNo}
        AND ROWNUM = 1
        order by acct_seq_no asc
    </select>

    <select id="getMbAcctSelectiveQueryHist" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct" databaseId="oracle">
        select <include refid="Base_Column"/> from ( SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT
        <where>
            <if test="statusFlag==null or statusFlag==''">
                AND acct_status != 'C'
            </if>
            <if test="cardNo !=null and cardNo !=''">
                AND card_no = #{cardNo}
            </if>
            <if test="statusFlag== '2'.toString()">
                AND acct_status = 'C'
            </if>
            <if test="baseAcctNo != null and baseAcctNo !=''">
                AND (base_acct_no =#{baseAcctNo} or card_no=#{baseAcctNo})
            </if>
            <if test="acctType != null and acctType !=''">
                AND ACCT_TYPE = #{acctType}
            </if>
            <if test="isQueryTAcctType == false">
                AND ACCT_TYPE != 'T'
            </if>
            <if test="prodType != null and prodType !=''">
                AND prod_type = #{prodType}
            </if>
            <if test="acctCcy != null and acctCcy !=''">
                AND ACCT_CCY = #{acctCcy}
            </if>
            <if test="acctSeqNo != null and acctSeqNo !=''">
                AND ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="clientNo != null and clientNo !=''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="acctStatus != null and acctStatus !=''">
                AND ACCT_STATUS = #{acctStatus}
            </if>
            <if test="acctName != null and acctName !=''">
                AND ACCT_NAME = #{acctName}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <if test="balType != null and balType !=''">
                AND BAL_TYPE = #{balType}
            </if>
            <if test="term != null and term !=''">
                AND TERM = #{term}
            </if>
            <if test="termType != null and termType !=''">
                AND TERM_TYPE = #{termType}
            </if>
        </where>
        ORDER BY base_acct_no,ACCT_SEQ_NO+0 ASC )
        where rownum <![CDATA[<=]]> 1000
    </select>

    <select id="getMbAcctAllByClientNoJS" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct" databaseId="oracle">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT
        WHERE client_no = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and rownum=1
        ORDER BY acct_open_date, base_acct_no,ACCT_SEQ_NO ASC
    </select>

</mapper>
