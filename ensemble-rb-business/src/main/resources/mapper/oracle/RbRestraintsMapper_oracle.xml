<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">

  <select id="selectHighPriAmtBySeqNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" databaseId="oracle">
    SELECT NVL(SUM(PLEDGED_AMT), 0) PLEDGED_AMT
    FROM RB_restraints
    WHERE RESTRAINTS_STATUS =  'A'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    AND restraint_type in
    <foreach item="item" index="index" collection="resType" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND res_priority &gt; (select res_priority
    from RB_restraints
    where res_seq_no = #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
    AND res_priority &lt;&gt; '0'
    AND internal_key = (select internal_key
    from RB_restraints
    where res_seq_no = #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
  </select>
  <select id="selectBefSeqAmtBySeqNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" databaseId="oracle">
    SELECT NVL(SUM(PLEDGED_AMT), 0) PLEDGED_AMT
    FROM RB_restraints
    WHERE #{resSeqNo} > res_seq_no
    AND RESTRAINTS_STATUS =  'A'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    AND restraint_type in
    <foreach item="item" index="index" collection="resType" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND res_priority = (select res_priority
    from RB_restraints
    where res_seq_no	= #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
    AND internal_key = (select internal_key
    from RB_restraints
    where res_seq_no = #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
    AND WAIT_SEQ <![CDATA[  <  ]]> (select WAIT_SEQ
    from RB_restraints
    where res_seq_no = #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
  </select>
</mapper>
