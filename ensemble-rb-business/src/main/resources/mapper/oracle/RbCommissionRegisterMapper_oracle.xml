<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">

  <select id="getMbCommissionRegister"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister" databaseId="oracle">
    select <include refid="Base_Column"/>
    from RB_COMMISSION_REGISTER
    <where>
      <if test="commissionDocumentType != null">
        COMMISSION_DOCUMENT_TYPE = #{commissionDocumentType}
      </if>
      <if test="commissionDocumentId != null">
        AND COMMISSION_DOCUMENT_ID = #{commissionDocumentId}
      </if>
      <if test="country != null">
        AND COUNTRY = #{country}
      </if>
      <if test="commissionClientName != null">
        AND COMMISSION_CLIENT_NAME   = #{commissionClientName}
      </if>
      <if test="baseAcctNo != null">
        AND BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test="referenceNo != null">
        AND CHANNEL_SEQ_NO = #{referenceNo}
      </if>
      <if test="tranStartDate != null">
        AND to_char(TRAN_DATE,'yyyymmdd') <![CDATA[>= ]]>  #{tranStartDate}
      </if>
      <if test="tranEndDate != null">
        AND to_char(TRAN_DATE,'yyyymmdd') <![CDATA[<= ]]>   #{tranEndDate}
      </if>
      <if test="clientNo != null and clientNo !='' " >
        AND CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>


</mapper>
