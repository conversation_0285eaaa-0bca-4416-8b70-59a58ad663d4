<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
    <delete id="deleteByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub" databaseId="oracle">
        delete from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND MAIN_SUB_IND = #{mainSubInd}
        AND nvl(CR_TOTAL_AMT,0) <![CDATA[ <= 0 ]]>
        AND CLIENT_NO = #{clientNo}
    </delete>
</mapper>
