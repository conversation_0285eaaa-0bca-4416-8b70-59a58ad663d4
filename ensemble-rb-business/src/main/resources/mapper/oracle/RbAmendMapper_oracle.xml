<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
    <!--Rb013 需求查询最后一次账户的登记时间 -->
    <select id="getAmendAttrInfoByAmendKeyAndAmendType" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        select * From(
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        <where>
            <if test="amendKey != null and amendKey.length() > 0">
                AND AMEND_KEY = #{amendKey,jdbcType=VARCHAR}
            </if>
            <if test="amendType != null and amendType.length()>0">
                AND AMEND_TYPE = #{amendType,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY TRAN_TIMESTAMP DESC
        )
        where  ROWNUM = 1
    </select>
</mapper>
