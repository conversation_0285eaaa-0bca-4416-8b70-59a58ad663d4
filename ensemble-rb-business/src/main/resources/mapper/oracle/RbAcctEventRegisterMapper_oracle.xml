<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
    <select id="getLastEventRegister" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister" databaseId="oracle">
        SELECT
        *
        FROM
        (
        SELECT * FROM
        RB_ACCT_EVENT_REGISTER
        <where>
        <if test="internalKey != null and internalKey != '' ">
            AND  INTERNAL_KEY = #{internalKey}
        </if>
        </where>
        order by TRAN_TIMESTAMP desc
        ) AS t
        WHERE ROWNUM = 1
    </select>

    <select id="getRbAcctCycleHist" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister" databaseId="oracle">
        SELECT <include refid="Base_Column"/>
        FROM RB_ACCT_EVENT_REGISTER
        WHERE INTERNAL_KEY = #{internalKey}
        <if test="clientNo != null and  clientNo != '' ">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="userId != null and userId.length() > 0">
            AND USER_ID = #{userId}
        </if>
        <if test="captDate != null ">
            AND to_char(TRAN_DATE,'yyyymmdd') = to_char(#{captDate},'yyyymmdd')
        </if>
        AND (TRAN_STATUS IS NULL OR TRAN_STATUS != 'R')
        order by tran_date desc
    </select>

    <select id="getListRefence" parameterType="java.util.HashMap" resultType="java.lang.String" databaseId="oracle">
        select REFERENCE
        from RB_ACCT_EVENT_REGISTER
        where CLIENT_NO = #{clientNo}
        <!--多法人改造 by LIYUANV-->
        <if test="companyList != null">
            AND COMPANY in
            <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and to_char(TRAN_DATE,'yyyymmdd') between #{startDate} and  #{endDate}
    </select>

    <select id="getZxqylist" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister" databaseId="oracle">
        SELECT <include refid="Base_Column"/>
        FROM RB_ACCT_EVENT_REGISTER
        WHERE BASE_ACCT_NO = #{actualBaseAcctNo}
        and PROD_TYPE in ('41020','12014','12015')
        and  to_char(TRAN_DATE,'yyyymmdd') between #{startDate} and  #{endDate}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

</mapper>
