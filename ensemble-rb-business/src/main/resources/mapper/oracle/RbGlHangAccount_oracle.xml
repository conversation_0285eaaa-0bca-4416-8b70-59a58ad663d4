<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
    <select id="getHangAccountByInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount" databaseId="oracle">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null  and hangSeqNo != '' ">
            AND HANG_SEQ_NO = #{hangSeqNo}
        </if>
        <if test="hangAmt != null  and hangAmt != '' ">
            AND HANG_AMT = #{hangAmt}
        </if>
        <if test="startDate != null  and startDate != '' ">
            AND TRAN_DATE <![CDATA[>=]]> to_date(#{startDate},' yyyy-MM-dd')
        </if>
        <if test="endDate != null  and endDate != '' ">
            AND TRAN_DATE <![CDATA[<=]]> to_date(#{endDate},' yyyy-MM-dd')
        </if>
        ORDER BY HANG_SEQ_NO, SUB_HANG_SEQ_NO
    </select>
</mapper>
