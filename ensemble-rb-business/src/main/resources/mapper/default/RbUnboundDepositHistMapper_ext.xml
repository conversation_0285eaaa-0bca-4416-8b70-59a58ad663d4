<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbUnboundDepositHist">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUnboundDepositHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUnboundDepositHist">
    select <include refid="Base_Column" />
    from RB_UNBOUND_DEPOSIT_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUnboundDepositHist">
    delete from RB_UNBOUND_DEPOSIT_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUnboundDepositHist">
    update RB_UNBOUND_DEPOSIT_HIST
    <set>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="agreementStatus != null">
        AGREEMENT_STATUS = #{agreementStatus},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="company != null">
        COMPANY = #{company}
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUnboundDepositHist">
    insert into RB_UNBOUND_DEPOSIT_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="agreementStatus != null">
        AGREEMENT_STATUS,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="agreementStatus != null">
        #{agreementStatus},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
</mapper>
