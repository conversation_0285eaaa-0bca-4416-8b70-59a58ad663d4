<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFileStatus">

    <update id="updateFileStatusByBatchNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFileStatus">
        update Rb_Agreement_File_Status
        <set>
            <if test="status != null and  status != '' ">
                STATUS = #{status, jdbcType=VARCHAR},
            </if>
        </set>
        where
        1=1
        <if test="batchNo != null and  batchNo != '' ">
           and Batch_No = #{batchNo, jdbcType=VARCHAR}
        </if>
    </update>

</mapper>
