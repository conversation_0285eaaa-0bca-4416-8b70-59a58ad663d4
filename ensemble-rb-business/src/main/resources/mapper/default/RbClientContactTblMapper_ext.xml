<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientContactTbl">

    <select id="getRbClientContactTblByInternalKeys" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientContactTbl">
        SELECT
        <include refid="Base_Column"/>
        from RB_CLIENT_CONTACT_TBL
        <where>
            <trim suffixOverrides="AND">
                <if test="internalKeys != null and internalKeys.size() > 0">
                    internal_key in
                    <foreach collection="internalKeys" open="(" close=")" separator="," index="index"
                             item="internalKey">
                        #{internalKey}
                    </foreach>
                </if>
                <if test="clientNo != null">
                    AND CLIENT_NO = #{clientNo}
                </if>
            </trim>
        </where>
    </select>

    <update id="updateRbClientContactTblByInternalKeys" parameterType="java.util.Map">
        UPDATE
        RB_CLIENT_CONTACT_TBL
        SET
        ADDRESS = #{address} ,
        COUNTRY = #{country},
        CITY = #{city},
        CITY_DIST = #{cityDist}，
        STATE = #{state},
        POSTAL_CODE = #{postalCode},
        CONTACT_TYPE = #{contactType},
        MOBILE_PHONE = #{mobilePhone},
        EMAIL = #{email}
        <where>
            <trim suffixOverrides="AND">
                <choose>
                    <when test="internalKeys != null and internalKeys.size() > 0">
                        internal_key in
                        <foreach collection="internalKeys" open="(" close=")" separator="," index="index"
                                 item="internalKey">
                            #{internalKey}
                        </foreach>
                    </when>
                    <otherwise>
                        INTERNAL_KEY = NULL
                    </otherwise>
                </choose>
                <if test="clientNo != null">
                    AND CLIENT_NO = #{clientNo}
                </if>
            </trim>
        </where>
    </update>
</mapper>
