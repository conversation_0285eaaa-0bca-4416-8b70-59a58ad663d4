<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementStatement">

    <select id="getActiveAgreementByClientNoAndId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementStatement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_STATEMENT
        where AGREEMENT_STATUS = 'A'
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="agreementId != null and agreementId != ''">
            AND AGREEMENT_ID = #{agreementId}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <update id="updateByCondition" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementStatement">
        update RB_AGREEMENT_STATEMENT
        <set>
            <if test="agreementId != null and agreementId != '' ">
                AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != '' ">
                CHANNEL = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="tranBranch != null and tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="signDate != null ">
                SIGN_DATE = #{signDate,jdbcType=DATE},
            </if>
            <if test="userId != null and userId != '' ">
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="startDate != null ">
                START_DATE = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null ">
                END_DATE = #{endDate,jdbcType=DATE},
            </if>
            <if test="agreementStatus != null and agreementStatus != '' ">
                AGREEMENT_STATUS = #{agreementStatus,jdbcType=VARCHAR},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>
            <if test="pushFlag != null and pushFlag != '' ">
                PUSH_FLAG = #{pushFlag,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != '' ">
                EMAIL = #{email,jdbcType=VARCHAR},
            </if>
            <if test="billingCycle != null and billingCycle != '' ">
                BILLING_CYCLE = #{billingCycle,jdbcType=VARCHAR},
            </if>
            <if test="lastBillingDate != null ">
                LAST_BILLING_DATE = #{lastBillingDate,jdbcType=DATE},
            </if>
            <if test="nextBillingDate != null ">
                NEXT_BILLING_DATE = #{nextBillingDate,jdbcType=DATE},
            </if>
        </set>
        <where>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO=#{clientNo}
            </if>
            AND agreement_id = #{agreementId}
        </where>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
</mapper>
