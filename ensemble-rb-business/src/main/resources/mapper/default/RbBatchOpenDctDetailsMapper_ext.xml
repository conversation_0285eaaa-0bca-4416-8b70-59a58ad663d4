<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails">

  <select id="getByPrimaryKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DCT_DETAILS
    where SEQ_NO = #{seqNo} AND BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getDetails" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DCT_DETAILS
    where BATCH_NO = #{batchNo} and REVERSAL_FLAG = 'N'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by SEQ_NO
  </select>
  <select id="queryDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DCT_DETAILS
    where BATCH_NO = #{batchNo} and BATCH_OPEN_STATUS=#{batchOpenStatus}
    <!-- 多法人改造 by luocwa -->
    <if test="companyList != null">
      AND COMPANY in
      <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by SEQ_NO
  </select>
  <select id="getMbBatchOpenDctDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DCT_DETAILS
    where SEQ_NO = #{seqNo} AND BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM (1)
    from RB_BATCH_OPEN_DCT_DETAILS
    where BATCH_NO = #{batchNo}
    AND BATCH_OPEN_STATUS = 'S'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumTranAmtByBatch" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_OPEN_DCT_DETAILS
    where BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumTranAmtBySucc" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_OPEN_DCT_DETAILS
    where BATCH_NO = #{batchNo}
     AND BATCH_OPEN_STATUS = 'S'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumByPhone" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    <if test="_databaseId == 'mysql'">
      select ifnull(max(count(*)),0)
      from RB_BATCH_OPEN_DCT_DETAILS
      where BATCH_NO = #{batchNo}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      group by MOBILE_NO having count(*)>1
    </if>
    <if test="_databaseId == 'oracle'">
      select nvl(max(count(*)),0)
      from RB_BATCH_OPEN_DCT_DETAILS
      where BATCH_NO = #{batchNo}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      group by MOBILE_NO having count(*)>1
    </if>

  </select>

  <select id="getSumByLocation" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    <if test="_databaseId == 'mysql'">
      select ifnull(max(count(*)),0)
      from RB_BATCH_OPEN_DCT_DETAILS
      where BATCH_NO = #{batchNo}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      group by LOCATION having count(*)>1
    </if>
    <if test="_databaseId == 'oracle'">
      select nvl(max(count(*)),0)
      from RB_BATCH_OPEN_DCT_DETAILS
      where BATCH_NO = #{batchNo}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      group by LOCATION having count(*)>1
    </if>

  </select>

  <update id="updateExt">
    UPDATE
    <include refid="Table_Name"/>
    <include refid="Base_Set_Ext"/>
    <include refid="PrimaryKey_Where_Ext"/>
  </update>
  <update id="updateExt1" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails">
    UPDATE RB_BATCH_OPEN_DCT_DETAILS
    <set>
      <if test="docType != null and docType != '' ">
        DOC_TYPE = #{docType,jdbcType=VARCHAR},
      </if>
      <if test="prefix != null and prefix != '' ">
        PREFIX = #{prefix},
      </if>
      <if test="voucherNo != null and voucherNo != '' ">
        VOUCHER_NO = #{voucherNo},
      </if>
      <if test="printCnt != null and printCnt != '' ">
        PRINT_CNT = #{printCnt},
      </if>
    </set>
    WHERE BATCH_NO = #{batchNo} AND SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <sql id="PrimaryKey_Where_Ext">
    <where>
      <if test="batchNo != null and  batchNo != '' ">
        AND BATCH_NO = #{batchNo,jdbcType=VARCHAR}
      </if>
      <if test="seqNo != null and  seqNo != '' ">
        AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </sql>

  <sql id="Base_Set_Ext">
    <set>
      <if test="jobRunId != null ">JOB_RUN_ID = #{jobRunId,jdbcType=VARCHAR},</if>
      <if test="batchNo != null ">BATCH_NO = #{batchNo,jdbcType=VARCHAR},</if>
      <if test="seqNo != null ">SEQ_NO = #{seqNo,jdbcType=VARCHAR},</if>
      <if test="channelSeqNo != null ">CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR},</if>
      <if test="subSeqNo != null ">SUB_SEQ_NO = #{subSeqNo,jdbcType=VARCHAR},</if>
      <if test="reference != null ">REFERENCE = #{reference,jdbcType=VARCHAR},</if>
      <if test="openBranch != null ">OPEN_BRANCH = #{openBranch,jdbcType=VARCHAR},</if>
      <if test="clientNo != null ">CLIENT_NO = #{clientNo,jdbcType=VARCHAR},</if>
      <if test="documentId != null ">DOCUMENT_ID = #{documentId,jdbcType=VARCHAR},</if>
      <if test="documentType != null ">DOCUMENT_TYPE = #{documentType,jdbcType=VARCHAR},</if>
      <if test="issCountry != null ">ISS_COUNTRY = #{issCountry,jdbcType=VARCHAR},</if>
      <if test="documentExpiryDate != null ">DOCUMENT_EXPIRY_DATE = #{documentExpiryDate,jdbcType=DATE},</if>
      <if test="birthday != null ">BIRTHDAY = #{birthday,jdbcType=DATE},</if>
      <if test="sex != null ">SEX = #{sex,jdbcType=VARCHAR},</if>
      <if test="chClientName != null ">CH_CLIENT_NAME = #{chClientName,jdbcType=VARCHAR},</if>
      <if test="clientName != null ">CLIENT_NAME = #{clientName,jdbcType=VARCHAR},</if>
      <if test="clientType != null ">CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},</if>
      <if test="location != null ">LOCATION = #{location,jdbcType=VARCHAR},</if>
      <if test="occupationCode != null ">OCCUPATION_CODE = #{occupationCode,jdbcType=VARCHAR},</if>
      <if test="inlandOffshore != null ">INLAND_OFFSHORE = #{inlandOffshore,jdbcType=VARCHAR},</if>
      <if test="email != null ">EMAIL = #{email,jdbcType=VARCHAR},</if>
      <if test="mobileNo != null ">MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},</if>
      <if test="contactType != null ">CONTACT_TYPE = #{contactType,jdbcType=VARCHAR},</if>
      <if test="phoneNo != null ">PHONE_NO = #{phoneNo,jdbcType=VARCHAR},</if>
      <if test="contactAddress != null ">CONTACT_ADDRESS = #{contactAddress,jdbcType=VARCHAR},</if>
      <if test="coprName != null ">COPR_NAME = #{coprName,jdbcType=VARCHAR},</if>
      <if test="cardPbInd != null ">CARD_PB_IND = #{cardPbInd,jdbcType=VARCHAR},</if>
      <if test="baseAcctNo != null ">BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},</if>
      <if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
      <if test="openCcy != null ">OPEN_CCY = #{openCcy,jdbcType=VARCHAR},</if>
      <if test="term != null ">TERM = #{term,jdbcType=VARCHAR},</if>
      <if test="termType != null ">TERM_TYPE = #{termType,jdbcType=VARCHAR},</if>
      <if test="acctNature != null ">ACCT_NATURE = #{acctNature,jdbcType=VARCHAR},</if>
      <if test="effectDate != null ">EFFECT_DATE = #{effectDate,jdbcType=DATE},</if>
      <if test="withdrawalType != null ">WITHDRAWAL_TYPE = #{withdrawalType,jdbcType=VARCHAR},</if>
      <if test="docType != null ">DOC_TYPE = #{docType,jdbcType=VARCHAR},</if>
      <if test="prefix != null ">PREFIX = #{prefix,jdbcType=VARCHAR},</if>
      <if test="voucherNo != null ">VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},</if>
      <if test="tranType != null ">TRAN_TYPE = #{tranType,jdbcType=VARCHAR},</if>
      <if test="tranAmt != null ">TRAN_AMT = #{tranAmt,jdbcType=DECIMAL},</if>
      <if test="balType != null ">BAL_TYPE = #{balType,jdbcType=VARCHAR},</if>
      <if test="autoRenewRollover != null ">AUTO_RENEW_ROLLOVER = #{autoRenewRollover,jdbcType=VARCHAR},</if>
      <if test="cashItem != null ">CASH_ITEM = #{cashItem,jdbcType=VARCHAR},</if>
      <if test="othAcctNo != null ">OTH_ACCT_NO = #{othAcctNo,jdbcType=VARCHAR},</if>
      <if test="othProdType != null ">OTH_PROD_TYPE = #{othProdType,jdbcType=VARCHAR},</if>
      <if test="othAcctCcy != null ">OTH_ACCT_CCY = #{othAcctCcy,jdbcType=VARCHAR},</if>
      <if test="othAcctSeqNo != null ">OTH_ACCT_SEQ_NO = #{othAcctSeqNo,jdbcType=VARCHAR},</if>
      <if test="othAcctName != null ">OTH_ACCT_NAME = #{othAcctName,jdbcType=VARCHAR},</if>
      <if test="othBalType != null ">OTH_BAL_TYPE = #{othBalType,jdbcType=VARCHAR},</if>
      <if test="othDocType != null ">OTH_DOC_TYPE = #{othDocType,jdbcType=VARCHAR},</if>
      <if test="othPrefix != null ">OTH_PREFIX = #{othPrefix,jdbcType=VARCHAR},</if>
      <if test="othVoucherNo != null ">OTH_VOUCHER_NO = #{othVoucherNo,jdbcType=VARCHAR},</if>
      <if test="signTime != null ">SIGN_TIME = #{signTime,jdbcType=DATE},</if>
      <if test="printCnt != null ">PRINT_CNT = #{printCnt,jdbcType=INTEGER},</if>
      <if test="reversalFlag != null ">REVERSAL_FLAG = #{reversalFlag,jdbcType=VARCHAR},</if>
      <if test="batchOpenStatus != null ">BATCH_OPEN_STATUS = #{batchOpenStatus,jdbcType=VARCHAR},</if>
      <if test="errorCode != null ">ERROR_CODE = #{errorCode,jdbcType=VARCHAR},</if>
      <if test="errorDesc != null ">ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},</if>
      <if test="retMsg != null ">RET_MSG = #{retMsg,jdbcType=VARCHAR},</if>
      <if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},</if>
      <if test="company != null ">COMPANY = #{company,jdbcType=VARCHAR},</if>
    </set>
  </sql>


  <select id="getNotPrintVoucherbyBatchNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDctDetails">
    select distinct batch_no
    from RB_BATCH_OPEN_DCT_DETAILS
    where batch_open_status='S'
    and open_branch=#{openBranch}
    and print_cnt='0'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
