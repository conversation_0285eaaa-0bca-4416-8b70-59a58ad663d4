<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult">

    <update id="updRbBatchBabInternalResult" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult">
        update RB_BATCH_BAB_INTERNAL_RESULT
        <set>
            <if test="retCode != null and  retCode != '' ">
                Ret_Code = #{retCode, jdbcType=VARCHAR},
            </if>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null and  errorCode != '' ">
                Error_Code = #{errorCode, jdbcType=VARCHAR},
            </if>
            <if test="errorDesc != null and  errorDesc != '' ">
                Error_Desc = #{errorDesc, jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                CLIENT_NO = #{clientNo, jdbcType=VARCHAR},
            </if>
            <if test="branch != null and  branch != '' ">
                BRANCH = #{branch, jdbcType=VARCHAR},
            </if>
        </set>
        <where>
        <if test="batchNo != null and  batchNo != '' ">
           and Batch_No = #{batchNo, jdbcType=VARCHAR}
        </if>
        <if test="seqNo != null and  seqNo != '' ">
            and Seq_No = #{seqNo, jdbcType=VARCHAR}
        </if>
        </where>
    </update>
    <update id="upBatchBabInternalResultStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult">
        update RB_BATCH_BAB_INTERNAL_RESULT
        <set>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
        </set>
        <where>
        <if test="baseAcctNo != null and  baseAcctNo != '' ">
            and BASE_ACCT_NO = #{baseAcctNo, jdbcType=VARCHAR}
        </if>
        <if test="prodType != null and  prodType != '' ">
            and PROD_TYPE = #{prodType, jdbcType=VARCHAR}
        </if>
        <if test="acctSeqNo != null and  acctSeqNo != '' ">
            and ACCT_SEQ_NO = #{acctSeqNo, jdbcType=VARCHAR}
        </if>
        <if test="acctCcy != null and  acctCcy != '' ">
            and ACCT_CCY = #{acctCcy, jdbcType=VARCHAR}
        </if>
        </where>
    </update>
    <update id="updateResultByApprovalNoAndAcctNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult">
        update RB_BATCH_BAB_INTERNAL_RESULT
        <set>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
        </set>
        <where>
        <if test="baseAcctNo != null and  baseAcctNo != '' ">
            and BASE_ACCT_NO = #{baseAcctNo, jdbcType=VARCHAR}
        </if>
        <if test="approvalNo != null and  approvalNo != '' ">
            and APPROVAL_NO = #{approvalNo, jdbcType=VARCHAR}
        </if>
        <if test="acctSeqNo != null and  acctSeqNo != '' ">
            and ACCT_SEQ_NO = #{acctSeqNo, jdbcType=VARCHAR}
        </if>
        </where>
    </update>
    <select id="selectListByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult">
        select <include refid="Base_Column"/>
        from <include refid="Table_Name"/>
        where approval_no = #{approvalNo}
        <if test="batchStatus != null and batchStatus != ''">
            and batch_status = #{batchStatus}
        </if>
        <if test="branchList != null and branchList.size() > 0">
            AND BRANCH IN
            <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
                #{branch}
            </foreach>
        </if>
    </select>


    <select id="selectBabInternalResultForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult">
        SELECT <include refid="Base_Column"/>
        FROM RB_BATCH_BAB_INTERNAL_RESULT
        WHERE BATCH_NO = #{batchNo}
        AND SEQ_NO = #{seqNo}
        AND BATCH_STATUS = 'P' FOR UPDATE
    </select>

    <update id="updateStatusNoTransaction"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchBabInternalResult">
        UPDATE RB_BATCH_BAB_INTERNAL_RESULT
        <set>
            BATCH_STATUS = #{batchStatus}
        </set>
        WHERE BATCH_NO = #{batchNo, jdbcType=VARCHAR}
        AND SEQ_NO = #{seqNo, jdbcType=VARCHAR}
        AND BATCH_STATUS = 'P'
    </update>
</mapper>
