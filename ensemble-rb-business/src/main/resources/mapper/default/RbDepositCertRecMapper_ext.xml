<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">

  <update id="updateByDepositCertNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
    <!-- WARNING - @mbggenerated This element is automatically generated by
        Galaxy Tools Generator, do not modify. This element was generated on Thu
        Jan 29 10:48:04 CST 2015. -->
    update RB_DEPOSIT_CERT_REC
    <set>
      <if test="delAuthUserId != null">
        del_auth_user_id = #{delAuthUserId},
      </if>
      <if test="delUserId != null">
        del_user_id = #{delUserId},
      </if>
      <if test="deleteDate != null">
        delete_date = #{deleteDate},
      </if>
      <if test="depositCertStatus != null">
        DEPOSIT_CERT_STATUS = #{depositCertStatus},
      </if>
      <if test="certNum != null">
        cert_num = #{certNum},
      </if>
      <if test="voucherStartNo != null">
        VOUCHER_START_NO = #{voucherStartNo},
      </if>
      <if test="voucherEndNo != null">
        VOUCHER_END_NO = #{voucherEndNo},
      </if>
      <if test="repairReason != null">
        REPAIR_REASON=#{repairReason},
      </if>
      <if test="preReference != null">
        PRE_REFERENCE= #{preReference},
      </if>
      <if test="cancelUserId != null">
        CANCEL_USER_ID= #{cancelUserId},
      </if>
      <if test="cancelAuthUserId != null">
        CANCEL_AUTH_USER_ID= #{cancelAuthUserId},
      </if>
      <if test="repairTime != null">
        REPAIR_TIME= #{repairTime},
      </if>
      <if test="cancelReason != null">
        CANCEL_REASON= #{cancelReason},
      </if>
    </set>
    where DEPOSIT_CERT_NO = #{depositCertNo}
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- Multi-legal entity field transformation by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="getDepositCertInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
    select <include refid="Base_Column"/>
    from RB_DEPOSIT_CERT_REC
    where DEPOSIT_CERT_NO = #{depositCertNo}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectDueDepositCertByDepositCertNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
	    select <include refid="Base_Column"/>

	     from RB_DEPOSIT_CERT_REC
	     WHERE DEPOSIT_CERT_NO = #{depositCertNo}
	       AND (cert_type = 'D'
	       OR cert_type = 'T') 
	       AND DEPOSIT_CERT_STATUS='A'
    <![CDATA[AND cert_end_date >= #{runDate,jdbcType=DATE}]]>
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectDueDepositCertByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
  select <include refid="Base_Column"/>
	     from RB_DEPOSIT_CERT_REC
	     WHERE INTERNAL_KEY = #{mbInternalKey}
         <if test="clientNo != null">
           and CLIENT_NO = #{clientNo}
         </if>
        <if test="depositCerNo != null and  depositCerNo != '' ">
          and DEPOSIT_CERT_NO = #{depositCerNo}
        </if>
        <if test="depositCertStatus != null and depositCertStatus.length() > 0">
          and DEPOSIT_CERT_STATUS = #{depositCertStatus}
        </if>
    <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
</select>
  <select id="getDepositCertInfoByUserID" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
    select <include refid="Base_Column"/>
	     from RB_DEPOSIT_CERT_REC
	     WHERE USER_ID = #{userId}
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="getCompanyByDepositCertNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
    select company from RB_DEPOSIT_CERT_REC where DEPOSIT_CERT_NO = #{depositCertNo}
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="getDepositCert" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
    select <include refid="Base_Column"/>
    from RB_DEPOSIT_CERT_REC
    where 1=1
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDepositCertInfoByClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec">
    select <include refid="Base_Column"/>
    from RB_DEPOSIT_CERT_REC
    where 1=1
    <if test="depositCertNo != null and depositCertNo != ''">
      and DEPOSIT_CERT_NO = #{depositCertNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
