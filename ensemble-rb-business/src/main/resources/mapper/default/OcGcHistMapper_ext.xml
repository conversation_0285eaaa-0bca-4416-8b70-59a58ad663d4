<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcGcHist">

    <select id="selectOcSettle" parameterType="java.util.Map" resultMap="Base_Result_Map" useCache="false">
        SELECT
        <include refid="Base_Column"/>
        FROM OC_GC_HIST WHERE TRAN_DATE<![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
        AND GC_CANCLE = 'G'
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
