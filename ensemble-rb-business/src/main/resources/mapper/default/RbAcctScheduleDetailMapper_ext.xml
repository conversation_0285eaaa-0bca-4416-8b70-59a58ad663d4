<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.repository.acct.schedule.RbAcctScheduleDetail">
  <!-- Created by daiduan on 2016/06/07 14:18:53. -->
<sql id="Base_Column_List">
    AMT_TYPE,
    COMPANY,
    END_DATE,
    INTERNAL_KEY,
    PAID_AMT,
    PRI_OUTSTANDING,
    SCHED_AMT,
    SCHED_SEQ_NO,
    STAGE_NO,
    START_DATE,
    TRAN_TIMESTAMP
  </sql>

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail">
    select <include refid="Base_Column_List"/>
    from RB_ACCT_SCHEDULE_DETAIL
    where SCHED_SEQ_NO = #{schedSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail">
    delete from RB_ACCT_SCHEDULE_DETAIL
    where SCHED_SEQ_NO = #{schedSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail">
    update RB_ACCT_SCHEDULE_DETAIL
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="stageNo != null">
        STAGE_NO = #{stageNo},
      </if>
      <if test="amtType != null">
        AMT_TYPE = #{amtType},
      </if>
      <if test="startDate != null">
        START_DATE = #{startDate},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate},
      </if>
      <if test="schedAmt != null">
        SCHED_AMT = #{schedAmt},
      </if>
      <if test="paidAmt != null">
        PAID_AMT = #{paidAmt},
      </if>
      <if test="priOutstanding != null">
        PRI_OUTSTANDING = #{priOutstanding},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where SCHED_SEQ_NO = #{schedSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail">
    insert into RB_ACCT_SCHEDULE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schedSeqNo != null">
        SCHED_SEQ_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="stageNo != null">
        STAGE_NO,
      </if>
      <if test="amtType != null">
        AMT_TYPE,
      </if>
      <if test="startDate != null">
        START_DATE,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="schedAmt != null">
        SCHED_AMT,
      </if>
      <if test="paidAmt != null">
        PAID_AMT,
      </if>
      <if test="priOutstanding != null">
        PRI_OUTSTANDING,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
         TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schedSeqNo != null">
        #{schedSeqNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="stageNo != null">
        #{stageNo},
      </if>
      <if test="amtType != null">
        #{amtType},
      </if>
      <if test="startDate != null">
        #{startDate},
      </if>
      <if test="endDate != null">
        #{endDate},
      </if>
      <if test="schedAmt != null">
        #{schedAmt},
      </if>
      <if test="paidAmt != null">
        #{paidAmt},
      </if>
      <if test="priOutstanding != null">
        #{priOutstanding},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <select id="getMbAcctScheduleDetail" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail">
    select <include refid="Base_Column_List"/>
    from RB_acct_schedule_detail
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <!-- order by stage_no+'',start_date 修改贷款明细查询结果排序-->
    order by sched_seq_no asc
  </select>
  <select id="getMbAcctScheduleDetailByBaseAcctNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail">
    select b.AMT_TYPE,b.START_DATE,b.END_DATE,sum(b.SCHED_AMT) sched_amt from RB_acct a, RB_acct_schedule_detail b
    where  a.INTERNAL_KEY = b.INTERNAL_KEY
    and base_acct_no = #{baseAcctNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND b.COMPANY = #{company}
    </if>
    group by b.AMT_TYPE,b.START_DATE,b.END_DATE
    order by b.START_DATE+0

  </select>
  <select id="getMbAcctScheduleDetailByIntClass" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail">
    select <include refid="Base_Column_List"/>
    from RB_acct_schedule_detail
    where INTERNAL_KEY = #{internalKey}
    and AMT_TYPE=#{amtType}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <!-- order by stage_no+'',start_date 修改贷款明细查询结果排序-->
    order by END_DATE asc
  </select>
</mapper>
