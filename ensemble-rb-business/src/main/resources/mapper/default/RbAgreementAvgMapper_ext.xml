<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAvg">

    <select id="getAgreementEffecting" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAvg" parameterType="java.util.Map" >
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            AGREEMENT_STATUS='A'
            <if test="agreementId != null and  agreementId != '' ">
                AND AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="runDate != null">
                AND START_DATE <![CDATA[ <= ]]> #{runDate,jdbcType=DATE}
                AND END_DATE <![CDATA[ >= ]]> #{runDate,jdbcType=DATE}
            </if>
        </where>
    </select>

</mapper>