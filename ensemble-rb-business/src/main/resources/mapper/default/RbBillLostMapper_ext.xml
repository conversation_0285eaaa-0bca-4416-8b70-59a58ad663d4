<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
    <select id="getMbPnLostBylossNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        select <include refid="Base_Column" />
        from RB_BILL_LOST
        where LOSS_NO = #{lossNo}
        <if test="validFlag != null and validFlag !=''">
            AND TRAN_VALID_FLAG = #{validFlag}
        </if>
    </select>

    <select id="getMbPnLostByBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        select <include refid="Base_Column" />
        from RB_BILL_LOST
        where BILL_NO = #{billNo}
        <if test="validFlag != null and validFlag !=''">
            AND TRAN_VALID_FLAG = #{validFlag}
        </if>
    </select>

    <select id="selectRbPnLostList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <trim suffixOverrides="AND">
                <if test="branchId != null and  branchId != '' ">
                    BRANCH_ID = #{branchId} AND
                </if>
                <if test="docType != null and  docType != '' ">
                    DOC_TYPE = #{docType} AND
                </if>
                <if test="docClass != null and  docClass != '' ">
                    DOC_CLASS = #{docClass} AND
                </if>
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billNo != null and  billNo != '' ">
                    BILL_NO = #{billNo} AND
                </if>
                <if test="tranBeginDate != null ">
                    TRAN_DATE &gt;= #{tranBeginDate,jdbcType=DATE} AND
                </if>
                <if test="tranEndDate != null ">
                    TRAN_DATE &lt;= #{tranEndDate,jdbcType=DATE} AND
                </if>
                <if test="lossNo != null and  lossNo != '' ">
                    LOSS_NO = #{lossNo} AND
                </if>
                <if test="billAmt != null ">
                    BILL_AMT = #{billAmt} AND
                </if>
            </trim>
        </where>
    </select>
    <select id="getMbPnLostByLossBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        select <include refid="Base_Column" />
        from RB_BILL_LOST
        where BILL_NO = #{billNo}
        <if test="lossNo != null and lossNo !=''">
            AND LOSS_NO = #{lossNo}
        </if>
        <if test="validFlag != null and validFlag !=''">
            AND TRAN_VALID_FLAG = #{validFlag}
        </if>
    </select>

    <update id="updateRbBillLost" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="tranValidFlag != null and  tranValidFlag != '' ">
                TRAN_VALID_FLAG = #{tranValidFlag},
            </if>
            <if test="unlostTelNo != null and  unlostTelNo != '' ">
                UNLOST_TEL_NO = #{unlostTelNo},
            </if>
            <if test="unlostDocumentId != null and  unlostDocumentId != '' ">
                UNLOST_DOCUMENT_ID = #{unlostDocumentId},
            </if>
            <if test="unlostDocumentType != null and  unlostDocumentType != '' ">
                UNLOST_DOCUMENT_TYPE = #{unlostDocumentType},
            </if>
            <if test="unlostTime != null ">
                UNLOST_TIME = #{unlostTime},
            </if>
            <if test="unlostName != null and  unlostName != '' ">
                UNLOST_NAME = #{unlostName},
            </if>
            <if test="unlostReason != null and  unlostReason != '' ">
                UNLOST_REASON = #{unlostReason},
            </if>
            <if test="unlostUser != null and  unlostUser != '' ">
                UNLOST_USER = #{unlostUser},
            </if>
            <if test="unlostBranch != null and  unlostBranch != '' ">
                UN_LOST_BRANCH = #{unlostBranch},
            </if>
            <if test="adviceNoteNo != null and  adviceNoteNo != '' ">
                ADVICE_NOTE_NO = #{adviceNoteNo},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <if test="lossNo != null and  lossNo != '' ">
                    LOSS_NO = #{lossNo} AND
                </if>
                <if test="billNo != null and  billNo != '' ">
                    BILL_NO = #{billNo} AND
                </if>
            </trim>
        </where>
    </update>

    <select id="getBillLostByStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <trim suffixOverrides="AND">
                <if test="tranValidFlag != null and  tranValidFlag != '' ">
                    TRAN_VALID_FLAG = #{tranValidFlag} AND
                </if>
                <if test="statusArr != null and statusArr != ''">
                    LOST_STATUS IN
                    <foreach item="item" index="index" collection="statusArr" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND
                </if>
            </trim>
        </where>
    </select>

    <select id="selectRbPnLostListByPage" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <trim suffixOverrides="AND">
                <if test="branchId != null and  branchId != '' ">
                    BRANCH_ID = #{branchId} AND
                </if>
                <if test="docType != null and  docType != '' ">
                    DOC_TYPE = #{docType} AND
                </if>
                <if test="docClass != null and  docClass != '' ">
                    DOC_CLASS = #{docClass} AND
                </if>
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billNo != null and  billNo != '' ">
                    BILL_NO = #{billNo} AND
                </if>
                <if test="tranBeginDate != null ">
                    TRAN_DATE &gt;= #{tranBeginDate,jdbcType=DATE} AND
                </if>
                <if test="tranEndDate != null ">
                    TRAN_DATE &lt;= #{tranEndDate,jdbcType=DATE} AND
                </if>
                <if test="lossNo != null and  lossNo != '' ">
                    LOSS_NO = #{lossNo} AND
                </if>
                <if test="billAmt != null ">
                    BILL_AMT = #{billAmt} AND
                </if>
                <if test="lostStatus != null and  lostStatus != ''">
                    LOST_STATUS = #{lostStatus} AND
                </if>
                <if test="tranValidFlagArry != null">
                    TRAN_VALID_FLAG IN
                    <foreach item="item" index="index" collection="tranValidFlagArry" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND
                </if>
                <if test="branches != null and branches.size()>0">
                    BRANCH_ID IN
                    <foreach item="item" index="index" collection="branches" open="(" separator="," close=")">
                        #{item.branch}
                    </foreach>
                    AND
                </if>
                <if test="minSignAmt != null ">
                    BILL_AMT <![CDATA[>=]]> #{minSignAmt} AND
                </if>
                <if test="maxSignAmt != null ">
                    BILL_AMT <![CDATA[<=]]> #{maxSignAmt} AND
                </if>
            </trim>
        </where>
    </select>

    <select id="getLostByBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost">
        select <include refid="Base_Column" />
        from RB_BILL_LOST
        where BILL_NO = #{billNo}
        <if test="clientNo != null and  clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        order by LOSS_NO desc
    </select>
</mapper>