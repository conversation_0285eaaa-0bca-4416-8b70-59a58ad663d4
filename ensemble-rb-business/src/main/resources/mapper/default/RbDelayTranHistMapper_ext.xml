<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    delete from RB_DELAY_TRAN_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    update RB_DELAY_TRAN_HIST
    <set>
      <if test="signTimestamp != null">
        SIGN_TIMESTAMP = #{signTimestamp},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        ACCT_CCY = #{acctCcy},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="terminalId != null">
        TERMINAL_ID = #{terminalId},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="narrative != null">
        NARRATIVE = #{narrative},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="tranDesc != null">
        TRAN_DESC = #{tranDesc},
      </if>
      <if test="cashItem != null">
        CASH_ITEM = #{cashItem},
      </if>
      <if test="tranNote != null">
        TRAN_NOTE = #{tranNote},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientName != null">
        CLIENT_NAME = #{clientName},
      </if>
      <if test="othInternalKey != null">
        OTH_INTERNAL_KEY = #{othInternalKey},
      </if>
      <if test="othBaseAcctNo != null">
        OTH_BASE_ACCT_NO = #{othBaseAcctNo},
      </if>
      <if test="othAcctSeqNo != null">
        OTH_ACCT_SEQ_NO = #{othAcctSeqNo},
      </if>
      <if test="othProdType != null">
        OTH_PROD_TYPE = #{othProdType},
      </if>
      <if test="othAcctCcy != null">
        OTH_ACCT_CCY = #{othAcctCcy},
      </if>
      <if test="othAcctDesc != null">
        OTH_ACCT_DESC = #{othAcctDesc},
      </if>
      <if test="payUnit != null">
        PAY_UNIT = #{payUnit},
      </if>
      <if test="commissionClientName != null">
        COMMISSION_CLIENT_NAME = #{commissionClientName},
      </if>
      <if test="commissionClientTel != null">
        COMMISSION_CLIENT_TEL = #{commissionClientTel},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="track2 != null">
        TRACK2 = #{track2},
      </if>
      <if test="track3 != null">
        TRACK3 = #{track3},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType},
      </if>
      <if test="prefix != null">
        PREFIX = #{prefix},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="exchangeTranCode != null">
        EXCHANGE_TRAN_CODE = #{exchangeTranCode},
      </if>
      <if test="exchangeTranCodet != null">
        EXCHANGE_TRAN_CODET = #{exchangeTranCodet},
      </if>
      <if test="othAcctName != null">
        OTH_ACCT_NAME = #{othAcctName},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="cancelReason != null">
        CANCEL_REASON = #{cancelReason},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference}
      </if>
      <if test="tranMethod != null">
        TRAN_METHOD = #{tranMethod}
      </if>
      <if test="cardNo != null">
        CARD_NO = #{cardNo}
      </if>
      <if test="othCardNo != null">
        OTH_CARD_NO = #{othCardNo}
      </if>
      <if test="settleCardFlag != null">
        SETTLE_CARD_FLAG = #{settleCardFlag}
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO = #{channelSeqNo}
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updateDelayHist" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    update RB_DELAY_TRAN_HIST
    <set>
      ARRIVAL_STATUS = 'C',
      <if test="cancelReason != null">
        CANCEL_REASON = #{cancelReason}
      </if>
    </set>
    where 1=1
    <if test="clientNo != null">
        and CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resSeqNo != null">
      and RES_SEQ_NO = #{resSeqNo, jdbcType=VARCHAR}
    </if>
    <if test="seqNo != null">
      and SEQ_NO = #{seqNo, jdbcType=VARCHAR}
    </if>
    <if test="internalKey != null">
      and INTERNAL_KEY = #{internalKey, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    insert into RB_DELAY_TRAN_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="signTimestamp != null">
        SIGN_TIMESTAMP,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="terminalId != null">
        TERMINAL_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="narrative != null">
        NARRATIVE,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="tranDesc != null">
        TRAN_DESC,
      </if>
      <if test="cashItem != null">
        CASH_ITEM,
      </if>
      <if test="tranNote != null">
        TRAN_NOTE,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="othInternalKey != null">
        OTH_INTERNAL_KEY,
      </if>
      <if test="othBaseAcctNo != null">
        OTH_BASE_ACCT_NO,
      </if>
      <if test="othAcctSeqNo != null">
        OTH_ACCT_SEQ_NO,
      </if>
      <if test="othProdType != null">
        OTH_PROD_TYPE,
      </if>
      <if test="othAcctCcy != null">
        OTH_ACCT_CCY,
      </if>
      <if test="othAcctDesc != null">
        OTH_ACCT_DESC,
      </if>
      <if test="payUnit != null">
        PAY_UNIT,
      </if>
      <if test="commissionClientName != null">
        COMMISSION_CLIENT_NAME,
      </if>
      <if test="commissionClientTel != null">
        COMMISSION_CLIENT_TEL,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="track2 != null">
        TRACK2,
      </if>
      <if test="track3 != null">
        TRACK3,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="prefix != null">
        PREFIX,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="exchangeTranCode != null">
        EXCHANGE_TRAN_CODE,
      </if>
      <if test="exchangeTranCodet != null">
        EXCHANGE_TRAN_CODET,
      </if>
      <if test="othAcctName != null">
        OTH_ACCT_NAME,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="revokeReason != null">
        CANCEL_REASON,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="tranMethod != null">
          TRAN_METHOD,
      </if>
      <if test="cardNo != null">
          CARD_NO,
      </if>
      <if test="othCardNo != null">
          OTH_CARD_NO,
      </if>
      <if test="settleCardFlag != null">
          SETTLE_CARD_FLAG,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="signTimestamp != null">
        #{signTimestamp},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="terminalId != null">
        #{terminalId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="narrative != null">
        #{narrative},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="tranDesc != null">
        #{tranDesc},
      </if>
      <if test="cashItem != null">
        #{cashItem},
      </if>
      <if test="tranNote != null">
        #{tranNote},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="othInternalKey != null">
        #{othInternalKey},
      </if>
      <if test="othBaseAcctNo != null">
        #{othBaseAcctNo},
      </if>
      <if test="othAcctSeqNo != null">
        #{othAcctSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="othProdType != null">
        #{othProdType},
      </if>
      <if test="othAcctCcy != null">
        #{othAcctCcy},
      </if>
      <if test="othAcctDesc != null">
        #{othAcctDesc},
      </if>
      <if test="payUnit != null">
        #{payUnit},
      </if>
      <if test="commissionClientName != null">
        #{commissionClientName},
      </if>
      <if test="commissionPhone != null">
        #{commissionPhone},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="track2 != null">
        #{track2},
      </if>
      <if test="track3 != null">
        #{track3},
      </if>
      <if test="tranCcy != null">
        #{tranCcy},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
      <if test="prefix != null">
        #{prefix},
      </if>
      <if test="voucherNo != null">
        #{voucherNo},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
      <if test="exchangeTranCode != null">
        #{exchangeTranCode},
      </if>
      <if test="exchangeTranCodet != null">
        #{exchangeTranCodet},
      </if>
      <if test="othAcctName != null">
        #{othAcctName},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="revokeReason != null">
        #{revokeReason},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="tranMethod != null">
        #{tranMethod},
      </if>
      <if test="cardNo != null">
        #{cardNo},
      </if>
      <if test="othCardNo != null">
        #{othCardNo},
      </if>
      <if test="settleCardFlag != null">
        #{settleCardFlag},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
    </trim>
  </insert>

  <select id="getDelayHistByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where SIGN_TIMESTAMP <![CDATA[<=]]> #{timeStamp}
    and ARRIVAL_STATUS = 'W'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getHistByInternalKey" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbDelayTranHistExt"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select
    <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where INTERNAL_KEY = #{internalKey}
    and ARRIVAL_STATUS = 'W'
    and  TRAN_DATE between #{startDate} and #{endDate}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getDelayHistBySeqNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where ARRIVAL_STATUS = 'W'
    <if test="timeStamp != null">
      and SIGN_TIMESTAMP >= #{timeStamp}
    </if>
    <if test="reference != null">
      and REFERENCE = #{reference}
    </if>
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="seqNo != null">
      and SEQ_NO = #{seqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDelayHistByInternalKeyStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where BASE_ACCT_NO = #{baseAcctNo}
    and ARRIVAL_STATUS = 'W'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDelayHistUc" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where CARD_NO = #{cardNo}
    and ARRIVAL_STATUS = 'W'
    and SOURCE_TYPE ='UC'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDelayHistByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    <where>
      <if test="internalKey != null">
        AND INTERNAL_KEY = #{internalKey}
      </if>
      <if test="reference != null and reference != '' ">
        AND REFERENCE = #{reference}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

    <update id="updateDelayHistOne" parameterType="java.util.Map">
        update RB_DELAY_TRAN_HIST
        <set>
          <if test="cancelReason != null and  cancelReason != '' ">
            CANCEL_REASON = #{cancelReason},
          </if>
            TRAN_TIMESTAMP = #{timestamp},
             ARRIVAL_STATUS = #{arrivalStatus}
        </set>
        where
          CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
          and SEQ_NO = #{seqNo, jdbcType=VARCHAR}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </update>

  <select id="getDelayHistByStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where INTERNAL_KEY = #{internalKey}
    and ARRIVAL_STATUS = 'W'
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDelayHistByOthBaseAcctNO" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayTranHist">
    select <include refid="Base_Column"/>
    from RB_DELAY_TRAN_HIST
    where (OTH_BASE_ACCT_NO = #{othBaseAcctNo} or OTH_CARD_NO = #{othBaseAcctNo})
    and ARRIVAL_STATUS = 'W'
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
