<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabRegister">

	<select id="selectByAcceptContractNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabRegister" parameterType="java.util.Map" >

		select <include refid="Base_Column"/>
		from RB_BAB_REGISTER
		where ACCEPT_CONTRACT_NO = #{acceptContractNo,jdbcType=DECIMAL}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<update id="updatePaymentFlag"  >
		UPDATE RB_BAB_REGISTER
		<set>
			<if test="paymentFlag != null and  paymentFlag != '' ">
				PAYMENT_FLAG = #{paymentFlag},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="acceptContractNo != null and  acceptContractNo != '' ">
					ACCEPT_CONTRACT_NO = #{acceptContractNo}  AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>
</mapper>
