<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardBookContent">

  <select id="selectByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardBookContent" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_CARD_BOOK_CONTENT
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo !='' " >
        AND CLIENT_NO = #{clientNo}
    </if>
  ORDER BY SEQ_NO DESC
  </select>

  <select id="selectPageNoLineNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardBookContent" parameterType="java.util.Map" >
      SELECT <include refid="Base_Column"/>
      FROM RB_CARD_BOOK_CONTENT
      WHERE internal_key = #{internalKey}
      AND tran_date IS NULL
      AND PRINT_PAGE_NO IS NOT NULL
      AND PRINT_LINE_NO IS NOT NULL
      <if test="clientNo != null and clientNo !='' " >
          AND CLIENT_NO = #{clientNo}
      </if>
      <if test="regType != null and regType !=''">
          AND REG_TYPE = #{regType}
      </if>
      ORDER BY PRINT_PAGE_NO DESC, PRINT_LINE_NO DESC
  </select>

    <update id="updateNullLine" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardBookContent" >
        update RB_CARD_BOOK_CONTENT
        <set >
            <if test="printPageNo != null" >
                PRINT_PAGE_NO = #{printPageNo},
            </if>
            <if test="printLineNo != null" >
                PRINT_LINE_NO = #{printLineNo},
            </if>
        </set>
        WHERE internal_key = #{internalKey}
        AND tran_date IS NULL
        <if test="clientNo != null and clientNo !=''" >
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="regType != null and regType!=''">
            AND REG_TYPE = #{regType}
        </if>
    </update>


    <select id="selectByBookNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardBookContent" parameterType="java.util.Map" >
        select <include refid="Base_Column"/>
        from RB_CARD_BOOK_CONTENT
        where REG_TYPE = 'T'
        <if test="bookNo != null and bookNo !='' " >
            AND BOOK_NO = #{bookNo}
        </if>
        ORDER BY SEQ_NO DESC
    </select>
</mapper>
