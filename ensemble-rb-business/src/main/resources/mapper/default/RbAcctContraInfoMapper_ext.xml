<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContraInfo">




    <!-- 根据他行账号进行查询-->
    <select id="queryByContraBaseAcctNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContraInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_CONTRA_INFO
        where  CONTRA_BASE_ACCT_NO =#{contraBaseAcctNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateClientNo" parameterType="java.util.Map">
        update RB_ACCT_CONTRA_INFO set CLIENT_NO = #{clientNo},TRAN_TIMESTAMP = #{tranTimestamp}
        where internal_key = #{internalKey} and CLIENT_NO = #{oldClientNo}
    </update>

    <select id="queryListByInternalKey" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContraInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_CONTRA_INFO
        where  INTERNAL_KEY in
        <foreach collection="internalKeys" item="internalKey" index="index" open="(" separator="," close=")">
            #{internalKey}
        </foreach>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
