<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementNorthbound">

    <select id="getIsNorthboundSign" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementNorthbound">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_NORTHBOUND
        where
        BASE_ACCT_NO = #{baseAcctNo}
        and NORTHBOUND_STATUS != '0'
    </select>

    <select id="getIsNorthboundSignS" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementNorthbound">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_NORTHBOUND
        where NORTHBOUND_STATUS != '0'
        <if test="baseAcctNo != null and baseAcctNo != '' ">
            and (BASE_ACCT_NO = #{baseAcctNo} or BASE_ACCT_NO=#{cardNo})
        </if>
        <if test="seqNo != null and seqNo != '' ">
            and acct_Seq_No = #{seqNo}
        </if>

    </select>
</mapper>