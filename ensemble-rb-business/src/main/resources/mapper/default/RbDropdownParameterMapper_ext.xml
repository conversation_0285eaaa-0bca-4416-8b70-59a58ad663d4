<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
                "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDropdownParameter">

        <select id="getTableName" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDropdownParameter">
                SELECT <include refid="Base_Column"/>
                FROM RB_DROPDOWN_PARAMETER
                WHERE SQL_ID = #{sqlId,jdbcType=VARCHAR}
        </select>

</mapper>