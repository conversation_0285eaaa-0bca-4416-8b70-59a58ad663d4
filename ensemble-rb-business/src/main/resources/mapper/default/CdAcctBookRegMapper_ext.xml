<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdAcctBookReg">
    <select id="getCdAcctBookRegByCardNos" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdAcctBookReg">
        select
        <include refid="Base_Column"/>
        from CD_ACCT_BOOK_REG
        <where>
            <include refid="Base_Where_List" />
        </where>
        order by OPEN_DATE desc
    </select>

    <sql id="Base_Where_List">
        <where>
            <trim suffixOverrides="AND">
                <if test="baseAcctNos != null">
                    BASE_ACCT_NO IN
                    <foreach item="item" index="index" collection="cardNos" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND
                </if>
                <if test="bookStatus != null and bookStatus != ''">
                    BOOK_STATUS = #{bookStatus} AND
                </if>
            </trim>
        </where>
    </sql>

    <select id="getCdAcctBookRegByCardNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdAcctBookReg">
        select
        <include refid="Base_Column"/>
        from CD_ACCT_BOOK_REG
        <where>
            <trim suffixOverrides="AND">
                <if test="baseAcctNo != null and baseAcctNo != ''">
                    BASE_ACCT_NO = #{baseAcctNo} AND
                </if>
                <if test="bookNo != null and bookNo != ''">
                    BOOK_NO = #{bookNo} AND
                </if>
                <if test="bookStatus != null and bookStatus != ''">
                    BOOK_STATUS = #{bookStatus} AND
                </if>
                <if test="clientNo != null and clientNo != ''">
                    CLIENT_NO = #{clientNo, jdbcType=VARCHAR} AND
                </if>
            </trim>
        </where>
        order by BOOK_NO
    </select>

    <select id="getCdAcctBookRegByCardNo1" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdAcctBookReg">
        select
        <include refid="Base_Column"/>
        from CD_ACCT_BOOK_REG
        <where>
            <trim suffixOverrides="AND">
                <if test="baseAcctNo != null and baseAcctNo != ''">
                    CARD_NO = #{baseAcctNo} AND
                </if>
                <if test="bookNo != null and bookNo != ''">
                    BOOK_NO = #{bookNo} AND
                </if>
                <if test="bookStatus != null and bookStatus != ''">
                    BOOK_STATUS = #{bookStatus} AND
                </if>
                <if test="clientNo != null and clientNo != ''">
                     CLIENT_NO = #{clientNo, jdbcType=VARCHAR} AND
                </if>
            </trim>
        </where>
        order by BOOK_NO
    </select>

    <update id="updateCdAcctBookReg" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdAcctBookReg">
        UPDATE CD_ACCT_BOOK_REG
        SET BOOK_STATUS = #{bookStatus,jdbcType=VARCHAR},
        LAST_CHANGE_USER_ID = #{lastChangeUserId,jdbcType=VARCHAR},
        LAST_CHANGE_BRANCH = #{lastChangeBranch,jdbcType=VARCHAR},
        LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE},
        OTH_REFERENCE = #{othReference,jdbcType=VARCHAR},
        CHANGE_REASON = #{changeReason,jdbcType=VARCHAR},
        NEXT_BOOK_NO = #{nextBookNo,jdbcType=VARCHAR}
        WHERE BOOK_STATUS = 'A'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo, jdbcType=VARCHAR}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <if test="bookNo != null and bookNo != ''">
            AND BOOK_NO = #{bookNo, jdbcType=VARCHAR}
        </if>
    </update>
</mapper>