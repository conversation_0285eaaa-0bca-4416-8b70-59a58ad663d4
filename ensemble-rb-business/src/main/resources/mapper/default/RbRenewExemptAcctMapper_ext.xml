<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRenewExemptAcct">

	<select id="selectByAcct"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRenewExemptAcct" parameterType="java.util.Map">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_RENEW_EXEMPT_ACCT
		<where>
			<trim suffixOverrides="AND">
				<if test="acctCcy != null and  acctCcy != '' ">
					ACCT_CCY = #{acctCcy}  AND
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo}  AND
				</if>
				<if test="prodType != null and  prodType != '' ">
					PROD_TYPE = #{prodType}  AND
				</if>
				<if test="acctSeqNo != null and  acctSeqNo != '' ">
					ACCT_SEQ_NO = #{acctSeqNo}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>


	<delete id="deleteByAcct"  parameterType="java.util.Map">
		DELETE FROM RB_RENEW_EXEMPT_ACCT
		<where>
			<trim suffixOverrides="AND">
				<if test="acctCcy != null and  acctCcy != '' ">
					ACCT_CCY = #{acctCcy}  AND
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo}  AND
				</if>
				<if test="prodType != null and  prodType != '' ">
					PROD_TYPE = #{prodType}  AND
				</if>
				<if test="acctSeqNo != null and  acctSeqNo != '' ">
					ACCT_SEQ_NO = #{acctSeqNo}  AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</delete>
</mapper>
