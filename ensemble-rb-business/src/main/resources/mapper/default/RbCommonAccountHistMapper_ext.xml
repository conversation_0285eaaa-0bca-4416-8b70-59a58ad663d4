<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommonAccountHist">
	<select id="getTranHistByRefAndClientNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommonAccountHist">
		select
		<include refid="Base_Column" />
		from RB_COMMON_ACCOUNT_HIST
		where REFERENCE = #{reference}
		AND CLIENT_NO = #{clientNo}
		AND TRAN_STATUS != 'R'
		AND TRAN_STATUS != 'X'
		AND TRAN_STATUS != 'W'
		<if test="taeSubSeqNo != null and  taeSubSeqNo != '' ">
			AND TAE_SUB_SEQ_NO = #{taeSubSeqNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		order by TRAN_DATE desc,SEQ_NO+0 desc
	</select>

	<select id="getRbCommonAccountHistByPage" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommonAccountHist">
		select
		<include refid="Base_Column" />
		from RB_COMMON_ACCOUNT_HIST
		where TRAN_STATUS != 'R'
		AND TRAN_STATUS != 'X'
		AND TRAN_STATUS != 'W'
		<if test="clientNo != null and clientNo != '' ">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="reference != null and reference.length() > 0">
			AND REFERENCE = #{reference}
		</if>
		<if test="channelSeqNo != null and channelSeqNo != '' ">
			AND CHANNEL_SEQ_NO = #{channelSeqNo}
		</if>
		<if test="startDate != null ">
			AND <![CDATA[ TRAN_DATE >= #{startDate}]]>
		</if>
		<if test="endDate != null ">
			AND <![CDATA[ TRAN_DATE <= #{endDate}]]>
		</if>
		<if test="sourceModule != null and sourceModule != ''">
			AND SOURCE_MODULE = #{sourceModule}
		</if>
		<if test="userId != null and userId != ''">
			AND USER_ID = #{userId}
		</if>
		<if test="tranBranch != null and tranBranch != ''">
			AND TRAN_BRANCH = #{tranBranch}
		</if>
		<if test="othRealBaseAcctNo != null and othRealBaseAcctNo != ''">
			AND OTH_REAL_BASE_ACCT_NO = #{othRealBaseAcctNo}
		</if>
		<if test="othBaseAcctNo != null and othBaseAcctNo != ''">
			AND OTH_BASE_ACCT_NO = #{othBaseAcctNo}
		</if>
		<if test="acctSeqNo != null and acctSeqNo != ''">
			AND ACCT_SEQ_NO = #{acctSeqNo}
		</if>
		<if test="branchList != null and branchList != ''">BRANCH IN
			<foreach collection="branchList" item="branchList" index="index" open="(" separator="," close=")">
				#{branchList}
			</foreach>
		</if>
		<if test="'1'.toString() == sortFlag">
			order by TRAN_TIMESTAMP asc,t.SEQ_NO+0 desc
		</if>
		<if test="'1'.toString() != sortFlag">
			order by TRAN_TIMESTAMP desc,SEQ_NO+0 desc
		</if>
	</select>
	<!--查询正向流水信息-->
	<select id="getTranHistByRefNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommonAccountHist">
		select
		<include refid="Base_Column"/>
		from RB_COMMON_ACCOUNT_HIST
		where REFERENCE = #{reference}
		AND REVERSAL_FLAG != 'Y'
		AND (TRAN_STATUS != 'X' OR TRAN_STATUS is null)
		order by TRAN_DATE desc,SEQ_NO+0 desc
	</select>
</mapper>
