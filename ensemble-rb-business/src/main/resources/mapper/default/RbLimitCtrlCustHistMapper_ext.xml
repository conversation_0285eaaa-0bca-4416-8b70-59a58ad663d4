<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitCtrlCustHist">
    <select id="selectCtrlCustHist" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitCtrlCustHist">
        SELECT <include refid="Base_Column" />
        FROM <include refid="Table_Name" />
        <where>
            <if test="seqNo != null and seqNo != ''">
                SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
            <if test="limitSceneNo != null and limitSceneNo != ''">
                AND LIMIT_SCENE_NO = #{limitSceneNo,jdbcType=VARCHAR}
            </if>
            <if test="limitMainType != null and limitMainType != ''">
                AND LIMIT_MAIN_TYPE = #{limitMainType,jdbcType=VARCHAR}
            </if>
            <if test="checkObjType != null and checkObjType != ''">
                AND CHECK_OBJ_TYPE = #{checkObjType,jdbcType=VARCHAR}
            </if>
            <if test="checkObjVal != null and checkObjVal != ''">
                AND CHECK_OBJ_VAL = #{checkObjVal,jdbcType=VARCHAR}
            </if>
            <if test="ctrlItemType != null and ctrlItemType != ''">
                AND CTRL_ITEM_TYPE = #{ctrlItemType,jdbcType=VARCHAR}
            </if>
            <if test="limitCtrlAmt != null and limitCtrlAmt != ''">
                AND LIMIT_CTRL_AMT = #{limitCtrlAmt}
            </if>
            <if test="limitCtrlNum != null and limitCtrlNum != ''">
                AND LIMIT_CTRL_NUM = #{limitCtrlNum}
            </if>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="createDate != null and createDate != ''">
                AND CREATE_DATE = #{createDate,jdbcType=DATE}
            </if>
            <if test="createTimestamp != null and createTimestamp != ''">
                AND CREATE_TIMESTAMP = #{createTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="tranTimestamp != null and tranTimestamp != ''">
                AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="tranBranch != null and tranBranch != ''">
                AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="company != null and company != ''">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId != ''">
                AND USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="effectDate != null and effectDate != ''">
                AND EFFECT_DATE = #{effectDate,jdbcType=DATE}
            </if>
            <if test="updateDate != null and updateDate != ''">
                AND UPDATE_DATE = #{updateDate,jdbcType=DATE}
            </if>
            <if test="lastChangeDate != null and lastChangeDate != ''">
                AND LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE}
            </if>
            <if test="expireDate != null and expireDate != ''">
                AND #{expireDate,jdbcType=DATE} BETWEEN EFFECT_DATE AND EXPIRE_DATE
            </if>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND TRAN_DATE BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
        ORDER BY TRAN_DATE DESC, CLIENT_NO ASC
    </select>

</mapper>