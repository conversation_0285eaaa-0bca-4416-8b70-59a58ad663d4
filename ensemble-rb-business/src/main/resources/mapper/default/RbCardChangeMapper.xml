<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.cd.CardChangeTable">

    <update id="updateCombo" parameterType="java.util.Map">
        UPDATE ${tableName}
        <set>
            ${set}
        </set>
        <where>
            <trim prefix="(" suffix=")" suffixOverrides="AND">
                <if test="clientNo != null" >
                    CLIENT_NO = #{clientNo} AND
                </if>
                ${where}
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    AND COMPANY = #{company}
                </if>
            </trim>
        </where>
    </update>

</mapper>
