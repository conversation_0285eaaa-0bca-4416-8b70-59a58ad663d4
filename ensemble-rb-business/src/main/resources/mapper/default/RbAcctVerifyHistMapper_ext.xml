<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctVerifyHist">
    <update id="updateByCondition" parameterType="java.util.Map">
        update RB_ACCT_VERIFY_HIST
        <set>
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="acctSeqNo != null and acctSeqNo != '' ">
                ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="acctVerifyStatus != null and acctVerifyStatus != '' ">
                ACCT_VERIFY_STATUS = #{acctVerifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="verificationDate != null">
                VERIFICATION_DATE = #{verificationDate},
            </if>
            <if test="operUserId != null and operUserId != '' ">
                OPER_USER_ID = #{operUserId,jdbcType=VARCHAR},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            CLIENT_NO = #{clientNo}
            AND BASE_ACCT_NO = #{baseAcctNoOld}
            AND ACCT_VERIFY_STATUS = #{verifyStatusOld}
            AND ACCT_SEQ_NO = #{acctSeqNoOld}
        </where>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>


    <update id="updateBaseAcctByOldBase" parameterType="java.util.Map">
        update RB_ACCT_VERIFY_HIST set
        BASE_ACCT_NO = #{newBaseAcctNo},
        ACCT_SEQ_NO = #{newAcctSeqNo},
        TRAN_TIMESTAMP = #{tranTimestamp},
        LAST_CHANGE_DATE = #{lastChangeDate}
        where
        BASE_ACCT_NO = #{oldBaseAcctNo} and ACCT_SEQ_NO = #{oldAcctSeqNo}
        and CLIENT_NO = #{clientNo}
    </update>
</mapper>