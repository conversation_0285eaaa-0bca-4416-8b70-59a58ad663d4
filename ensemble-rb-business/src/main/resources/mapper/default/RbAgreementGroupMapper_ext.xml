<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">

  <select id="selectByInternalKey" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_GROUP
    where INTERNAL_KEY = #{internalKey}
    and client_no = #{clientNo}
    AND AGREEMENT_STATUS = 'A'
  </select>

  <select id="selectStatusAOrF" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_GROUP
    where INTERNAL_KEY = #{internalKey}
    and client_no = #{clientNo}
    AND (
    AGREEMENT_STATUS = 'F'
    <if test="effectiveStatus != null and effectiveStatus !=''">
      or AGREEMENT_STATUS = #{effectiveStatus}
    </if>
    )
  </select>

  <select id="selectByAgreementId" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_GROUP
    where AGREEMENT_ID = #{agreementId}
    <if test="clientNo != null and clientNo !=''">
      AND CLIENT_NO = #{clientNo}
    </if>
    AND AGREEMENT_STATUS in ('A','F')
  </select>
  <select id="selectByReference" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_GROUP
    where REFERENCE = #{reference}
    AND AGREEMENT_STATUS in ('A','F')
  </select>
  <select id="selectByAgreementDate" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_GROUP
    where #{runDate} between AGREEMENT_START_DATE AND AGREEMENT_END_DATE
    AND AGREEMENT_STATUS = 'A'
  </select>
  <select id="selectByAgreementRunDate" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_GROUP
    where #{runDate} <![CDATA[ >= ]]> AGREEMENT_END_DATE
    AND AGREEMENT_STATUS = 'A'
  </select>
  <!--存款组协议解除限制-->
  <select id="getAllAgreementGroup" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup">
    SELECT <include refid="Base_Column"/>
    FROM rb_agreement_group
    WHERE  AGREEMENT_START_DATE IS NOT NULL
    AND AGREEMENT_START_DATE <![CDATA[ <= ]]> #{runDate,jdbcType=DATE}
    AND AGREEMENT_START_DATE <![CDATA[ > ]]> #{lastRunDate,jdbcType=DATE}
    AND AGREEMENT_STATUS ='F'
  </select>
</mapper>
