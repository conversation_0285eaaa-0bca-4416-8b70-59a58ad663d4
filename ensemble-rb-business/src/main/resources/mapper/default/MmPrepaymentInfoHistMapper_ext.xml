<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist">

<sql id="Base_Column_List">
    PREPAYMENT_NO,
    INTERNAL_KEY,
    DEAL_NO,
    CLIENT_NO,
    TAKE_ACCT_NO,
    CCY,
    PRINCIPAL_AMT,
    LINK_DEAL_INTERNAL_KEY,
    OTH_DEAL_NO,
    COUNTERPARTY,
    PLACE_ACCT_NO,
    EFFECT_DATE,
    MATURITY_DATE,
    PREPAYMENT_STATUS,
    APPR_USER_ID,
    APPROVAL_DATE,
    TRAN_DATE,
    USER_ID,
    LAST_CHANGE_DATE,
    RENEW_DATE,
    DEAL_REASON,
    TRAN_TIMESTAMP,
    TERM,
    TERM_TYPE
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist">
    select <include refid="Base_Column_List"/>
    from MM_PREPAYMENT_INFO_HIST
    where PREPAYMENT_NO = #{prepaymentNo}
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist">
    delete from MM_PREPAYMENT_INFO_HIST
    where PREPAYMENT_NO = #{prepaymentNo}
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist">
    update MM_PREPAYMENT_INFO_HIST
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="dealNo != null">
        DEAL_NO = #{dealNo},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="othDealNo != null">
        OTH_DEAL_NO = #{othDealNo},
      </if>
      <if test="counterparty != null">
        COUNTERPARTY = #{counterparty},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO = #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO = #{placeAcctNo},
      </if>
      <if test="prepaymentStatus != null">
        PREPAYMENT_STATUS = #{prepaymentStatus},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT = #{principalAmt},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID = #{apprUserId},
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE = #{approvalDate},
      </if>
      <if test="dealReason != null">
        DEAL_REASON = #{dealReason},
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY = #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        RENEW_DATE = #{renewDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP= #{tranTimestamp},
      </if>
      <if test="term != null">
        TERM= #{term},
      </if>
      <if test="termType != null">
        TERM_TYPE= #{termType}
      </if>
    </set>
    where PREPAYMENT_NO = #{prepaymentNo}
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist">
    insert into MM_PREPAYMENT_INFO_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="prepaymentNo != null">
        PREPAYMENT_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="dealNo != null">
        DEAL_NO,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="othDealNo != null">
        OTH_DEAL_NO,
      </if>
      <if test="counterparty != null">
        COUNTERPARTY,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO,
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO,
      </if>
      <if test="prepaymentStatus != null">
        PREPAYMENT_STATUS,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID,
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE,
      </if>
      <if test="dealReason != null">
        DEAL_REASON,
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY,
      </if>
      <if test="renewDate != null">
        RENEW_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="term != null">
        TERM,
      </if>
      <if test="termType != null">
        TERM_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="prepaymentNo != null">
        #{prepaymentNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="dealNo != null">
        #{dealNo},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="othDealNo != null">
        #{othDealNo},
      </if>
      <if test="counterparty != null">
        #{counterparty},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="takeAcctNo != null">
        #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        #{placeAcctNo},
      </if>
      <if test="prepaymentStatus != null">
        #{prepaymentStatus},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="principalAmt != null">
        #{principalAmt},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        #{apprUserId},
      </if>
      <if test="approvalDate != null">
        #{approvalDate},
      </if>
      <if test="dealReason != null">
        #{dealReason},
      </if>
      <if test="linkDealInternalKey != null">
        #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        #{renewDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="term != null">
        #{term},
      </if>
      <if test="termType != null">
        #{termType},
      </if>
    </trim>
  </insert>
  <select id="getMmPrepaymentByDealNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_PREPAYMENT_INFO_HIST
    WHERE DEAL_NO = #{dealNo}
    AND USER_ID NOT IN (#{userId})
    AND PREPAYMENT_STATUS!='V'
  </select>
  <select id="getMmPrepaymentByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmPrepaymentInfoHist">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_PREPAYMENT_INFO_HIST
    WHERE COUNTERPARTY = #{counterparty}
    AND USER_ID NOT IN (#{userId})
    AND PREPAYMENT_STATUS!='V'
  </select>
</mapper>
