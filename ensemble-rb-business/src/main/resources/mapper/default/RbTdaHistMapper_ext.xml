<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    select <include refid="Base_Column"/>
    from RB_TDA_HIST
    where INTERNAL_KEY = #{internalKey}
      <if test="seqNo != null">
        AND SEQ_NO = #{seqNo}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbTdaHistByInternalKeyOrderBy" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    <!-- This sorting algorithm is used for exclusive transactions. Please do not modify it. -->
    SELECT
    A.*
    FROM
    RB_TDA_HIST A,
    RB_TRAN_HIST B
    WHERE
    A.CLIENT_NO = B.CLIENT_NO
    AND A.CLIENT_NO = #{ clientNo }
    AND A.REFERENCE = B.REFERENCE
    AND A.INTERNAL_KEY = B.INTERNAL_KEY
    AND B.REVERSAL_FLAG != 'Y'
    AND B.TRAN_STATUS = 'N'
    AND A.TDA_STATUS = 'A'
    AND A.INTERNAL_KEY = #{ internalKey }
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND A.COMPANY = #{company}
    </if>
    ORDER BY
    A.TRAN_TIMESTAMP ASC
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    delete from RB_TDA_HIST
    where INTERNAL_KEY = #{internalKey}
      <if test="seqNo != null">
        AND SEQ_NO = #{seqNo}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
 
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    update RB_TDA_HIST
    <set>
      <if test="acctOpenDate != null">
        ACCT_OPEN_DATE = #{acctOpenDate},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="acctMovtDate != null">
        ACCT_MOVT_DATE = #{acctMovtDate},
      </if>
      <if test="movtStatus != null">
        MOVT_STATUS = #{movtStatus},
      </if>
      <if test="depTermPeriod != null">
        DEP_TERM_PERIOD = #{depTermPeriod},
      </if>
      <if test="depTermType != null">
        DEP_TERM_TYPE = #{depTermType},
      </if>
      <if test="acctLevelIntRate != null">
        ACCT_LEVEL_INT_RATE = #{acctLevelIntRate},
      </if>
      <if test="grossInterestAmt != null">
        GROSS_INTEREST_AMT = #{grossInterestAmt},
      </if>
      <if test="taxAmt != null">
        TAX_AMT = #{taxAmt},
      </if>
      <if test="intAdj != null">
        INT_ADJ = #{intAdj},
      </if>
      <if test="netInterestAmt != null">
        NET_INTEREST_AMT = #{netInterestAmt},
      </if>
      <if test="matNoticeFlag != null">
        MAT_NOTICE_FLAG = #{matNoticeFlag},
      </if>
      <if test="renewNo != null">
        RENEW_NO = #{renewNo},
      </if>
      <if test="rolloverNo != null">
        ROLLOVER_NO = #{rolloverNo},
      </if>
      <if test="partialRenewRoll != null">
        PARTIAL_RENEW_ROLL = #{partialRenewRoll},
      </if>
      <if test="debtAmt != null">
        DEBT_AMT = #{debtAmt},
      </if>
      <if test="principalAmtActual != null">
        PRINCIPAL_AMT_ACTUAL = #{principalAmtActual},
      </if>
      <if test="autoRenewRollover != null">
        AUTO_RENEW_ROLLOVER = #{autoRenewRollover},
      </if>
      <if test="addtlPrincipal != null">
        ADDTL_PRINCIPAL = #{addtlPrincipal},
      </if>
      <if test="noticePeriod != null">
        NOTICE_PERIOD = #{noticePeriod},
      </if>
      <if test="seqRenewRolloverNo != null">
        SEQ_RENEW_ROLLOVER_NO = #{seqRenewRolloverNo},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="intAdjCtd != null">
        INT_ADJ_CTD = #{intAdjCtd},
      </if>
      <if test="debtIntRate != null">
        DEBT_INT_RATE = #{debtIntRate},
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO = #{tranSeqNo},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="revSeqNo != null">
        REV_SEQ_NO = #{revSeqNo},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType},
      </if>
      <if test="prefix != null">
        PREFIX = #{prefix},
      </if>
      <if test="addTerm != null">
        ADD_TERM = #{addTerm},
      </if>
		<if test="tranTimestamp != null">
			TRAN_TIMESTAMP = #{tranTimestamp}
		</if>
      <if test="lostNo != null">
        LOST_NO = #{lostNo}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
        AND SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    insert into RB_TDA_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="acctOpenDate != null">
        ACCT_OPEN_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="acctMovtDate != null">
        ACCT_MOVT_DATE,
      </if>
      <if test="movtStatus != null">
        MOVT_STATUS,
      </if>
      <if test="depTermPeriod != null">
        DEP_TERM_PERIOD,
      </if>
      <if test="depTermType != null">
        DEP_TERM_TYPE,
      </if>
      <if test="acctLevelIntRate != null">
        ACCT_LEVEL_INT_RATE,
      </if>
      <if test="grossInterestAmt != null">
        GROSS_INTEREST_AMT,
      </if>
      <if test="taxAmt != null">
        TAX_AMT,
      </if>
      <if test="intAdj != null">
        INT_ADJ,
      </if>
      <if test="netInterestAmt != null">
        NET_INTEREST_AMT,
      </if>
      <if test="matNoticeFlag != null">
        MAT_NOTICE_FLAG,
      </if>
      <if test="renewNo != null">
        RENEW_NO,
      </if>
      <if test="rolloverNo != null">
        ROLLOVER_NO,
      </if>
      <if test="partialRenewRoll != null">
        PARTIAL_RENEW_ROLL,
      </if>
      <if test="debtAmt != null">
        DEBT_AMT,
      </if>
      <if test="principalAmtActual != null">
        PRINCIPAL_AMT_ACTUAL,
      </if>
      <if test="autoRenewRollover != null">
        AUTO_RENEW_ROLLOVER,
      </if>
      <if test="addtlPrincipal != null">
        ADDTL_PRINCIPAL,
      </if>
      <if test="noticePeriod != null">
        NOTICE_PERIOD,
      </if>
      <if test="seqRenewRolloverNo != null">
        SEQ_RENEW_ROLLOVER_NO,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="intAdjCtd != null">
        INT_ADJ_CTD,
      </if>
      <if test="debtIntRate != null">
        DEBT_INT_RATE,
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="revSeqNo != null">
        REV_SEQ_NO,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="prefix != null">
        PREFIX,
      </if>
      <if test="addTerm != null">
        ADD_TERM,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="lostNo != null">
        LOST_NO,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="acctOpenDate != null">
        #{acctOpenDate},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="acctMovtDate != null">
        #{acctMovtDate},
      </if>
      <if test="movtStatus != null">
        #{movtStatus},
      </if>
      <if test="principalAmt != null">
        #{principalAmt},
      </if>
      <if test="depTermPeriod != null">
        #{depTermPeriod},
      </if>
      <if test="depTermType != null">
        #{depTermType},
      </if>
      <if test="acctLevelIntRate != null">
        #{acctLevelIntRate},
      </if>
      <if test="grossInterestAmt != null">
        #{grossInterestAmt},
      </if>
      <if test="taxAmt != null">
        #{taxAmt},
      </if>
      <if test="intAdj != null">
        #{intAdj},
      </if>
      <if test="netInterestAmt != null">
        #{netInterestAmt},
      </if>
      <if test="matNoticeFlag != null">
        #{matNoticeFlag},
      </if>
      <if test="renewNo != null">
        #{renewNo},
      </if>
      <if test="rolloverNo != null">
        #{rolloverNo},
      </if>
      <if test="partialRenewRoll != null">
        #{partialRenewRoll},
      </if>
      <if test="debtAmt != null">
        #{debtAmt},
      </if>
      <if test="principalAmtActual != null">
        #{principalAmtActual},
      </if>
      <if test="autoRenewRollover != null">
        #{autoRenewRollover},
      </if>
      <if test="addtlPrincipal != null">
        #{addtlPrincipal},
      </if>
      <if test="noticePeriod != null">
        #{noticePeriod},
      </if>
      <if test="seqRenewRolloverNo != null">
        #{seqRenewRolloverNo},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="intAdjCtd != null">
        #{intAdjCtd},
      </if>
      <if test="debtIntRate != null">
        #{debtIntRate},
      </if>
      <if test="tranSeqNo != null">
        #{tranSeqNo},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="revSeqNo != null">
        #{revSeqNo},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
      <if test="prefix != null">
        #{prefix},
      </if>
      <if test="addTerm != null">
        #{addTerm},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="lostNo != null">
        #{lostNo},
      </if>
      <if test="company != null">
        #{company}
      </if>
    </trim>
  </insert>
  <select id="getMbTdaHistByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    <!-- This sorting algorithm is used for exclusive transactions. Please do not modify it. -->
    select a.*
    from   RB_TDA_HIST a,RB_TRAN_HIST b
    where  a.REFERENCE = b.REFERENCE
      AND b.REVERSAL_FLAG != 'Y'
    AND    b.TRAN_STATUS = 'N'
    AND    a.INTERNAL_KEY = #{internalKey}
    AND    a.INTERNAL_KEY = b.INTERNAL_KEY
    AND    (a.MOVT_STATUS = 'A'or a.MOVT_STATUS = 'X')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    order by a.ADD_TERM+0 desc,a.ACCT_MOVT_DATE desc
  </select>
   <delete id="delMbTdaHistByRefNo" parameterType="java.util.Map">
    delete from RB_TDA_HIST
    where INTERNAL_KEY = #{internalKey}
      <if test="refNo != null">
        AND REFERENCE = #{refNo}
      </if>
     <!-- 多法人改造 by luocwa -->
     <if test="company != null and company != '' ">
       AND COMPANY = #{company}
     </if>
      AND CLIENT_NO = #{clientNo}
  </delete>
  <select id="getMbTdaHistByRefNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    select a.*
    from   RB_TDA_HIST a,RB_TRAN_HIST b
    where  a.CLIENT_NO = b.CLIENT_NO
    AND  a.CLIENT_NO = #{clientNo}
    AND  a.REFERENCE = b.REFERENCE
    AND  b.REVERSAL_FLAG != 'Y'
    AND  b.TRAN_STATUS = 'N'
    <if test="eventType != null">
     AND  b.EVENT_TYPE = #{eventType}
    </if>
    AND  a.TDA_STATUS = 'A'
    AND  a.INTERNAL_KEY = #{internalKey}
    AND  a.INTERNAL_KEY = b.INTERNAL_KEY
  <if test="refNo != null">
    AND    a.REFERENCE = #{refNo}
  </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
</select>
  <select id="getMbTdaHistListByRefNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    select *
    from  RB_TDA_HIST
    where REFERENCE = #{refNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbTdaHistByKeyOrderByTranDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    select <include refid="Base_Column"/>
    from RB_TDA_HIST
    where
    <trim suffixOverrides="AND">
      <if test="movtStatus != null and  movtStatus != '' ">
        MOVT_STATUS = #{movtStatus}  AND
      </if>
      <if test="depTermPeriod != null and  depTermPeriod != '' ">
        DEP_TERM_PERIOD = #{depTermPeriod}  AND
      </if>
      <if test="intAdj != null ">
        INT_ADJ = #{intAdj}  AND
      </if>
      <if test="netInterestAmt != null ">
        NET_INTEREST_AMT = #{netInterestAmt}  AND
      </if>
      <if test="rolloverNo != null and  rolloverNo != '' ">
        ROLLOVER_NO = #{rolloverNo}  AND
      </if>
      <if test="intAdjCtd != null ">
        INT_ADJ_CTD = #{intAdjCtd}  AND
      </if>
      <if test="tranSeqNo != null and  tranSeqNo != '' ">
        TRAN_SEQ_NO = #{tranSeqNo}  AND
      </if>
      <if test="taxAmt != null ">
        TAX_AMT = #{taxAmt}  AND
      </if>
      <if test="renewNo != null and  renewNo != '' ">
        RENEW_NO = #{renewNo}  AND
      </if>
      <if test="clientNo != null and  clientNo != '' ">
        CLIENT_NO = #{clientNo}  AND
      </if>
      <if test="internalKey != null ">
        INTERNAL_KEY = #{internalKey}  AND
      </if>
      <if test="acctOpenDate != null ">
        ACCT_OPEN_DATE = #{acctOpenDate}  AND
      </if>
      <if test="matNoticeFlag != null and  matNoticeFlag != '' ">
        MAT_NOTICE_FLAG = #{matNoticeFlag}  AND
      </if>
      <if test="seqRenewRolloverNo != null and  seqRenewRolloverNo != '' ">
        SEQ_RENEW_ROLLOVER_NO = #{seqRenewRolloverNo}  AND
      </if>
      <if test="docType != null and  docType != '' ">
        DOC_TYPE = #{docType}  AND
      </if>
      <if test="addTerm != null and  addTerm != '' ">
        ADD_TERM = #{addTerm}  AND
      </if>
      <if test="company != null and  company != '' ">
        COMPANY = #{company}  AND
      </if>
      <if test="maturityDate != null ">
        MATURITY_DATE = #{maturityDate}  AND
      </if>
      <if test="depTermType != null and  depTermType != '' ">
        DEP_TERM_TYPE = #{depTermType}  AND
      </if>
      <if test="acctLevelIntRate != null ">
        ACCT_LEVEL_INT_RATE = #{acctLevelIntRate}  AND
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp}  AND
      </if>
      <if test="reference != null and  reference != '' ">
        REFERENCE = #{reference}  AND
      </if>
      <if test="revSeqNo != null and  revSeqNo != '' ">
        REV_SEQ_NO = #{revSeqNo}  AND
      </if>
      <if test="partialRenewRoll != null and  partialRenewRoll != '' ">
        PARTIAL_RENEW_ROLL = #{partialRenewRoll}  AND
      </if>
      <if test="principalAmtActual != null ">
        PRINCIPAL_AMT_ACTUAL = #{principalAmtActual}  AND
      </if>
      <if test="autoRenewRollover != null and  autoRenewRollover != '' ">
        AUTO_RENEW_ROLLOVER = #{autoRenewRollover}  AND
      </if>
      <if test="addtlPrincipal != null and  addtlPrincipal != '' ">
        ADDTL_PRINCIPAL = #{addtlPrincipal}  AND
      </if>
      <if test="noticePeriod != null and  noticePeriod != '' ">
        NOTICE_PERIOD = #{noticePeriod}  AND
      </if>
      <if test="tranScene != null and  tranScene != '' ">
        TRAN_SCENE = #{tranScene}  AND
      </if>
      <if test="acctMovtDate != null ">
        ACCT_MOVT_DATE = #{acctMovtDate,jdbcType=DATE}  AND
      </if>
      <if test="lostNo != null and  lostNo != '' ">
        LOST_NO = #{lostNo}  AND
      </if>
      <if test="seqNo != null and  seqNo != '' ">
        SEQ_NO = #{seqNo}  AND
      </if>
      <if test="grossInterestAmt != null ">
        GROSS_INTEREST_AMT = #{grossInterestAmt}  AND
      </if>
      <if test="debtAmt != null ">
        DEBT_AMT = #{debtAmt}  AND
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID = #{userId}  AND
      </if>
      <if test="debtIntRate != null ">
        DEBT_INT_RATE = #{debtIntRate}  AND
      </if>
      <if test="prefix != null and  prefix != '' ">
        PREFIX = #{prefix}  AND
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        COMPANY = #{company} AND
      </if>
      <if test="startDate != null ">
        ACCT_MOVT_DATE <![CDATA[  >=  ]]> #{startDate,jdbcType=DATE}  AND
      </if>
      <if test="endDate != null ">
        ACCT_MOVT_DATE <![CDATA[ <= ]]> #{endDate,jdbcType=DATE}  AND
      </if>
    </trim>
    AND TDA_STATUS = 'A'
    ORDER  BY ACCT_MOVT_DATE DESC, TRAN_TIMESTAMP DESC
  </select>
  <select id="getMbTdaHistByInternalKeyAndClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    <!-- This sorting algorithm is used for exclusive transactions. Please do not modify it. -->
    select a.*
    from   RB_TDA_HIST a,RB_TRAN_HIST b
    where
    a.CLIENT_NO = b.CLIENT_NO
    AND a.CLIENT_NO = #{clientNo}
    AND a.REFERENCE = b.REFERENCE
    AND b.REVERSAL_FLAG != 'Y'
    AND b.TRAN_STATUS = 'N'
    AND a.TDA_STATUS = 'A'
    AND    a.INTERNAL_KEY = #{internalKey}
    AND    a.INTERNAL_KEY = b.INTERNAL_KEY
    AND    (a.MOVT_STATUS = 'A'or a.MOVT_STATUS = 'X')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    order by a.ADD_TERM+0 desc,a.ACCT_MOVT_DATE desc
  </select>
  <select id="getMbTdaHistByInternalKeyAndClientNo2" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    <!-- This sorting algorithm is used for exclusive transactions. Please do not modify it. -->
    select a.*
    from   RB_TDA_HIST a,RB_TRAN_HIST b
    where
    a.CLIENT_NO = b.CLIENT_NO
    AND a.CLIENT_NO = #{clientNo}
    AND a.REFERENCE = b.REFERENCE
    AND b.REVERSAL_FLAG != 'Y'
    AND b.TRAN_STATUS = 'N'
    AND a.TDA_STATUS = 'A'
    AND    a.INTERNAL_KEY = #{internalKey}
    AND    a.INTERNAL_KEY = b.INTERNAL_KEY
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    order by a.ADD_TERM+0 desc,a.ACCT_MOVT_DATE desc
  </select>
  <select id="getMbTdaHistBc" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    <!-- This sorting algorithm is used for exclusive transactions. Please do not modify it. -->
    select a.*
    from   RB_TDA_HIST a
    where
    a.CLIENT_NO = #{clientNo}
    AND a.TDA_STATUS = 'A'
    AND    a.INTERNAL_KEY = #{internalKey}
    AND a.reference like '%bc'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    order by a.ADD_TERM+0 desc,a.ACCT_MOVT_DATE desc
  </select>

  <select id="getMbTdaHistByInternalKeyAndClientNo1" parameterType="java.util.Map" resultType="java.lang.String">
    <!-- This sorting algorithm is used for exclusive transactions. Please do not modify it. -->
    select max(a.ACCT_MOVT_DATE) acct_movt_date
    from   RB_TDA_HIST a,RB_TRAN_HIST b
    where
    a.CLIENT_NO = b.CLIENT_NO
    AND a.CLIENT_NO = #{clientNo}
    AND a.REFERENCE = b.REFERENCE
    AND b.REVERSAL_FLAG != 'Y'
    AND b.TRAN_STATUS = 'N'
    AND a.TDA_STATUS = 'A'
    AND    a.INTERNAL_KEY = #{internalKey}
    AND    a.INTERNAL_KEY = b.INTERNAL_KEY
    AND    (a.MOVT_STATUS = 'A'or a.MOVT_STATUS = 'X')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    order by a.ADD_TERM+0 desc,a.ACCT_MOVT_DATE desc
  </select>
  <select id="getMbTdaHistByInternalKeyOrderByStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaHist">
    <!-- This sorting algorithm is used for exclusive transactions. Please do not modify it. -->
    SELECT
    A.*
    FROM
    RB_TDA_HIST A,
    RB_TRAN_HIST B
    WHERE
    A.CLIENT_NO = B.CLIENT_NO
    AND A.CLIENT_NO = #{ clientNo }
    AND A.REFERENCE = B.REFERENCE
    AND A.INTERNAL_KEY = B.INTERNAL_KEY
    AND B.REVERSAL_FLAG != 'Y'
    AND B.TRAN_STATUS = 'N'
    AND A.TDA_STATUS = 'A'
    AND A.INTERNAL_KEY = #{ internalKey }
    AND A.MOVT_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND A.COMPANY = #{company}
    </if>
    ORDER BY
    A.TRAN_TIMESTAMP ASC
  </select>
</mapper>
