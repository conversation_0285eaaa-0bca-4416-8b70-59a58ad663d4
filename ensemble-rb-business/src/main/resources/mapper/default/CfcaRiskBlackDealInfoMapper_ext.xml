<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CfcaRiskBlackDealInfo">

    <select id="selectOneCfcaRiskBlackDealInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CfcaRiskBlackDealInfo">
        SELECT <include refid="Base_Column" />
        FROM CFCA_RISK_BLACK_DEAL_INFO
        WHERE DATA_TYP = #{dataTyp} AND VCHR_NUM = #{vchrNum} AND BLKL_TYP = #{blklTyp}
    </select>

    <update id="updateCfcaRiskBlackDealInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CfcaRiskBlackDealInfo">
        UPDATE CFCA_RISK_BLACK_DEAL_INFO
        SET TRAN_TIMESTAMP = #{tranTimestamp}, TRAN_DATE = #{tranDate}
        WHERE DATA_TYP = #{dataTyp} AND VCHR_NUM = #{vchrNum} AND BLKL_TYP = #{blklTyp}
    </update>

    <delete id="dealCfcaRiskBlackDealInfoNoToday" parameterType="java.util.Map">
        DELETE FROM CFCA_RISK_BLACK_DEAL_INFO
        WHERE TRAN_DATE <![CDATA[ < ]]> #{tranDate,jdbcType=DATE}
    </delete>
</mapper>

