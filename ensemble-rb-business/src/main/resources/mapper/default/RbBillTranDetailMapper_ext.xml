<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillTranDetail">
    <sql id="Base_Column_A">
        A.CCY_SIGN,
        A.BILL_AMT,
        A.PAYEE_ACCT_NAME,
        A.APPR_USER_ID,
        A.FEE_OSD_AMT,
        A.USER_ID,
        A.TRANFER_CASH_FLAG,
        A.PRINT_TYPE,
        A.DEAL_RESULT,
        A.FEE_CHARGE_TYPE,
        A.TRAN_TYPE,
        A.DOC_TYPE,
        A.DOC_CLASS,
        A.BILL_STATUS,
        A.CENTER_OPER,
        A.FEE_REAL_AMT,
        A<PERSON>IL<PERSON>_TYPE,
        <PERSON><PERSON><PERSON>_<PERSON>,
        <PERSON><PERSON>,
        <PERSON><PERSON>,
        <PERSON><PERSON>,
        <PERSON><PERSON>,
        <PERSON><PERSON>_<PERSON>,
        <PERSON><PERSON>E_TYPE,
        A.ORIG_BILL_NO,
        A.PAYER_BASE_ACCT_NO,
        A.RETURN_BASE_ACCT_NO,
        A.TRAN_BRANCH,
        A.BILL_NO,
        A.RETURN_ACCT_NAME,
        A.AUTH_USER_ID,
        A.TRAN_DATE,
        A.SERIAL_NO,
        A.ORIG_SERIAL_NO,
        A.CHANNEL_SEQ_NO,
        A.TRAN_TIMESTAMP,
        A.SIGN_DATE,
        A.HANG_SEQ_NO,
        A.CASH_ITEM,
        A.PAYER_ACCT_NAME,
        A.CLIENT_NO,
        A.PAYEE_ACCT_NO,
        A.ISSUE_BANK_NO,
        A.SUB_SEQ_NO
    </sql>

    <select id="selectMbPnTranDetailOne" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillTranDetail">
        SELECT <include refid="Base_Column" />
        FROM RB_BILL_TRAN_DETAIL
        WHERE
        <if test="origSerialNo != null">
            ORIG_SERIAL_NO = #{origSerialNo}
        </if>
        <if test="operateType != null">
            AND OPERATE_TYPE = #{operateType}
        </if>
        <if test="clientNo != null and clientNo != '' ">
            AND CLIENT_NO = #{clientNo}
        </if>
    </select>


    <select id="selectMbPnTranDetailList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillTranDetail">
        SELECT <include refid="Base_Column" />
        FROM RB_BILL_TRAN_DETAIL
        WHERE 1=1
        <if test="operateType != null and operateType !=''">
            AND OPERATE_TYPE = #{operateType}
        </if>
        <if test="branch != null and branch !=''">
            AND TRAN_BRANCH = #{branch}
        </if>
        <if test="billType != null and billType !=''">
            AND BILL_TYPE = #{billType}
        </if>
        <if test="tranBeginDate != null ">
            <![CDATA[AND TRAN_DATE >= #{tranBeginDate,jdbcType=DATE}]]>
        </if>
        <if test="tranEndDate != null">
            <![CDATA[AND TRAN_DATE <= #{tranEndDate,jdbcType=DATE}]]>
        </if>
        <if test="billNo != null and billNo !=''">
            AND BILL_NO = #{billNo}
        </if>
        <if test="docType != null and docType !=''">
            AND DOC_TYPE = #{docType}
        </if>
        <if test="docClass != null and docClass !=''">
            AND DOC_CLASS = #{docClass}
        </if>
        ORDER BY TRAN_DATE DESC
    </select>


    <select id="selectMbPnTranDetailListBySerialNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillTranDetail">
        SELECT <include refid="Base_Column_A" />
        ,B.BILL_APPLY_NO
        FROM RB_BILL_TRAN_DETAIL A
        LEFT JOIN MB_BILL_REGISTER B ON A.ORIG_SERIAL_NO = B.SERIAL_NO
        WHERE 1=1
        <if test="serialNo != null and serialNo !=''">
            AND A.SERIAL_NO = #{serialNo}
        </if>
        <if test="operateType != null and operateType !=''">
            AND A.OPERATE_TYPE = #{operateType}
        </if>
        <if test="billStatus != null and billStatus !=''">
            AND A.BILL_STATUS = #{billStatus}
        </if>
        <if test="startDate != null and startDate !=''">
            <![CDATA[AND A.TRAN_DATE >= #{startDate,jdbcType=DATE}]]>
        </if>
        <if test="endDate != null and endDate !=''">
            <![CDATA[AND A.TRAN_DATE <= #{endDate,jdbcType=DATE}]]>
        </if>
        <if test="billNo != null and billNo !=''">
            AND A.BILL_NO = #{billNo}
        </if>
        <if test="origSerialNo != null and origSerialNo !=''">
            AND A.ORIG_SERIAL_NO = #{origSerialNo}
        </if>
        <if test="branchs != null and branchs !=''">
            AND A.TRAN_BRANCH IN
            <foreach item="item" index="index" collection="branchs" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY A.TRAN_DATE DESC, A.SERIAL_NO DESC
    </select>
</mapper>