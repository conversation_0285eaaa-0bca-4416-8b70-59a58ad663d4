<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">

  <select id="selectByContrastBatNo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
      and REVERSAL_FLAG = 'N'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getSumByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM (1)
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
      AND BATCH_STATUS = 'S'
      AND REVERSAL_FLAG = 'N'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="getSumTranAmtByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
      AND BATCH_STATUS = 'S'
      AND REVERSAL_FLAG = 'N'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumActTranAmtByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(ACT_TRAN_AMT)
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
    AND BATCH_STATUS = 'S'
    AND REVERSAL_FLAG = 'N'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumTranAmtByBatch" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumTranferRevservalDetails" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
      AND BATCH_STATUS = 'S'
      AND REVERSAL_FLAG = 'Y'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getTranferRevservalDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    select
    <include refid="Base_Column"/>
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
    AND BATCH_STATUS = 'S'
    AND REVERSAL_FLAG = 'Y'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="queryDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
    <if test="batchStatus != null and batchStatus!='' ">
      and BATCH_STATUS=#{batchStatus}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by SEQ_NO
  </select>
  <select id="getDetails" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo} and REVERSAL_FLAG = 'N'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by SEQ_NO
  </select>

  <select id="selectSumTranAmtByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{batchNo}
    AND REVERSAL_FLAG = 'N'
    <if test="batchStatus != null and batchStatus.length() > 0">
      AND BATCH_STATUS = #{batchStatus}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByContrastBatNo2" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRANSFER_DETAILS
    where BATCH_NO = #{contrastBatNo}
    and REVERSAL_FLAG = 'Y'
  </select>

  <select id="getInfoForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    SELECT <include refid="Base_Column"/>
    FROM RB_BATCH_TRANSFER_DETAILS
    WHERE BATCH_NO = #{batchNo}
    AND SEQ_NO = #{seqNo}
    AND BATCH_STATUS IN ('P','W') FOR UPDATE
  </select>
  <select id="selectRbBatchTransferDetailsForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    SELECT <include refid="Base_Column"/>
    FROM RB_BATCH_TRANSFER_DETAILS
    WHERE BATCH_NO = #{batchNo}
    AND SEQ_NO = #{seqNo}
    AND BATCH_STATUS = #{batchStatus} FOR UPDATE
  </select>

  <update id="updateStatusNoTransaction" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    update RB_BATCH_TRANSFER_DETAILS
    <set>
      BATCH_STATUS = #{batchStatus}
    </set>
    where BATCH_NO = #{batchNo, jdbcType=VARCHAR}
      AND SEQ_NO = #{seqNo, jdbcType=VARCHAR}
  </update>
  <select id="getTranferDetailsByBatchNoAndStatusPForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRANSFER_DETAILS
    where  BATCH_STATUS = 'P'
    and BATCH_NO = #{contrastBatNo}
    for update
  </select>
  <select id="getTranferDetailsByBatchNoAndSeqNoForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTransferDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRANSFER_DETAILS
    where  seq_no =#{seqNo}
    and BATCH_NO = #{contrastBatNo}
    for update
  </select>
  <update id="updateByBatchNoAndStatusP"  parameterType="java.util.Map">
    UPDATE  RB_BATCH_TRANSFER_DETAILS
    SET BATCH_STATUS = 'F',ERROR_DESC = 'SONIC后置系统级异常，请联系管理员处理！',ERROR_CODE = 'RB9839'
    WHERE BATCH_STATUS = 'P'
    AND   BATCH_NO = #{contrastBatNo}
  </update>
  <select id="getSumTranAmtByBatchTotal" parameterType="java.util.HashMap"
          resultType="java.util.HashMap">
    SELECT SUM(TRAN_AMT) total_amt,COUNT(1) total_num
    FROM RB_BATCH_TRANSFER_DETAILS
    WHERE BATCH_NO = #{contrastBatNo}
  </select>
</mapper>
