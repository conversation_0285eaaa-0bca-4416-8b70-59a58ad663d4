<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint">

    <select id="selectByResSeqNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_ACCT_RESTRAINT
        <where>
            RES_SEQ_NO = #{resSeqNo}
            and RES_STATUS = 'A'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>

    <select id="selectByResVoucherNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_ACCT_RESTRAINT
        <where>
            BILL_DOC_TYPE = #{billDocType}
            and BILL_VOUCHER_NO = #{billVoucherNo}
            and RES_STATUS = 'A'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>


    <select id="selectByPrimaryKeyAndClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_ACCT_RESTRAINT
        <where>
            RES_STATUS = 'A' and
            <trim suffixOverrides="AND">
                <if test="resSeqNo != null and  resSeqNo != '' ">
                    RES_SEQ_NO = #{resSeqNo} AND
                </if>
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="internalKey != null  ">
                    INTERNAL_KEY = # {internalKey} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = # {clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>


    <delete id="deleteByResSeqNoAndClientNo">
        UPDATE RB_BAB_ACCT_RESTRAINT SET RES_STATUS='E'
        <where>
            <trim suffixOverrides="AND">
                <if test="resSeqNo != null and  resSeqNo != '' ">
                    RES_SEQ_NO = #{resSeqNo} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </delete>

    <delete id="deleteByBaseAcctNoAndClientNo">
        UPDATE RB_BAB_ACCT_RESTRAINT SET RES_STATUS='E'
        <where>
            <trim suffixOverrides="AND">
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="baseAcctNo != null and  baseAcctNo != '' ">
                    BASE_ACCT_NO = #{baseAcctNo} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </delete>

    <delete id="deleteByVoucherBaseAcctNoAndClientNo">
        UPDATE RB_BAB_ACCT_RESTRAINT SET RES_STATUS='E'
        <where>
            <trim suffixOverrides="AND">
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="baseAcctNo != null and  baseAcctNo != '' ">
                    BASE_ACCT_NO = #{baseAcctNo} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </delete>


    <select id="selectByAcceptContractNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_ACCT_RESTRAINT
        <where>
            ACCEPT_CONTRACT_NO = #{acceptContractNo}
            and RES_STATUS = 'A'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>


    <select id="selectRestraintByAcct" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_ACCT_RESTRAINT
        <where>
            RES_STATUS = 'A' and
            <trim suffixOverrides="AND">
                <if test="baseAcctNo != null and  baseAcctNo != '' ">
                    BASE_ACCT_NO = #{baseAcctNo} AND
                </if>
                <if test="prodType != null and  prodType != '' ">
                    PROD_TYPE = #{prodType} AND
                </if>
                <if test="acctSeqNo != null and  acctSeqNo != '' ">
                    ACCT_SEQ_NO = #{acctSeqNo} AND
                </if>
                <if test="acctCcy != null and  acctCcy != '' ">
                    ACCT_CCY = #{acctCcy} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>

        </where>
    </select>
    <select id="selectByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint">
        SELECT
            accept_contract_no
        FROM
        RB_BAB_ACCT_RESTRAINT
        where
            RES_STATUS =  #{resStatus} and
            INTERNAL_KEY = #{internalKey}and
            CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        group by accept_contract_no

    </select>

</mapper>
