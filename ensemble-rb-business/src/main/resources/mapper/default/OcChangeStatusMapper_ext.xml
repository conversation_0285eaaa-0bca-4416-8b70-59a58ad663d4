<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus">

  <!--<select id="selectOcChangeStatus" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus" parameterType="java.lang.String" >-->
    <!--select <include refid="Base_Column"/>-->
    <!--from oc_change_status-->
    <!--where  CHANGE_REGION = #{changeRegion}-->
    <!--and CHANGE_BRANCH = #{changeBranch}-->
  <!--</select>-->
  <!--<insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus">-->
    <!--insert into OC_CHANGE_STATUS-->
    <!--<trim prefix="(" suffix=")" suffixOverrides=",">-->
      <!--<if test="changeBranch != null">-->
        <!--CHANGE_BRANCH,-->
      <!--</if>-->
      <!--<if test="changeRegion != null">-->
        <!--CHANGE_REGION,-->
      <!--</if>-->
      <!--<if test="changeDate != null">-->
        <!--CHANGE_DATE,-->
      <!--</if>-->
      <!--<if test="changeSession != null">-->
        <!--CHANGE_SESSION,-->
      <!--</if>-->
      <!--<if test="preChangeDate != null">-->
        <!--PRE_CHANGE_DATE,-->
      <!--</if>-->
      <!--<if test="preChangeSession != null">-->
        <!--PRE_CHANGE_SESSION,-->
      <!--</if>-->
      <!--<if test="status != null">-->
        <!--STATUS,-->
      <!--</if>-->
      <!--<if test="preStatus != null">-->
        <!--PRE_STATUS,-->
      <!--</if>-->
      <!--<if test="preSessionStatus != null">-->
        <!--PRE_SESSION_STATUS,-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP,-->
      <!--</if>-->
      <!--<if test="jumpSessionFlag != null">-->
        <!--JUMP_SESSION_FLAG,-->
      <!--</if>-->
      <!--<if test="gcFileFlag != null">-->
        <!--GC_FILE_FLAG,-->
      <!--</if>-->
      <!--<if test="jumpDoneFlag != null">-->
        <!--JUMP_DONE_FLAG,-->
      <!--</if>-->
      <!--<if test="openCloseStatus != null">-->
        <!--OPEN_CLOSE_STATUS,-->
      <!--</if>-->
    <!--</trim>-->
    <!--<trim prefix="values (" suffix=")" suffixOverrides=",">-->
      <!--<if test="changeBranch != null">-->
        <!--#{changeBranch},-->
      <!--</if>-->
      <!--<if test="changeRegion != null">-->
        <!--#{changeRegion},-->
      <!--</if>-->
      <!--<if test="changeDate != null">-->
        <!--#{changeDate},-->
      <!--</if>-->
      <!--<if test="changeSession != null">-->
        <!--#{changeSession},-->
      <!--</if>-->
      <!--<if test="preChangeDate != null">-->
        <!--#{preChangeDate},-->
      <!--</if>-->
      <!--<if test="preChangeSession != null">-->
        <!--#{preChangeSession},-->
      <!--</if>-->
      <!--<if test="status != null">-->
        <!--#{status},-->
      <!--</if>-->
      <!--<if test="preStatus != null">-->
        <!--#{preStatus},-->
      <!--</if>-->
      <!--<if test="preSessionStatus != null">-->
        <!--#{preSessionStatus},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--#{tranTimestamp},-->
      <!--</if>-->
      <!--<if test="jumpSessionFlag != null">-->
        <!--#{jumpSessionFlag},-->
      <!--</if>-->
      <!--<if test="gcFileFlag != null">-->
        <!--#{gcFileFlag},-->
      <!--</if>-->
      <!--<if test="jumpDoneFlag != null">-->
        <!--#{jumpDoneFlag},-->
      <!--</if>-->
      <!--<if test="openCloseStatus != null">-->
        <!--#{openCloseStatus},-->
      <!--</if>-->
    <!--</trim>-->
  <!--</insert>-->

  <update id="updateOpenCloseOc" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus">
    update oc_change_status
    <set>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="preStatus != null">
        pre_status = #{preStatus}
      </if>
    </set>
    where change_branch = #{changeBranch}
     and  CHANGE_REGION= #{changeRegion}
     and  CHANGE_DATE= #{changeDate}
     and CHANGE_SESSION= #{changeSession}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateBpStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus">
    update oc_change_status
    <set>
      <if test="changeDate1 != null">
        change_date = #{changeDate1},
      </if>
      <if test="changeSession1 != null">
        change_session = #{changeSession1},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="preStatus != null">
        pre_status = #{preStatus},
      </if>
      <if test="preChangeSession != null">
        pre_change_session = #{preChangeSession},
      </if>
      <if test="preChangeDate != null">
        pre_change_date = #{preChangeDate},
      </if>
      <if test="preSessionStatus != null">
        pre_session_status = #{preSessionStatus}
      </if>
    </set>
    where change_branch = #{changeBranch}
    and  CHANGE_REGION= #{changeRegion}
    and  CHANGE_DATE= #{changeDate}
    and CHANGE_SESSION= #{changeSession}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <!--<update id="updateByPrimary" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus">-->
    <!--update oc_change_status-->
    <!--<set>-->
      <!--<if test="changeDate != null">-->
        <!--CHANGE_DATE = #{changeDate},-->
      <!--</if>-->
      <!--<if test="changeSession != null">-->
        <!--CHANGE_SESSION = #{changeSession},-->
      <!--</if>-->
      <!--<if test="preChangeDate != null">-->
        <!--PRE_CHANGE_DATE = #{preChangeDate},-->
      <!--</if>-->
      <!--<if test="preChangeSession != null">-->
        <!--PRE_CHANGE_SESSION = #{preChangeSession},-->
      <!--</if>-->
      <!--<if test="status != null">-->
        <!--STATUS = #{status},-->
      <!--</if>-->
      <!--<if test="preStatus != null">-->
        <!--PRE_STATUS = #{preStatus},-->
      <!--</if>-->
      <!--<if test="preSessionStatus != null">-->
        <!--PRE_SESSION_STATUS = #{preSessionStatus},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP = #{tranTimestamp},-->
      <!--</if>-->
      <!--<if test="openCloseStatus != null">-->
        <!--OPEN_CLOSE_STATUS = #{openCloseStatus}-->
      <!--</if>-->
    <!--</set>-->
    <!--where CHANGE_BRANCH = #{changeBranch}-->
    <!--and  CHANGE_REGION= #{changeRegion}-->
  <!--</update>-->
  <!--<select id="selectByChange" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus" parameterType="java.lang.String" >-->
    <!--select <include refid="Base_Column"/>-->
    <!--from oc_change_status-->
    <!--where change_branch = #{changeBranch}-->
    <!--and  CHANGE_REGION= #{changeRegion}-->
    <!--<if test="changeDate != null">-->
    <!--and  CHANGE_DATE= #{changeDate}-->
    <!--</if>-->
    <!--<if test="changeSession != null and changeSession.length() > 0">-->
    <!--and CHANGE_SESSION= #{changeSession}-->
  <!--</if>-->
  <!--</select>-->
  <!--<update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus">-->
    <!--update OC_CHANGE_STATUS-->
    <!--<set>-->
      <!--<if test="changeDate != null">-->
        <!--CHANGE_DATE = #{changeDate},-->
      <!--</if>-->
      <!--<if test="changeSession != null">-->
        <!--CHANGE_SESSION = #{changeSession},-->
      <!--</if>-->
      <!--<if test="preChangeDate != null">-->
        <!--PRE_CHANGE_DATE = #{preChangeDate},-->
      <!--</if>-->
      <!--<if test="preChangeSession != null">-->
        <!--PRE_CHANGE_SESSION = #{preChangeSession},-->
      <!--</if>-->
      <!--<if test="status != null">-->
        <!--STATUS = #{status},-->
      <!--</if>-->
      <!--<if test="preStatus != null">-->
        <!--PRE_STATUS = #{preStatus},-->
      <!--</if>-->
      <!--<if test="preSessionStatus != null">-->
        <!--PRE_SESSION_STATUS = #{preSessionStatus},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP = #{tranTimestamp},-->
      <!--</if>-->
      <!--<if test="jumpSessionFlag != null">-->
        <!--JUMP_SESSION_FLAG = #{jumpSessionFlag},-->
      <!--</if>-->
      <!--<if test="gcFileFlag != null">-->
        <!--GC_FILE_FLAG = #{gcFileFlag},-->
      <!--</if>-->
      <!--<if test="jumpDoneFlag != null">-->
        <!--JUMP_DONE_FLAG = #{jumpDoneFlag},-->
      <!--</if>-->
      <!--<if test="openCloseStatus != null">-->
        <!--OPEN_CLOSE_STATUS = #{openCloseStatus}-->
      <!--</if>-->
    <!--</set>-->
    <!--where CHANGE_BRANCH = #{changeBranch}-->
    <!--AND CHANGE_REGION = #{changeRegion}-->
  <!--</update>-->
  <!--<delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeStatus">-->
    <!--delete from OC_CHANGE_STATUS-->
    <!--where CHANGE_BRANCH = #{changeBranch}-->
    <!--<if test="changeRegion != null">-->
      <!--AND CHANGE_REGION = #{changeRegion}-->
    <!--</if>-->
  <!--</delete>-->
</mapper>
