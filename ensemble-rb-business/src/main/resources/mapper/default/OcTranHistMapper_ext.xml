<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where OC_REF_NO = #{ocRefNo}
        <if test="clientNo != null and  clientNo != '' ">
            and CLIENT_NO = #{clientNo}
        </if>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <!-- Created by LANMM on 2017/04/28 14:27:02. -->
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        insert into OC_TRAN_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ocRefNo != null">
                OC_REF_NO,
            </if>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="changeNo != null">
                CHANGE_NO,
            </if>
            <if test="changeRegion != null">
                CHANGE_REGION,
            </if>
            <if test="changeDate != null">
                CHANGE_DATE,
            </if>
            <if test="changeSession != null">
                CHANGE_SESSION,
            </if>
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO,
            </if>
            <if test="acctName != null">
                ACCT_NAME,
            </if>
            <if test="docType != null">
                DOC_TYPE,
            </if>
            <if test="prefix != null">
                PREFIX,
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO,
            </if>
            <if test="issDate != null">
                ISS_DATE,
            </if>
            <if test="tranAmt != null">
                TRAN_AMT,
            </if>
            <if test="payPassword != null">
                PAY_PASSWORD,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="othChangeNo != null">
                OTH_CHANGE_NO,
            </if>
            <if test="othChangeName != null">
                OTH_CHANGE_NAME,
            </if>
            <if test="othAcctNo != null">
                OTH_ACCT_NO,
            </if>
            <if test="othAcctName != null">
                OTH_ACCT_NAME,
            </if>
            <if test="othDocType != null">
                OTH_DOC_TYPE,
            </if>
            <if test="othPrefix != null">
                OTH_PREFIX,
            </if>
            <if test="othVoucherNo != null">
                OTH_VOUCHER_NO,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="apprUserId != null">
                APPR_USER_ID,
            </if>
            <if test="ocTranStatus != null">
                OC_TRAN_STATUS,
            </if>
            <if test="preTranStatus != null">
                PRE_TRAN_STATUS,
            </if>
            <if test="retFlag != null">
                RET_FLAG,
            </if>
            <if test="retProcessMode != null">
                RET_PROCESS_MODE,
            </if>
            <if test="orgOcRefNo != null">
                ORG_OC_REF_NO,
            </if>
            <if test="doneMethod != null">
                DONE_METHOD,
            </if>
            <if test="doneDate != null">
                DONE_DATE,
            </if>
            <if test="doneUserId != null">
                DONE_USER_ID,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="resSeqNo != null">
                RES_SEQ_NO,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="retChangeDate != null">
                RET_CHANGE_DATE,
            </if>
            <if test="retChangeSession != null">
                RET_CHANGE_SESSION,
            </if>
            <if test="retRecord != null">
                RET_RECORD
            </if>
            <!--多法人改造 by LIYUANV-->
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <!--多法人改造 by LIYUANV-->
            <if test="company != null">
                #{company},
            </if>
            <if test="ocRefNo != null">
                #{ocRefNo},
            </if>
            <if test="channelSeqNo != null">
                #{channelSeqNo},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="changeNo != null">
                #{changeNo},
            </if>
            <if test="changeRegion != null">
                #{changeRegion},
            </if>
            <if test="changeDate != null">
                #{changeDate},
            </if>
            <if test="changeSession != null">
                #{changeSession},
            </if>
            <if test="tranType != null">
                #{tranType},
            </if>
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="acctSeqNo != null">
                #{acctSeqNo},
            </if>
            <if test="acctName != null">
                #{acctName},
            </if>
            <if test="docType != null">
                #{docType},
            </if>
            <if test="prefix != null">
                #{prefix},
            </if>
            <if test="voucherNo != null">
                #{voucherNo},
            </if>
            <if test="issDate != null">
                #{issDate},
            </if>
            <if test="tranAmt != null">
                #{tranAmt},
            </if>
            <if test="payPassword != null">
                #{payPassword},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="othChangeNo != null">
                #{othChangeNo},
            </if>
            <if test="othChangeName != null">
                #{othChangeName},
            </if>
            <if test="othAcctNo != null">
                #{othAcctNo},
            </if>
            <if test="othAcctName != null">
                #{othAcctName},
            </if>
            <if test="othDocType != null">
                #{othDocType},
            </if>
            <if test="othPrefix != null">
                #{othPrefix},
            </if>
            <if test="othVoucherNo != null">
                #{othVoucherNo},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="apprUserId != null">
                #{apprUserId},
            </if>
            <if test="ocTranStatus != null">
                #{ocTranStatus},
            </if>
            <if test="preTranStatus != null">
                #{preTranStatus},
            </if>
            <if test="retFlag != null">
                #{retFlag},
            </if>
            <if test="retReason != null">
                #{retReason},
            </if>
            <if test="retProcessMode != null">
                #{retProcessMode},
            </if>
            <if test="orgOcRefNo != null">
                #{orgOcRefNo},
            </if>
            <if test="doneMethod != null">
                #{doneMethod},
            </if>
            <if test="doneDate != null">
                #{doneDate},
            </if>
            <if test="doneUserId != null">
                #{doneUserId},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="resSeqNo != null">
                #{resSeqNo},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="reasonCode != null">
                #{reasonCode},
            </if>
            <if test="reversalReason != null">
                #{reversalReason},
            </if>
            <if test="retChangeDate != null">
                #{retChangeDate},
            </if>
            <if test="retChangeSession != null">
                #{retChangeSession},
            </if>
            <if test="retRecord != null">
                #{retRecord}
            </if>
        </trim>
    </insert>

    <!-- 查询同城业务信息-->
    <select id="getOcTranInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        <where>
        <if test="bean.ocRefNo != null and bean.ocRefNo.length() > 0"><!--同城交易流水号-->
            and OC_REF_NO = #{bean.ocRefNo}
        </if>
        <if test="bean.tranType != null and bean.tranType.length() > 0"><!--交易类型-->
            and TRAN_TYPE = #{bean.tranType}
        </if>
        <if test="bean.baseAcctNo != null and bean.baseAcctNo.length() > 0"><!--账号-->
            and BASE_ACCT_NO = #{bean.baseAcctNo}
        </if>
        <if test="bean.tranBranch != null and bean.tranBranch.length() > 0"><!--交易机构-->
            and TRAN_BRANCH = #{bean.tranBranch}
        </if>
        <if test="bean.changeNo != null and bean.changeNo.length() > 0"><!--交换号-->
            and CHANGE_NO = #{bean.changeNo}
        </if>
        <if test="bean.changeRegion != null and bean.changeRegion.length() > 0"><!--交换地区-->
            and CHANGE_REGION = #{bean.changeRegion}
        </if>
        <if test="bean.changeDate != null"><!--交换日期-->
            and CHANGE_DATE = #{bean.changeDate}
        </if>
        <if test="bean.changeSession != null and bean.changeSession.length() > 0"><!--交换场次-->
            and CHANGE_SESSION = #{bean.changeSession}
        </if>
        <if test="startDate != null  and endDate != null "><!--交易日期-->
            and TRAN_DATE between #{startDate} AND #{endDate}
        </if>
        <if test="bean.ocTranStatus != null and bean.ocTranStatus.length() > 0"><!--交易状态(收妥入账会用到)-->
            and OC_TRAN_STATUS = #{bean.ocTranStatus}
        </if>
        <if test="bean.docType != null and bean.docType.length() > 0">
            and DOC_TYPE = #{bean.docType}
        </if>
        <if test="bean.reference != null and bean.reference.length() > 0">
            and REFERENCE = #{bean.reference}
        </if>
        <if test="bean.voucherNo != null and bean.voucherNo.length() > 0">
            and VOUCHER_NO = #{bean.voucherNo}
        </if>
        <if test="bean.othDocType != null and bean.othDocType.length() > 0">
            and OTH_DOC_TYPE = #{bean.othDocType}
        </if>
        <if test="bean.othVoucherNo != null and bean.othVoucherNo.length() > 0">
            and OTH_VOUCHER_NO = #{bean.othVoucherNo}
        </if>
        <if test="bean.userId != null and bean.userId.length() > 0">
            and USER_ID = #{bean.userId}
        </if>
        <if test="bean.othChangeNo != null and bean.othChangeNo.length() > 0">
            and OTH_CHANGE_NO = #{bean.othChangeNo}
        </if>
        <if test="bean.retRecord != null and bean.retRecord.length() > 0">
            and RET_RECORD = #{bean.retRecord}
        </if>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
        ORDER BY TRAN_TIMESTAMP DESC,TRAN_DATE DESC
    </select>
    <!-- 查询同城业务信息-->
    <select id="getOcTranInfosR" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where OC_TRAN_STATUS IN ('P','T')
        <if test="bean.ocRefNo != null and bean.ocRefNo.length() > 0"><!--同城交易流水号-->
            and OC_REF_NO = #{bean.ocRefNo}
        </if>
        <if test="bean.tranType != null and bean.tranType.length() > 0"><!--交易类型-->
            and TRAN_TYPE = #{bean.tranType}
        </if>
        <if test="bean.baseAcctNo != null and bean.baseAcctNo.length() > 0"><!--账号-->
            and BASE_ACCT_NO = #{bean.baseAcctNo}
        </if>
        <if test="bean.tranBranch != null and bean.tranBranch.length() > 0"><!--交易机构-->
            and TRAN_BRANCH = #{bean.tranBranch}
        </if>
        <if test="bean.changeNo != null and bean.changeNo.length() > 0"><!--交换号-->
            and CHANGE_NO = #{bean.changeNo}
        </if>
        <if test="bean.changeRegion != null and bean.changeRegion.length() > 0"><!--交换地区-->
            and CHANGE_REGION = #{bean.changeRegion}
        </if>
        <if test="bean.changeDate != null"><!--交换日期-->
            and CHANGE_DATE = #{bean.changeDate}
        </if>
        <if test="bean.changeSession != null and bean.changeSession.length() > 0"><!--交换场次-->
            and CHANGE_SESSION = #{bean.changeSession}
        </if>
        <if test="startDate != null  and endDate != null "><!--交易日期-->
            and TRAN_DATE between #{startDate} AND #{endDate}
        </if>
        <if test="bean.ocTranStatus != null and bean.ocTranStatus.length() > 0"><!--交易状态(收妥入账会用到)-->
            and OC_TRAN_STATUS = #{bean.ocTranStatus}
        </if>
        <if test="bean.docType != null and bean.docType.length() > 0">
            and DOC_TYPE = #{bean.docType}
        </if>
        <if test="bean.reference != null and bean.reference.length() > 0">
            and REFERENCE = #{bean.reference}
        </if>
        <if test="bean.voucherNo != null and bean.voucherNo.length() > 0">
            and VOUCHER_NO = #{bean.voucherNo}
        </if>
        <if test="bean.othDocType != null and bean.othDocType.length() > 0">
            and OTH_DOC_TYPE = #{bean.othDocType}
        </if>
        <if test="bean.othVoucherNo != null and bean.othVoucherNo.length() > 0">
            and OTH_VOUCHER_NO = #{bean.othVoucherNo}
        </if>
        <if test="bean.userId != null and bean.userId.length() > 0">
            and USER_ID = #{bean.userId}
        </if>
        <if test="bean.othChangeNo != null and bean.othChangeNo.length() > 0">
            and OTH_CHANGE_NO = #{bean.othChangeNo}
        </if>
        <if test="bean.retRecord != null and bean.retRecord.length() > 0">
            and RET_RECORD = #{bean.retRecord}
        </if>

        AND TRAN_TYPE NOT IN('ODNR','ICNR','OCRR','ICRR','OCNR','IDNR','ODRR','IDRR','TCDR')
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY TRAN_TIMESTAMP DESC,TRAN_DATE DESC
    </select>
    <!-- 查询同城业务信息-->
    <select id="getOcTranInfosToUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where CHANGE_NO = #{changeNo}
        and CHANGE_REGION = #{changeRegion}
        and OC_TRAN_STATUS='P'
        and change_date is NULL
        and change_session is null
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <!-- 冲正时查询原流水号 -->
    <select id="getOneOcTranInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where TRAN_DATE = #{tranDate}
        <if test="ocRefNo != null and ocRefNo.length() > 0"><!--交易流水号 -->
            and OC_REF_NO = #{ocRefNo}
        </if>
        <if test="channelSeqNo != null and channelSeqNo.length() > 0"><!-- 渠道流水号 -->
            and CHANNEL_SEQ_NO = #{channelSeqNo}
        </if>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <!-- 根据原流水号查询 -->
    <select id="getOneByOcRefNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where  OC_REF_NO = #{ocRefNo}

    </select>

    <!-- 根据原流水号查询 -->
    <select id="getOcTranInfoByVoucherNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where  TRAN_TYPE in ('ODNN','OCNN')
        AND RET_FLAG='Y'
        AND OC_TRAN_STATUS in ('N','P')
        and RET_PROCESS_MODE is not null
        AND VOUCHER_NO = #{voucherNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <!-- 同城交易流水修改-->
    <update id="updOcTranInfoBySeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        update OC_TRAN_HIST
        <set>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO = #{channelSeqNo},
            </if>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="changeNo != null">
                CHANGE_NO = #{changeNo},
            </if>
            <if test="changeRegion != null">
                CHANGE_REGION = #{changeRegion},
            </if>
            <if test="changeDate != null">
                CHANGE_DATE = #{changeDate},
            </if>
            <if test="changeSession != null">
                CHANGE_SESSION = #{changeSession},
            </if>
            <if test="tranType != null">
                TRAN_TYPE = #{tranType},
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO = #{baseAcctNo},
            </if>
            <if test="prodType != null">
                PROD_TYPE = #{prodType},
            </if>
            <if test="ccy != null">
                CCY = #{ccy},
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO = #{acctSeqNo},
            </if>
            <if test="acctName != null">
                ACCT_NAME = #{acctName},
            </if>
            <if test="docType != null">
                DOC_TYPE = #{docType},
            </if>
            <if test="prefix != null">
                PREFIX = #{prefix},
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO = #{voucherNo},
            </if>
            <if test="issDate != null">
                ISS_DATE = #{issDate},
            </if>
            <if test="tranAmt != null">
                TRAN_AMT = #{tranAmt},
            </if>
            <if test="payPassword != null">
                PAY_PASSWORD = #{payPassword},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="othChangeNo != null">
                OTH_CHANGE_NO = #{othChangeNo},
            </if>
            <if test="othChangeName != null">
                OTH_CHANGE_NAME = #{othChangeName},
            </if>
            <if test="othAcctNo != null">
                OTH_ACCT_NO = #{othAcctNo},
            </if>
            <if test="othAcctName != null">
                OTH_ACCT_NAME = #{othAcctName},
            </if>
            <if test="othDocType != null">
                OTH_DOC_TYPE = #{othDocType},
            </if>
            <if test="othPrefix != null">
                OTH_PREFIX = #{othPrefix},
            </if>
            <if test="othVoucherNo != null">
                OTH_VOUCHER_NO = #{othVoucherNo},
            </if>
            <if test="userId != null">
                USER_ID = #{userId},
            </if>
            <if test="apprUserId != null">
                APPR_USER_ID = #{apprUserId},
            </if>
            <if test="ocTranStatus != null">
                OC_TRAN_STATUS = #{ocTranStatus},
            </if>
            <if test="preTranStatus != null">
                PRE_TRAN_STATUS = #{preTranStatus},
            </if>
            <if test="retFlag != null">
                RET_FLAG = #{retFlag},
            </if>
            <if test="retProcessMode != null">
                RET_PROCESS_MODE = #{retProcessMode},
            </if>
            <if test="orgOcRefNo != null">
                ORG_OC_REF_NO = #{orgOcRefNo},
            </if>
            <if test="doneMethod != null">
                DONE_METHOD = #{doneMethod},
            </if>
            <if test="doneDate != null">
                DONE_DATE = #{doneDate},
            </if>
            <if test="doneUserId != null">
                DONE_USER_ID = #{doneUserId},
            </if>
            <if test="reference != null">
                REFERENCE = #{reference},
            </if>
            <if test="resSeqNo != null">
                RES_SEQ_NO = #{resSeqNo},
            </if>
            <if test="doneFailReason != null">
                DONE_FAIL_REASON = #{doneFailReason},
            </if>
            <if test="retChangeDate != null">
                RET_CHANGE_DATE = #{retChangeDate},
            </if>
            <if test="retChangeSession != null">
                RET_CHANGE_SESSION = #{retChangeSession},
            </if>
            <if test="retRecord != null">
                RET_RECORD = #{retRecord},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where OC_REF_NO = #{ocRefNo}
            <if test="clientNo != null and  clientNo != '' ">
               and CLIENT_NO = #{clientNo}
            </if>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <!-- 报盘后更新交易流水场次，日期信息-->
    <update id="updOcTranInfoByBp" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        update OC_TRAN_HIST
        <set>
            CHANGE_DATE = #{changeDate},
            CHANGE_SESSION = #{changeSession}
        </set>
        where CHANGE_NO = #{changeNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and CHANGE_REGION = #{changeRegion}
        and OC_TRAN_STATUS='P'
        <if test="ocRefNo != null">
        and OC_REF_NO = #{ocRefNo}
        </if>
        and change_date is NULL
        and change_session is null
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
    </update>

    <!-- 报盘后更新交易流水场次，日期信息-->
    <update id="updOcTranInfoByBpQx" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        update OC_TRAN_HIST
        <set>
            CHANGE_DATE = #{changeDateNew},
            CHANGE_SESSION = #{changeSessionNew}
        </set>
        where CHANGE_NO = #{changeNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and CHANGE_REGION = #{changeRegion}
        and OC_TRAN_STATUS='P'
        and change_date = #{changeDate}
        and change_session = #{changeSession}
        and CLIENT_NO = #{clientNo}
    </update>


    <!-- 查询已完成交易笔数金额 -->
    <select id="getDoneCountByUser" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
	    USER_ID,
	    OTH_CHANGE_NO,
	    DOC_TYPE,
        TRAN_TYPE,
        count(*) AS DONE_NUM,
        SUM(TRAN_AMT) DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE  CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND TRAN_BRANCH= #{tranBranch}
        <!--
	    AND UPPER(USER_ID)= UPPER(#{userId})
	    -->
        <if test="othChangeNo != null">
            AND OTH_CHANGE_NO= #{othChangeNo}
        </if>
        <if test="docType != null">
            AND DOC_TYPE= #{docType}
        </if>
        AND OC_TRAN_STATUS in ('N','P')
        <if test="tranType =='CR'">
        AND  TRAN_TYPE in ('ICNN','ICRN','OCRN')
        </if>
        <if test="tranType =='DR'">
        AND  TRAN_TYPE in ('IDNN','IDRN','ODRN')
        </if>
        <if test="userId != null">
        AND USER_ID= #{userId}
        </if>
        GROUP BY
        TRAN_TYPE,TRAN_BRANCH,USER_ID,OTH_CHANGE_NO,DOC_TYPE

  </select>
    <!-- 查询已完成交易笔数金额 -->
    <select id="getDoneCountCrDrR" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
        USER_ID,
        OTH_CHANGE_NO,
        DOC_TYPE,
        TRAN_TYPE,
        count(*) AS DONE_NUM,
        SUM(TRAN_AMT) DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND RET_CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND TRAN_BRANCH= #{tranBranch}
        AND UPPER(USER_ID)= UPPER(#{userId})
        <if test="othChangeNo != null">
            AND OTH_CHANGE_NO= #{othChangeNo}
        </if>
        <if test="docType != null">
            AND DOC_TYPE= #{docType}
        </if>
        AND OC_TRAN_STATUS in ('N','P')
        and TRAN_TYPE = #{tranType}
        GROUP BY
        TRAN_TYPE,TRAN_BRANCH,USER_ID,OTH_CHANGE_NO,DOC_TYPE
    </select>

    <!-- 查询已录入的提出业务交易笔数金额 -->
    <select id="getOutputCount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
        USER_ID,
        OTH_CHANGE_NO,
        DOC_TYPE,
        TRAN_TYPE,
        '1' AS DONE_NUM,
        TRAN_AMT AS DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE  CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND (OC_TRAN_STATUS ='P' or OC_TRAN_STATUS ='N' or OC_TRAN_STATUS ='T')
        AND  TRAN_TYPE in ('OCNN','OCRN','ODNN','ODRN','OTCR')
    </select>

    <!-- 查询已录入的提入业务交易笔数金额 -->
    <select id="getInputCount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
        USER_ID,
        OTH_CHANGE_NO,
        DOC_TYPE,
        TRAN_TYPE,
        '1' AS DONE_NUM,
        TRAN_AMT AS DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE  CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND (OC_TRAN_STATUS ='P' or OC_TRAN_STATUS ='N')
        AND  TRAN_TYPE in ('ICNN','ICRN','IDNN','IDRN','OCRN','ODRN')
    </select>

    <!-- 查询已完成交易笔数金额 -->
    <select id="getDoneCount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
        USER_ID,
        OTH_CHANGE_NO,
        DOC_TYPE,
        TRAN_TYPE,
        '1' AS DONE_NUM,
        TRAN_AMT AS DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE  CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND OC_TRAN_STATUS ='P'
        AND  TRAN_TYPE in ('ICNN','ICRN','IDNN','IDRN','OCRN','ODRN')
    </select>
    <!-- 查询已录入借方交易笔数金额 -->
    <select id="getEnterCountD" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
        USER_ID,
        OTH_CHANGE_NO,
        DOC_TYPE,
        TRAN_TYPE,
        '1' AS DONE_NUM,
        TRAN_AMT AS DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE  CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND OC_TRAN_STATUS in ('N','P')
        AND  TRAN_TYPE in ('IDNN','IDRN','ODRN')
    </select>
    <select id="getEnterCountC" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
        USER_ID,
        OTH_CHANGE_NO,
        DOC_TYPE,
        TRAN_TYPE,
        '1' AS DONE_NUM,
        TRAN_AMT AS DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE  CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND OC_TRAN_STATUS in ('N','P')
        AND  TRAN_TYPE in ('ICNN','ICRN','OCRN')
    </select>
    <!-- 查询提出被退票的已完成交易笔数金额 -->
    <select id="getDoneCount1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.OcDoneCount">
        SELECT
        TRAN_BRANCH,
        USER_ID,
        OTH_CHANGE_NO,
        DOC_TYPE,
        TRAN_TYPE,
        '1' AS DONE_NUM,
        TRAN_AMT AS DONE_AMT
        FROM
        OC_TRAN_HIST
        WHERE  RET_CHANGE_DATE =#{retChangeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND RET_CHANGE_SESSION =#{retChangeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND OC_TRAN_STATUS ='P'
        AND  TRAN_TYPE in ('ODRN','OCRN')
    </select>
    <!-- 收妥入账的查询-->
    <select id="getOcTranHists" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where DONE_METHOD IS NULL
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="ocRefNo != null"><!--同城交易流水号-->
            and OC_REF_NO = #{ocRefNo}
        </if>
        <if test="tranType != null"><!--交易类型-->
            and TRAN_TYPE = #{tranType}
        </if>
        <if test="tranBranch != null"><!--交易机构-->
            and TRAN_BRANCH = #{tranBranch}
        </if>
        <if test="changeNo != null"><!--交换号-->
            and CHANGE_NO = #{changeNo}
        </if>
        <if test="changeRegion != null"><!--交换地区-->
            and CHANGE_REGION = #{changeRegion}
        </if>
        <if test="changeDate != null"><!--交换日期-->
            and CHANGE_DATE = #{changeDate}
        </if>
        <if test="changeSession != null"><!--交换场次-->
            and CHANGE_SESSION = #{changeSession}
        </if>
        <if test="ocTranStatus != null"><!--交易状态(收妥入账会用到)-->
            and OC_TRAN_STATUS = #{ocTranStatus}
        </if>

            and RET_PROCESS_MODE IS NULL<!--退票处理方式-->
    </select>
    <!-- 查询同城业务信息-->
    <select id="getOcTranInfo1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where  CHANGE_DATE is null
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="bean.ocRefNo != null and bean.ocRefNo.length() > 0"><!--同城交易流水号-->
            and OC_REF_NO = #{bean.ocRefNo}
        </if>
        <if test="bean.tranType != null and bean.tranType.length() > 0"><!--交易类型-->
            and TRAN_TYPE = #{bean.tranType}
        </if>
        <if test="bean.baseAcctNo != null and bean.baseAcctNo.length() > 0"><!--账号-->
            and BASE_ACCT_NO = #{bean.baseAcctNo}
        </if>
        <if test="bean.tranBranch != null and bean.tranBranch.length() > 0"><!--交易机构-->
            and TRAN_BRANCH = #{bean.tranBranch}
        </if>
        <if test="bean.changeNo != null and bean.changeNo.length() > 0"><!--交换号-->
            and CHANGE_NO = #{bean.changeNo}
        </if>
        <if test="bean.changeRegion != null and bean.changeRegion.length() > 0"><!--交换地区-->
            and CHANGE_REGION = #{bean.changeRegion}
        </if>

            and CHANGE_SESSION is null
        <if test="startDate != null  and endDate != null "><!--交易日期-->
            and TRAN_DATE between #{startDate} AND #{endDate}
        </if>
        <if test="bean.ocTranStatus != null and bean.ocTranStatus.length() > 0"><!--交易状态(收妥入账会用到)-->
            and OC_TRAN_STATUS = #{bean.ocTranStatus}
        </if>
        <if test="bean.docType != null and bean.docType.length() > 0">
            and DOC_TYPE = #{bean.docType}
        </if>
        <if test="bean.voucherNo != null and bean.voucherNo.length() > 0">
            and VOUCHER_NO = #{bean.voucherNo}
        </if>
        <if test="bean.othDocType != null and bean.othDocType.length() > 0">
            and OTH_DOC_TYPE = #{bean.othDocType}
        </if>
        <if test="bean.othVoucherNo != null and bean.othVoucherNo.length() > 0">
            and OTH_VOUCHER_NO = #{bean.othVoucherNo}
        </if>
        <if test="bean.retRecord != null and bean.retRecord.length() > 0">
            and RET_RECORD = #{bean.retRecord}
        </if>
    </select>
    <!-- 查询同城业务信息（未复核）-->
    <select id="getOcTranInfosClose" parameterType="java.util.Map" resultMap="Base_Result_Map">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        <where>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="tranBranch != null and tranBranch.length() > 0"><!--交易机构-->
            and TRAN_BRANCH = #{tranBranch}
        </if>
        <if test="ocTranStatus != null and ocTranStatus.length() > 0"><!--交易状态(未复核)-->
            and OC_TRAN_STATUS = #{ocTranStatus}
        </if>
        </where>
     </select>

    <!-- 根据原流水查退票产生新的流水 -->
    <select id="getOcRefNoByOrg" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where  ORG_OC_REF_NO = #{orgOcRefNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="reference != null and reference!= ''">
            AND REFERENCE = #{reference}
        </if>
        AND OC_REF_NO !=ORG_OC_REF_NO
        <if test="clientNo != null and clientNo!= ''">
            and CLIENT_NO = #{clientNo}
        </if>
    </select>
    <select id="getOcRefNoByOrgRefNO" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where  OC_REF_NO = #{ocRefNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND OC_REF_NO =ORG_OC_REF_NO
    </select>
    <!--提出借方，收妥入账，异常入账后，通过原流水会查出两笔-->
    <select id="getListByOrg" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        from OC_TRAN_HIST
        where  ORG_OC_REF_NO = #{orgOcRefNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND OC_REF_NO !=ORG_OC_REF_NO
    </select>
    <!-- 查询已完成交易 -->
    <select id="getDoneInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        WHERE  CHANGE_DATE =#{changeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND CHANGE_SESSION =#{changeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND OC_TRAN_STATUS ='P'
        AND TRAN_TYPE in ('ICNN','ICRN','IDNN','IDRN','OCRN','ODRN')
    </select>
    <!-- 查询提出被退票的已完成交易 -->
    <select id="getDoneInfo1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        WHERE  RET_CHANGE_DATE =#{retChangeDate}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_NO = #{changeNo}
        AND RET_CHANGE_SESSION =#{retChangeSession}
        AND CHANGE_REGION = #{changeRegion}
        AND OC_TRAN_STATUS ='P'
        AND TRAN_TYPE in ('ODRN','OCRN')
    </select>
    <!-- 查询所有未收妥入账的提出托收交易 -->
    <select id="getOcTccrHists" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        WHERE TRAN_TYPE ='ODNN'
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND OC_TRAN_STATUS ='P'
        AND CHANGE_DATE IS NOT NULL
        AND CHANGE_SESSION IS NOT NULL
        AND DONE_METHOD IS NULL
        AND RET_PROCESS_MODE IS NULL
    </select>

    <!-- 查询未收妥入账的提出托收交易 -->
    <select id="getOcTccrHistsByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        WHERE TRAN_TYPE ='ODNN'
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND OC_TRAN_STATUS ='P'
        AND CHANGE_DATE IS NOT NULL
        AND CHANGE_SESSION IS NOT NULL
        AND DONE_METHOD IS NULL
        AND RET_PROCESS_MODE IS NULL
        AND BASE_ACCT_NO = #{baseAcctNo}
        AND CLIENT_NO = #{clientNo}
        <trim suffixOverrides="AND">
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy}  AND
            </if>
            <if test="prodType != null and  prodType != '' ">
                PROD_TYPE = #{prodType}  AND
            </if>
            <if test="acctSeqNo != null and  acctSeqNo != ''">
                ACCT_SEQ_NO = #{acctSeqNo}  AND
            </if>
        </trim>
    </select>

    <!-- 机构当天必须处理完所有提入业务 -->
    <select id="getInTranListBybranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        WHERE  TRAN_BRANCH in (#{fromBranch},#{toBranch})
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND OC_TRAN_STATUS ='N'
        AND TRAN_TYPE in ('IDNN','ICNN')
    </select>


    <!-- 最后一场提出业务 -->
    <select id="getOutTranListByOrgSession" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        WHERE  TRAN_BRANCH in (#{fromBranch},#{toBranch})
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CHANGE_REGION = #{changeRegion}
        AND OC_TRAN_STATUS ='P'
        AND TRAN_TYPE in ('ODRN','OCRN')
    </select>

    <!-- 获取流水数据 -->
    <select id="getOcTranHistCount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        <where>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="startDate != null and endDate != null ">
            and CHANGE_DATE between #{startDate} AND #{endDate}
        </if>
        <if test="tranAmt != null and tranAmt != null">
            and TRAN_AMT <![CDATA[>=]]> #{tranAmt}
        </if>
        </where>
    </select>

    <!-- 提出未收妥入账的交易 -->
    <select id="getOcTranHistNotDone" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcTranHist">
        select
        <include refid="Base_Column"/>
        FROM OC_TRAN_HIST
        WHERE TRAN_TYPE ='ODNN'
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

        AND OC_TRAN_STATUS ='P'
<!--        AND done_flag is null-->
        AND BASE_ACCT_NO = #{baseAcctNo}
        AND CLIENT_NO = #{clientNo}
        <trim suffixOverrides="AND">
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy}  AND
            </if>
            <if test="prodType != null and  prodType != '' ">
                PROD_TYPE = #{prodType}  AND
            </if>
            <if test="acctSeqNo != null and  acctSeqNo != ''">
                ACCT_SEQ_NO = #{acctSeqNo}  AND
            </if>
        </trim>
    </select>
</mapper>
