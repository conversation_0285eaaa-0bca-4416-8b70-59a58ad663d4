<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCreditBseRestrictSign">

    <select id="getRbCreditByCardNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCreditBseRestrictSign">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_CREDIT_BSE_RESTRICT_SIGN
        WHERE
        <if test="cardNo != null and cardNo != '' " >
            CARD_NO = #{cardNo}
        </if>
            AND SETTLE_FLAG = 'Y'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getRbCreditBystlSeqNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCreditBseRestrictSign">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_CREDIT_BSE_RESTRICT_SIGN
        WHERE SETTLE_FLAG = 'Y'
        <if test="stlSeqNo != null and stlSeqNo != '' " >
            AND SEQ_NO = #{stlSeqNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>

    <update id="updateSETTLE_FLAGAndDate" parameterType="java.util.Map">
        update RB_CREDIT_BSE_RESTRICT_SIGN
        <set>
            <if test="settleFlag != null">
                SETTLE_FLAG = #{settleFlag},
            </if>
            <if test="date != null">
                LAST_CHANGE_DATE = #{date}
            </if>
        </set>
        where SEQ_NO = #{resSeqNo}
        AND CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
</mapper>
