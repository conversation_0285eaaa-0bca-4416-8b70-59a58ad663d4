<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctStats">
    <select id="selectByPrimaryKeyExt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctStats"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctStats">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        select
        <include refid="Base_Column"/>
        from RB_acct_stats
        where INTERNAL_KEY = #{internalKey}
        and CTRL_DATE = #{ctrlDate,jdbcType=DATE}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectByinternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctStats"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctStats">
        select
        <include refid="Base_Column"/>
        from RB_acct_stats
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectByInternalKeys" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.RbAcctStatsModel">
        select DATA_MONTH,sum(MON_AVG_AMT) MON_AVG_AMT,COUNT(1) COUNT
        from RB_acct_stats
        where client_no = #{clientNo}
        and DATA_MONTH BETWEEN #{startDate} and #{endDate}
        <if test="internalKeys != null ">
            and internal_key IN
            <foreach collection="internalKeys" item="internalKey" index="index" open="(" separator="," close=")">
                #{internalKey}
            </foreach>
        </if>
        <if test="company != null and company.length() > 0">
            AND COMPANY = #{company}
        </if>
        group by DATA_MONTH
    </select>

    <select id="getRbAcctStatsByDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.RbAcctStatsModel">
        select sum(MON_AVG_AMT) MON_AVG_AMT,COUNT(1) COUNT
        from RB_acct_stats
        where client_no = #{clientNo}
        and DATA_MONTH BETWEEN #{startDate} and #{endDate}
        <if test="internalKeys != null ">
            and internal_key IN
            <foreach collection="internalKeys" item="internalKey" index="index" open="(" separator="," close=")">
                #{internalKey}
            </foreach>
        </if>
        <if test="company != null and company.length() > 0">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectByInternalKeyAndMonth" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.RbAcctStatsModel">
        select *
        from RB_acct_stats
        where client_no = #{clientNo}
        <if test="dataMonth != '' and dataMonth != null">
            and DATA_MONTH =#{dataMonth}
        </if>
        <if test="internalKey != '' and internalKey != null">
            and internal_key =#{internalKey}
        </if>
        <if test="company != null and company.length() > 0">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getYearAvgAmt" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.RbAcctStatsModel">
        SELECT AVG(MON_AVG_AMT) AS YEAR_AVG_AMT
        FROM RB_acct_stats
        WHERE YEAR(TRAN_DATE) = #{lastYear}
        and internal_key=#{internalKey}
        and client_no=#{clientNo};
    </select>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctStats">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        insert into RB_acct_stats
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="ctrlDate != null">
                CTRL_DATE,
            </if>
            <if test="actualBal != null">
                ACTUAL_BAL,
            </if>
            <if test="aggBalCtd != null">
                AGG_BAL_CTD,
            </if>
            <if test="aggBalMtd != null">
                AGG_BAL_MTD,
            </if>
            <if test="aggBalQtd != null">
                AGG_BAL_QTD,
            </if>
            <if test="aggBalStd != null">
                AGG_BAL_STD,
            </if>
            <if test="aggBalYtd != null">
                AGG_BAL_YTD,
            </if>
            <if test="ctdDays != null">
                CTD_DAYS,
            </if>
            <if test="mtdDays != null">
                MTD_DAYS,
            </if>
            <if test="qtdDays != null">
                QTD_DAYS,
            </if>
            <if test="stdDays != null">
                STD_DAYS,
            </if>
            <if test="ytdDays != null">
                YTD_DAYS,
            </if>
            <if test="actMtdDays != null">
                ACT_MTD_DAYS,
            </if>
            <if test="actQtdDays != null">
                ACT_QTD_DAYS,
            </if>
            <if test="actStdDays != null">
                ACT_STD_DAYS,
            </if>
            <if test="actYtdDays != null">
                ACT_YTD_DAYS,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="ctrlDate != null">
                #{ctrlDate},
            </if>
            <if test="actualBal != null">
                #{actualBal},
            </if>
            <if test="aggBalCtd != null">
                #{aggBalCtd},
            </if>
            <if test="aggBalMtd != null">
                #{aggBalMtd},
            </if>
            <if test="aggBalQtd != null">
                #{aggBalQtd},
            </if>
            <if test="aggBalStd != null">
                #{aggBalStd},
            </if>
            <if test="aggBalYtd != null">
                #{aggBalYtd},
            </if>
            <if test="ctdDays != null">
                #{ctdDays},
            </if>
            <if test="mtdDays != null">
                #{mtdDays},
            </if>
            <if test="qtdDays != null">
                #{qtdDays},
            </if>
            <if test="stdDays != null">
                #{stdDays},
            </if>
            <if test="ytdDays != null">
                #{ytdDays},
            </if>
            <if test="actMtdDays != null">
                #{actMtdDays},
            </if>
            <if test="actQtdDays != null">
                #{actQtdDays},
            </if>
            <if test="actStdDays != null">
                #{actStdDays},
            </if>
            <if test="actYtdDays != null">
                #{actYtdDays},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctStats">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        update RB_acct_stats
        <set>
            <if test="actualBal != null">
                ACTUAL_BAL = #{actualBal},
            </if>
            <if test="aggBalCtd != null">
                AGG_BAL_CTD = #{aggBalCtd},
            </if>
            <if test="aggBalMtd != null">
                AGG_BAL_MTD = #{aggBalMtd},
            </if>
            <if test="aggBalQtd != null">
                AGG_BAL_QTD = #{aggBalQtd},
            </if>
            <if test="aggBalStd != null">
                AGG_BAL_STD = #{aggBalStd},
            </if>
            <if test="aggBalYtd != null">
                AGG_BAL_YTD = #{aggBalYtd},
            </if>
            <if test="ctdDays != null">
                CTD_DAYS = #{ctdDays},
            </if>
            <if test="mtdDays != null">
                MTD_DAYS = #{mtdDays},
            </if>
            <if test="qtdDays != null">
                QTD_DAYS = #{qtdDays},
            </if>
            <if test="stdDays != null">
                STD_DAYS = #{stdDays},
            </if>
            <if test="ytdDays != null">
                YTD_DAYS = #{ytdDays},
            </if>
            <if test="actMtdDays != null">
                ACT_MTD_DAYS = #{actMtdDays},
            </if>
            <if test="actQtdDays != null">
                ACT_QTD_DAYS = #{actQtdDays},
            </if>
            <if test="actStdDays != null">
                ACT_STD_DAYS = #{actStdDays},
            </if>
            <if test="actYtdDays != null">
                ACT_YTD_DAYS = #{actYtdDays},
            </if>
            <if test="company != null">
                COMPANY = #{company},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        and CTRL_DATE = #{ctrlDate,jdbcType=DATE}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="selectByInternalKeyAndPeriod" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.cm.RbAcctStatsModel">
        select *
        from RB_acct_stats
        where client_no = #{clientNo}
        <if test="internalKey != '' and internalKey != null">
            and internal_key =#{internalKey}
        </if>
        <if test="startMonth != null and endMonth != null">
            and DATA_MONTH BETWEEN #{startMonth} and #{endMonth}
        </if>
    </select>
</mapper>
