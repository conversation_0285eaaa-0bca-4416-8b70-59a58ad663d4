<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmRenewDefaults">
<sql id="Base_Column_List">
    INTERNAL_KEY,
    TENOR,
    MATURITY_DATE,
    INT_TYPE,
    ACTUAL_RATE,
    FLOAT_RATE,
    SPREAD_RATE,
    SPREAD_PERCENT,
    ACCT_FIXED_RATE,
    ACCT_SPREAD_RATE,
    ACCT_PERCENT_RATE,
    REAL_RATE,
    TREASURY_MARGIN,
    TRAN_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmRenewDefaults" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmRenewDefaults">
    select <include refid="Base_Column_List"/>
    from MM_RENEW_DEFAULTS
    where INTERNAL_KEY = #{internalKey}
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmRenewDefaults">
    delete from MM_RENEW_DEFAULTS
    where INTERNAL_KEY = #{internalKey}
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmRenewDefaults">
    update MM_RENEW_DEFAULTS
    <set>
      <if test="tenor != null">
        TENOR = #{tenor},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="intType != null">
        INT_TYPE = #{intType},
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE = #{actualRate},
      </if>
      <if test="floatRate != null">
        FLOAT_RATE = #{floatRate},
      </if>
      <if test="spreadRate != null">
        SPREAD_RATE = #{spreadRate},
      </if>
      <if test="spreadPercent != null">
        SPREAD_PERCENT = #{spreadPercent},
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE = #{acctFixedRate},
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE = #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE = #{acctPercentRate},
      </if>
      <if test="realRate != null">
        REAL_RATE = #{realRate},
      </if>
      <if test="treasuryMargin != null">
        TREASURY_MARGIN = #{treasuryMargin},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP= #{tranTimestamp}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmRenewDefaults">
    insert into MM_RENEW_DEFAULTS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="tenor != null">
        TENOR,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="spreadRate != null">
        SPREAD_RATE,
      </if>
      <if test="spreadPercent != null">
        SPREAD_PERCENT,
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE,
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE,
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="treasuryMargin != null">
        TREASURY_MARGIN,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="tenor != null">
        #{tenor},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="spreadRate != null">
        #{spreadRate},
      </if>
      <if test="spreadPercent != null">
        #{spreadPercent},
      </if>
      <if test="acctFixedRate != null">
        #{acctFixedRate},
      </if>
      <if test="acctSpreadRate != null">
        #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        #{acctPercentRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="treasuryMargin != null">
        #{treasuryMargin},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <select id="getMmRenewDefaultsByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmRenewDefaults">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_RENEW_DEFAULTS
    WHERE INTERNAL_KEY = #{internalKey}
  </select>
</mapper>
