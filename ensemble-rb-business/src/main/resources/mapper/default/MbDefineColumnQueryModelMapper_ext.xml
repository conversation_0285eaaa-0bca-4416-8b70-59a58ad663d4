<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.dcits.ensemble.rb.business.model.acct.MbDefineColumnQueryModel">
    <select id="getDefineColumnValueList"
    parameterType="com.dcits.ensemble.rb.business.model.acct.MbDefineColumnQueryModel"
    resultType="com.dcits.ensemble.rb.business.model.acct.MbDefineColumnQueryModel">
        select ${columnString}
        from ${tableName};
    </select>
</mapper>