<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbRunDateNotice">

    <select id="selectNoticeInfoByTranDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbRunDateNotice" databaseId="oracle" useCache="false" flushCache="true">
        select TRAN_DATE, SWITCH_YN, NEXT_RUN_DATE, COMPANY, TRAN_TIMESTAMP,EXTRACT_DATA_STATUS_FLAG from RB_RUN_DATE_NOTICE where TRAN_DATE = #{trandate,jdbcType=DATE}
    </select>


</mapper>