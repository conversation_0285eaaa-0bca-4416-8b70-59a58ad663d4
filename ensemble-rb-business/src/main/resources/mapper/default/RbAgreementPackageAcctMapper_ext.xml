<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackageAcct">

  <select id="selectByAgreementId" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackageAcct" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackageAcct">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_PACKAGE_ACCT
    where AGREEMENT_ID = #{agreementId}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbAgreementPackageByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackageAcct">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_PACKAGE_ACCT
    <where>
    <if test="baseAcctNo != null and baseAcctNo != ''">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
<!--  <select id="selectBenefitAcctByPage" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"-->
<!--          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.BenefitAcctModel">-->
<!--    select pkg.AGREEMENT_ID, a.AGREEMENT_OPEN_DATE, pkg.CLIENT_NO, doc.DOCUMENT_TYPE, doc.DOCUMENT_ID,-->
<!--    acct.BASE_ACCT_NO, acct.PROD_TYPE, acct.ACCT_CCY, acct.ACCT_SEQ_NO, acct.REASON_CODE-->
<!--    from mb_agreement_package pkg, cif_client_document doc, mb_agreement_package_acct acct, mb_agreement a-->
<!--    where doc.client_no = pkg.client_no and acct.agreement_id = pkg.agreement_id and pkg.agreement_id = a.agreement_id-->
<!--    <if test="baseParam.clientNo != null and baseParam.clientNo.length() > 0">-->
<!--      and pkg.client_no = #{baseParam.clientNo}-->
<!--    </if>-->
<!--    <if test="baseParam.documentType != null and baseParam.documentType.length() > 0">-->
<!--      and doc.document_type = #{baseParam.documentType}-->
<!--    </if>-->
<!--    <if test="baseParam.documentId != null and baseParam.documentId.length() > 0">-->
<!--      and doc.document_id = #{baseParam.documentId}-->
<!--    </if>-->
<!--    <if test="baseParam.issCountry != null and baseParam.issCountry.length() > 0">-->
<!--      and doc.iss_country = #{baseParam.issCountry}-->
<!--    </if>-->
<!--    <if test="baseParam.agreementId != null and baseParam.agreementId.length() > 0">-->
<!--      and pkg.agreement_id = #{baseParam.agreementId}-->
<!--    </if>-->
<!--  </select>-->
</mapper>
