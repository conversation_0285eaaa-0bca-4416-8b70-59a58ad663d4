<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg">

    <sql id="Base_Column_List">
        <trim suffixOverrides=",">
            DEP_BASE_ACCT_NO,
            EX_DISTANT_REG_STATUS,
            SEQ_NO,
            TRAN_DATE,
            DELIVERY_METHOD,
            BUSI_NO,
            FORE_CCY,
            DEP_CCY,
            REMARKS,
            BASE_ACCT_NO,
            FORE_BASE_ACCT_NO,
            INTERNAL_KEY,
            USER_ID,
            BRANCH,
            RESTRAINT_SEQ_NO,
            DEAL_REC_ACCT,
            CLIENT_NO,
            LOSS_CNY_AMT,
            LOSS_FORE_AMT,
            LOSS_AMT,
            OFFER_LETTER,
            TEAM_BANK_RATE,
            FORE_AMT,
            DEP_AMT,
            INCOME_AMT,
            FIX_DEAL_DATE,
            CNY_BASE_ACCT_NO,
            CNY_AMT,
            TEAM_BANK_NAME,
            CHANNEL_SEQ_NO,
            CHANGE_DEAL_DATE,
            CHANGE_DEAL_DATE_END,
            THEN_WARING,
            DEFER_DEAL_TYPE,
            DEFER_TRAN_AMT,
            DEFER_BUSI_NO,
            DEFER_CCY,
            DEFER_FIX_DATE,
            DEFER_RATE,
            DEFER_CHANGE_DATE,
            DEFER_CHANGE_DATE_END,
            DEDUCT_AMT,
            FAR_RATE,
            OPTIONS,
            WARING,
            FORE_INTERNAL_KEY,
            DEP_INTERNAL_KEY,
            CNY_INTERNAL_KEY,
            OCC_AMT,
            OCC_CCY,
            RECORD_DATE_CHANNEL_SEQ_NO,
            EX_DEAL_TYPE,
            TO_BANK_FLAG,
            REMAIN_EX_AMT,
            REMAIN_EX_AMT_CNY,
            EX_TRAN_DATE,
            PRE_TRAN_DATE,
            BEFORE_EX_RATE,
            PRE_AMT,
            PRE_OFFER,
            EX_BRANCH,
            EX_TRAN_AMT,
            NEAR_RATE,
            EX_USER_ID,
            TO_CLEAR_ACCT_NO_CNY,
            TO_CLEAR_ACCT_NO_FORE,
            CLEAR_ACCT_NO,
            TO_CLIENT_FLAG,
            EX_TYPE,
            CH_CLIENT_NAME,
            EN_CLIENT_NAME,
            WARING_FLAG,
            THEN_WARING_FLAG,
            LAST_BAIL_AMOUNT,
        </trim>
    </sql>

    <select id="getExchangeDistantReg" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg">
        select <include refid="Base_Column"/>
        from RB_EXCHANGE_DISTANT_REG
        <where>
            EX_DISTANT_REG_STATUS in ('0','1','2')
            <if test="recordDateChannelSeqNo != null and  recordDateChannelSeqNo != '' ">
                AND RECORD_DATE_CHANNEL_SEQ_NO = #{recordDateChannelSeqNo}
            </if>
            <if test="depBaseAcctNo != null and  depBaseAcctNo != '' ">
                AND DEP_BASE_ACCT_NO = #{depBaseAcctNo}
            </if>
            <if test="cnyInternalKey != null ">
                AND CNY_INTERNAL_KEY = #{cnyInternalKey}
            </if>
            <if test="deliveryMethod != null and  deliveryMethod != '' ">
                AND DELIVERY_METHOD = #{deliveryMethod}
            </if>
            <if test="busiNo != null and  busiNo != '' ">
                AND BUSI_NO = #{busiNo}
            </if>
            <if test="foreCcy != null and  foreCcy != '' ">
                AND FORE_CCY = #{foreCcy}
            </if>
            <if test="depCcy != null and  depCcy != '' ">
                AND DEP_CCY = #{depCcy}
            </if>
            <if test="remarks != null and  remarks != '' ">
                AND REMARKS = #{remarks}
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="chClientName != null and  chClientName != '' ">
                AND CH_CLIENT_NAME = #{chClientName}
            </if>
            <if test="foreBaseAcctNo != null and  foreBaseAcctNo != '' ">
                AND FORE_BASE_ACCT_NO = #{foreBaseAcctNo}
            </if>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey}
            </if>
            <if test="depInternalKey != null ">
                AND DEP_INTERNAL_KEY = #{depInternalKey}
            </if>
            <if test="exType != null and  exType != '' ">
                AND EX_TYPE = #{exType}
            </if>
            <if test="dealRecAcct != null and  dealRecAcct != '' ">
                AND DEAL_REC_ACCT = #{dealRecAcct}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="occCcy != null and  occCcy != '' ">
                AND OCC_CCY = #{occCcy}
            </if>
            <if test="foreInternalKey != null ">
                AND FORE_INTERNAL_KEY = #{foreInternalKey}
            </if>
            <if test="offerLetter != null ">
                AND OFFER_LETTER = #{offerLetter}
            </if>
            <if test="teamBankRate != null ">
                AND TEAM_BANK_RATE = #{teamBankRate}
            </if>
            <if test="changeDealDate != null ">
                AND CHANGE_DEAL_DATE = #{changeDealDate}
            </if>
            <if test="enClientName != null and  enClientName != '' ">
                AND EN_CLIENT_NAME = #{enClientName}
            </if>
            <if test="fixDealDate != null ">
                AND FIX_DEAL_DATE = #{fixDealDate}
            </if>
            <if test="cnyBaseAcctNo != null and  cnyBaseAcctNo != '' ">
                AND CNY_BASE_ACCT_NO = #{cnyBaseAcctNo}
            </if>
            <if test="teamBankName != null and  teamBankName != '' ">
                AND TEAM_BANK_NAME = #{teamBankName}
            </if>
            <if test="lossForeAmt != null and  lossForeAmt != '' ">
                AND LOSS_FORE_AMT = #{lossForeAmt}
            </if>
            <if test="lossCnyAmt != null and  lossCnyAmt != '' ">
                AND LOSS_CNY_AMT = #{lossCnyAmt}
            </if>
            <if test="waringFlag != null and  waringFlag != '' ">
                AND ANDWARING_FLAG = #{waringFlag}
            </if>
            <if test="thenWaringFlag != null and  thenWaringFlag != '' ">
                AND THEN_WARING_FLAG = #{thenWaringFlag}
            </if>
            <if test="restraintSeqNo != null and  restraintSeqNo != '' ">
                AND RESTRAINT_SEQ_NO = #{restraintSeqNo}
            </if>
        </where>
    </select>

    <select id="selectRegByBranch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg">
        select <include refid="Base_Column"/>
        from RB_EXCHANGE_DISTANT_REG
        <where> 1=1
            <if test='isAllExchange != null and isAllExchange != "" and isAllExchange == "Y"'>
                AND REMAIN_EX_AMT = 0
            </if>
            <if test='isAllExchange != null and isAllExchange != "" and isAllExchange == "N"'>
                AND REMAIN_EX_AMT != 0
            </if>
            <if test='isNotDelete != null and isNotDelete != "" and isNotDelete == "Y"'>
                AND EX_DISTANT_REG_STATUS != 1
            </if>
            <if test="exDistantRegStatus != null and  exDistantRegStatus != '' ">
                AND EX_DISTANT_REG_STATUS = #{exDistantRegStatus}
            </if>
            <if test="exType != null and  exType != '' ">
                AND EX_TYPE = #{exType}
            </if>
            <if test="busiNo != null and  busiNo != '' ">
                AND BUSI_NO = #{busiNo}
            </if>
            <if test="chClientName != null and  chClientName != '' ">
                AND CH_CLIENT_NAME = #{chClientName}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="branch != null and  branch != '' ">
                AND BRANCH = #{branch}
            </if>
            <if test="deliveryMethod != null and  deliveryMethod != '' ">
                AND DELIVERY_METHOD = #{deliveryMethod}
            </if>
            <if test="exDealType != null and exDealType != '' ">
                AND EX_DEAL_TYPE = #{exDealType}
            </if>
            <if test="startDate != null ">
                AND TRAN_DATE<![CDATA[  >=  ]]> #{startDate}
            </if>
            <if test="endDate != null ">
                AND TRAN_DATE <![CDATA[  <=  ]]>  #{endDate}
            </if>
            <if test="dealRecAcct != null and  dealRecAcct != '' ">
                AND DEAL_REC_ACCT = #{dealRecAcct}
            </if>
            <if test="clientNo != null and clientNo != '' ">
                AND DEAL_REC_ACCT = '1'
            </if>
        </where>
        order by BUSI_NO
    </select>

    <update id="updateBySeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg">
        update RB_EXCHANGE_DISTANT_REG
        set EX_DISTANT_REG_STATUS = '1',OPTIONS = 'D',LAST_CHANGE_DATE = #{lastChangeDate}
        where SEQ_NO = #{seqNo} and CLIENT_NO=#{clientNo}
    </update>

    <select id="selectAgainest" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg">
        select <include refid="Base_Column"/>
        from RB_EXCHANGE_DISTANT_REG
        <where> 1=1
            <if test='isAllExchange != null and isAllExchange != "" and isAllExchange == "Y"'>
                AND REMAIN_EX_AMT = 0
            </if>
            <if test='isAllExchange != null and isAllExchange != "" and isAllExchange == "N"'>
                AND REMAIN_EX_AMT != 0
            </if>
            <if test='isNotDelete != null and isNotDelete != "" and isNotDelete == "Y"'>
                AND EX_DISTANT_REG_STATUS != 1
            </if>
            <if test="exType != null and exType != '' ">
                AND EX_TYPE = #{exType}
            </if>
            <if test="busiNo != null and busiNo != '' ">
                AND BUSI_NO = #{busiNo}
            </if>
            <if test="chClientName != null and chClientName != '' ">
                AND CH_CLIENT_NAME = #{chClientName}
            </if>
            <if test="enClientName != null and enClientName != '' ">
                AND EN_CLIENT_NAME = #{enClientName}
            </if>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="foreCcy != null and foreCcy != ''">
                AND FORE_CCY = #{foreCcy}
            </if>
            <if test="regTranDate != null ">
                AND TRAN_DATE = #{regTranDate}
            </if>
        </where>
    </select>

    <select id="selectDistantRegAll" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg">
        select <include refid="Base_Column_List"/>
        from RB_EXCHANGE_DISTANT_REG
        <where>
            <trim suffixOverrides="AND">
                <if test="busiNo != null and  busiNo != '' ">
                    BUSI_NO = #{busiNo}  AND
                </if>
                <if test="dealRecAcct != null and  dealRecAcct != '' ">
                    DEAL_REC_ACCT = #{dealRecAcct}  AND
                </if>
            </trim>
        </where>
    </select>

    <select id="selectExRegByBranch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg">
        select <include refid="Base_Column"/>
        from RB_EXCHANGE_DISTANT_REG
        <where>
            <if test='isAllExchange != null and isAllExchange != "" and isAllExchange == "Y"'>
                AND REMAIN_EX_AMT = 0
            </if>
            <if test='isAllExchange != null and isAllExchange != "" and isAllExchange == "N"'>
                AND REMAIN_EX_AMT != 0
            </if>
            <if test='isNotDelete != null and isNotDelete != "" and isNotDelete == "Y"'>
                AND EX_DISTANT_REG_STATUS != 1
            </if>
            <if test="options != null and options != '' ">
                AND OPTIONS = #{options}
            </if>
            <if test="exDistantRegStatus != null and exDistantRegStatus != '' ">
                AND EX_DISTANT_REG_STATUS = #{exDistantRegStatus}
            </if>
            <if test="exType != null and  exType != '' ">
                AND EX_TYPE = #{exType}
            </if>
            <if test="busiNo != null and  busiNo != '' ">
                AND BUSI_NO = #{busiNo}
            </if>
            <if test="chClientName != null and  chClientName != '' ">
                AND CH_CLIENT_NAME = #{chClientName}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="branch != null and  branch != '' ">
                AND BRANCH = #{branch}
            </if>
            <if test="startDate != null ">
                AND EX_TRAN_DATE<![CDATA[  >=  ]]> #{startDate}
            </if>
            <if test="endDate != null ">
                AND EX_TRAN_DATE <![CDATA[  <=  ]]>  #{endDate}
            </if>
            <if test="deliveryMethod != null and  deliveryMethod != '' ">
                AND DELIVERY_METHOD = #{deliveryMethod}
            </if>
            <if test="exDealType != null and exDealType != '' ">
                AND EX_DEAL_TYPE = #{exDealType}
            </if>
            <if test="dealRecAcct != null and  dealRecAcct != '' ">
                AND DEAL_REC_ACCT = #{dealRecAcct}
            </if>
            <if test="clientNo != null or  chClientName != null ">
                AND DEAL_REC_ACCT = '1'
            </if>
        </where>
        order by BUSI_NO
    </select>

    <update id="updateByDistantBusiNo"  parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg" >
        UPDATE <include refid="Table_Name" />
        <set>
            <if test="lastChangeDate != null ">
                LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE},
            </if>
            <if test="depBaseAcctNo != null and depBaseAcctNo != '' ">
                DEP_BASE_ACCT_NO = #{depBaseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="exDistantRegStatus != null and exDistantRegStatus != '' ">
                EX_DISTANT_REG_STATUS = #{exDistantRegStatus,jdbcType=VARCHAR},
            </if>
            <if test="seqNo != null and seqNo != '' ">
                SEQ_NO = #{seqNo,jdbcType=VARCHAR},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="deliveryMethod != null and deliveryMethod != '' ">
                DELIVERY_METHOD = #{deliveryMethod,jdbcType=VARCHAR},
            </if>
            <if test="busiNo != null and busiNo != '' ">
                BUSI_NO = #{busiNo,jdbcType=VARCHAR},
            </if>
            <if test="foreCcy != null and foreCcy != '' ">
                FORE_CCY = #{foreCcy,jdbcType=VARCHAR},
            </if>
            <if test="depCcy != null and depCcy != '' ">
                DEP_CCY = #{depCcy,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != '' ">
                REMARKS = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="foreBaseAcctNo != null and foreBaseAcctNo != '' ">
                FORE_BASE_ACCT_NO = #{foreBaseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="internalKey != null ">
                INTERNAL_KEY = #{internalKey,jdbcType=INTEGER},
            </if>
            <if test="userId != null and userId != '' ">
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="branch != null and branch != '' ">
                BRANCH = #{branch,jdbcType=VARCHAR},
            </if>
            <if test="restraintSeqNo != null and restraintSeqNo != '' ">
                RESTRAINT_SEQ_NO = #{restraintSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="dealRecAcct != null and dealRecAcct != '' ">
                DEAL_REC_ACCT = #{dealRecAcct,jdbcType=VARCHAR},
            </if>
            <if test="lossCnyAmt != null ">
                LOSS_CNY_AMT = #{lossCnyAmt,jdbcType=DECIMAL},
            </if>
            <if test="lossForeAmt != null ">
                LOSS_FORE_AMT = #{lossForeAmt,jdbcType=DECIMAL},
            </if>
            <if test="lossAmt != null ">
                LOSS_AMT = #{lossAmt,jdbcType=DECIMAL},
            </if>
            <if test="offerLetter != null ">
                OFFER_LETTER = #{offerLetter,jdbcType=DECIMAL},
            </if>
            <if test="teamBankRate != null ">
                TEAM_BANK_RATE = #{teamBankRate,jdbcType=DECIMAL},
            </if>
            <if test="foreAmt != null ">
                FORE_AMT = #{foreAmt,jdbcType=DECIMAL},
            </if>
            <if test="depAmt != null ">
                DEP_AMT = #{depAmt,jdbcType=DECIMAL},
            </if>
            <if test="incomeAmt != null ">
                INCOME_AMT = #{incomeAmt,jdbcType=DECIMAL},
            </if>
            <if test="fixDealDate != null ">
                FIX_DEAL_DATE = #{fixDealDate,jdbcType=DATE},
            </if>
            <if test="cnyBaseAcctNo != null and cnyBaseAcctNo != '' ">
                CNY_BASE_ACCT_NO = #{cnyBaseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="cnyAmt != null ">
                CNY_AMT = #{cnyAmt,jdbcType=DECIMAL},
            </if>
            <if test="teamBankName != null and teamBankName != '' ">
                TEAM_BANK_NAME = #{teamBankName,jdbcType=VARCHAR},
            </if>
            <if test="channelSeqNo != null and channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="changeDealDate != null ">
                CHANGE_DEAL_DATE = #{changeDealDate,jdbcType=DATE},
            </if>
            <if test="changeDealDateEnd != null ">
                CHANGE_DEAL_DATE_END = #{changeDealDateEnd,jdbcType=DATE},
            </if>
            <if test="thenWaring != null and thenWaring != '' ">
                THEN_WARING = #{thenWaring,jdbcType=VARCHAR},
            </if>
            <if test="deferDealType != null and deferDealType != '' ">
                DEFER_DEAL_TYPE = #{deferDealType,jdbcType=VARCHAR},
            </if>
            <if test="deferTranAmt != null ">
                DEFER_TRAN_AMT = #{deferTranAmt,jdbcType=DECIMAL},
            </if>
            <if test="deferBusiNo != null and deferBusiNo != '' ">
                DEFER_BUSI_NO = #{deferBusiNo,jdbcType=VARCHAR},
            </if>
            <if test="deferCcy != null and deferCcy != '' ">
                DEFER_CCY = #{deferCcy,jdbcType=VARCHAR},
            </if>
            <if test="deferFixDate != null ">
                DEFER_FIX_DATE = #{deferFixDate,jdbcType=DATE},
            </if>
            <if test="deferRate != null ">
                DEFER_RATE = #{deferRate,jdbcType=DECIMAL},
            </if>
            <if test="deferChangeDate != null ">
                DEFER_CHANGE_DATE = #{deferChangeDate,jdbcType=DATE},
            </if>
            <if test="deferChangeDateEnd != null ">
                DEFER_CHANGE_DATE_END = #{deferChangeDateEnd,jdbcType=DATE},
            </if>
            <if test="deductAmt != null ">
                DEDUCT_AMT = #{deductAmt,jdbcType=DECIMAL},
            </if>
            <if test="farRate != null ">
                FAR_RATE = #{farRate,jdbcType=DECIMAL},
            </if>
            <if test="options != null and options != '' ">
                OPTIONS = #{options,jdbcType=VARCHAR},
            </if>
            <if test="waring != null and waring != '' ">
                WARING = #{waring,jdbcType=VARCHAR},
            </if>
            <if test="foreInternalKey != null ">
                FORE_INTERNAL_KEY = #{foreInternalKey,jdbcType=INTEGER},
            </if>
            <if test="depInternalKey != null ">
                DEP_INTERNAL_KEY = #{depInternalKey,jdbcType=INTEGER},
            </if>
            <if test="cnyInternalKey != null ">
                CNY_INTERNAL_KEY = #{cnyInternalKey,jdbcType=INTEGER},
            </if>
            <if test="occAmt != null ">
                OCC_AMT = #{occAmt,jdbcType=DECIMAL},
            </if>
            <if test="occCcy != null and occCcy != '' ">
                OCC_CCY = #{occCcy,jdbcType=VARCHAR},
            </if>
            <if test="recordDateChannelSeqNo != null and recordDateChannelSeqNo != '' ">
                RECORD_DATE_CHANNEL_SEQ_NO = #{recordDateChannelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="exDealType != null and exDealType != '' ">
                EX_DEAL_TYPE = #{exDealType,jdbcType=VARCHAR},
            </if>
            <if test="toBankFlag != null and toBankFlag != '' ">
                TO_BANK_FLAG = #{toBankFlag,jdbcType=VARCHAR},
            </if>
            <if test="remainExAmt != null ">
                REMAIN_EX_AMT = #{remainExAmt,jdbcType=DECIMAL},
            </if>
            <if test="remainExAmtCny != null ">
                REMAIN_EX_AMT_CNY = #{remainExAmtCny,jdbcType=DECIMAL},
            </if>
            <if test="exTranDate != null ">
                EX_TRAN_DATE = #{exTranDate,jdbcType=DATE},
            </if>
            <if test="preTranDate != null ">
                PRE_TRAN_DATE = #{preTranDate,jdbcType=DATE},
            </if>
            <if test="beforeExRate != null ">
                BEFORE_EX_RATE = #{beforeExRate,jdbcType=DECIMAL},
            </if>
            <if test="preAmt != null ">
                PRE_AMT = #{preAmt,jdbcType=DECIMAL},
            </if>
            <if test="preOffer != null ">
                PRE_OFFER = #{preOffer,jdbcType=DECIMAL},
            </if>
            <if test="exBranch != null and exBranch != '' ">
                EX_BRANCH = #{exBranch,jdbcType=VARCHAR},
            </if>
            <if test="exTranAmt != null ">
                EX_TRAN_AMT = #{exTranAmt,jdbcType=DECIMAL},
            </if>
            <if test="nearRate != null ">
                NEAR_RATE = #{nearRate,jdbcType=DECIMAL},
            </if>
            <if test="exUserId != null and exUserId != '' ">
                EX_USER_ID = #{exUserId,jdbcType=VARCHAR},
            </if>
            <if test="toClearAcctNoCny != null and toClearAcctNoCny != '' ">
                TO_CLEAR_ACCT_NO_CNY = #{toClearAcctNoCny,jdbcType=VARCHAR},
            </if>
            <if test="toClearAcctNoFore != null and toClearAcctNoFore != '' ">
                TO_CLEAR_ACCT_NO_FORE = #{toClearAcctNoFore,jdbcType=VARCHAR},
            </if>
            <if test="clearAcctNo != null and clearAcctNo != '' ">
                CLEAR_ACCT_NO = #{clearAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="toClientFlag != null and toClientFlag != '' ">
                TO_CLIENT_FLAG = #{toClientFlag,jdbcType=VARCHAR},
            </if>
            <if test="exType != null and exType != '' ">
                EX_TYPE = #{exType,jdbcType=VARCHAR},
            </if>
            <if test="chClientName != null and chClientName != '' ">
                CH_CLIENT_NAME = #{chClientName,jdbcType=VARCHAR},
            </if>
            <if test="enClientName != null and enClientName != '' ">
                EN_CLIENT_NAME = #{enClientName,jdbcType=VARCHAR},
            </if>
            <if test="waringFlag != null and waringFlag != '' ">
                WARING_FLAG = #{waringFlag,jdbcType=VARCHAR},
            </if>
            <if test="thenWaringFlag != null and thenWaringFlag != '' ">
                THEN_WARING_FLAG = #{thenWaringFlag,jdbcType=VARCHAR},
            </if>
            <if test="lastBailAmount != null ">
                LAST_BAIL_AMOUNT = #{lastBailAmount,jdbcType=DECIMAL},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="busiNo != null and  busiNo != '' ">
                    BUSI_NO = #{busiNo}  AND
                </if>
                <if test="dealRecAcct != null and  dealRecAcct != '' ">
                    DEAL_REC_ACCT = #{dealRecAcct}  AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
            </trim>
        </where>
    </update>

    <select id="selectDepAmtSum" parameterType="java.util.Map"
            resultType="java.math.BigDecimal">
        select SUM(DEP_AMT)
        from RB_EXCHANGE_DISTANT_REG
        <where>
            <trim suffixOverrides="AND">
                DEAL_REC_ACCT = '0' AND
                <if test="busiNo != null and  busiNo != '' ">
                    BUSI_NO = #{busiNo}  AND
                </if>
            </trim>
        </where>
    </select>

</mapper>