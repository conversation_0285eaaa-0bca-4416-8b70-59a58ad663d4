<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageIntMatrix">

    <select id="getRbDcStageIntMatrixListByDayNum"  parameterType="java.util.Map" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        WHERE STAGE_CODE = #{stageCode,jdbcType=VARCHAR}
          AND DAY_NUM <![CDATA[ <= ]]>  #{dayNum,jdbcType=INTEGER}
    </select>

    <select id="getRbDcStageIntMatrixList"  parameterType="java.util.Map" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        WHERE STAGE_CODE = #{stageCode,jdbcType=VARCHAR}
    </select>

    <select id="getRbDcStageIntMatrixListByStageCode"  parameterType="java.util.Map" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        WHERE STAGE_CODE = #{stageCode,jdbcType=VARCHAR}
        order by PERIOD_FREQ
    </select>

</mapper>