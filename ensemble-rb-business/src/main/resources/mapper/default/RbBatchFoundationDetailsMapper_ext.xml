<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchFoundationDetails">


	<select id="getBatchDetailsRs" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchFoundationDetails" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchFoundationDetails">
		SELECT
		<include refid="Base_Column" />
		FROM RB_BATCH_FOUNDATION_DETAILS
		WHERE BATCH_NO = #{batchNo,jdbcType=VARCHAR}
		 and  FROZEN_SEQ_NO IS NOT NULL
		 and SEQ_NO = #{seqNo,jdbcType=VARCHAR}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
