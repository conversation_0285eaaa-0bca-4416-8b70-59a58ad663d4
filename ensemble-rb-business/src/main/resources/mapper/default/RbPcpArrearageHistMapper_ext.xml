<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpArrearageHist">

  <select id="selectEodPcpArrearageHist" parameterType="java.util.Map"
          resultType="java.util.Map">
    <if test="_databaseId == 'oracle'">
				SELECT   MIN (INTERNAL_KEY) START_KEY, MAX (INTERNAL_KEY) END_KEY, count(1) ROW_COUNT
				FROM   (  SELECT DISTINCT INTERNAL_KEY
                  FROM   RB_PCP_ARREARAGE_HIST
                  WHERE OSD_STATUS = 'C'

                  <if test="company != null and company != '' ">
                    AND COMPANY = #{company}
                  </if>

                  ORDER BY   INTERNAL_KEY)
				GROUP BY   TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
    <if test="_databaseId == 'mysql'">

			SELECT
			MIN(INTERNAL_KEY) START_KEY,
			MAX(INTERNAL_KEY) END_KEY
			FROM
			(
              SELECT
                DISTINCT INTERNAL_KEY INTERNAL_KEY,
                @rownum :=@rownum + 1 AS rownum
                FROM
                RB_PCP_ARREARAGE_HIST mm,
                (SELECT @rownum := -1) t
                 WHERE OSD_STATUS = 'C'


                <if test="company != null and company != '' ">
                  AND COMPANY = #{company}
                </if>

                ORDER BY
                mm.INTERNAL_KEY
			) tt
			GROUP BY
			FLOOR(
			tt.rownum /#{maxPerCount} )

    </if>
  </select>
  <select id="queryPcpArrearage" parameterType="java.util.Map" resultType="java.lang.String">
    SELECT DISTINCT INTERNAL_KEY
    FROM   RB_PCP_ARREARAGE_HIST
    WHERE OSD_STATUS = 'C'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND INTERNAL_KEY between #{START_KEY} and #{END_KEY}
    ORDER BY  INTERNAL_KEY
  </select>
  <select id="queryPcpArrearageByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpArrearageHist">
    select
    <include refid="Base_Column"/>
    FROM RB_PCP_ARREARAGE_HIST
    WHERE OSD_STATUS = 'C'
    AND INTERNAL_KEY BETWEEN #{START_KEY} and #{END_KEY}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY INTERNAL_KEY,OSD_DATE ASC
  </select>
  <select id="queryPcpArrearageBystatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpArrearageHist">
    select
    <include refid="Base_Column"/>
    FROM RB_PCP_ARREARAGE_HIST
    WHERE OSD_STATUS = 'C'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY INTERNAL_KEY,OSD_DATE ASC
  </select>
  <select id="getMbPcpArrearageHistByDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpArrearageHist">
    select
    <include refid="Base_Column"/>
    FROM RB_PCP_ARREARAGE_HIST
    WHERE OSD_STATUS = 'C'
    AND INTERNAL_KEY = #{internalKey}
    <if test="startDate !=  null">
      AND OSD_DATE between #{startDate} AND #{endDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY OSD_STATUS
  </select>
</mapper>
