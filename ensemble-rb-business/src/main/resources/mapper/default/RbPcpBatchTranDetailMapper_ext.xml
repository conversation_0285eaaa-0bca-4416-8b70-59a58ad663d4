<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpBatchTranDetail">

  <select id="selectByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpBatchTranDetail">
    SELECT <include refid="Base_Column"/>
    FROM RB_PCP_BATCH_TRAN_DETAIL
    WHERE BASE_ACCT_NO = #{baseAcctNo}
    <if test="startDate != null and endDate != null">
      AND TRAN_DATE BETWEEN #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
    </if>
    <if test="upDownType != null and upDownType != '' ">
      AND UP_DOWN_TYPE = #{upDownType}
    </if>
    <if  test="startAmt != null and endAmt != null">
      <![CDATA[ AND
      TRAN_AMT > #{startAmt,jdbcType=DECIMAL} AND
      TRAN_AMT < #{endAmt,jdbcType=DECIMAL}
      ]]>
    </if>
  </select>
  <select id="selectByBatchNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpBatchTranDetail">
    SELECT <include refid="Base_Column"/>
    FROM RB_PCP_BATCH_TRAN_DETAIL
    WHERE BATCH_NO = #{batchNo}
    <if test="startDate != null and endDate != null">
      AND TRAN_DATE BETWEEN #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
    </if>
    <if test="upDownType != null and upDownType != '' ">
      AND UP_DOWN_TYPE = #{upDownType}
    </if>
    <if  test="startAmt != null and endAmt != null">
      <![CDATA[ AND
      TRAN_AMT > #{startAmt,jdbcType=DECIMAL} AND
      TRAN_AMT < #{endAmt,jdbcType=DECIMAL}
      ]]>
    </if>
  </select>
</mapper>