<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlBatchTranDetail">
	<select id="getRepeatProdData" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlBatchTranDetail">
		select <include refid="Base_Column"/>
		from RB_GL_BATCH_TRAN_DETAIL
		where BATCH_NO = #{batchNo}
		<if test="detailBranch != null and detailBranch.length() > 0">
			AND ACCT_BRANCH = #{detailBranch}
		</if>
		<if test="detailProdType != null and detailProdType.length() > 0">
			AND PROD_TYPE = #{detailProdType}
		</if>
		<if test="detailBranch != null and detailBranch.length() > 0">
			AND ACCT_CCY = #{detailCcy}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<update id="upBatchByLinkValue" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails">
		update RB_GL_BATCH_TRAN_DETAIL
		set BATCH_STATUS = 'S'
		where link_value = #{linkValue}
	</update>
	<select id="getBatchByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlBatchTranDetail">
		select <include refid="Base_Column"/>
		from RB_GL_BATCH_TRAN_DETAIL
		where BATCH_NO = #{batchNo}
		<if test="batchStatus != null and batchStatus != ''">
			AND BATCH_STATUS = #{batchStatus}
		</if>
		<if test="branchList != null and branchList.size() > 0">
			AND ACCT_BRANCH IN
			<foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
				#{branch}
			</foreach>
		</if>
	</select>
</mapper>
