<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMsa">

    <select id="selectByCondition" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMsa">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_MSA
        <where>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND  BASE_ACCT_NO =  #{baseAcctNo ,jdbcType=VARCHAR}
            </if>
            <if test="acctSeqNo != null and  acctSeqNo != '' ">
                AND  ACCT_SEQ_NO =  #{acctSeqNo ,jdbcType=VARCHAR}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND  PROD_TYPE =  #{prodType ,jdbcType=VARCHAR}
            </if>
            <if test="acctCcy != null and  acctCcy != '' ">
                AND  ACCT_CCY =  #{acctCcy ,jdbcType=VARCHAR}
            </if>
            <if test="agreementStatus != null and  agreementStatus != '' ">
                AND  AGREEMENT_STATUS =  #{agreementStatus ,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
