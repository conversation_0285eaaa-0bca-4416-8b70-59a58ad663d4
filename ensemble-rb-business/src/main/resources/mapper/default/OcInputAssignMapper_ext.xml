<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">
    <!-- Created by admin on 2017/05/09 11:44:02. -->
    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">
    select *
    from OC_INPUT_ASSIGN
    where ASSIGN_SEQ_NO = #{assignSeqNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </select>

    <select id="getAssignInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">
        select *
        from OC_INPUT_ASSIGN
        <where>
        <if test="assignSeqNo != null">
            and ASSIGN_SEQ_NO = #{assignSeqNo}
        </if>
        <if test="changeNo != null">
            and CHANGE_NO = #{changeNo}
        </if>
        <if test="changeDate != null">
            and CHANGE_DATE = #{changeDate}
        </if>
        <if test="changeSession != null">
            and CHANGE_SESSION = #{changeSession}
        </if>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>

    <select id="selectInputAssign" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">
        select *
        from OC_INPUT_ASSIGN
        where change_no = #{changeNo}
        and CHANGE_REGION=#{changeRegion}
        and CHANGE_DATE=#{changeDate}
        and CHANGE_SESSION=#{changeSession}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <!--<delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">-->
    <!--delete from OC_INPUT_ASSIGN-->
    <!--where ASSIGN_SEQ_NO = #{assignSeqNo}-->
  <!--</delete>-->

    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">
        update OC_INPUT_ASSIGN
        <set>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="userId != null">
                USER_ID = #{userId},
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="changeNo != null">
                CHANGE_NO = #{changeNo},
            </if>
            <if test="changeRegion != null">
                CHANGE_REGION = #{changeRegion},
            </if>
            <if test="changeDate != null">
                CHANGE_DATE = #{changeDate},
            </if>
            <if test="changeSession != null">
                CHANGE_SESSION = #{changeSession},
            </if>
            <if test="idrTotalNum != null">
                IDR_TOTAL_NUM = #{idrTotalNum},
            </if>
            <if test="idrTotalAmt != null">
                IDR_TOTAL_AMT = #{idrTotalAmt},
            </if>
            <if test="icrTotalNum != null">
                ICR_TOTAL_NUM = #{icrTotalNum},
            </if>
            <if test="icrTotalAmt != null">
                ICR_TOTAL_AMT = #{icrTotalAmt},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where ASSIGN_SEQ_NO = #{assignSeqNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>


    <!--<insert id="insertOcInputAssign" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">-->
        <!--insert into OC_INPUT_ASSIGN-->
        <!--<trim prefix="(" suffix=")" suffixOverrides=",">-->
            <!--<if test="assignSeqNo != null">-->
                <!--ASSIGN_SEQ_NO,-->
            <!--</if>-->
            <!--<if test="tranDate != null">-->
                <!--TRAN_DATE,-->
            <!--</if>-->
            <!--<if test="userId != null">-->
                <!--USER_ID,-->
            <!--</if>-->
            <!--<if test="tranBranch != null">-->
                <!--TRAN_BRANCH,-->
            <!--</if>-->
            <!--<if test="changeNo != null">-->
                <!--CHANGE_NO,-->
            <!--</if>-->
            <!--<if test="changeRegion != null">-->
                <!--CHANGE_REGION,-->
            <!--</if>-->
            <!--<if test="changeDate != null">-->
                <!--CHANGE_DATE,-->
            <!--</if>-->
            <!--<if test="changeSession != null">-->
                <!--CHANGE_SESSION,-->
            <!--</if>-->
            <!--<if test="idrTotalNum != null">-->
                <!--IDR_TOTAL_NUM,-->
            <!--</if>-->
            <!--<if test="idrTotalAmt != null">-->
                <!--IDR_TOTAL_AMT,-->
            <!--</if>-->
            <!--<if test="icrTotalNum != null">-->
                <!--ICR_TOTAL_NUM,-->
            <!--</if>-->
            <!--<if test="icrTotalAmt != null">-->
                <!--ICR_TOTAL_AMT,-->
            <!--</if>-->
            <!--<if test="tranTimestamp != null">-->
                <!--TRAN_TIMESTAMP,-->
            <!--</if>-->
        <!--</trim>-->
        <!--<trim prefix="values (" suffix=")" suffixOverrides=",">-->
            <!--<if test="assignSeqNo != null">-->
                <!--#{assignSeqNo},-->
            <!--</if>-->
            <!--<if test="tranDate != null">-->
                <!--#{tranDate},-->
            <!--</if>-->
            <!--<if test="userId != null">-->
                <!--#{userId},-->
            <!--</if>-->
            <!--<if test="tranBranch != null">-->
                <!--#{tranBranch},-->
            <!--</if>-->
            <!--<if test="changeNo != null">-->
                <!--#{changeNo},-->
            <!--</if>-->
            <!--<if test="changeRegion != null">-->
                <!--#{changeRegion},-->
            <!--</if>-->
            <!--<if test="changeDate != null">-->
                <!--#{changeDate},-->
            <!--</if>-->
            <!--<if test="changeSession != null">-->
                <!--#{changeSession},-->
            <!--</if>-->
            <!--<if test="idrTotalNum != null">-->
                <!--#{idrTotalNum},-->
            <!--</if>-->
            <!--<if test="idrTotalAmt != null">-->
                <!--#{idrTotalAmt},-->
            <!--</if>-->
            <!--<if test="icrTotalNum != null">-->
                <!--#{icrTotalNum},-->
            <!--</if>-->
            <!--<if test="icrTotalAmt != null">-->
                <!--#{icrTotalAmt},-->
            <!--</if>-->
            <!--<if test="tranTimestamp != null">-->
                <!--#{tranTimestamp},-->
            <!--</if>-->
        <!--</trim>-->
    <!--</insert>-->


    <!--  修改-->
    <update id="updOcInputAssign" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">

            update OC_INPUT_ASSIGN
            <set>
                <if test="tranDate != null">
                    TRAN_DATE = #{tranDate},
                </if>
                <if test="userId != null">
                    USER_ID = #{userId},
                </if>
                <if test="tranBranch != null">
                    TRAN_BRANCH = #{tranBranch},
                </if>
                <if test="changeNo != null">
                    CHANGE_NO = #{changeNo},
                </if>
                <if test="changeRegion != null">
                    CHANGE_REGION = #{changeRegion},
                </if>
                <if test="changeDate != null">
                    CHANGE_DATE = #{changeDate},
                </if>
                <if test="changeSession != null">
                    CHANGE_SESSION = #{changeSession},
                </if>
                <if test="idrTotalNum != null">
                    IDR_TOTAL_NUM = #{idrTotalNum},
                </if>
                <if test="idrTotalAmt != null">
                    IDR_TOTAL_AMT = #{idrTotalAmt},
                </if>
                <if test="icrTotalNum != null">
                    ICR_TOTAL_NUM = #{icrTotalNum},
                </if>
                <if test="icrTotalAmt != null">
                    ICR_TOTAL_AMT = #{icrTotalAmt},
                </if>
                <if test="tranTimestamp != null">
                    TRAN_TIMESTAMP = #{tranTimestamp}
                </if>
            </set>
        where
        ASSIGN_SEQ_NO= #{assignSeqNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
</mapper>
