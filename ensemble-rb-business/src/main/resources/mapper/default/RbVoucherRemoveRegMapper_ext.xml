<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherRemoveReg">

	<!--根据条件查询-->
	<select id="selectByCondtion"  resultMap="Base_Result_Map" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherRemoveReg">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_VOUCHER_REMOVE_REG
		<where>
			<trim suffixOverrides="AND">
				<if test="regStatus != null and  regStatus != '' ">
					REG_STATUS = #{regStatus}  AND
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo}  AND
				</if>
				<if test="voucherNo != null and  voucherNo != '' ">
					VOUCHER_NO = #{voucherNo}  AND
				</if>
				<if test="mediumRemoveFlag!= null and  mediumRemoveFlag != '' ">
					MEDIUM_REMOVE_FLAG = #{mediumRemoveFlag}  AND
				</if>
				<if test="prodType != null and  prodType != '' ">
					PROD_TYPE = #{prodType}  AND
				</if>
				<if test="prefix != null and  prefix != '' ">
					PREFIX = #{prefix}  AND
				</if>
				<if test="acctSeqNo != null and  acctSeqNo != '' ">
					ACCT_SEQ_NO = #{acctSeqNo}  AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="tranDate != null ">
					TRAN_DATE = #{tranDate,jdbcType=DATE}  AND
				</if>
				<if test="docType != null and  docType != '' ">
					DOC_TYPE = #{docType}  AND
				</if>
				<if test="internalKey != null ">
					INTERNAL_KEY = #{internalKey}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<update id="updateRemoveFlag" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherRemoveReg" >
		UPDATE
		<include refid="Table_Name" />
		<set>
			<if test="mediumRemoveFlag != null and mediumRemoveFlag != '' ">
				MEDIUM_REMOVE_FLAG = #{mediumRemoveFlag},
			</if>
		</set>
		<where>
			<trim prefix="(" suffix=")" suffixOverrides="AND">
				<if test="regStatus != null and regStatus != '' ">
					REG_STATUS = #{regStatus}
					AND
				</if>
				<if test="baseAcctNo != null and baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo}
					AND
				</if>
				<if test="voucherNo != null and voucherNo != '' ">
					VOUCHER_NO = #{voucherNo}
					AND
				</if>
				<if test="prodType != null and prodType != '' ">
					PROD_TYPE = #{prodType}
					AND
				</if>
				<if test="prefix != null and prefix != '' ">
					PREFIX = #{prefix}
					AND
				</if>
				<if test="acctSeqNo != null and acctSeqNo != '' ">
					ACCT_SEQ_NO = #{acctSeqNo}
					AND
				</if>
				<if test="clientNo != null and clientNo != '' ">
					CLIENT_NO = #{clientNo}
					AND
				</if>
				<if test="tranDate != null ">
					TRAN_DATE = #{tranDate,jdbcType=DATE}
					AND
				</if>
				<if test="docType != null and docType != '' ">
					DOC_TYPE = #{docType}
					AND
				</if>
				<if test="internalKey != null ">
					INTERNAL_KEY = #{internalKey}
					AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

</mapper>
