<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange">
  <!-- Created by <PERSON><PERSON><PERSON><PERSON> on 2017/05/20 14:23:09. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange">
    select *
    from RB_ACCT_CLIENT_CHANGE
    where SEQ_NO = #{seqNo}
      <if test="internalKey != null">
        AND INTERNAL_KEY = #{internalKey}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectNoSuccess" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange">
    select *
    from RB_ACCT_CLIENT_CHANGE
    where SEQ_NO = #{seqNo}
    AND PROD_TYPE != 'S'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange">
    delete from RB_ACCT_CLIENT_CHANGE
    where SEQ_NO = #{seqNo}
      <if test="internalKey != null">
        AND INTERNAL_KEY = #{internalKey}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange">
    update RB_ACCT_CLIENT_CHANGE
    <set>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctCcy != null">
        ACCT_CCY = #{acctCcy},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="oldClientNo != null">
        OLD_CLIENT_NO = #{oldClientNo},
      </if>
      <if test="newClientNo != null">
        NEW_CLIENT_NO = #{newClientNo},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where SEQ_NO = #{seqNo}
        AND INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange">
    insert into RB_ACCT_CLIENT_CHANGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="oldClientNo != null">
        OLD_CLIENT_NO,
      </if>
      <if test="newClientNo != null">
        NEW_CLIENT_NO,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="oldClientNo != null">
        #{oldClientNo},
      </if>
      <if test="newClientNo != null">
        #{newClientNo},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="getRbAcctClientChangeByStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange">
    select *
    from RB_ACCT_CLIENT_CHANGE
    where
    (PROD_TYPE = 'F' or PROD_TYPE='N')
    AND BASE_ACCT_NO=#{baseAcctNo}
  </select>

  <update id="updateClientChangeBySeqNO" parameterType="java.util.Map">
    update RB_ACCT_CLIENT_CHANGE
    <set>
      <if test="prodType != null " >
        prod_Type = #{prodType},
      </if>
      <if test = "internalKey !=null and internalKey != ''">
        internal_Key = #{internalKey},
      </if>
    </set>
    where  seq_no = #{seqNo}
    and client_No = #{clientNo}
    and old_Client_No = #{oldClientNo}
    and new_Client_No = #{newClientNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
