<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.bc.unit.acct.transaction.model.EgProdAcctBal">

    <sql id="Table_Name">
        ENS_EG.EG_PROD_ACCT_BAL
    </sql>

    <sql id="Base_Column">
        <trim suffixOverrides=",">
            BRANCH,
            PROD_TYPE,
            CCY,
            AMT_TYPE,
            BALANCE,
            SOURCE_TYPE,
            SYSTEM_ID,
            RUN_DATE,
            OTH_BRANCH,
            DEAL_FLAG,
            DATA_FLAG,
            BUSI_PROD,
            GL_CODE,
            LAST_CHANGE_DATE,
            PROFIT_CENTER,
            SEND_SYSTEM,
            COMPANY,
            TRAN_TIMESTAMP,
            EVENT_TYPE,
            TRAN_TYPE,
            INDIVIDUAL_FLAG,
            ACCT_STATUS,
            BASE_ACCT_NO,
            AMOUNT_NATURE,
            INT_CALC_BAL,
            INTERNAL_KEY,
            ACCT_SEQ_NO,
            SEQ_NO,
        </trim>
    </sql>

    <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.bc.unit.acct.transaction.model.EgProdAcctBal">
        <result property="branch" column="BRANCH"/>
        <result property="prodType" column="PROD_TYPE"/>
        <result property="ccy" column="CCY"/>
        <result property="amtType" column="AMT_TYPE"/>
        <result property="balance" column="BALANCE"/>
        <result property="sourceType" column="SOURCE_TYPE"/>
        <result property="systemId" column="SYSTEM_ID"/>
        <result property="runDate" column="RUN_DATE"/>
        <result property="othBranch" column="OTH_BRANCH"/>
        <result property="dealFlag" column="DEAL_FLAG"/>
        <result property="dataFlag" column="DATA_FLAG"/>
        <result property="busiProd" column="BUSI_PROD"/>
        <result property="glCode" column="GL_CODE"/>
        <result property="lastChangeDate" column="LAST_CHANGE_DATE"/>
        <result property="profitCenter" column="PROFIT_CENTER"/>
        <result property="sendSystem" column="SEND_SYSTEM"/>
        <result property="company" column="COMPANY"/>
        <result property="tranTimestamp" column="TRAN_TIMESTAMP"/>
        <result property="eventType" column="EVENT_TYPE"/>
        <result property="tranType" column="TRAN_TYPE"/>
        <result property="individualFlag" column="INDIVIDUAL_FLAG"/>
        <result property="acctStatus" column="ACCT_STATUS"/>
        <result property="baseAcctNo" column="BASE_ACCT_NO"/>
        <result property="amountNature" column="AMOUNT_NATURE"/>
        <result property="intCalcBal" column="INT_CALC_BAL"/>
        <result property="internalKey" column="INTERNAL_KEY"/>
        <result property="acctSeqNo" column="ACCT_SEQ_NO"/>
        <result property="seqNo" column="SEQ_NO"/>
    </resultMap>

    <sql id="Base_Where">
        <where>
            <if test="branch != null and  branch != '' ">
                AND BRANCH = #{branch,jdbcType=VARCHAR}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="amtType != null and  amtType != '' ">
                AND AMT_TYPE = #{amtType,jdbcType=VARCHAR}
            </if>
            <if test="balance != null ">
                AND BALANCE = #{balance,jdbcType=DECIMAL}
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                AND SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
            </if>
            <if test="systemId != null and  systemId != '' ">
                AND SYSTEM_ID = #{systemId,jdbcType=VARCHAR}
            </if>
            <if test="runDate != null ">
                AND RUN_DATE = #{runDate,jdbcType=DATE}
            </if>
            <if test="othBranch != null and  othBranch != '' ">
                AND OTH_BRANCH = #{othBranch,jdbcType=VARCHAR}
            </if>
            <if test="dealFlag != null and  dealFlag != '' ">
                AND DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="dataFlag != null and  dataFlag != '' ">
                AND DATA_FLAG = #{dataFlag,jdbcType=VARCHAR}
            </if>
            <if test="busiProd != null and  busiProd != '' ">
                AND BUSI_PROD = #{busiProd,jdbcType=VARCHAR}
            </if>
            <if test="glCode != null and  glCode != '' ">
                AND GL_CODE = #{glCode,jdbcType=VARCHAR}
            </if>
            <if test="lastChangeDate != null ">
                AND LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE}
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                AND PROFIT_CENTER = #{profitCenter,jdbcType=VARCHAR}
            </if>
            <if test="sendSystem != null and  sendSystem != '' ">
                AND SEND_SYSTEM = #{sendSystem,jdbcType=VARCHAR}
            </if>
            <if test="company != null and  company != '' ">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
            <if test="tranTimestamp != null and  tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="eventType != null and  eventType != '' ">
                AND EVENT_TYPE = #{eventType,jdbcType=VARCHAR}
            </if>
            <if test="tranType != null and  tranType != '' ">
                AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
            </if>
            <if test="individualFlag != null and  individualFlag != '' ">
                AND INDIVIDUAL_FLAG = #{individualFlag,jdbcType=VARCHAR}
            </if>
            <if test="acctStatus != null and  acctStatus != '' ">
                AND ACCT_STATUS = #{acctStatus,jdbcType=VARCHAR}
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="amountNature != null and  amountNature != '' ">
                AND AMOUNT_NATURE = #{amountNature,jdbcType=VARCHAR}
            </if>
            <if test="intCalcBal != null and  intCalcBal != '' ">
                AND INT_CALC_BAL = #{intCalcBal,jdbcType=VARCHAR}
            </if>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
            </if>
            <if test="acctSeqNo != null and  acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="PrimaryKey_Where">
        <where>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="Base_Set">
        <set>
            <if test="branch != null and branch != '' ">
                BRANCH = #{branch,jdbcType=VARCHAR},
            </if>
            <if test="prodType != null and prodType != '' ">
                PROD_TYPE = #{prodType,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null and ccy != '' ">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="amtType != null and amtType != '' ">
                AMT_TYPE = #{amtType,jdbcType=VARCHAR},
            </if>
            <if test="balance != null ">
                BALANCE = #{balance,jdbcType=DECIMAL},
            </if>
            <if test="sourceType != null and sourceType != '' ">
                SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="systemId != null and systemId != '' ">
                SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="runDate != null ">
                RUN_DATE = #{runDate,jdbcType=DATE},
            </if>
            <if test="othBranch != null and othBranch != '' ">
                OTH_BRANCH = #{othBranch,jdbcType=VARCHAR},
            </if>
            <if test="dealFlag != null and dealFlag != '' ">
                DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="dataFlag != null and dataFlag != '' ">
                DATA_FLAG = #{dataFlag,jdbcType=VARCHAR},
            </if>
            <if test="busiProd != null and busiProd != '' ">
                BUSI_PROD = #{busiProd,jdbcType=VARCHAR},
            </if>
            <if test="glCode != null and glCode != '' ">
                GL_CODE = #{glCode,jdbcType=VARCHAR},
            </if>
            <if test="lastChangeDate != null ">
                LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE},
            </if>
            <if test="profitCenter != null and profitCenter != '' ">
                PROFIT_CENTER = #{profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="sendSystem != null and sendSystem != '' ">
                SEND_SYSTEM = #{sendSystem,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null and eventType != '' ">
                EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="tranType != null and tranType != '' ">
                TRAN_TYPE = #{tranType,jdbcType=VARCHAR},
            </if>
            <if test="individualFlag != null and individualFlag != '' ">
                INDIVIDUAL_FLAG = #{individualFlag,jdbcType=VARCHAR},
            </if>
            <if test="acctStatus != null and acctStatus != '' ">
                ACCT_STATUS = #{acctStatus,jdbcType=VARCHAR},
            </if>
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="amountNature != null and amountNature != '' ">
                AMOUNT_NATURE = #{amountNature,jdbcType=VARCHAR},
            </if>
            <if test="intCalcBal != null and intCalcBal != '' ">
                INT_CALC_BAL = #{intCalcBal,jdbcType=VARCHAR},
            </if>
            <if test="internalKey != null ">
                INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},
            </if>
            <if test="acctSeqNo != null and acctSeqNo != '' ">
                ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="seqNo != null and seqNo != '' ">
                SEQ_NO = #{seqNo,jdbcType=VARCHAR},
            </if>
        </set>
    </sql>

    <sql id="Base_Select">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </sql>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'"> ${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'"> *</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}</if>
    </sql>

    <sql id="comet_row_num_step_column">
        <if test="cometSqlType == 'total'"> count(*) AS TOTAL</if>
        <if test="cometSqlType == 'offset'"> *</if>
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="branch != null ">BRANCH,</if>
            <if test="prodType != null ">PROD_TYPE,</if>
            <if test="ccy != null ">CCY,</if>
            <if test="amtType != null ">AMT_TYPE,</if>
            <if test="balance != null ">BALANCE,</if>
            <if test="sourceType != null ">SOURCE_TYPE,</if>
            <if test="systemId != null ">SYSTEM_ID,</if>
            <if test="runDate != null ">RUN_DATE,</if>
            <if test="othBranch != null ">OTH_BRANCH,</if>
            <if test="dealFlag != null ">DEAL_FLAG,</if>
            <if test="dataFlag != null ">DATA_FLAG,</if>
            <if test="busiProd != null ">BUSI_PROD,</if>
            <if test="glCode != null ">GL_CODE,</if>
            <if test="lastChangeDate != null ">LAST_CHANGE_DATE,</if>
            <if test="profitCenter != null ">PROFIT_CENTER,</if>
            <if test="sendSystem != null ">SEND_SYSTEM,</if>
            <if test="company != null ">COMPANY,</if>
            <if test="tranTimestamp != null ">TRAN_TIMESTAMP,</if>
            <if test="eventType != null ">EVENT_TYPE,</if>
            <if test="tranType != null ">TRAN_TYPE,</if>
            <if test="individualFlag != null ">INDIVIDUAL_FLAG,</if>
            <if test="acctStatus != null ">ACCT_STATUS,</if>
            <if test="baseAcctNo != null ">BASE_ACCT_NO,</if>
            <if test="amountNature != null ">AMOUNT_NATURE,</if>
            <if test="intCalcBal != null ">INT_CALC_BAL,</if>
            <if test="internalKey != null ">INTERNAL_KEY,</if>
            <if test="acctSeqNo != null ">ACCT_SEQ_NO,</if>
            <if test="seqNo != null ">SEQ_NO,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="branch != null ">#{branch,jdbcType=VARCHAR},</if>
            <if test="prodType != null ">#{prodType,jdbcType=VARCHAR},</if>
            <if test="ccy != null ">#{ccy,jdbcType=VARCHAR},</if>
            <if test="amtType != null ">#{amtType,jdbcType=VARCHAR},</if>
            <if test="balance != null ">#{balance,jdbcType=DECIMAL},</if>
            <if test="sourceType != null ">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="systemId != null ">#{systemId,jdbcType=VARCHAR},</if>
            <if test="runDate != null ">#{runDate,jdbcType=DATE},</if>
            <if test="othBranch != null ">#{othBranch,jdbcType=VARCHAR},</if>
            <if test="dealFlag != null ">#{dealFlag,jdbcType=VARCHAR},</if>
            <if test="dataFlag != null ">#{dataFlag,jdbcType=VARCHAR},</if>
            <if test="busiProd != null ">#{busiProd,jdbcType=VARCHAR},</if>
            <if test="glCode != null ">#{glCode,jdbcType=VARCHAR},</if>
            <if test="lastChangeDate != null ">#{lastChangeDate,jdbcType=DATE},</if>
            <if test="profitCenter != null ">#{profitCenter,jdbcType=VARCHAR},</if>
            <if test="sendSystem != null ">#{sendSystem,jdbcType=VARCHAR},</if>
            <if test="company != null ">#{company,jdbcType=VARCHAR},</if>
            <if test="tranTimestamp != null ">#{tranTimestamp,jdbcType=VARCHAR},</if>
            <if test="eventType != null ">#{eventType,jdbcType=VARCHAR},</if>
            <if test="tranType != null ">#{tranType,jdbcType=VARCHAR},</if>
            <if test="individualFlag != null ">#{individualFlag,jdbcType=VARCHAR},</if>
            <if test="acctStatus != null ">#{acctStatus,jdbcType=VARCHAR},</if>
            <if test="baseAcctNo != null ">#{baseAcctNo,jdbcType=VARCHAR},</if>
            <if test="amountNature != null ">#{amountNature,jdbcType=VARCHAR},</if>
            <if test="intCalcBal != null ">#{intCalcBal,jdbcType=VARCHAR},</if>
            <if test="internalKey != null ">#{internalKey,jdbcType=BIGINT},</if>
            <if test="acctSeqNo != null ">#{acctSeqNo,jdbcType=VARCHAR},</if>
            <if test="seqNo != null ">#{seqNo,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>

    <update id="updateByPrimaryKey">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>


    <update id="updateByEntity">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="s.branch != null and s.branch != '' ">
                BRANCH = #{s.branch,jdbcType=VARCHAR},
            </if>
            <if test="s.prodType != null and s.prodType != '' ">
                PROD_TYPE = #{s.prodType,jdbcType=VARCHAR},
            </if>
            <if test="s.ccy != null and s.ccy != '' ">
                CCY = #{s.ccy,jdbcType=VARCHAR},
            </if>
            <if test="s.amtType != null and s.amtType != '' ">
                AMT_TYPE = #{s.amtType,jdbcType=VARCHAR},
            </if>
            <if test="s.balance != null ">
                BALANCE = #{s.balance,jdbcType=DECIMAL},
            </if>
            <if test="s.sourceType != null and s.sourceType != '' ">
                SOURCE_TYPE = #{s.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="s.systemId != null and s.systemId != '' ">
                SYSTEM_ID = #{s.systemId,jdbcType=VARCHAR},
            </if>
            <if test="s.runDate != null ">
                RUN_DATE = #{s.runDate,jdbcType=DATE},
            </if>
            <if test="s.othBranch != null and s.othBranch != '' ">
                OTH_BRANCH = #{s.othBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.dealFlag != null and s.dealFlag != '' ">
                DEAL_FLAG = #{s.dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.dataFlag != null and s.dataFlag != '' ">
                DATA_FLAG = #{s.dataFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.busiProd != null and s.busiProd != '' ">
                BUSI_PROD = #{s.busiProd,jdbcType=VARCHAR},
            </if>
            <if test="s.glCode != null and s.glCode != '' ">
                GL_CODE = #{s.glCode,jdbcType=VARCHAR},
            </if>
            <if test="s.lastChangeDate != null ">
                LAST_CHANGE_DATE = #{s.lastChangeDate,jdbcType=DATE},
            </if>
            <if test="s.profitCenter != null and s.profitCenter != '' ">
                PROFIT_CENTER = #{s.profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="s.sendSystem != null and s.sendSystem != '' ">
                SEND_SYSTEM = #{s.sendSystem,jdbcType=VARCHAR},
            </if>
            <if test="s.company != null and s.company != '' ">
                COMPANY = #{s.company,jdbcType=VARCHAR},
            </if>
            <if test="s.tranTimestamp != null and s.tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{s.tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="s.eventType != null and s.eventType != '' ">
                EVENT_TYPE = #{s.eventType,jdbcType=VARCHAR},
            </if>
            <if test="s.tranType != null and s.tranType != '' ">
                TRAN_TYPE = #{s.tranType,jdbcType=VARCHAR},
            </if>
            <if test="s.individualFlag != null and s.individualFlag != '' ">
                INDIVIDUAL_FLAG = #{s.individualFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.acctStatus != null and s.acctStatus != '' ">
                ACCT_STATUS = #{s.acctStatus,jdbcType=VARCHAR},
            </if>
            <if test="s.baseAcctNo != null and s.baseAcctNo != '' ">
                BASE_ACCT_NO = #{s.baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="s.amountNature != null and s.amountNature != '' ">
                AMOUNT_NATURE = #{s.amountNature,jdbcType=VARCHAR},
            </if>
            <if test="s.intCalcBal != null and s.intCalcBal != '' ">
                INT_CALC_BAL = #{s.intCalcBal,jdbcType=VARCHAR},
            </if>
            <if test="s.internalKey != null ">
                INTERNAL_KEY = #{s.internalKey,jdbcType=BIGINT},
            </if>
            <if test="s.acctSeqNo != null and s.acctSeqNo != '' ">
                ACCT_SEQ_NO = #{s.acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.seqNo != null and s.seqNo != '' ">
                SEQ_NO = #{s.seqNo,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="w.branch != null and w.branch != '' ">
                AND BRANCH = #{w.branch,jdbcType=VARCHAR}
            </if>
            <if test="w.prodType != null and w.prodType != '' ">
                AND PROD_TYPE = #{w.prodType,jdbcType=VARCHAR}
            </if>
            <if test="w.ccy != null and w.ccy != '' ">
                AND CCY = #{w.ccy,jdbcType=VARCHAR}
            </if>
            <if test="w.amtType != null and w.amtType != '' ">
                AND AMT_TYPE = #{w.amtType,jdbcType=VARCHAR}
            </if>
            <if test="w.balance != null ">
                AND BALANCE = #{w.balance,jdbcType=DECIMAL}
            </if>
            <if test="w.sourceType != null and w.sourceType != '' ">
                AND SOURCE_TYPE = #{w.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="w.systemId != null and w.systemId != '' ">
                AND SYSTEM_ID = #{w.systemId,jdbcType=VARCHAR}
            </if>
            <if test="w.runDate != null ">
                AND RUN_DATE = #{w.runDate,jdbcType=DATE}
            </if>
            <if test="w.othBranch != null and w.othBranch != '' ">
                AND OTH_BRANCH = #{w.othBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.dealFlag != null and w.dealFlag != '' ">
                AND DEAL_FLAG = #{w.dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.dataFlag != null and w.dataFlag != '' ">
                AND DATA_FLAG = #{w.dataFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.busiProd != null and w.busiProd != '' ">
                AND BUSI_PROD = #{w.busiProd,jdbcType=VARCHAR}
            </if>
            <if test="w.glCode != null and w.glCode != '' ">
                AND GL_CODE = #{w.glCode,jdbcType=VARCHAR}
            </if>
            <if test="w.lastChangeDate != null ">
                AND LAST_CHANGE_DATE = #{w.lastChangeDate,jdbcType=DATE}
            </if>
            <if test="w.profitCenter != null and w.profitCenter != '' ">
                AND PROFIT_CENTER = #{w.profitCenter,jdbcType=VARCHAR}
            </if>
            <if test="w.sendSystem != null and w.sendSystem != '' ">
                AND SEND_SYSTEM = #{w.sendSystem,jdbcType=VARCHAR}
            </if>
            <if test="w.company != null and w.company != '' ">
                AND COMPANY = #{w.company,jdbcType=VARCHAR}
            </if>
            <if test="w.tranTimestamp != null and w.tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{w.tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="w.eventType != null and w.eventType != '' ">
                AND EVENT_TYPE = #{w.eventType,jdbcType=VARCHAR}
            </if>
            <if test="w.tranType != null and w.tranType != '' ">
                AND TRAN_TYPE = #{w.tranType,jdbcType=VARCHAR}
            </if>
            <if test="w.individualFlag != null and w.individualFlag != '' ">
                AND INDIVIDUAL_FLAG = #{w.individualFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.acctStatus != null and w.acctStatus != '' ">
                AND ACCT_STATUS = #{w.acctStatus,jdbcType=VARCHAR}
            </if>
            <if test="w.baseAcctNo != null and w.baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{w.baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="w.amountNature != null and w.amountNature != '' ">
                AND AMOUNT_NATURE = #{w.amountNature,jdbcType=VARCHAR}
            </if>
            <if test="w.intCalcBal != null and w.intCalcBal != '' ">
                AND INT_CALC_BAL = #{w.intCalcBal,jdbcType=VARCHAR}
            </if>
            <if test="w.internalKey != null ">
                AND INTERNAL_KEY = #{w.internalKey,jdbcType=BIGINT}
            </if>
            <if test="w.acctSeqNo != null and w.acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{w.acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.seqNo != null and w.seqNo != '' ">
                AND SEQ_NO = #{w.seqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <delete id="delete">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </delete>

    <select id="count" parameterType="java.util.Map" resultType="int">
        SELECT count(1) FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </select>

    <select id="selectOne" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectList" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
        <include refid="Base_Select"/>
        FOR UPDATE
    </select>

    <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </delete>
</mapper>
