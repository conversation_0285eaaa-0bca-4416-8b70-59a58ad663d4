<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword">

    <select id="selectByPrimaryKeyExt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        select
        <include refid="Base_Column"/>
        from RB_PASSWORD
        where PWD_TYPE = #{pwdType}
        and PWD_KEY = #{pwdKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="deleteByPrimaryKeyExt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        DELETE from RB_PASSWORD
        where PWD_TYPE = #{pwdType}
        and PWD_KEY = #{pwdKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getActivePassword" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword">
        select
        <include refid="Base_Column"/>
        from RB_PASSWORD
        where PWD_TYPE = #{pwdType}
        and PWD_KEY = #{pwdKey}
        <if test="clientNo != null and  clientNo != '' ">
            AND  CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and PASSWORD_STATUS != 'E'
    </select>

    <select id="getMbPasswords" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword">
        select
        <include refid="Base_Column"/>
        from RB_PASSWORD
        where PWD_KEY = #{pwdKey}
        <if test="client_no != null and  client_no != '' ">
          AND  CLIENT_NO = #{client_no}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword">
        <!--
          WARNING - @mbggener

          ated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        insert into RB_PASSWORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pwdType != null">
                PWD_TYPE,
            </if>
            <if test="pwdKey != null">
                PWD_KEY,
            </if>
            <if test="password != null">
                PASSWORD,
            </if>
            <if test="passwordStatus != null">
                PASSWORD_STATUS,
            </if>
            <if test="failureTimes != null">
                FAILURE_TIMES,
            </if>
            <if test="passwordEffectDate != null">
                PASSWORD_EFFECT_DATE,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="changeInd != null">
                CHANGE_IND,
            </if>
            <if test="company != null">
                COMPANY,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pwdType != null">
                #{pwdType},
            </if>
            <if test="pwdKey != null">
                #{pwdKey},
            </if>
            <if test="password != null">
                #{password},
            </if>
            <if test="passwordStatus != null">
                #{passwordStatus},
            </if>
            <if test="failureTimes != null">
                #{failureTimes},
            </if>
            <if test="passwordEffectDate != null">
                #{passwordEffectDate},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate},
            </if>
            <if test="changeInd != null">
                #{changeInd},
            </if>
            <if test="company != null">
                #{company},
            </if>

        </trim>
    </insert>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPassword">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        update RB_PASSWORD
        <set>
            <if test="password != null">
                PASSWORD = #{password},
            </if>
            <if test="passwordStatus != null">
                PASSWORD_STATUS = #{passwordStatus},
            </if>
            <if test="failureTimes != null">
                FAILURE_TIMES = #{failureTimes},
            </if>
            <if test="passwordEffectDate != null">
                PASSWORD_EFFECT_DATE = #{passwordEffectDate},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="company != null">
                COMPANY = #{company},
            </if>
        </set>
        where PWD_TYPE = #{pwdType}
        and PWD_KEY = #{pwdKey}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="selectByPrimaryKeyAndClientNo"  resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column" />
        FROM
        RB_PASSWORD
        <where>
            <trim suffixOverrides="AND">
                <if test="pwdKey != null and  pwdKey != '' ">
                    PWD_KEY = #{pwdKey}  AND
                </if>
                <if test="pwdType != null and  pwdType != '' ">
                    PWD_TYPE = #{pwdType}  AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo}  AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
</mapper>
