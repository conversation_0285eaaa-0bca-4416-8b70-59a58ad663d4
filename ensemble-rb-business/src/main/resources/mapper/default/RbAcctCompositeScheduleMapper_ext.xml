<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctCompositeSchedule">
<sql id="Base_Column_List">
    ADD_AMT,
    COMPANY,
    COMPOSITE_NO,
    CUR_BASIC_DAY,
    DAC_VALUE,
    DEAL_DAY,
    END_DATE,
    FIR_PERIOD,
    GRACE_END_MONTH,
    GRACE_PERIOD,
    INTERNAL_KEY,
    INT_DEAL_DAY,
    INT_NEXT_DEAL_DATE,
    INT_PERIOD_FREQ,
    MID_PERIOD,
    NEXT_DEAL_DATE,
    PERIOD_FREQ,
    SCHED_MODE,
    SECOND_REC_DAY,
    START_DATE,
    ADD_RATIO,
    TOTAL_AMT,
    TRAN_TIMESTAMP,
    ACCT_PERCENT_RATE,
    ACCT_SPREAD_RATE,
    ACCT_FIXED_RATE,
    AGREE_CHANGE_TYPE
  </sql>
  
  <select id="selectCompositeScheduleByStartDate" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctCompositeSchedule"
          parameterType="java.util.Map" >
    select  <include refid="Base_Column_List"/>
    from RB_acct_composite_schedule
    where INTERNAL_KEY = #{internalKey}
      and START_DATE = #{startDate}
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </select>
</mapper>
