<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbReceiptPrint">

    <select id="getMbReceiptPrintListByAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbReceiptPrint">
        select * from (select  a.*
        from
        rb_receipt_print a , rb_tran_hist b
        <where>
            a.reference = b.reference
            and a.tran_type = b.tran_type
            and a.tran_amt = b.tran_amt
            and (a.payer_base_acct_no = b.base_acct_no or
            a.payee_acct_no = b.base_acct_no)
            and b.tran_status != 'W'
            <if test="narrativeClass != null and narrativeClass != 'AL' and narrativeClass.length() > 0">
                and b.narrative_code in ( select narrative_code from  rb_narrative_def c  where c.narrative_class = #{narrativeClass})
            </if>
            <if test="acctNos != null and acctNos.size() > 0">
                and b.base_acct_no in
                <foreach collection="acctNos" open="(" close=")" separator="," index="index" item="baseAcctNo">
                    #{baseAcctNo}
                </foreach>
            </if>

            <!--多法人改造 by luocwa  -->
            <if test="startDate != null and startDate.length() > 0">
                AND a.tran_date <![CDATA[>=]]> #{startDate,jdbcType=DATE}
            </if>
            <if test="endDate != null and endDate.length() > 0">
                AND a.tran_date <![CDATA[<=]]> #{endDate,jdbcType=DATE}
            </if>
            <if test="lowAmt != null">
                AND a.TRAN_AMT <![CDATA[>=]]> #{lowAmt}
            </if>
            <if test="highAmt != null">
                AND a.TRAN_AMT <![CDATA[<=]]> #{highAmt}
            </if>
            <if test="printType != null and printType == '0'.toString()">
                AND a.PRINT_CNT = 0
            </if>
            <if test="printType != null and printType == '1'.toString()">
                AND a.PRINT_CNT <![CDATA[>]]> 0
            </if>
            <if test="payeeAcctNo != null and payeeAcctNo.length() > 0">
                AND a.PAYEE_ACCT_NO = #{payeeAcctNo}
            </if>
            <if test="payeeAcctName != null and payeeAcctName.length() > 0">
                AND a.PAYEE_ACCT_NAME = #{payeeAcctName}
            </if>
            <if test="reference != null and reference.length() > 0">
                AND a.REFERENCE = #{reference}
            </if>
        </where>
        ) a order by a.tran_time desc,a.reference,a.RECEIPT_NO
    </select>

    <select id="getMbReceiptPrintListForSumAmt" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(TRAN_AMT) TRAN_AMT from (
        select a.TRAN_AMT
        from rb_receipt_print a , rb_tran_hist b
        <where>a.reference = b.reference
            and a.tran_type = b.tran_type
            and a.tran_amt = b.tran_amt
            and (a.payer_base_acct_no = b.base_acct_no or
            a.payee_acct_no = b.base_acct_no)
            and b.tran_status != 'W'
            <if test="narrativeClass != null and narrativeClass != 'AL' and narrativeClass.length() > 0">
                and b.narrative_code in ( select narrative_code from  rb_narrative_def c where c.narrative_class = #{narrativeClass})
            </if>
            <if test="acctNos != null and acctNos.size() > 0">
                and b.base_acct_no in
                <foreach collection="acctNos" open="(" close=")" separator="," index="index" item="baseAcctNo">
                    #{baseAcctNo}
                </foreach>
            </if>
            <!--多法人改造 by luocwa  -->
            <if test="startDate != null and startDate.length() > 0">
                AND a.tran_date <![CDATA[>=]]> #{startDate,jdbcType=DATE}
            </if>
            <if test="endDate != null and endDate.length() > 0">
                AND a.tran_date <![CDATA[<=]]> #{endDate,jdbcType=DATE}
            </if>
            <if test="lowAmt != null">
                AND a.TRAN_AMT <![CDATA[>=]]> #{lowAmt}
            </if>
            <if test="highAmt != null">
                AND a.TRAN_AMT <![CDATA[<=]]> #{highAmt}
            </if>
            <if test="printType != null and printType == '0'.toString()">
                AND a.PRINT_CNT = 0
            </if>
            <if test="printType != null and printType == '1'.toString()">
                AND a.PRINT_CNT <![CDATA[>]]> 0
            </if>
            <if test="payeeAcctNo != null and payeeAcctNo.length() > 0">
                AND a.PAYEE_ACCT_NO = #{payeeAcctNo}
            </if>
            <if test="payeeAcctName != null and payeeAcctName.length() > 0">
                AND a.PAYEE_ACCT_NAME = #{payeeAcctName}
            </if>
            <if test="reference != null and reference.length() > 0">
                AND a.REFERENCE = #{reference}
            </if>
        </where>
        )
    </select>

    <update id="updatebyReference" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbReceiptPrint">
        update RB_RECEIPT_PRINT
        <set>
            print_cnt = #{printCnt}
        </set>
        where REFERENCE = #{reference}
        <if test="receiptNo != null">
            AND RECEIPT_NO = #{receiptNo}
        </if>
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!--多法人改造 by LIYUANV-->
        <!--        <if test="tranDate != null">
                    AND TRAN_DATE = #{tranDate,jdbcType=DATE}
                </if>-->
    </update>

    <update id="updatebyReference1" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbReceiptPrint">
        update RB_RECEIPT_PRINT
        <set>
            remark = #{remark},object_name = #{objectName}
        </set>
        where REFERENCE = #{reference}
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
    </update>

    <select id="selectBatchInqCount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        SELECT
        count(1)
        from
        rb_receipt_print a inner join rb_tran_hist b on (a.payer_base_acct_no=b.base_acct_no or
        a.payee_acct_no=b.base_acct_no)
        and a.reference=b.reference and a.tran_type=b.tran_type and a.tran_amt=b.tran_amt
        <if test="acctNos != null and acctNos.size() > 0">
            and b.base_acct_no in
            <foreach collection="acctNos" open="(" close=")" separator="," index="index" item="baseAcctNo">
                #{baseAcctNo}
            </foreach>
        </if>
        and b.tran_status != 'W'
        <!--多法人改造 by luocwa  -->
        <if test="startDate != null and startDate.length() > 0">
            AND a.tran_date <![CDATA[>=]]> #{startDate,jdbcType=DATE}
        </if>
        <if test="endDate != null and endDate.length() > 0">
            AND a.tran_date <![CDATA[<=]]> #{endDate,jdbcType=DATE}
        </if>
        <if test="minAmt != null">
            AND a.TRAN_AMT <![CDATA[>=]]> #{minAmt}
        </if>
        <if test="maxAmt != null">
            AND a.TRAN_AMT <![CDATA[<=]]> #{maxAmt}
        </if>
        <if test="printType != null and printType == '0'.toString()">
            AND a.PRINT_CNT = 0
        </if>
        <if test="printType != null and printType == '1'.toString()">
            AND a.PRINT_CNT <![CDATA[>]]> 0
        </if>
        <if test="othBaseAcctNo != null and othBaseAcctNo.length() > 0">
            AND a.PAYEE_ACCT_NO = #{othBaseAcctNo}
        </if>
        <if test="othAcctName != null and othAcctName.length() > 0">
            AND a.PAYEE_ACCT_NAME = #{othAcctName}
        </if>
        <include refid="comet_step_where"/>
    </select>


    <select id="getReceiptPrintListByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbReceiptPrint">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_RECEIPT_PRINT a
        WHERE a.reference = #{reference} and a.object_name is not null
    </select>


    <update id="updRealInfoByRefAndDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbReceiptPrint">
        update RB_RECEIPT_PRINT
        <set>
            <if test="narrative != null and narrative != '' ">
                NARRATIVE = #{narrative,jdbcType=VARCHAR},
            </if>
            <if test="tranNote != null and tranNote != '' ">
                TRAN_NOTE = #{tranNote,jdbcType=VARCHAR},
            </if>
            <if test="payeeAcctNo != null and payeeAcctNo != '' ">
                PAYEE_ACCT_NO = #{payeeAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="payeeAcctName != null and payeeAcctName != '' ">
                PAYEE_ACCT_NAME = #{payeeAcctName,jdbcType=VARCHAR},
            </if>
            <if test="payeeBankCode != null and payeeBankCode != '' ">
                PAYEE_BANK_CODE = #{payeeBankCode,jdbcType=VARCHAR},
            </if>
            <if test="payeeBankName != null and payeeBankName != '' ">
                PAYEE_BANK_NAME = #{payeeBankName,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE REFERENCE = #{reference}
        <if test="tranDate !=  null">
            AND TRAN_DATE = #{tranDate,jdbcType=DATE}
        </if>
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
    </update>

</mapper>
