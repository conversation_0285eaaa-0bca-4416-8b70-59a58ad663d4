<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent">

  <select id="selectByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >
  select <include refid="Base_Column"/>
  from RB_PB_CONTENT
  where INTERNAL_KEY = #{internalKey}
      <if test="clientNo != null and clientNo !='' " >
          AND CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  ORDER BY SEQ_NO DESC
  </select>
  <select id="select" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >
  	select <include refid="Base_Column"/>
  	from RB_PB_CONTENT
  	where INTERNAL_KEY = #{internalKey}
        <if test="printPageNo != null and printPageNo !=''">
            AND PRINT_PAGE_NO = #{printPageNo}
        </if>
        <if test="printLineNo != null and printLineNo !=''">
            AND PRINT_LINE_NO = #{printLineNo}
        </if>
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </select>
  <select id="selectPageNoLineNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PB_CONTENT
        WHERE internal_key = #{internalKey}
        AND tran_date IS NULL
        AND actual_amt IS NULL
        AND PRINT_PAGE_NO IS NOT NULL
        AND PRINT_LINE_NO IS NOT NULL
     <if test="clientNo != null and clientNo !='' " >
         AND CLIENT_NO = #{clientNo}
     </if>
     <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
      ORDER BY PRINT_PAGE_NO DESC, PRINT_LINE_NO DESC

  </select>

    <select id="selectPageNoLineNoXXX" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >

        SELECT <include refid="Base_Column"/>
        FROM RB_PB_CONTENT
        WHERE internal_key = #{internalKey}
        AND tran_date IS NULL
        AND actual_amt IS NULL
        AND PRINT_PAGE_NO IS NOT NULL
        AND PRINT_LINE_NO IS NOT NULL
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY PRINT_PAGE_NO DESC, PRINT_LINE_NO DESC

    </select>

    <update id="updatePageNoLineNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" >
      update RB_PB_CONTENT
    <set >
      <if test="printPageNo != null" >
        PRINT_PAGE_NO = #{printPageNo},
      </if>
      <if test="printLineNo != null" >
        PRINT_LINE_NO = #{printLineNo},
      </if>
        <if test="tranTimestamp != null">
            TRAN_TIMESTAMP = #{tranTimestamp},
        </if>
    </set>
	 WHERE internal_key = #{internalKey}
	   AND PRINT_PAGE_NO IS NOT NULL
	   AND PRINT_LINE_NO IS NOT NULL
	   AND tran_date IS NULL
<!--
	   AND seq_no IS NULL
-->
	   AND actual_amt IS NULL
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </update>
    <select id="selectBySeqNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >
        SELECT <include refid="Base_Column"/>
        FROM RB_PB_CONTENT
        WHERE seq_no = #{seqNo}
        AND tran_date IS NOT NULL
        AND seq_no IS NOT NULL
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectPrintSum" resultType="java.math.BigDecimal" parameterType="java.util.Map" >
        SELECT sum(1)
        FROM RB_PB_CONTENT
        WHERE internal_key = #{internalKey}
            <if test="clientNo != null and clientNo !='' " >
                AND CLIENT_NO = #{clientNo}
            </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getPrintedInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >
        SELECT <include refid="Base_Column"/>
        FROM RB_PB_CONTENT
        WHERE internal_key = #{internalKey}
        AND tran_date IS NOT NULL
        AND seq_no IS NOT NULL
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="queryTdNextPrintLine" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >
        SELECT <include refid="Base_Column"/>
        FROM RB_PB_CONTENT
        WHERE internal_key = #{internalKey}
        AND PRINT_PAGE_NO IS NOT NULL
        AND PRINT_LINE_NO IS NOT NULL
        AND tran_date IS NULL
<!--
        AND seq_no IS NULL
-->
        AND actual_amt IS NULL
        and voucher_no is null
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="queryTdBlankPrintLine" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map" >
        SELECT <include refid="Base_Column"/>
        FROM RB_PB_CONTENT
        WHERE internal_key = #{internalKey}
        AND TRAN_DESC = 'acctOpenBak'
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getPrintPageNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" parameterType="java.util.Map">
        SELECT MAX(PRINT_PAGE_NO)
        FROM RB_PB_CONTENT
        WHERE internal_key = #{internalKey}
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateTdNullLine" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" >
        update RB_PB_CONTENT
        <set >
            <if test="printPageNo != null" >
                PRINT_PAGE_NO = #{printPageNo},
            </if>
            <if test="printLineNo != null" >
                PRINT_LINE_NO = #{printLineNo},
            </if>
        </set>
        WHERE internal_key = #{internalKey}
        AND tran_date IS NULL
<!--
        AND seq_no IS NULL
-->
        AND actual_amt IS NULL
        and voucher_no is null
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="updateTdNullLine1" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent" >
        update RB_PB_CONTENT
        <set >
            tran_desc = '部提',
            tran_timestamp = #{tranTimestamp}
        </set>
        WHERE internal_key = #{internalKey}
        AND PRINT_PAGE_NO = #{printPageNo}
        AND PRINT_LINE_NO = #{printLineNo}
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <delete id="deleteBySeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent">
        delete from RB_PB_CONTENT
        where SEQ_NO = #{seqNo}
        <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
</mapper>
