<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeePackage">
	<select id="getEffectFeePackage" parameterType="java.util.Map" resultMap="Base_Result_Map">
    select
       <include refid="Base_Column" />
			 from RB_FEE_PACKAGE

			 where PACKAGE_ID = #{packageId}
			   and PACKAGE_STATUS = 'A'
		<![CDATA[
			   and EFFECT_DATE <= #{runDate} and END_DATE > #{runDate}
		]]>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
  </select>
	<select id="selectEffectDateEndDateMature" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeePackage">
		select PACKAGE_ID, PACKAGE_DESC, PACKAGE_TYPE,
			   PACKAGE_MODE, PACKAGE_CCY, PACKAGE_FEE_TYPE,
			   SETTLE_CCY, SETTLE_AMT, PACKAGE_PERIOD_FREQ,
			   DEAL_DAY, NEXT_DEAL_DATE, PACKAGE_AMT,
			   PACKAGE_NUM, PROCESS_MODE, PROCESS_ORDER,
			   CLIENT_TYPE, PACKAGE_STATUS, EFFECT_DATE,
			   END_DATE,TRAN_TIMESTAMP,COMPANY
		from RB_FEE_PACKAGE
		where (
				<![CDATA[
				EFFECT_DATE <= #{runDate} and END_DATE > #{runDate}
				]]>
<!-- 多法人改造 by fudd -->
				<if test="company != null and company != '' ">
				AND COMPANY = #{company}
				</if>
		    	)
		   or (END_DATE<![CDATA[ <= ]]> #{runDate}
				<if test="company != null and company != '' ">
					AND COMPANY = #{company}
				</if>
		    	)
		order by PACKAGE_ID

  </select>
	<select id="selectNextDealDateMature" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeePackage">

		select PACKAGE_ID, PACKAGE_DESC, PACKAGE_TYPE,
			   PACKAGE_MODE, PACKAGE_CCY, PACKAGE_FEE_TYPE,
			   SETTLE_CCY, SETTLE_AMT, PACKAGE_PERIOD_FREQ,
			   DEAL_DAY, NEXT_DEAL_DATE, PACKAGE_AMT,
			   PACKAGE_NUM, PROCESS_MODE, PROCESS_ORDER,
			   CLIENT_TYPE, PACKAGE_STATUS, EFFECT_DATE,
			   END_DATE
		from RB_FEE_PACKAGE
		where PACKAGE_STATUS = 'A'
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		  <![CDATA[
		  and EFFECT_DATE <= #{runDate}
		  and END_DATE > #{runDate}
		  and NEXT_DEAL_DATE > #{lastRunDate}
		order by PACKAGE_ID
		]]>
  </select>
	<select id="getPackageByPackageId" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeePackage" parameterType="java.util.Map">
		select * from rb_fee_package
		where package_id = #{packageId}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
