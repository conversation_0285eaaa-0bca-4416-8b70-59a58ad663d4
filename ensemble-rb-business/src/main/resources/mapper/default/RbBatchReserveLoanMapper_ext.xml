<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveLoan">



    <select id="getReserveLoanForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveLoan"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveLoan">
        select <include refid="Base_Column"/>
        from rb_batch_reserve_loan
        where contract_no = #{contractNo}
        and bill_voucher_no = #{billVoucherNo}
        for update
    </select>

</mapper>