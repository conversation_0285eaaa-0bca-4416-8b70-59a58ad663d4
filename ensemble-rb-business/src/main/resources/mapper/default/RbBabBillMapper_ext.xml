<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">

    <!-- Replacement of bank-backed bills -->
    <update id="updateByBillInfo" parameterType="java.util.Map">
        UPDATE RB_BAB_BILL
        <set>
            <if test="newBillType != null and  newBillType != '' ">
                BILL_TYPE = #{newBillType},
            </if>
            <if test="newBillVoucherNo != null and  newBillVoucherNo != '' ">
                BILL_VOUCHER_NO = #{newBillVoucherNo},
            </if>
            <if test="newBillDocType != null and  newBillDocType != '' ">
                BILL_DOC_TYPE = #{newBillDocType}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="oldBillType != null and  oldBillType != '' ">
                    BILL_TYPE = #{oldBillType} AND
                </if>
                <if test="oldBillVoucherNo != null and  oldBillVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{oldBillVoucherNo} AND
                </if>
                <if test="oldBillDocType != null and  oldBillDocType != '' ">
                    BILL_DOC_TYPE = #{oldBillDocType} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>


    <update id="updateStatusByBillNo" parameterType="java.util.Map">
        UPDATE RB_BAB_BILL
        <set>
            <if test="billDocType != null and  billDocType != '' ">
                BILL_DOC_TYPE = #{billDocType},
            </if>
            <if test="billVoucherNo != null and  billVoucherNo != '' ">
                BILL_VOUCHER_NO = #{billVoucherNo},
            </if>
            <if test="acceptContractNo != null and  acceptContractNo != '' ">
                ACCEPT_CONTRACT_NO = #{acceptContractNo}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="billStatus != null and  billStatus != '' ">
                    BILL_STATUS = #{billStatus} AND
                </if>
                <if test="acceptStatus != null and  acceptStatus != '' ">
                    ACCEPT_STATUS = #{acceptStatus} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>


    <select id="selectRbBabBillForCancelByChannelSeqNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL
        <where>
            ACCEPT_STATUS != '06' AND
            <trim suffixOverrides="AND">
                <if test="channelSeqNo != null and  channelSeqNo != '' ">
                    CHANNEL_SEQ_NO = #{channelSeqNo} AND
                </if>
                <if test="subSeqNo != null and  subSeqNo != '' ">
                    SUB_SEQ_NO = #{subSeqNo} AND
                </if>
                <if test="channelDate != null  ">
                    CHANNEL_DATE = #{channelDate} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>

    <select id="selectRbBabBillForCancel" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL
        <where>
            ACCEPT_STATUS != '06' AND
            <trim suffixOverrides="AND">
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="billPrefix != null and  billPrefix != '' ">
                    BILL_PREFIX = #{billPrefix} AND
                </if>
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>


    <select id="selectRbBabBillByVoucher" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL
        <where>
            ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
            BILL_TYPE = #{billType} AND
            BILL_VOUCHER_NO = #{billVoucherNo} AND
            BILL_DOC_TYPE = #{billDocType}
            and BILL_STATUS not in ('03','06')
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>


    <update id="updateStatusByVoucherNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        UPDATE RB_BAB_BILL
        <set>
            <if test="acceptStatus != null and  acceptStatus != '' ">
                ACCEPT_STATUS = #{acceptStatus},
            </if>
            <if test="billStatus != null and  billStatus != '' ">
                BILL_STATUS = #{billStatus},
            </if>
        </set>
        <where>
            BILL_STATUS='01'
            and ACCEPT_STATUS='01'
            <trim suffixOverrides="AND">
                <if test="billType != null and  billType != '' ">
                    AND BILL_TYPE = #{billType}
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    AND BILL_VOUCHER_NO = #{billVoucherNo}
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    AND BILL_DOC_TYPE = #{billDocType}
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    AND CLIENT_NO = #{clientNo}
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    AND COMPANY = #{company}
                </if>
            </trim>
        </where>
    </update>


    <update id="updateAcceptStatusByVoucherNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        UPDATE RB_BAB_BILL
        <set>
            <if test="acceptStatus != null and  acceptStatus != '' ">
                ACCEPT_STATUS = #{acceptStatus},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>


    <update id="updateByVoucherNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        UPDATE RB_BAB_BILL
        <set>
            <if test="payeeBankAddr != null and  payeeBankAddr != '' ">
                PAYEE_BANK_ADDR = #{payeeBankAddr},
            </if>
            <if test="billAmt != null ">
                BILL_AMT = #{billAmt},
            </if>
            <if test="payerCcy != null and  payerCcy != '' ">
                PAYER_CCY = #{payerCcy},
            </if>
            <if test="payeeBaseAcctNo != null and  payeeBaseAcctNo != '' ">
                PAYEE_BASE_ACCT_NO = #{payeeBaseAcctNo},
            </if>
            <if test="pldAmount != null ">
                PLD_AMOUNT = #{pldAmount},
            </if>
            <if test="billMaturityDate != null ">
                BILL_MATURITY_DATE = #{billMaturityDate},
            </if>
            <if test="payeeProdType != null and  payeeProdType != '' ">
                PAYEE_PROD_TYPE = #{payeeProdType},
            </if>
            <if test="payeeAcctName != null and  payeeAcctName != '' ">
                PAYEE_ACCT_NAME = #{payeeAcctName},
            </if>
            <if test="payerBaseAcctNo != null and  payerBaseAcctNo != '' ">
                PAYER_BASE_ACCT_NO = #{payerBaseAcctNo},
            </if>
            <if test="payerAcctSeqNo != null and  payerAcctSeqNo != '' ">
                PAYER_ACCT_SEQ_NO = #{payerAcctSeqNo},
            </if>
            <if test="payerBranch != null and  payerBranch != '' ">
                PAYER_BRANCH = #{payerBranch},
            </if>
            <if test="payerBranchAddr != null and  payerBranchAddr != '' ">
                PAYER_BRANCH_ADDR = #{payerBranchAddr},
            </if>
            <if test="acceptContractNo != null and  acceptContractNo != '' ">
                ACCEPT_CONTRACT_NO = #{acceptContractNo},
            </if>
            <if test="acceptStatus != null and  acceptStatus != '' ">
                ACCEPT_STATUS = #{acceptStatus},
            </if>
            <if test="resSeqNo != null and  resSeqNo != '' ">
                RES_SEQ_NO = #{resSeqNo},
            </if>
            <if test="payerProdType != null and  payerProdType != '' ">
                PAYER_PROD_TYPE = #{payerProdType},
            </if>
            <if test="billSignDate != null ">
                BILL_SIGN_DATE = #{billSignDate},
            </if>
            <if test="payerAcctName != null and  payerAcctName != '' ">
                PAYER_ACCT_NAME = #{payerAcctName},
            </if>
            <if test="billAcceptDate != null ">
                BILL_ACCEPT_DATE = #{billAcceptDate},
            </if>
            <if test="payerBranchName != null and  payerBranchName != '' ">
                PAYER_BRANCH_NAME = #{payerBranchName},
            </if>
            <if test="payeeAcctSeqNo != null and  payeeAcctSeqNo != '' ">
                PAYEE_ACCT_SEQ_NO = #{payeeAcctSeqNo},
            </if>
            <if test="payeeBankName != null and  payeeBankName != '' ">
                PAYEE_BANK_NAME = #{payeeBankName},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="billStatus != null and  billStatus != '' ">
                BILL_STATUS = #{billStatus},
            </if>
            <if test="payeeAcctCcy != null and  payeeAcctCcy != '' ">
                PAYEE_ACCT_CCY = #{payeeAcctCcy},
            </if>
            <if test="servChargeReference != null and  servChargeReference != '' ">
                SERV_CHARGE_REFERENCE = #{servChargeReference},
            </if>
            <if test="lostNo != null and  lostNo != '' ">
                LOST_NO = #{lostNo},
            </if>
            <if test="innerAcctAdvancedAmt != null ">
                INNER_ACCT_ADVANCED_AMT = #{innerAcctAdvancedAmt},
            </if>
            <if test="overAmount != null ">
                OVER_AMOUNT = #{overAmount},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>

    <select id="selectRbBabBill" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL
        <where>
            ACCEPT_STATUS != '06' AND
            <trim suffixOverrides="AND">
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="billPrefix != null and  billPrefix != '' ">
                    BILL_PREFIX = #{billPrefix} AND
                </if>
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>

    <select id="selectRbBabBillNormal" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL
        <where>
            ACCEPT_STATUS = '01' AND
            <trim suffixOverrides="AND">
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="billPrefix != null and  billPrefix != '' ">
                    BILL_PREFIX = #{billPrefix} AND
                </if>
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>

    <update id="updateresSeqNoToNull" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        UPDATE RB_BAB_BILL
        <set>
            PLD_AMOUNT = null,
            RES_SEQ_NO = null,
        </set>
        <where>
            BILL_TYPE = #{billType} AND
            BILL_VOUCHER_NO = #{billVoucherNo} AND
            BILL_DOC_TYPE = #{billDocType} AND
            CLIENT_NO = #{clientNo}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </update>
    <update id="updateBillStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        UPDATE RB_BAB_BILL
        <set>
            <if test="lostNo != null and  lostNo != '' ">
                LOST_NO = #{lostNo},
            </if>
            <if test="billStatus != null and  billStatus != '' ">
                BILL_STATUS = #{billStatus},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>
    <select id="selectByLostNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL
        <where>
            LOST_NO = #{lostNo}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>


    <select id="selectRbBabBillByVoucherNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL
        <where>
            <trim suffixOverrides="AND">
                <if test="billStatus != null and  billStatus != '' ">
                    BILL_STATUS = #{billStatus} AND
                </if>
                <if test="acceptStatus != null and  acceptStatus != '' ">
                    ACCEPT_STATUS = #{acceptStatus} AND
                </if>
                <if test="billPrefix != null and  billPrefix != '' ">
                    BILL_PREFIX = #{billPrefix} AND
                </if>
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>


    <update id="updateBillLostNoToNull" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        UPDATE RB_BAB_BILL
        <set>
            LOST_NO = null,
            <if test="billStatus != null and  billStatus != '' ">
                BILL_STATUS = #{billStatus},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="billType != null and  billType != '' ">
                    BILL_TYPE = #{billType} AND
                </if>
                <if test="billVoucherNo != null and  billVoucherNo != '' ">
                    BILL_VOUCHER_NO = #{billVoucherNo} AND
                </if>
                <if test="billDocType != null and  billDocType != '' ">
                    BILL_DOC_TYPE = #{billDocType} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>
    <update id="updateStatusByAcceptContractNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill">
        UPDATE RB_BAB_BILL
        <set>
            <if test="newBillStatus != null and  newBillStatus != '' ">
                BILL_STATUS = #{newBillStatus},
            </if>
            <if test="newAcceptStatus != null and  newAcceptStatus != '' ">
                ACCEPT_STATUS = #{newAcceptStatus},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="oldBillStatus != null and  oldBillStatus != '' ">
                    BILL_STATUS = #{oldBillStatus} AND
                </if>
                <if test="oldAcceptStatus != null and  oldAcceptStatus != '' ">
                    ACCEPT_STATUS = #{oldAcceptStatus} AND
                </if>
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>

</mapper>
