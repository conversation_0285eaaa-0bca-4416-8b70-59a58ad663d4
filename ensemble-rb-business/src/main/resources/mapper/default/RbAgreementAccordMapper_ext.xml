<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">

	<select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		select <include refid="Base_Column"/>
		from RB_AGREEMENT_ACCORD
		where AGREEMENT_ID = #{agreementId}
		<if test="seqNo != null">
			AND SEQ_NO = #{seqNo}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		<if test="_databaseId == 'mysql'">
			ORDER BY (seq_no+0)
		</if>
		<if test="_databaseId == 'oracle'">
			ORDER BY to_number(seq_no)
		</if>
	</select>

	<select id="getMbAgreementAccordSplit" parameterType="java.util.Map" resultType="java.util.Map">
		<if test="_databaseId == 'mysql'">
			SELECT
			MIN(agreement_id) START_KEY,
			MAX(agreement_id) END_KEY,COUNT(1) ROW_COUNT
			FROM
			(
			SELECT
			DISTINCT agreement_id,
			@rownum :=@rownum + 1 AS rownum
			FROM
			RB_AGREEMENT_ACCORD,
			(SELECT @rownum := -1) t
			WHERE accord_prod_type='S'
<!-- 多法人改造 by LIYUANV -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			GROUP BY agreement_id
			ORDER BY agreement_id
			) tt
			GROUP BY
			FLOOR(
			tt.rownum /#{maxPerCount} )
		</if>
		<if test="_databaseId == 'oracle'">
			SELECT MIN (agreement_id) START_KEY, MAX (agreement_id) END_KEY,COUNT(1) ROW_COUNT
			FROM (SELECT DISTINCT agreement_id
			FROM RB_AGREEMENT_ACCORD
			WHERE accord_prod_type='S'
<!-- 多法人改造 by LIYUANV -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			GROUP BY agreement_id
			ORDER BY agreement_id)
			GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
		</if>
	</select>
	<select id="queryMbAgreementAccordForKeys" parameterType="java.util.Map" resultType="String">
		SELECT agreement_id
		FROM RB_AGREEMENT_ACCORD
		WHERE accord_prod_type='S'
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		  AND agreement_id BETWEEN #{startKey} and #{endKey}
		GROUP BY agreement_id
		ORDER BY agreement_id
	</select>

	<delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		delete from RB_AGREEMENT_ACCORD
		where AGREEMENT_ID = #{agreementId}
		<if test="seqNo != null">
			AND SEQ_NO = #{seqNo}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</delete>
	<update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		update RB_AGREEMENT_ACCORD
		<set>
			<if test="term != null">
				TERM = #{term},
			</if>
			<if test="termType != null">
				TERM_TYPE = #{termType},
			</if>
			<if test="accordProdType != null">
				ACCORD_PROD_TYPE = #{accordProdType},
			</if>
			<if test="nearAmt != null">
				NEAR_AMT = #{nearAmt},
			</if>
			<if test="intType != null">
				INT_TYPE = #{intType},
			</if>
			<if test="intClass != null">
				INT_CLASS = #{intClass},
			</if>
			<if test="actualRate != null">
				ACTUAL_RATE = #{actualRate},
			</if>
			<if test="floatRate != null">
				FLOAT_RATE = #{floatRate},
			</if>
			<if test="acctPercentRate != null">
				ACCT_PERCENT_RATE = #{acctPercentRate},
			</if>
			<if test="acctSpreadRate != null">
				ACCT_SPREAD_RATE = #{acctSpreadRate},
			</if>
			<if test="acctFixedRate != null">
				ACCT_FIXED_RATE = #{acctFixedRate},
			</if>
			<if test="tranTimestamp != null">
				TRAN_TIMESTAMP = #{tranTimestamp},
			</if>
			<if test="company != null">
				COMPANY = #{company},
			</if>
			<if test="baseAcctNo != null">
				BASE_ACCT_NO = #{baseAcctNo},
			</if>
			<if test="prodType != null">
				PROD_TYPE = #{prodType},
			</if>
			<if test="acctSeqNo != null">
				ACCT_SEQ_NO = #{acctSeqNo},
			</if>
			<if test="acctCcy != null">
				ACCT_CCY = #{acctCcy},
			</if>
			<if test="agreementStatus != null">
				AGREEMENT_STATUS = #{agreementStatus},
			</if>
			<if test="yearBasis != null">
				YEAR_BASIS = #{yearBasis},
			</if>
			<if test="startDate != null">
				START_DATE = #{startDate},
			</if>
			<if test="endDate != null">
				END_DATE = #{endDate}
			</if>
		</set>
		where AGREEMENT_ID = #{agreementId}
		AND SEQ_NO = #{seqNo}
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		insert into RB_AGREEMENT_ACCORD
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="agreementId != null">
				AGREEMENT_ID,
			</if>
			<if test="seqNo != null">
				SEQ_NO,
			</if>
			<if test="term != null">
				TERM,
			</if>
			<if test="termType != null">
				TERM_TYPE,
			</if>
			<if test="accordProdType != null">
				ACCORD_PROD_TYPE,
			</if>
			<if test="nearAmt != null">
				NEAR_AMT,
			</if>
			<if test="intType != null">
				INT_TYPE,
			</if>
			<if test="intClass != null">
				INT_CLASS,
			</if>
			<if test="actualRate != null">
				ACTUAL_RATE,
			</if>
			<if test="floatRate != null">
				FLOAT_RATE,
			</if>
			<if test="acctPercentRate != null">
				ACCT_PERCENT_RATE,
			</if>
			<if test="acctSpreadRate != null">
				ACCT_SPREAD_RATE,
			</if>
			<if test="acctFixedRate != null">
				ACCT_FIXED_RATE,
			</if>
			<if test="tranTimestamp != null">
				TRAN_TIMESTAMP,
			</if>
			<if test="company != null">
				COMPANY,
			</if>
			<if test="baseAcctNo != null">
				BASE_ACCT_NO,
			</if>
			<if test="prodType != null">
				PROD_TYPE,
			</if>
			<if test="acctSeqNo != null">
				ACCT_SEQ_NO,
			</if>
			<if test="acctCcy != null">
				ACCT_CCY,
			</if>
			<if test="agreementStatus != null">
				AGREEMENT_STATUS,
			</if>
			<if test="yearBasis != null">
				YEAR_BASIS,
			</if>
			<if test="startDate != null">
				START_DATE,
			</if>
			<if test="endDate != null">
				END_DATE,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="agreementId != null">
				#{agreementId},
			</if>
			<if test="seqNo != null">
				#{seqNo},
			</if>
			<if test="term != null">
				#{term},
			</if>
			<if test="termType != null">
				#{termType},
			</if>
			<if test="accordProdType != null">
				#{accordProdType},
			</if>
			<if test="nearAmt != null">
				#{nearAmt},
			</if>
			<if test="intType != null">
				#{intType},
			</if>
			<if test="intClass != null">
				#{intClass},
			</if>
			<if test="actualRate != null">
				#{actualRate},
			</if>
			<if test="floatRate != null">
				#{floatRate},
			</if>
			<if test="acctPercentRate != null">
				#{acctPercentRate},
			</if>
			<if test="acctSpreadRate != null">
				#{acctSpreadRate},
			</if>
			<if test="acctFixedRate != null">
				#{acctFixedRate},
			</if>
			<if test="tranTimestamp != null">
				#{tranTimestamp},
			</if>
			<if test="company != null">
				#{company},
			</if>
			<if test="baseAcctNo != null">
				#{baseAcctNo},
			</if>
			<if test="prodType != null">
				#{prodType},
			</if>
			<if test="acctSeqNo != null">
				#{acctSeqNo},
			</if>
			<if test="acctCcy != null">
				#{acctCcy},
			</if>
			<if test="agreementType != null">
				#{agreementType},
			</if>
			<if test="agreementStatus != null">
				#{agreementStatus},
			</if>
			<if test="yearBasis != null">
				#{yearBasis},
			</if>
			<if test="startDate != null">
				#{startDate},
			</if>
			<if test="endDate != null">
				#{endDate},
			</if>
		</trim>
	</insert>
	<select id="selectAgrByType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_ACCORD
		where AGREEMENT_STATUS='A'
		<if test="baseAcctNo != null and baseAcctNo != ''">
			and BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="prodType != null and prodType != ''">
			and PROD_TYPE = #{prodType}
		</if>
		<if test="acctCcy != null and acctCcy != ''">
			and ACCT_CCY = #{acctCcy}
		</if>
		<if test="acctSeqNo != null and acctSeqNo != ''">
			and ACCT_SEQ_NO = #{acctSeqNo}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>

<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
        ORDER BY NEAR_AMT DESC
	</select>


	<select id="selectAgreementAccordInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_ACCORD
		where AGREEMENT_STATUS='A'
		<if test="internalKey != null and internalKey != ''">
			and INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>

	</select>

	<select id="selectAgrByTypeOne" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		select
		<include refid="Base_Column"/>
		FROM RB_AGREEMENT_ACCORD A
		WHERE A.NEAR_AMT =
		(SELECT MAX(NEAR_AMT) FROM RB_AGREEMENT_ACCORD
		where AGREEMENT_STATUS='A'
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		<if test="baseAcctNo != null and baseAcctNo != ''">
			and BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="prodType != null and prodType != ''">
			and PROD_TYPE = #{prodType}
		</if>
		<if test="acctCcy != null and acctCcy != ''">
			and ACCT_CCY = #{acctCcy}
		</if>
		<if test="acctSeqNo != null and acctSeqNo != ''">
			and ACCT_SEQ_NO = #{acctSeqNo}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>
		)
		<if test="baseAcctNo != null and baseAcctNo != ''">
		and BASE_ACCT_NO = #{baseAcctNo}
	    </if>
		<if test="prodType != null and prodType != ''">
			and PROD_TYPE = #{prodType}
		</if>
		<if test="acctCcy != null and acctCcy != ''">
			and ACCT_CCY = #{acctCcy}
		</if>
		<if test="acctSeqNo != null and acctSeqNo != ''">
			and ACCT_SEQ_NO = #{acctSeqNo}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>
		and AGREEMENT_STATUS='A'
	</select>

	<select id="selectAgrByFormNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_ACCORD
		where AGREEMENT_STATUS='A'
		<if test="baseAcctNo != null and baseAcctNo != ''">
			and BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="prodType != null and prodType != ''">
			and PROD_TYPE = #{prodType}
		</if>
		<if test="acctCcy != null and acctCcy != ''">
			and ACCT_CCY = #{acctCcy}
		</if>
		<if test="acctSeqNo != null and acctSeqNo != ''">
			and ACCT_SEQ_NO = #{acctSeqNo}
		</if>
		<if test="intRateFormNo != null and intRateFormNo != ''">
			and INT_RATE_FORM_NO = #{intRateFormNo}
		</if>

<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY NEAR_AMT DESC
	</select>

	<select id="getIntRateForm" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_ACCORD
		where INT_RATE_FORM_NO is not null
		<if test="baseAcctNo != null and baseAcctNo != ''">
			and BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>
		and AGREEMENT_STATUS='A'
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY NEAR_AMT DESC
	</select>

	<select id="getDaysBefore" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_ACCORD
		where END_DATE <![CDATA[ >= ]]> #{targetDate,jdbcType=DATE}
	</select>
</mapper>