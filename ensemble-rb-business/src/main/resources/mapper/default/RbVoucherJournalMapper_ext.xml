<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal">
  <!-- Created by <PERSON><PERSON>j<PERSON><PERSON> on 2018/12/05 15:48:00. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal">
    select *
    from RB_VOUCHER_JOURNAL
    where VOUCHER_JOURNAL_ID = #{voucherJournalId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal">
    delete from RB_VOUCHER_JOURNAL
    where VOUCHER_JOURNAL_ID = #{voucherJournalId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal">
    update RB_VOUCHER_JOURNAL
    <set>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType},
      </if>
      <if test="prefix != null">
        PREFIX = #{prefix},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo},
      </if>
      <if test="cardNo != null">
        CARD_NO = #{cardNo},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount},
      </if>
      <if test="voucherStatus != null">
        VOUCHER_STATUS = #{voucherStatus},
      </if>
      <if test="oldStatus != null">
        OLD_STATUS = #{oldStatus},
      </if>
      <if test="programId != null">
        PROGRAM_ID = #{programId},
      </if>
      <if test="tranDesc != null">
        TRAN_DESC = #{tranDesc},
      </if>
      <if test="moduleId != null">
        MODULE_ID = #{moduleId},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO = #{channelSeqNo},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="checkUserId != null">
        CHECK_USER_ID = #{checkUserId},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="canReasonCode != null">
        CAN_REASON_CODE = #{canReasonCode},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where VOUCHER_JOURNAL_ID = #{voucherJournalId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal">
    insert into RB_VOUCHER_JOURNAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="voucherJournalId != null">
        VOUCHER_JOURNAL_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="prefix != null">
        PREFIX,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="cardNo != null">
        CARD_NO,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="voucherStatus != null">
        VOUCHER_STATUS,
      </if>
      <if test="oldStatus != null">
        OLD_STATUS,
      </if>
      <if test="programId != null">
        PROGRAM_ID,
      </if>
      <if test="tranDesc != null">
        TRAN_DESC,
      </if>
      <if test="moduleId != null">
        MODULE_ID,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="checkUserId != null">
        CHECK_USER_ID,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="canReasonCode != null">
        CAN_REASON_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="voucherJournalId != null">
        #{voucherJournalId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
      <if test="prefix != null">
        #{prefix},
      </if>
      <if test="voucherNo != null">
        #{voucherNo},
      </if>
      <if test="cardNo != null">
        #{cardNo},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="amount != null">
        #{amount},
      </if>
      <if test="voucherStatus != null">
        #{voucherStatus},
      </if>
      <if test="oldStatus != null">
        #{oldStatus},
      </if>
      <if test="programId != null">
        #{programId},
      </if>
      <if test="tranDesc != null">
        #{tranDesc},
      </if>
      <if test="moduleId != null">
        #{moduleId},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="checkUserId != null">
        #{checkUserId},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="canReasonCode != null">
        #{canReasonCode},
      </if>
    </trim>
  </insert>
  <select id="getRbVoucherByRefAndSeq" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal">
    SELECT *
    FROM RB_VOUCHER_JOURNAL
    WHERE REFERENCE = #{reference}
    <if test="baseAcctNo != null and baseAcctNo !=''">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="cardNo != null and cardNo !=''">
      and CARD_NO = #{cardNo}
    </if>
    <if test="clientNo != null and clientNo !=''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="channelSeqNo != null and channelSeqNo !=''">
      and CHANNEL_SEQ_NO = #{channelSeqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getRbVoucherList" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherBusiModel">
    SELECT
    j.DOC_TYPE DOC_TYPE,
    j.VOUCHER_NO VOUCHER_NO,
    j.VOUCHER_STATUS VOUCHER_STATUS,
    j.BASE_ACCT_NO BASE_ACCT_NO,
    j.CARD_NO CARD_NO,
    j.VOUCHER_STATUS TRAN_EVENT,
    j.TRAN_DATE TRAN_DATE,
    j.TRAN_BRANCH TRAN_BRANCH,
    j.USER_ID USER_ID,
    j.OLD_STATUS OLD_STATUS
    FROM RB_VOUCHER_JOURNAL j
    WHERE 1=1
    <if test="baseAcctNo != null and baseAcctNo !=''">
      and (j.BASE_ACCT_NO = #{baseAcctNo}  or j.CARD_NO = #{baseAcctNo})
    </if>
    <if test="docType != null and docType !=''">
      and j.DOC_TYPE = #{docType}
    </if>
    <if test="prefix != null and prefix !=''">
      and j.PREFIX = #{prefix}
    </if>
    <if test="voucherNo != null and voucherNo !=''">
      and j.VOUCHER_NO = #{voucherNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by  j.TRAN_DATE desc
  </select>
</mapper>
