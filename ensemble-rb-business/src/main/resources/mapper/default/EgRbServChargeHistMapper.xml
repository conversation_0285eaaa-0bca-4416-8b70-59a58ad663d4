<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.cm.reg.EgRbServChargeHist">

  <sql id="Table_Name">
      ENS_EG.EG_RB_SERV_CHARGE_HIST
  </sql>

  <sql id="Base_Column">
    <trim suffixOverrides=",">
            SC_SEQ_NO,
            TRAN_BRANCH,
            ACCT_<PERSON>ANCH,
            TRAN_DATE,
            REFERENCE,
            CHANNEL_SEQ_NO,
            HIST_SEQ_NO,
            PROD_TYPE,
            AMT_TYPE,
            TRAN_TYPE,
            FEE_AMT,
            SOURCE_TYPE,
            REVERSAL_FLAG,
            REVERSAL_DATE,
            REVERSAL_SEQ_NO,
             DEAL_FLAG,
    </trim>
  </sql>

  <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.model.cm.reg.EgRbServChargeHist">
        <result property="scSeqNo" column="SC_SEQ_NO"/>
        <result property="tranBranch" column="TRAN_BRANCH"/>
        <result property="acctBranch" column="ACCT_BRANCH"/>
        <result property="tranDate" column="TRAN_DATE"/>
        <result property="reference" column="REFERENCE"/>
        <result property="channelSeqNo" column="CHANNEL_SEQ_NO"/>
        <result property="histSeqNo" column="HIST_SEQ_NO"/>
        <result property="prodType" column="PROD_TYPE"/>
        <result property="amtType" column="AMT_TYPE"/>
        <result property="tranType" column="TRAN_TYPE"/>
        <result property="feeAmt" column="FEE_AMT"/>
        <result property="sourceType" column="SOURCE_TYPE"/>
        <result property="reversalFlag" column="REVERSAL_FLAG"/>
        <result property="reversalDate" column="REVERSAL_DATE"/>
        <result property="reversalSeqNo" column="REVERSAL_SEQ_NO"/>
        <result property="dealFlag" column="DEAL_FLAG"/>
  </resultMap>

  <sql id="Base_Where">
    <where>
            <if test="scSeqNo != null and  scSeqNo != '' ">
          AND SC_SEQ_NO = #{scSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="tranBranch != null and  tranBranch != '' ">
          AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
        </if>
            <if test="acctBranch != null and  acctBranch != '' ">
          AND ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR}
        </if>
            <if test="tranDate != null ">
          AND TRAN_DATE = #{tranDate,jdbcType=DATE}
        </if>
            <if test="reference != null and  reference != '' ">
          AND REFERENCE = #{reference,jdbcType=VARCHAR}
        </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
          AND CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="histSeqNo != null and  histSeqNo != '' ">
          AND HIST_SEQ_NO = #{histSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="prodType != null and  prodType != '' ">
          AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
        </if>
            <if test="amtType != null and  amtType != '' ">
          AND AMT_TYPE = #{amtType,jdbcType=VARCHAR}
        </if>
            <if test="tranType != null and  tranType != '' ">
          AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
        </if>
            <if test="feeAmt != null ">
          AND FEE_AMT = #{feeAmt,jdbcType=DECIMAL}
        </if>
            <if test="sourceType != null and  sourceType != '' ">
          AND SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
        </if>
            <if test="reversalFlag != null and  reversalFlag != '' ">
          AND REVERSAL_FLAG = #{reversalFlag,jdbcType=VARCHAR}
        </if>
            <if test="reversalDate != null ">
          AND REVERSAL_DATE = #{reversalDate,jdbcType=DATE}
        </if>
            <if test="reversalSeqNo != null and  reversalSeqNo != '' ">
          AND REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="dealFlag != null and  dealFlag != '' ">
          AND DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR}
        </if>
    </where>
  </sql>

  <sql id="PrimaryKey_Where">
    <where>
    </where>
  </sql>

  <sql id="Base_Set">
    <set>
                <if test="scSeqNo != null and scSeqNo != '' ">
                SC_SEQ_NO = #{scSeqNo,jdbcType=VARCHAR},
            </if>
                <if test="tranBranch != null and tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
            </if>
                <if test="acctBranch != null and acctBranch != '' ">
                ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR},
            </if>
                <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
                <if test="reference != null and reference != '' ">
                REFERENCE = #{reference,jdbcType=VARCHAR},
            </if>
                <if test="channelSeqNo != null and channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR},
            </if>
                <if test="histSeqNo != null and histSeqNo != '' ">
                HIST_SEQ_NO = #{histSeqNo,jdbcType=VARCHAR},
            </if>
                <if test="prodType != null and prodType != '' ">
                PROD_TYPE = #{prodType,jdbcType=VARCHAR},
            </if>
                <if test="amtType != null and amtType != '' ">
                AMT_TYPE = #{amtType,jdbcType=VARCHAR},
            </if>
                <if test="tranType != null and tranType != '' ">
                TRAN_TYPE = #{tranType,jdbcType=VARCHAR},
            </if>
                <if test="feeAmt != null ">
                FEE_AMT = #{feeAmt,jdbcType=DECIMAL},
            </if>
                <if test="sourceType != null and sourceType != '' ">
                SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
            </if>
                <if test="reversalFlag != null and reversalFlag != '' ">
                REVERSAL_FLAG = #{reversalFlag,jdbcType=VARCHAR},
            </if>
                <if test="reversalDate != null ">
                REVERSAL_DATE = #{reversalDate,jdbcType=DATE},
            </if>
                <if test="reversalSeqNo != null and reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR},
            </if>
                <if test="dealFlag != null and dealFlag != '' ">
                DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR},
            </if>
    </set>
  </sql>

  <sql id="Base_Select">
    SELECT
    <include refid="Base_Column"/>
    FROM
    <include refid="Table_Name"/>
    <include refid="Base_Where"/>
  </sql>

  <sql id="comet_step_column">
    <if test="cometSqlType == 'segment'"> ${cometKeyField} as KEY_FIELD</if>
    <if test="cometSqlType == 'page'"> *</if>
  </sql>

  <sql id="comet_step_where">
    <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}</if>
  </sql>

  <sql id="comet_row_num_step_column">
    <if test="cometSqlType == 'total'"> count(*) AS TOTAL</if>
    <if test="cometSqlType == 'offset'"> *</if>
  </sql>

  <insert id="insert">
    INSERT INTO
    <include refid="Table_Name"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="scSeqNo != null ">SC_SEQ_NO,</if>
          <if test="tranBranch != null ">TRAN_BRANCH,</if>
          <if test="acctBranch != null ">ACCT_BRANCH,</if>
          <if test="tranDate != null ">TRAN_DATE,</if>
          <if test="reference != null ">REFERENCE,</if>
          <if test="channelSeqNo != null ">CHANNEL_SEQ_NO,</if>
          <if test="histSeqNo != null ">HIST_SEQ_NO,</if>
          <if test="prodType != null ">PROD_TYPE,</if>
          <if test="amtType != null ">AMT_TYPE,</if>
          <if test="tranType != null ">TRAN_TYPE,</if>
          <if test="feeAmt != null ">FEE_AMT,</if>
          <if test="sourceType != null ">SOURCE_TYPE,</if>
          <if test="reversalFlag != null ">REVERSAL_FLAG,</if>
          <if test="reversalDate != null ">REVERSAL_DATE,</if>
          <if test="reversalSeqNo != null ">REVERSAL_SEQ_NO,</if>
          <if test="dealFlag != null ">DEAL_FLAG,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="scSeqNo != null ">#{scSeqNo,jdbcType=VARCHAR},</if>
          <if test="tranBranch != null ">#{tranBranch,jdbcType=VARCHAR},</if>
          <if test="acctBranch != null ">#{acctBranch,jdbcType=VARCHAR},</if>
          <if test="tranDate != null ">#{tranDate,jdbcType=DATE},</if>
          <if test="reference != null ">#{reference,jdbcType=VARCHAR},</if>
          <if test="channelSeqNo != null ">#{channelSeqNo,jdbcType=VARCHAR},</if>
          <if test="histSeqNo != null ">#{histSeqNo,jdbcType=VARCHAR},</if>
          <if test="prodType != null ">#{prodType,jdbcType=VARCHAR},</if>
          <if test="amtType != null ">#{amtType,jdbcType=VARCHAR},</if>
          <if test="tranType != null ">#{tranType,jdbcType=VARCHAR},</if>
          <if test="feeAmt != null ">#{feeAmt,jdbcType=DECIMAL},</if>
          <if test="sourceType != null ">#{sourceType,jdbcType=VARCHAR},</if>
          <if test="reversalFlag != null ">#{reversalFlag,jdbcType=VARCHAR},</if>
          <if test="reversalDate != null ">#{reversalDate,jdbcType=DATE},</if>
          <if test="reversalSeqNo != null ">#{reversalSeqNo,jdbcType=VARCHAR},</if>
          <if test="dealFlag != null ">#{dealFlag,jdbcType=VARCHAR},</if>
    </trim>
  </insert>

  <update id="update">
    UPDATE
    <include refid="Table_Name"/>
    <include refid="Base_Set"/>
    <include refid="PrimaryKey_Where"/>
  </update>

  <update id="updateByPrimaryKey">
    UPDATE
    <include refid="Table_Name"/>
    <include refid="Base_Set"/>
    <include refid="PrimaryKey_Where"/>
  </update>


  <update id="updateByEntity">
    UPDATE
    <include refid="Table_Name"/>
    <set>
                <if test="s.scSeqNo != null and s.scSeqNo != '' ">
                SC_SEQ_NO = #{s.scSeqNo,jdbcType=VARCHAR},
            </if>
                <if test="s.tranBranch != null and s.tranBranch != '' ">
                TRAN_BRANCH = #{s.tranBranch,jdbcType=VARCHAR},
            </if>
                <if test="s.acctBranch != null and s.acctBranch != '' ">
                ACCT_BRANCH = #{s.acctBranch,jdbcType=VARCHAR},
            </if>
                <if test="s.tranDate != null ">
                TRAN_DATE = #{s.tranDate,jdbcType=DATE},
            </if>
                <if test="s.reference != null and s.reference != '' ">
                REFERENCE = #{s.reference,jdbcType=VARCHAR},
            </if>
                <if test="s.channelSeqNo != null and s.channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{s.channelSeqNo,jdbcType=VARCHAR},
            </if>
                <if test="s.histSeqNo != null and s.histSeqNo != '' ">
                HIST_SEQ_NO = #{s.histSeqNo,jdbcType=VARCHAR},
            </if>
                <if test="s.prodType != null and s.prodType != '' ">
                PROD_TYPE = #{s.prodType,jdbcType=VARCHAR},
            </if>
                <if test="s.amtType != null and s.amtType != '' ">
                AMT_TYPE = #{s.amtType,jdbcType=VARCHAR},
            </if>
                <if test="s.tranType != null and s.tranType != '' ">
                TRAN_TYPE = #{s.tranType,jdbcType=VARCHAR},
            </if>
                <if test="s.feeAmt != null ">
                FEE_AMT = #{s.feeAmt,jdbcType=DECIMAL},
            </if>
                <if test="s.sourceType != null and s.sourceType != '' ">
                SOURCE_TYPE = #{s.sourceType,jdbcType=VARCHAR},
            </if>
                <if test="s.reversalFlag != null and s.reversalFlag != '' ">
                REVERSAL_FLAG = #{s.reversalFlag,jdbcType=VARCHAR},
            </if>
                <if test="s.reversalDate != null ">
                REVERSAL_DATE = #{s.reversalDate,jdbcType=DATE},
            </if>
                <if test="s.reversalSeqNo != null and s.reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{s.reversalSeqNo,jdbcType=VARCHAR},
            </if>

                <if test="s.dealFlag != null and s.dealFlag != '' ">
                DEAL_FLAG = #{s.dealFlag,jdbcType=VARCHAR},
            </if>
    </set>
    <where>
            <if test="w.scSeqNo != null and w.scSeqNo != '' ">
          AND SC_SEQ_NO = #{w.scSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="w.tranBranch != null and w.tranBranch != '' ">
          AND TRAN_BRANCH = #{w.tranBranch,jdbcType=VARCHAR}
        </if>
            <if test="w.acctBranch != null and w.acctBranch != '' ">
          AND ACCT_BRANCH = #{w.acctBranch,jdbcType=VARCHAR}
        </if>
            <if test="w.tranDate != null ">
          AND TRAN_DATE = #{w.tranDate,jdbcType=DATE}
        </if>
            <if test="w.reference != null and w.reference != '' ">
          AND REFERENCE = #{w.reference,jdbcType=VARCHAR}
        </if>
            <if test="w.channelSeqNo != null and w.channelSeqNo != '' ">
          AND CHANNEL_SEQ_NO = #{w.channelSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="w.histSeqNo != null and w.histSeqNo != '' ">
          AND HIST_SEQ_NO = #{w.histSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="w.prodType != null and w.prodType != '' ">
          AND PROD_TYPE = #{w.prodType,jdbcType=VARCHAR}
        </if>
            <if test="w.amtType != null and w.amtType != '' ">
          AND AMT_TYPE = #{w.amtType,jdbcType=VARCHAR}
        </if>
            <if test="w.tranType != null and w.tranType != '' ">
          AND TRAN_TYPE = #{w.tranType,jdbcType=VARCHAR}
        </if>
            <if test="w.feeAmt != null ">
          AND FEE_AMT = #{w.feeAmt,jdbcType=DECIMAL}
        </if>
            <if test="w.sourceType != null and w.sourceType != '' ">
          AND SOURCE_TYPE = #{w.sourceType,jdbcType=VARCHAR}
        </if>
            <if test="w.reversalFlag != null and w.reversalFlag != '' ">
          AND REVERSAL_FLAG = #{w.reversalFlag,jdbcType=VARCHAR}
        </if>
            <if test="w.reversalDate != null ">
          AND REVERSAL_DATE = #{w.reversalDate,jdbcType=DATE}
        </if>
            <if test="w.reversalSeqNo != null and w.reversalSeqNo != '' ">
          AND REVERSAL_SEQ_NO = #{w.reversalSeqNo,jdbcType=VARCHAR}
        </if>
            <if test="w.dealFlag != null and w.dealFlag != '' ">
          AND DEAL_FLAG = #{w.dealFlag,jdbcType=VARCHAR}
        </if>
    </where>
  </update>


  <delete id="delete">
    DELETE FROM
    <include refid="Table_Name"/>
    <include refid="Base_Where"/>
  </delete>

  <select id="count" parameterType="java.util.Map" resultType="int">
    SELECT count(1) FROM
    <include refid="Table_Name"/>
    <include refid="Base_Where"/>
  </select>

  <select id="selectOne" resultMap="Base_Result_Map">
    <include refid="Base_Select"/>
  </select>

  <select id="selectList" resultMap="Base_Result_Map">
    <include refid="Base_Select"/>
  </select>

  <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
    <include refid="Base_Select"/>
    FOR UPDATE
  </select>

  <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
    SELECT
    <include refid="Base_Column"/>
    FROM
    <include refid="Table_Name"/>
    <include refid="PrimaryKey_Where"/>
  </select>

</mapper>
