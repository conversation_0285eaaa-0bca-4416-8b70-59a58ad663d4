<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGrandHist">

  <select id="selectLastHistByDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGrandHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGrandHist">
    select
    <include refid="Base_Column"/>
    from RB_PCP_GRAND_HIST
    where INTERNAL_KEY = #{internalkey}
    AND TRAN_DATE = #{tranDate,jdbcType=DATE}
    AND PCP_GROUP_ID = #{pcpGroupId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByKeyAndDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGrandHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGrandHist">
    select
    <include refid="Base_Column"/>
    from RB_PCP_GRAND_HIST
    where INTERNAL_KEY = #{internalkey}
    AND TRAN_DATE = #{tranDate,jdbcType=DATE}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
