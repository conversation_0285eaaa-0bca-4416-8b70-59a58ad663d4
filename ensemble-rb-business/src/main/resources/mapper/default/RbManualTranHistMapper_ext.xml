<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist">
        select
        <include refid="Base_Column"/>
        from RB_MANUAL_TRAN_HIST
        where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist">
        select
        <include refid="Base_Column"/>
        from RB_MANUAL_TRAN_HIST
        where REFERENCE = #{reference}
        and REVERSAL = 'N'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectMbManualTranHist" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist">
        select
        <include refid="Base_Column"/>
        from RB_MANUAL_TRAN_HIST
        where VALUE_DATE BETWEEN #{fromDate} AND #{toDate}
        AND (MANUAL_STATUS = 'N' or MANUAL_STATUS is null)
        AND SEQ_NO BETWEEN #{startKey} and #{endKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist">
    delete from RB_MANUAL_TRAN_HIST
    where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist">
        update RB_MANUAL_TRAN_HIST
        <set>
            <if test="glSeqNo != null">
                GL_SEQ_NO = #{glSeqNo},
            </if>
            <if test="glClientNo != null">
                GL_CLIENT_NO = #{glClientNo},
            </if>
            <if test="glCode != null">
                GL_CODE = #{glCode},
            </if>
            <if test="glCcy != null">
                GL_CCY = #{glCcy},
            </if>
            <if test="glProfitCenter != null">
                GL_PROFIT_CENTRE = #{glProfitCenter},
            </if>
            <if test="glBranch != null">
                GL_BRANCH = #{glBranch},
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="postDate != null">
                POST_DATE = #{postDate},
            </if>
            <if test="valueDate != null">
                VALUE_DATE = #{valueDate},
            </if>
            <if test="tranAmt != null">
                TRAN_AMT = #{tranAmt},
            </if>
            <if test="ccy != null">
                CCY = #{ccy},
            </if>
            <if test="reference != null">
                REFERENCE = #{reference},
            </if>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO = #{channelSeqNo},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="userId != null">
                USER_ID = #{userId},
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO = #{bankSeqNo},
            </if>
            <if test="reversal != null">
                REVERSAL = #{reversal},
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE = #{sourceType},
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE = #{sourceModule},
            </if>
            <if test="manualStatus != null">
                MANUAL_STATUS = #{manualStatus},
            </if>
            <if test="businessUnit != null">
                BUSINESS_UNIT = #{businessUnit},
            </if>
            <if test="crDrInd != null">
                CR_DR_IND = #{crDrInd},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbManualTranHist">
        insert into RB_MANUAL_TRAN_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                SEQ_NO,
            </if>
            <if test="glSeqNo != null">
                GL_SEQ_NO,
            </if>
            <if test="glClientNo != null">
                GL_CLIENT_NO,
            </if>
            <if test="glCode != null">
                GL_CODE,
            </if>
            <if test="glCcy != null">
                GL_CCY,
            </if>
            <if test="glProfitCenter != null">
                GL_PROFIT_CENTRE,
            </if>
            <if test="glBranch != null">
                GL_BRANCH,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="postDate != null">
                POST_DATE,
            </if>
            <if test="valueDate != null">
                VALUE_DATE,
            </if>
            <if test="tranAmt != null">
                TRAN_AMT,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO,
            </if>
            <if test="reversal != null">
                REVERSAL,
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE,
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE,
            </if>
            <if test="manualStatus != null">
                MANUAL_STATUS,
            </if>
            <if test="businessUnit != null">
                BUSINESS_UNIT,
            </if>
            <if test="crDrInd != null">
                CR_DR_IND,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                #{seqNo},
            </if>
            <if test="glSeqNo != null">
                #{glSeqNo},
            </if>
            <if test="glClientNo != null">
                #{glClientNo},
            </if>
            <if test="glCode != null">
                #{glCode},
            </if>
            <if test="glCcy != null">
                #{glCcy},
            </if>
            <if test="glProfitCenter != null">
                #{glProfitCenter},
            </if>
            <if test="glBranch != null">
                #{glBranch},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="postDate != null">
                #{postDate},
            </if>
            <if test="valueDate != null">
                #{valueDate},
            </if>
            <if test="tranAmt != null">
                #{tranAmt},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="channelSeqNo != null">
                #{channelSeqNo},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="bankSeqNo != null">
                #{bankSeqNo},
            </if>
            <if test="reversal != null">
                #{reversal},
            </if>
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="sourceModule != null">
                #{sourceModule},
            </if>
            <if test="manualStatus != null">
                #{manualStatus},
            </if>
            <if test="businessUnit != null">
                #{businessUnit},
            </if>
            <if test="crDrInd != null">
                #{crDrInd},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
</mapper>
