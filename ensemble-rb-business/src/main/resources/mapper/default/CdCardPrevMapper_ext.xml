<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
	<!--多法人改造 by LIYUANV-->
	<select id="getRegularCardByApplyNo" parameterType="java.util.Map" resultType="java.util.Map">
		select
		min(substr(ca.CARD_NO,0,18)) minCardNoKey ,
		max(substr(ca.CARD_NO,0,18)) maxCardNoKey,
		count(1) totalNum,
		cr.DOC_TYPE docType
		from CD_CARD_PREV ca,CD_MAKE_CARD_REG cr
		where  ca.APPLY_NO = cr.APPLY_NO
		AND ca.APPLY_NO = #{applyNo}
		AND cr.APPLY_DATE = #{applyDate}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		GROUP BY cr.DOC_TYPE
	</select>

	<!--多法人改造 by LIYUANV-->
	<select id="getCardPrevByApply" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select
		<include refid="Base_Column"/>
		from CD_CARD_PREV
		where APPLY_NO in (
		select APPLY_NO from CD_MAKE_CARD_REG
		where APPLY_NO = #{applyNo}
		AND APPLY_DATE = #{applyDate}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		)
		GROUP BY DOC_TYPE,CARD_NO
	</select>

	<!--多法人改造 by LIYUANV-->
	<select id="getCardByVoucherNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select <include refid="Base_Column"/>
		from CD_CARD_PREV
		where CARD_NO like '%${voucherNo}%'
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<!--多法人改造 by LIYUANV-->
	<update id="updateByApplyNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		update CD_CARD_PREV
		<set>
			<if test="batchJobNo != null">
				BATCH_JOB_NO = #{batchJobNo},
			</if>
		</set>
		where APPLY_NO = #{applyNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<!--多法人改造 by LIYUANV-->
	<select id="getMaxCardNoByBatchJobNo" parameterType="java.util.Map" resultType="java.util.Map">
		select max(CARD_NO) as MAX_CARD_NO,min(CARD_NO) as MIN_CARD_NO
		from CD_CARD_PREV
		where BATCH_JOB_NO = #{batchJobNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<!--多法人改造 by LIYUANV-->
	<select id="getMaxCardNoByBatchJobNoApplyNo" parameterType="java.util.Map" resultType="java.util.Map">
		select max(CARD_NO) as MAX_CARD_NO,min(CARD_NO) as MIN_CARD_NO
		from CD_CARD_PREV
		where BATCH_JOB_NO = #{batchJobNo} and APPLY_NO = #{applyNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<!--多法人改造 by LIYUANV-->
	<update id="updateByBatchJobNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		update CD_CARD_PREV
		<set>
			<if test="cardStatus != null">
				CARD_STATUS = #{cardStatus},
			</if>
		</set>
		where BATCH_JOB_NO = #{batchJobNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<!--多法人改造 by LIYUANV-->
	<update id="updateByBatchJobNoApplyNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		update CD_CARD_PREV
		<set>
			<if test="cardStatus != null">
				CARD_STATUS = #{cardStatus},
			</if>
		</set>
		where BATCH_JOB_NO = #{batchJobNo} and APPLY_NO = #{applyNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<select id="getCardInfoByBatchApplyNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select
		<include refid="Base_Column"/>
		from CD_CARD_PREV
		where APPLY_NO = #{applyNo}
		<if test="tranBranch != null and tranBranch != ''">
			AND TRAN_BRANCH = #{tranBranch}
		</if>
		<if test="cardVoucherStatus != null and cardVoucherStatus != ''">
			AND Card_Voucher_Status = #{cardVoucherStatus}
		</if>
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		order by CARD_NO
	</select>

	<update id="updateCdCardPrevStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		update CD_CARD_PREV
		<set>
			CARD_VOUCHER_STATUS = '2'
		</set>
		<where>
			<if test="startNo != null and startNo != ''  ">
				and SUBSTR(CARD_NO, 0, 18) = #{startNo}
			</if>
			<if test="applyNo != null and applyNo != '' ">
				and APPLY_NO = #{applyNo}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</update>

	<select id="getUnSignLuckyCard" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select <include refid="Base_Column"/>
		from CD_CARD_PREV
		where
		card_voucher_status = #{cardVoucherStatus}
		<if test="applyNo != null and applyNo != ''">
			AND APPLY_NO = #{applyNo}
		</if>
		<if test="prodType != null and prodType != ''">
			AND prod_Type = #{prodType}
		</if>
		<if test="cardNo != null and cardNo != ''">
			AND card_No = #{cardNo}
		</if>
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		order by APPLY_NO,CARD_NO
	</select>

	<select id="getCardInfoByApplyNos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select <include refid="Base_Column"/>
		from CD_CARD_PREV
		where APPLY_NO IN
		<foreach item="item" index="index" collection="applyNos" open="(" separator="," close=")">
			#{item}
		</foreach>
		order by APPLY_NO
	</select>

	<select id="getCardInfoByClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select <include refid="Base_Column"/>
		from CD_CARD_PREV
		where CLIENT_NO = #{clientNo}
	</select>

	<select id="getCdCardPrevByCardNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select
		<include refid="Base_Column"/>
		from CD_CARD_PREV
		where APPLY_NO = #{applyNo}
		<if test="cardNo != null and cardNo != '' ">
			and SUBSTR(CARD_NO, 0, 18) = #{cardNo}
		</if>
	</select>

	<select id="getSmallestVoucherNoInTailBox" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardPrev">
		select
		<include refid="Base_Column"/>
		from cd_card_prev a
		where PROD_TYPE = #{prodType}
		<if test="voucherList != null">
			and substr(a.card_no,1,18) in
			<foreach item="item" index="index" collection="voucherList" open="(" separator="," close=")">
				#{item.voucherStartNo}
			</foreach>
		</if>
		order by card_no asc
	</select>

	<select id="getMaxMinCardNoByApplyNo" parameterType="java.util.Map"  resultType="java.util.Map">
		select max(CARD_NO) as MAX_CARD_NO,min(CARD_NO) as MIN_CARD_NO
		from CD_CARD_PREV
		where APPLY_NO = #{applyNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>