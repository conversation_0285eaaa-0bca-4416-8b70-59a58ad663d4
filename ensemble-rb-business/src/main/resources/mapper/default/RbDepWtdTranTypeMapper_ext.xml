<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">
    select
    <include refid="Base_Column"/>
    from RB_DEP_WTD_TRAN_TYPE
    where DEP_TRAN_TYPE = #{depTranType}
    <if test="wtdTranType != null">
      AND WTD_TRAN_TYPE = #{wtdTranType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectDepTranType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">

    select
    <include refid="Base_Column"/>
    from RB_DEP_WTD_TRAN_TYPE
    where WTD_TRAN_TYPE = #{wtdTranType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectWtdTranType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">

    select
    <include refid="Base_Column"/>
    from RB_DEP_WTD_TRAN_TYPE
    where DEP_TRAN_TYPE = #{depTranType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">
    delete from RB_DEP_WTD_TRAN_TYPE
    where DEP_TRAN_TYPE = #{depTranType}
    <if test="wtdTranType != null">
      AND WTD_TRAN_TYPE = #{wtdTranType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">
    update RB_DEP_WTD_TRAN_TYPE
    <set>

      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where DEP_TRAN_TYPE = #{depTranType}
    AND WTD_TRAN_TYPE = #{wtdTranType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">
    insert into RB_DEP_WTD_TRAN_TYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="depTranType != null">
        DEP_TRAN_TYPE,
      </if>
      <if test="wtdTranType != null">
        WTD_TRAN_TYPE,
      </if>

      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="depTranType != null">
        #{depTranType},
      </if>
      <if test="wtdTranType != null">
        #{wtdTranType},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
</mapper>

