<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
<sql id="Base_Column_List">
    INTERNAL_KEY,
    SETTLE_NO,
    EVENT_TYPE,
    REF_NO,
    REFERENCE,
    SETTLE_ACCT_CLASS,
    SETTLE_METHOD,
    PAY_REC_IND,
    TRAN_TYPE,
    AMT_TYPE,
    SETTLE_CLIENT,
    SETTLE_BANK_FLAG,
    SETTLE_BANK_NAME,
    SETTLE_BRANCH,
    SETTLE_ACCT_INTERNAL_KEY,
    SETTLE_BASE_ACCT_NO,
    SETTLE_ACCT_NAME,
    SETTLE_PROD_TYPE,
    SETTLE_ACCT_CCY,
    SETTLE_ACCT_SEQ_NO,
    SETTLE_CCY,
    SETTLE_AMT,
    SETTLE_XRATE,
    SETTLE_XRATE_ID,
    PRIORITY,
    SETTLE_WEIGHT,
    AUTO_BLOCKING,
    TRUSTED_PAY_NO,
    RESTRAINT_SEQ_NO,
    DAC_VALUE,
    COMPANY,
    TRAN_TIMESTAMP,
    PROFIT_RATIO,
    CONTRIBUTIVE_RATIO,
    IS_SELF_SUPPORT
  </sql>
  <select id="selectBySettleNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">

    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where  INTERNAL_KEY = #{internalKey}
    and SETTLE_NO = #{settleNo}
  </select>
  <select id="selectBySettleAcctInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where SETTLE_ACCT_INTERNAL_KEY = #{internalKey}
  </select>
  <select id="selectByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
  </select>
  <select id="selectByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where REFERENCE = #{reference}
  </select>
  <select id="selectAcctSettleDefault" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    and (REF_NO is null or REF_NO = '')
  </select>
  <select id="selectByReceiptNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    and REF_NO = #{refNo}
  </select>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Sep 10 18:46:06 CST 2015.
    -->
    insert into mm_acct_settle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="settleNo != null">
        SETTLE_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="eventType != null">
        EVENT_TYPE,
      </if>
      <if test="refNo != null">
        REF_NO,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="settleAcctClass != null">
        SETTLE_ACCT_CLASS,
      </if>
      <if test="settleMethod != null">
        SETTLE_METHOD,
      </if>
      <if test="payRecInd != null">
        PAY_REC_IND,
      </if>
      <if test="amtType != null">
        AMT_TYPE,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="settleClient != null">
        SETTLE_CLIENT,
      </if>
      <if test="settleBranch != null">
        SETTLE_BRANCH,
      </if>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY,
      </if>
      <if test="settleBaseAcctNo != null">
        SETTLE_BASE_ACCT_NO,
      </if>
      <if test="settleProdType != null">
        SETTLE_PROD_TYPE,
      </if>
      <if test="settleAcctCcy != null">
        SETTLE_ACCT_CCY,
      </if>
      <if test="settleAcctSeqNo != null">
        SETTLE_ACCT_SEQ_NO,
      </if>
      <if test="settleCcy != null">
        SETTLE_CCY,
      </if>
      <if test="settleAmt != null">
        SETTLE_AMT,
      </if>
      <if test="settleXrate != null">
        SETTLE_XRATE,
      </if>
      <if test="settleXrateId != null">
        SETTLE_XRATE_ID,
      </if>
      <if test="priority != null">
        PRIORITY,
      </if>
      <if test="settleWeight != null">
        SETTLE_WEIGHT,
      </if>
      <if test="autoBlocking != null">
        AUTO_BLOCKING,
      </if>
      <if test="trustedPayNo != null">
        TRUSTED_PAY_NO,
      </if>
      <if test="restraintSeqNo != null">
        RESTRAINT_SEQ_NO,
      </if>
      <if test="dacValue != null">
        DAC_VALUE,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="settleBankFlag != null">
        SETTLE_BANK_FLAG,
      </if>
      <if test="settleBankName != null">
        SETTLE_BANK_NAME,
      </if>
      <if test="settleAcctName != null">
        SETTLE_ACCT_NAME,
      </if>
      <if test="profitRatio != null">
        PROFIT_RATIO,
      </if>
      <if test="contributiveRatio != null">
        CONTRIBUTIVE_RATIO,
      </if>
      <if test="isSelfSupport != null">
        IS_SELF_SUPPORT,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="settleNo != null">
        #{settleNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="eventType != null">
        #{eventType},
      </if>
      <if test="refNo != null">
        #{refNo},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="settleAcctClass != null">
        #{settleAcctClass},
      </if>
      <if test="settleMethod != null">
        #{settleMethod},
      </if>
      <if test="payRecInd != null">
        #{payRecInd},
      </if>
      <if test="amtType != null">
        #{amtType},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="settleClient != null">
        #{settleClient},
      </if>
      <if test="settleBranch != null">
        #{settleBranch},
      </if>
      <if test="settleAcctInternalKey != null">
        #{settleAcctInternalKey},
      </if>
      <if test="settleBaseAcctNo != null">
        #{settleBaseAcctNo},
      </if>
      <if test="settleProdType != null">
        #{settleProdType},
      </if>
      <if test="settleAcctCcy != null">
        #{settleAcctCcy},
      </if>
      <if test="settleAcctSeqNo != null">
        #{settleAcctSeqNo},
      </if>
      <if test="settleCcy != null">
        #{settleCcy},
      </if>
      <if test="settleAmt != null">
        #{settleAmt},
      </if>
      <if test="settleXrate != null">
        #{settleXrate},
      </if>
      <if test="settleXrateId != null">
        #{settleXrateId},
      </if>
      <if test="priority != null">
        #{priority},
      </if>
      <if test="settleWeight != null">
        #{settleWeight},
      </if>
      <if test="autoBlocking != null">
        #{autoBlocking},
      </if>
      <if test="trustedPayNo != null">
        #{trustedPayNo},
      </if>
      <if test="restraintSeqNo != null">
        #{restraintSeqNo},
      </if>
      <if test="dacValue != null">
        #{dacValue},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="settleBankFlag != null">
        #{settleBankFlag},
      </if>
      <if test="settleBankName != null">
        #{settleBankName},
      </if>
      <if test="settleAcctName != null">
        #{settleAcctName},
      </if>
      <if test="profitRatio != null">
        #{profitRatio},
      </if>
      <if test="contributiveRatio != null">
        #{contributiveRatio},
      </if>
      <if test="isSelfSupport != null">
        #{isSelfSupport},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Sep 10 18:46:06 CST 2015.
    -->
    update mm_acct_settle
    <set>
      <if test="eventType != null">
        EVENT_TYPE = #{eventType},
      </if>
      <if test="refNo != null">
        REF_NO = #{refNo},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="settleAcctClass != null">
        SETTLE_ACCT_CLASS = #{settleAcctClass},
      </if>
      <if test="settleMethod != null">
        SETTLE_METHOD = #{settleMethod},
      </if>
      <if test="payRecInd != null">
        PAY_REC_IND = #{payRecInd},
      </if>
      <if test="amtType != null">
        AMT_TYPE = #{amtType},
      </if>
      <if test="settleClient != null">
        SETTLE_CLIENT = #{settleClient},
      </if>
      <if test="settleBranch != null">
        SETTLE_BRANCH = #{settleBranch},
      </if>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey},
      </if>
      <if test="settleBaseAcctNo != null">
        SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo},
      </if>
      <if test="settleProdType != null">
        SETTLE_PROD_TYPE = #{settleProdType},
      </if>
      <if test="settleAcctCcy != null">
        SETTLE_ACCT_CCY = #{settleAcctCcy},
      </if>
      <if test="settleAcctSeqNo != null">
        SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo},
      </if>
      <if test="settleCcy != null">
        SETTLE_CCY = #{settleCcy},
      </if>
      <if test="settleAmt != null">
        SETTLE_AMT = #{settleAmt},
      </if>
      <if test="settleXrate != null">
        SETTLE_XRATE = #{settleXrate},
      </if>
      <if test="settleXrateId != null">
        SETTLE_XRATE_ID = #{settleXrateId},
      </if>
      <if test="priority != null">
        PRIORITY = #{priority},
      </if>
      <if test="settleWeight != null">
        SETTLE_WEIGHT = #{settleWeight},
      </if>
      <if test="autoBlocking != null">
        AUTO_BLOCKING = #{autoBlocking},
      </if>
      <if test="trustedPayNo != null">
        TRUSTED_PAY_NO = #{trustedPayNo},
      </if>
      <if test="restraintSeqNo != null">
        RESTRAINT_SEQ_NO = #{restraintSeqNo},
      </if>
      <if test="dacValue != null">
        DAC_VALUE = #{dacValue},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="settleBankFlag != null">
        SETTLE_BANK_FLAG = #{settleBankFlag},
      </if>
      <if test="settleAcctName != null">
        SETTLE_ACCT_NAME = #{settleAcctName},
      </if>
      <if test="settleBankName != null">
        SETTLE_BANK_NAME = #{settleBankName},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="profitRatio != null">
        PROFIT_RATIO = #{profitRatio},
      </if>
      <if test="contributiveRatio != null">
        CONTRIBUTIVE_RATIO = #{contributiveRatio},
      </if>
      <if test="isSelfSupport != null">
        IS_SELF_SUPPORT = #{isSelfSupport}
      </if>
    </set>
    where SETTLE_NO = #{settleNo}
    and INTERNAL_KEY = #{internalKey}
  </update>
  <delete id="deleteByInternalKey" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    delete from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    <if test="settleNo != null">
      AND SETTLE_NO = #{settleNo}
    </if>
  </delete>
  <delete id="deleteByInternalKeyEventType" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    delete from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    AND EVENT_TYPE = #{eventType}
  </delete>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    delete from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    AND SETTLE_NO = #{settleNo}
  </delete>
  <select id="selectAcctSettleBySettleAcctType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    and SETTLE_ACCT_CLASS = #{settleAcctClass}
  </select>
  <select id="selectAcctSettleByEventType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    and event_type = #{eventType}
  </select>
  <delete id="deleteByRefNo" parameterType="java.util.Map">
    delete from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    AND REF_NO = #{refNo}
  </delete>
  <select id="selectSettleBySettleAcctClasses" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    select <include refid="Base_Column_List"/>
    from mm_acct_settle
    where INTERNAL_KEY = #{internalKey}
    and SETTLE_ACCT_CLASS  IN
    <foreach item="item" index="index" collection="settleAcctClasses" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="getAcctSettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    SELECT <include refid="Base_Column_List"/>
    FROM mm_acct_settle
    WHERE  internal_key = #{internalKey}
    AND settle_acct_class = 'AUT'
    AND pay_rec_ind = 'REC'
    AND amt_type IN ('ALL', 'PR')
  </select>
  <select id="getIntSettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    SELECT <include refid="Base_Column_List"/>
    FROM mm_acct_settle
    WHERE  internal_key = #{internalKey}
    AND settle_acct_class = 'AUT'
    AND pay_rec_ind = 'REC'
    AND amt_type IN( 'ALL','INT')
  </select>
  <select id="getPaySettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAcctSettle">
    SELECT <include refid="Base_Column_List"/>
    FROM mm_acct_settle
    WHERE  internal_key = #{internalKey}
    AND settle_acct_class = 'PAY'
    AND pay_rec_ind = 'PAY'
  </select>
</mapper>
