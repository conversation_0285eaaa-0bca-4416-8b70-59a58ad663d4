<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchUncacctDef">
	<select id="retriveBranchUncAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchUncacctDef">
		select <include refid="Base_Column"/>
		from RB_BRANCH_UNCACCT_DEF
		where
		FCY_OBUNC_ACCT_NO is not null
		and LCY_OBUNC_ACCT_NO is not null
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchUncacctDef">
		insert into RB_BRANCH_UNCACCT_DEF
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="branch != null">
				BRANCH,
			</if>
			<if test="ccy != null">
				CCY,
			</if>
			<if test="fcyExBrnAcct != null">
				FCY_EX_BRN_ACCT,
			</if>
			<if test="lcyExBrnAcct != null">
				LCY_EX_BRN_ACCT,
			</if>
			<if test="fcyIbuncAcctNo != null">
				FCY_IBUNC_ACCT_NO,
			</if>
			<if test="lcyIbuncAcctNo != null">
				LCY_IBUNC_ACCT_NO,
			</if>
			<if test="fcyObuncAcctNo != null">
				FCY_OBUNC_ACCT_NO,
			</if>
			<if test="lcyObuncAcctNo != null">
				LCY_OBUNC_ACCT_NO,
			</if>
			<if test="tranTimestamp != null">
				TRAN_TIMESTAMP,
			</if>
			<if test="company != null">
				COMPANY,
			</if>
			<if test="fcyRemitBrnAcct != null">
				FCY_REMIT_BRN_ACCT,
			</if>
			<if test="lcyRemitBrnAcct != null">
				LCY_REMIT_BRN_ACCT,
			</if>
			<if test="lcyExBrnComAcct != null">
				LCY_EX_BRN_COM_ACCT,
			</if>
			<if test="fcyExBrnComAcct != null">
				FCY_EX_BRN_COM_ACCT,
			</if>
			<if test="fcyRemitBrnComAcct != null">
				FCY_REMIT_BRN_COM_ACCT,
			</if>
			<if test="lcyRemitBrnComAcct != null">
				LCY_REMIT_BRN_COM_ACCT,
			</if>
			<if test="clientNo != null">
				CLIENT_NO,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="branch != null">
				#{branch},
			</if>
			<if test="ccy != null">
				#{ccy},
			</if>
			<if test="fcyExBrnAcct != null">
				#{fcyExBrnAcct},
			</if>
			<if test="lcyExBrnAcct != null">
				#{lcyExBrnAcct},
			</if>
			<if test="fcyIbuncAcctNo != null">
				#{fcyIbuncAcctNo},
			</if>
			<if test="lcyIbuncAcctNo != null">
				#{lcyIbuncAcctNo},
			</if>
			<if test="fcyObuncAcctNo != null">
				#{fcyObuncAcctNo},
			</if>
			<if test="lcyObuncAcctNo != null">
				#{lcyObuncAcctNo},
			</if>
			<if test="tranTimestamp != null">
				#{tranTimestamp},
			</if>
			<if test="company != null">
				#{company},
			</if>
			<if test="fcyRemitBrnAcct != null">
				#{fcyRemitBrnAcct},
			</if>
			<if test="lcyRemitBrnAcct != null">
				#{lcyRemitBrnAcct},
			</if>
			<if test="lcyExBrnComAcct != null">
				#{lcyExBrnComAcct},
			</if>
			<if test="fcyExBrnComAcct != null">
				#{fcyExBrnComAcct},
			</if>
			<if test="fcyRemitBrnComAcct != null">
				#{fcyRemitBrnComAcct},
			</if>
			<if test="lcyRemitBrnComAcct != null">
				#{lcyRemitBrnComAcct},
			</if>
			<if test="clientNo != null">
				#{clientNo},
			</if>
		</trim>
	</insert>
	<select id="getBranchUncacctDefFlag" parameterType="java.util.Map" resultType="java.lang.Integer">
		SELECT count(1) FROM RB_BRANCH_UNCACCT_DEF
		WHERE FCY_EX_BRN_ACCT = #{baseAcctNo} OR LCY_EX_BRN_ACCT = #{baseAcctNo}
		OR FCY_IBUNC_ACCT_NO = #{baseAcctNo} OR LCY_IBUNC_ACCT_NO = #{baseAcctNo}
		OR FCY_OBUNC_ACCT_NO = #{baseAcctNo} OR LCY_OBUNC_ACCT_NO = #{baseAcctNo}
		OR FCY_REMIT_BRN_ACCT = #{baseAcctNo} OR LCY_REMIT_BRN_ACCT = #{baseAcctNo}
		OR LCY_EX_BRN_COM_ACCT = #{baseAcctNo} OR FCY_EX_BRN_COM_ACCT = #{baseAcctNo}
		OR FCY_REMIT_BRN_COM_ACCT = #{baseAcctNo} OR LCY_REMIT_BRN_COM_ACCT = #{baseAcctNo}
	</select>
</mapper>