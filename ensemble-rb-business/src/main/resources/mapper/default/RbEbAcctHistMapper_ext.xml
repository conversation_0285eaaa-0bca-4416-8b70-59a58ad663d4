<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbEbAcctHist">
    <select id="selectByParam" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbEbAcctHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbEbAcctHist">
        select
        <include refid="Base_Column"/>
        from RB_EB_ACCT_HIST
        <where>
            <if test="baseAcctNo != null and baseAcctNo.length() > 0">
                AND BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="documentId != null and documentId.length() > 0">
                AND DOCUMENT_ID = #{documentId}
            </if>
            <if test="documentType != null and documentType.length() > 0">
                AND DOCUMENT_TYPE = #{documentType}
            </if>
            <if test="busiType != null and busiType.length() > 0">
                AND BUSI_TYPE = #{busiType}
            </if>
            <if test="tradeType != null and tradeType.length() > 0">
                AND TRADE_TYPE = #{tradeType}
            </if>
            <if test="acctType != null and acctType.length() > 0">
                AND ACCT_TYPE = #{acctType}
            </if>
        </where>
        ORDER BY ACCT_OPEN_DATE ASC
    </select>
</mapper>
