<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist">
<sql id="Base_Column_List">
    SEQ_NO,
    EVENT_TYPE,
    TRAN_TYPE,
    INTERNAL_KEY,
    DEAL_NO,
    TRAN_BRANCH,
    <PERSON>ANCH,
    PROFIT_CENTRE,
    ACCT_EXEC,
    INTERNAL_EXTERNAL,
    CLIENT_NO,
    TBOOK,
    COUNTERPARTY,
    PROD_TYPE,
    PROC_TYPE,
    SOURCE_TYPE,
    SOURCE_MODULE,
    BUSINESS_UNIT,
    CLIENT_TYPE,
    EFFECT_DATE,
    TAKE_PLACE,
    LAST_CYCLE_DATE,
    NEXT_CYCLE_DATE,
    MATURITY_DATE,
    TERM,
    TERM_TYPE,
    TAKE_ACCT_NO,
    PLACE_ACCT_NO,
    STATUS,
    CCY,
    PRINCIPAL_AMT,
    INT_TYPE,
    ACTUAL_RATE,
    FLOAT_RATE,
    SPREAD_RATE,
    SPREAD_PERCENT,
    ACCT_FIXED_RATE,
    ACCT_SPREAD_RATE,
    ACCT_PERCENT_RATE,
    REAL_RATE,
    TREASURY_MARGIN,
    INT_AMT,
    YIELD_RATE,
    PERIOD_YIELD,
    YIELD_TO_DATE,
    TRAN_DATE,
    INT_ACCRUED_CTD,
    INT_ACCRUED,
    INT_ADJ_CTD,
    INT_ADJ,
    INT_POSTED_CTD,
    INT_POSTED,
    TAX_TYPE,
    TAX_RATE,
    TAX_ACCRUED_CTD,
    TAX_ACCRUED,
    TAX_POSTED_CTD,
    TAX_POSTED,
    SETTLE_DATE,
    YEAR_BASIS,
    MONTH_BASIS,
    USER_ID,
    LAST_CHANGE_DATE,
    APPR_USER_ID,
    APPROVAL_DATE,
    DEL_REASON,
    LINK_DEAL_INTERNAL_KEY,
    RENEW_DATE,
    INT_CAP,
    AUTO_CHANGE,
    REFERENCE,
    BANK_SEQ_NO,
    REVERSAL,
    GL_POSTED,
    TRAN_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist">
    select <include refid="Base_Column_List"/>
    from MM_DETAIL_HIST
    where SEQ_NO = #{seqNo}
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist">
    delete from MM_DETAIL_HIST
    where SEQ_NO = #{seqNo}
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist">
    update MM_DETAIL_HIST
    <set>
      <if test="eventType != null">
        EVENT_TYPE = #{eventType},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="dealNo != null">
        DEAL_NO = #{dealNo},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="branch != null">
        BRANCH = #{branch},
      </if>
      <if test="profitCenter != null">
        PROFIT_CENTRE = #{profitCenter},
      </if>
      <if test="acctExec != null">
        ACCT_EXEC = #{acctExec},
      </if>
      <if test="internalExternal != null">
        INTERNAL_EXTERNAL = #{internalExternal},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="tbook != null">
        TBOOK = #{tbook},
      </if>
      <if test="counterparty != null">
        COUNTERPARTY = #{counterparty},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="procType != null">
        PROC_TYPE = #{procType},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE = #{sourceModule},
      </if>
      <if test="businessUnit != null">
        BUSINESS_UNIT = #{businessUnit},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="takePlace != null">
        TAKE_PLACE = #{takePlace},
      </if>
      <if test="lastCycleDate != null">
        LAST_CYCLE_DATE = #{lastCycleDate},
      </if>
      <if test="nextCycleDate != null">
        NEXT_CYCLE_DATE = #{nextCycleDate},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="term != null">
        TERM = #{term},
      </if>
      <if test="termType != null">
        TERM_TYPE = #{termType},
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO = #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO = #{placeAcctNo},
      </if>
      <if test="status != null">
        STATUS = #{status},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT = #{principalAmt},
      </if>
      <if test="intType != null">
        INT_TYPE = #{intType},
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE = #{actualRate},
      </if>
      <if test="floatRate != null">
        FLOAT_RATE = #{floatRate},
      </if>
      <if test="spreadRate != null">
        SPREAD_RATE = #{spreadRate},
      </if>
      <if test="spreadPercent != null">
        SPREAD_PERCENT = #{spreadPercent},
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE = #{acctFixedRate},
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE = #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE = #{acctPercentRate},
      </if>
      <if test="realRate != null">
        REAL_RATE = #{realRate},
      </if>
      <if test="treasuryMargin != null">
        TREASURY_MARGIN = #{treasuryMargin},
      </if>
      <if test="intAmt != null">
        INT_AMT = #{intAmt},
      </if>
      <if test="yieldRate != null">
        YIELD_RATE = #{yieldRate},
      </if>
      <if test="periodYield != null">
        PERIOD_YIELD = #{periodYield},
      </if>
      <if test="yieldToDate != null">
        YIELD_TO_DATE = #{yieldToDate},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="intAccruedCtd != null">
        INT_ACCRUED_CTD = #{intAccruedCtd},
      </if>
      <if test="intAccrued != null">
        INT_ACCRUED = #{intAccrued},
      </if>
      <if test="intAdjCtd != null">
        INT_ADJ_CTD = #{intAdjCtd},
      </if>
      <if test="intAdj != null">
        INT_ADJ = #{intAdj},
      </if>
      <if test="intPostedCtd != null">
        INT_POSTED_CTD = #{intPostedCtd},
      </if>
      <if test="intPosted != null">
        INT_POSTED = #{intPosted},
      </if>
      <if test="taxType != null">
        TAX_TYPE = #{taxType},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate},
      </if>
      <if test="taxAccruedCtd != null">
        TAX_ACCRUED_CTD = #{taxAccruedCtd},
      </if>
      <if test="taxAccrued != null">
        TAX_ACCRUED = #{taxAccrued},
      </if>
      <if test="taxPostedCtd != null">
        TAX_POSTED_CTD = #{taxPostedCtd},
      </if>
      <if test="taxPosted != null">
        TAX_POSTED = #{taxPosted},
      </if>
      <if test="settleDate != null">
        SETTLE_DATE = #{settleDate},
      </if>
      <if test="yearBasis != null">
        YEAR_BASIS = #{yearBasis},
      </if>
      <if test="monthBasis != null">
        MONTH_BASIS = #{monthBasis},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID = #{apprUserId},
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE = #{approvalDate},
      </if>
      <if test="delReason != null">
        DEL_REASON = #{delReason},
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY = #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        RENEW_DATE = #{renewDate},
      </if>
      <if test="intCap != null">
        INT_CAP = #{intCap},
      </if>
      <if test="autoChange != null">
        AUTO_CHANGE = #{autoChange},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO = #{bankSeqNo},
      </if>
      <if test="reversal != null">
        REVERSAL = #{reversal},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP= #{tranTimestamp}
      </if>
    </set>
    where SEQ_NO = #{seqNo}
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist">
    insert into MM_DETAIL_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="eventType != null">
        EVENT_TYPE,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="dealNo != null">
        DEAL_NO,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="branch != null">
        BRANCH,
      </if>
      <if test="profitCenter != null">
        PROFIT_CENTRE,
      </if>
      <if test="acctExec != null">
        ACCT_EXEC,
      </if>
      <if test="internalExternal != null">
        INTERNAL_EXTERNAL,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="tbook != null">
        TBOOK,
      </if>
      <if test="counterparty != null">
        COUNTERPARTY,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="procType != null">
        PROC_TYPE,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE,
      </if>
      <if test="businessUnit != null">
        BUSINESS_UNIT,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="takePlace != null">
        TAKE_PLACE,
      </if>
      <if test="lastCycleDate != null">
        LAST_CYCLE_DATE,
      </if>
      <if test="nextCycleDate != null">
        NEXT_CYCLE_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="term != null">
        TERM,
      </if>
      <if test="termType != null">
        TERM_TYPE,
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO,
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="spreadRate != null">
        SPREAD_RATE,
      </if>
      <if test="spreadPercent != null">
        SPREAD_PERCENT,
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE,
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE,
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="treasuryMargin != null">
        TREASURY_MARGIN,
      </if>
      <if test="intAmt != null">
        INT_AMT,
      </if>
      <if test="yieldRate != null">
        YIELD_RATE,
      </if>
      <if test="periodYield != null">
        PERIOD_YIELD,
      </if>
      <if test="yieldToDate != null">
        YIELD_TO_DATE,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="intAccruedCtd != null">
        INT_ACCRUED_CTD,
      </if>
      <if test="intAccrued != null">
        INT_ACCRUED,
      </if>
      <if test="intAdjCtd != null">
        INT_ADJ_CTD,
      </if>
      <if test="intAdj != null">
        INT_ADJ,
      </if>
      <if test="intPostedCtd != null">
        INT_POSTED_CTD,
      </if>
      <if test="intPosted != null">
        INT_POSTED,
      </if>
      <if test="taxType != null">
        TAX_TYPE,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="taxAccruedCtd != null">
        TAX_ACCRUED_CTD,
      </if>
      <if test="taxAccrued != null">
        TAX_ACCRUED,
      </if>
      <if test="taxPostedCtd != null">
        TAX_POSTED_CTD,
      </if>
      <if test="taxPosted != null">
        TAX_POSTED,
      </if>
      <if test="settleDate != null">
        SETTLE_DATE,
      </if>
      <if test="yearBasis != null">
        YEAR_BASIS,
      </if>
      <if test="monthBasis != null">
        MONTH_BASIS,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID,
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE,
      </if>
      <if test="delReason != null">
        DEL_REASON,
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY,
      </if>
      <if test="renewDate != null">
        RENEW_DATE,
      </if>
      <if test="intCap != null">
        INT_CAP,
      </if>
      <if test="autoChange != null">
        AUTO_CHANGE,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO,
      </if>
      <if test="reversal != null">
        REVERSAL,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="eventType != null">
        #{eventType},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="dealNo != null">
        #{dealNo},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="profitCenter != null">
        #{profitCenter},
      </if>
      <if test="acctExec != null">
        #{acctExec},
      </if>
      <if test="internalExternal != null">
        #{internalExternal},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="tbook != null">
        #{tbook},
      </if>
      <if test="counterparty != null">
        #{counterparty},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="procType != null">
        #{procType},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="sourceModule != null">
        #{sourceModule},
      </if>
      <if test="businessUnit != null">
        #{businessUnit},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="takePlace != null">
        #{takePlace},
      </if>
      <if test="lastCycleDate != null">
        #{lastCycleDate},
      </if>
      <if test="nextCycleDate != null">
        #{nextCycleDate},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="term != null">
        #{term},
      </if>
      <if test="termType != null">
        #{termType},
      </if>
      <if test="takeAcctNo != null">
        #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        #{placeAcctNo},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="principalAmt != null">
        #{principalAmt},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="spreadRate != null">
        #{spreadRate},
      </if>
      <if test="spreadPercent != null">
        #{spreadPercent},
      </if>
      <if test="acctFixedRate != null">
        #{acctFixedRate},
      </if>
      <if test="acctSpreadRate != null">
        #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        #{acctPercentRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="treasuryMargin != null">
        #{treasuryMargin},
      </if>
      <if test="intAmt != null">
        #{intAmt},
      </if>
      <if test="yieldRate != null">
        #{yieldRate},
      </if>
      <if test="periodYield != null">
        #{periodYield},
      </if>
      <if test="yieldToDate != null">
        #{yieldToDate},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="intAccruedCtd != null">
        #{intAccruedCtd},
      </if>
      <if test="intAccrued != null">
        #{intAccrued},
      </if>
      <if test="intAdjCtd != null">
        #{intAdjCtd},
      </if>
      <if test="intAdj != null">
        #{intAdj},
      </if>
      <if test="intPostedCtd != null">
        #{intPostedCtd},
      </if>
      <if test="intPosted != null">
        #{intPosted},
      </if>
      <if test="taxType != null">
        #{taxType},
      </if>
      <if test="taxRate != null">
        #{taxRate},
      </if>
      <if test="taxAccruedCtd != null">
        #{taxAccruedCtd},
      </if>
      <if test="taxAccrued != null">
        #{taxAccrued},
      </if>
      <if test="taxPostedCtd != null">
        #{taxPostedCtd},
      </if>
      <if test="taxPosted != null">
        #{taxPosted},
      </if>
      <if test="settleDate != null">
        #{settleDate},
      </if>
      <if test="yearBasis != null">
        #{yearBasis},
      </if>
      <if test="monthBasis != null">
        #{monthBasis},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        #{apprUserId},
      </if>
      <if test="approvalDate != null">
        #{approvalDate},
      </if>
      <if test="delReason != null">
        #{delReason},
      </if>
      <if test="linkDealInternalKey != null">
        #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        #{renewDate},
      </if>
      <if test="intCap != null">
        #{intCap},
      </if>
      <if test="autoChange != null">
        #{autoChange},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="bankSeqNo != null">
        #{bankSeqNo},
      </if>
      <if test="reversal != null">
        #{reversal},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <select id="selectMmDetailHist" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist">
    select <include refid="Base_Column_List"/>
    from mm_detail_hist
    where  tran_date BETWEEN  #{lastRunDate} and  #{yesterday}
     AND (gl_posted = 'N' or gl_posted is null)
     AND seq_no BETWEEN #{startKey} and #{endKey}
    ORDER BY seq_no
  </select>
  <select id="selectMmDetailHistSplit" parameterType="java.util.Map"  resultType="java.util.Map">
<if test="_databaseId == 'mysql'">
            SELECT
            MIN(seq_no) START_KEY,
            MAX(seq_no) END_KEY,COUNT(*) ROW_COUNT
            FROM
            (
            SELECT
            seq_no,
            @rownum :=@rownum + 1 AS rownum
            FROM (SELECT
            DISTINCT seq_no FROM mm_detail_hist,
            (SELECT @rownum := -1) t
            where tran_date BETWEEN #{lastRunDate} AND #{yesterday}
	           AND (gl_posted = 'N' or gl_posted is null))t1
            ORDER BY seq_no
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
          SELECT MIN (seq_no) START_KEY, MAX (seq_no) END_KEY,COUNT(*) ROW_COUNT
            FROM (SELECT DISTINCT seq_no
            from mm_detail_hist
            where tran_date BETWEEN #{lastRunDate} AND #{yesterday}
	           AND (gl_posted = 'N' or gl_posted is null)
            ORDER BY seq_no)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
  </select>
  <select id="selectMmDetailHistSplitOB" parameterType="java.util.Map"  resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      MIN(seq_no) START_KEY,
      MAX(seq_no) END_KEY,COUNT(*) ROW_COUNT
      FROM
      (
      SELECT
      seq_no,
      @rownum :=@rownum + 1 AS rownum
      FROM (SELECT
      DISTINCT seq_no FROM mm_detail_hist,
      (SELECT @rownum := -1) t
      where tran_date BETWEEN #{lastRunDate} AND #{yesterday}
      AND (gl_posted = 'N' or gl_posted is null))t1
      ORDER BY seq_no
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT MIN (seq_no) START_KEY, MAX (seq_no) END_KEY,COUNT(*) ROW_COUNT
      FROM (SELECT DISTINCT seq_no
      from mm_detail_hist
      where tran_date =  #{runDate}
      AND (gl_posted = 'N' or gl_posted is null)
      ORDER BY seq_no)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>
  <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetailHist" >
    update mm_detail_hist set gl_posted='Y'   where SEQ_NO = #{seqNo}
  </update>
</mapper>
