<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdSeqSkip">

    <select id="getCdSequenceSkip" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdSeqSkip">
        SELECT <include refid="Base_Column" />
            FROM CD_SEQ_SKIP
        WHERE CARD_BIN = #{cardBin}
        <if test="seqNo != null and seqNo.length() > 0">
            AND (start_no &lt;= #{seqNo} and end_no &gt;= #{seqNo})
        </if>
        <if test="company!=null and company.length()>0">
            AND COMPANY=#{company}
        </if>
    </select>
</mapper>

