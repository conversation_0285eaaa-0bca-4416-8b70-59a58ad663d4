<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBailRegister">

    <select id="selectRbAcctBailRegister" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBailRegister">
        SELECT <include refid="Base_Column"/>
        FROM <include refid="Table_Name" />
        <where>
        <if test="baseAcctNo != null and baseAcctNo != '' ">
            AND  (DC_BASE_ACCT_NO=#{baseAcctNo} or DC_BASE_ACCT_NO=#{cardNo})
        </if>
            <if test="dcAcctSeqNo != null and dcAcctSeqNo != '' ">
                and DC_ACCT_SEQ_NO=#{dcAcctSeqNo}
            </if>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>

</mapper>
