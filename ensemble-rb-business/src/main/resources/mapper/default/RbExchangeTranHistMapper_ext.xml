<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">

  <sql id="join_Column">
    <trim suffixOverrides=",">
      a.SEQ_NO,
      a.CASH_SEQ_NO,
      a.TAE_SUB_SEQ_NO,
      a.EXCHANGE_SEQ_NO,
      a.DEPOSIT_INTERNAL_KEY,
      a.DEPOSIT_BASE_ACCT_NO,
      a.CR_ACCT_CCY,
      a.DEPOSIT_ACCT_SEQ_NO,
      a.DEPOSIT_PROD_TYPE,
      a.DEPOSIT_BALANCE_TYPE,
      a.DEPOSIT_SEQ_NO,
      a.WITHDRAW_INTERNAL_KEY,
      a.WITHDRAW_BASE_ACCT_NO,
      a.WITHDRAW_ACCT_CCY,
      a.WITHDRAW_ACCT_SEQ_NO,
      a.WITHDRAW_PROD_TYPE,
      a.WITHDRAW_BALANCE_TYPE,
      a.WITHDRAW_SEQ_NO,
      a.CLIENT_NO,
      a.TRAN_TYPE,
      a.TRAN_DATE,
      a.TRAN_BRANCH,
      a.REVERSAL_DATE,
      a.REVERSAL_TRAN_TYPE,
      a.EXCHANGE_TRAN_STATUS,
      a.SELL_BUY_IND,
      a.QUOTE_TYPE,
      a.RATE_TYPE,
      a.BUY_AMOUNT,
      a.BUY_CCY,
      a.BUY_RATE,
      a.SELL_CCY,
      a.SELL_AMOUNT,
      a.SELL_RATE,
      a.EXCH_RATE,
      a.FLOAT_RATE,
      a.BASE_QUOTE_TYPE,
      a.BASE_RATE_TYPE,
      a.BASE_RATE,
      a.BASE_EQUIV_AMT,
      a.CROSS_RATE,
      a.CROSS_RATE_ATTR,
      a.UNC_CROSS_RATE,
      a.INNER_RATE,
      a.CHANGE_BASE_EQUIV_AMT,
      a.CHANGE_CNY_AMOUNT,
      a.CHANGE_BASE_RATE,
      a.CHANGE_BASE_RATE_TYPE,
      a.CHANGE_RATE,
      a.CHANGE_BASE_QUOTE_TYPE,
      a.CHANGE_QUOTE_TYPE,
      a.CHANGE_RATE_TYPE,
      a.REFERENCE,
      a.TRACE_REF_NO,
      a.SOURCE_TYPE,
      a.BANK_SEQ_NO,
      a.REMARK,
      a.SOURCE_MODULE,
      a.TERMINAL_ID,
      a.TRACE_REF_CODE,
      a.APPROVAL_DATE,
      a.VALUE_DATE,
      a.APPR_USER_ID,
      a.APPR_AUTH_USER_ID,
      a.USER_ID,
      a.AUTH_USER_ID,
      a.REVERSAL_AUTH_USER_ID,
      a.REVERSAL_USER_ID,
      a.PROFIT_CENTER,
      a.TRAN_TIMESTAMP,
      a.COMPANY,
      a.FCY_CTRL_IBUNC_AMT,
      a.IBUNC_REFERENCE,
      a.OBUNC_REFERENCE,
      a.UNC_STATUS,
      a.COUPON_RATE_TYPE,
      a.MIN_AMT_FLAG,
      a.FLOAT_POINT,
      a.COUNTRY_LOC,
      a.EFFECT_TIME,
      a.EFFECT_DATE,
      a.SELL_UNC_RATE,
      a.BUY_UNC_RATE
    </trim>
  </sql>


  <update id="updateByReference" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
    update RB_EXCHANGE_TRAN_HIST
    <set>
      <if test="reversalTranType != null">
        REVERSAL_TRAN_TYPE = #{reversalTranType},
      </if>
      <if test="reversalDate != null">
        REVERSAL_DATE = #{reversalDate},
      </if>
      <if test="reversalUserId != null">
        REVERSAL_USER_ID = #{reversalUserId},
      </if>
      <if test="uncStatus != null">
        UNC_STATUS = #{uncStatus},
      </if>
    </set>
    where REFERENCE = #{reference}
    and CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
    <select id="getResutByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
        select <include refid="Base_Column"/>
        from RB_EXCHANGE_TRAN_HIST
        where REFERENCE = #{reference}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
        order by TRAN_DATE desc
    </select>

  <select id="getResutByReferenceAndClientNo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
    select <include refid="Base_Column"/>
    from RB_EXCHANGE_TRAN_HIST
    where REFERENCE = #{reference}
    and CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_DATE desc
  </select>
  <!--结售汇登记列表查询，供维护外汇交易收支编码-->
  <select id="getResuBySomeCondition" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
    select <include refid="Base_Column"/>
    from RB_EXCHANGE_TRAN_HIST
    where
    <if test="startDate != null and endDate != null">
      TRAN_DATE   between #{startDate} and #{endDate}
    </if>
    <if test="sellBuyInd != null and sellBuyInd.length() > 0">
      AND SELL_BUY_IND = #{sellBuyInd}
    </if>
      and (DEPOSIT_BASE_ACCT_NO = #{baseAcctNo} or WITHDRAW_BASE_ACCT_NO = #{baseAcctNo})
      and (BUY_CCY = #{ccy} or SELL_CCY = #{ccy})
    <if test="clientNo != null and clientNo.length() > 0">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_DATE desc
  </select>
  <!--结售汇登记列表查询，供维护外汇交易收支编码-->
  <select id="getResuBySomeConditionByPage" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
    select <include refid="join_Column"/>
    from RB_EXCHANGE_TRAN_HIST a ,RB_EXCHANGE_TRAN_ATTACH b
    where a.SEQ_NO = b.SEQ_NO AND a.CLIENT_NO = B.CLIENT_NO
    <if test="startDate != null and endDate != null">
      AND a.TRAN_DATE   between #{startDate} and #{endDate}
    </if>
    <if test="sellBuyInd != null and sellBuyInd.length() > 0">
      AND a.SELL_BUY_IND = #{sellBuyInd}
    </if>
    and (a.DEPOSIT_BASE_ACCT_NO = #{baseAcctNo} or a.WITHDRAW_BASE_ACCT_NO = #{baseAcctNo})
    and (a.BUY_CCY = #{ccy} or a.SELL_CCY = #{ccy})
    <if test="clientNo != null and clientNo.length() > 0">
      and a.CLIENT_NO = #{clientNo}
    </if>
    <if test="exchangeReportType != null and exchangeReportType.length() > 0">
      and b.EXCHANGE_REPORT_TYPE = #{exchangeReportType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    order by TRAN_DATE desc
  </select>

  <!--结售汇登记列表查询，供维护外汇交易收支编码, 2022/11/11新需求，baseAcctNo改为非必输-->
  <select id="getResBySomeConditionByPage" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
    select <include refid="join_Column"/>
    from RB_EXCHANGE_TRAN_HIST a ,RB_EXCHANGE_TRAN_ATTACH b
    where a.SEQ_NO = b.SEQ_NO AND a.CLIENT_NO = B.CLIENT_NO
    <if test="startDate != null and endDate != null">
      AND a.TRAN_DATE   between #{startDate} and #{endDate}
    </if>
    <if test="sellBuyInd != null and sellBuyInd.length() > 0">
      AND a.SELL_BUY_IND = #{sellBuyInd}
    </if>
    <if test="baseAcctNo != null and baseAcctNo.length() > 0">
    and (a.DEPOSIT_BASE_ACCT_NO = #{baseAcctNo} or a.WITHDRAW_BASE_ACCT_NO = #{baseAcctNo})
    </if>
    and (a.BUY_CCY = #{ccy} or a.SELL_CCY = #{ccy})
    <if test="clientNo != null and clientNo.length() > 0">
      and a.CLIENT_NO = #{clientNo}
    </if>
    <if test="branchList != null and branchList.size() > 0">
      and tran_branch in
      <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
        #{branch}
      </foreach>
    </if>
    <if test="exchangeReportType != null and exchangeReportType.length() > 0">
      and b.EXCHANGE_REPORT_TYPE = #{exchangeReportType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    order by TRAN_DATE desc
  </select>
  <!--通过交易时间查询结售汇交易历史-->
  <select id="getRbExchangeTranHistByTrandate" parameterType="java.util.Map"  resultType="int">
    select count(1)
    from RB_EXCHANGE_TRAN_HIST
    where SEQ_NO is not null
    <if test="startDate != null ">
      AND  TRAN_DATE  <![CDATA[ >= ]]>  #{startDate}
    </if>
    <if test="endDate != null">
      AND  TRAN_DATE   <![CDATA[ <= ]]> #{endDate}
    </if>
    <if test="baseAcctNo != null and baseAcctNo.length()>0 ">
      and (WITHDRAW_BASE_ACCT_NO = #{baseAcctNo} or DEPOSIT_BASE_ACCT_NO =  #{baseAcctNo})
    </if>
  </select>

  <update id="updateUncStatusByReference" parameterType="java.util.Map">
    update RB_EXCHANGE_TRAN_HIST
    <set>
      <if test="uncStatus != null">
        UNC_STATUS = #{uncStatus},
      </if>
      <if test="reference != null">
        OBUNC_REFERENCE = #{reference},
      </if>
    </set>
    where TRAN_BRANCH = #{tranBranch}
    and UNC_STATUS='P'
    and TRAN_DATE= #{tranDate}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateUncStatusByBrnCcy" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
    update RB_EXCHANGE_TRAN_HIST
    <set>
      <if test="uncStatus != null and uncStatus != ''">
        UNC_STATUS = #{uncStatus},
      </if>
      <if test="ibuncReference != null and ibuncReference!=''">
        IBUNC_REFERENCE = #{ibuncReference},
      </if>
    </set>
    where TRAN_BRANCH = #{tranBranch}
    AND SELL_BUY_IND=#{sellBuyInd}
    and BUY_CCY=#{buyCcy}
    and SELL_CCY=#{sellCcy}
    and SELL_BUY_IND = #{sellBuyInd}
    <if test="tranDate != null">
      AND TRAN_DATE= #{tranDate} and REVERSAL_DATE is null
    </if>
    <if test="reversalDate != null">
      AND REVERSAL_DATE= #{reversalDate} and TRAN_DATE &lt; REVERSAL_DATE
    </if>
  </update>
</mapper>
