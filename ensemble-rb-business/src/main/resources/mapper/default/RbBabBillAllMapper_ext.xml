<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillAll">

	<update id="updateByVoucherNo" parameterType="java.util.Map">
		UPDATE RB_BAB_BILL_ALL
		set BILL_STATUS='03',ACCEPT_STATUS='05'
		<where>
			<trim suffixOverrides="AND">
				<if test="acceptContractNo != null and  acceptContractNo != '' ">
					ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
				</if>
				<if test="billDocType != null and  billDocType != '' ">
					BILL_DOC_TYPE = #{billDocType} AND
				</if>
				<if test="billVoucherNo != null and  billVoucherNo != '' ">
					BILL_VOUCHER_NO = #{billVoucherNo} AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

	<select id="getRbBabBillAllCount" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillAll">
		SELECT
		<include refid="Base_Column"/>
		FROM
		RB_BAB_BILL_ALL
		<where>
			<trim suffixOverrides="AND">
				<if test="startDate != null  and endDate != null  ">
					CHANNEL_DATE  between #{startDate,jdbcType=DATE}} AND #{endDate,jdbcType=DATE}} AND
				</if>
				<if test="billType != null and  billType != '' ">
					BILL_TYPE = #{billType} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

</mapper>
