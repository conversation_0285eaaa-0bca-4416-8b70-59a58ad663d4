<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIntDetailHist">
	<select id="getIntDetailHistByDayNum" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIntDetailHist">
		SELECT
			AGREEMENT_ID,
			INTERNAL_KEY,
			LAST_CHANGE_DATE,
			CYCLE_POSTED_PRE,
			CYCLE_TOTAL_POSTED,
			REAL_RATE,
			STANDARD_REAL_RATE,
			SEC_POSTED_PRE,
			SEC_TOTAL_POSTED,
			SEC_CYCLE_POSTED,
			SEC_REAL_RATE,
			INT_ACCRUED_CALC_CTD,
			INT_ACCRUED_CTD,
			INT_ACCRUED_DIFF,
			INT_ACCRUED,
			INT_ADJ,
			INT_ADJ_CTD
		FROM RB_IDEP_INT_DETAIL_HIST
		WHERE <![CDATA[
               AGREEMENT_ID = #{agreementId}
		  AND LAST_CHANGE_DATE >=#{runDate}- numtodsinterval(#{baseDays},'day')
             ]]>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
       ORDER BY LAST_CHANGE_DATE DESC
	</select>
	<insert id="backInsert" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIntDetail">
		insert into RB_IDEP_INT_DETAIL_HIST
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="agreementId != null">
				AGREEMENT_ID,
			</if>
			<if test="internalKey != null">
				INTERNAL_KEY,
			</if>
			<if test="lastChangeDate != null">
				LAST_CHANGE_DATE,
			</if>
			<if test="cyclePostedPre != null">
				CYCLE_POSTED_PRE,
			</if>
			<if test="cycleTotalPosted != null">
				CYCLE_TOTAL_POSTED,
			</if>
			<if test="realRate != null">
				REAL_RATE,
			</if>
			<if test="standardRealRate != null">
				STANDARD_REAL_RATE,
			</if>
			<if test="secPostedPre != null">
				SEC_POSTED_PRE,
			</if>
			<if test="secTotalPosted != null">
				SEC_TOTAL_POSTED,
			</if>
			<if test="secCyclePosted != null">
				SEC_CYCLE_POSTED,
			</if>
			<if test="secRealRate != null">
				SEC_REAL_RATE,
			</if>
			<if test="intAccruedCalcCtd != null">
				ACT_INT_ACCRUED_CTD,
			</if>
			<if test="intAccruedCtd != null">
				INT_ACCRUED_CTD,
			</if>
			<if test="intAccruedDiff != null">
				INT_ACCRUED_DIFF,
			</if>
			<if test="intAccrued != null">
				INT_ACCRUED,
			</if>
			<if test="intAdj != null">
				INT_ADJ,
			</if>
			<if test="intAdjCtd != null">
				INT_ADJ_CTD,
			</if>
			<if test="remark != null">
				REMARK,
			</if>
			<if test="userId != null">
				USER_ID,
			</if>
			<if test="company != null">
				COMPANY,
			</if>
			<if test="tranTimestamp != null">
				TRAN_TIMESTAMP,
			</if>
			<if test="clientNo != null">
				CLIENT_NO,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="agreementId != null">
				#{agreementId},
			</if>
			<if test="internalKey != null">
				#{internalKey},
			</if>
			<if test="lastChangeDate != null">
				#{lastChangeDate},
			</if>
			<if test="cyclePostedPre != null">
				#{cyclePostedPre},
			</if>
			<if test="cycleTotalPosted != null">
				#{cycleTotalPosted},
			</if>
			<if test="realRate != null">
				#{realRate},
			</if>
			<if test="standardRealRate != null">
				#{standardRealRate},
			</if>
			<if test="secPostedPre != null">
				#{secPostedPre},
			</if>
			<if test="secTotalPosted != null">
				#{secTotalPosted},
			</if>
			<if test="secCyclePosted != null">
				#{secCyclePosted},
			</if>
			<if test="secRealRate != null">
				#{secRealRate},
			</if>
			<if test="intAccruedCalcCtd != null">
				#{intAccruedCalcCtd},
			</if>
			<if test="intAccruedCtd != null">
				#{intAccruedCtd},
			</if>
			<if test="intAccruedDiff != null">
				#{intAccruedDiff},
			</if>
			<if test="intAccrued != null">
				#{intAccrued},
			</if>
			<if test="intAdj != null">
				#{intAdj},
			</if>
			<if test="intAdjCtd != null">
				#{intAdjCtd},
			</if>
			<if test="remark != null">
				#{remark},
			</if>
			<if test="userId != null">
				#{userId},
			</if>
			<if test="company != null">
				#{company},
			</if>
			<if test="tranTimestamp != null">
				#{tranTimestamp},
			</if>
			<if test="clientNo != null">
				#{clientNo},
			</if>
		</trim>
	</insert>
</mapper>
