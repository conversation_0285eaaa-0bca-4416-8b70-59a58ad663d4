<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_INT
    where STAGE_CODE = #{stageCode}
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByStageCodeEvent" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_INT
    where STAGE_CODE = #{stageCode}
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="eventType != null">
      AND EVENT_TYPE = #{eventType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt">
    delete from RB_DC_STAGE_INT
    where STAGE_CODE = #{stageCode}
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt">
    update RB_DC_STAGE_INT
    <set>
      <if test="issueYear != null">
        ISSUE_YEAR = #{issueYear},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="eventType != null">
        EVENT_TYPE = #{eventType},
      </if>
      <if test="intCalcType != null">
        INT_CALC_TYPE = #{intCalcType},
      </if>
      <if test="intType != null">
        INT_TYPE = #{intType},
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE = #{actualRate},
      </if>
      <if test="floatRate != null">
        FLOAT_RATE = #{floatRate},
      </if>
      <if test="realRate != null">
        REAL_RATE = #{realRate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS = #{stageProdClass}
      </if>
    </set>
    where STAGE_CODE = #{stageCode}
    AND SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInt">
    insert into RB_DC_STAGE_INT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        ISSUE_YEAR,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="eventType != null">
        EVENT_TYPE,
      </if>
      <if test="intCalcType != null">
        INT_CALC_TYPE,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        #{issueYear},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="eventType != null">
        #{eventType},
      </if>
      <if test="intCalcType != null">
        #{intCalcType},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
</mapper>
