<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftInterestRecovery">

    <select id="getInterestRecoveryByCondition" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftInterestRecovery"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column"/>
        from RB_OVERDRAFT_INTEREST_RECOVERY
        <where>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=INTEGER}
            </if>
            <if test="outstanding != null ">
                AND OUTSTANDING = #{outstanding,jdbcType=DECIMAL}
            </if>
            <if test="repayNum != null ">
                AND REPAY_NUM = #{repayNum,jdbcType=INTEGER}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="cycleDate != null ">
                AND CYCLE_DATE = #{cycleDate,jdbcType=DATE}
            </if>
            <if test="tranDate != null ">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE}
            </if>
            <if test="tranTimestamp != null and  tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="recoverInterest != null ">
                AND RECOVER_INTEREST = #{recoverInterest,jdbcType=DECIMAL}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
            <if test="company != null and  company != '' ">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY CYCLE_DATE ASC
    </select>



</mapper>
