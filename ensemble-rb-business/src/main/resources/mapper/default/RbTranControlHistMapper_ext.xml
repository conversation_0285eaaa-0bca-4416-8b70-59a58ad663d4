<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranControlHist">
	<update id="updateBySeqNoAndSubSeqNo" parameterType="java.util.Map">
		update RB_TRAN_CONTROL_HIST
		<set>
			<if test="onlineTranStatus != null">
				ONLINE_TRAN_STATUS = #{onlineTranStatus},
			</if>
			<if test="sourceModule != null">
				SOURCE_MODULE = #{sourceModule},
			</if>
		</set>
		where CHANNEL_SEQ_NO = #{channelSeqNo}
		and SUB_SEQ_NO = #{subSeqNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<update id="updateBySeqNoAndSubSeqNoforUpdate" parameterType="java.util.Map">
		update RB_TRAN_CONTROL_HIST
		<set>
			<if test="onlineTranStatus != null">
				ONLINE_TRAN_STATUS = #{onlineTranStatus},
			</if>
			<if test="sourceModule != null">
				SOURCE_MODULE = #{sourceModule},
			</if>
		</set>
		<where>
		<if test="channelSeqNo != null and  channelSeqNo != '' ">
		and CHANNEL_SEQ_NO = #{channelSeqNo}
		</if>
			<if test="customerSeqNo != null and customerSeqNo != '' ">
				AND (SUB_SEQ_NO = #{customerSeqNo} or CUSTOMER_SEQ_NO = #{customerSeqNo} )
			</if>
		<if test="reference != null and  reference != '' ">
		and REFERENCE = #{reference}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</update>

	<select id="getReversalMsgByCondition"  resultMap="Base_Result_Map">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
			MESSAGE_TYPE IN ('1000','1200')
			and
			<trim suffixOverrides="AND">
				<if test="sourceType != null and  sourceType != '' ">
					SOURCE_TYPE = #{sourceType}  AND
				</if>
				<if test="channelDate != null and  channelDate != '' ">
					CHANNEL_DATE = #{channelDate}  AND
				</if>
				<if test="channelSeqNo != null and  channelSeqNo != '' ">
					CHANNEL_SEQ_NO = #{channelSeqNo}  AND
				</if>
				<if test="subSeqNo != null and  subSeqNo != '' ">
					SUB_SEQ_NO = #{subSeqNo}  AND
				</if>
				<if test="reference != null and  reference != '' ">
					REFERENCE = #{reference}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>
	<update id="updateBySeqNoAndTranDate" parameterType="java.util.Map">
		update RB_TRAN_CONTROL_HIST
		<set>
			ONLINE_TRAN_STATUS = #{tranStatus},
		</set>
		where CHANNEL_SEQ_NO = #{channelSeqNo}
		and SUB_SEQ_NO = #{subSeqNo} and TRAN_DATE = #{tranDate,jdbcType=DATE}
	</update>
	<select id="getReversalTranControlHist" resultMap="Base_Result_Map">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
			<if test="reference != null and reference !='' ">
				AND REFERENCE = #{reference}
			</if>
			<if test="channelSeqNo != null and channelSeqNo != '' ">
				AND CHANNEL_SEQ_NO = #{channelSeqNo}
			</if>
			<if test="customerSeqNo != null and customerSeqNo != '' ">
				AND (SUB_SEQ_NO = #{customerSeqNo} or CUSTOMER_SEQ_NO = #{customerSeqNo} )
			</if>
		</where>
	</select>

	<select id="getReversalTranControlHistForUpdate" resultMap="Base_Result_Map">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
			<if test="reference != null and reference !='' ">
				AND REFERENCE = #{reference}
			</if>
			<if test="channelSeqNo != null and channelSeqNo != '' ">
				AND CHANNEL_SEQ_NO = #{channelSeqNo}
			</if>
			<if test="customerSeqNo != null and customerSeqNo != '' ">
				AND (SUB_SEQ_NO = #{customerSeqNo} or CUSTOMER_SEQ_NO = #{customerSeqNo} )
			</if>
		</where>
		for update
	</select>
</mapper>
