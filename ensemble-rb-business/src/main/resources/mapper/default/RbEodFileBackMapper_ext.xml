<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbEodFileBack">

	<select id="getFileBackByTranDate" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbEodFileBack">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_EOD_FILE_BACK
		WHERE
		TRAN_DATE = #{tranDate}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<update id="setEodFileBackByTranDate" parameterType="java.util.Map">
		update RB_EOD_FILE_BACK
		<set>
			COUNT_EOD = #{newCount}
		</set>
		where TRAN_DATE = #{tranDate}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<update id="setInpFileBackByTranDate" parameterType="java.util.Map">
		update RB_EOD_FILE_BACK
		<set>
			COUNT_INP = #{newCount}
		</set>
		where TRAN_DATE = #{tranDate}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
</mapper>
