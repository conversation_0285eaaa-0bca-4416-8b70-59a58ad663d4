<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClFileRegister">
	<select id="getFileRegisterByTranDate" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClFileRegister">
		SELECT *
		FROM RB_CL_FILE_REGISTER
		WHERE TRAN_DATE = #{tranDate}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="getFileRegTranDateAndFileClass" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClFileRegister">
		SELECT *
		FROM RB_CL_FILE_REGISTER
		WHERE TRAN_DATE = #{tranDate}
		AND FILE_CLASS = #{fileClass}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getFileRegTranDateAndFileName" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClFileRegister">
		SELECT *
		FROM RB_CL_FILE_REGISTER
		WHERE TRAN_DATE = #{tranDate}
		AND FILE_NAME = #{fileName}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
