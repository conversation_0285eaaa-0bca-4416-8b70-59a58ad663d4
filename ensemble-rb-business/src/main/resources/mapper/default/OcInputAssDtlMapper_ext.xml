<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssDtl">
    <!-- Created by admin on 2017/05/09 11:44:02. -->
    <select id="selectByPrimaryKeyExt" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssign">
        select *
        from OC_INPUT_ASS_DTL
        where ASSIGN_SEQ_NO = #{assignSeqNo}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAssignInfoDtl" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssDtl">
        select *
        from OC_INPUT_ASS_DTL
        <where>
            <if test="assignSeqNo != null">
                and ASSIGN_SEQ_NO = #{assignSeqNo}
            </if>
            <if test="changeNo != null">
                and CHANGE_NO = #{changeNo}
            </if>
            <if test="changeDate != null">
                and CHANGE_DATE = #{changeDate}
            </if>
            <if test="changeSession != null">
                and CHANGE_SESSION = #{changeSession}
            </if>
            <if test="branch != null">
                and RECORD_BRANCH = #{branch}
            </if>
            <if test="userId != null">
                and UPPER(RECORD_USER_ID) = UPPER(#{userId})
            </if>
            <if test="othChangeNo != null">
                and OTH_BANK_CODE = #{othChangeNo}
            </if>
            <if test="docType != null">
                and DOC_TYPE = #{docType}
            </if>
            <!--多法人改造 by LIYUANV-->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>

    <!-- Created by admin on 2017/05/09 11:44:34. -->
    <!--<insert id="insertOcInputAssDtl" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssDtl">-->
    <!--insert into OC_INPUT_ASS_DTL-->
    <!--<trim prefix="(" suffix=")" suffixOverrides=",">-->
    <!--<if test="assignSeqNo != null">-->
    <!--ASSIGN_SEQ_NO,-->
    <!--</if>-->
    <!--<if test="changeNo != null">-->
    <!--CHANGE_NO,-->
    <!--</if>-->
    <!--<if test="changeRegion != null">-->
    <!--CHANGE_REGION,-->
    <!--</if>-->
    <!--<if test="changeDate != null">-->
    <!--CHANGE_DATE,-->
    <!--</if>-->
    <!--<if test="changeSession != null">-->
    <!--CHANGE_SESSION,-->
    <!--</if>-->
    <!--<if test="recordBranch != null">-->
    <!--RECORD_BRANCH,-->
    <!--</if>-->
    <!--<if test="recordUserId != null">-->
    <!--RECORD_USER_ID,-->
    <!--</if>-->
    <!--<if test="othBankCode != null">-->
    <!--OTH_BANK_CODE,-->
    <!--</if>-->
    <!--<if test="docType != null">-->
    <!--DOC_TYPE,-->
    <!--</if>-->
    <!--<if test="idrNum != null">-->
    <!--IDR_NUM,-->
    <!--</if>-->
    <!--<if test="idrAmt != null">-->
    <!--IDR_AMT,-->
    <!--</if>-->
    <!--<if test="icrNum != null">-->
    <!--ICR_NUM,-->
    <!--</if>-->
    <!--<if test="icrAmt != null">-->
    <!--ICR_AMT,-->
    <!--</if>-->
    <!--<if test="tranTimestamp != null">-->
    <!--TRAN_TIMESTAMP,-->
    <!--</if>-->
    <!--</trim>-->
    <!--<trim prefix="values (" suffix=")" suffixOverrides=",">-->
    <!--<if test="assignSeqNo != null">-->
    <!--#{assignSeqNo},-->
    <!--</if>-->
    <!--<if test="changeNo != null">-->
    <!--#{changeNo},-->
    <!--</if>-->
    <!--<if test="changeRegion != null">-->
    <!--#{changeRegion},-->
    <!--</if>-->
    <!--<if test="changeDate != null">-->
    <!--#{changeDate},-->
    <!--</if>-->
    <!--<if test="changeSession != null">-->
    <!--#{changeSession},-->
    <!--</if>-->
    <!--<if test="recordBranch != null">-->
    <!--#{recordBranch},-->
    <!--</if>-->
    <!--<if test="recordUserId != null">-->
    <!--#{recordUserId},-->
    <!--</if>-->
    <!--<if test="othBankCode != null">-->
    <!--#{othBankCode},-->
    <!--</if>-->
    <!--<if test="docType != null">-->
    <!--#{docType},-->
    <!--</if>-->
    <!--<if test="idrNum != null">-->
    <!--#{idrNum},-->
    <!--</if>-->
    <!--<if test="idrAmt != null">-->
    <!--#{idrAmt},-->
    <!--</if>-->
    <!--<if test="icrNum != null">-->
    <!--#{icrNum},-->
    <!--</if>-->
    <!--<if test="icrAmt != null">-->
    <!--#{icrAmt},-->
    <!--</if>-->
    <!--<if test="tranTimestamp != null">-->
    <!--#{tranTimestamp},-->
    <!--</if>-->
    <!--</trim>-->
    <!--</insert>-->

    <!--<delete id="deleteByUserinfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcInputAssDtl">-->
    <!--delete from OC_INPUT_ASS_DTL-->
    <!--where 1=1-->
    <!--<if test="assignSeqNo != null">-->
    <!--and  ASSIGN_SEQ_NO= #{assignSeqNo}-->
    <!--</if>-->
    <!--<if test="recordBranch != null">-->
    <!--and  RECORD_BRANCH= #{recordBranch}-->

    <!--</if>-->
    <!--<if test="recordUserId != null">-->
    <!--and RECORD_USER_ID= #{recordUserId}-->
    <!--</if>-->

    <!--<if test="docType != null">-->
    <!--and  DOC_TYPE= #{docType}-->
    <!--</if>-->
    <!--</delete>-->

    <!--<delete id="deleteByPrimaryKeyExt" parameterType="java.util.Map">-->
    <!--delete from OC_INPUT_ASS_DTL-->
    <!--where    ASSIGN_SEQ_NO= #{assignSeqNo}-->
    <!--</delete>-->


    <select id="getAssignInfoDtlCount" parameterType="java.util.Map"
            resultType="java.util.Map">
        select COUNT(*) NUM
        from OC_INPUT_ASS_DTL
        <where>
        <if test="assignSeqNo != null">
            and ASSIGN_SEQ_NO = #{assignSeqNo}
        </if>
        <if test="changeNo != null">
            and CHANGE_NO = #{changeNo}
        </if>
        <if test="changeDate != null">
            and CHANGE_DATE = #{changeDate}
        </if>
        <if test="changeSession != null">
            and CHANGE_SESSION = #{changeSession}
        </if>
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>

</mapper>
