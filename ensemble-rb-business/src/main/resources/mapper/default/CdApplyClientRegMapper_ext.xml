<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdApplyClientReg">
    <select id="getCdApplyClientRegListbyApplyNos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdApplyClientReg">
        select
        <include refid="Base_Column"/>
        from CD_APPLY_CLIENT_REG
        where APPLY_NO IN
        <foreach item="item" index="index" collection="applyNos" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
