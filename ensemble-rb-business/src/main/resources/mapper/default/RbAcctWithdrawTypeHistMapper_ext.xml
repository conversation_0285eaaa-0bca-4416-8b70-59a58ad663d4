<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawTypeHist">

    <select id="getCountByWithdrawKey" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select count(1)
    from RB_ACCT_WITHDRAW_TYPE_Hist
    where CLIENT_NO = #{clientNo} and WITHDRAW_KEY = #{withdrawKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getListByWithdrawKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawTypeHist">
    select <include refid="Base_Column"/>
    from RB_ACCT_WITHDRAW_TYPE_HIST
    where WITHDRAW_KEY = #{withdrawKey}
    <if test="clientNo != null and clientNo !=''">
        AND CLIENT_NO = #{clientNo}
    </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    order by TRAN_DATE asc
    </select>
</mapper>
