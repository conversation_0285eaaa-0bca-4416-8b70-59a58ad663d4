<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortUpdateHist">

  <select id="selectByChangeDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortUpdateHist">
    SELECT <include refid="Base_Column"/>
    FROM RB_FEE_AMORT_UPDATE_HIST
    <where>
      <if test="scSeqNo != null and scSeqNo.length() > 0">
        AND SC_SEQ_NO = #{scSeqNo}
      </if>
      <if test="startDate != null and endDate != null ">
        AND LAST_CHANGE_DATE BETWEEN #{startDate} AND #{endDate}
      </if>
    </where>
  </select>
</mapper>