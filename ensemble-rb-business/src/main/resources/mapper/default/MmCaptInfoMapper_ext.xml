<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmCaptInfo">
  <!-- Created by admin on 2019/01/14 16:23:44. -->
  <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmCaptInfo">
    update MM_CAPT_INFO
      set GL_POSTED = 'Y'
    where SEQ_NO = #{seqNo}
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmCaptInfo">
    insert into MM_CAPT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="captDate != null">
        CAPT_DATE,
      </if>
      <if test="intClass != null">
        INT_CLASS,
      </if>
      <if test="acctNo != null">
        ACCT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="branch != null">
        BRANCH,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="intPosted != null">
        INT_POSTED,
      </if>
      <if test="intPostedCtd != null">
        INT_POSTED_CTD,
      </if>
      <if test="intAdj != null">
        INT_ADJ,
      </if>
      <if test="intAccrued != null">
        INT_ACCRUED,
      </if>
      <if test="taxPostedCtd != null">
        TAX_POSTED_CTD,
      </if>
      <if test="taxPosted != null">
        TAX_POSTED,
      </if>
      <if test="tranSource != null">
        TRAN_SOURCE,
      </if>
      <if test="taxType != null">
        TAX_TYPE,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="profitCenter != null">
        PROFIT_CENTRE,
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE,
      </if>
      <if test="businessUnit != null">
        BUSINESS_UNIT,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="reversal != null">
        REVERSAL,
      </if>
      <if test="payInt != null">
        PAY_INT,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="accountingStatus != null">
        ACCOUNTING_STATUS,
      </if>
      <if test="glPosted != null">
        GL_POSTED,
      </if>
      <if test="payIntBal != null">
        PAY_INT_BAL,
      </if>
      <if test="tranStatus != null">
        TRAN_STATUS,
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE,
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE,
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE,
      </if>
      <if test="agreeChangeType != null">
        AGREE_CHANGE_TYPE,
      </if>
      <if test="agreeFixedRate != null">
        AGREE_FIXED_RATE,
      </if>
      <if test="agreePercentRate != null">
        AGREE_PERCENT_RATE,
      </if>
      <if test="agreeSpreadRate != null">
        AGREE_SPREAD_RATE,
      </if>
      <if test="splitRateFlag != null">
        SPLIT_RATE_FLAG,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="yearBasis != null">
        YEAR_BASIS,
      </if>
      <if test="startDate != null">
        START_DATE,
      </if>
      <if test="intAmt != null">
        INT_AMT,
      </if>
      <if test="days != null">
        DAYS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="systemId != null">
        #{systemId},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="captDate != null">
        #{captDate},
      </if>
      <if test="intClass != null">
        #{intClass},
      </if>
      <if test="acctNo != null">
        #{acctNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="intPosted != null">
        #{intPosted},
      </if>
      <if test="intPostedCtd != null">
        #{intPostedCtd},
      </if>
      <if test="intAdj != null">
        #{intAdj},
      </if>
      <if test="intAccrued != null">
        #{intAccrued},
      </if>
      <if test="taxPostedCtd != null">
        #{taxPostedCtd},
      </if>
      <if test="taxPosted != null">
        #{taxPosted},
      </if>
      <if test="tranSource != null">
        #{tranSource},
      </if>
      <if test="taxType != null">
        #{taxType},
      </if>
      <if test="taxRate != null">
        #{taxRate},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="profitCenter != null">
        #{profitCenter},
      </if>
      <if test="sourceModule != null">
        #{sourceModule},
      </if>
      <if test="businessUnit != null">
        #{businessUnit},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="reversal != null">
        #{reversal},
      </if>
      <if test="payInt != null">
        #{payInt},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="accountingStatus != null">
        #{accountingStatus},
      </if>
      <if test="glPosted != null">
        #{glPosted},
      </if>
      <if test="payIntBal != null">
        #{payIntBal},
      </if>
      <if test="tranStatus != null">
        #{tranStatus},
      </if>
      <if test="acctSpreadRate != null">
        #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        #{acctPercentRate},
      </if>
      <if test="acctFixedRate != null">
        #{acctFixedRate},
      </if>
      <if test="agreeChangeType != null">
        #{agreeChangeType},
      </if>
      <if test="agreeFixedRate != null">
        #{agreeFixedRate},
      </if>
      <if test="agreePercentRate != null">
        #{agreePercentRate},
      </if>
      <if test="agreeSpreadRate != null">
        #{agreeSpreadRate},
      </if>
      <if test="splitRateFlag != null">
        #{splitRateFlag},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="yearBasis != null">
        #{yearBasis},
      </if>
      <if test="startDate != null">
        #{startDate},
      </if>
      <if test="intAmt != null">
        #{intAmt},
      </if>
      <if test="days != null">
        #{days},
      </if>
    </trim>
  </insert>

  <insert id="insertMbCaptInfo" parameterType="java.util.List">
    <![CDATA[
    insert into MM_CAPT_INFO (
	SEQ_NO,
	INTERNAL_KEY,
	CAPT_DATE,
	INT_CLASS,
	ACCT_NO,
	PROD_TYPE,
	BRANCH,
	SOURCE_TYPE,
	CCY,
	CLIENT_NO,
	INT_POSTED,
	INT_POSTED_CTD,
	INT_ADJ,
	INT_ACCRUED,
	TRAN_SOURCE,
	ACTUAL_RATE,
	FLOAT_RATE,
	REAL_RATE,
	PROFIT_CENTRE,
	SOURCE_MODULE,
	BUSINESS_UNIT,
	CLIENT_TYPE,
	REMARK,
	REVERSAL,
	ACCOUNTING_STATUS,
	SPLIT_RATE_FLAG,
	START_DATE,
    REFERENCE
    )
    values(
	#{seqNo},
	#{internalKey},
	#{captDate},
	#{intClass},
	#{acctNo},
	#{prodType},
	#{branch},
	#{sourceType},
	#{ccy},
	#{clientNo},
	#{intPosted},
	#{intPostedCtd},
	#{intAdj},
	#{intAccrued},
	#{tranSource},
	#{actualRate},
	#{floatRate},
	#{realRate},
	#{profitCenter},
	#{sourceModule},
	#{businessUnit},
	#{clientType},
	#{remark},
	#{reversal},
	#{accountingStatus},
	#{splitRateFlag},
	#{startDate},
    #{reference})
        ]]>
  </insert>

<!--
  <resultMap id="IrlGlInfoBean" type="com.dcits.limarket.data.dbmodel.IrlGlInfo">
    <result column="EFFECT_DATE" property="effectDate" jdbcType="DATE" javaType="String"/>
    <result column="CAPT_DATE" property="captDate" jdbcType="DATE" javaType="String"/>
    <result column="TRAN_DATE" property="tranDate" jdbcType="DATE" javaType="String"/>
  </resultMap>
-->
  <select id="getCaptInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmCaptInfo">
    SELECT *
    FROM MM_CAPT_INFO
    WHERE INTERNAL_KEY = #{internalKey}
    AND INT_CLASS = #{intClass}
  </select>
</mapper>
