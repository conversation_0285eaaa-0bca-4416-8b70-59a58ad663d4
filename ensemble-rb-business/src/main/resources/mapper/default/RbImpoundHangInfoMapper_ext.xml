<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundHangInfo">

    <select id="getMbImpoundHangInfoByCondition" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundHangInfo">
        select
        <include refid="Base_Column"/>
        from RB_IMPOUND_HANG_INFO
        where 1=1
        <if test="startDate != null ">
            AND
            <![CDATA[  #{startDate}<= TRAN_DATE ]]>
        </if>
        <if test="endDate != null ">
            AND
            <![CDATA[ TRAN_DATE <=  #{endDate}]]>
        </if>
        <if test="baseAcctNo != null and  baseAcctNo != '' ">
            AND BASE_ACCT_NO  = #{baseAcctNo}
        </if>
        <if test="payerBaseAcctNo != null and  payerBaseAcctNo != '' ">
            AND PAYER_BASE_ACCT_NO  = #{payerBaseAcctNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbImpoundHangInfoByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundHangInfo">
        select
        <include refid="Base_Column"/>
        from RB_IMPOUND_HANG_INFO
        where 1=1
        <if test="reference != null and  reference != '' ">
            AND REFERENCE  = #{reference}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

</mapper>
