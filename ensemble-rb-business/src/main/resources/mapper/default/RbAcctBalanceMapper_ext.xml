<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">

    <select id="getRbAcctBalanceForUpdate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance" flushCache="true"
            useCache="false">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_BALANCE
        where INTERNAL_KEY = #{internalKey}
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        for update
    </select>

    <update id="updatePrevBalForEod" parameterType="java.util.Map">
        update RB_ACCT_BALANCE
        set total_amount_last_prev = total_amount_prev,
        total_amount_prev = total_amount ,
        agg_bal=IFNULL(agg_bal,0)+abs(total_amount)*(DATEDIFF(#{runDate}, #{lastRunDate})),
        LAST_BAL_UPD_DATE = #{runDate,jdbcType=DATE}
        where
        LAST_BAL_UPD_DATE <![CDATA[ <> ]]> #{runDate}
        AND internal_key BETWEEN #{startKey,jdbcType=BIGINT} and #{endKey,jdbcType=BIGINT}
        <!-- Multi-legal transformation by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <update id="updateLastPrevBalForEod" parameterType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            update RB_ACCT_BALANCE
            set total_amount_last_prev = total_amount_prev
            where total_amount_prev <![CDATA[  <> ]]> ifnull(total_amount_last_prev,0)
            and LAST_BAL_UPD_DATE <![CDATA[ <> ]]> #{runDate}
            AND internal_key BETWEEN #{startKey,jdbcType=BIGINT} and #{endKey,jdbcType=BIGINT}
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </if>
        <if test="_databaseId == 'oracle'">
            update RB_ACCT_BALANCE
            set total_amount_last_prev = total_amount_prev
            where total_amount_prev <![CDATA[  <> ]]> NVL(total_amount_last_prev,0)
            and LAST_BAL_UPD_DATE <![CDATA[ <> ]]> #{runDate}
            AND internal_key BETWEEN #{startKey,jdbcType=BIGINT} and #{endKey,jdbcType=BIGINT}
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </if>
    </update>

    <select id="queryEffectBalAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT internal_key, client_no
        FROM RB_ACCT_BALANCE
        WHERE
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <![CDATA[(
            total_amount <> total_amount_prev
            )
            AND internal_key BETWEEN #{startKey} and #{endKey}
            and LAST_BAL_UPD_DATE < #{runDate,jdbcType=DATE}
    ]]>
    </select>

    <select id="getProductBalanceCurrentResult" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT SUM(A.TOTAL_AMOUNT) as TOTAL_AMOUNT
        FROM RB_ACCT_BALANCE A
        LEFT JOIN RB_ACCT B ON A.INTERNAL_KEY = B.INTERNAL_KEY
        WHERE A.CLIENT_NO = B.CLIENT_NO
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND B.PROD_TYPE= #{prodType}
        AND B.ACCT_BRANCH= #{branch}
        AND B.ACCT_CCY= #{ccy}
        AND B.ACCT_STATUS != 'C'
    </select>
    <select id="getHotAcctBalance" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance"
            parameterType="java.util.Map" flushCache="true" useCache="false">
        SELECT b.internal_key,
        b.total_amount + a.tran_amt AS total_amount,
        b.total_amount_prev,
        b.pld_amount,
        b.finreg_amount,
        b.dos_amount,
        b.od_amount,
        b.odd_amount,
        b.last_bal_upd_date,
        b.last_change_date,
        b.last_change_user_id,
        b.client_no,
        b.dac_value,
        b.tran_timestamp,
        b.company
        FROM (  SELECT internal_key,
        client_no,
        SUM (
        CASE cr_dr_ind
        WHEN 'D' THEN tran_amt
        WHEN 'C' THEN tran_amt * -1
        END)
        tran_amt
        FROM rb_upd_balance_tran
        WHERE     bal_calc_flag = 'N'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND internal_key = #{internalKey, jdbcType=BIGINT}
        AND client_no = #{clientNo, jdbcType=VARCHAR}
        AND seq_no != #{seqNo, jdbcType=VARCHAR}
        GROUP BY internal_key, client_no) a,
        rb_acct_balance b
        WHERE     a.internal_key = b.internal_key
        AND a.client_no = b.client_no
        AND b.internal_key = #{internalKey, jdbcType=BIGINT}
        AND b.client_no = #{clientNo, jdbcType=VARCHAR}
    </select>

    <select id="getUsefulBalanceByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT
        <include refid="Base_Column"/>
        from RB_ACCT_BALANCE
        where CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND (total_amount + pld_amount) &lt; 0
        ORDER BY total_amount + pld_amount asc
    </select>


    <select id="getBalanceByStageCode" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT BA.INTERNAL_KEY,
        BA.TOTAL_AMOUNT,
        BA.TOTAL_AMOUNT_PREV,
        BA.PLD_AMOUNT,
        BA.FINREG_AMOUNT,
        BA.DOS_AMOUNT,
        BA.OD_AMOUNT,
        BA.ODD_AMOUNT,
        BA.LAST_BAL_UPD_DATE,
        BA.LAST_CHANGE_DATE,
        BA.LAST_CHANGE_USER_ID,
        BA.CLIENT_NO,
        BA.DAC_VALUE,
        BA.TRAN_TIMESTAMP,
        BA.COMPANY
        from RB_ACCT_BALANCE BA, RB_ACCT_ATTACH attc
        where BA.INTERNAL_KEY = attc.INTERNAL_KEY
        and BA.CLIENT_NO = attc.CLIENT_NO
        and BA.CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        and attc.STAGE_CODE = #{stageCode}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>

    <select id="getTotalAmountFinAcct" parameterType="java.util.Map"
            resultType="java.math.BigDecimal">
        SELECT SUM(-1*a.total_amount)
        from RB_ACCT_BALANCE a
        left join  rb_acct b
        on a.internal_key =b.internal_key
        and a.client_no =b.client_no
        where b.CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND b.COMPANY = #{company}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND b.BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="prodTypes != null ">
            AND PROD_TYPE in
            <foreach item="item" index="index" collection="prodTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTotalAmountAgreementAcct" parameterType="java.util.Map"
            resultType="java.math.BigDecimal">
        SELECT SUM(-1*b.total_amount)
        from RB_ACCT a
        left join  rb_acct_balance b
        on a.internal_key =b.internal_key
        where (a.BASE_ACCT_NO = #{baseAcctNo} or a.CARD_NO = #{baseAcctNo}) AND a.AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR}
    </select>

    <select id="getRbBalanceList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT
        <include refid="Base_Column"/>
        from RB_ACCT_BALANCE
        where TOTAL_AMOUNT_PREV <![CDATA[ <> ]]> 0
        <if test="longs != null ">
            and INTERNAL_KEY in
            <foreach item="item" index="index" collection="longs" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getFinancialTotalAmount" parameterType="java.util.Map"
            resultType="java.math.BigDecimal">
        SELECT SUM(-1*b.total_amount - b.pld_amount) as finAmt
        from rb_acct_balance b
        where b.internal_key in
        (select a.internal_key
        from RB_ACCT a
        where (a.BASE_ACCT_NO = #{baseAcctNo} or a.CARD_NO = #{baseAcctNo})
        <if test="prodTypes != null ">
            AND a.PROD_TYPE in
            <foreach item="item" index="index" collection="prodTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="prodType != null and prodType.length() > 0">
            AND a.PROD_TYPE = #{prodType}
        </if>
        <if test="agreementId != null and agreementId.length() > 0">
            AND a.AGREEMENT_ID = #{agreementId}
        </if>
        AND a.AGREEMENT_ID IS NOT NULL
        AND a.ACCT_STATUS ='A')
    </select>

    <select id="getSumBalByInternalKeys" parameterType="java.util.Map"
            resultType="java.math.BigDecimal">
        SELECT SUM(total_amount)
        from rb_acct_balance
        <where>
            <if test="internalKeys != null ">
                INTERNAL_KEY in
                <foreach item="item" index="index" collection="internalKeys" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getSpecialProdBalance" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT T1.* FROM RB_ACCT_BALANCE T1,RB_ACCT T2
        <where>
            <if test="clientNo != null and clientNo != ''">
                AND T2.CLIENT_NO = #{clientNo}
            </if>
                AND T1.INTERNAL_KEY = T2.INTERNAL_KEY AND T2.ACCT_STATUS not in ('C','R')
            <if test="prodType != null and prodType != ''">
                AND T2.PROD_TYPE = #{prodType}
            </if>
            <if test="acctCcy != null and acctCcy != ''">
                AND T2.ACCT_CCY = #{acctCcy}
            </if>
        </where>
    </select>

    <select id="selectListByInternalKeys" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT
        <include refid="Base_Column"/>
        from RB_ACCT_BALANCE
        where INTERNAL_KEY in
            <foreach item="item" index="index" collection="internalKeys" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    <select id="selectByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT
        <include refid="Base_Column"/>
        from RB_ACCT_BALANCE
        where
        <if test="internalKey != null ">
             INTERNAL_KEY = #{internalKey}
        </if>
    </select>

    <update id="updateClientNo" parameterType="java.util.Map">
        update RB_ACCT_BALANCE set CLIENT_NO = #{clientNo},TRAN_TIMESTAMP = #{tranTimestamp}
        where internal_key = #{internalKey} and CLIENT_NO = #{oldClientNo}
    </update>
    <select id="getBalanceListByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
        SELECT
        <include refid="Base_Column"/>
        from RB_ACCT_BALANCE
        <where>
            <if test="longs != null ">
                and INTERNAL_KEY in
                <foreach item="item" index="index" collection="longs" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateBalanceLastChangeDate" parameterType="java.util.Map">
        update RB_ACCT_BALANCE set  last_change_date = #{lastChangeDate}
        where internal_key = #{internalKey} and CLIENT_NO = #{clientNo}
    </update>
</mapper>
