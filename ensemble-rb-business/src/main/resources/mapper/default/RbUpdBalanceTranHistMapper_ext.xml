<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTranHist">


    <select id="getRbUpdBalanceTranHistMaxSeqNo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTranHist">
        SELECT * FROM RB_UPD_BALANCE_TRAN_HIST WHERE TIMES_SEQ_NO = (
        SELECT MAX(TIMES_SEQ_NO) AS timesSeqNo
        FROM RB_UPD_BALANCE_TRAN_HIST
        WHERE  internal_key =#{internalKey}
        AND tran_date =#{runDate,jdbcType=DATE}
        and client_no =#{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        )
    </select>
</mapper>
