<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">

  <!--多法人改造 by LIYUANV-->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    select <include refid="Base_Column"/>
    from CD_CARD_FILE_JOB
    where BATCH_JOB_NO = #{batchJobNo}
      <if test="fileType != null">
        AND FILE_TYPE = #{fileType}
      </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
    <select id="getCdCardFileJobByPage" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    select <include refid="Base_Column"/>
    from CD_CARD_FILE_JOB
    where APPLY_DATE    between #{startDate} and #{endDate}
      <if test="applyBranch != null and applyBranch != ''">
          AND TRAN_BRANCH = #{applyBranch}
      </if>
      <if test="prodType != null and prodType != ''">
          AND PROD_TYPE = #{prodType}
    </if>
      <if test="sameCardFlag != null and sameCardFlag != ''">
        AND SAME_CARD_FLAG = #{sameCardFlag}
      </if>
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCdCardFileJobByApplyNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    select <include refid="Base_Column"/>
    from CD_CARD_FILE_JOB
    where APPLY_NO  = #{applyNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
   <select id="getCdCardFileJobByStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    select <include refid="Base_Column"/>
    from CD_CARD_FILE_JOB
    <where>
      <if test="fileStatus != null">
          AND FILE_STATUS = #{fileStatus}
      </if>
      <if test="fileType != null">
          AND FILE_TYPE = #{fileType}
      </if>
     <if test="company != null and company != '' ">
       AND COMPANY = #{company}
     </if>
    </where>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCdCardFileJobByStatusApplyNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    select <include refid="Base_Column"/>
    from CD_CARD_FILE_JOB
    <where>
    <if test="fileStatus != null">
      AND FILE_STATUS = #{fileStatus}
    </if>
    <if test="fileType != null">
      AND FILE_TYPE = #{fileType}
    </if>
    <if test="applyNo != null">
      AND APPLY_NO = #{applyNo}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <!--多法人改造 by LIYUANV-->
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    delete from CD_CARD_FILE_JOB
    where BATCH_JOB_NO = #{batchJobNo}
      <if test="fileType != null">
        AND FILE_TYPE = #{fileType}
      </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <!--多法人改造 by LIYUANV-->
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    update CD_CARD_FILE_JOB
    <set>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="cardNum != null">
        CARD_NUM = #{cardNum},
      </if>
      <if test="cardNoFrom != null">
        CARD_NO_FROM = #{cardNoFrom},
      </if>
      <if test="applyNo != null">
        APPLY_NO = #{applyNo},
      </if>
      <if test="applyDate != null">
        APPLY_DATE = #{applyDate},
      </if>
      <if test="fileStatus != null">
        FILE_STATUS = #{fileStatus},
      </if>
      <if test="fileName != null">
        FILE_NAME = #{fileName},
      </if>
      <if test="filePath != null">
        FILE_PATH = #{filePath},
      </if>
      <if test="startTime != null">
        START_TIME = #{startTime},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="makeCardType != null">
        MAKE_CARD_TYPE = #{makeCardType},
      </if>
      <if test="sameCardFlag != null and  sameCardFlag != '' ">
        SAME_CARD_FLAG = #{sameCardFlag}
      </if>
      <if test="sourceType != null and  sourceType != '' ">
        SOURCE_TYPE = #{sourceType}
      </if>
    </set>
    where BATCH_JOB_NO = #{batchJobNo}
        AND FILE_TYPE = #{fileType}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardFileJob">
    insert into CD_CARD_FILE_JOB
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchJobNo != null">
        BATCH_JOB_NO,
      </if>
      <if test="fileType != null">
        FILE_TYPE,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="cardNum != null">
        CARD_NUM,
      </if>
      <if test="cardNoFrom != null">
        CARD_NO_FROM,
      </if>
      <if test="applyNo != null">
        APPLY_NO,
      </if>
      <if test="applyDate != null">
        APPLY_DATE,
      </if>
      <if test="fileStatus != null">
        FILE_STATUS,
      </if>
      <if test="fileName != null">
        FILE_NAME,
      </if>
      <if test="filePath != null">
        FILE_PATH,
      </if>

      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="makeCardType != null">
        MAKE_CARD_TYPE,
      </if>
      <if test="sameCardFlag != null and  sameCardFlag != '' ">
        SAME_CARD_FLAG,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchJobNo != null">
        #{batchJobNo},
      </if>
      <if test="fileType != null">
        #{fileType},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="cardNum != null">
        #{cardNum},
      </if>
      <if test="cardNoFrom != null">
        #{cardNoFrom},
      </if>
      <if test="applyNo != null">
        #{applyNo},
      </if>
      <if test="applyDate != null">
        #{applyDate},
      </if>
      <if test="fileStatus != null">
        #{fileStatus},
      </if>
      <if test="fileName != null">
        #{fileName},
      </if>
      <if test="filePath != null">
        #{filePath},
      </if>
      <if test="startTime != null">
        #{startTime},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="makeCardType != null">
        #{makeCardType},
      </if>
      <if test="sameCardFlag != null and  sameCardFlag != '' ">
        #{sameCardFlag},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
    </trim>
  </insert>
</mapper>
