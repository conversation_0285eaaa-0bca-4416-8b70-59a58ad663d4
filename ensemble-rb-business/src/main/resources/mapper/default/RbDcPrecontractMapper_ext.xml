<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    where PRECONTRACT_NO = #{precontractNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    delete from RB_DC_PRECONTRACT
    where PRECONTRACT_NO = #{precontractNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    update RB_DC_PRECONTRACT
    <set>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="precontractType != null">
        PRECONTRACT_TYPE = #{precontractType},
      </if>
      <if test="precontractDate != null">
        PRECONTRACT_DATE = #{precontractDate},
      </if>
      <if test="precontractOpenDate != null">
        PRECONTRACT_OPEN_DATE = #{precontractOpenDate},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="issueYear != null">
        ISSUE_YEAR = #{issueYear},
      </if>
      <if test="stageCode != null">
        STAGE_CODE = #{stageCode},
      </if>
      <if test="issueStartDate != null">
        ISSUE_START_DATE = #{issueStartDate},
      </if>
      <if test="issueEndDate != null">
        ISSUE_END_DATE = #{issueEndDate},
      </if>
      <if test="issueAmt != null">
        ISSUE_AMT = #{issueAmt},
      </if>
      <if test="precontractCcy != null">
        PRECONTRACT_CCY = #{precontractCcy},
      </if>
      <if test="precontractAmt != null">
        PRECONTRACT_AMT = #{precontractAmt},
      </if>
      <if test="seqNo != null">
        SEQ_NO = #{seqNo},
      </if>
      <if test="intCalcType != null">
        INT_CALC_TYPE = #{intCalcType},
      </if>
      <if test="intType != null">
        INT_TYPE = #{intType},
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE = #{actualRate},
      </if>
      <if test="floatRate != null">
        FLOAT_RATE = #{floatRate},
      </if>
      <if test="realRate != null">
        REAL_RATE = #{realRate},
      </if>
      <if test="othInternalKey != null">
        OTH_INTERNAL_KEY = #{othInternalKey},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="precontractBranch != null">
        PRECONTRACT_BRANCH = #{precontractBranch},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="precontractStatus != null">
        PRECONTRACT_STATUS = #{precontractStatus},
      </if>
      <if test="delUserId != null">
        DEL_USER_ID = #{delUserId},
      </if>
      <if test="deleteDate != null">
        DELETE_DATE = #{deleteDate},
      </if>
      <if test="delReason != null">
        DEL_REASON = #{delReason},
      </if>
      <if test="failureReason != null">
        FAILURE_REASON = #{failureReason},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="narrative != null">
        NARRATIVE = #{narrative},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="autoSettleFlag != null">
        AUTO_SETTLE_FLAG = #{autoSettleFlag},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo},
      </if>
      <if test="printCnt != null">
        PRINT_CNT = #{printCnt},
      </if>
      <if test="channel != null">
        CHANNEL = #{channel},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        ACCT_CCY = #{acctCcy},
      </if>
      <if test="acctName != null">
        ACCT_NAME = #{acctName},
      </if>
      <if test="othBaseAcctNo != null">
        OTH_BASE_ACCT_NO = #{othBaseAcctNo},
      </if>
      <if test="othProdType != null">
        OTH_PROD_TYPE = #{othProdType},
      </if>
      <if test="othAcctSeqNo != null">
        OTH_ACCT_SEQ_NO = #{othAcctSeqNo},
      </if>
      <if test="othCcy != null">
        OTH_CCY = #{othCcy},
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS = #{stageProdClass},
      </if>
      <if test="cycleIntFlag != null and  cycleIntFlag != '' ">
        CYCLE_INT_FLAG = #{cycleIntFlag},
      </if>
      <if test="cycleFreq != null and  cycleFreq != '' ">
        CYCLE_FREQ = #{cycleFreq},
      </if>
      <if test="intDay != null and  intDay != '' ">
        INT_DAY = #{intDay},
      </if>
      <if test="nextCycleDate != null">
        NEXT_CYCLE_DATE = #{nextCycleDate}
      </if>
    </set>
    where PRECONTRACT_NO = #{precontractNo}
        AND  CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    insert into RB_DC_PRECONTRACT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="precontractNo != null">
        PRECONTRACT_NO,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="precontractType != null">
        PRECONTRACT_TYPE,
      </if>
      <if test="precontractDate != null">
        PRECONTRACT_DATE,
      </if>
      <if test="precontractOpenDate != null">
        PRECONTRACT_OPEN_DATE,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="issueYear != null">
        ISSUE_YEAR,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="issueStartDate != null">
        ISSUE_START_DATE,
      </if>
      <if test="issueEndDate != null">
        ISSUE_END_DATE,
      </if>
      <if test="issueAmt != null">
        ISSUE_AMT,
      </if>
      <if test="precontractCcy != null">
        PRECONTRACT_CCY,
      </if>
      <if test="precontractAmt != null">
        PRECONTRACT_AMT,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="intCalcType != null">
        INT_CALC_TYPE,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="othInternalKey != null">
        OTH_INTERNAL_KEY,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="precontractBranch != null">
        PRECONTRACT_BRANCH,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="precontractStatus != null">
        PRECONTRACT_STATUS,
      </if>
      <if test="delUserId != null">
        DEL_USER_ID,
      </if>
      <if test="delAuthUserId != null">
        DEL_AUTH_USER_ID,
      </if>
      <if test="deleteDate != null">
        DELETE_DATE,
      </if>
      <if test="delReason != null">
        DEL_REASON,
      </if>
      <if test="failureReason != null">
        FAILURE_REASON,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="narrative != null">
        NARRATIVE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="autoSettleFlag != null">
        AUTO_SETTLE_FLAG,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="printCnt != null">
        PRINT_CNT,
      </if>
      <if test="channel != null">
        CHANNEL,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="acctName != null">
        ACCT_NAME,
      </if>
      <if test="othBaseAcctNo != null">
        OTH_BASE_ACCT_NO,
      </if>
      <if test="othProdType != null">
        OTH_PROD_TYPE,
      </if>
      <if test="othAcctSeqNo != null">
        OTH_ACCT_SEQ_NO,
      </if>
      <if test="othCcy != null">
        OTH_CCY,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
      <if test="cycleIntFlag != null and  cycleIntFlag != '' ">
        CYCLE_INT_FLAG,
      </if>
      <if test="cycleFreq != null and  cycleFreq != '' ">
        CYCLE_FREQ,
      </if>
      <if test="intDay != null and  intDay != '' ">
        INT_DAY,
      </if>
      <if test="nextCycleDate != null">
        NEXT_CYCLE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="precontractNo != null">
        #{precontractNo},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="precontractType != null">
        #{precontractType},
      </if>
      <if test="precontractDate != null">
        #{precontractDate},
      </if>
      <if test="precontractOpenDate != null">
        #{precontractOpenDate},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="issueYear != null">
        #{issueYear},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="issueStartDate != null">
        #{issueStartDate},
      </if>
      <if test="issueEndDate != null">
        #{issueEndDate},
      </if>
      <if test="issueAmt != null">
        #{issueAmt},
      </if>
      <if test="precontractCcy != null">
        #{precontractCcy},
      </if>
      <if test="precontractAmt != null">
        #{precontractAmt},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="intCalcType != null">
        #{intCalcType},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="othInternalKey != null">
        #{othInternalKey},
      </if>
      <if test="restraintSeqNo != null">
        #{restraintSeqNo},
      </if>
      <if test="precontractBranch != null">
        #{precontractBranch},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="precontractStatus != null">
        #{precontractStatus},
      </if>
      <if test="delUserId != null">
        #{delUserId},
      </if>
      <if test="delAuthUserId != null">
        #{delAuthUserId},
      </if>
      <if test="deleteDate != null">
        #{deleteDate},
      </if>
      <if test="delReason != null">
        #{delReason},
      </if>
      <if test="failureReason != null">
        #{failureReason},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="narrative != null">
        #{narrative},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="autoSettleFlag != null">
        #{autoSettleFlag},
      </if>
      <if test="voucherNo != null">
        #{voucherNo},
      </if>
      <if test="printCnt != null">
        #{printCnt},
      </if>
      <if test="channel != null">
        #{channel},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="acctName != null">
        #{acctName},
      </if>
      <if test="othBaseAcctNo != null">
        #{othBaseAcctNo},
      </if>
      <if test="othProdType != null">
        #{othProdType},
      </if>
      <if test="othAcctSeqNo != null">
        #{othAcctSeqNo},
      </if>
      <if test="othCcy != null">
        #{othCcy},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
      <if test="cycleIntFlag != null and  cycleIntFlag != '' ">
        #{cycleIntFlag},
      </if>
      <if test="cycleFreq != null and  cycleFreq != '' ">
        #{cycleFreq},
      </if>
      <if test="intDay != null and  intDay != '' ">
        #{intDay},
      </if>
      <if test="nextCycleDate != null">
        #{nextCycleDate},
      </if>
    </trim>
  </insert>
  <select id="getByPrecontractNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    where  PRECONTRACT_STATUS != 'R'
    <if test="precontractNo !='' and precontractNo != null">
      AND PRECONTRACT_NO = #{precontractNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!--<select id="getDcPrecontractForEodSplit" parameterType="java.util.Map" resultType="java.util.Map">
    <![CDATA[
      SELECT count(*) ROW_COUNT
      from RB_DC_PRECONTRACT
      where ( PRECONTRACT_STATUS = 'P' and PRECONTRACT_OPEN_DATE = #{runDate})
      or ( PRECONTRACT_STATUS = 'S' and ISSUE_START_DATE - 1 = #{runDate})
      order by PRECONTRACT_NO DESC
    ]]>
  </select>-->
  <select id="getDcPrecontractForEod" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT

      where ( PRECONTRACT_STATUS = 'P' and PRECONTRACT_OPEN_DATE <![CDATA[<= ]]>#{runDate})
      or ( PRECONTRACT_STATUS = 'S' and ISSUE_START_DATE <![CDATA[<= ]]>#{runDate})

    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
      order by PRECONTRACT_NO DESC

  </select>
  <select id="getByOpenInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    where PRECONTRACT_TYPE = #{precontractType}
    AND INTERNAL_KEY = #{openInternalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByOpenInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    where  INTERNAL_KEY = #{openInternalKey}
    AND  CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getDcPrecontractByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    <where>
    <if test="clientNo != null and clientNo !=''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="precontractBranch != null and precontractBranch !=''">
      AND PRECONTRACT_BRANCH = #{precontractBranch}
    </if>
    <if test="stageCode != null and stageCode !=''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null and prodType !=''">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="getDcPrecontractByProdTypeAndStageCode" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT dc
    WHERE
    PROD_TYPE = #{prodType, jdbcType=VARCHAR}
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear, jdbcType=VARCHAR}
    </if>
    <if test="timeStart != null and timeEnd != null">
      AND
      ISSUE_START_DATE &gt; #{timeStart, jdbcType=VARCHAR}
      AND
      ISSUE_END_DATE &lt; #{timeEnd, jdbcType=VARCHAR}
    </if>
    <if test="timeStart != null and timeEnd == null">
      AND
      ISSUE_END_DATE &gt; #{timeStart, jdbcType=VARCHAR}
    </if>
    <if test="timeStart == null and timeEnd != null">
      AND
      ISSUE_END_DATE &lt; #{timeEnd, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY ISSUE_YEAR,ISSUE_START_DATE DESC
  </select>
  <select id="selectByInternalKey"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    WHERE OTH_INTERNAL_KEY = #{internalKey}
    AND PRECONTRACT_STATUS != 'R'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByDate"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    WHERE PRECONTRACT_STATUS != 'R'
    AND  PRECONTRACT_DATE between #{issueStartDate} and #{issueEndDate}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- Optimize segmented sql -->
  <select id="queryDcPrecontractForKeys" parameterType="java.util.Map" resultType="String">
    SELECT precontract_no
    FROM RB_DC_PRECONTRACT

    where (
    <![CDATA[
      ( PRECONTRACT_STATUS = 'P' and ( PRECONTRACT_OPEN_DATE <= #{runDate}))
    or ( PRECONTRACT_STATUS = 'S' and ( ISSUE_START_DATE <= #{runDate}))
    )]]>
    AND (precontract_no BETWEEN #{startKey} and #{endKey})
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY precontract_no

  </select>
  <select id="queryDcPrecontractForDetail" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    where
    <![CDATA[(
    ( PRECONTRACT_STATUS = 'P' and (PRECONTRACT_OPEN_DATE <= #{runDate}) )
    or ( PRECONTRACT_STATUS = 'S' and (ISSUE_START_DATE <= #{runDate}) )
    )]]>
     AND (precontract_no BETWEEN #{startKey} and #{endKey})
     <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY precontract_no

  </select>
  <select id="getDcPrecontractForEodSplit" parameterType="java.util.Map" resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      MIN(precontract_no) START_KEY,
      MAX(precontract_no) END_KEY,COUNT(1) ROW_COUNT
      FROM
      (
      SELECT
      DISTINCT precontract_no,
      @rownum :=@rownum + 1 AS rownum
      FROM
      RB_DC_PRECONTRACT,
      (SELECT @rownum := -1) t
      <![CDATA[
      where ( PRECONTRACT_STATUS = 'P' and ( PRECONTRACT_OPEN_DATE <={runDate}))
      or ( PRECONTRACT_STATUS = 'S' and ( ISSUE_START_DATE <= #{runDate} ))
      ]]>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>

      ORDER BY precontract_no
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
    </if>
    <if test="_databaseId == 'oracle'">
      /**/
      SELECT MIN (precontract_no) START_KEY, MAX (precontract_no) END_KEY,COUNT(1) ROW_COUNT
      FROM (SELECT DISTINCT precontract_no
      FROM RB_DC_PRECONTRACT
      <![CDATA[
      where (( PRECONTRACT_STATUS = 'P' and (PRECONTRACT_OPEN_DATE <= #{runDate}) )
      or ( PRECONTRACT_STATUS = 'S' and (ISSUE_START_DATE <= #{runDate}) ) )
      AND STAGE_CODE IN ( SELECT STAGE_CODE FROM DC_STAGE_DEFINE WHERE
      ISSUE_START_DATE <= #{runDate}
      AND ISSUE_END_DATE >= #{runDate}
        ]]>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
          )

      ORDER BY precontract_no)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>
  <select id="getByYear"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    WHERE
    INTERNAL_KEY IS NOT NULL
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      AND PRECONTRACT_CCY = #{acctCcy}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="test" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    where ( PRECONTRACT_STATUS = 'P' and PRECONTRACT_OPEN_DATE = #{runDate})
    or ( PRECONTRACT_STATUS = 'S' and ISSUE_START_DATE = #{runDate})
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY precontract_no
  </select>

  <select id="getSumAmt" parameterType="java.util.Map"
          resultType="java.math.BigDecimal">
    SELECT
    SUM (PRECONTRACT_AMT)
    FROM
    RB_DC_PRECONTRACT
    WHERE
    (
    PRECONTRACT_STATUS = 'O'
    OR PRECONTRACT_TYPE = 'G'
    )
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getByInternalKeyAndPreDate"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    WHERE
    PRECONTRACT_STATUS  in ('S','P')
    <if test="internalKey != null and internalKey != ''">
      AND OTH_INTERNAL_KEY = #{internalKey}
    </if>
    <if test="preStartDate != null">
      AND PRECONTRACT_DATE &gt;= #{preStartDate}
    </if>
    <if test="preEndDate != null">
      AND PRECONTRACT_DATE &lt;= #{preEndDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByCountLimit"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT SUM(PRECONTRACT_AMT) AS PRECONTRACT_AMT
    FROM RB_DC_PRECONTRACT
    WHERE PRECONTRACT_STATUS not in ('R','D')
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getByStageAndProd"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    <where>
    <if test="issueYear != null and issueYear != ''">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE= #{stageCode}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <!--by zhuxyw-->
  <select id="getDataByClientNoAndStageCode"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    <where>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE= #{stageCode}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="queryDcPrecontract"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    <where>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode} AND PRECONTRACT_STATUS IN ('S')
    </if>
    <if test="baseAcctNo != null and baseAcctNo != ''">
      AND OTH_BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="acctSeqNo != null and acctSeqNo != ''">
      AND OTH_ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="prodType != null and prodType != ''">
      AND OTH_PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null and acctCcy != ''">
      AND OTH_CCY = #{acctCcy}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="queryByBaseAcctNo"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    <where>
    <if test="othBaseAcctNo != null and othBaseAcctNo != ''">
      AND OTH_BASE_ACCT_NO = #{othBaseAcctNo} AND PRECONTRACT_STATUS = 'S' AND STAGE_PROD_CLASS = 'ST'
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="selectListByStageCode"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    <where>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode} AND PRECONTRACT_TYPE = 'S'
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="selectRedemptionListByStageCode"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    <where>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="company != null and company != ''">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="getDcPrecontractByStageCode" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    <where>
    <if test="clientNo != null and clientNo !=''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="precontractBranch != null and precontractBranch !=''">
      AND PRECONTRACT_BRANCH = #{precontractBranch}
    </if>
    <if test="stageCode != null and stageCode !=''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>


  <select id="getByOthBaseAcctNoAndPreDate"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    WHERE
    PRECONTRACT_STATUS  in ('S','P')
    <if test="othBaseAcctNo != null and othBaseAcctNo != ''">
      AND OTH_BASE_ACCT_NO = #{othBaseAcctNo}
    </if>
    <if test="preStartDate != null">
      AND PRECONTRACT_DATE &gt;= #{preStartDate}
    </if>
    <if test="preEndDate != null">
      AND PRECONTRACT_DATE &lt;= #{preEndDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectPrecontractData"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT
    <where>
    <if test="subsInternalKey != null and subsInternalKey != ''">
      AND SUBS_INTERNAL_KEY = #{subsInternalKey}
    </if>
    <if test="clientNo != null and  clientNo != '' ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="precontractStatus != null and precontractStatus != ''">
      AND PRECONTRACT_STATUS = #{precontractStatus}
    </if>
    <if test="preStartDate != null">
      AND PRECONTRACT_DATE &gt;= #{preStartDate}
    </if>
    <if test="preEndDate != null">
      AND PRECONTRACT_DATE &lt;= #{preEndDate}
    </if>
    <if test="precontractNo !='' and precontractNo != null">
      AND PRECONTRACT_NO = #{precontractNo}
    </if>
    <if test="depTermInternalKey != null and depTermInternalKey != ''">
      AND DEP_TERM_INTERNAL_KEY = #{depTermInternalKey}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="getBalance" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    SELECT
    SUM( TOTAL_AMOUNT )
    FROM
    (
    SELECT
    ABS(TOTAL_AMOUNT) as TOTAL_AMOUNT
    FROM
    RB_ACCT_BALANCE
    WHERE
    INTERNAL_KEY IN (
    SELECT
    INTERNAL_KEY
    FROM
    RB_DC_PRECONTRACT
    WHERE
    ACCT_CCY = #{acctCcy}
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="branchList != null and branchList.size > 0">
      AND PRECONTRACT_BRANCH IN
      <foreach collection="branchList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="issueYear != null and issueYear != ''">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    AND (ACCT_STATUS != 'C' or ACCT_STATUS is null)
    AND PRECONTRACT_STATUS = 'O'
    AND STAGE_PROD_CLASS = 'DC'
    )
    )
  </select>

  <select id="getBranchProdBalance" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    SELECT
    SUM( TOTAL_AMOUNT )
    FROM
    (
    SELECT
    ABS(TOTAL_AMOUNT) as TOTAL_AMOUNT
    FROM
    RB_ACCT_BALANCE
    WHERE
    INTERNAL_KEY IN (
    SELECT
    INTERNAL_KEY
    FROM
    RB_ACCT
    WHERE
    INDIVIDUAL_FLAG = 'N'
    AND INTERNAL_KEY IN (
    SELECT
    INTERNAL_KEY
    FROM
    RB_DC_PRECONTRACT
    WHERE
    ACCT_CCY = #{acctCcy}
    AND PRECONTRACT_BRANCH = #{branch}
    <if test="issueYear != null and issueYear != ''">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    AND (ACCT_STATUS != 'C' or ACCT_STATUS is null)
    AND PRECONTRACT_STATUS = 'O'
    )
    )
    ) d
  </select>

  <select id="getClosedBalance" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    SELECT
    SUM( TOTAL_AMOUNT )
    FROM
    (
    SELECT
    ABS(TOTAL_AMOUNT) as TOTAL_AMOUNT
    FROM
    RB_ACCT_BALANCE
    WHERE
    INTERNAL_KEY IN (
    SELECT
    INTERNAL_KEY
    FROM
    RB_DC_PRECONTRACT
    WHERE
    PRECONTRACT_STATUS = 'O'
    AND (ACCT_STATUS != 'C' or ACCT_STATUS is null)
    <if test="branchList != null and branchList.size > 0">
      AND PRECONTRACT_BRANCH IN
      <foreach collection="branchList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    AND STAGE_CODE = #{stageCode}
    )
    )
  </select>



  <select id="getByPrecontractrg" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    where PRECONTRACT_TYPE = 'S'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    AND PRECONTRACT_DATE BETWEEN  #{startDate} AND  #{endDate}
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    AND STAGE_PROD_CLASS ='ST'
  </select>

  <select id="getByPrecontractsg" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract">
    SELECT <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT
    where PRECONTRACT_TYPE = 'G'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    AND PRECONTRACT_DATE BETWEEN  #{startDate} AND  #{endDate}
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    AND STAGE_PROD_CLASS ='ST'
  </select>

</mapper>
