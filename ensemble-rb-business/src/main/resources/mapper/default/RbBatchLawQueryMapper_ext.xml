<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchLawQuery">

  <select id="selectByContrastBatNo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchLawQuery">
    select *
    from RB_BATCH_LAW_QUERY
    where BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getSumByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM (1)
    from RB_BATCH_LAW_QUERY
    where BATCH_NO = #{batchNo}
    AND STATUS = 'S'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
