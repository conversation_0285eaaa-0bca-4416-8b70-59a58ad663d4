<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelMsg">

    <select id="selectOneByClientNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelMsg"
            parameterType="java.util.Map" flushCache="true"
            useCache="false">
        select
        <include refid="Base_Column"/>
        from RB_CHANNEL_MSG
        where CLIENT_NO = #{clientNo} and MSG_SET_TYPE = #{msgSetType}
    </select>
    <select id="selectMsgListByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelMsg">
        select
        <include refid="Base_Column"/>
        from RB_CHANNEL_MSG  where CLIENT_NO = #{clientNo}
    </select>
    <select id="selectByClientNoAndType" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelMsg">
        select
        <include refid="Base_Column"/>
        from RB_CHANNEL_MSG  where CLIENT_NO = #{clientNo} and msg_Set_Type = #{msgSetType}
    </select>
</mapper>
