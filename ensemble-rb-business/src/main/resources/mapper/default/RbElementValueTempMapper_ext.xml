<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbElementValueTemp">

  <delete id="deleteAll" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbElementValueTemp">
    delete from RB_ELEMENT_VALUE_TEMP where CLIENT_NO=#{clientNo} and INTERNAL_KEY = #{internalKey} AND ELEM_SEQ_NO=#{elemSeqNo}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </delete>

  <select id="selectByInternalKeyFeeType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbElementValueTemp" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbElementValueTemp">
    select ELEM_SEQ_NO, INTERNAL_KEY, FEE_TYPE,
           ELEMENT_NAME, ELEMENT_VALUE, RUN_DATE
    from RB_ELEMENT_VALUE_TEMP where INTERNAL_KEY = #{internalKey}
                                 and FEE_TYPE = #{feeType}
                                 and RUN_DATE = #{runDate}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </select>
</mapper>
