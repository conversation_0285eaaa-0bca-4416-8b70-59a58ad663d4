<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
  <!-- Created by ch<PERSON><PERSON>d on 2017/07/26 20:11:23. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    select *
    from RB_DC_TOHONOR_REC_INFO
    where INTERNAL_KEY = #{internalKey}
    <if test="tranDate != null">
      AND TRAN_DATE = #{tranDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    delete from RB_DC_TOHONOR_REC_INFO
    where INTERNAL_KEY = #{internalKey}
    <if test="tranDate != null">
      AND TRAN_DATE = #{tranDate}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    update RB_DC_TOHONOR_REC_INFO
    <set>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="stageCode != null">
        STAGE_CODE = #{stageCode},
      </if>
      <if test="acctName != null">
        ACCT_NAME = #{acctName},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="acctOpenDate != null">
        ACCT_OPEN_DATE = #{acctOpenDate},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="priAmt != null">
        PRI_AMT = #{priAmt},
      </if>
      <if test="yearRate != null">
        YEAR_RATE = #{yearRate},
      </if>
      <if test="intAmt != null">
        INT_AMT = #{intAmt},
      </if>
      <if test="tohonorResult != null">
        TOHONOR_RESULT = #{tohonorResult},
      </if>
      <if test="failueReason != null">
        FAILUE_REASON = #{failueReason},
      </if>
      <if test="priintBaseAcctNo != null">
        PRIINT_BASE_ACCT_NO = #{priintBaseAcctNo},
      </if>
      <if test="priintAcctSeqNo != null">
        PRIINT_ACCT_SEQ_NO = #{priintAcctSeqNo},
      </if>
      <if test="priintProdType != null">
        PRIINT_PROD_TYPE = #{priintProdType},
      </if>
      <if test="batchOnline != null">
        BATCH_ONLINE = #{batchOnline},
      </if>
      <if test="dealType != null">
        DEAL_TYPE = #{dealType},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="priintAcctName != null">
        PRIINT_ACCT_NAME = #{priintAcctName},
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS = #{stageProdClass}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND TRAN_DATE = #{tranDate}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    insert into RB_DC_TOHONOR_REC_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="acctName != null">
        ACCT_NAME,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="acctOpenDate != null">
        ACCT_OPEN_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="priAmt != null">
        PRI_AMT,
      </if>
      <if test="yearRate != null">
        YEAR_RATE,
      </if>
      <if test="intAmt != null">
        INT_AMT,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tohonorResult != null">
        TOHONOR_RESULT,
      </if>
      <if test="failureReason != null">
        FAILURE_REASON,
      </if>
      <if test="priintBaseAcctNo != null">
        PRIINT_BASE_ACCT_NO,
      </if>
      <if test="priintAcctSeqNo != null">
        PRIINT_ACCT_SEQ_NO,
      </if>
      <if test="priintProdType != null">
        PRIINT_PROD_TYPE,
      </if>
      <if test="batchOnline != null">
        BATCH_ONLINE,
      </if>
      <if test="dealType != null">
        DEAL_TYPE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="tohonorRecAmt != null">
        TOHONOR_REC_AMT,
      </if>
      <if test="priintAcctName != null">
        PRIINT_ACCT_NAME,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="acctName != null">
        #{acctName},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="acctOpenDate != null">
        #{acctOpenDate},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="priAmt != null">
        #{priAmt},
      </if>
      <if test="yearRate != null">
        #{yearRate},
      </if>
      <if test="intAmt != null">
        #{intAmt},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tohonorResult != null">
        #{tohonorResult},
      </if>
      <if test="failReason != null">
        #{failReason},
      </if>
      <if test="priintInternalKey != null">
        #{priintInternalKey},
      </if>
      <if test="priintBaseAcctNo != null">
        #{priintBaseAcctNo},
      </if>
      <if test="priintAcctSeqNo != null">
        #{priintAcctSeqNo},
      </if>
      <if test="priintProdType != null">
        #{priintProdType},
      </if>
      <if test="priintCcy != null">
        #{priintCcy},
      </if>
      <if test="batchOnline != null">
        #{batchOnline},
      </if>
      <if test="dealType != null">
        #{dealType},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="tohonorRecAmt != null">
        #{tohonorRecAmt},
      </if>
      <if test="priintAcctName != null">
        #{priintAcctName},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
  <select id="selectTohonorInfoByStageCode" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    select *
    from RB_DC_TOHONOR_REC_INFO
    where STAGE_CODE = #{stageCode}
    <if test="tohonorResult != null">
      AND TOHONOR_RESULT = #{tohonorResult}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    select *
    from RB_DC_TOHONOR_REC_INFO
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getByInternalKeyAndDealType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    select *
    from RB_DC_TOHONOR_REC_INFO
    where INTERNAL_KEY = #{internalKey}
    <if test="dealType != null">
      AND DEAL_TYPE = #{dealType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectDcTohonorRecInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    select *
    from RB_DC_TOHONOR_REC_INFO
    where 1=1
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="stageProdClass != null and stageProdClass != ''">
      AND STAGE_PROD_CLASS = #{stageProdClass}
    </if>
    <if test="tohonorResult != null and tohonorResult != ''">
      AND TOHONOR_RESULT = #{tohonorResult}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="baseAcctNo != null and baseAcctNo != ''">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="acctSeqNo != null and acctSeqNo != ''">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="ccy != null and ccy != ''">
      AND CCY = #{ccy}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
  </select>
  <update id="updateByPrimaryKeyAndDeal" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    update RB_DC_TOHONOR_REC_INFO
    <set>
      <if test="tohonorResult != null">
        TOHONOR_RESULT = #{tohonorResult},
      </if>
      <if test="failureReason != null">
        FAILURE_REASON = #{failureReason}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <if test="dealType != null">
      AND DEAL_TYPE = #{dealType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="selectTohonorInfodf" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcTohonorRecInfo">
    select
    <include refid="Base_Column"/>
    from RB_DC_TOHONOR_REC_INFO
    where PROD_TYPE = #{prodType}
    <if test="tranBranch != null and tranBranch !=''">
      AND TRAN_BRANCH =  #{tranBranch}
    </if>
    AND  #{startDate} <![CDATA[ <= ]]> TRAN_DATE
    AND  #{endDate} <![CDATA[ >= ]]> TRAN_DATE
    <if test="stageCode != null and stageCode !=''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="baseAcctNo != null and baseAcctNo !=''">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="tohonorResult != null and tohonorResult !=''">
      AND TOHONOR_RESULT = #{tohonorResult}
    </if>
    AND STAGE_PROD_CLASS ='ST' AND DEAL_TYPE = 'A' ORDER BY ACCT_OPEN_DATE
  </select>
  <select id="getSettleCount" parameterType="java.util.Map" resultType="int">
    SELECT count(1)
    from RB_DC_TOHONOR_REC_INFO
    where 1=1
    <if test="tranDate != null">
      AND TRAN_DATE = #{tranDate,jdbcType=DATE}
    </if>
    <if test="tohonorResult != null and tohonorResult != ''">
      AND TOHONOR_RESULT = #{tohonorResult}
    </if>
    <if test="dealType != null and dealType != ''">
      AND DEAL_TYPE = #{dealType}
    </if>
    <if test="batchOnline != null and batchOnline != ''">
      AND BATCH_ONLINE = #{batchOnline}
    </if>
  </select>
</mapper>
