<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranType">

  <select id="getExchangeTranType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">
    select <include refid="Base_Column"/>
    from RB_EXCHANGE_TRAN_TYPE
    where 1=1
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getExchangeTranTypeByExType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepWtdTranType">
    select <include refid="Base_Column"/>
    from RB_EXCHANGE_TRAN_TYPE
    where EX_TYPE = #{exType}
    and OP_TYPE =  #{opType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  
</mapper>
