<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">

	<select id="getMaxSeqNo" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT max(ACCT_SEQ_NO+0) MAX_SEQ_NO
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE BASE_ACCT_NO = #{baseAcctNo}
		  AND PROD_TYPE = #{prodType}
		  AND CLIENT_NO = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>


	<select id="getRegisterForSeqNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE BASE_ACCT_NO = #{baseAcctNo}
		AND PROD_TYPE = #{prodType}
		AND CLIENT_NO = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getFinRegisterByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE INTERNAL_KEY = #{internalKey}
		AND CLIENT_NO = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="selectListByParentInternalKeyAndAgreementId"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_FINANCIAL_ACCT_REGISTER
		<where>
			<trim suffixOverrides="AND">
				<if test="parentInternalKey != null ">
					PARENT_INTERNAL_KEY = #{parentInternalKey}  AND
				</if>
				<if test="agreementId != null and agreementId != ''">
					AGREEMENT_ID = #{agreementId}  AND
				</if>
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="acctStatus != null and acctStatus != '' ">
					ACCT_STATUS = #{acctStatus}   AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
			ORDER BY ACCT_SEQ_NO+0 ASC
		</where>
	</select>

	<select id="selectListByParentInternalKeyAndAgreementIdFiFo"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_FINANCIAL_ACCT_REGISTER
		<where>
			<trim suffixOverrides="AND">
				<if test="parentInternalKey != null ">
					PARENT_INTERNAL_KEY = #{parentInternalKey}  AND
				</if>
				<if test="agreementId != null and agreementId != ''">
					AGREEMENT_ID = #{agreementId}  AND
				</if>
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="acctStatus != null and acctStatus != '' ">
					ACCT_STATUS = #{acctStatus}   AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
			ORDER BY ACCT_SEQ_NO+0 DESC
		</where>
	</select>

	<select id="getFinRegisterByParentKeyAndSeqNo"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_FINANCIAL_ACCT_REGISTER
		<where>
			<trim suffixOverrides="AND">
				<if test="parentInternalKey != null ">
					PARENT_INTERNAL_KEY = #{parentInternalKey}  AND
				</if>
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="seqNo != null and seqNo != '' ">
					ACCT_SEQ_NO = #{seqNo}   AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="getFinRegisterByParentKeyAndStatus"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_FINANCIAL_ACCT_REGISTER
		<where>
			<trim suffixOverrides="AND">
				<if test="parentInternalKey != null ">
					PARENT_INTERNAL_KEY = #{parentInternalKey}  AND
				</if>
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="acctStatus != null and acctStatus != '' ">
					ACCT_STATUS = #{acctStatus}   AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
			ORDER BY ACCT_SEQ_NO+0 ASC
		</where>
	</select>

	<select id="getFinRegisterByAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE AGREEMENT_ID = #{agreementId}
		AND CLIENT_NO = #{clientNo}
		<if test="acctStatus != null and acctStatus != '' ">
			AND ACCT_STATUS = #{acctStatus}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY ACCT_SEQ_NO
	</select>

	<select id="getFinRegByIdAndDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE AGREEMENT_ID = #{agreementId}
		AND CLIENT_NO = #{clientNo}
		<if test="acctStatus != null and acctStatus != '' ">
			AND ACCT_STATUS = #{acctStatus}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND ACCT_OPEN_DATE between #{startDate} AND #{endDate}
		ORDER BY ACCT_SEQ_NO+0 ASC
	</select>

	<select id="getCountTotalAmountByParentInternalKeyAndAgreementId"  parameterType="java.util.Map" resultType="java.math.BigDecimal">
		SELECT
		sum(TOTAL_AMOUNT)
		FROM
		RB_FINANCIAL_ACCT_REGISTER
		<where>
			<trim suffixOverrides="AND">
				<if test="parentInternalKey != null ">
					PARENT_INTERNAL_KEY = #{parentInternalKey}  AND
				</if>
				<if test="agreementId != null and agreementId != ''">
					AGREEMENT_ID = #{agreementId}  AND
				</if>
				<if test="acctStatus != null and acctStatus != '' ">
					ACCT_STATUS = #{acctStatus}   AND
				</if>
				<if test="clientNo != null and clientNo != '' ">
					CLIENT_NO = #{clientNo}   AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="getFinRegByAcctAndDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE BASE_ACCT_NO = #{baseAcctNo}
		<if test="parentInternalKey != null ">
			AND PARENT_INTERNAL_KEY = #{parentInternalKey}
		</if>
		<if test="acctStatus != null and acctStatus != '' ">
			AND ACCT_STATUS = #{acctStatus}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND ACCT_OPEN_DATE between #{startDate} AND #{endDate}
	</select>

	<update id="updatePrevFinBalForEod" parameterType="java.util.Map">

		update RB_FINANCIAL_ACCT_REGISTER set total_amount_prev = total_amount, LAST_BAL_UPD_DATE = #{runDate}
            where <![CDATA[
                  LAST_BAL_UPD_DATE <> #{runDate} AND internal_key BETWEEN #{startKey} and #{endKey}
				]]>
<!-- 多法人改造 by fudd -->
				<if test="company != null and company != '' ">
			  	AND COMPANY = #{company}
				</if>

    </update>

	<select id="getFinRegByAcctAndDateType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE BASE_ACCT_NO = #{baseAcctNo}
		<if test="parentInternalKey != null ">
			AND PARENT_INTERNAL_KEY = #{parentInternalKey}
		</if>
		<if test="acctStatus != null and acctStatus != '' ">
			AND ACCT_STATUS = #{acctStatus}
		</if>
		<if test="intType != null and  intType != '' ">
			AND INT_TYPE = #{intType}
		</if>
		<if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND TRAN_DATE between #{startDate} AND #{endDate}
	</select>

	<select id="getTotalIntByParentInternalKeyAndAgreementId"  parameterType="java.util.Map" resultType="java.math.BigDecimal">
		SELECT
		sum(INT_ACCRUED)
		FROM
		RB_FINANCIAL_ACCT_REGISTER
		<where>
			<trim suffixOverrides="AND">
				<if test="parentInternalKey != null ">
					PARENT_INTERNAL_KEY = #{parentInternalKey}  AND
				</if>
				<if test="agreementId != null and agreementId != ''">
					AGREEMENT_ID = #{agreementId}  AND
				</if>
				<if test="acctStatus != null and acctStatus != '' ">
					ACCT_STATUS = #{acctStatus}   AND
				</if>
				<if test="clientNo != null and clientNo != '' ">
					CLIENT_NO = #{clientNo}   AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="selectByCheck" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		where (acct_status != 'C' OR (acct_status = 'C' AND ACCT_CLOSE_DATE = #{runDate}))
		and TRAN_BRANCH = #{branch}
		and ACCT_CCY = #{ccy}
		and PROD_TYPE = #{prodType}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getFinRegisterByInternalKeyForLock" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_ACCT_REGISTER
		WHERE INTERNAL_KEY = #{internalKey} AND CLIENT_NO = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		 FOR UPDATE
	</select>


	<select id="getRbNoteList"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_FINANCIAL_ACCT_REGISTER
		<where>
			<trim suffixOverrides="AND">
				<if test="agreementId != null and agreementId != ''">
					AGREEMENT_ID = #{agreementId}  AND
				</if>
				<if test="acctStatus != null and acctStatus != '' ">
					ACCT_STATUS = #{acctStatus}   AND
				</if>
				<if test="clientNo != null and clientNo != '' ">
					CLIENT_NO = #{clientNo}   AND
				</if>
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>
</mapper>
