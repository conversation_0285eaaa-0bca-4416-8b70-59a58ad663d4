<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
    <!-- Created by admin on 2017/07/24 11:19:33. -->
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
        insert into RB_GL_WRITE_OFF_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="hangSeqNo != null">
                HANG_SEQ_NO,
            </if>
            <if test="writeOffSeqNo != null">
                WRITE_OFF_SEQ_NO,
            </if>
            <if test="writeOffStatus != null">
                WRITE_OFF_STATUS,
            </if>
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="writeOffAmt != null">
                WRITE_OFF_AMT,
            </if>
            <if test="hangBal != null">
                HANG_BAL,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="othBaseAcctNo != null">
                OTH_BASE_ACCT_NO,
            </if>
            <if test="othAcctName != null">
                OTH_ACCT_NAME,
            </if>
            <if test="othBranch != null">
                OTH_BRANCH,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="authUserId != null">
                AUTH_USER_ID,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="hangSeqNo != null">
                #{hangSeqNo},
            </if>
            <if test="writeOffSeqNo != null">
                #{writeOffSeqNo},
            </if>
            <if test="writeOffStatus != null">
                #{writeOffStatus},
            </if>
            <if test="tranType != null">
                #{tranType},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="writeOffAmt != null">
                #{writeOffAmt},
            </if>
            <if test="hangBal != null">
                #{hangBal},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="othBaseAcctNo != null">
                #{othBaseAcctNo},
            </if>
            <if test="othAcctName != null">
                #{othAcctName},
            </if>
            <if test="othBranch != null">
                #{othBranch},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="authUserId != null">
                #{authUserId},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
    <select id="getWriteOffAccountByRefNo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
    SELECT *
    FROM RB_GL_WRITE_OFF_ACCOUNT
    WHERE reference = #{reference}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </select>

    <select id="getWriteOffList" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
        SELECT <include refid="Base_Column"/>
        FROM RB_GL_WRITE_OFF_ACCOUNT b
        WHERE 1=1
        <if test="hangSeqNo != null and hangSeqNo != '' ">
            and b.HANG_SEQ_NO =#{hangSeqNo}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo != '' ">
            and b.SUB_HANG_SEQ_NO =#{subHangSeqNo}
        </if>
        <if test="branch != null and branch != '' ">
            and b.TRAN_BRANCH =#{branch}
        </if>
        <if test="startDate != null and endDate != null ">
            and b.TRAN_DATE BETWEEN #{startDate} and #{endDate}
        </if>
        <if test="ccy != null and ccy !='' ">
            and b.CCY = #{ccy}
        </if>
        <if test="baseAcctNo != null and baseAcctNo !='' ">
            and b.BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="hangWriteOffStatus != null and hangWriteOffStatus !='' ">
            and b.WRITE_OFF_STATUS = #{hangWriteOffStatus}
        </if>
        <if test="tranAmt != null and tranAmt !='' ">
            and b.WRITE_OFF_AMT = #{tranAmt}
        </if>
        <if test="reference != null and reference != ''">
            AND b.REFERENCE = #{reference}
        </if>
        ORDER BY b.HANG_SEQ_NO, b.SUB_HANG_SEQ_NO
    </select>

    <update id="updateWriteOffAccountByRefNo" parameterType="java.util.HashMap">
        update RB_GL_WRITE_OFF_ACCOUNT
        <set>
            <if test="status != null">
                ACCT_STATUS = #{status},
            </if>
        </set>

    </update>

    <update id="updateWriteOffAccount" parameterType="java.util.HashMap">
        update RB_GL_WRITE_OFF_ACCOUNT
        <set>
            <if test="writeOffStatus != null">
                WRITE_OFF_STATUS = #{writeOffStatus},
            </if>
            TRAN_TIMESTAMP = #{tranTimestamp}
        </set>
        where HANG_SEQ_NO = #{hangSeqNo}
        AND SUB_HANG_SEQ_NO = #{subHangSeqNo}
        AND WRITE_OFF_SEQ_NO = #{writeOffSeqNo}
        AND CLIENT_NO =#{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="getMaxWriteOffAccount" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
        SELECT *
        FROM RB_GL_WRITE_OFF_ACCOUNT
        WHERE hang_seq_no = #{hangSeqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="writeOffStatus != null and writeOffStatus.length() > 0">
            AND WRITE_OFF_STATUS = #{writeOffStatus}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by hang_seq_no desc ,  WRITE_OFF_SEQ_NO desc
    </select>
    <select id="getRbGlWriteOffAccountCount" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT *
        FROM RB_GL_WRITE_OFF_ACCOUNT b
        WHERE 1=1
        <if test="hangSeqNo != null and hangSeqNo.length() > 0 ">
            and b.WRITE_OFF_SEQ_NO =#{hangSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo.length() > 0 ">
            and b.SUB_HANG_SEQ_NO =#{subHangSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="branch != null and branch.length() > 0">
            and b.TRAN_BRANCH =#{branch,jdbcType=VARCHAR}
        </if>
        <if test="startDate != null and endDate.length() > 0 ">
            and b.TRAN_DATE BETWEEN #{startDate} and #{endDate}
        </if>
        <if test="ccy != null and ccy.length() > 0 ">
            and b.CCY = #{ccy,jdbcType=VARCHAR}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0 ">
            and b.BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="hangWriteOffStatus != null and hangWriteOffStatus.length() > 0 ">
            and b.WRITE_OFF_STATUS = #{hangWriteOffStatus,jdbcType=VARCHAR}
        </if>
        <if test="tranAmt != null and tranAmt !='' ">
            and b.WRITE_OFF_AMT = #{tranAmt}
        </if>
    </select>
    <update id="updateWriteOffAccountOthByRefNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
        update RB_GL_WRITE_OFF_ACCOUNT
        <set>
            <if test="othBaseAcctNo != null">
                OTH_BASE_ACCT_NO = #{othBaseAcctNo},
            </if>
            <if test="othAcctName != null">
                OTH_ACCT_NAME  = #{othAcctName},
            </if>
            <if test="othBranch != null">
                OTH_BRANCH = #{othBranch},
            </if>
        </set>
        where reference = #{reference}
          and Client_NO =  #{clientNo}
            <if test="hangSeqNo != null and hangSeqNo.length() > 0">
                and WRITE_OFF_SEQ_NO =#{hangSeqNo}
            </if>
            <if test="subHangSeqNo != null and subHangSeqNo.length() > 0 ">
                and SUB_HANG_SEQ_NO =#{subHangSeqNo}
            </if>
    </update>
    <select id="getWriteOffAccountByInfo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
        SELECT *
        FROM RB_GL_WRITE_OFF_ACCOUNT
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null  and hangSeqNo != '' ">
            AND HANG_SEQ_NO = #{hangSeqNo}
        </if>
        <if test="writeOffAmt != null  and writeOffAmt != '' ">
            AND WRITE_OFF_AMT = #{writeOffAmt}
        </if>
        <if test="startDate != null   ">
            AND TRAN_DATE <![CDATA[>=]]> #{startDate}
        </if>
        <if test="endDate != null   ">
            AND TRAN_DATE <![CDATA[<=]]> #{endDate}
        </if>
        ORDER BY HANG_SEQ_NO, SUB_HANG_SEQ_NO
    </select>
    <select id="queryRbGlWriteOffAccountRev" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlWriteOffAccount">
        SELECT *
        FROM RB_GL_WRITE_OFF_ACCOUNT
        WHERE hang_seq_no = #{hangSeqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test='isReversal != null and isReversal == "Y"'>
            AND WRITE_OFF_STATUS = 'R'
        </if>
        <if test='isReversal != null and isReversal == "N"'>
            AND WRITE_OFF_STATUS != 'R'
        </if>
    </select>

    <update id="updateWriteOffByReference" parameterType="java.util.HashMap">
        update RB_GL_WRITE_OFF_ACCOUNT
        <set>
            <if test="othBranch != null and  othBranch != '' ">
                OTH_BRANCH = #{othBranch},
            </if>
            <if test="narrative != null and  narrative != '' ">
                NARRATIVE = #{narrative},
            </if>
            <if test="othAcctName != null and  othAcctName != '' ">
                OTH_ACCT_NAME = #{othAcctName},
            </if>
            <if test="othBaseAcctNo != null and  othBaseAcctNo != '' ">
                OTH_BASE_ACCT_NO = #{othBaseAcctNo},
            </if>
        </set>
        where REFERENCE =#{reference}
        AND CLIENT_NO =#{clientNo}
    </update>
</mapper>
