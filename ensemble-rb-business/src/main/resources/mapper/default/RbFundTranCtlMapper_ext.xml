<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFundTranCtl">
  <!-- Created by admin on 2018/10/16 14:52:34. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFundTranCtl"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFundTranCtl">
    select *
    from RB_FUND_TRAN_CTL
    where ACCT_CLASS = #{acctClass}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFundTranCtl">
    delete from RB_FUND_TRAN_CTL
    where ACCT_CLASS = #{acctClass}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFundTranCtl">
    update RB_FUND_TRAN_CTL
    <set>
      <if test="ctlParameter != null">
        CTL_PARAMETER = #{ctlParameter},
      </if>
      <if test="ctlType != null">
        CTL_TYPE = #{ctlType},
      </if>
      <if test="ctlAttr != null">
        CTL_ATTR = #{ctlAttr},
      </if>
      <if test="ctlDesc != null">
        CTL_DESC = #{ctlDesc},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where ACCT_CLASS = #{acctClass}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFundTranCtl">
    insert into RB_FUND_TRAN_CTL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="acctClass != null">
        ACCT_CLASS,
      </if>
      <if test="ctlParameter != null">
        CTL_PARAMETER,
      </if>
      <if test="ctlType != null">
        CTL_TYPE,
      </if>
      <if test="ctlAttr != null">
        CTL_ATTR,
      </if>
      <if test="ctlDesc != null">
        CTL_DESC,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="acctClass != null">
        #{acctClass},
      </if>
      <if test="ctlParameter != null">
        #{ctlParameter},
      </if>
      <if test="ctlType != null">
        #{ctlType},
      </if>
      <if test="ctlAttr != null">
        #{ctlAttr},
      </if>
      <if test="ctlDesc != null">
        #{ctlDesc},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
</mapper>
