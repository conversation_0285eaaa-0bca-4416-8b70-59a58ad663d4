<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnLost">
  <!-- Created by admin on 2017/10/16 13:24:06. -->


  <select id="getMbPnLostBylossNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnLost" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnLost">
    select *
    from RB_PN_LOST
    where LOSS_NO = #{lossNo}
    <if test="validFlag != null and validFlag !=''">
      AND   TRAN_VALID_FLAG = #{validFlag}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbPnLostByBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnLost" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnLost">
    select *
    from RB_PN_LOST
    where BILL_NO = #{billNo}
    <if test="validFlag != null and validFlag !=''">
      AND   TRAN_VALID_FLAG = #{validFlag}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
