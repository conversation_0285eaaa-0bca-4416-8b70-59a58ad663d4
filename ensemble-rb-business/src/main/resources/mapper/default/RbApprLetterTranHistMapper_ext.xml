<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterTranHist">
  <!-- Created by he<PERSON><PERSON> on 2017/08/22 14:12:21. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterTranHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterTranHist">
    select *
    from RB_APPR_LETTER_TRAN_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterTranHist">
    delete from RB_APPR_LETTER_TRAN_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterTranHist">
    update RB_APPR_LETTER_TRAN_HIST
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="eventType != null">
        EVENT_TYPE = #{eventType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="crDrMaintInd != null">
        CR_DR_MAINT_IND = #{crDrMaintInd},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="branch != null">
        BRANCH = #{branch},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="terminalId != null">
        TERMINAL_ID = #{terminalId},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="tranCategory != null">
        TRAN_CATEGORY = #{tranCategory},
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO = #{bankSeqNo},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="reversalTranType != null">
        REVERSAL_TRAN_TYPE = #{reversalTranType},
      </if>
      <if test="reversalDate != null">
        REVERSAL_DATE = #{reversalDate},
      </if>
      <if test="previousBalAmt != null">
        PREVIOUS_BAL_AMT = #{previousBalAmt},
      </if>
      <if test="actualBalAmt != null">
        ACTUAL_BAL_AMT = #{actualBalAmt},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        ACCT_CCY = #{acctCcy},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="acctBranch != null">
        ACCT_BRANCH = #{acctBranch},
      </if>
      <if test="glCode != null">
        GL_CODE = #{glCode},
      </if>
      <if test="acctDesc != null">
        ACCT_DESC = #{acctDesc},
      </if>
      <if test="traceId != null">
        TRACE_ID = #{traceId},
      </if>
      <if test="narrative != null">
        NARRATIVE = #{narrative},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID = #{apprUserId},
      </if>
      <if test="tranDesc != null">
        TRAN_DESC = #{tranDesc},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="cashItem != null">
        CASH_ITEM = #{cashItem},
      </if>
      <if test="tranNote != null">
        TRAN_NOTE = #{tranNote},
      </if>
      <if test="tranStatus != null">
        TRAN_STATUS = #{tranStatus},
      </if>
      <if test="othSeqNo != null">
        OTH_SEQ_NO = #{othSeqNo},
      </if>
      <if test="othInternalKey != null">
        OTH_INTERNAL_KEY = #{othInternalKey},
      </if>
      <if test="othBranch != null">
        OTH_BRANCH = #{othBranch},
      </if>
      <if test="othBankName != null">
        OTH_BANK_NAME = #{othBankName},
      </if>
      <if test="othBankCode != null">
        OTH_BANK_CODE = #{othBankCode},
      </if>
      <if test="othBaseAcctNo != null">
        OTH_BASE_ACCT_NO = #{othBaseAcctNo},
      </if>
      <if test="othAcctSeqNo != null">
        OTH_ACCT_SEQ_NO = #{othAcctSeqNo},
      </if>
      <if test="othProdType != null">
        OTH_PROD_TYPE = #{othProdType},
      </if>
      <if test="othAcctCcy != null">
        OTH_ACCT_CCY = #{othAcctCcy},
      </if>
      <if test="othAcctDesc != null">
        OTH_ACCT_DESC = #{othAcctDesc},
      </if>
      <if test="othReference != null">
        OTH_REFERENCE = #{othReference},
      </if>
      <if test="servCharge != null">
        SERV_CHARGE = #{servCharge},
      </if>
      <if test="sortPriority != null">
        SORT_PRIORITY = #{sortPriority},
      </if>
      <if test="apprLetterNo != null">
        APPR_LETTER_NO = #{apprLetterNo},
      </if>
      <if test="apprType != null">
        APPR_TYPE = #{apprType},
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterTranHist">
    insert into RB_APPR_LETTER_TRAN_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="eventType != null">
        EVENT_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="crDrMaintInd != null">
        CR_DR_MAINT_IND,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="branch != null">
        BRANCH,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="terminalId != null">
        TERMINAL_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="tranCategory != null">
        TRAN_CATEGORY,
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="reversalTranType != null">
        REVERSAL_TRAN_TYPE,
      </if>
      <if test="reversalDate != null">
        REVERSAL_DATE,
      </if>
      <if test="previousBalAmt != null">
        PREVIOUS_BAL_AMT,
      </if>
      <if test="actualBalAmt != null">
        ACTUAL_BAL_AMT,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="acctBranch != null">
        ACCT_BRANCH,
      </if>
      <if test="glCode != null">
        GL_CODE,
      </if>
      <if test="acctDesc != null">
        ACCT_DESC,
      </if>
      <if test="traceId != null">
        TRACE_ID,
      </if>
      <if test="narrative != null">
        NARRATIVE,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID,
      </if>
      <if test="tranDesc != null">
        TRAN_DESC,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="cashItem != null">
        CASH_ITEM,
      </if>
      <if test="tranNote != null">
        TRAN_NOTE,
      </if>
      <if test="tranStatus != null">
        TRAN_STATUS,
      </if>
      <if test="othSeqNo != null">
        OTH_SEQ_NO,
      </if>
      <if test="othInternalKey != null">
        OTH_INTERNAL_KEY,
      </if>
      <if test="othBranch != null">
        OTH_BRANCH,
      </if>
      <if test="othBankName != null">
        OTH_BANK_NAME,
      </if>
      <if test="othBankCode != null">
        OTH_BANK_CODE,
      </if>
      <if test="othBaseAcctNo != null">
        OTH_BASE_ACCT_NO,
      </if>
      <if test="othAcctSeqNo != null">
        OTH_ACCT_SEQ_NO,
      </if>
      <if test="othProdType != null">
        OTH_PROD_TYPE,
      </if>
      <if test="othAcctCcy != null">
        OTH_ACCT_CCY,
      </if>
      <if test="othAcctDesc != null">
        OTH_ACCT_DESC,
      </if>
      <if test="othReference != null">
        OTH_REFERENCE,
      </if>
      <if test="servCharge != null">
        SERV_CHARGE,
      </if>
      <if test="sortPriority != null">
        SORT_PRIORITY,
      </if>
      <if test="apprLetterNo != null">
        APPR_LETTER_NO,
      </if>
      <if test="apprType != null">
        APPR_TYPE,
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null">
        #{company},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="eventType != null">
        #{eventType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="crDrMaintInd != null">
        #{crDrMaintInd},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="terminalId != null">
        #{terminalId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="tranCategory != null">
        #{tranCategory},
      </if>
      <if test="bankSeqNo != null">
        #{bankSeqNo},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="reversalTranType != null">
        #{reversalTranType},
      </if>
      <if test="reversalDate != null">
        #{reversalDate},
      </if>
      <if test="previousBalAmt != null">
        #{previousBalAmt},
      </if>
      <if test="actualBalAmt != null">
        #{actualBalAmt},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="acctBranch != null">
        #{acctBranch},
      </if>
      <if test="glCode != null">
        #{glCode},
      </if>
      <if test="acctDesc != null">
        #{acctDesc},
      </if>
      <if test="traceId != null">
        #{traceId},
      </if>
      <if test="narrative != null">
        #{narrative},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="apprUserId != null">
        #{apprUserId},
      </if>
      <if test="tranDesc != null">
        #{tranDesc},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="cashItem != null">
        #{cashItem},
      </if>
      <if test="tranNote != null">
        #{tranNote},
      </if>
      <if test="tranStatus != null">
        #{tranStatus},
      </if>
      <if test="othSeqNo != null">
        #{othSeqNo},
      </if>
      <if test="othInternalKey != null">
        #{othInternalKey},
      </if>
      <if test="othBranch != null">
        #{othBranch},
      </if>
      <if test="othBankName != null">
        #{othBankName},
      </if>
      <if test="othBankCode != null">
        #{othBankCode},
      </if>
      <if test="othBaseAcctNo != null">
        #{othBaseAcctNo},
      </if>
      <if test="othAcctSeqNo != null">
        #{othAcctSeqNo},
      </if>
      <if test="othProdType != null">
        #{othProdType},
      </if>
      <if test="othAcctCcy != null">
        #{othAcctCcy},
      </if>
      <if test="othAcctDesc != null">
        #{othAcctDesc},
      </if>
      <if test="othReference != null">
        #{othReference},
      </if>
      <if test="servCharge != null">
        #{servCharge},
      </if>
      <if test="sortPriority != null">
        #{sortPriority},
      </if>
      <if test="apprLetterNo != null">
        #{apprLetterNo},
      </if>
      <if test="apprType != null">
        #{apprType},
      </if>
    </trim>
  </insert>
</mapper>
