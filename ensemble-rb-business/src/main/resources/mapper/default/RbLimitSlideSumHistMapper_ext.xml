<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitSlideSumHist">
    <select id="selectSlideSumInfoByCreateDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitSlideSumHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitSlideSumHist">
        select
        <include refid="Base_Column"/>
        from RB_LIMIT_SLIDE_SUM_HIST
        where CLIENT_NO = #{clientNo}
        and CHECK_OBJ_VAL = #{checkObjVal}
        and LIMIT_SCENE_NO = #{limitSceneNo}
        and VALID_FLAG = #{validFlag}
        and CREATE_DATE <![CDATA[  >  ]]> #{createDate}
        <!--多法人改造 by luocwa  -->
        <if test="company != null and company.length() > 0">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="updateRbLimitSlideSumHistByParam" parameterType="java.util.Map">
        UPDATE RB_LIMIT_SLIDE_SUM_HIST
        <set>
            <if test="newLimitSceneNo != null and newLimitSceneNo != '' ">
                LIMIT_SCENE_NO = #{newLimitSceneNo},
            </if>
            <if test="newCheckObjVal != null and newCheckObjVal != '' ">
                CHECK_OBJ_VAL = #{newCheckObjVal},
            </if>
            <if test="newReference != null and newReference != '' ">
                REFERENCE = #{newReference}
            </if>
        </set>
        <where>
            LIMIT_SCENE_NO = #{limitSceneNo}
            AND CLIENT_NO = #{clientNo}
            AND CHECK_OBJ_VAL = #{checkObjVal}
        </where>
    </select>
</mapper>