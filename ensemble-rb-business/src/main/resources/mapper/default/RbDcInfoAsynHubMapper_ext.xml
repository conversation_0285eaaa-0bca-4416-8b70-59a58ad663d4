<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcInfoAsynHub">

    <select id="selectByCondition" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcInfoAsynHub" useCache="false" flushCache="true">
        SELECT
        SEQ_NO,
        TRAN_TYPE,
        CLIENT_NO,
        BASE_ACCT_NO,
        REFERENCE,
        TRAN_DATE,
        TRAN_TIMESTAMP,
        DATA_FLAG,
        STATUS,
        ASYN_DATE,
        FAILURE_TIMES,
        JSON_DATA,
        RET_MSG
        FROM
        RB_DC_INFO_ASYN_HUB
        <where>
            <trim prefixOverrides = "OR">
            <if test="oneStatus != null and oneStatus != ''">
               OR  STATUS = #{oneStatus}
            </if>
            <if test="twoStatus != null and twoStatus != ''">
                OR   STATUS = #{twoStatus}
            </if>
            </trim>
        </where>
    </select>

</mapper>