<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping">
  <!-- Created by admin on 2017/03/28 11:09:52. -->
  <sql id="Base_Where_Ext">
    <where>
      <if test="channelTypeRule != null and  channelTypeRule != '' ">
        AND CHANNEL_TYPE_RULE = (case CHANNEL_TYPE_RULE when 'ALL' then CHANNEL_TYPE_RULE else #{channelTypeRule,jdbcType=VARCHAR} end)
      </if>
      <if test="channel != null and  channel != '' ">
        AND CHANNEL = #{channel,jdbcType=VARCHAR}
      </if>
      <if test="interTranType != null and  interTranType != '' ">
        AND INTER_TRAN_TYPE = #{interTranType,jdbcType=VARCHAR}
      </if>
      <if test="tranType != null and  tranType != '' ">
        AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
      </if>
      <if test="acctCcy != null and  acctCcy != '' ">
        AND ACCT_CCY = #{acctCcy,jdbcType=VARCHAR}
      </if>
      <if test="acctBranch != null and  acctBranch != '' ">
        AND (ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR} OR ACCT_BRANCH = 'ALL')
      </if>
      <if test="othAcctNo != null and  othAcctNo != '' ">
        AND OTH_ACCT_NO = #{othAcctNo,jdbcType=VARCHAR}
      </if>
      <if test="libraOpTime != null ">
        AND LIBRA_OP_TIME = #{libraOpTime,jdbcType=BIGINT}
      </if>
      <if test="tranTimestamp != null and  tranTimestamp != '' ">
        AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
      </if>
      <if test="company != null and  company != '' ">
        AND COMPANY = #{company,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping">
    select *
    from RB_INTERNAL_ACCT_MAPPING
    where CHANNEL = #{channel}
      <if test="tranType != null">
        AND TRAN_TYPE = #{tranType}
      </if>
      <if test="acctBranch != null">
        AND ( ACCT_BRANCH = #{acctBranch} OR ACCT_BRANCH = 'ALL')
      </if>
      <if test="acctCcy != null">
        AND ACCT_CCY = #{acctCcy}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping">
    delete from RB_INTERNAL_ACCT_MAPPING
    where CHANNEL = #{channel}
      <if test="tranType != null">
        AND TRAN_TYPE = #{tranType}
      </if>
      <if test="acctBranch != null">
        AND ACCT_BRANCH = #{acctBranch}
      </if>
      <if test="acctCcy != null">
        AND ACCT_CCY = #{acctCcy}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping">
    update RB_INTERNAL_ACCT_MAPPING
    <set>
      <if test="interTranType != null">
        INTER_TRAN_TYPE = #{interTranType},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo}
      </if>
    </set>
    where CHANNEL = #{channel}
        AND TRAN_TYPE = #{tranType}
        AND ( ACCT_BRANCH = #{acctBranch} OR ACCT_BRANCH = 'ALL')
        AND ACCT_CCY = #{acctCcy}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping">
    insert into RB_INTERNAL_ACCT_MAPPING
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channel != null">
        CHANNEL,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="acctBranch != null">
        ACCT_BRANCH,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="interTranType != null">
        INTER_TRAN_TYPE,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channel != null">
        #{channel},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="acctBranch != null">
        #{acctBranch},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="interTranType != null">
        #{interTranType},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="selectOneExt" resultMap="Base_Result_Map">
    SELECT
    <include refid="Base_Column"/>
    FROM
    <include refid="Table_Name"/>
    <include refid="Base_Where_Ext"/>
  </select>
  <select id="getInnerAcctMappingFlag" parameterType="java.util.Map" resultType="java.lang.Integer">
    select count(*) from rb_internal_acct_mapping where base_acct_no = #{baseAcctNo}
  </select>

  <select id="getInnerAcctMappingListBefore" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping">
    SELECT a.base_acct_no
    FROM RB_INTERNAL_ACCT_MAPPING a
    where a.base_acct_no in
    <foreach collection="baseAcctNos" index="" item="baseAcctNo" open="(" separator="," close=")">
      #{baseAcctNo}
    </foreach>
  </select>

<!--  <select id="getInnerAcctMappingList" parameterType="java.util.Map" resultType="java.util.Map">-->
<!--    SELECT a.base_acct_no KEY,count(1) VALUE-->
<!--    FROM RB_INTERNAL_ACCT_MAPPING a-->
<!--    where a.base_acct_no in-->
<!--    <foreach collection="baseAcctNos" index="" item="baseAcctNo" open="(" separator="," close=")">-->
<!--      #{baseAcctNo}-->
<!--    </foreach>-->
<!--    group by a.base_acct_no-->
<!--  </select>-->
  <select id="getMappingByChannalAndCompany" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping">
    SELECT <include refid="Base_Column"/>
    from RB_INTERNAL_ACCT_MAPPING
    where ACCT_BRANCH = #{acctBranch}
    <if test="channelTypeRule != null">
      AND CHANNEL_TYPE_RULE = #{channelTypeRule}
    </if>
    <if test="tranType != null">
      AND TRAN_TYPE = #{tranType}
    </if>
    <if test="acctCcy != null">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="companyList != null">
      AND COMPANY in
      <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>
</mapper>
