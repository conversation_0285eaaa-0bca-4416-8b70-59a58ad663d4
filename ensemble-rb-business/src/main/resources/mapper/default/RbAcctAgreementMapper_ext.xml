<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAgreement">

  <select id="selectByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAgreement" parameterType="java.util.List" >

    select <include refid="Base_Column"/>
    from RB_ACCT_AGREEMENT
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
