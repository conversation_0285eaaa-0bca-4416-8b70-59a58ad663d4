<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementJdl">

	<sql id="Base_Column_Join">
		A.ACCT_CCY as ACCT_CCY,
		A.TERM as TERM,
		A.BASIS_RATE as BASIS_RATE,
		A.QUARTER_INT_FLAG as QUARTER_INT_FLAG,
		A.ACCT_TYPE as ACCT_TYPE,
		A.TERM_TYPE as TERM_TYPE,
		A.USER_ID as USER_ID,
		A.CANCEL_DATE as CANCEL_DATE,
		A.CANCEL_USER_ID as CANCEL_USER_ID,
		A.SIGN_PROD_TYPE as SIGN_PROD_TYPE,
		A.AGREEMENT_ID as AGREEMENT_ID,
		A.INTERNAL_KEY as INTERNAL_KEY,
		A.AGREE_INT_RATE as AGREE_INT_RATE,
		A.CLIENT_NO as CLIENT_NO,
		A.START_DATE as START_DATE,
		A.AGREEMENT_STATUS as AGREEMENT_STATUS,
		A.AGREE_INT as AGREE_INT,
		A.TRAN_DATE as TRAN_DATE,
		A.PROD_TYPE as PROD_TYPE,
		A.START_DATE_AGG as START_DATE_AGG,
		A.END_DATE_INT as END_DATE_INT,
		A.BASE_ACCT_NO as BASE_ACCT_NO,
		A.END_DATE as END_DATE,
		A.CUR_AVG as CUR_AVG,
		A.AGREE_AVG as AGREE_AVG,
		A.END_DATE_AGG as END_DATE_AGG,
		A.START_DATE_INT as START_DATE_INT,
		A.DIFF_INT as DIFF_INT,
		A.ACCT_SEQ_NO as ACCT_SEQ_NO
<!--&#45;&#45; 		B.ACCT_NAME as ACCT_NAME,-->
<!--&#45;&#45; 		B.ACCT_BRANCH as BRANCH-->
	</sql>

	<select id="getAgreements" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementJdl">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_JDL a
		<where>
<!-- 多法人改造 by LIYUANV -->
			<if test="company != null and company != '' ">
				AND a.COMPANY = #{company}
			</if>
			<if test="baseAcctNo != null and baseAcctNo != ''">
				AND BASE_ACCT_NO = #{baseAcctNo}
			</if>
			<if test="internalKey != null and internalKey != ''">
				AND INTERNAL_KEY = #{internalKey}
			</if>
			<if test="ccy != null and ccy != ''">
				AND ACCT_CCY = #{ccy}
			</if>
			<if test="clientNo != null and clientNo != ''">
				AND CLIENT_NO = #{clientNo}
			</if>
			<if test="acctType != null and acctType != ''">
				AND ACCT_TYPE = #{acctType}
			</if>
			<if test="status != null and status != ''">
				AND AGREEMENT_STATUS = #{status}
			</if>
			<if test="agreementId != null and agreementId != ''">
				AND AGREEMENT_ID = #{agreementId}
			</if>
		</where>
		order by BASE_ACCT_NO, START_DATE
	</select>


	<select id="getAgreementsByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementJdl">
		select <include refid="Base_Column_Join"/>
		from RB_AGREEMENT_JDL A
		LEFT JOIN RB_ACCT B ON A.INTERNAL_KEY = B.INTERNAL_KEY
		where A.CLIENT_NO = B.CLIENT_NO
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND A.COMPANY = #{company}
		</if>
		<if test="branch != null and branch != ''">
			AND B.ACCT_BRANCH= #{branch}
		</if>
		<if test="internalKey != null and internalKey != ''">
			AND A.INTERNAL_KEY = #{internalKey}
		</if>
		<if test="baseAcctNo != null and baseAcctNo != ''">
			AND A.BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="ccy != null and ccy != ''">
			AND A.ACCT_CCY = #{ccy}
		</if>
		<if test="clientNo != null and clientNo != ''">
			AND A.CLIENT_NO = #{clientNo}
		</if>
		<if test="acctType != null and acctType != ''">
			AND A.ACCT_TYPE = #{acctType}
		</if>
		<if test="status != null and status != ''">
			AND A.AGREEMENT_STATUS = #{status}
		</if>
		<if test="agreementId != null and agreementId != ''">
			AND A.AGREEMENT_ID = #{agreementId}
		</if>
		order by A.BASE_ACCT_NO, A.START_DATE
	</select>


	<select id="getActiveAgreementsByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementJdl">
		select <include refid="Base_Column_Join"/>
		from RB_AGREEMENT_JDL A
		LEFT JOIN RB_ACCT B ON A.INTERNAL_KEY = B.INTERNAL_KEY
		where A.CLIENT_NO = B.CLIENT_NO
		AND A.AGREEMENT_STATUS != 'E'
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND A.COMPANY = #{company}
		</if>
		<if test="branch != null and branch != ''">
			AND B.BRANCH= #{branch}
		</if>
		<if test="internalKey != null and internalKey != ''">
			AND A.INTERNAL_KEY = #{internalKey}
		</if>
		<if test="baseAcctNo != null and baseAcctNo != ''">
			AND A.BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="ccy != null and ccy != ''">
			AND A.ACCT_CCY = #{ccy}
		</if>
		<if test="clientNo != null and clientNo != ''">
			AND A.CLIENT_NO = #{clientNo}
		</if>
		<if test="acctType != null and acctType != ''">
			AND A.ACCT_TYPE = #{acctType}
		</if>
		<if test="agreementId != null and agreementId != ''">
			AND A.AGREEMENT_ID = #{agreementId}
		</if>
		order by A.BASE_ACCT_NO, A.START_DATE
	</select>
</mapper>
