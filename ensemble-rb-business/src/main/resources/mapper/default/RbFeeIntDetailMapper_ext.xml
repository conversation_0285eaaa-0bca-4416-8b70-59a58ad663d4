<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeIntDetail">

  <select id="selectListByParam" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeIntDetail">
    select <include refid="Base_Column"/>
    from RB_FEE_INT_DETAIL
    where STATUS != 'D'
      <if test="feeIntNo != null and feeIntNo.length() > 0">
        AND FEE_INT_NO = #{feeIntNo}
      </if>
      <if test="tranBranch != null and tranBranch.length() > 0">
        AND TRAN_BRANCH = #{tranBranch}
      </if>
      <if test="userId != null and userId.length() > 0">
        AND USER_ID = #{userId}
      </if>
      <if test="startDate != null and endDate != null">
        AND TRAN_DATE BETWEEN #{startDate} and #{endDate}
      </if>
    order by fee_Int_No  desc ,tran_date desc
  </select>
</mapper>
