<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList">


	<delete id="deleteByDataTypeAndValue" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList">
		delete from RC_ALL_LIST
		where DATA_VALUE = #{dataValue}
		<if test="dataType != null">
			AND DATA_TYPE = #{dataType}
		</if>
		<if test="listType != null">
			AND LIST_TYPE = #{listType}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</delete>

	<delete id="deleteByData" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList">
		delete from RC_ALL_LIST
		where DATA_VALUE = #{dataValue}
		<if test="dataType != null">
			AND DATA_TYPE = #{dataType}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</delete>


	<update id="updateByTypeAndValue" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList">
		UPDATE RC_ALL_LIST
		<set>
			<if test="maturityDate != null ">
				MATURITY_DATE = #{maturityDate},
			</if>
			<if test="narrative != null and narrative != '' ">
				NARRATIVE = #{narrative},
			</if>
			<if test="clientName != null and clientName != '' ">
				CLIENT_NAME = #{clientName},
			</if>
			<if test="remark1 != null and remark1 != '' ">
				REMARK1 = #{remark1},
			</if>
			<if test="effectDate != null ">
				EFFECT_DATE = #{effectDate},
			</if>
			<if test="documentType != null and documentType != '' ">
				DOCUMENT_TYPE = #{documentType},
			</if>
			<if test="tranBranch != null and tranBranch != '' ">
				TRAN_BRANCH = #{tranBranch},
			</if>
			<if test="company != null and company != '' ">
				COMPANY = #{company},
			</if>
			<if test="remark2 != null and remark2 != '' ">
				REMARK2 = #{remark2},
			</if>
			<if test="issCountry != null and issCountry != '' ">
				ISS_COUNTRY = #{issCountry},
			</if>
			<if test="dataType != null and dataType != '' ">
				DATA_TYPE = #{dataType},
			</if>
			<if test="dataValue != null and dataValue != '' ">
				DATA_VALUE = #{dataValue},
			</if>
			<if test="listType != null and listType != '' ">
				LIST_TYPE = #{listType},
			</if>
			<if test="acctName != null and acctName != '' ">
				ACCT_NAME = #{acctName},
			</if>
			<if test="sourceType != null and sourceType != '' ">
				SOURCE_TYPE = #{sourceType},
			</if>
			<if test="tranTimestamp != null ">
				TRAN_TIMESTAMP = #{tranTimestamp},
			</if>
			<if test="userId != null and userId != '' ">
				USER_ID = #{userId},
			</if>
			<if test="ourBankFlag != null and ourBankFlag != '' ">
				OUR_BANK_FLAG = #{ourBankFlag},
			</if>
			<if test="clientNo != null and clientNo != '' ">
				CLIENT_NO = #{clientNo},
			</if>
			<if test="tranDate != null ">
				TRAN_DATE = #{tranDate},
			</if>
			<if test="remark3 != null and remark3 != '' ">
				REMARK3 = #{remark3},
			</if>
			<if test="inputBranch != null and  inputBranch != '' ">
				INPUT_BRANCH = #{inputBranch},
			</if>
		</set>
		<where>
			<trim prefix="(" suffix=")" suffixOverrides="AND">
				<if test="listType != null and  listType != '' ">
					LIST_TYPE = #{listType}  AND
				</if>
				<if test="documentType != null and documentType != '' ">
					DOCUMENT_TYPE = #{documentType}
					AND
				</if>
				<if test="dataValue != null and dataValue != '' ">
					DATA_VALUE = #{dataValue}
					AND
				</if>
				<if test="dataType != null and dataType != '' ">
					DATA_TYPE = #{dataType} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>
	<sql id="Base_Select_List">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
			<include refid="Base_Where_List" />
		</where>
	</sql>

	<sql id="Base_Select_List_Remark3">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
			<include refid="Base_Where_List_Remark3" />
		</where>
	</sql>


	<sql id="Base_Where_List">
		<trim suffixOverrides="AND">
			<if test="documentType != null and  documentType != '' ">
				DOCUMENT_TYPE = #{documentType}  AND
			</if>
			<if test="dataType != null and  dataType != '' ">
				DATA_TYPE = #{dataType}  AND
			</if>
			<if test="dataValue != null and  dataValue != '' ">
				DATA_VALUE = #{dataValue}  AND
			</if>
			<if test="listType != null ">
				LIST_TYPE IN
				<foreach item="item" index="index" collection="listType" open="(" separator="," close=")">
					#{item}
				</foreach>
				AND
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				COMPANY = #{company} AND
			</if>
		</trim>
	</sql>

	<sql id="Base_Where_List_Remark3">
		<trim suffixOverrides="AND">
			<if test="maturityDate != null ">
				MATURITY_DATE = #{maturityDate}  AND
			</if>
			<if test="narrative != null and  narrative != '' ">
				NARRATIVE = #{narrative}  AND
			</if>
			<if test="clientName != null and  clientName != '' ">
				CLIENT_NAME = #{clientName}  AND
			</if>
			<if test="remark1 != null and  remark1 != '' ">
				REMARK1 = #{remark1}  AND
			</if>
			<if test="effectDate != null ">
				EFFECT_DATE = #{effectDate}  AND
			</if>
			<if test="documentType != null and  documentType != '' ">
				DOCUMENT_TYPE = #{documentType}  AND
			</if>
			<if test="tranBranch != null and  tranBranch != '' ">
				TRAN_BRANCH = #{tranBranch}  AND
			</if>
			<if test="company != null and  company != '' ">
				COMPANY = #{company}  AND
			</if>
			<if test="issCountry != null and  issCountry != '' ">
				ISS_COUNTRY = #{issCountry}  AND
			</if>
			<if test="dataType != null and  dataType != '' ">
				DATA_TYPE = #{dataType}  AND
			</if>
			<if test="dataValue != null and  dataValue != '' ">
				DATA_VALUE = #{dataValue}  AND
			</if>
			<if test="listType != null and  listType != '' ">
				LIST_TYPE = #{listType}  AND
			</if>
			<if test="acctName != null and  acctName != '' ">
				ACCT_NAME = #{acctName}  AND
			</if>
			<if test="sourceType != null and  sourceType != '' ">
				SOURCE_TYPE = #{sourceType}  AND
			</if>
			<if test="tranTimestamp != null  ">
				TRAN_TIMESTAMP = #{tranTimestamp}  AND
			</if>

			<if test="userId != null and  userId != '' ">
				USER_ID = #{userId}  AND
			</if>
			<if test="isOurBankFlag != null and  isOurBankFlag != '' ">
				IS_OUR_BANK_FLAG = #{isOurBankFlag}  AND
			</if>
			<if test="clientNo != null and  clientNo != '' ">
				CLIENT_NO = #{clientNo}  AND
			</if>
			<if test="tranDate != null ">
				TRAN_DATE = #{tranDate}  AND
			</if>
			<if test="remark3 != null and  remark3 != '' ">
				REMARK3 = #{remark3}  AND
			</if>
			<if test="inputBranch != null and  inputBranch != '' ">
				INPUT_BRANCH = #{inputBranch}  AND
			</if>
			<if test="'1'=='1'">
			    (REMARK2 ! = 'N' OR  REMARK2 IS NULL )AND
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				COMPANY = #{company} AND
			</if>
		</trim>
	</sql>


	<select id="getListsByTypeAndValueAndTypes"  resultMap="Base_Result_Map">
		<include refid="Base_Select_List" />
	</select>

	<select id="getListsByTypeAndValueAndRemark3"  resultMap="Base_Result_Map">
		<include refid="Base_Select_List_Remark3" />
	</select>

	<select id="getListsByTypeAndValueAcctName"  resultMap="Base_Result_Map">
		<include refid="Base_Select_List_Remark3" />
	</select>

	<select id="getListsByTypeAndValueAcctNameAndClientNo"  resultMap="Base_Result_Map">
		<include refid="Base_Select_List_Remark3" />
	</select>

	<select id="getRcAllList" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList" >
		select <include refid="Base_Column"/>
		from rc_all_list
		<where>
			<if test="dataType != null and dataType != ''" >
				AND DATA_TYPE = #{dataType,jdbcType=VARCHAR}
			</if>
			<if test="dataValue != null and dataValue != ''" >
				AND DATA_VALUE = #{dataValue,jdbcType=VARCHAR}
			</if>
			<if test="documentType != null and documentType != ''" >
				AND DOCUMENT_TYPE = #{documentType,jdbcType=VARCHAR}
			</if>
			<if test="issCountry != null and issCountry != ''" >
				AND ISS_COUNTRY = #{issCountry,jdbcType=VARCHAR}
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			<if test="listCategory != null and listCategory != ''" >
				AND LIST_CATEGORY = #{listCategory,jdbcType=VARCHAR}
			</if>
		</where>
	</select>


	<select id="getRcAllListByClientNoListType"  resultMap="Base_Result_Map">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
		<if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
		</if>
		<if test="listTypes != null and listTypes.length > 0 ">
			AND LIST_TYPE IN
			<foreach item="item" index="index" collection="listTypes" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>


	<select id="getRcAllListByAcctNameAndDocumentId" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList" >
		select <include refid="Base_Column"/>
		from rc_all_list
		<where>
		<if test="acctName != null and acctName.length() > 0" >
			AND ACCT_NAME = #{acctName,jdbcType=VARCHAR}
		</if>
		<if test="documentId != null and documentId.length() > 0" >
			AND DATA_VALUE = #{documentId,jdbcType=VARCHAR}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>

	<select id="getRcAllListByClientNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList" parameterType="java.util.Map" >
		select <include refid="Base_Column"/>
		from rc_all_list
		<where>
			<if test="clientNo != null and clientNo != ''">
				AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
			</if>
			<if test="tranDate != null and company != ''">
				AND <![CDATA[ EFFECT_DATE <= #{tranDate}]]>
			</if>
			<if test="tranDate != null and company != ''">
				AND <![CDATA[ MATURITY_DATE >= #{tranDate}]]>
			</if>
			<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</select>


	<select id="getActiveRcAllList" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList" parameterType="java.util.Map" >
		select <include refid="Base_Column"/>
		from rc_all_list
		<where>
			<if test="dataType != null and dataType != ''">
				AND DATA_TYPE= #{dataType, jdbcType=VARCHAR}
			</if>
			<if test="dataValue != null and dataValue != ''">
				AND DATA_VALUE= #{dataValue, jdbcType=VARCHAR}
			</if>
			<if test="tranDate != null and company != ''">
				AND <![CDATA[ EFFECT_DATE <= #{tranDate}]]>
			</if>
			<if test="tranDate != null and company != ''">
				AND <![CDATA[ MATURITY_DATE >= #{tranDate}]]>
			</if>
			<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</select>


</mapper>
