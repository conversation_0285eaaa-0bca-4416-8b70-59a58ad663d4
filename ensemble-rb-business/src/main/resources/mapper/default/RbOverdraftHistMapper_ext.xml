<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftHist">

    <select id="getRbOverdraftHistsByParams" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftHist"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column"/>
        from rb_overdraft_hist
        where CMISLOAN_NO is not null
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementId != null and agreementId != ''">
            and AGREEMENT_ID = #{agreementId}
        </if>
        <if test="reference != null and reference != ''">
            and REFERENCE = #{reference}
        </if>
        <if test="tranDate != null ">
            and TRAN_DATE = #{tranDate}
        </if>
        <if test="cmisloanNoList != null and cmisloanNoList !=''">
            AND CMISLOAN_NO IN
            <foreach item="item" index="index" collection="cmisloanNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--多法人改造 by luocwa  -->
        <if test="company != null and company != ''">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getRbOverdraftHistsByParamsPage" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftHist"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column"/>
        from rb_overdraft_hist
        where CMISLOAN_NO is not null
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementId != null and agreementId != ''">
            and AGREEMENT_ID = #{agreementId}
        </if>
        <if test="reference != null and reference != ''">
            and REFERENCE = #{reference}
        </if>
        <if test="tranDate != null ">
            and TRAN_DATE = #{tranDate}
        </if>
        <!--多法人改造 by luocwa  -->
        <if test="company != null and company != ''">
            AND COMPANY = #{company}
        </if>
        ORDER BY TRAN_DATE DESC
    </select>

</mapper>
