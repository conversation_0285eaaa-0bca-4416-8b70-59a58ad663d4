<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrRate">

  <select id="getAccordInfoByFreq" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrRate">
    select <include refid="Base_Column"/>
    from RB_IDEP_INCR_RATE
    where AGREEMENT_ID = #{agreementId}
    <if test="periodFreq != null">
      AND PERIOD_FREQ = #{periodFreq}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY DAY_NUM + 0 ASC, NEAR_AMT ASC
  </select>

  <select id="getIncrRatesByDayNum" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrRate">
    SELECT <include refid="Base_Column"/>
    FROM RB_IDEP_INCR_RATE
    WHERE AGREEMENT_ID = #{agreementId} AND DAY_NUM = #{dayNum}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY DAY_NUM + 0 ASC, NEAR_AMT ASC
  </select>
</mapper>
