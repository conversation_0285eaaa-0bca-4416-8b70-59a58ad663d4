<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbWechatAmtInfo">
	<select id="selectWechatAmtInfoChannel" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbWechatAmtInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbWechatAmtInfo">
		select <include refid="Base_Column"/>
		from RB_WECHAT_AMT_INFO
		<where>
  				CHANNEL = #{channel}
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</select>
	<update id="updateByChannel" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbWechatAmtInfo">
		update RB_WECHAT_AMT_INFO
		<set>
			<if test="effectDate != null and effectDate != '' ">
				EFFECT_DATE = #{effectDate},
			</if>
			<if test="minBal != null ">
				MIN_BAL = #{minBal},
			</if>
		</set>
		where CHANNEL = #{channel}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
</mapper>
