<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">

    <select id="selectByPrimaryKeyExt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_SEC_BALANCE
        where INTERNAL_KEY = #{internalKey}
        and AMT_TYPE = #{amtType}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbAcctBalance" parameterType="Long"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_SEC_BALANCE
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">
        insert into RB_ACCT_SEC_BALANCE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="amtType != null">
                AMT_TYPE,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="dacValue != null">
                DAC_VALUE,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="amtType != null">
                #{amtType},
            </if>
            <if test="totalAmount != null">
                #{totalAmount},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate},
            </if>
            <if test="dacValue != null">
                #{dacValue},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">
        update RB_ACCT_SEC_BALANCE
        <set>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="dacValue != null">
                DAC_VALUE = #{dacValue},
            </if>
            <if test="company != null">
                COMPANY = #{company},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        and AMT_TYPE = #{amtType}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

<!--    <select id="selectCorpOverdraftAcct" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"-->
<!--            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">-->

<!--        SELECT-->
<!--        <include refid="Base_Column"/>-->
<!--        <![CDATA[FROM  RB_ACCT_SEC_BALANCE masb-->
<!--            WHERE masb.amt_type = 'ODD'-->
<!--            and masb.total_amount > 0-->
<!--       ]]>-->
<!--    </select>-->
    <select id="selectCorpOverdraftAcctSplit" parameterType="java.util.Map" resultType="java.util.Map">
    SELECT COUNT(1) ROW_COUNT
    FROM  RB_ACCT_SEC_BALANCE masb
        WHERE masb.amt_type = 'ODD'
        and masb.total_amount <![CDATA[ >  ]]> 0
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbAcctSecBalByAmtType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_SEC_BALANCE
        where INTERNAL_KEY = #{internalKey}
        and AMT_TYPE = #{amtType}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectByInternalKeyAndAmtType"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_SEC_BALANCE
        where INTERNAL_KEY = #{internalKey}
        and AMT_TYPE = #{amtType}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSecBalance">
        delete from RB_ACCT_SEC_BALANCE
        where INTERNAL_KEY = #{internalKey}
        <if test="amtType != null">
            AND AMT_TYPE = #{amtType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
</mapper>
