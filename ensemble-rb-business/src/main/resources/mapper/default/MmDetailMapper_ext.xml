<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
<sql id="Base_Column_List">
    INTERNAL_KEY,
    DEAL_NO,
    TRAN_BRANCH,
    <PERSON>ANCH,
    PROFIT_CENTRE,
    ACCT_EXEC,
    INTERNAL_EXTERNAL,
    CLIENT_NO,
    TBOOK,
    COUNTERPARTY,
    PROD_TYPE,
    PROC_TYPE,
    SOURCE_TYPE,
    SOURCE_MODULE,
    BUSINESS_UNIT,
    CLIENT_TYPE,
    EFFECT_DATE,
    TAKE_PLACE,
    LAST_CYCLE_DATE,
    NEXT_CYCLE_DATE,
    MATURITY_DATE,
    TERM,
    TERM_TYPE,
    TAKE_ACCT_NO,
    PLACE_ACCT_NO,
    STATUS,
    CCY,
    PRINCIPAL_AMT,
    INT_TYPE,
    ACTUAL_RATE,
    FLOAT_RATE,
    SPREAD_RATE,
    SPREAD_PERCENT,
    ACCT_FIXED_RATE,
    ACCT_SPREAD_RATE,
    ACCT_PERCENT_RATE,
    REAL_RATE,
    TREASURY_MARGIN,
    INT_AMT,
    YIELD_RATE,
    PERIOD_YIELD,
    YIELD_TO_DATE,
    TRAN_DATE,
    INT_ACCRUED_CTD,
    INT_ACCRUED,
    INT_ADJ_CTD,
    INT_ADJ,
    INT_POSTED_CTD,
    INT_POSTED,
    TAX_TYPE,
    TAX_RATE,
    TAX_ACCRUED_CTD,
    TAX_ACCRUED,
    TAX_POSTED_CTD,
    TAX_POSTED,
    SETTLE_DATE,
    YEAR_BASIS,
    MONTH_BASIS,
    USER_ID,
    LAST_CHANGE_DATE,
    APPR_USER_ID,
    APPROVAL_DATE,
    DEL_REASON,
    LINK_DEAL_INTERNAL_KEY,
    RENEW_DATE,
    INT_CAP,
    AUTO_CHANGE,
    TRAN_TIMESTAMP,
    REFUSE_REASON
  </sql>
  <sql id="A_Base_Column_List">
    a.INTERNAL_KEY,
    a.DEAL_NO,
    a.TRAN_BRANCH,
    a.BRANCH,
    a.PROFIT_CENTRE,
    a.ACCT_EXEC,
    a.INTERNAL_EXTERNAL,
    a.CLIENT_NO,
    a.TBOOK,
    a.COUNTERPARTY,
    a.PROD_TYPE,
    a.PROC_TYPE,
    a.SOURCE_TYPE,
    a.SOURCE_MODULE,
    a.BUSINESS_UNIT,
    a.CLIENT_TYPE,
    a.EFFECT_DATE,
    a.TAKE_PLACE,
    a.LAST_CYCLE_DATE,
    a.NEXT_CYCLE_DATE,
    a.MATURITY_DATE,
    a.TERM,
    a.TERM_TYPE,
    a.TAKE_ACCT_NO,
    a.PLACE_ACCT_NO,
    a.STATUS,
    a.CCY,
    a.PRINCIPAL_AMT,
    a.INT_TYPE,
    a.ACTUAL_RATE,
    a.FLOAT_RATE,
    a.SPREAD_RATE,
    a.SPREAD_PERCENT,
    a.ACCT_FIXED_RATE,
    a.ACCT_SPREAD_RATE,
    a.ACCT_PERCENT_RATE,
    a.REAL_RATE,
    a.TREASURY_MARGIN,
    a.INT_AMT,
    a.YIELD_RATE,
    a.PERIOD_YIELD,
    a.YIELD_TO_DATE,
    a.TRAN_DATE,
    a.INT_ACCRUED_CTD,
    a.INT_ACCRUED,
    a.INT_ADJ_CTD,
    a.INT_ADJ,
    a.INT_POSTED_CTD,
    a.INT_POSTED,
    a.TAX_TYPE,
    a.TAX_RATE,
    a.TAX_ACCRUED_CTD,
    a.TAX_ACCRUED,
    a.TAX_POSTED_CTD,
    a.TAX_POSTED,
    a.SETTLE_DATE,
    a.YEAR_BASIS,
    a.MONTH_BASIS,
    a.USER_ID,
    a.LAST_CHANGE_DATE,
    a.APPR_USER_ID,
    a.APPROVAL_DATE,
    a.DEL_REASON,
    a.LINK_DEAL_INTERNAL_KEY,
    a.RENEW_DATE,
    a.INT_CAP,
    a.AUTO_CHANGE,
    a.TRAN_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    select <include refid="Base_Column_List"/>
    from MM_DETAIL
    where INTERNAL_KEY = #{internalKey}
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    delete from MM_DETAIL
    where INTERNAL_KEY = #{internalKey}
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    update MM_DETAIL
    <set>
      <if test="dealNo != null">
        DEAL_NO = #{dealNo},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="branch != null">
        BRANCH = #{branch},
      </if>
      <if test="profitCenter != null">
        PROFIT_CENTRE = #{profitCenter},
      </if>
      <if test="acctExec != null">
        ACCT_EXEC = #{acctExec},
      </if>
      <if test="internalExternal != null">
        INTERNAL_EXTERNAL = #{internalExternal},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="tbook != null">
        TBOOK = #{tbook},
      </if>
      <if test="counterparty != null">
        COUNTERPARTY = #{counterparty},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="procType != null">
        PROC_TYPE = #{procType},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE = #{sourceModule},
      </if>
      <if test="businessUnit != null">
        BUSINESS_UNIT = #{businessUnit},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="takePlace != null">
        TAKE_PLACE = #{takePlace},
      </if>
      <if test="lastCycleDate != null">
        LAST_CYCLE_DATE = #{lastCycleDate},
      </if>
      <if test="nextCycleDate != null">
        NEXT_CYCLE_DATE = #{nextCycleDate},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="term != null">
        TERM = #{term},
      </if>
      <if test="termType != null">
        TERM_TYPE = #{termType},
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO = #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO = #{placeAcctNo},
      </if>
      <if test="status != null">
        STATUS = #{status},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT = #{principalAmt},
      </if>
      <if test="intType != null">
        INT_TYPE = #{intType},
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE = #{actualRate},
      </if>
      <if test="floatRate != null">
        FLOAT_RATE = #{floatRate},
      </if>
      <if test="spreadRate != null">
        SPREAD_RATE = #{spreadRate},
      </if>
      <if test="spreadPercent != null">
        SPREAD_PERCENT = #{spreadPercent},
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE = #{acctFixedRate},
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE = #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE = #{acctPercentRate},
      </if>
      <if test="realRate != null">
        REAL_RATE = #{realRate},
      </if>
      <if test="treasuryMargin != null">
        TREASURY_MARGIN = #{treasuryMargin},
      </if>
      <if test="intAmt != null">
        INT_AMT = #{intAmt},
      </if>
      <if test="yieldRate != null">
        YIELD_RATE = #{yieldRate},
      </if>
      <if test="periodYield != null">
        PERIOD_YIELD = #{periodYield},
      </if>
      <if test="yieldToDate != null">
        YIELD_TO_DATE = #{yieldToDate},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="intAccruedCtd != null">
        INT_ACCRUED_CTD = #{intAccruedCtd},
      </if>
      <if test="intAccrued != null">
        INT_ACCRUED = #{intAccrued},
      </if>
      <if test="intAdjCtd != null">
        INT_ADJ_CTD = #{intAdjCtd},
      </if>
      <if test="intAdj != null">
        INT_ADJ = #{intAdj},
      </if>
      <if test="intPostedCtd != null">
        INT_POSTED_CTD = #{intPostedCtd},
      </if>
      <if test="intPosted != null">
        INT_POSTED = #{intPosted},
      </if>
      <if test="taxType != null">
        TAX_TYPE = #{taxType},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate},
      </if>
      <if test="taxAccruedCtd != null">
        TAX_ACCRUED_CTD = #{taxAccruedCtd},
      </if>
      <if test="taxAccrued != null">
        TAX_ACCRUED = #{taxAccrued},
      </if>
      <if test="taxPostedCtd != null">
        TAX_POSTED_CTD = #{taxPostedCtd},
      </if>
      <if test="taxPosted != null">
        TAX_POSTED = #{taxPosted},
      </if>
      <if test="settleDate != null">
        SETTLE_DATE = #{settleDate},
      </if>
      <if test="yearBasis != null">
        YEAR_BASIS = #{yearBasis},
      </if>
      <if test="monthBasis != null">
        MONTH_BASIS = #{monthBasis},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID = #{apprUserId},
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE = #{approvalDate},
      </if>
      <if test="delReason != null">
        DEL_REASON = #{delReason},
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY = #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        RENEW_DATE = #{renewDate},
      </if>
      <if test="intCap != null">
        INT_CAP = #{intCap},
      </if>
      <if test="autoChange != null">
        AUTO_CHANGE = #{autoChange},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP= #{tranTimestamp},
      </if>
      <if test="refuseReason != null">
        REFUSE_REASON= #{refuseReason}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    insert into MM_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="dealNo != null">
        DEAL_NO,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="branch != null">
        BRANCH,
      </if>
      <if test="profitCenter != null">
        PROFIT_CENTRE,
      </if>
      <if test="acctExec != null">
        ACCT_EXEC,
      </if>
      <if test="internalExternal != null">
        INTERNAL_EXTERNAL,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="tbook != null">
        TBOOK,
      </if>
      <if test="counterparty != null">
        COUNTERPARTY,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="procType != null">
        PROC_TYPE,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE,
      </if>
      <if test="businessUnit != null">
        BUSINESS_UNIT,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="takePlace != null">
        TAKE_PLACE,
      </if>
      <if test="lastCycleDate != null">
        LAST_CYCLE_DATE,
      </if>
      <if test="nextCycleDate != null">
        NEXT_CYCLE_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="term != null">
        TERM,
      </if>
      <if test="termType != null">
        TERM_TYPE,
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO,
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="spreadRate != null">
        SPREAD_RATE,
      </if>
      <if test="spreadPercent != null">
        SPREAD_PERCENT,
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE,
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE,
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="treasuryMargin != null">
        TREASURY_MARGIN,
      </if>
      <if test="intAmt != null">
        INT_AMT,
      </if>
      <if test="yieldRate != null">
        YIELD_RATE,
      </if>
      <if test="periodYield != null">
        PERIOD_YIELD,
      </if>
      <if test="yieldToDate != null">
        YIELD_TO_DATE,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="intAccruedCtd != null">
        INT_ACCRUED_CTD,
      </if>
      <if test="intAccrued != null">
        INT_ACCRUED,
      </if>
      <if test="intAdjCtd != null">
        INT_ADJ_CTD,
      </if>
      <if test="intAdj != null">
        INT_ADJ,
      </if>
      <if test="intPostedCtd != null">
        INT_POSTED_CTD,
      </if>
      <if test="intPosted != null">
        INT_POSTED,
      </if>
      <if test="taxType != null">
        TAX_TYPE,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="taxAccruedCtd != null">
        TAX_ACCRUED_CTD,
      </if>
      <if test="taxAccrued != null">
        TAX_ACCRUED,
      </if>
      <if test="taxPostedCtd != null">
        TAX_POSTED_CTD,
      </if>
      <if test="taxPosted != null">
        TAX_POSTED,
      </if>
      <if test="settleDate != null">
        SETTLE_DATE,
      </if>
      <if test="yearBasis != null">
        YEAR_BASIS,
      </if>
      <if test="monthBasis != null">
        MONTH_BASIS,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID,
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE,
      </if>
      <if test="delReason != null">
        DEL_REASON,
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY,
      </if>
      <if test="renewDate != null">
        RENEW_DATE,
      </if>
      <if test="intCap != null">
        INT_CAP,
      </if>
      <if test="autoChange != null">
        AUTO_CHANGE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="refuseReason != null">
        REFUSE_REASON,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="dealNo != null">
        #{dealNo},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="profitCenter != null">
        #{profitCenter},
      </if>
      <if test="acctExec != null">
        #{acctExec},
      </if>
      <if test="internalExternal != null">
        #{internalExternal},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="tbook != null">
        #{tbook},
      </if>
      <if test="counterparty != null">
        #{counterparty},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="procType != null">
        #{procType},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="sourceModule != null">
        #{sourceModule},
      </if>
      <if test="businessUnit != null">
        #{businessUnit},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="takePlace != null">
        #{takePlace},
      </if>
      <if test="lastCycleDate != null">
        #{lastCycleDate},
      </if>
      <if test="nextCycleDate != null">
        #{nextCycleDate},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="term != null">
        #{term},
      </if>
      <if test="termType != null">
        #{termType},
      </if>
      <if test="takeAcctNo != null">
        #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        #{placeAcctNo},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="principalAmt != null">
        #{principalAmt},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="spreadRate != null">
        #{spreadRate},
      </if>
      <if test="spreadPercent != null">
        #{spreadPercent},
      </if>
      <if test="acctFixedRate != null">
        #{acctFixedRate},
      </if>
      <if test="acctSpreadRate != null">
        #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        #{acctPercentRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="treasuryMargin != null">
        #{treasuryMargin},
      </if>
      <if test="intAmt != null">
        #{intAmt},
      </if>
      <if test="yieldRate != null">
        #{yieldRate},
      </if>
      <if test="periodYield != null">
        #{periodYield},
      </if>
      <if test="yieldToDate != null">
        #{yieldToDate},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="intAccruedCtd != null">
        #{intAccruedCtd},
      </if>
      <if test="intAccrued != null">
        #{intAccrued},
      </if>
      <if test="intAdjCtd != null">
        #{intAdjCtd},
      </if>
      <if test="intAdj != null">
        #{intAdj},
      </if>
      <if test="intPostedCtd != null">
        #{intPostedCtd},
      </if>
      <if test="intPosted != null">
        #{intPosted},
      </if>
      <if test="taxType != null">
        #{taxType},
      </if>
      <if test="taxRate != null">
        #{taxRate},
      </if>
      <if test="taxAccruedCtd != null">
        #{taxAccruedCtd},
      </if>
      <if test="taxAccrued != null">
        #{taxAccrued},
      </if>
      <if test="taxPostedCtd != null">
        #{taxPostedCtd},
      </if>
      <if test="taxPosted != null">
        #{taxPosted},
      </if>
      <if test="settleDate != null">
        #{settleDate},
      </if>
      <if test="yearBasis != null">
        #{yearBasis},
      </if>
      <if test="monthBasis != null">
        #{monthBasis},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        #{apprUserId},
      </if>
      <if test="approvalDate != null">
        #{approvalDate},
      </if>
      <if test="delReason != null">
        #{delReason},
      </if>
      <if test="linkDealInternalKey != null">
        #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        #{renewDate},
      </if>
      <if test="intCap != null">
        #{intCap},
      </if>
      <if test="autoChange != null">
        #{autoChange},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="refuseReason != null">
        #{refuseReason},
      </if>
    </trim>
  </insert>
  <select id="getMmDetailByDealNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_DETAIL
    WHERE DEAL_NO = #{dealNo}
    AND status!='M'
  </select>

  <select id="getMmDetailByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_DETAIL
    WHERE INTERNAL_KEY = #{internalKey}
    AND status IN ('V','A','P')
  </select>
  <select id="getMmDetailByinternalExternal" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_DETAIL
    WHERE DEAL_NO = #{dealNo}
      AND INTERNAL_EXTERNAL = #{internalExternal}
  </select>
<!--  <select id="selectMaturityMmDetail" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">-->
<!--    select <include refid="Base_Column_List"/>-->
<!--      from mm_detail-->
<!--      where  maturity_date >= #{lastRunDate}-->
<!--        and  status='P'-->
<!--        and  link_deal_internal_key is not null-->
<!--        ORDER BY internal_key-->
<!--  </select>-->
<!--  <select id="selectMaturityMmDetailSplit" parameterType="java.util.Map"  resultType="java.util.Map">
    SELECT COUNT(1) ROW_COUNT
    FROM mm_detail md
    where md.maturity_date=#{lastRunDate}
    and  md.status='P'
    and md.link_deal_internal_key is not null
  </select>-->

  <select id="selectMaturityMmDetailSplit" parameterType="java.util.Map" resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      MIN(internal_key) START_KEY,
      MAX(internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM
      (
      SELECT
      DISTINCT internal_key,
      @rownum :=@rownum + 1 AS rownum
      FROM
      mm_detail
      (SELECT @rownum := -1) t
      WHERE acct_status = 'P'
      AND maturity_date = #{lastRunDate}
      ORDER BY internal_key
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT MIN (internal_key) START_KEY, MAX (internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM
      (SELECT DISTINCT internal_key
      FROM mm_detail
      WHERE acct_status = 'P'
      AND maturity_date = #{lastRunDate}
      ORDER BY internal_key)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>

  <select id="selectValueMmDetail" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    select <include refid="Base_Column_List"/>
      from mm_detail
      where  effect_date=#{baseParam.runDate}
        and  status='A'
        and  link_deal_internal_key is not null
        ORDER BY internal_key
  </select>
  <select id="selectValueMmDetailSplit" parameterType="java.util.Map"  resultType="java.util.Map">
    SELECT COUNT(1) ROW_COUNT
    FROM mm_detail md
    where md.effect_date=#{lastRunDate}
    and  md.status='A'
    and md.link_deal_internal_key is not null
  </select>

  <select id="selectBeLinkedMmDetail" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="Base_Column_List"/>
    FROM mm_detail
    where LINK_DEAL_INTERNAL_KEY = #{linkDealInternalKey}
  </select>


  <select id="getMmdetailByInternalKeys" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    select <include refid="Base_Column_List"/>
    from MM_DETAIL
    where INTERNAL_KEY IN
    <foreach item="item" index="index" collection="internalKeys" open="(" separator="," close=")">
    #{item}
  </foreach>
  </select>

  
  <select id="getMmDetailByTranBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_DETAIL
    WHERE status='V'
    <if test="dealNo != null">
      AND DEAL_NO = #{dealNo}
    </if>
    <if test="internalExternal != null">
      AND INTERNAL_EXTERNAL = #{internalExternal}
    </if>
    <if test="tranBranch != null">
      AND TRAN_BRANCH = #{tranBranch}
    </if>
    <if test="userId != null">
      AND user_id != #{userId}
    </if>
  </select>



  <select id="getMmDetailByReceipt" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="A_Base_Column_List"/>, b.receipt_no
    FROM MM_DETAIL a,MM_RECEIPT b
    WHERE a.internal_key = b.internal_key
    AND b.APPROVAL_STATUS = 'N'
    <if test="dealNo != null">
      AND a.DEAL_NO = #{dealNo}
    </if>
    <if test="internalExternal != null">
      AND a.INTERNAL_EXTERNAL = #{internalExternal}
    </if>
    <if test="tranBranch != null">
      AND a.TRAN_BRANCH = #{tranBranch}
    </if>
  </select>
<!--  <select id="queryMmDetailForBatch" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"  resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">-->
<!--    select <include refid="Base_Column_List"/>-->
<!--      from mm_detail-->
<!--      where STATUS  = 'A'-->
<!--      or STATUS  = 'P'-->
<!--        ORDER BY TAKE_PLACE DESC-->
<!--  </select>-->
<!--  <select id="queryMmDetailForBatchSplit" parameterType="java.util.Map"  resultType="java.util.Map">
    SELECT COUNT(1) ROW_COUNT
    from mm_detail md
    where  md.last_change_date=#{lastRunDate}
  </select>-->
  <select id="queryMmDetailForBatchSplit" parameterType="java.util.Map" resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      MIN(internal_key) START_KEY,
      MAX(internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM
      (
      SELECT
      DISTINCT internal_key,
      @rownum :=@rownum + 1 AS rownum
      FROM
      mm_detail,
      (SELECT @rownum := -1) t
      WHERE status != 'M'
      AND (last_change_date BETWEEN #{lastRunDate} AND #{yesterday} or EFFECT_DATE BETWEEN #{lastRunDate} AND #{yesterday} )
      ORDER BY internal_key
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT MIN (internal_key) START_KEY, MAX (internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM
      (SELECT DISTINCT internal_key
      FROM mm_detail
      WHERE status != 'M'
      AND (last_change_date BETWEEN #{lastRunDate} AND #{yesterday} or EFFECT_DATE BETWEEN #{lastRunDate} AND #{yesterday} )
      ORDER BY internal_key)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>

  <select id="getAllMmDetailByDealNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_DETAIL
    WHERE DEAL_NO = #{dealNo}
  </select>
  <select id="getMmDetailByDealNoForChange" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT
    mmd.INTERNAL_KEY,
    mmd.DEAL_NO,
    mbr.RECEIPT_NO AS RECEIPT_NO,
    mmd.TAKE_PLACE,
    mmd.PROD_TYPE,
    mmd.CLIENT_NO,
    mmd.BRANCH,
    mmd.COUNTERPARTY,
    mba.CONTRA_ACCT_NAME AS CONTRA_ACCT_NAME,
    mmd.PRINCIPAL_AMT,
    mmd.EFFECT_DATE,
    mmd.MATURITY_DATE,
    mmd.TERM,
    mmd.TERM_TYPE,
    mmd.STATUS,
    mmd.INT_TYPE,
    mmd.ACTUAL_RATE,
    mmd.ACCT_FIXED_RATE,
    mmd.TREASURY_MARGIN,
    mmd.REAL_RATE,
    mmd.YEAR_BASIS,
    mmd.MONTH_BASIS,
    mmd.TAKE_ACCT_NO,
    mmd.PLACE_ACCT_NO
    FROM
    mm_detail mmd
    LEFT JOIN RB_receipt mbr
    on mmd.INTERNAL_KEY = mbr.ACCT_INTERNAL_KEY
    LEFT JOIN RB_acct_attach mba
    on mmd.COUNTERPARTY = mba.CONTRA_ACCT_NO
    WHERE mmd.DEAL_NO = #{dealNo}
  </select>
  <select id="getMmDetailInfoByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmDetail">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_DETAIL
    WHERE INTERNAL_KEY = #{internalKey}
  </select>

  <insert id="insertMmDetail" parameterType="java.util.List">
    <![CDATA[
    insert into MM_DETAIL (
        INTERNAL_KEY,
        DEAL_NO,
        TRAN_BRANCH,
        BRANCH,
        PROFIT_CENTRE,
        ACCT_EXEC,
        INTERNAL_EXTERNAL,
        CLIENT_NO,
        TBOOK,
        COUNTERPARTY,
        PROD_TYPE,
        PROC_TYPE,
        SOURCE_TYPE,
        SOURCE_MODULE,
        BUSINESS_UNIT,
        CLIENT_TYPE,
        EFFECT_DATE,
        TAKE_PLACE,
        LAST_CYCLE_DATE,
        NEXT_CYCLE_DATE,
        MATURITY_DATE,
        TERM,
        TERM_TYPE,
        TAKE_ACCT_NO,
        PLACE_ACCT_NO,
        STATUS,
        CCY,
        PRINCIPAL_AMT,
        INT_TYPE,
        ACTUAL_RATE,
        FLOAT_RATE,
        SPREAD_RATE,
        SPREAD_PERCENT,
        ACCT_FIXED_RATE,
        ACCT_SPREAD_RATE,
        ACCT_PERCENT_RATE,
        REAL_RATE,
        TREASURY_MARGIN,
        INT_AMT,
        YIELD_RATE,
        PERIOD_YIELD,
        YIELD_TO_DATE,
        TRAN_DATE,
        INT_ACCRUED_CTD,
        INT_ACCRUED,
        INT_ADJ_CTD,
        INT_ADJ,
        INT_POSTED_CTD,
        INT_POSTED,
        TAX_TYPE,
        TAX_RATE,
        TAX_ACCRUED_CTD,
        TAX_ACCRUED,
        TAX_POSTED_CTD,
        TAX_POSTED,
        SETTLE_DATE,
        YEAR_BASIS,
        MONTH_BASIS,
        USER_ID,
        LAST_CHANGE_DATE,
        APPR_USER_ID,
        APPROVAL_DATE,
        DEL_REASON,
        LINK_DEAL_INTERNAL_KEY,
        RENEW_DATE,
        INT_CAP,
        AUTO_CHANGE,
        TRAN_TIMESTAMP
    )
    values(
        #{internalKey},
        #{dealNo},
        #{tranBranch},
        #{branch},
        #{profitCenter},
        #{acctExec},
        #{internalExternal},
        #{clientNo},
        #{tbook},
        #{counterparty},
        #{prodType},
        #{procType},
        #{sourceType},
        #{sourceModule},
        #{businessUnit},
        #{clientType},
        #{effectDate},
        #{takePlace},
        #{lastCycleDate},
        #{nextCycleDate},
        #{maturityDate},
        #{term},
        #{termType},
        #{takeAcctNo},
        #{placeAcctNo},
        #{status},
        #{ccy},
        #{principalAmt},
        #{intType},
        #{actualRate},
        #{floatRate},
        #{spreadRate},
        #{spreadPercent},
        #{acctFixedRate},
        #{acctSpreadRate},
        #{acctPercentRate},
        #{realRate},
        #{treasuryMargin},
        #{intAmt},
        #{yieldRate},
        #{periodYield},
        #{yieldToDate},
        #{tranDate},
        #{intAccruedCtd},
        #{intAccrued},
        #{intAdjCtd},
        #{intAdj},
        #{intPostedCtd},
        #{intPosted},
        #{taxType},
        #{taxRate},
        #{taxAccruedCtd},
        #{taxAccrued},
        #{taxPostedCtd},
        #{taxPosted},
        #{settleDate},
        #{yearBasis},
        #{monthBasis},
        #{userId},
        #{lastChangeDate},
        #{apprUserId},
        #{approvalDate},
        #{delReason},
        #{linkDealInternalKey},
        #{renewDate},
        #{intCap},
        #{autoChange},
        #{tranTimestamp})
        ]]>
  </insert>
</mapper>
