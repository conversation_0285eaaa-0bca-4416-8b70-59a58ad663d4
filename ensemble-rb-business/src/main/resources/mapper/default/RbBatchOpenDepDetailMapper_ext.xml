<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDepositDetail">

  <select id="getDepSumTranAmtByBatch" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_OPEN_DEPOSIT_DETAIL
    where BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDepDetails" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDepositDetail" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDepositDetail">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DEPOSIT_DETAIL
    where BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by SEQ_NO
  </select>
  <select id="getChargeFeeInfoForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDepositDetail" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDepositDetail">
    SELECT <include refid="Base_Column"/>
    FROM RB_BATCH_OPEN_DEPOSIT_DETAIL
    WHERE BATCH_NO = #{batchNo}
    AND  SEQ_NO = #{seqNo,jdbcType=VARCHAR}
    AND BATCH_STATUS = #{batchStatus,jdbcType=VARCHAR}
    FOR UPDATE
  </select>

  <update id="updateExt">
    UPDATE
    <include refid="Table_Name"/>
    <include refid="Base_Set"/>
    <include refid="PrimaryKey_Where_Ext"/>
  </update>

  <sql id="PrimaryKey_Where_Ext">
    <where>
      <if test="batchNo != null and  batchNo != '' ">
        AND BATCH_NO = #{batchNo,jdbcType=VARCHAR}
      </if>
      <if test="seqNo != null and  seqNo != '' ">
        AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </sql>

</mapper>
