<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAutoCreateDetail">

  <select id="selectExistDetail" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAutoCreateDetail" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAutoCreateDetail">
    SELECT <include refid="Base_Column"/>
    FROM RB_AUTO_CREATE_DETAIL
    <where>
    <!--多法人改造 by luocwa  -->
    <if test="branch != null and branch != ''">
      AND BRANCH = #{branch}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and ccy != ''">
      AND CCY = #{ccy}
    </if>
    <if test="acctName != null and acctName != ''">
      AND ACCT_NAME = #{acctName}
    </if>
    </where>
  </select>
</mapper>