<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RcInnerList">

	<select id="selectTheStatusA" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RcInnerList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RcInnerList" >
		select <include refid="Base_Column"/>
		from RC_INNER_LIST
		where 1=1
		<if test="dataType != null" >
			AND DATA_TYPE = #{dataType,jdbcType=VARCHAR}
		</if>
		<if test="dataValue != null" >
			AND DATA_VALUE = #{dataValue,jdbcType=VARCHAR}
		</if>
		<if test="listType != null" >
			AND LIST_TYPE = #{listType,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getByData" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RcInnerList">
		select
		<include refid="Base_Column"/>
		FROM RC_INNER_LIST
		WHERE
		1=1
		<if test="dataType != null and dataType.length() > 0">
			AND DATA_TYPE = #{company}
		</if>
		<if test="dataValue != null and dataValue.length() > 0">
			AND DATA_VALUE = #{company}
		</if>
		<if test="listType != null and listType.length() > 0">
			AND LIST_TYPE = #{company}
		</if>
		<if test="status != null and status.length() > 0">
			AND STATUS = #{company}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="updateRecordStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RcInnerList">
		update RC_INNER_LIST
		set 	RECORD_STATUS = 'C'
		where DATA_VALUE = #{dataValue}
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
