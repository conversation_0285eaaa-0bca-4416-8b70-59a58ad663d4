<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbEbGlMap">

    <select id="selectByProdType" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbEbGlMap">
        select
        <include refid="Base_Column"/>
        from RB_EB_GL_MAP
        <where>
            <trim suffixOverrides="AND">
                <if test="prodType != null and  prodType != '' ">
                    PROD_TYPE = #{prodType}  AND
                </if>
                <if test="eventType != null and  eventType != '' ">
                    EVENT_TYPE = #{eventType}  AND
                </if>
                <if test="tranType != null and  tranType != '' ">
                    TRAN_TYPE = #{tranType}  AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
</mapper>
