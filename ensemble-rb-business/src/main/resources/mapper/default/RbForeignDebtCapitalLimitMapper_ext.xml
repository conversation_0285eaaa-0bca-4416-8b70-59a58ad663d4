<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbForeignDebtCapitalLimit">
  <resultMap id="RbForeignDebtCapitalLimitMap" type="com.dcits.ensemble.rb.business.entity.dbmodel.RbForeignDebtCapitalLimit">
    <result column="trade_no" property="tradeNo"  javaType="String" />
  </resultMap>
  <select id="getRbForeignDebtCapitalLimit" resultMap="RbForeignDebtCapitalLimitMap"
          parameterType="java.util.Map">
    select
    status,
    trade_no,
    client_no,
    acct_name,
    total_limit,
    expenses,
    company,
    tran_timestamp,
    base_acct_no,
    settlement_ed_oth_amt,
    settlement_ed_ccy,
    settlement_ed_amt,
    due_amount
    from Rb_Foreign_Debt_Capital_Limit
    <where>
      1=1  AND STATUS = 'A'
      <if test="tradeNo != null and tradeNo != '' ">
        AND TRADE_NO = #{tradeNo}
      </if>
      <if test="baseAcctNo != null and baseAcctNo != ''  ">
        AND BASE_ACCT_NO = #{baseAcctNo}
      </if>



    </where>
  </select>
  
</mapper>
