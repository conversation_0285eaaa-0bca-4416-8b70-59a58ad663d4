<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPasswordHist">
    <insert id="insertExt">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        insert into RB_PASSWORD_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pwdKey != null">
                PWD_KEY,
            </if>
            <if test="pwdType != null">
                PWD_TYPE,
            </if>
            <if test="passwordOld != null">
                PASSWORD_OLD,
            </if>
            <if test="passwordNew != null">
                PASSWORD_NEW,
            </if>
            <if test="modifyPasswordType != null">
                MODIFY_PASSWORD_TYPE,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="modifyReason != null">
                MODIFY_REASON,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>

            <if test="authUserId != null">
                AUTH_USER_ID,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pwdKey != null">
                #{pwdKey},
            </if>
            <if test="pwdType != null">
                #{pwdType},
            </if>
            <if test="passwordOld != null">
                #{passwordOld},
            </if>
            <if test="passwordNew != null">
                #{passwordNew},
            </if>
            <if test="modifyPasswordType != null">
                #{modifyPasswordType},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="modifyReason != null">
                #{modifyReason},
            </if>
            <if test="userId != null">
                #{userId},
            </if>

            <if test="authUserId != null">
                #{authUserId},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="reference != null">
                #{reference},
            </if>

        </trim>
    </insert>
</mapper>
