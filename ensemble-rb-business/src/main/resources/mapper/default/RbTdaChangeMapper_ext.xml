<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaChange">

  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaChange">
    insert into RB_TDA_CHANGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="changeTdaSeqNo != null">
        CHANGE_TDA_SEQ_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="term != null">
        TERM,
      </if>
      <if test="termType != null">
        TERM_TYPE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="newTerm != null">
        NEW_TERM,
      </if>
      <if test="newTermType != null">
        NEW_TERM_TYPE,
      </if>
      <if test="newMaturityDate != null">
        NEW_MATURITY_DATE,
      </if>
      <if test="changeTdaStatus != null">
        CHANGE_TDA_STATUS,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tdaChangeType != null">
        TDA_CHANGE_TYPE,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="changeTdaSeqNo != null">
        #{changeTdaSeqNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="term != null">
        #{term},
      </if>
      <if test="termType != null">
        #{termType},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="newTerm != null">
        #{newTerm},
      </if>
      <if test="newTermType != null">
        #{newTermType},
      </if>
      <if test="newMaturityDate != null">
        #{newMaturityDate},
      </if>
      <if test="changeTdaStatus != null">
        #{changeTdaStatus},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tdaChangeType != null">
        #{tdaChangeType},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
  <update id="updateByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaChange">
     update MB_TDA_HIST
     <set>
      <if test="changeTdaSeqNo != null">
        CHANGE_TDA_SEQ_NO=#{changeTdaSeqNo},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY=#{internalKey},
      </if>
      <if test="term != null">
        TERM=#{term},
      </if>
      <if test="termType != null">
        TERM_TYPE=#{termType},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE=#{maturityDate},
      </if>
      <if test="newTerm != null">
        NEW_TERM=#{newTerm},
      </if>
      <if test="newTermType != null">
        NEW_TERM_TYPE=#{newTermType},
      </if>
      <if test="newMaturityDate != null">
        NEW_MATURITY_DATE=#{newMaturityDate},
      </if>
      <if test="changeTdaStatus != null">
        CHANGE_TDA_STATUS=#{changeTdaStatus},
      </if>
      <if test="tranDate != null">
        TRAN_DATE=#{tranDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP=#{tranTimestamp},
      </if>
      <if test="userId != null">
        USER_ID=#{userId},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH=#{tranBranch},
      </if>
      <if test="tdaChangeType != null">
        TDA_CHANGE_TYPE=#{tdaChangeType}
      </if>
    </set>
      where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getMbTdaChangeList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaChange">
    select <include refid="Base_Column"/>
    from RB_TDA_CHANGE
    where INTERNAL_KEY = #{internalKey}
    <if test="changeTdaStatus != null">
      AND  CHANGE_TDA_STATUS=#{changeTdaStatus}
    </if>
    <if test="clientNo != null">
      AND  CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY TRAN_DATE DESC
  </select>

  <update id="updateMbTdaChangeStatus" parameterType="java.util.Map">
    update RB_TDA_CHANGE
    <set>
      <if test="changeTdaStatus != null">
        CHANGE_TDA_STATUS=#{changeTdaStatus}
      </if>
    </set>
    where CHANGE_TDA_SEQ_NO=#{changeTdaSeqNo}
    AND  CLIENT_NO = #{clientNo}
    AND CHANGE_TDA_STATUS!='C'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
