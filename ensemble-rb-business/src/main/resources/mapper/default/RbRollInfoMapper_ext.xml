<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRollInfo">
	<select id="get" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRollInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRollInfo">
		SELECT INTERNAL_KEY,SYSTEM_ID,INT_CLASS,TRAN_DATE,INT_TYPE,SPREAD_RATE,
			   REAL_RATE,ACTUAL_RATE,SPREAD_PERCENT,INT_APPL_TYPE,RATE_EFFECT_TYPE,NEXT_ROLL_DATE,
			   ROLL_FREQ,ROL<PERSON>_DAY,NEW_INT_TYPE,NEW_SPREAD_RATE,NEW_REAL_RATE,
			   NEW_ACTUAL_RATE,NEW_SPREAD_PERCENT,NEW_INT_APPL_TYPE,NEW_RATE_EFFECT_TYPE,NEW_NEXT_ROLL_DATE,NEW_ROLL_FREQ,
			   NEW_ROLL_DAY,USER_ID,COMPANY,IS_RETRY_FLAG,TRAN_TIMESTAMP,TRAN_TIME,ROUTER_KEY,CALC_BY_INT FROM RB_ROLL_INFO
		WHERE IRL_INTERNAL_KEY = #{irlInternalKey}
		  AND INT_CLASS = #{intClass}
		  AND TRAN_DATE = #{tranDate,jdbcType=DATE}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getHistByIrlInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRollInfo">
		SELECT * FROM RB_ROLL_INFO
		<where>
		<if test="irlInternalKey != null">
			AND INTERNAL_KEY = #{irlInternalKey}
		</if>
		<if test="startDate != null">
			AND TRAN_DATE <![CDATA[>=]]> #{startDate,jdbcType=DATE}
		</if>
		<if test="endDate != null">
			AND TRAN_DATE <![CDATA[<=]]> #{endDate,jdbcType=DATE}
		</if>
		<if test="clientNo != null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
		order by TRAN_TIMESTAMP desc
	</select>
</mapper>
