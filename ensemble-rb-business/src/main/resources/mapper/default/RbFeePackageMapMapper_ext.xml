<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeePackageMap">
	<select id="getPackageByPackageId" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeePackage" parameterType="java.util.Map">
		select * from RB_FEE_PACKAGE
		where PACKAGE_ID = #{packageId}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="getAllRbFeePackageMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeePackageMap">
		select *
		from RB_FEE_PACKAGE_MAP
		where 1=1
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
