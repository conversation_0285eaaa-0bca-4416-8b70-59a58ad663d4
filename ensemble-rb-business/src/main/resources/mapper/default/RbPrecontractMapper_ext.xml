<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey}
    <if test="precontractCcy != null">
      AND PRECONTRACT_CCY = #{precontractCcy}
    </if>
    <if test="precontractAmt != null">
      AND PRECONTRACT_AMT = #{precontractAmt}
    </if>
    <if test="precontractDrawDate != null">
      AND PRECONTRACT_DRAW_DATE = #{precontractDrawDate}
    </if>
    <if test="tranBranch != null">
      AND TRAN_BRANCH = #{tranBranch}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    delete from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey}
    <if test="precontractCcy != null">
      AND PRECONTRACT_CCY = #{precontractCcy}
    </if>
    <if test="precontractAmt != null">
      AND PRECONTRACT_AMT = #{precontractAmt}
    </if>
    <if test="precontractDrawDate != null">
      AND PRECONTRACT_DRAW_DATE = #{precontractDrawDate}
    </if>
    <if test="tranBranch != null">
      AND TRAN_BRANCH = #{tranBranch}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    update RB_PRECONTRACT
    <set>
      <if test="precontractNo != null">
        PRECONTRACT_NO = #{precontractNo},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctName != null">
        ACCT_NAME = #{acctName},
      </if>
      <if test="precontractDate != null">
        PRECONTRACT_DATE = #{precontractDate},
      </if>
      <if test="precontractMethod != null">
        PRECONTRACT_METHOD = #{precontractMethod},
      </if>
      <if test="precontractWtdType != null">
        PRECONTRACT_WTD_TYPE = #{precontractWtdType},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="mobileNo != null">
        MOBILE_NO = #{mobileNo},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="precontractStatus != null">
        PRECONTRACT_STATUS = #{precontractStatus},
      </if>
      <if test="unlostUserId != null">
        UNLOST_USER_ID = #{unlostUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="precontractType != null">
        PRECONTRACT_TYPE = #{precontractType}
      </if>
      <if test="violateAdj != null">
        VIOLATE_ADJ = #{violateAdj}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND PRECONTRACT_CCY = #{precontractCcy}
    AND PRECONTRACT_AMT = #{precontractAmt}
    AND PRECONTRACT_DRAW_DATE = #{precontractDrawDate}
    AND TRAN_BRANCH = #{tranBranch}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    insert into RB_PRECONTRACT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="precontractNo != null">
        PRECONTRACT_NO,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="acctName != null">
        ACCT_NAME,
      </if>
      <if test="precontractDate != null">
        PRECONTRACT_DATE,
      </if>
      <if test="precontractCcy != null">
        PRECONTRACT_CCY,
      </if>
      <if test="precontractAmt != null">
        PRECONTRACT_AMT,
      </if>
      <if test="precontractDrawDate != null">
        PRECONTRACT_DRAW_DATE,
      </if>
      <if test="precontractMethod != null">
        PRECONTRACT_METHOD,
      </if>
      <if test="precontractWtdType != null">
        PRECONTRACT_WTD_TYPE,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="mobileNo != null">
        MOBILE_NO,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="precontractStatus != null">
        PRECONTRACT_STATUS,
      </if>
      <if test="unlostUserId != null">
        UNLOST_USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="precontractType != null">
        PRECONTRACT_TYPE,
      </if>
      <if test="violateAdj != null">
        VIOLATE_ADJ,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="precontractNo != null">
        #{precontractNo},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="acctName != null">
        #{acctName},
      </if>
      <if test="precontractDate != null">
        #{precontractDate},
      </if>
      <if test="precontractCcy != null">
        #{precontractCcy},
      </if>
      <if test="precontractAmt != null">
        #{precontractAmt},
      </if>
      <if test="precontractDrawDate != null">
        #{precontractDrawDate},
      </if>
      <if test="precontractMethod != null">
        #{precontractMethod},
      </if>
      <if test="precontractWtdType != null">
        #{precontractWtdType},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="mobileNo != null">
        #{mobileNo},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="precontractStatus != null">
        #{precontractStatus},
      </if>
      <if test="unlostUserId != null">
        #{unlostUserId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="precontractType != null">
        #{precontractType},
      </if>
      <if test="violateAdj != null">
        #{violateAdj},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="getMbPrecontract" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey}
    and PRECONTRACT_DRAW_DATE  = #{precontractDrawDate}
    and PRECONTRACT_AMT = #{precontractAmt}
    and PRECONTRACT_STATUS != 'C'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbPrecontractReversal" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey}
    and PRECONTRACT_DRAW_DATE  = #{precontractDrawDate}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbPrecontractStatus" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey}
    and PRECONTRACT_DRAW_DATE  = #{precontractDrawDate}
    and PRECONTRACT_AMT = #{precontractAmt}
    and PRECONTRACT_STATUS = #{precontractStatus}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbPrecontracts" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey}
    and PRECONTRACT_DRAW_DATE  = #{precontractDrawDate}
    and PRECONTRACT_STATUS = #{precontractStatus}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbPrecontractOnly" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey}
    and PRECONTRACT_CCY = #{ccy}
    and PRECONTRACT_AMT = #{precontractAmt}
    and PRECONTRACT_DRAW_DATE  = #{precontractDrawDate}
    and TRAN_BRANCH = #{tranBranch}
    and PRECONTRACT_STATUS = 'A'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectMbPrecontracts" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey} and PRECONTRACT_STATUS = #{precontractStatus}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectMbPrecontractsByBranch" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where TRAN_BRANCH = #{tranBranch} and PRECONTRACT_STATUS = #{precontractStatus}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectMbPrecontractsByType" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey} and PRECONTRACT_TYPE = #{precontractType} and PRECONTRACT_STATUS  IN ('A')
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="precontractDate != null ">
      AND PRECONTRACT_DATE <![CDATA[<=]]> #{precontractDate,jdbcType=DATE}
    </if>
  </select>
  <select id="getPreMbPrecontracts" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey} and (PRECONTRACT_STATUS = 'A' or PRECONTRACT_STATUS = 'C')
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- Batch modification status -->
  <update id="updateByPrecontractNoForDue" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    update RB_PRECONTRACT set
                            LAST_CHANGE_DATE =  #{lastChangeDate},
                            PRECONTRACT_STATUS = #{precontractStatus}
    where PRECONTRACT_NO = #{precontractNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updateByPrecontractNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    update RB_PRECONTRACT
    <set>
      <if test="precontractDate != null">
        PRECONTRACT_DATE = #{precontractDate},
      </if>
      <if test="precontractMethod != null">
        PRECONTRACT_METHOD = #{precontractMethod},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="precontractWtdType != null">
        PRECONTRACT_WTD_TYPE = #{precontractWtdType},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="mobileNo != null">
        MOBILE_NO = #{mobileNo},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="precontractStatus != null">
        PRECONTRACT_STATUS = #{precontractStatus},
      </if>
      <if test="unlostUserId != null">
        UNLOST_USER_ID = #{unlostUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="precontractType != null">
        PRECONTRACT_TYPE = #{precontractType},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctName != null">
        ACCT_NAME = #{acctName},
      </if>
      <if test="precontractAmt != null">
        PRECONTRACT_AMT = #{precontractAmt},
      </if>
      <if test="precontractCcy != null">
        PRECONTRACT_CCY = #{precontractCcy},
      </if>
      <if test="precontractDrawDate != null">
        PRECONTRACT_DRAW_DATE = #{precontractDrawDate},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="violateAdj != null">
        VIOLATE_ADJ = #{violateAdj},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where PRECONTRACT_NO = #{precontractNo} and CLIENT_NO = #{clientNo}
    <if test="internalKey != null and internalKey != ''">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getMbPrecontractNotice" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/> from RB_PRECONTRACT
    <where>
    <if test="applyBranchIdList != null">
      AND TRAN_BRANCH IN ${applyBranchIdList}
    </if>
    <if test="precontractStatus != null">
      AND PRECONTRACT_STATUS = #{precontractStatus}
    </if>
    <if test="startDate != null">
      AND PRECONTRACT_DATE &gt;= #{startDate}
    </if>
    <if test="endDate != null">
      AND PRECONTRACT_DATE &lt;= #{endDate}
    </if>
    <if test="precontractType != null">
      AND PRECONTRACT_TYPE = #{precontractType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    ORDER BY PRECONTRACT_DATE DESC
  </select>

  <select id="getMbPrecontractNoticeByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/> from RB_PRECONTRACT
    <where>
    <if test="applyBranchIdList != null">
      AND TRAN_BRANCH IN ${applyBranchIdList}
    </if>
    <if test="precontractStatus != null">
      AND PRECONTRACT_STATUS = #{precontractStatus}
    </if>
    <if test="startDate != null">
      AND PRECONTRACT_DATE &gt;= #{startDate}
    </if>
    <if test="endDate != null">
      AND PRECONTRACT_DATE &lt;= #{endDate}
    </if>
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    ORDER BY PRECONTRACT_DATE DESC
  </select>

  <select id="getMbPrecontractByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    <where>
    <if test="reference != null">
      AND REFERENCE = #{reference}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="getRbPreContracts" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey, jdbcType=DECIMAL} and CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and PRECONTRACT_STATUS = 'A' ORDER BY PRECONTRACT_NO+0
  </select>

  <select id="getRbPreContractsByPreContractNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where INTERNAL_KEY = #{internalKey, jdbcType=DECIMAL} and CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    <if test="precontractNo != null and precontractNo != ''">
      AND PRECONTRACT_NO = #{precontractNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

    and PRECONTRACT_STATUS = 'A' order by TRAN_TIMESTAMP DESC
  </select>

  <select id="getRbPreContractsByStatus" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPrecontract">
    select <include refid="Base_Column"/>
    from RB_PRECONTRACT
    where 1=1
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="precontractNo != null">
      AND PRECONTRACT_NO = #{precontractNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="precontractDrawDate != null">
      AND PRECONTRACT_DRAW_DATE = #{precontractDrawDate,jdbcType=DATE}
    </if>
    <if test="precontractType != null and  precontractType != '' ">
      AND PRECONTRACT_TYPE = #{precontractType}
    </if>
    and (PRECONTRACT_STATUS = 'A' or PRECONTRACT_STATUS = 'B')
  </select>

</mapper>
