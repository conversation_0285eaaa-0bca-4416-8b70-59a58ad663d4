<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalCreateAuto">
    <select id="selectInternalCreateAutoAll"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalCreateAuto">
        SELECT <include refid="Base_Column"/>
        from  RB_INTERNAL_CREATE_AUTO
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
