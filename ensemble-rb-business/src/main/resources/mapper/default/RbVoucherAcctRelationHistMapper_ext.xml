<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist">

    <!-- Created by <PERSON>hangjhaj on 2018/12/05 15:47:26. -->
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist">
        insert into RB_VOUCHER_ACCT_RELATION_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="docType != null">
                DOC_TYPE,
            </if>
            <if test="prefix != null">
                PREFIX,
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO,
            </if>
            <if test="voucherStatus != null">
                VOUCHER_STATUS,
            </if>
            <if test="oldStatus != null">
                OLD_STATUS,
            </if>
            <if test="cardNo != null">
                CARD_NO,
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="acctCcy != null">
                ACCT_CCY,
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO,
            </if>
            <if test="collatInd != null">
                COLLAT_IND,
            </if>
            <if test="collatNo != null">
                COLLAT_NO,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="canReasonCode != null">
                CAN_REASON_CODE,
            </if>
            <if test="tranDate != null  ">
                TRAN_DATE = #{tranDate}  AND
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="docType != null">
                #{docType},
            </if>
            <if test="prefix != null">
                #{prefix},
            </if>
            <if test="voucherNo != null">
                #{voucherNo},
            </if>
            <if test="voucherStatus != null">
                #{voucherStatus},
            </if>
            <if test="oldStatus != null">
                #{oldStatus},
            </if>
            <if test="cardNo != null">
                #{cardNo},
            </if>
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="acctCcy != null">
                #{acctCcy},
            </if>
            <if test="acctSeqNo != null">
                #{acctSeqNo},
            </if>
            <if test="collatInd != null">
                #{collatInd},
            </if>
            <if test="collatNo != null">
                #{collatNo},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="canReasonCode != null">
                #{canReasonCode},
            </if>
            <if test="openBranch != null">
                #{openBranch},
            </if>
            <if test="tranDate != null  ">
                #{tranDate},
            </if>
        </trim>
    </insert>
    <select id="getMbVoucherAcctRelationByVoucher" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION_HIST
        WHERE 1=1
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <if test="voucherStatus != null and voucherStatus !=''">
            and VOUCHER_STATUS = #{voucherStatus}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbVoucherAcctRelationHistByBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION_HIST
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbVoucherAcctRelationHistByCardNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION_HIST
        WHERE CARD_NO = #{cardNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getInfoByAcctVouchStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION_HIST
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="seqNo != null and seqNo !=''">
            and ACCT_SEQ_NO = #{seqNo}
        </if>
        <if test="tranDate != null and tranDate !=''">
            and TRAN_DATE = #{tranDate,jdbcType=DATE}
        </if>
        <if test="voucherStatus != null and voucherStatus !=''">
            and VOUCHER_STATUS = #{voucherStatus}
        </if>
        <if test="oldStatus != null and oldStatus !=''">
            and OLD_STATUS = #{oldStatus}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbVoucherAcctRelationByAcctNoVouch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION_HIST
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="isCard =='true'">
            and CARD_NO IS NOT NULL
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
