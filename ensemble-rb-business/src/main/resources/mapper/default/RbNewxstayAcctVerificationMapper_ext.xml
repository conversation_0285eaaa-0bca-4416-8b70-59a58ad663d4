<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbNewxstayAcctVerification">
	<update id="updateByBaseAcctNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbNewxstayAcctVerification">
		update RB_NEWXSTAY_ACCT_VERIFICATION
		<set>
			<if test="acctVerification != null and  acctVerification != '' ">
				ACCT_VERIFICATION = #{acctVerification},
			</if>
			<if test="verificationDate != null ">
				VERIFICATION_DATE = #{verificationDate},
			</if>
			<if test="verificationUserId != null and  verificationUserId != '' ">
				VERIFICATION_USER_ID = #{verificationUserId},
			</if>
			<if test="tranBranch != null and  tranBranch != '' ">
				TRAN_BRANCH = #{tranBranch},
			</if>
			<if test="tranTimestamp != null ">
				TRAN_TIMESTAMP = #{tranTimestamp}
			</if>
		</set>
		where BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
		<if test="clientNo != null and  clientNo != '' ">
			AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
</mapper>
