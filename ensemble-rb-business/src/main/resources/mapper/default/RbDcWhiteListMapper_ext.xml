<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteList">
  <!-- Created by guowj1 on 20200331 10:40:36. -->
  <select id="queryDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteList">
    select
    <include refid="Base_Column"/>
    from RB_DC_WHITE_LIST
    where
    <if test="stageCode != null">
      STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="tranFileResult != null and  tranFileResult != ''">
      AND TRAN_FILE_RESULT = #{tranFileResult}
    </if>
    <if test="batchStatus != null">
      AND BATCH_STATUS = #{batchStatus}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY STAGE_CODE, CLIENT_NO, SEQ_NO ASC
  </select>

  <!--<insert id="insert" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteList">
    insert into RB_DC_WHITE_LIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchNo != null and  batchNo != ''">
        BATCH_NO,
      </if>
      <if test="chClientName != null and  chClientName != ''">
        CH_CLIENT_NAME,
      </if>
      <if test="prodType != null and  prodType != ''">
        PROD_TYPE,
      </if>
      <if test="stageCode != null and  stageCode != ''">
        STAGE_CODE,
      </if>
      <if test="documentType != null and  documentType != ''">
        DOCUMENT_TYPE,
      </if>
      <if test="documentId != null and  documentId != ''">
        DOCUMENT_ID,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranFileResult != null and  tranFileResult != ''">
        TRAN_FILE_RESULT,
      </if>
      <if test="batchStatus != null and  batchStatus != ''">
        BATCH_STATUS,
      </if>
      <if test="errorDesc != null and  errorDesc != ''">
        ERROR_DESC,
      </if>
      <if test="jobRunId != null ">
        JOB_RUN_ID,
      </if>
      <if test="errorCode != null and  errorCode != ''">
        ERROR_CODE ,
      </if>
      <if test="retMsg != null and  retMsg != ''">
        RET_MSG,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchNo != null and  batchNo != ''">
        #{batchNo},
      </if>
      <if test="chClientName != null and  chClientName != ''">
        #{chClientName},
      </if>
      <if test="prodType != null and  prodType != ''">
        #{prodType},
      </if>
      <if test="stageCode != null and  stageCode != ''">
        #{stageCode},
      </if>
      <if test="documentType != null and  documentType != ''">
        #{documentType},
      </if>
      <if test="documentId != null and  documentId != ''">
        #{documentId},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranFileResult != null and  tranFileResult != ''">
        #{tranFileResult},
      </if>
      <if test="batchStatus != null and  batchStatus != ''">
        #{batchStatus},
      </if>
      <if test="errorDesc != null and  errorDesc != ''">
        #{errorDesc},
      </if>
      <if test="jobRunId != null">
        #{jobRunId},
      </if>
      <if test="errorCode != null and  errorCode != ''">
        #{errorCode},
      </if>
      <if test="retMsg != null and  retMsg != ''">
        #{retMsg},
      </if>

    </trim>
  </insert>-->

  <select id="getRbDcWhiteListByStageCode" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteList">
    select
    <include refid="Base_Column"/>
    from RB_DC_WHITE_LIST
    where 1=1
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getRbDcWhiteListNumByStageCode" parameterType="java.util.Map" resultType="java.lang.Integer">
    select COUNT(*)
    from RB_DC_WHITE_LIST
    where 1=1
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="deleteRbDcWhiteList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteList">
    DELETE FROM RB_DC_WHITE_LIST
    where
    <if test="prodType != null">
      PROD_TYPE = #{prodType}
    </if>
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="tranFileResult != null and  tranFileResult != ''">
      AND TRAN_FILE_RESULT = #{tranFileResult}
    </if>
    <if test="batchStatus != null">
      AND BATCH_STATUS = #{batchStatus}
    </if>
    <if test="chClientName != null">
      AND CH_CLIENT_NAME = #{chClientName}
    </if>
    <if test="documentType != null">
      AND DOCUMENT_TYPE = #{documentType}
    </if>
    <if test="documentId != null">
      AND DOCUMENT_ID = #{documentId}
    </if>
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
    <if test="batchNo != null">
      AND BATCH_NO = #{batchNo}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>



  <select id="updateRbDcWhiteList">
    UPDATE
    <include refid="Table_Name"/>
    <include refid="Base_Set"/>
    where
    <if test="stageCode != null">
      STAGE_CODE = #{stageCode}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

</mapper>

