<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    delete from RB_OPEN_CLOSE_REG
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    <set>
      <if test="acctSeqNo != null and  acctSeqNo != '' ">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctType != null and  acctType != '' ">
        ACCT_TYPE = #{acctType},
      </if>
      <if test="ACCT_CCY != null and  ACCT_CCY != '' ">
        ACCT_CCY = #{ACCT_CCY},
      </if>
      <if test="tranDate != null ">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="reference != null and  reference != '' ">
        REFERENCE = #{reference},
      </if>
      <if test="TRAN_BRANCH != null and  TRAN_BRANCH != '' ">
        TRAN_BRANCH = #{TRAN_BRANCH},
      </if>
      <if test="company != null and  company != '' ">
        COMPANY = #{company},
      </if>
      <if test="isInformBank != null and  isInformBank != '' ">
        IS_INFORM_BANK_FLAG = #{isInformBank},
      </if>
      <if test="internalKey != null ">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="acctStatus != null and  acctStatus != '' ">
        ACCT_STATUS = #{acctStatus},
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="cardNo != null and  cardNo != '' ">
        CARD_NO = #{cardNo},
      </if>
      <if test="prodType != null and  prodType != '' ">
        PROD_TYPE = #{prodType},
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID = #{userId},
      </if>
      <if test="narrative != null and  narrative != '' ">
        NARRATIVE = #{narrative},
      </if>
      <if test="acctNature != null and  acctNature != '' ">
        ACCT_NATURE = #{acctNature},
      </if>
      <if test="regType != null and  regType != '' ">
        REG_TYPE = #{regType},
      </if>
      <if test="opMethod != null and  opMethod != '' ">
        OP_METHOD = #{opMethod},
      </if>
      <if test="sucFlag != null and  sucFlag != '' ">
        SUC_FLAG = #{sucFlag},
      </if>
      <if test="documentId != null and  documentId != '' ">
        DOCUMENT_ID = #{documentId},
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    insert into RB_OPEN_CLOSE_REG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="acctSeqNo != null and  acctSeqNo != '' ">
        ACCT_SEQ_NO,
      </if>
      <if test="acctType != null and  acctType != '' ">
        ACCT_TYPE,
      </if>
      <if test="ACCT_CCY != null and  ACCT_CCY != '' ">
        ACCT_CCY,
      </if>
      <if test="tranDate != null ">
        TRAN_DATE,
      </if>
      <if test="reference != null and  reference != '' ">
        REFERENCE,
      </if>
      <if test="TRAN_BRANCH != null and  TRAN_BRANCH != '' ">
        TRAN_BRANCH,
      </if>
      <if test="company != null and  company != '' ">
        COMPANY,
      </if>
      <if test="isInformBank != null and  isInformBank != '' ">
        IS_INFORM_BANK_FLAG,
      </if>
      <if test="internalKey != null ">
        INTERNAL_KEY,
      </if>
      <if test="acctStatus != null and  acctStatus != '' ">
        ACCT_STATUS,
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        BASE_ACCT_NO,
      </if>
      <if test="cardNo != null and  cardNo != '' ">
        CARD_NO,
      </if>
      <if test="prodType != null and  prodType != '' ">
        PROD_TYPE,
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID,
      </if>
      <if test="narrative != null and  narrative != '' ">
        NARRATIVE,
      </if>
      <if test="acctNature != null and  acctNature != '' ">
        ACCT_NATURE,
      </if>
      <if test="seqNo != null and  seqNo != '' ">
        SEQ_NO,
      </if>
      <if test="regType != null and  regType != '' ">
        REG_TYPE,
      </if>
      <if test="opMethod != null and  opMethod != '' ">
        OP_METHOD,
      </if>
      <if test="clientNo != null and  clientNo != '' ">
        CLIENT_NO,
      </if>
      <if test="sucFlag != null and  sucFlag != '' ">
        SUC_FLAG,
      </if>
      <if test="documentId != null and  documentId != '' ">
        DOCUMENT_ID,
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="acctSeqNo != null and  acctSeqNo != '' ">
        #{acctSeqNo},
      </if>
      <if test="acctType != null and  acctType != '' ">
        #{acctType},
      </if>
      <if test="ACCT_CCY != null and  ACCT_CCY != '' ">
        #{ACCT_CCY},
      </if>
      <if test="tranDate != null ">
        #{tranDate},
      </if>
      <if test="reference != null and  reference != '' ">
        #{reference},
      </if>
      <if test="TRAN_BRANCH != null and  TRAN_BRANCH != '' ">
        #{TRAN_BRANCH},
      </if>
      <if test="company != null and  company != '' ">
        #{company},
      </if>
      <if test="isInformBank != null and  isInformBank != '' ">
        #{isInformBank},
      </if>
      <if test="internalKey != null ">
        #{internalKey},
      </if>
      <if test="acctStatus != null and  acctStatus != '' ">
        #{acctStatus},
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        #{baseAcctNo},
      </if>
      <if test="prodType != null and  prodType != '' ">
        #{prodType},
      </if>
      <if test="userId != null and  userId != '' ">
        #{userId},
      </if>
      <if test="narrative != null and  narrative != '' ">
        #{narrative},
      </if>
      <if test="acctNature != null and  acctNature != '' ">
        #{acctNature},
      </if>
      <if test="seqNo != null and  seqNo != '' ">
        #{seqNo},
      </if>
      <if test="regType != null and  regType != '' ">
        #{regType},
      </if>
      <if test="opMethod != null and  opMethod != '' ">
        #{opMethod},
      </if>
      <if test="clientNo != null and  clientNo != '' ">
        #{clientNo},
      </if>
      <if test="sucFlag != null and  sucFlag != '' ">
        #{sucFlag},
      </if>
      <if test="documentId != null and  documentId != '' ">
        #{documentId},
      </if>
      <if test="tranTimestamp != null ">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
  <select id="selectForReversal" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where      1=1
    <if test="baseAcctNo != null">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="cardNo != null">
      and CARD_NO = #{cardNo}
    </if>
    <if test="prodType != null">
      and   PROD_TYPE = #{prodType}
    </if>
    <if test="ACCT_CCY != null">
      and  ACCT_CCY = #{ACCT_CCY}
    </if>
    <if test="acctSeqNo != null">
      and  ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="acctNature != null">
      and ACCT_NATURE = #{acctNature}
    </if>
    <if test="acctType != null">
      and  ACCT_TYPE = #{acctType}
    </if>
    <if test="internalKey != null">
      and  INTERNAL_KEY = #{internalKey}
    </if>
    <if test="regType != null">
      and  REG_TYPE = #{regType}
    </if>
    <if test="opMethod != null">
      and  OP_METHOD = #{opMethod}
    </if>
    <if test="TRAN_BRANCH != null">
      and TRAN_BRANCH = #{TRAN_BRANCH}
    </if>
    <if test="tranDate != null">
      and TRAN_DATE = #{tranDate,jdbcType=DATE}
    </if>
    <if test="userId != null">
      and   USER_ID = #{userId}
    </if>
    <if test="reference != null">
      and  REFERENCE = #{reference}
    </if>
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="company != null">
      and  COMPANY = #{company}
    </if>
    <if test="narrative != null">
      and NARRATIVE = #{narrative}
    </if>
    <if test="acctStatus != null">
      and ACCT_STATUS = #{acctStatus}
    </if>
    <if test="sucFlag != null">
      and SUC_FLAG = #{sucFlag}
    </if>
    <if test="isInformBank != null">
      and IS_INFORM_BANK_FLAG = #{isInformBank}
    </if>
    <if test="documentId != null">
      and DOCUMENT_ID = #{documentId}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getPreOpenCloseAcctListSQL" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where      1=1
    <if test="baseAcctNo != null and  baseAcctNo != ''">
      and (BASE_ACCT_NO = #{baseAcctNo}
      OR CARD_NO = #{baseAcctNo})
    </if>
    <if test="cardNo != null and  cardNo != ''">
      and CARD_NO = #{cardNo}
    </if>
    <if test="prodType != null and  prodType != ''">
      and   PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and  ccy != ''">
      and  ACCT_CCY = #{ccy}
    </if>
    <if test="acctSeqNo != null and  acctSeqNo != ''">
      and  ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="acctNature != null and  acctNature != ''">
      and ACCT_NATURE = #{acctNature}
    </if>
    <if test="acctType != null and  acctType != ''">
      and  ACCT_TYPE = #{acctType}
    </if>

    <if test="regType != null and  regType != ''">
      and  REG_TYPE = #{regType}
    </if>
    <if test="TRAN_BRANCH != null and  TRAN_BRANCH != ''">
      and TRAN_BRANCH = #{TRAN_BRANCH}
    </if>
    <if test="startDate != null">
      AND ( TRAN_DATE between #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE} )
    </if>
    <if test="userId != null and  userId != ''">
      and   USER_ID = #{userId}
    </if>
    <if test="reference != null and  reference != ''">
      and  REFERENCE = #{reference}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="company != null and  company != ''">
      and  COMPANY = #{company}
    </if>
    <if test="narrative != null and  narrative != ''">
      and NARRATIVE = #{narrative}
    </if>
    <if test="acctStatus != null and  acctStatus != '' ">
      and ACCT_STATUS = #{acctStatus}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by  TRAN_DATE
  </select>

  <select id="getOpenCloseAcctListSQL" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where      1=1
    <if test="baseAcctNo != null and  baseAcctNo != ''">
      and (BASE_ACCT_NO = #{baseAcctNo}
      OR CARD_NO = #{baseAcctNo})
    </if>
    <if test="prodType != null and  prodType != ''">
      and   PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and  ccy != ''">
      and  ACCT_CCY = #{ccy}
    </if>
    <if test="acctSeqNo != null and  acctSeqNo != ''">
      and  ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="acctNature != null and  acctNature != ''">
      and ACCT_NATURE = #{acctNature}
    </if>
    <if test="acctType != null and  acctType != ''">
      and  ACCT_TYPE = #{acctType}
    </if>

    <if test="regType != null and  regType != ''">
      and  REG_TYPE = #{regType}
    </if>
    <if test="TRAN_BRANCH != null and  TRAN_BRANCH != ''">
      and TRAN_BRANCH = #{TRAN_BRANCH}
    </if>
    <if test="startDate != null">
      AND ( TRAN_DATE between #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE} )
    </if>
    <if test="userId != null and  userId != ''">
      and   USER_ID = #{userId}
    </if>
    <if test="reference != null and  reference != ''">
      and  REFERENCE = #{reference}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="company != null and  company != ''">
      and  COMPANY = #{company}
    </if>
    <if test="narrative != null and  narrative != ''">
      and NARRATIVE = #{narrative}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and (ACCT_STATUS !='I' OR ACCT_STATUS IS NULL)
    order by  TRAN_DATE,BASE_ACCT_NO
  </select>

  <select id="getOpenCloseAcctListByGLSQL" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select A.*
    from RB_OPEN_CLOSE_REG A
    LEFT JOIN RB_ACCT B ON A.INTERNAL_KEY = B.INTERNAL_KEY
    where      A.CLIENT_NO = B.CLIENT_NO
    <if test="baseAcctNo != null and  baseAcctNo != ''">
      and (A.BASE_ACCT_NO = #{baseAcctNo}
      OR A.CARD_NO = #{baseAcctNo})
    </if>
    <if test="prodType != null and  prodType != ''">
      and   A.PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and  ccy != ''">
      and  A.ACCT_CCY = #{ccy}
    </if>
    <if test="acctSeqNo != null and  acctSeqNo != ''">
      and  A.ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="acctNature != null and  acctNature != ''">
      and A.ACCT_NATURE = #{acctNature}
    </if>
    <if test="acctType != null and  acctType != ''">
      and  A.ACCT_TYPE = #{acctType}
    </if>

    <if test="regType != null and  regType != ''">
      and  A.REG_TYPE = #{regType}
    </if>
    <if test="TRAN_BRANCH != null and  TRAN_BRANCH != ''">
      and A.TRAN_BRANCH = #{TRAN_BRANCH}
    </if>
    <if test="startDate != null">
      AND ( A.TRAN_DATE between #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE} )
    </if>
    <if test="userId != null and  userId != ''">
      and   A.USER_ID = #{userId}
    </if>
    <if test="reference != null and  reference != ''">
      and  A.REFERENCE = #{reference}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and A.CLIENT_NO = #{clientNo}
    </if>
    <if test="company != null and  company != ''">
      and  A.COMPANY = #{company}
    </if>
    <if test="narrative != null and  narrative != ''">
      and A.NARRATIVE = #{narrative}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND A.COMPANY = #{company}
    </if>
    and (A.ACCT_STATUS !='I' OR A.ACCT_STATUS IS NULL)
    and B.SOURCE_MODULE = 'GL'
    order by  A.TRAN_DATE,A.BASE_ACCT_NO
  </select>

  <select id="getAcctOpenCloseSendBankList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where  SUC_FLAG = 'Y'  and ACCT_NATURE = '0005' and ACCT_STATUS in('A','C','N','I','H')
    <if test="tranDate != null">
      AND  TRAN_DATE= #{tranDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--  by zhuxyw -->
  <select id="getOpenCloseAcctRegByAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where  REG_TYPE='2' and client_no = #{clientNo} and BASE_ACCT_NO = #{baseAcctNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <update id="updateAcctOpenCloseSendBankList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    set IS_INFORM_BANK_FLAG = #{isInformBank}
    where  1=1
    and seq_no = #{seqNo}
    and client_no = #{clientNo}
    and IS_INFORM_BANK_FLAG = 'N'
  </update>

  <select id="getOpenCloseAcctReg" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where INTERNAL_KEY = #{internalKey}
    and CLIENT_NO = #{clientNo}
    and REG_TYPE = #{regType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <update id="updateCardForChangeVoucher" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    <set>
      <if test="tranDate != null ">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="reference != null and  reference != '' ">
        REFERENCE = #{reference},
      </if>
      <if test="TRAN_BRANCH != null and  TRAN_BRANCH != '' ">
        TRAN_BRANCH = #{TRAN_BRANCH},
      </if>
      <if test="cardNo != null and  cardNo != '' ">
        CARD_NO = #{cardNo},
      </if>
      <if test="newProdType != null and  newProdType != '' ">
        PROD_TYPE = #{newProdType},
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID = #{userId},
      </if>
      <if test="narrative != null and  narrative != '' ">
        NARRATIVE = #{narrative},
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
    </set>
    where  1=1
    <if test="internalKey != null and  internalKey != ''">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <if test="baseAcctNo != null and  baseAcctNo != ''">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="acctSeqNo != null and  acctSeqNo != ''">
      and  ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateBaseAcctNoForChangeVoucher" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    <set>
      <if test="tranDate != null ">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="reference != null and  reference != '' ">
        REFERENCE = #{reference},
      </if>
      <if test="cardNo != null and  cardNo != '' ">
        CARD_NO = #{cardNo},
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID = #{userId},
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
    </set>
    where  1=1
    <if test="internalKey != null and  internalKey != ''">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <if test="baseAcctNo != null and  baseAcctNo != ''">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null and  prodType != ''">
      and PROD_TYPE = #{prodType}
    </if>
    <if test="acctSeqNo != null and  acctSeqNo != ''">
      and ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updateRegBySeq1" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    <set>
      <if test="acctBranch != null and  acctBranch != '' ">
        ACCT_BRANCH = #{acctBranch},
      </if>
      <if test="openBranch != null and  openBranch != '' ">
        OPEN_BRANCH = #{openBranch},
      </if>
      <if test="tranBranch != null and  tranBranch != '' ">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="regType!= null and regType !='' ">
        REG_TYPE = #{regType},
      </if>

    </set>
    where  REG_TYPE = '1'
    <if test="internalKey != null and  internalKey != ''">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <if test="baseAcctNo != null and  baseAcctNo != ''">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null and  prodType != ''">
      and PROD_TYPE = #{prodType}
    </if>
    <if test="acctSeqNo != null and  acctSeqNo != ''">
      and ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
  </update>

  <update id="updateActiveDateByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    <set>
      <if test="activeDate != null ">
        ACTIVE_DATE = #{activeDate},
      </if>
    </set>
    where  1=1
    <if test="internalKey != null and  internalKey != ''">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateAcctStatusByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    <set>
      <if test="acctStatus != null ">
        ACCT_STATUS = #{acctStatus},
      </if>
    </set>
    where  1=1
    <if test="internalKey != null and  internalKey != ''">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="regType != null and  regType != ''">
      and REG_TYPE = #{regType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="getCloseAcctListSQL" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where      1=1
    AND REG_TYPE = '2'
    <if test="baseAcctNo != null and  baseAcctNo != ''">
      and (BASE_ACCT_NO = #{baseAcctNo}
      OR CARD_NO = #{baseAcctNo})
    </if>
    <if test="prodType != null and  prodType != ''">
      and   PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and  ccy != ''">
      and  ACCT_CCY = #{ccy}
    </if>
    <if test="acctSeqNo != null and  acctSeqNo != ''">
      and  ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="acctNature != null and  acctNature != ''">
      and ACCT_NATURE = #{acctNature}
    </if>
    <if test="acctType != null and  acctType != ''">
      and  ACCT_TYPE = #{acctType}
    </if>
    <if test="openBranch != null and  openBranch != ''">
      and OPEN_BRANCH = #{openBranch}
    </if>
    <if test="startDate != null">
      AND ( TRAN_DATE between #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE} )
    </if>
    <if test="userId != null and  userId != ''">
      and   USER_ID = #{userId}
    </if>
    <if test="reference != null and  reference != ''">
      and  REFERENCE = #{reference}
    </if>
    <if test="clientNo != null and  clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="company != null and  company != ''">
      and  COMPANY = #{company}
    </if>
    <if test="narrative != null and  narrative != ''">
      and NARRATIVE = #{narrative}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and (ACCT_STATUS !='I' OR ACCT_STATUS IS NULL)
    order by  TRAN_DATE,BASE_ACCT_NO
  </select>

  <select id="getRbOpenCloseRegList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where INTERNAL_KEY = #{internalKey}
    and CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getOpenCloseAcctRegList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where INTERNAL_KEY = #{internalKey}
    and CLIENT_NO = #{clientNo}
    and REG_TYPE = #{regType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by tran_timestamp desc
  </select>
  <update id="updateByReferenceAndInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    update RB_OPEN_CLOSE_REG
    <set>
      <if test="narrative != null ">
        narrative = #{narrative},
      </if>
    </set>
    <where>
      <if test="reference != null">
        and reference = #{reference}
      </if>
      <if test="internalKey != null">
        and internal_key = #{internalKey}
      </if>
      <if test="clientNo != null and  clientNo != ''">
        and CLIENT_NO = #{clientNo}
      </if>
    </where>
  </update>

  <select id="getCloseDateReg" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
    select <include refid="Base_Column"/>
    from RB_OPEN_CLOSE_REG
    where INTERNAL_KEY = #{internalKey}
    and CLIENT_NO = #{clientNo}
    and REG_TYPE = #{regType}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if> ORDER BY TRAN_TIMESTAMP DESC
  </select>
</mapper>
