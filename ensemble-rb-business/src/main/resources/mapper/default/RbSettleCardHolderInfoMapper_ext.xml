<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardHolderInfo">
<!-- Primary and secondary card replacement sql -->
	<update id="updateByMainCardNoAndMainCardFlag" parameterType="java.util.Map">
		update RB_SETTLE_CARD_HOLDER_INFO
		<set>
				MAIN_CARD_NO=#{mainCardNo},
				MAIN_CARD_FLAG=#{mainCardFlag}
		</set>
		where  CARD_NO = #{cardNo}
		<if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO= #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<update id="mbSettleCardRenewByCardNo" parameterType="java.util.Map">
		update RB_SETTLE_CARD_HOLDER_INFO
		<set>
			<if test="newCardNo != null">
				CARD_NO=#{newCardNo},
			</if>
		</set>
		where  CARD_NO = #{cardNo}
		<if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO= #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<update id="mbSettleCardRenewMainByCardNo" parameterType="java.util.Map">
		update RB_SETTLE_CARD_HOLDER_INFO
		<set>
			<if test="newCardNo != null">
				CARD_NO=#{newCardNo},
			</if>
			<if test="newCardNo != null">
				MAIN_CARD_NO=#{newCardNo},
			</if>
		</set>
		where  CARD_NO = #{cardNo}
		<if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO= #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<update id="updateMainCard" parameterType="java.util.Map">
		update RB_SETTLE_CARD_HOLDER_INFO
		<set>
			<if test="newCardNo != null">
				MAIN_CARD_NO=#{newCardNo},
			</if>
		</set>
		where  MAIN_CARD_NO = #{cardNo}
		<if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO= #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<select id="getCardHolderInfoLimitBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardHolderInfo">
		select <include refid="Base_Column"/>
		from RB_SETTLE_CARD_HOLDER_INFO
		<where>
		<if test="documentId != null and documentId != ''">
			AND document_id= #{documentId}
		</if>
		<if test="documentType != null and documentType != ''">
			AND document_type= #{documentType}
		</if>
		<if test="branchList != null and branchList.size() > 0">
			and tran_branch in
			<foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
				#{branch}
			</foreach>
		</if>
		</where>
	</select>
</mapper>
