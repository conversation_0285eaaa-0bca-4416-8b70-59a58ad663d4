<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrMergeHist">

    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrMergeHist">
        insert into
        RB_ACCR_MERGE_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                SEQ_NO,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="effectDate != null">
                EFFECT_DATE,
            </if>
            <if test="tranCcy != null">
                TRAN_CCY,
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE,
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE,
            </if>
            <if test="businessUnit != null">
                BUSINESS_UNIT,
            </if>
            <if test="amtType != null">
                AMT_TYPE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="acctBranch != null">
                ACCT_BRANCH,
            </if>
            <if test="accountingStatus != null">
                ACCOUNTING_STATUS,
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTER,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="clientType != null">
                CLIENT_TYPE,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="systemId != null">
                SYSTEM_ID,
            </if>
            <if test="reversal != null">
                REVERSAL,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="priAmt != null ">
                PRI_AMT,
            </if>
            <if test="intAmt != null ">
                INT_AMT,
            </if>
            <if test="odpAmt != null ">
                ODP_AMT,
            </if>
            <if test="odiAmt != null ">
                ODI_AMT,
            </if>
            <if test="tranProfitCenter != null">
                TRAN_PROFIT_CENTER,
            </if>
            <if test="eventType != null">
                EVENT_TYPE,
            </if>
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="tranDate != null ">
                TRAN_DATE,
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO,
            </if>
            <if test="glCode != null">
                GL_CODE,
            </if>
            <if test="channelDate != null ">
                CHANNEL_DATE,
            </if>
            <if test="taxAmt != null ">
                TAX_AMT,
            </if>
            <if test="crDrInd != null ">
                CR_DR_IND,
            </if>
            <if test="glPostedFlag != null">
                GL_POSTED_FLAG,
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP,
            </if>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO,
            </if>
            <if test="reversalSeqNo != null">
                REVERSAL_SEQ_NO,
            </if>
            <if test="reversalDate != null ">
                REVERSAL_DATE,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="inStatus != null">
                IN_STATUS,
            </if>
            <if test="settleBranch != null">
                SETTLE_BRANCH,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                #{seqNo},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="effectDate != null ">
                #{effectDate, jdbcType=DATE},
            </if>
            <if test="tranCcy != null ">
                #{tranCcy},
            </if>
            <if test="sourceModule != null">
                #{sourceModule},
            </if>
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="businessUnit != null">
                #{businessUnit},
            </if>
            <if test="amtType != null">
                #{amtType},
            </if>
            <if test="amount != null ">
                #{amount, jdbcType=DECIMAL},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="acctBranch != null">
                #{acctBranch},
            </if>
            <if test="accountingStatus != null">
                #{accountingStatus},
            </if>
            <if test="profitCenter != null">
                #{profitCenter},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="clientType != null">
                #{clientType},
            </if>
            <if test="clientNo != null ">
                #{clientNo},
            </if>
            <if test="systemId != null">
                #{systemId},
            </if>
            <if test="reversal != null">
                #{reversal},
            </if>
            <if test="narrative != null ">
                #{narrative},
            </if>
            <if test="priAmt != null ">
                #{priAmt},
            </if>
            <if test="intAmt != null ">
                #{intAmt},
            </if>
            <if test="odpAmt != null ">
                #{odpAmt},
            </if>
            <if test="odiAmt != null ">
                #{odiAmt},
            </if>
            <if test="tranProfitCenter != null ">
                #{tranProfitCenter},
            </if>
            <if test="eventType != null ">
                #{eventType},
            </if>
            <if test="tranType != null ">
                #{tranType},
            </if>
            <if test="tranDate != null ">
                #{tranDate, jdbcType=DATE},
            </if>
            <if test="bankSeqNo != null">
                #{bankSeqNo},
            </if>
            <if test="glCode != null ">
                #{glCode},
            </if>
            <if test="channelDate != null ">
                #{channelDate, jdbcType=DATE},
            </if>
            <if test="taxAmt != null ">
                #{taxAmt},
            </if>
            <if test="crDrInd != null ">
                #{crDrInd},
            </if>
            <if test="glPostedFlag != null ">
                #{glPostedFlag},
            </if>
            <if test="tranTimestamp != null ">
                #{tranTimestamp, jdbcType=TIMESTAMP},
            </if>
            <if test="channelSeqNo != null ">
                #{channelSeqNo},
            </if>
            <if test="reversalSeqNo != null ">
                #{reversalSeqNo},
            </if>
            <if test="reversalDate != null ">
                #{reversalDate, jdbcType=DATE},
            </if>
            <if test="company != null ">
                #{company},
            </if>
            <if test="inStatus != null ">
                #{inStatus},
            </if>
            <if test="settleBranch != null ">
                #{settleBranch},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrMergeHist">
        UPDATE RB_ACCR_MERGE_HIST
        <set>
            <if test="reference != null and  reference != '' ">
                REFERENCE = #{reference},
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE = #{effectDate},
            </if>
            <if test="tranCcy != null and  tranCcy != '' ">
                TRAN_CCY = #{tranCcy},
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                SOURCE_MODULE = #{sourceModule},
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                SOURCE_TYPE = #{sourceType},
            </if>
            <if test="businessUnit != null and  businessUnit != '' ">
                BUSINESS_UNIT = #{businessUnit},
            </if>
            <if test="amtType != null and  amtType != '' ">
                AMT_TYPE = #{amtType},
            </if>
            <if test="amount != null ">
                AMOUNT = #{amount},
            </if>
            <if test="prodType != null and  prodType != '' ">
                PROD_TYPE = #{prodType},
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo},
            </if>
            <if test="acctBranch != null and  acctBranch != '' ">
                ACCT_BRANCH = #{acctBranch},
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                ACCOUNTING_STATUS = #{accountingStatus},
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                PROFIT_CENTER = #{profitCenter},
            </if>
            <if test="ccy != null and  ccy != '' ">
                CCY = #{ccy},
            </if>
            <if test="clientType != null and  clientType != '' ">
                CLIENT_TYPE = #{clientType},
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                CLIENT_NO = #{clientNo},
            </if>
            <if test="systemId != null and  systemId != '' ">
                SYSTEM_ID = #{systemId},
            </if>
            <if test="reversal != null and  reversal != '' ">
                REVERSAL = #{reversal},
            </if>
            <if test="narrative != null and  narrative != '' ">
                NARRATIVE = #{narrative},
            </if>
            <if test="priAmt != null ">
                PRI_AMT = #{priAmt},
            </if>
            <if test="intAmt != null ">
                INT_AMT = #{intAmt},
            </if>
            <if test="odpAmt != null ">
                ODP_AMT = #{odpAmt},
            </if>
            <if test="odiAmt != null ">
                ODI_AMT = #{odiAmt},
            </if>
            <if test="tranProfitCenter != null and  tranProfitCenter != '' ">
                TRAN_PROFIT_CENTER = #{tranProfitCenter},
            </if>
            <if test="eventType != null and  eventType != '' ">
                EVENT_TYPE = #{eventType},
            </if>
            <if test="tranType != null and  tranType != '' ">
                TRAN_TYPE = #{tranType},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="bankSeqNo != null and  bankSeqNo != '' ">
                BANK_SEQ_NO = #{bankSeqNo},
            </if>
            <if test="glCode != null and  glCode != '' ">
                GL_CODE = #{glCode},
            </if>
            <if test="channelDate != null ">
                CHANNEL_DATE = #{channelDate},
            </if>
            <if test="taxAmt != null ">
                TAX_AMT = #{taxAmt},
            </if>
            <if test="crDrInd != null and  crDrInd != '' ">
                CR_DR_IND = #{crDrInd},
            </if>
            <if test="glPostedFlag != null and  glPostedFlag != '' ">
                GL_POSTED_FLAG = #{glPostedFlag},
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{channelSeqNo},
            </if>
            <if test="reversalSeqNo != null and  reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{reversalSeqNo},
            </if>
            <if test="reversalDate != null ">
                REVERSAL_DATE = #{reversalDate},
            </if>
            <if test="company != null and  company != '' ">
                COMPANY = #{company},
            </if>
            <if test="inStatus != null and  inStatus != '' ">
                IN_STATUS = #{inStatus},
            </if>
            <if test="settleBranch != null and  settleBranch != '' ">
                SETTLE_BRANCH = #{settleBranch},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="seqNo != null and  seqNo != '' ">
                    SEQ_NO = #{seqNo}  AND
                </if>
                <!-- 多法人改造 by LIYUANV -->
                <if test="company != null and company != '' ">
                    AND COMPANY = #{company}
                </if>
            </trim>
        </where>
    </update>

    <select id="sumAccrMergeInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrMergeHist" useCache="false">
        select
            TRAN_BRANCH,
            EFFECT_DATE,
            TRAN_CCY,
            SOURCE_MODULE,
            SOURCE_TYPE,
            BUSINESS_UNIT,
            AMT_TYPE,
            AMOUNT,
            PROD_TYPE,
            BASE_ACCT_NO,
            ACCT_BRANCH,
            ACCOUNTING_STATUS,
            PROFIT_CENTER,
            CCY,
            SYSTEM_ID,ExtBatchGlTranToRbGlHistStep
            REVERSAL,
            '计提合并'AS NARRATIVE,
            TRAN_PROFIT_CENTER,
            'ACR' AS EVENT_TYPE,
            TRAN_TYPE,
            TRAN_DATE,
            BANK_SEQ_NO,
            GL_CODE,
            CHANNEL_DATE,
            TAX_AMT,
            CR_DR_IND,
            GL_POSTED_FLAG,
            TRAN_TIMESTAMP,
            CHANNEL_SEQ_NO,
            REVERSAL_SEQ_NO,
            REVERSAL_DATE,
            COMPANY,
            IN_STATUS,
            SETTLE_BRANCH
        FROM RB_ACCR_MERGE_HIST
        WHERE TRAN_DATE BETWEEN #{lastRunDate} AND #{yesterday}
              AND ACCT_BRANCH =  #{acctBranch}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
      </select>

    <select id="extAccrMergeInfoStepSegment" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrMergeHist" useCache="false">
        select distinct acctBranch
        from RB_ACCR_MERGE_HIST
        where in_status !='S'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="extAccrMergeInfoStepSegmentInFo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrMergeHist" useCache="false">
        select <include refid="Base_Column"/>
        from RB_ACCR_MERGE_HIST
        where in_status !='S'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="truncateAccrMergeInfo">
        truncate table RB_ACCR_MERGE_HIST
    </update>


</mapper>
