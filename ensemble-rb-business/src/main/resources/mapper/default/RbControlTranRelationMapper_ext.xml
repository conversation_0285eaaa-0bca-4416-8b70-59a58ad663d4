<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbControlTranRelation">

  <select id="selectControlTranTypeEffecting" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbControlTranRelation">
    select <include refid="Base_Column"/>
    from Rb_CONTROL_TRAN_RELATION
    where EFFECT_FLAG = 'Y'
      <if test="controlClass != null and  controlClass != '' ">
        AND CONTROL_CLASS = #{controlClass,jdbcType=VARCHAR}
      </if>
      <if test="serviceNo != null and  serviceNo != '' ">
        AND SERVICE_NO = #{serviceNo,jdbcType=VARCHAR}
      </if>
      <if test="tranType != null and  tranType != '' ">
        AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
      </if>
      <if test="company != null and  company != '' ">
        AND COMPANY = #{company,jdbcType=VARCHAR}
      </if>
  </select>
</mapper>
