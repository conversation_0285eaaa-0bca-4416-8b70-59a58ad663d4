<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherSellInfo">

    <select id="getSellInfoByVoucher" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherSellInfo">
        select <include refid="Base_Column"/>
        from RB_VOUCHER_SELL_INFO
        where #{voucherNo} BETWEEN  VOUCHER_START_NO  AND  VOUCHER_END_NO
        <if test="docType != null and  docType != ''">
            AND DOC_TYPE = #{docType}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
