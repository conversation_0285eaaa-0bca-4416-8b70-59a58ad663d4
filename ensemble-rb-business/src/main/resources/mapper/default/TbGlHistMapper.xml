<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.TbGlHist">

    <sql id="Table_Name">
        TB_GL_HIST
    </sql>

    <sql id="Base_Column">
        <trim suffixOverrides=",">
            TRADE_NO,
            CHANNEL_SEQ_NO,
            REFERENCE,
            SEND_SYSTEM,
            SYSTEM_ID,
            TRAN_BRANCH,
            <PERSON>ANCH,
            CCY,
            SOURCE_MODULE,
            SOURCE_TYPE,
            TRAN_DATE,
            EFFECT_DATE,
            EVENT_TYPE,
            TRAN_TYPE,
            ACCOUNTING_STATUS,
            CLIENT_TYPE,
            AMT_TYPE,
            AMOUNT,
            BUSI_PROD,
            MARKETING_PROD,
            MARKETING_PROD_DESC,
            NARRATIVE,
            ACCT_INTERNAL_KEY,
            BASE_ACCT_NO,
            CLIENT_NO,
            ACCT_SEQ_NO,
            TERM,
            TERM_TYPE,
            PROFIT_CENTER,
            SUBJECT_CODE,
            GL_SEQ_NO,
            COMPANY,
            CHANNEL_DATE,
            FLAT_RATE,
            CUST_RATE,
            OTH_CCY,
            OTH_AMOUNT,
            USER_ID,
            REVERSAL,
            REVERSAL_DATE,
            REVERSAL_SEQ_NO,
            TAX_PERCENT,
            TAX_AMT,
            TRAN_CATEGORY,
            UN_REAL,
            SETTLE_MODE,
            TRAN_TIMESTAMP,
            BUS_SEQ_NO,
            GL_POSTED_FLAG,
            SUB_SEQ_NO,
        </trim>
    </sql>

    <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.model.entity.dbmodel.TbGlHist">
        <result property="tradeNo" column="TRADE_NO"/>
        <result property="channelSeqNo" column="CHANNEL_SEQ_NO"/>
        <result property="reference" column="REFERENCE"/>
        <result property="sendSystem" column="SEND_SYSTEM"/>
        <result property="systemId" column="SYSTEM_ID"/>
        <result property="tranBranch" column="TRAN_BRANCH"/>
        <result property="branch" column="BRANCH"/>
        <result property="ccy" column="CCY"/>
        <result property="sourceModule" column="SOURCE_MODULE"/>
        <result property="sourceType" column="SOURCE_TYPE"/>
        <result property="tranDate" column="TRAN_DATE"/>
        <result property="effectDate" column="EFFECT_DATE"/>
        <result property="eventType" column="EVENT_TYPE"/>
        <result property="tranType" column="TRAN_TYPE"/>
        <result property="accountingStatus" column="ACCOUNTING_STATUS"/>
        <result property="clientType" column="CLIENT_TYPE"/>
        <result property="amtType" column="AMT_TYPE"/>
        <result property="amount" column="AMOUNT"/>
        <result property="busiProd" column="BUSI_PROD"/>
        <result property="marketingProd" column="MARKETING_PROD"/>
        <result property="marketingProdDesc" column="MARKETING_PROD_DESC"/>
        <result property="narrative" column="NARRATIVE"/>
        <result property="acctInternalKey" column="ACCT_INTERNAL_KEY"/>
        <result property="baseAcctNo" column="BASE_ACCT_NO"/>
        <result property="clientNo" column="CLIENT_NO"/>
        <result property="acctSeqNo" column="ACCT_SEQ_NO"/>
        <result property="term" column="TERM"/>
        <result property="termType" column="TERM_TYPE"/>
        <result property="profitCenter" column="PROFIT_CENTER"/>
        <result property="subjectCode" column="SUBJECT_CODE"/>
        <result property="glSeqNo" column="GL_SEQ_NO"/>
        <result property="company" column="COMPANY"/>
        <result property="channelDate" column="CHANNEL_DATE"/>
        <result property="flatRate" column="FLAT_RATE"/>
        <result property="custRate" column="CUST_RATE"/>
        <result property="othCcy" column="OTH_CCY"/>
        <result property="othAmount" column="OTH_AMOUNT"/>
        <result property="userId" column="USER_ID"/>
        <result property="reversal" column="REVERSAL"/>
        <result property="reversalDate" column="REVERSAL_DATE"/>
        <result property="reversalSeqNo" column="REVERSAL_SEQ_NO"/>
        <result property="taxPercent" column="TAX_PERCENT"/>
        <result property="taxAmt" column="TAX_AMT"/>
        <result property="tranCategory" column="TRAN_CATEGORY"/>
        <result property="unReal" column="UN_REAL"/>
        <result property="settleMode" column="SETTLE_MODE"/>
        <result property="tranTimestamp" column="TRAN_TIMESTAMP"/>
        <result property="busSeqNo" column="BUS_SEQ_NO"/>
        <result property="glPostedFlag" column="GL_POSTED_FLAG"/>
        <result property="subSeqNo" column="SUB_SEQ_NO"/>
    </resultMap>

    <sql id="Base_Where">
        <where>
            <if test="tradeNo != null and  tradeNo != '' ">
                AND TRADE_NO = #{tradeNo,jdbcType=VARCHAR}
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                AND CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="reference != null and  reference != '' ">
                AND REFERENCE = #{reference,jdbcType=VARCHAR}
            </if>
            <if test="sendSystem != null and  sendSystem != '' ">
                AND SEND_SYSTEM = #{sendSystem,jdbcType=VARCHAR}
            </if>
            <if test="systemId != null and  systemId != '' ">
                AND SYSTEM_ID = #{systemId,jdbcType=VARCHAR}
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="branch != null and  branch != '' ">
                AND BRANCH = #{branch,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                AND SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                AND SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
            </if>
            <if test="tranDate != null ">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE}
            </if>
            <if test="effectDate != null ">
                AND EFFECT_DATE = #{effectDate,jdbcType=DATE}
            </if>
            <if test="eventType != null and  eventType != '' ">
                AND EVENT_TYPE = #{eventType,jdbcType=VARCHAR}
            </if>
            <if test="tranType != null and  tranType != '' ">
                AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                AND ACCOUNTING_STATUS = #{accountingStatus,jdbcType=VARCHAR}
            </if>
            <if test="clientType != null and  clientType != '' ">
                AND CLIENT_TYPE = #{clientType,jdbcType=VARCHAR}
            </if>
            <if test="amtType != null and  amtType != '' ">
                AND AMT_TYPE = #{amtType,jdbcType=VARCHAR}
            </if>
            <if test="amount != null ">
                AND AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="busiProd != null and  busiProd != '' ">
                AND BUSI_PROD = #{busiProd,jdbcType=VARCHAR}
            </if>
            <if test="marketingProd != null and  marketingProd != '' ">
                AND MARKETING_PROD = #{marketingProd,jdbcType=VARCHAR}
            </if>
            <if test="marketingProdDesc != null and  marketingProdDesc != '' ">
                AND MARKETING_PROD_DESC = #{marketingProdDesc,jdbcType=VARCHAR}
            </if>
            <if test="narrative != null and  narrative != '' ">
                AND NARRATIVE = #{narrative,jdbcType=VARCHAR}
            </if>
            <if test="acctInternalKey != null ">
                AND ACCT_INTERNAL_KEY = #{acctInternalKey,jdbcType=BIGINT}
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="acctSeqNo != null and  acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="term != null and  term != '' ">
                AND TERM = #{term,jdbcType=VARCHAR}
            </if>
            <if test="termType != null and  termType != '' ">
                AND TERM_TYPE = #{termType,jdbcType=VARCHAR}
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                AND PROFIT_CENTER = #{profitCenter,jdbcType=VARCHAR}
            </if>
            <if test="subjectCode != null and  subjectCode != '' ">
                AND SUBJECT_CODE = #{subjectCode,jdbcType=VARCHAR}
            </if>
            <if test="glSeqNo != null and  glSeqNo != '' ">
                AND GL_SEQ_NO = #{glSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="company != null and  company != '' ">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
            <if test="channelDate != null ">
                AND CHANNEL_DATE = #{channelDate,jdbcType=DATE}
            </if>
            <if test="flatRate != null ">
                AND FLAT_RATE = #{flatRate,jdbcType=DECIMAL}
            </if>
            <if test="custRate != null ">
                AND CUST_RATE = #{custRate,jdbcType=DECIMAL}
            </if>
            <if test="othCcy != null and  othCcy != '' ">
                AND OTH_CCY = #{othCcy,jdbcType=VARCHAR}
            </if>
            <if test="othAmount != null ">
                AND OTH_AMOUNT = #{othAmount,jdbcType=DECIMAL}
            </if>
            <if test="userId != null and  userId != '' ">
                AND USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="reversal != null and  reversal != '' ">
                AND REVERSAL = #{reversal,jdbcType=VARCHAR}
            </if>
            <if test="reversalDate != null ">
                AND REVERSAL_DATE = #{reversalDate,jdbcType=DATE}
            </if>
            <if test="reversalSeqNo != null and  reversalSeqNo != '' ">
                AND REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="taxPercent != null ">
                AND TAX_PERCENT = #{taxPercent,jdbcType=DECIMAL}
            </if>
            <if test="taxAmt != null ">
                AND TAX_AMT = #{taxAmt,jdbcType=DECIMAL}
            </if>
            <if test="tranCategory != null and  tranCategory != '' ">
                AND TRAN_CATEGORY = #{tranCategory,jdbcType=VARCHAR}
            </if>
            <if test="unReal != null and  unReal != '' ">
                AND UN_REAL = #{unReal,jdbcType=VARCHAR}
            </if>
            <if test="settleMode != null and  settleMode != '' ">
                AND SETTLE_MODE = #{settleMode,jdbcType=VARCHAR}
            </if>
            <if test="tranTimestamp != null and  tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="busSeqNo != null and  busSeqNo != '' ">
                AND BUS_SEQ_NO = #{busSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="glPostedFlag != null and  glPostedFlag != '' ">
                AND GL_POSTED_FLAG = #{glPostedFlag,jdbcType=VARCHAR}
            </if>
            <if test="subSeqNo != null and  subSeqNo != '' ">
                AND SUB_SEQ_NO = #{subSeqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="PrimaryKey_Where">
        <where>
            <if test="tradeNo != null and  tradeNo != '' ">
                AND TRADE_NO = #{tradeNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="Base_Set">
        <set>
            <if test="tradeNo != null and tradeNo != '' ">
                TRADE_NO = #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="channelSeqNo != null and channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="reference != null and reference != '' ">
                REFERENCE = #{reference,jdbcType=VARCHAR},
            </if>
            <if test="sendSystem != null and sendSystem != '' ">
                SEND_SYSTEM = #{sendSystem,jdbcType=VARCHAR},
            </if>
            <if test="systemId != null and systemId != '' ">
                SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="tranBranch != null and tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="branch != null and branch != '' ">
                BRANCH = #{branch,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null and ccy != '' ">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="sourceModule != null and sourceModule != '' ">
                SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null and sourceType != '' ">
                SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE = #{effectDate,jdbcType=DATE},
            </if>
            <if test="eventType != null and eventType != '' ">
                EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="tranType != null and tranType != '' ">
                TRAN_TYPE = #{tranType,jdbcType=VARCHAR},
            </if>
            <if test="accountingStatus != null and accountingStatus != '' ">
                ACCOUNTING_STATUS = #{accountingStatus,jdbcType=VARCHAR},
            </if>
            <if test="clientType != null and clientType != '' ">
                CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},
            </if>
            <if test="amtType != null and amtType != '' ">
                AMT_TYPE = #{amtType,jdbcType=VARCHAR},
            </if>
            <if test="amount != null ">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="busiProd != null and busiProd != '' ">
                BUSI_PROD = #{busiProd,jdbcType=VARCHAR},
            </if>
            <if test="marketingProd != null and marketingProd != '' ">
                MARKETING_PROD = #{marketingProd,jdbcType=VARCHAR},
            </if>
            <if test="marketingProdDesc != null and marketingProdDesc != '' ">
                MARKETING_PROD_DESC = #{marketingProdDesc,jdbcType=VARCHAR},
            </if>
            <if test="narrative != null and narrative != '' ">
                NARRATIVE = #{narrative,jdbcType=VARCHAR},
            </if>
            <if test="acctInternalKey != null ">
                ACCT_INTERNAL_KEY = #{acctInternalKey,jdbcType=BIGINT},
            </if>
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="acctSeqNo != null and acctSeqNo != '' ">
                ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="term != null and term != '' ">
                TERM = #{term,jdbcType=VARCHAR},
            </if>
            <if test="termType != null and termType != '' ">
                TERM_TYPE = #{termType,jdbcType=VARCHAR},
            </if>
            <if test="profitCenter != null and profitCenter != '' ">
                PROFIT_CENTER = #{profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="subjectCode != null and subjectCode != '' ">
                SUBJECT_CODE = #{subjectCode,jdbcType=VARCHAR},
            </if>
            <if test="glSeqNo != null and glSeqNo != '' ">
                GL_SEQ_NO = #{glSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>
            <if test="channelDate != null ">
                CHANNEL_DATE = #{channelDate,jdbcType=DATE},
            </if>
            <if test="flatRate != null ">
                FLAT_RATE = #{flatRate,jdbcType=DECIMAL},
            </if>
            <if test="custRate != null ">
                CUST_RATE = #{custRate,jdbcType=DECIMAL},
            </if>
            <if test="othCcy != null and othCcy != '' ">
                OTH_CCY = #{othCcy,jdbcType=VARCHAR},
            </if>
            <if test="othAmount != null ">
                OTH_AMOUNT = #{othAmount,jdbcType=DECIMAL},
            </if>
            <if test="userId != null and userId != '' ">
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="reversal != null and reversal != '' ">
                REVERSAL = #{reversal,jdbcType=VARCHAR},
            </if>
            <if test="reversalDate != null ">
                REVERSAL_DATE = #{reversalDate,jdbcType=DATE},
            </if>
            <if test="reversalSeqNo != null and reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="taxPercent != null ">
                TAX_PERCENT = #{taxPercent,jdbcType=DECIMAL},
            </if>
            <if test="taxAmt != null ">
                TAX_AMT = #{taxAmt,jdbcType=DECIMAL},
            </if>
            <if test="tranCategory != null and tranCategory != '' ">
                TRAN_CATEGORY = #{tranCategory,jdbcType=VARCHAR},
            </if>
            <if test="unReal != null and unReal != '' ">
                UN_REAL = #{unReal,jdbcType=VARCHAR},
            </if>
            <if test="settleMode != null and settleMode != '' ">
                SETTLE_MODE = #{settleMode,jdbcType=VARCHAR},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="busSeqNo != null and busSeqNo != '' ">
                BUS_SEQ_NO = #{busSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="glPostedFlag != null and glPostedFlag != '' ">
                GL_POSTED_FLAG = #{glPostedFlag,jdbcType=VARCHAR},
            </if>
            <if test="subSeqNo != null and subSeqNo != '' ">
                SUB_SEQ_NO = #{subSeqNo,jdbcType=VARCHAR},
            </if>
        </set>
    </sql>

    <sql id="Base_Select">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </sql>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'"> ${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'"> *</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null ">
            and ${cometKeyField} between #{cometStart} and #{cometEnd}
        </if>
    </sql>

    <sql id="comet_row_num_step_column">
        <if test="cometSqlType == 'total'"> count(*) AS TOTAL</if>
        <if test="cometSqlType == 'offset'"> *</if>
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tradeNo != null ">TRADE_NO,</if>
            <if test="channelSeqNo != null ">CHANNEL_SEQ_NO,</if>
            <if test="reference != null ">REFERENCE,</if>
            <if test="sendSystem != null ">SEND_SYSTEM,</if>
            <if test="systemId != null ">SYSTEM_ID,</if>
            <if test="tranBranch != null ">TRAN_BRANCH,</if>
            <if test="branch != null ">BRANCH,</if>
            <if test="ccy != null ">CCY,</if>
            <if test="sourceModule != null ">SOURCE_MODULE,</if>
            <if test="sourceType != null ">SOURCE_TYPE,</if>
            <if test="tranDate != null ">TRAN_DATE,</if>
            <if test="effectDate != null ">EFFECT_DATE,</if>
            <if test="eventType != null ">EVENT_TYPE,</if>
            <if test="tranType != null ">TRAN_TYPE,</if>
            <if test="accountingStatus != null ">ACCOUNTING_STATUS,</if>
            <if test="clientType != null ">CLIENT_TYPE,</if>
            <if test="amtType != null ">AMT_TYPE,</if>
            <if test="amount != null ">AMOUNT,</if>
            <if test="busiProd != null ">BUSI_PROD,</if>
            <if test="marketingProd != null ">MARKETING_PROD,</if>
            <if test="marketingProdDesc != null ">MARKETING_PROD_DESC,</if>
            <if test="narrative != null ">NARRATIVE,</if>
            <if test="acctInternalKey != null ">ACCT_INTERNAL_KEY,</if>
            <if test="baseAcctNo != null ">BASE_ACCT_NO,</if>
            <if test="clientNo != null ">CLIENT_NO,</if>
            <if test="acctSeqNo != null ">ACCT_SEQ_NO,</if>
            <if test="term != null ">TERM,</if>
            <if test="termType != null ">TERM_TYPE,</if>
            <if test="profitCenter != null ">PROFIT_CENTER,</if>
            <if test="subjectCode != null ">SUBJECT_CODE,</if>
            <if test="glSeqNo != null ">GL_SEQ_NO,</if>
            <if test="company != null ">COMPANY,</if>
            <if test="channelDate != null ">CHANNEL_DATE,</if>
            <if test="flatRate != null ">FLAT_RATE,</if>
            <if test="custRate != null ">CUST_RATE,</if>
            <if test="othCcy != null ">OTH_CCY,</if>
            <if test="othAmount != null ">OTH_AMOUNT,</if>
            <if test="userId != null ">USER_ID,</if>
            <if test="reversal != null ">REVERSAL,</if>
            <if test="reversalDate != null ">REVERSAL_DATE,</if>
            <if test="reversalSeqNo != null ">REVERSAL_SEQ_NO,</if>
            <if test="taxPercent != null ">TAX_PERCENT,</if>
            <if test="taxAmt != null ">TAX_AMT,</if>
            <if test="tranCategory != null ">TRAN_CATEGORY,</if>
            <if test="unReal != null ">UN_REAL,</if>
            <if test="settleMode != null ">SETTLE_MODE,</if>
            <if test="tranTimestamp != null ">TRAN_TIMESTAMP,</if>
            <if test="busSeqNo != null ">BUS_SEQ_NO,</if>
            <if test="glPostedFlag != null ">GL_POSTED_FLAG,</if>
            <if test="subSeqNo != null ">SUB_SEQ_NO,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tradeNo != null ">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="channelSeqNo != null ">#{channelSeqNo,jdbcType=VARCHAR},</if>
            <if test="reference != null ">#{reference,jdbcType=VARCHAR},</if>
            <if test="sendSystem != null ">#{sendSystem,jdbcType=VARCHAR},</if>
            <if test="systemId != null ">#{systemId,jdbcType=VARCHAR},</if>
            <if test="tranBranch != null ">#{tranBranch,jdbcType=VARCHAR},</if>
            <if test="branch != null ">#{branch,jdbcType=VARCHAR},</if>
            <if test="ccy != null ">#{ccy,jdbcType=VARCHAR},</if>
            <if test="sourceModule != null ">#{sourceModule,jdbcType=VARCHAR},</if>
            <if test="sourceType != null ">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="tranDate != null ">#{tranDate,jdbcType=DATE},</if>
            <if test="effectDate != null ">#{effectDate,jdbcType=DATE},</if>
            <if test="eventType != null ">#{eventType,jdbcType=VARCHAR},</if>
            <if test="tranType != null ">#{tranType,jdbcType=VARCHAR},</if>
            <if test="accountingStatus != null ">#{accountingStatus,jdbcType=VARCHAR},</if>
            <if test="clientType != null ">#{clientType,jdbcType=VARCHAR},</if>
            <if test="amtType != null ">#{amtType,jdbcType=VARCHAR},</if>
            <if test="amount != null ">#{amount,jdbcType=DECIMAL},</if>
            <if test="busiProd != null ">#{busiProd,jdbcType=VARCHAR},</if>
            <if test="marketingProd != null ">#{marketingProd,jdbcType=VARCHAR},</if>
            <if test="marketingProdDesc != null ">#{marketingProdDesc,jdbcType=VARCHAR},</if>
            <if test="narrative != null ">#{narrative,jdbcType=VARCHAR},</if>
            <if test="acctInternalKey != null ">#{acctInternalKey,jdbcType=BIGINT},</if>
            <if test="baseAcctNo != null ">#{baseAcctNo,jdbcType=VARCHAR},</if>
            <if test="clientNo != null ">#{clientNo,jdbcType=VARCHAR},</if>
            <if test="acctSeqNo != null ">#{acctSeqNo,jdbcType=VARCHAR},</if>
            <if test="term != null ">#{term,jdbcType=VARCHAR},</if>
            <if test="termType != null ">#{termType,jdbcType=VARCHAR},</if>
            <if test="profitCenter != null ">#{profitCenter,jdbcType=VARCHAR},</if>
            <if test="subjectCode != null ">#{subjectCode,jdbcType=VARCHAR},</if>
            <if test="glSeqNo != null ">#{glSeqNo,jdbcType=VARCHAR},</if>
            <if test="company != null ">#{company,jdbcType=VARCHAR},</if>
            <if test="channelDate != null ">#{channelDate,jdbcType=DATE},</if>
            <if test="flatRate != null ">#{flatRate,jdbcType=DECIMAL},</if>
            <if test="custRate != null ">#{custRate,jdbcType=DECIMAL},</if>
            <if test="othCcy != null ">#{othCcy,jdbcType=VARCHAR},</if>
            <if test="othAmount != null ">#{othAmount,jdbcType=DECIMAL},</if>
            <if test="userId != null ">#{userId,jdbcType=VARCHAR},</if>
            <if test="reversal != null ">#{reversal,jdbcType=VARCHAR},</if>
            <if test="reversalDate != null ">#{reversalDate,jdbcType=DATE},</if>
            <if test="reversalSeqNo != null ">#{reversalSeqNo,jdbcType=VARCHAR},</if>
            <if test="taxPercent != null ">#{taxPercent,jdbcType=DECIMAL},</if>
            <if test="taxAmt != null ">#{taxAmt,jdbcType=DECIMAL},</if>
            <if test="tranCategory != null ">#{tranCategory,jdbcType=VARCHAR},</if>
            <if test="unReal != null ">#{unReal,jdbcType=VARCHAR},</if>
            <if test="settleMode != null ">#{settleMode,jdbcType=VARCHAR},</if>
            <if test="tranTimestamp != null ">#{tranTimestamp,jdbcType=VARCHAR},</if>
            <if test="busSeqNo != null ">#{busSeqNo,jdbcType=VARCHAR},</if>
            <if test="glPostedFlag != null ">#{glPostedFlag,jdbcType=VARCHAR},</if>
            <if test="subSeqNo != null ">#{subSeqNo,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>

    <update id="updateByPrimaryKey">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>


    <update id="updateByEntity">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="s.tradeNo != null and s.tradeNo != '' ">
                TRADE_NO = #{s.tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="s.channelSeqNo != null and s.channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{s.channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.reference != null and s.reference != '' ">
                REFERENCE = #{s.reference,jdbcType=VARCHAR},
            </if>
            <if test="s.sendSystem != null and s.sendSystem != '' ">
                SEND_SYSTEM = #{s.sendSystem,jdbcType=VARCHAR},
            </if>
            <if test="s.systemId != null and s.systemId != '' ">
                SYSTEM_ID = #{s.systemId,jdbcType=VARCHAR},
            </if>
            <if test="s.tranBranch != null and s.tranBranch != '' ">
                TRAN_BRANCH = #{s.tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.branch != null and s.branch != '' ">
                BRANCH = #{s.branch,jdbcType=VARCHAR},
            </if>
            <if test="s.ccy != null and s.ccy != '' ">
                CCY = #{s.ccy,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceModule != null and s.sourceModule != '' ">
                SOURCE_MODULE = #{s.sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceType != null and s.sourceType != '' ">
                SOURCE_TYPE = #{s.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="s.tranDate != null ">
                TRAN_DATE = #{s.tranDate,jdbcType=DATE},
            </if>
            <if test="s.effectDate != null ">
                EFFECT_DATE = #{s.effectDate,jdbcType=DATE},
            </if>
            <if test="s.eventType != null and s.eventType != '' ">
                EVENT_TYPE = #{s.eventType,jdbcType=VARCHAR},
            </if>
            <if test="s.tranType != null and s.tranType != '' ">
                TRAN_TYPE = #{s.tranType,jdbcType=VARCHAR},
            </if>
            <if test="s.accountingStatus != null and s.accountingStatus != '' ">
                ACCOUNTING_STATUS = #{s.accountingStatus,jdbcType=VARCHAR},
            </if>
            <if test="s.clientType != null and s.clientType != '' ">
                CLIENT_TYPE = #{s.clientType,jdbcType=VARCHAR},
            </if>
            <if test="s.amtType != null and s.amtType != '' ">
                AMT_TYPE = #{s.amtType,jdbcType=VARCHAR},
            </if>
            <if test="s.amount != null ">
                AMOUNT = #{s.amount,jdbcType=DECIMAL},
            </if>
            <if test="s.busiProd != null and s.busiProd != '' ">
                BUSI_PROD = #{s.busiProd,jdbcType=VARCHAR},
            </if>
            <if test="s.marketingProd != null and s.marketingProd != '' ">
                MARKETING_PROD = #{s.marketingProd,jdbcType=VARCHAR},
            </if>
            <if test="s.marketingProdDesc != null and s.marketingProdDesc != '' ">
                MARKETING_PROD_DESC = #{s.marketingProdDesc,jdbcType=VARCHAR},
            </if>
            <if test="s.narrative != null and s.narrative != '' ">
                NARRATIVE = #{s.narrative,jdbcType=VARCHAR},
            </if>
            <if test="s.acctInternalKey != null ">
                ACCT_INTERNAL_KEY = #{s.acctInternalKey,jdbcType=BIGINT},
            </if>
            <if test="s.baseAcctNo != null and s.baseAcctNo != '' ">
                BASE_ACCT_NO = #{s.baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="s.acctSeqNo != null and s.acctSeqNo != '' ">
                ACCT_SEQ_NO = #{s.acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.term != null and s.term != '' ">
                TERM = #{s.term,jdbcType=VARCHAR},
            </if>
            <if test="s.termType != null and s.termType != '' ">
                TERM_TYPE = #{s.termType,jdbcType=VARCHAR},
            </if>
            <if test="s.profitCenter != null and s.profitCenter != '' ">
                PROFIT_CENTER = #{s.profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="s.subjectCode != null and s.subjectCode != '' ">
                SUBJECT_CODE = #{s.subjectCode,jdbcType=VARCHAR},
            </if>
            <if test="s.glSeqNo != null and s.glSeqNo != '' ">
                GL_SEQ_NO = #{s.glSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.company != null and s.company != '' ">
                COMPANY = #{s.company,jdbcType=VARCHAR},
            </if>
            <if test="s.channelDate != null ">
                CHANNEL_DATE = #{s.channelDate,jdbcType=DATE},
            </if>
            <if test="s.flatRate != null ">
                FLAT_RATE = #{s.flatRate,jdbcType=DECIMAL},
            </if>
            <if test="s.custRate != null ">
                CUST_RATE = #{s.custRate,jdbcType=DECIMAL},
            </if>
            <if test="s.othCcy != null and s.othCcy != '' ">
                OTH_CCY = #{s.othCcy,jdbcType=VARCHAR},
            </if>
            <if test="s.othAmount != null ">
                OTH_AMOUNT = #{s.othAmount,jdbcType=DECIMAL},
            </if>
            <if test="s.userId != null and s.userId != '' ">
                USER_ID = #{s.userId,jdbcType=VARCHAR},
            </if>
            <if test="s.reversal != null and s.reversal != '' ">
                REVERSAL = #{s.reversal,jdbcType=VARCHAR},
            </if>
            <if test="s.reversalDate != null ">
                REVERSAL_DATE = #{s.reversalDate,jdbcType=DATE},
            </if>
            <if test="s.reversalSeqNo != null and s.reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{s.reversalSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.taxPercent != null ">
                TAX_PERCENT = #{s.taxPercent,jdbcType=DECIMAL},
            </if>
            <if test="s.taxAmt != null ">
                TAX_AMT = #{s.taxAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.tranCategory != null and s.tranCategory != '' ">
                TRAN_CATEGORY = #{s.tranCategory,jdbcType=VARCHAR},
            </if>
            <if test="s.unReal != null and s.unReal != '' ">
                UN_REAL = #{s.unReal,jdbcType=VARCHAR},
            </if>
            <if test="s.settleMode != null and s.settleMode != '' ">
                SETTLE_MODE = #{s.settleMode,jdbcType=VARCHAR},
            </if>
            <if test="s.tranTimestamp != null and s.tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{s.tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="s.busSeqNo != null and s.busSeqNo != '' ">
                BUS_SEQ_NO = #{s.busSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.glPostedFlag != null and s.glPostedFlag != '' ">
                GL_POSTED_FLAG = #{s.glPostedFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.subSeqNo != null and s.subSeqNo != '' ">
                SUB_SEQ_NO = #{s.subSeqNo,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="w.tradeNo != null and w.tradeNo != '' ">
                AND TRADE_NO = #{w.tradeNo,jdbcType=VARCHAR}
            </if>
            <if test="w.channelSeqNo != null and w.channelSeqNo != '' ">
                AND CHANNEL_SEQ_NO = #{w.channelSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.reference != null and w.reference != '' ">
                AND REFERENCE = #{w.reference,jdbcType=VARCHAR}
            </if>
            <if test="w.sendSystem != null and w.sendSystem != '' ">
                AND SEND_SYSTEM = #{w.sendSystem,jdbcType=VARCHAR}
            </if>
            <if test="w.systemId != null and w.systemId != '' ">
                AND SYSTEM_ID = #{w.systemId,jdbcType=VARCHAR}
            </if>
            <if test="w.tranBranch != null and w.tranBranch != '' ">
                AND TRAN_BRANCH = #{w.tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.branch != null and w.branch != '' ">
                AND BRANCH = #{w.branch,jdbcType=VARCHAR}
            </if>
            <if test="w.ccy != null and w.ccy != '' ">
                AND CCY = #{w.ccy,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceModule != null and w.sourceModule != '' ">
                AND SOURCE_MODULE = #{w.sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceType != null and w.sourceType != '' ">
                AND SOURCE_TYPE = #{w.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="w.tranDate != null ">
                AND TRAN_DATE = #{w.tranDate,jdbcType=DATE}
            </if>
            <if test="w.effectDate != null ">
                AND EFFECT_DATE = #{w.effectDate,jdbcType=DATE}
            </if>
            <if test="w.eventType != null and w.eventType != '' ">
                AND EVENT_TYPE = #{w.eventType,jdbcType=VARCHAR}
            </if>
            <if test="w.tranType != null and w.tranType != '' ">
                AND TRAN_TYPE = #{w.tranType,jdbcType=VARCHAR}
            </if>
            <if test="w.accountingStatus != null and w.accountingStatus != '' ">
                AND ACCOUNTING_STATUS = #{w.accountingStatus,jdbcType=VARCHAR}
            </if>
            <if test="w.clientType != null and w.clientType != '' ">
                AND CLIENT_TYPE = #{w.clientType,jdbcType=VARCHAR}
            </if>
            <if test="w.amtType != null and w.amtType != '' ">
                AND AMT_TYPE = #{w.amtType,jdbcType=VARCHAR}
            </if>
            <if test="w.amount != null ">
                AND AMOUNT = #{w.amount,jdbcType=DECIMAL}
            </if>
            <if test="w.busiProd != null and w.busiProd != '' ">
                AND BUSI_PROD = #{w.busiProd,jdbcType=VARCHAR}
            </if>
            <if test="w.marketingProd != null and w.marketingProd != '' ">
                AND MARKETING_PROD = #{w.marketingProd,jdbcType=VARCHAR}
            </if>
            <if test="w.marketingProdDesc != null and w.marketingProdDesc != '' ">
                AND MARKETING_PROD_DESC = #{w.marketingProdDesc,jdbcType=VARCHAR}
            </if>
            <if test="w.narrative != null and w.narrative != '' ">
                AND NARRATIVE = #{w.narrative,jdbcType=VARCHAR}
            </if>
            <if test="w.acctInternalKey != null ">
                AND ACCT_INTERNAL_KEY = #{w.acctInternalKey,jdbcType=BIGINT}
            </if>
            <if test="w.baseAcctNo != null and w.baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{w.baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="w.clientNo != null and w.clientNo != '' ">
                AND CLIENT_NO = #{w.clientNo,jdbcType=VARCHAR}
            </if>
            <if test="w.acctSeqNo != null and w.acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{w.acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.term != null and w.term != '' ">
                AND TERM = #{w.term,jdbcType=VARCHAR}
            </if>
            <if test="w.termType != null and w.termType != '' ">
                AND TERM_TYPE = #{w.termType,jdbcType=VARCHAR}
            </if>
            <if test="w.profitCenter != null and w.profitCenter != '' ">
                AND PROFIT_CENTER = #{w.profitCenter,jdbcType=VARCHAR}
            </if>
            <if test="w.subjectCode != null and w.subjectCode != '' ">
                AND SUBJECT_CODE = #{w.subjectCode,jdbcType=VARCHAR}
            </if>
            <if test="w.glSeqNo != null and w.glSeqNo != '' ">
                AND GL_SEQ_NO = #{w.glSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.company != null and w.company != '' ">
                AND COMPANY = #{w.company,jdbcType=VARCHAR}
            </if>
            <if test="w.channelDate != null ">
                AND CHANNEL_DATE = #{w.channelDate,jdbcType=DATE}
            </if>
            <if test="w.flatRate != null ">
                AND FLAT_RATE = #{w.flatRate,jdbcType=DECIMAL}
            </if>
            <if test="w.custRate != null ">
                AND CUST_RATE = #{w.custRate,jdbcType=DECIMAL}
            </if>
            <if test="w.othCcy != null and w.othCcy != '' ">
                AND OTH_CCY = #{w.othCcy,jdbcType=VARCHAR}
            </if>
            <if test="w.othAmount != null ">
                AND OTH_AMOUNT = #{w.othAmount,jdbcType=DECIMAL}
            </if>
            <if test="w.userId != null and w.userId != '' ">
                AND USER_ID = #{w.userId,jdbcType=VARCHAR}
            </if>
            <if test="w.reversal != null and w.reversal != '' ">
                AND REVERSAL = #{w.reversal,jdbcType=VARCHAR}
            </if>
            <if test="w.reversalDate != null ">
                AND REVERSAL_DATE = #{w.reversalDate,jdbcType=DATE}
            </if>
            <if test="w.reversalSeqNo != null and w.reversalSeqNo != '' ">
                AND REVERSAL_SEQ_NO = #{w.reversalSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.taxPercent != null ">
                AND TAX_PERCENT = #{w.taxPercent,jdbcType=DECIMAL}
            </if>
            <if test="w.taxAmt != null ">
                AND TAX_AMT = #{w.taxAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.tranCategory != null and w.tranCategory != '' ">
                AND TRAN_CATEGORY = #{w.tranCategory,jdbcType=VARCHAR}
            </if>
            <if test="w.unReal != null and w.unReal != '' ">
                AND UN_REAL = #{w.unReal,jdbcType=VARCHAR}
            </if>
            <if test="w.settleMode != null and w.settleMode != '' ">
                AND SETTLE_MODE = #{w.settleMode,jdbcType=VARCHAR}
            </if>
            <if test="w.tranTimestamp != null and w.tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{w.tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="w.busSeqNo != null and w.busSeqNo != '' ">
                AND BUS_SEQ_NO = #{w.busSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.glPostedFlag != null and w.glPostedFlag != '' ">
                AND GL_POSTED_FLAG = #{w.glPostedFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.subSeqNo != null and w.subSeqNo != '' ">
                AND SUB_SEQ_NO = #{w.subSeqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <delete id="delete">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </delete>

    <select id="count" parameterType="java.util.Map" resultType="int">
        SELECT count(1) FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </select>

    <select id="selectOne" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectList" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
        <include refid="Base_Select"/>
        FOR UPDATE
    </select>

    <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </delete>

    <!--更新ob_gl_hist过账标记-->
    <update id="updateTbGlHistBySeqNo" parameterType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            update
            <include refid="Table_Name"/>
            SET GL_POSTED_FLAG='Y'
            WHERE ifnull(GL_POSTED_FLAG,'N')='N'
            and TRADE_NO IN
            <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="_databaseId == 'oracle'">
            update
            <include refid="Table_Name"/>
            SET GL_POSTED_FLAG='Y'
            WHERE ifnull(GL_POSTED_FLAG,'N')='N'
            and TRADE_NO IN
            <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <!--更新tb_gl_hist过账标记-->
    <update id="updateGlPostedYByTradeNo" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.TbGlHist">
        <if test="_databaseId == 'mysql'">
            update <include refid="Table_Name"/>
            SET GL_POSTED_FLAG='Y'
            WHERE ifnull(GL_POSTED_FLAG,'N')='N'
            and TRADE_NO = #{tradeNo}
        </if>
        <if test="_databaseId == 'oracle'">
            update <include refid="Table_Name"/>
            SET GL_POSTED_FLAG='Y'
            WHERE nvl(GL_POSTED_FLAG,'N')='N'
            and TRADE_NO = #{tradeNo}
        </if>
    </update>

</mapper>
