<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcIndvlCoprQuota">

	<update id="updateIndvlCoprQuota">
		UPDATE
		<include refid="Table_Name"/>
		<include refid="Base_Set"/>
		<where>
			<if test="parentQuotaClass != null ">
				AND PARENT_QUOTA_CLASS = #{parentQuotaClass}
			</if>
			<if test="individualFlag != null ">
				AND INDIVIDUAL_FLAG = #{individualFlag}
			</if>
			<if test="stageCode != null">
				AND STAGE_CODE = #{stageCode}
			</if>
			<if test="issueYear != null">
				AND ISSUE_YEAR = #{issueYear}
			</if>
			<if test="branch != null">
				AND BRANCH = #{branch}
			</if>
			<if test="ccy != null">
				AND CCY = #{ccy}
			</if>
			<if test="stageType != null">
				AND STAGE_TYPE = #{stageType}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</update>

</mapper>