<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">
    select <include refid="Base_Column"/>
    from RB_DC_BRANCH_AMT
    where STAGE_CODE = #{stageCode}
    <if test="channel != null">
      AND CHANNEL = #{channel}
    </if>
    <if test="branch != null">
      AND BRANCH = #{branch}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">
    delete from RB_DC_BRANCH_AMT
    where STAGE_CODE = #{stageCode}
    <if test="channel != null">
      AND CHANNEL = #{channel}
    </if>
    <if test="branch != null">
      AND BRANCH = #{branch}
    </if>
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">
    update RB_DC_BRANCH_AMT
    <set>
      <if test="issueYear != null">
        ISSUE_YEAR = #{issueYear},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="totalLimit != null">
        TOTAL_LIMIT = #{totalLimit},
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT = #{distributeLimit},
      </if>
      <if test="holdingLimit != null">
        HOLDING_LIMIT = #{holdingLimit},
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT = #{leaveLimit},
      </if>
      <if test="backStatus != null">
        BACK_STATUS = #{backStatus},
      </if>
      <if test="errorDesc != null">
        ERROR_DESC = #{errorDesc},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS = #{stageProdClass}
      </if>
    </set>
    where STAGE_CODE = #{stageCode}
    AND CHANNEL = #{channel}
    AND PROD_TYPE = #{prodType}
    AND BRANCH = #{branch}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">
    insert into RB_DC_BRANCH_AMT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        ISSUE_YEAR,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="channel != null">
        CHANNEL,
      </if>
      <if test="branch != null">
        BRANCH,
      </if>
      <if test="totalLimit != null">
        TOTAL_LIMIT,
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT,
      </if>
      <if test="holdingLimit != null">
        HOLDING_LIMIT,
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        #{issueYear},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="channel != null">
        #{channel},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="totalLimit != null">
        #{totalLimit},
      </if>
      <if test="distributeLimit != null">
        #{distributeLimit},
      </if>
      <if test="holdingLimit != null">
        #{holdingLimit},
      </if>
      <if test="leaveLimit != null">
        #{leaveLimit},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="selectDcBranchAmtList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">
    select <include refid="Base_Column"/>
    from RB_DC_BRANCH_AMT
    where STAGE_CODE = #{stageCode}
    <if test="channel != null">
      AND CHANNEL = #{channel}
    </if>
    <if test="branch != null">
      AND BRANCH = #{branch}
    </if>
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="ccy != null">
      AND CCY = #{ccy}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectDcBranchAmtListEod" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">
    select <include refid="Base_Column"/>
    from RB_DC_BRANCH_AMT
    where STAGE_CODE = #{stageCode}
    AND ( BACK_STATUS = 'N' OR BACK_STATUS IS NULL )
    <if test="channel != null">
      AND CHANNEL = #{channel}
    </if>
    <if test="branch != null">
      AND BRANCH = #{branch}
    </if>
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="ccy != null">
      AND CCY = #{ccy}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSumByLeaveLimt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcBranchAmt">
    select
    sum(DISTRIBUTE_LIMIT) as DISTRIBUTE_LIMIT,
    sum(HOLDING_LIMIT) as HOLDING_LIMIT,
    sum(LEAVE_LIMIT) as LEAVE_LIMIT
    from RB_DC_BRANCH_AMT
    where STAGE_CODE = #{stageCode}
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      AND CCY = #{acctCcy}
    </if>
    <if test="channel != null">
      AND CHANNEL = #{channel}
    </if>
    <if test="tranBranch != null">
      AND BRANCH = #{tranBranch}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
