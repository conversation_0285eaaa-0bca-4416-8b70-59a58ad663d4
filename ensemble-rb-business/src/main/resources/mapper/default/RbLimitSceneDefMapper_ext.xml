<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitSceneDef">

    <select id="getLimitSceneDetailList" resultType="com.dcits.ensemble.rb.limit.business.model.LimitScene">
        SELECT
            DEF.LIMIT_SCENE_DESC,
            DEF.LIMIT_CONVERT,
            DEF.LIMIT_CCY,
            DEF.VALID_FLAG,
            DEF.LIMIT_SCENE_NO,
            DEF.CHECK_OBJ_TYPE,
            DEF.LIMIT_MAIN_TYPE,
            CONF.LIMIT_BRANCH_RANGE,
            CONF.LIMIT_BRANCH_ID,
            CONF.CTRL_ITEM_TYPE,
            CONF.SUM_TYPE,
            CONF.PERIOD_VALUE,
            CONF.PERIOD_TYPE,
            CONF.LIMIT_CTRL_BGN_DATE,
            CONF.LIMIT_CTRL_END_DATE,
            CONF.LIMIT_CTRL_BGN_TIME,
            CONF.LIMIT_CTRL_END_TIME,
            CONF.LIMIT_CTRL_AMT,
            CONF.LIMIT_CTRL_NUM,
            CONF.DEAL_FLOW,
            CONF.ALLOW_CUSTOM_FLAG,
            CONF.COMPANY,
            CONF.TRAN_TIMESTAMP,
            CONF.ALLOW_EXCEED_FLAG,
            CONF.ONLY_CUSTOM,
            CONF.TEMP_LIMIT_FLAG,
            CONF.TEMP_LIMIT_VALID_TERM
        FROM RB_LIMIT_SCENE_DEF DEF
        LEFT JOIN RB_LIMIT_CTRL_CONF CONF
        ON DEF.LIMIT_SCENE_NO = CONF.LIMIT_SCENE_NO
        <where>
            <if test="validFlag != null and  validFlag != '' ">
                AND DEF.VALID_FLAG = #{validFlag,jdbcType=VARCHAR}
            </if>
            <if test="allowCustomFlag != null and  allowCustomFlag != '' ">
                AND CONF.ALLOW_CUSTOM_FLAG = #{allowCustomFlag,jdbcType=VARCHAR}
            </if>
            <if test="limitSceneNo != null and  limitSceneNo != '' ">
                AND DEF.LIMIT_SCENE_NO = #{limitSceneNo,jdbcType=VARCHAR}
            </if>
            <if test="checkObjType != null and  checkObjType != '' ">
                AND DEF.CHECK_OBJ_TYPE = #{checkObjType,jdbcType=VARCHAR}
            </if>
            <if test="limitMainType != null and  limitMainType != '' ">
                AND DEF.LIMIT_MAIN_TYPE = #{limitMainType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getLimitCustCtrlDetailList" resultType="com.dcits.ensemble.rb.limit.business.model.LimitScene">
        SELECT
            DEF.LIMIT_SCENE_DESC,
            DEF.LIMIT_CONVERT,
            DEF.VALID_FLAG,
            DEF.LIMIT_SCENE_NO,
            DEF.CHECK_OBJ_TYPE,
            DEF.LIMIT_MAIN_TYPE,
            CONF.LIMIT_BRANCH_RANGE,
            CONF.LIMIT_BRANCH_ID,
            CONF.CTRL_ITEM_TYPE,
            CONF.SUM_TYPE,
            CONF.PERIOD_VALUE,
            CONF.PERIOD_TYPE,
            CONF.LIMIT_CTRL_BGN_DATE,
            CONF.LIMIT_CTRL_END_DATE,
            CONF.LIMIT_CTRL_BGN_TIME,
            CONF.LIMIT_CTRL_END_TIME,
            CONF.DEAL_FLOW,
            CONF.ALLOW_CUSTOM_FLAG,
            CONF.COMPANY,
            CUST.LIMIT_CTRL_AMT,
            CUST.LIMIT_CTRL_NUM,
            CUST.CHECK_OBJ_VAL,
            CUST.EFFECT_DATE,
            CUST.EXPIRE_DATE,
            CUST.CLIENT_NO
        FROM RB_LIMIT_CTRL_CUSTOM_INFO CUST
        LEFT JOIN RB_LIMIT_SCENE_DEF DEF ON CUST.LIMIT_SCENE_NO = DEF.LIMIT_SCENE_NO
        LEFT JOIN RB_LIMIT_CTRL_CONF CONF ON CUST.LIMIT_SCENE_NO = CONF.LIMIT_SCENE_NO AND CONF.VALID_FLAG = 'Y'
        <where>
            <if test="ctrlItemType != null and  ctrlItemType != '' ">
                AND CONF.CTRL_ITEM_TYPE = #{ctrlItemType,jdbcType=VARCHAR}
            </if>
            <if test="allowCustomFlag != null and  allowCustomFlag != '' ">
                AND CONF.ALLOW_CUSTOM_FLAG = #{allowCustomFlag,jdbcType=VARCHAR}
            </if>
            <if test="limitSceneNo != null and  limitSceneNo != '' ">
                AND DEF.LIMIT_SCENE_NO = #{limitSceneNo,jdbcType=VARCHAR}
            </if>
            <if test="checkObjType != null and  checkObjType != '' ">
                AND DEF.CHECK_OBJ_TYPE = #{checkObjType,jdbcType=VARCHAR}
            </if>
            <if test="limitMainType != null and  limitMainType != '' ">
                AND DEF.LIMIT_MAIN_TYPE = #{limitMainType,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CUST.CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND CUST.CHECK_OBJ_VAL = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
