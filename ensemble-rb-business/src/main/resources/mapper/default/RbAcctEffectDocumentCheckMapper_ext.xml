<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEffectDocumentCheck">

    <select id="selectByCondition" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.acct.RbAcctEffectDocumentCheckModel">
        SELECT AEDC.*, RC.ACCT_CLASS,RC.CLIENT_NO,RC.ACCT_SEQ_NO
        FROM RB_ACCT_EFFECT_DOCUMENT_CHECK AEDC
        INNER JOIN RB_ACCT RC
        ON AEDC.BASE_ACCT_NO = RC.BASE_ACCT_NO
        OR AEDC.BASE_ACCT_NO = RC.CARD_NO
        <where>
            RC.ACCT_CLASS IS NOT NULL
            <if test="batchNo != null and  batchNo != '' ">
                AND BATCH_NO = #{batchNo,jdbcType=VARCHAR}
            </if>
            <if test="resSeqNoAllowNull != null and  resSeqNoAllowNull == 'false' ">
                AND RES_SEQ_NO IS NOT NULL
            </if>
            <if test="baseAcctNos != null and  baseAcctNos.size()>0  ">
                AND AEDC.BASE_ACCT_NO in (
                <foreach collection="baseAcctNos" separator="," item="item">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                )
            </if>

        </where>
    </select>
    <select id="selectByMutiCondition" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEffectDocumentCheck"
            resultType="com.dcits.ensemble.rb.business.model.acct.RbAcctEffectDocumentCheckModel">
        SELECT AEDC.*
        FROM RB_ACCT_EFFECT_DOCUMENT_CHECK AEDC
        <where>
            RES_SEQ_NO IS NULL
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>

        </where>
    </select>
    <select id="getRelationByCardNoOrBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.acct.RbAcctEffectDocumentCheckModel">
        SELECT AEDC.*
        FROM RB_ACCT_EFFECT_DOCUMENT_CHECK AEDC
        <where>
            RES_SEQ_NO IS NULL
            <if test="baseAcctNos != null and  baseAcctNos.size()>0  ">
                AND AEDC.BASE_ACCT_NO in (
                <foreach collection="baseAcctNos" separator="," item="item">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
        </where>
    </select>


    <!--更新影像明细批量表状态和限制编号-->
    <update id="updateDetailsByPrimaryKey" parameterType="java.util.Map">
        UPDATE RB_ACCT_EFFECT_DOCUMENT_CHECK
        <set>
            <if test="batchStatus != null and batchStatus != ''">
                BATCH_STATUS=#{batchStatus},
            </if>
            <if test="resSeqNo != null and resSeqNo != ''">
                RES_SEQ_NO=#{resSeqNo},
            </if>
        </set>
        <where>
            <if test="seqNo != null and seqNo!='' ">
                AND SEQ_NO =#{seqNo}
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="batchNo != null and  batchNo != '' ">
                AND BATCH_NO = #{batchNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>

</mapper>
