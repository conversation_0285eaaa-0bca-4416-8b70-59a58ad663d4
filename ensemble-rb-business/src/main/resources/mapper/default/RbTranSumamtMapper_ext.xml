<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranSumamt">
    <insert id="insertForEod" parameterType="java.util.List">
        insert into
        RB_TRAN_SUMAMT
        (
        <include refid="Base_Column"/>
         )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.tranDate},
            #{item.sumCount},
            #{item.sumAmount},
            #{item.remark},
            #{item.tranTimestamp}
            from dual
        </foreach>
    </insert>
    <select id="getSumAmtInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranSumamt"
            useCache="false">
        select
            concat(sum(SUM_COUNT), '') SUM_COUNT,
            sum(SUM_AMOUNT)            SUM_AMOUNT
        from RB_TRAN_SUMAMT
        where tran_date = #{tranDate}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
