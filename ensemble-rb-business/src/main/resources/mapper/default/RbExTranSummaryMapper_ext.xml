<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary">

  <!--查询垂直库汇总表未处理的记录进行二次汇总 for 系统内平盘-->
  <select id="getBranchSumEntryForSumUnc" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary">
    SELECT TRAN_DATE,
    BRANCH,
    BUY_CCY,
    SELL_CCY,
    SELL_BUY_IND,
    (case TRAN_TYPE when 'FX48' then 'FX50' when 'FX49' then 'FX50' when 'FX50' then 'FX50' else '' end) AS TRAN_TYPE,
    REVERSAL,
    STATUS,
    SUM(AMT_BUY) AS AMT_BUY,
    SUM(AMT_SELL) AS AMT_SELL
    FROM RB_EX_TRAN_SUMMARY
    WHERE (STATUS IS NULL OR STATUS = 'N')
    AND TRAN_DATE between  #{lastRunDate,jdbcType=DATE} and #{yesterday,jdbcType=DATE}
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    GROUP BY TRAN_DATE, BRANCH, BUY_CCY, SELL_CCY, SELL_BUY_IND, (case TRAN_TYPE when 'FX48' then 'FX50' when 'FX49' then 'FX50' when 'FX50' then 'FX50' else '' end), REVERSAL, STATUS
  </select>

  <update id="updateRbExTranSummary" parameterType="java.util.Map">
    update RB_EX_TRAN_SUMMARY
    <set>
      <if test="newStatus != null and newStatus != ''">
        STATUS = #{newStatus},
      </if>
      <if test="uncCcyRate != null">
        UNC_CCY_RATE = #{uncCcyRate},
      </if>
      <if test="uncLcyAmt != null">
        UNC_LCY_AMT = #{uncLcyAmt},
      </if>
      <if test="ibuncReference != null and ibuncReference != ''">
        IBUNC_REFERENCE = #{ibuncReference},
      </if>
    </set>
    where TRAN_DATE = #{tranDate}
    and BRANCH = #{branch}
    <if test="buyCcy != null and buyCcy != ''">
      and BUY_CCY = #{buyCcy}
    </if>
    <if test="sellCcy != null and sellCcy != ''">
      and SELL_CCY = #{sellCcy}
    </if>
    <if test="sellBuyInd != null and sellBuyInd != ''">
      and SELL_BUY_IND = #{sellBuyInd}
    </if>
    <if test="reversal != null and reversal != ''">
      and REVERSAL = #{reversal}
    </if>
    <if test="oldStatus != null and oldStatus != ''">
      and STATUS = #{oldStatus}
    </if>
  </update>

  <update id="updateTranSummaryForMarketUnc" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary">
    update RB_EX_TRAN_SUMMARY
    <set>
      <if test="status != null and status != ''">
        STATUS = #{status},
      </if>
      <if test="obuncReference != null and obuncReference != ''">
        OBUNC_REFERENCE = #{obuncReference},
      </if>
    </set>
    where STATUS IN ('D', 'S')
    AND SELL_BUY_IND != 'E'
    AND TRAN_DATE = #{tranDate}
    AND BRANCH = #{branch}
    AND (BUY_CCY = #{ccy} OR SELL_CCY = #{ccy})
  </update>

  <select id="getRbExTranSummaryList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary">
    SELECT
    <include refid="Base_Column" />
    FROM RB_EX_TRAN_SUMMARY
    WHERE TRAN_DATE = #{tranDate}
    and BRANCH = #{branch}
    <if test="buyCcy != null and buyCcy != ''">
      and BUY_CCY = #{buyCcy}
    </if>
    <if test="sellCcy != null and sellCcy != ''">
      and SELL_CCY = #{sellCcy}
    </if>
    <if test="sellBuyInd != null and sellBuyInd != ''">
      and SELL_BUY_IND = #{sellBuyInd}
    </if>
    <if test="reversal != null and reversal != ''">
      and REVERSAL = #{reversal}
    </if>
    <if test="nodeId != null and nodeId != ''">
      and NODE_ID = #{nodeId}
    </if>
    <if test="refFlag != null and refFlag == 'Y'.toString()">
      and IBUNC_REFERENCE IS NULL
    </if>
    <if test="refFlag != null and refFlag == 'N'.toString()">
      and IBUNC_REFERENCE IS NOT NULL
    </if>
  </select>
</mapper>
