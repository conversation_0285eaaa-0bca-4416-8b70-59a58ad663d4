<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_OBSERVE_DAYS
    where OBSERVE_START_DATE = #{observeStartDate,jdbcType=DATE}
      <if test="prodType != null">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="stageCode != null">
        AND STAGE_CODE = #{stageCode}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays">
    delete from RB_DC_STAGE_OBSERVE_DAYS
    where OBSERVE_START_DATE = #{observeStartDate,jdbcType=DATE}
      <if test="prodType != null">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="stageCode != null">
        AND STAGE_CODE = #{stageCode}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays">
    update RB_DC_STAGE_OBSERVE_DAYS
    <set>
      <if test="observeEndDate != null">
        OBSERVE_END_DATE = #{observeEndDate},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="company != null">
        COMPANY = #{company}
      </if>
    </set>
    where OBSERVE_START_DATE = #{observeStartDate,jdbcType=DATE}
        AND PROD_TYPE = #{prodType}
        AND STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays">
    insert into RB_DC_STAGE_OBSERVE_DAYS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="observeStartDate != null">
        OBSERVE_START_DATE,
      </if>
      <if test="observeEndDate != null">
        OBSERVE_END_DATE,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="observeStartDate != null">
        #{observeStartDate},
      </if>
      <if test="observeEndDate != null">
        #{observeEndDate},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="queryStageObserveDaysForTigger" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_OBSERVE_DAYS
    where STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY OBSERVE_END_DATE desc
  </select>
  <select id="getListByStageCodeAndProdType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageObserveDays">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_OBSERVE_DAYS
    <where>
      <if test="stageCode != null">
        STAGE_CODE = #{stageCode}
      </if>
      <if test="prodType != null">
        AND PROD_TYPE = #{prodType}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    ORDER BY OBSERVE_START_DATE
  </select>
</mapper>
