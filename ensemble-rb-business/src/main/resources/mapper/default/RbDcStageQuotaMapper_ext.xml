<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageQuota">

	<update id="updateStageQuota">
		UPDATE
		<include refid="Table_Name"/>
		<include refid="Base_Set"/>
		<where>
			<if test="parentQuotaClass != null ">
				AND PARENT_QUOTA_CLASS = #{parentQuotaClass}
			</if>
			<if test="individualFlag != null ">
				AND INDIVIDUAL_FLAG = #{individualFlag}
			</if>
			<if test="stageCode != null">
				AND STAGE_CODE = #{stageCode}
			</if>
			<if test="issueYear != null">
				AND ISSUE_YEAR = #{issueYear}
			</if>
			<if test="branch != null">
				AND BRANCH = #{branch}
			</if>
			<if test="ccy != null">
				AND CCY = #{ccy}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</update>

	<select id="stageQuotaSum" parameterType="java.util.Map" resultType="java.math.BigDecimal">
		SELECT
			SUM(a.TOTAL_QUOTA)
		FROM
			RB_DC_STAGE_QUOTA a,RB_DC_STAGE_DEFINE b
		WHERE
			b.STAGE_STATUS != 'E' AND b.STAGE_STATUS != 'D'
			AND b.STAGE_PROD_CLASS = 'DC'
			AND b.PROD_TYPE = '14006'
			AND a.STAGE_CODE = b.STAGE_CODE
			<if test="branch != null and branch != ''">
				AND a.BRANCH = #{branch}
			</if>
	</select>

</mapper>