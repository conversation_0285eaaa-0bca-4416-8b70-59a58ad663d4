<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchChargeFee">
  <select id="getSumTranAmtByBatchNo" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM(TRAN_AMT)
    from RB_BATCH_CHARGE_FEE
    where CONTRAST_BAT_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getChargeFeeInfoForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchChargeFee">
    SELECT <include refid="Base_Column"/>
    FROM RB_BATCH_CHARGE_FEE
    WHERE BATCH_NO = #{batchNo}
    AND SEQ_NO = #{seqNo}
    AND BATCH_FILE_STATUS = #{batchFileStatus} FOR UPDATE
  </select>
</mapper>
