<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpLimitCumulative">

  <update id="updateMbPcpLimitCumulative" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpLimitCumulative">
    update RB_PCP_LIMIT_CUMULATIVE
    <set>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="crDrMaintInd != null">
        #{crDrMaintInd},
      </if>
      <if test="limitAmt != null">
        #{limitAmt},
      </if>
      <if test="limitNum != null">
        #{limitNum},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND   LIMIT_TYPE = #{limitType}
    <if test="clientNo != null">
        AND CLIENT_NO =  #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertMbPcpLimitCumulative" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpLimitCumulative">
    insert into RB_PCP_LIMIT_CUMULATIVE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="limitType != null">
        LIMIT_TYPE,
      </if>
      <if test="crDrMaintInd != null">
        CR_DR_MAINT_IND,
      </if>
      <if test="limitAmt != null">
        LIMIT_AMT,
      </if>
      <if test="limitNum != null">
        LIMIT_NUM,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="clientNo != null">
        CLIENT_NO ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="limitType != null">
        #{limitType},
      </if>
      <if test="crDrMaintInd != null">
        #{crDrMaintInd},
      </if>
      <if test="limitAmt != null">
        #{limitAmt},
      </if>
      <if test="limitNum != null">
        #{limitNum},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
    </trim>
  </insert>
  <select id="getMbPcpLimitCumulative" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpLimitCumulative">
    select
    <include refid="Base_Column"/>
    from RB_PCP_LIMIT_CUMULATIVE
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null">
      AND CLIENT_NO =  #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbPcpLimitCumulativeByLimitType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpLimitCumulative">
    select
    <include refid="Base_Column"/>
    from RB_PCP_LIMIT_CUMULATIVE
    where INTERNAL_KEY = #{internalKey}
    AND LIMIT_TYPE = #{limitType}
    <if test="clientNo != null">
      AND CLIENT_NO =  #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteMbPcpLimitCumulative" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpLimitCumulative">
    delete from RB_PCP_LIMIT_CUMULATIVE
    where INTERNAL_KEY = #{internalKey}
    <if test="limitType != null">
      AND LIMIT_TYPE = #{limitType}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO =  #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
</mapper>
