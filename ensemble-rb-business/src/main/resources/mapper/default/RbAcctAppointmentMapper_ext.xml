<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAppointment">

	<select id="selectByDueAndDate" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAppointment">
		select
		<include refid="Base_Column"/>
		from RB_ACCT_APPOINTMENT
		<where>
			<if test="applyId != null and  applyId != '' ">
				AND APPLY_ID = #{applyId}
			</if>
			<if test="clientName != null and  clientName != '' ">
				AND CLIENT_NAME = #{clientName}
			</if>
			<if test="startDate != null and endDate != null ">
				AND TRAN_DATE BETWEEN #{startDate} AND #{endDate}
			</if>
			<if test="baseAcctNo != null and  baseAcctNo != '' ">
				AND BASE_ACCT_NO = #{baseAcctNo}
			</if>
			<if test="appointmentStatus != null and  appointmentStatus != '' ">
				AND APPOINTMENT_STATUS = #{appointmentStatus}
			</if>
			<!--多法人改造 by LIYUANV-->
			<if test="company != null and company.length() > 0">
				AND COMPANY = #{company}
			</if>
		</where>
	</select>

</mapper>
