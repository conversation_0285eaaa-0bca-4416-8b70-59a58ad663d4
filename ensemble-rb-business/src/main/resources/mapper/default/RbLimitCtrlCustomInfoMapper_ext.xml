<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitCtrlCustomInfo">

    <select id="getLimitCtrlCustomDetailList" resultType="com.dcits.ensemble.rb.limit.business.model.LimitScene">
        SELECT
            DEF.LIMIT_SCENE_DESC,
            DEF.LIMIT_CONVERT,
            DEF.VALID_FLAG,
            DEF.LIMIT_SCENE_NO,
            DEF.CHECK_OBJ_TYPE,
            DEF.LIMIT_MAIN_TYPE,
            CUST.LIMIT_CTRL_AMT,
            CUST.LIMIT_CTRL_NUM,
            CUST.CTRL_ITEM_TYPE,
            CUST.CHECK_OBJ_VAL,
            CUST.EFFECT_DATE,
            CUST.EXPIRE_DATE,
            CUST.CLIENT_NO
        FROM RB_LIMIT_CTRL_CUSTOM_INFO CUST
        LEFT JOIN RB_LIMIT_SCENE_DEF DEF
            ON CUST.LIMIT_SCENE_NO = DEF.LIMIT_SCENE_NO
        WHERE
            CUST.CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            <if test="limitSceneNo != null and  limitSceneNo != '' ">
                AND CUST.LIMIT_SCENE_NO = #{limitSceneNo,jdbcType=VARCHAR}
            </if>
            <if test="checkObjVal != null and  checkObjVal != '' ">
                AND CUST.CHECK_OBJ_VAL = #{checkObjVal,jdbcType=VARCHAR}
            </if>
            <if test="ctrlItemType != null and  ctrlItemType != '' ">
                AND CUST.CTRL_ITEM_TYPE = #{ctrlItemType,jdbcType=VARCHAR}
            </if>
            <if test="limitMainType != null and  limitMainType != '' ">
                AND DEF.LIMIT_MAIN_TYPE = #{limitMainType,jdbcType=VARCHAR}
            </if>
    </select>

    <select id="getLimitCustomInfoByBaseAcctNoOrCardNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitCtrlCustomInfo">
        SELECT
        CLIENT_NO,
        LIMIT_SCENE_NO,
        CHECK_OBJ_VAL,
        CTRL_ITEM_TYPE,
        LIMIT_CTRL_NUM,
        LIMIT_CTRL_AMT,
        TEMP_LIMIT_FLAG,
        EFFECT_DATE,
        EXPIRE_DATE,
        COMPANY,
        TRAN_TIMESTAMP,
        UPDATE_DATE,
        CREATE_DATE,
        CREATE_TIMESTAMP,
        LAST_CHANGE_DATE
        FROM RB_LIMIT_CTRL_CUSTOM_INFO
        WHERE
        <if test="clientNo != null and clientNo != '' ">
            CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        <if test="limitSceneNo != null and limitSceneNo != '' ">
            AND LIMIT_SCENE_NO = #{limitSceneNo,jdbcType=VARCHAR}
        </if>
        <if test="cardNo != null and cardNo != '' ">
            AND ( CHECK_OBJ_VAL = #{cardNo,jdbcType=VARCHAR}
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                OR CHECK_OBJ_VAL = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            )
        </if>
    </select>

    <select id="updateRbLimitCustomInfoByParam" parameterType="java.util.Map">
        UPDATE RB_LIMIT_CTRL_CUSTOM_INFO
        <set>
            <if test="newLimitSceneNo != null and newLimitSceneNo != '' ">
                LIMIT_SCENE_NO = #{newLimitSceneNo},
            </if>
            <if test="newCheckObjVal != null and newCheckObjVal != '' ">
                CHECK_OBJ_VAL = #{newCheckObjVal}
            </if>
        </set>
        <where>
            LIMIT_SCENE_NO = #{limitSceneNo}
            AND CLIENT_NO = #{clientNo}
            AND CHECK_OBJ_VAL = #{checkObjVal}
        </where>
    </select>

</mapper>
