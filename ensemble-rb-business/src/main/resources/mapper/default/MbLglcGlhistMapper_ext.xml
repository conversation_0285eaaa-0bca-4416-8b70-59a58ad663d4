<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
    <!-- Created by <PERSON><PERSON><PERSON><PERSON> on 2017/09/12  -->
    <sql id="Base_Column_List">
  PROD_TYPE,
  OPTION_TYPE,
  TRADE_NO,
  REFERENCE,
  REVERSEL,
  CHANNEL_SEQ_NO,
  TRAN_BRANCH,
  OPER_USER_ID,
  CLIENT_NO,
  TRAN_PROFIT_CENTRE,
  NARRATIVE,
  TRAN_DATE,
  BANK_SEQ_NO,
  BRANCH,
  AMT_TYPE,
  AMOUNT,
  TRAN_CCY,
  GL_POSTED,
  SYSTEM_ID,
  SOURCE_MODULE,
  SOURCE_TYPE,
  PROFIT_CENTRE,
  EVENT_TYPE,
  TRAN_TYPE,
  TRAN_NO
  </sql>
    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        select
        <include refid="Base_Column_List"/>
        from MB_LGLC_GLHIST
        where TRADE_NO = #{tradeNo}
    </select>

    <select id="selectByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        select
        <include refid="Base_Column_List"/>
        from MB_LGLC_GLHIST
        where REFERENCE = #{reference}
        and REVERSEL != 'Y'
    </select>

    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
    delete from MB_LGLC_GLHIST
    where REFERENCE = #{reference}
  </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        update MB_LGLC_GLHIST
        <set>
            <if test="prodType != null">
                PROD_TYPE = #{prodType},
            </if>
            <if test="optionType != null">
                OPTION_TYPE = #{optionType},
            </if>
            <if test="tradeNo != null">
                TRADE_NO = #{tradeNo},
            </if>
            <if test="reversel != null">
                REVERSEL = #{reversel},
            </if>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO = #{channelSeqNo},
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="operUserId != null">
                OPER_USER_ID = #{operUserId},
            </if>
            <if test="clientNo != null">
                CLIENT_NO = #{clientNo},
            </if>
            <if test="tranProfitCenter != null">
                TRAN_PROFIT_CENTRE = #{tranProfitCenter},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO = #{bankSeqNo},
            </if>
            <if test="branch != null">
                BRANCH = #{branch},
            </if>
            <if test="amtType != null">
                AMT_TYPE = #{amtType},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount},
            </if>
            <if test="tranCcy != null">
                TRAN_CCY = #{tranCcy},
            </if>
            <if test="glPosted != null">
                GL_POSTED = #{glPosted},
            </if>
            <if test="systemId != null">
                SYSTEM_ID = #{systemId},
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE = #{sourceModule},
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE = #{sourceType},
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTRE = #{profitCenter},
            </if>
            <if test="eventType != null">
                EVENT_TYPE = #{eventType},
            </if>
            <if test="tranType != null">
                TRAN_TYPE = #{tranType}
            </if>
            <if test="tranNo != null">
                TRAN_NO = #{tranNo}
            </if>
        </set>
        where REFERENCE = #{reference}
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        insert into MB_LGLC_GLHIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="optionType != null">
                OPTION_TYPE,
            </if>
            <if test="tradeNo != null">
                TRADE_NO,
            </if>
            <if test="reversel != null">
                REVERSEL,
            </if>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="operUserId != null">
                OPER_USER_ID,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="tranProfitCenter != null">
                TRAN_PROFIT_CENTRE,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO,
            </if>
            <if test="branch != null">
                BRANCH,
            </if>
            <if test="amtType != null">
                AMT_TYPE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="tranCcy != null">
                TRAN_CCY,
            </if>
            <if test="glPosted != null">
                GL_POSTED,
            </if>
            <if test="systemId != null">
                SYSTEM_ID,
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE,
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE,
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTRE,
            </if>
            <if test="eventType != null">
                EVENT_TYPE,
            </if>
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="tranNo != null">
                TRAN_NO,
            </if>
            <!--多法人改造 by LIYUANV-->
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <!--多法人改造 by LIYUANV-->
            <if test="company != null">
                #{company},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="optionType != null">
                #{optionType},
            </if>
            <if test="tradeNo != null">
                #{tradeNo},
            </if>
            <if test="reversel != null">
                #{reversel},
            </if>
            <if test="channelSeqNo != null">
                #{channelSeqNo},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="operUserId != null">
                #{operUserId},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="tranProfitCenter != null">
                #{tranProfitCenter},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="bankSeqNo != null">
                #{bankSeqNo},
            </if>
            <if test="branch != null">
                #{branch},
            </if>
            <if test="amtType != null">
                #{amtType},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="tranCcy != null">
                #{tranCcy},
            </if>
            <if test="glPosted != null">
                #{glPosted},
            </if>
            <if test="systemId != null">
                #{systemId},
            </if>
            <if test="sourceModule != null">
                #{sourceModule},
            </if>
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="profitCenter != null">
                #{profitCenter},
            </if>
            <if test="eventType != null">
                #{eventType},
            </if>
            <if test="tranType != null">
                #{tranType},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="tranNo != null">
                #{tranNo},
            </if>
        </trim>
    </insert>
    <select id="selectByTradeNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        select
        <include refid="Base_Column_List"/>
        from(
        select
        <include refid="Base_Column_List"/>
        from MB_LGLC_GLHIST
        where TRADE_NO = #{tradeNo} order by tran_no desc
        )
    </select>
    <select id="selectMbLglcSplitOB" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(tran_no) START_KEY,
            MAX(tran_no) END_KEY,COUNT(*) ROW_COUNT
            FROM
            (
            SELECT
            tran_no,
            @rownum :=@rownum + 1 AS rownum
            FROM (SELECT DISTINCT tran_no FROM MB_LGLC_GLHIST,
            (SELECT @rownum := -1) t
            where tran_date = #{runDate}
            AND gl_posted = 'N'
            ORDER BY tran_no
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (tran_no) START_KEY, MAX (tran_no) END_KEY,COUNT(*) ROW_COUNT
            FROM (SELECT DISTINCT tran_no
            from MB_LGLC_GLHIST
            where tran_date = #{runDate}
            AND gl_posted = 'N'
            ORDER BY tran_no)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>
    <select id="selectForExt" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        select
        <include refid="Base_Column_List"/>
        from MB_LGLC_GLHIST
        where GL_POSTED = 'N'
        and TRAN_DATE BETWEEN #{fromDate} AND #{toDate}
        and TRAN_NO BETWEEN #{startKey} AND #{endKey}
    </select>
    <select id="selectMbLglcSplit" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(tran_no) START_KEY,
            MAX(tran_no) END_KEY,COUNT(*) ROW_COUNT
            FROM
            (
            SELECT
            tran_no,
            @rownum :=@rownum + 1 AS rownum
            FROM (SELECT DISTINCT tran_no FROM MB_LGLC_GLHIST,
            (SELECT @rownum := -1) t
            where tran_date BETWEEN #{lastRunDate} AND #{yesterday}
            AND gl_posted = 'N'
            ORDER BY tran_no
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (tran_no) START_KEY, MAX (tran_no) END_KEY,COUNT(*) ROW_COUNT
            FROM (SELECT DISTINCT tran_no
            from MB_LGLC_GLHIST
            where tran_date BETWEEN #{lastRunDate} AND #{yesterday}
            AND gl_posted = 'N'
            ORDER BY tran_no)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>
    <select id="selectForEod" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        select
        <include refid="Base_Column_List"/>
        from MB_LGLC_GLHIST
        where GL_POSTED = 'N'
        and tran_date BETWEEN #{lastRunDate} AND #{yesterday}
        AND TRAN_NO BETWEEN #{startKey} and #{endKey}
        ORDER BY TRAN_NO
    </select>
    <select id="queryAutoLglcForKeys" parameterType="java.util.Map" resultType="java.lang.String">
    select TRAN_NO
    from MB_LGLC_GLHIST
    where GL_POSTED = 'N'
    and tran_date BETWEEN #{lastRunDate} AND #{yesterday}
    and TRAN_NO BETWEEN #{startKey} AND #{endKey}
    ORDER BY TRAN_NO
  </select>
    <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
    update MB_LGLC_GLHIST set gl_posted='Y'   where TRAN_NO = #{tranNo}
  </update>
    <select id="selectMbLgLcHistByDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        select
        <include refid="Base_Column_List"/>
        from MB_LGLC_GLHIST
        where prod_type not in('11E','11P')
        AND TRAN_DATE = #{tranDate}
        order BY TRAN_TIMESTAMP asc
    </select>

    <select id="selectMbLgLcHistByDateSplit" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'oracle'">
            SELECT MIN (REFERENCE) START_KEY, MAX (REFERENCE) END_KEY ,count(1) ROW_COUNT FROM (
            select
            REFERENCE
            from MB_LGLC_GLHIST
            where prod_type not in('11E','11P')
            AND TRAN_DATE = #{runDate}
            order BY TRAN_TIMESTAMP asc)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>

    <select id="selectMbLgLcHistByDateForKeys" parameterType="java.util.Map" resultType="String">
    SELECT  REFERENCE
    FROM MB_LGLC_GLHIST
    where prod_type not in('11E','11P')
    AND TRAN_DATE = #{runDate}
    and REFERENCE BETWEEN #{startKey} and #{endKey}
    ORDER BY TRAN_TIMESTAMP asc
  </select>


    <select id="selectMbLgLcHistByDateDetails" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist">
        select
        <include refid="Base_Column_List"/>
        from MB_LGLC_GLHIST
        where prod_type not in('11E','11P')
        AND TRAN_DATE = #{runDate}
        and REFERENCE BETWEEN #{startKey} and #{endKey}
        order BY TRAN_TIMESTAMP asc
    </select>
</mapper>
