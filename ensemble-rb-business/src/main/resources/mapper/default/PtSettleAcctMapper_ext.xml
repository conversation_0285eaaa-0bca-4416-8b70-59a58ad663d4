<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.PtSettleAcct">
  <!-- Created by admin on 2017/10/12 16:08:36. -->
  <sql id="Base_Column_List">
    CHANNEL_FLAG,
    BRANCH,
    CLIENT_NO,
    CCY,
    PROFIT_CENTRE,
    GL_CODE,
    SEQ_NO,
    ACCT_NO
  </sql>

  <select id="selectInfoMsg" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.PtSettleAcct" parameterType="java.util.Map" >
    SELECT <include refid="Base_Column_List"/>
    FROM pt_settle_acct
    WHERE channel_flag = #{channelFlag}
    <if test="branch != null and branch!=''">
      AND branch = #{branch}
    </if>
    <if test="clientNo != null and clientNo!=''">
      AND client_no = #{clientNo}
    </if>
    <if test="glCode != null and glCode!=''">
      AND gl_code = #{glCode}
    </if>
    <if test="ccy != null and ccy!=''">
      AND ccy = #{ccy}
    </if>
    <if test="profitCenter != null and profitCenter!=''">
      AND profit_centre = #{profitCenter}
    </if>
    <if test="seqNo != null and seqNo!=''">
      AND seq_no = #{seqNo}
    </if>
  </select>
</mapper>
