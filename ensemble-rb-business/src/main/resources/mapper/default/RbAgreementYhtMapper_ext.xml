<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">

    <select id="getMbAgreementYht" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
            <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE AGREEMENT_STATUS = 'A'
        <if test="baseAcctNo!=null and  baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy!=null  and  acctCcy != '' ">
            AND ACCT_CCY = #{acctCcy}
        </if>
        <if test="acctSeqNo!=null and acctSeqNo != ''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="prodType!=null and prodType != ''">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="clientNo!=null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbYhtByAgreement1" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
            <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE AGREEMENT_ID = #{agreementId}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMainByMainAgreementId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE MAIN_AGREEMENT_ID = #{mainagreementId}
        AND YHT_ACCT_FLAG = 'A'
        AND AGREEMENT_STATUS = 'A'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbYhtByAgreement" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
            <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE PARENT_INTERNAL_KEY = (
            SELECT agreement_key
            from RB_AGREEMENT
            where agreement_id = #{agreementId}
             AND AGREEMENT_STATUS = 'A'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        )
    </select>
    <select id="getByParentInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE PARENT_INTERNAL_KEY = #{parentInternalKey}
        AND AGREEMENT_STATUS = 'A'
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY BASE_ACCT_NO
    </select>

    <select id="getSelfAcctAndMyself" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE
        AGREEMENT_STATUS = 'A' AND(
        INTERNAL_KEY = #{internalKey}
        OR
        BASE_ACCT_NO = CONCAT('${baseAcctNo}','-0000')
        )
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY BASE_ACCT_NO
    </select>





    <select id="getInvalidByParent" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE PARENT_INTERNAL_KEY = #{parentInternalKey}
        AND AGREEMENT_STATUS = 'E'
        <if test="baseAcctNo!=null">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY BASE_ACCT_NO
    </select>
    <select id="getOldBaseAcctNoInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE AGREEMENT_STATUS = 'E'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="baseAcctNo!=null">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy!=null">
            AND ACCT_CCY = #{acctCcy}
        </if>
        <if test="acctSeqNo!=null">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="prodType!=null">
            AND PROD_TYPE = #{prodType}
        </if>

    </select>
    <update id="updateMaxSeqNoByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        update RB_AGREEMENT_YHT
        <set>
            <if test="nextMaxSeqNo != null">
                NEXT_MAX_SEQ_NO = #{nextMaxSeqNo},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="getByInternalKey" parameterType="java.util.Map"
                     resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE INTERNAL_KEY = #{internalKey}
            <if test="clientNo!=null">
                AND CLIENT_NO = #{clientNo}
            </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND AGREEMENT_STATUS = 'A'
    </select>
    <select id="getMainByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE AGREEMENT_ID = #{agreementId}
        AND YHT_ACCT_FLAG = #{yhtAcctFalg}
        AND AGREEMENT_STATUS = 'A'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>
    <select id="getByMainAgreementId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE MAIN_AGREEMENT_ID = #{mainAgreementId}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND AGREEMENT_STATUS = 'A'
    </select>
    <select id="getByMainAgreementIdInd" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE MAIN_AGREEMENT_ID = #{mainAgreementId}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND AGREEMENT_STATUS = 'A'
        AND INT_FLAG='Y'
    </select>


    <select id="getByBaseAcctNoLike" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE BASE_ACCT_NO LIKE '${baseAcctNo}%'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        AND AGREEMENT_STATUS = 'A'
        AND YHT_ACCT_FLAG = 'B'
        AND ACCT_REAL_FLAG != 'Y'
    </select>




    <select id="getSelfAgreementYht" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE SELF_FLAG = 'Y'
            AND AGREEMENT_STATUS = 'A'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="parentInternalKey!=null">
            AND PARENT_INTERNAL_KEY = #{parentInternalKey}
        </if>
        <if test="clientNo!=null">
            AND CLIENT_NO = #{clientNo}
        </if>
    </select>

    <select id="getBySelfInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE  AGREEMENT_STATUS = 'A'
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="selfFlag != null and selfFlag.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY BASE_ACCT_NO
    </select>
    <select id="getSonListByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE PARENT_INTERNAL_KEY = #{parentInternalKey}
        AND AGREEMENT_STATUS = 'A'
        AND SELF_FLAG = 'N'
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="lawImpRule != null and lawImpRule.length() > 0 and lawImpRule in {'1'.toString(),'3'.toString()}">
            ORDER BY BASE_ACCT_NO ASC
        </if>
        <if test="lawImpRule != null and lawImpRule.length() > 0 and lawImpRule == '2'.toString()">
            ORDER BY BASE_ACCT_NO desc
        </if>

    </select>
    <select id="getOpenMbAgreementYht" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE AGREEMENT_STATUS = 'A'
        <if test="baseAcctNo!=null and  baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy!=null  and  acctCcy != '' ">
            AND ACCT_CCY = #{acctCcy}
        </if>
        <if test="acctSeqNo!=null and acctSeqNo != ''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="prodType!=null and prodType != ''">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="clientNo!=null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="acctOrgSchema !=null and acctOrgSchema != ''">
            AND YHT_ACCT_ORG_SCHEMA = #{acctOrgSchema}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getBranchMbAgreementYht" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT a
        WHERE AGREEMENT_STATUS = 'A'
        AND SELF_FLAG = 'Y'
        AND a.internal_key in (select internal_key from rb_acct b where a.internal_key=b.internal_key
        <if test="acctBranch != null and acctBranch.length() > 0">
            AND b.acct_branch = #{acctBranch}
        </if>
        )
        AND a.internal_key in (select internal_key from rb_acct_balance c where a.internal_key=c.internal_key
        and c.total_amount > 0)
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbAgreementYhtE" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht">
        select
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT_YHT
        WHERE 1=1
        <if test="agreementStatus != null and  agreementStatus != ''">
            AND AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <if test="baseAcctNo!=null and  baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy!=null  and  acctCcy != '' ">
            AND ACCT_CCY = #{acctCcy}
        </if>
        <if test="acctSeqNo!=null and acctSeqNo != ''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="prodType!=null and prodType != ''">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="clientNo!=null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
