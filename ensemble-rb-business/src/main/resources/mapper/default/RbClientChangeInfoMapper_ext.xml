<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChangeInfo">
    <update id="updateStatus"  parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChangeInfo">
        UPDATE <include refid="Table_Name" />
        <set>
            <if test="clientChangeStatus != null and  clientChangeStatus != '' ">
                CLIENT_CHANGE_STATUS = #{clientChangeStatus},
            </if>
        </set>
        where CLIENT_NO = #{clientNo}
        AND SEQ_NO = #{seqNo}
        <if test="levelUprightFlag != null and levelUprightFlag != '' ">
            AND LEVEL_UPRIGHT_FLAG = #{levelUprightFlag}
        </if>
        AND TABLE_NAME = #{tableName}
        AND CLIENT_CHANGE_OPERATE_TYPE = #{clientChangeOperateType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <update id="updateTableByClient" parameterType="java.util.Map">
        update ${tableName}
        <set>
            <if test="newClientNo != null " >
                CLIENT_NO = #{newClientNo},
            </if>
        </set>
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="updateTableDocumentByClient" parameterType="java.util.Map">
        update ${tableName}
        <set>
            <if test="newClientNo != null " >
                CLIENT_NO = #{newClientNo},
            </if>
            <if test="documentId != null " >
                DOCUMENT_ID = #{documentId},
            </if>
            <if test="documentType != null " >
                DOCUMENT_TYPE = #{documentType},
            </if>
            <if test="acctName != null " >
                ACCT_NAME = #{acctName},
            </if>
            <if test="altAcctName != null " >
                ALT_ACCT_NAME = #{altAcctName},
            </if>
        </set>
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="internalKey != null ">
            AND internal_Key = #{internalKey}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="updateTableByInternalKey" parameterType="java.util.Map">
        update ${tableName}
        <set>
            <if test="newClientNo != null " >
                CLIENT_NO = #{newClientNo},
            </if>
        </set>
        where CLIENT_NO = #{clientNo}
        and INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <delete id="deleteTableByClientNo" parameterType="java.util.Map">
        DELETE FROM ${tableName}
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <delete id="deleteTableByClientNoInternalKey" parameterType="java.util.Map">
        DELETE FROM ${tableName}
        where CLIENT_NO = #{clientNo}
        and INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <select id="getClientChange" resultMap="Base_Result_Map" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
        <if test="seqNo != null and  seqNo != '' ">
            AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
        </if>
        <if test="clientChangeOperateType != null and  clientChangeOperateType != '' ">
            AND CLIENT_CHANGE_OPERATE_TYPE = #{clientChangeOperateType,jdbcType=VARCHAR}
        </if>
        <if test="mergeNo != null and  mergeNo != '' ">
            AND merge_No = #{mergeNo}
        </if>
        <if test="clientChangeStatus != null and  clientChangeStatus != '' ">
            AND CLIENT_CHANGE_STATUS = #{clientChangeStatus,jdbcType=VARCHAR}
        </if>
        <if test="tableName != null and  tableName != '' ">
            AND	TABLE_NAME = #{tableName}
        </if>
        <if test="clientNo != null and  clientNo != '' ">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>

    <update id="updateTableByInternalKeyAndClientNo" parameterType="java.util.Map">
        update ${tableName}
        <set>
            <if test="newClientNo != null " >
                CLIENT_NO = #{newClientNo},
            </if>
        </set>
        where CLIENT_NO = #{clientNo}
        and (
        ${amendKey} = #{internalKey}
        or ${amendKey} = #{baseAcctNo}
        <if test="cardNo != null and  cardNo != '' ">
            or	${amendKey}  = #{cardNo}
        </if>
        )
    </update>

    <delete id="deleteTableByClientNoAmendKey" parameterType="java.util.Map">
        DELETE FROM ${tableName}
        where CLIENT_NO = #{clientNo}
        and (
        ${amendKey} = #{internalKey}
        or  ${amendKey} = #{baseAcctNo}
        <if test="cardNo != null and  cardNo != '' ">
            or	${amendKey}  = #{cardNo}
        </if>
        )

    </delete>

    <select id="selectNoSuccess" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChangeInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChangeInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        where SEQ_NO = #{seqNo,jdbcType=VARCHAR}
        AND CLIENT_CHANGE_STATUS != 'S'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateTableDocumentByBaseNoOne" parameterType="java.util.Map">
        update ${tableName}
        <set>
            <if test="newClientNo != null " >
                CLIENT_NO = #{newClientNo},
            </if>
            <if test="documentId != null " >
                DOCUMENT_ID = #{documentId},
            </if>
            <if test="documentType != null " >
                DOCUMENT_TYPE = #{documentType},
            </if>
            <if test="acctName != null " >
                ACCT_NAME = #{acctName},
            </if>
            <if test="altAcctName != null " >
                ALT_ACCT_NAME = #{altAcctName},
            </if>
        </set>
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="baseAcctNo != null ">
            AND base_acct_no = #{baseAcctNo}
        </if>
        <if test="cardNo != null ">
            AND card_no = #{cardNo}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

</mapper>
