<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">

  <!-- Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/05/10 14:18:30. -->
  <!--<select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">-->
    <!--select-->
    <!--<include refid="Base_Column"/>-->
    <!--from OC_CHANGE_BRANCH-->
    <!--where TRAN_BRANCH = #{tranBranch}-->
  <!--</select>-->
  <!--<delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">-->
    <!--delete from OC_CHANGE_BRANCH-->
    <!--where TRAN_BRANCH = #{tranBranch}-->
  <!--</delete>-->
  <!--<update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">-->
    <!--update OC_CHANGE_BRANCH-->
    <!--<set>-->
      <!--<if test="changeBranch != null">-->
        <!--CHANGE_BRANCH = #{changeBranch},-->
      <!--</if>-->
      <!--<if test="settleBranch != null">-->
        <!--SETTLE_BRANCH = #{settleBranch},-->
      <!--</if>-->
      <!--<if test="changeRegion != null">-->
        <!--CHANGE_REGION = #{changeRegion},-->
      <!--</if>-->
      <!--<if test="changeNo != null">-->
        <!--CHANGE_NO = #{changeNo},-->
      <!--</if>-->
      <!--<if test="settleAcctNo != null">-->
        <!--SETTLE_ACCT_NO = #{settleAcctNo},-->
      <!--</if>-->
      <!--<if test="settleProdType != null">-->
        <!--SETTLE_PROD_TYPE = #{settleProdType},-->
      <!--</if>-->
      <!--<if test="settleCcy != null">-->
        <!--SETTLE_CCY = #{settleCcy},-->
      <!--</if>-->
      <!--<if test="settleSeqNo != null">-->
        <!--SETTLE_SEQ_NO = #{settleSeqNo},-->
      <!--</if>-->
      <!--<if test="branchType != null">-->
        <!--BRANCH_TYPE = #{branchType},-->
      <!--</if>-->
      <!--<if test="changeBranchType != null">-->
        <!--CHANGE_BRANCH_TYPE = #{changeBranchType},-->
      <!--</if>-->
      <!--<if test="branchName != null">-->
        <!--BRANCH_NAME = #{branchName},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP = #{tranTimestamp},-->
      <!--</if>-->
      <!--<if test="ocCheckStatus != null">-->
        <!--OC_CHECK_STATUS = #{ocCheckStatus},-->
      <!--</if>-->
      <!--<if test="tempAcctNo != null">-->
        <!--TEMP_ACCT_NO = #{tempAcctNo},-->
      <!--</if>-->
      <!--<if test="tempProdType != null">-->
        <!--TEMP_PROD_TYPE = #{tempProdType},-->
      <!--</if>-->
      <!--<if test="tempCcy != null">-->
        <!--TEMP_CCY = #{tempCcy},-->
      <!--</if>-->
      <!--<if test="tempSeqNo != null">-->
        <!--TEMP_SEQ_NO = #{tempSeqNo},-->
      <!--</if>-->
      <!--<if test="usedAcctNo != null">-->
        <!--USED_ACCT_NO = #{usedAcctNo},-->
      <!--</if>-->
      <!--<if test="usedProdType != null">-->
        <!--USED_PROD_TYPE = #{usedProdType},-->
      <!--</if>-->
      <!--<if test="usedCcy != null">-->
        <!--USED_CCY = #{usedCcy},-->
      <!--</if>-->
      <!--<if test="usedSeqNo != null">-->
        <!--USED_SEQ_NO = #{usedSeqNo},-->
      <!--</if>-->
      <!--<if test="budgetAcctNo != null">-->
        <!--BUDGET_ACCT_NO = #{budgetAcctNo},-->
      <!--</if>-->
      <!--<if test="budgetProdType != null">-->
        <!--BUDGET_PROD_TYPE = #{budgetProdType},-->
      <!--</if>-->
      <!--<if test="budgetCcy != null">-->
        <!--BUDGET_CCY = #{budgetCcy},-->
      <!--</if>-->
      <!--<if test="budgetSeqNo != null">-->
        <!--BUDGET_SEQ_NO = #{budgetSeqNo},-->
      <!--</if>-->
      <!--<if test="transitAcctNo != null">-->
        <!--TRANSIT_ACCT_NO = #{transitAcctNo},-->
      <!--</if>-->
      <!--<if test="transitProdType != null">-->
        <!--TRANSIT_PROD_TYPE = #{transitProdType},-->
      <!--</if>-->
      <!--<if test="transitCcy != null">-->
        <!--TRANSIT_CCY = #{transitCcy},-->
      <!--</if>-->
      <!--<if test="transitSeqNo != null">-->
        <!--TRANSIT_SEQ_NO = #{transitSeqNo},-->
      <!--</if>-->
      <!--<if test="changeRegionName != null">-->
        <!--CHANGE_REGION_NAME = #{changeRegionName}-->
      <!--</if>-->
    <!--</set>-->
    <!--where TRAN_BRANCH = #{tranBranch}-->
  <!--</update>-->
  <!--<insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">-->
    <!--insert into OC_CHANGE_BRANCH-->
    <!--<trim prefix="(" suffix=")" suffixOverrides=",">-->
      <!--<if test="tranBranch != null">-->
        <!--TRAN_BRANCH,-->
      <!--</if>-->
      <!--<if test="changeBranch != null">-->
        <!--CHANGE_BRANCH,-->
      <!--</if>-->
      <!--<if test="settleBranch != null">-->
        <!--SETTLE_BRANCH,-->
      <!--</if>-->
      <!--<if test="changeRegion != null">-->
        <!--CHANGE_REGION,-->
      <!--</if>-->
      <!--<if test="changeNo != null">-->
        <!--CHANGE_NO,-->
      <!--</if>-->
      <!--<if test="settleAcctNo != null">-->
        <!--SETTLE_ACCT_NO,-->
      <!--</if>-->
      <!--<if test="settleProdType != null">-->
        <!--SETTLE_PROD_TYPE,-->
      <!--</if>-->
      <!--<if test="settleCcy != null">-->
        <!--SETTLE_CCY,-->
      <!--</if>-->
      <!--<if test="settleSeqNo != null">-->
        <!--SETTLE_SEQ_NO,-->
      <!--</if>-->
      <!--<if test="branchType != null">-->
        <!--BRANCH_TYPE,-->
      <!--</if>-->
      <!--<if test="changeBranchType != null">-->
        <!--CHANGE_BRANCH_TYPE,-->
      <!--</if>-->
      <!--<if test="branchName != null">-->
        <!--BRANCH_NAME,-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP,-->
      <!--</if>-->
      <!--<if test="ocCheckStatus != null">-->
        <!--OC_CHECK_STATUS,-->
      <!--</if>-->
      <!--<if test="tempAcctNo != null">-->
        <!--TEMP_ACCT_NO,-->
      <!--</if>-->
      <!--<if test="tempProdType != null">-->
        <!--TEMP_PROD_TYPE,-->
      <!--</if>-->
      <!--<if test="tempCcy != null">-->
        <!--TEMP_CCY,-->
      <!--</if>-->
      <!--<if test="tempSeqNo != null">-->
        <!--TEMP_SEQ_NO,-->
      <!--</if>-->
      <!--<if test="usedAcctNo != null">-->
        <!--USED_ACCT_NO,-->
      <!--</if>-->
      <!--<if test="usedProdType != null">-->
        <!--USED_PROD_TYPE,-->
      <!--</if>-->
      <!--<if test="usedCcy != null">-->
        <!--USED_CCY,-->
      <!--</if>-->
      <!--<if test="usedSeqNo != null">-->
        <!--USED_SEQ_NO,-->
      <!--</if>-->
      <!--<if test="budgetAcctNo != null">-->
        <!--BUDGET_ACCT_NO,-->
      <!--</if>-->
      <!--<if test="budgetProdType != null">-->
        <!--BUDGET_PROD_TYPE,-->
      <!--</if>-->
      <!--<if test="budgetCcy != null">-->
        <!--BUDGET_CCY,-->
      <!--</if>-->
      <!--<if test="budgetSeqNo != null">-->
        <!--BUDGET_SEQ_NO,-->
      <!--</if>-->
      <!--<if test="transitAcctNo != null">-->
        <!--TRANSIT_ACCT_NO,-->
      <!--</if>-->
      <!--<if test="transitProdType != null">-->
        <!--TRANSIT_PROD_TYPE,-->
      <!--</if>-->
      <!--<if test="transitCcy != null">-->
        <!--TRANSIT_CCY,-->
      <!--</if>-->
      <!--<if test="transitSeqNo != null">-->
        <!--TRANSIT_SEQ_NO,-->
      <!--</if>-->
      <!--<if test="changeRegionName != null">-->
        <!--CHANGE_REGION_NAME-->
      <!--</if>-->
    <!--</trim>-->
    <!--<trim prefix="values (" suffix=")" suffixOverrides=",">-->
      <!--<if test="tranBranch != null">-->
        <!--#{tranBranch},-->
      <!--</if>-->
      <!--<if test="changeBranch != null">-->
        <!--#{changeBranch},-->
      <!--</if>-->
      <!--<if test="settleBranch != null">-->
        <!--#{settleBranch},-->
      <!--</if>-->
      <!--<if test="changeRegion != null">-->
        <!--#{changeRegion},-->
      <!--</if>-->
      <!--<if test="changeNo != null">-->
        <!--#{changeNo},-->
      <!--</if>-->
      <!--<if test="settleAcctNo != null">-->
        <!--#{settleAcctNo},-->
      <!--</if>-->
      <!--<if test="settleProdType != null">-->
        <!--#{settleProdType},-->
      <!--</if>-->
      <!--<if test="settleCcy != null">-->
        <!--#{settleCcy},-->
      <!--</if>-->
      <!--<if test="settleSeqNo != null">-->
        <!--#{settleSeqNo},-->
      <!--</if>-->
      <!--<if test="branchType != null">-->
        <!--#{branchType},-->
      <!--</if>-->
      <!--<if test="changeBranchType != null">-->
        <!--#{changeBranchType},-->
      <!--</if>-->
      <!--<if test="branchName != null">-->
        <!--#{branchName},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--#{tranTimestamp},-->
      <!--</if>-->
      <!--<if test="ocCheckStatus != null">-->
        <!--#{ocCheckStatus},-->
      <!--</if>-->
      <!--<if test="tempAcctNo != null">-->
        <!--#{tempAcctNo},-->
      <!--</if>-->
      <!--<if test="tempProdType != null">-->
        <!--#{tempProdType},-->
      <!--</if>-->
      <!--<if test="tempCcy != null">-->
        <!--#{tempCcy},-->
      <!--</if>-->
      <!--<if test="tempSeqNo != null">-->
        <!--#{tempSeqNo},-->
      <!--</if>-->
      <!--<if test="usedAcctNo != null">-->
        <!--#{usedAcctNo},-->
      <!--</if>-->
      <!--<if test="usedProdType != null">-->
        <!--#{usedProdType},-->
      <!--</if>-->
      <!--<if test="usedCcy != null">-->
        <!--#{usedCcy},-->
      <!--</if>-->
      <!--<if test="usedSeqNo != null">-->
        <!--#{usedSeqNo},-->
      <!--</if>-->
      <!--<if test="budgetAcctNo != null">-->
        <!--#{budgetAcctNo},-->
      <!--</if>-->
      <!--<if test="budgetProdType != null">-->
        <!--#{budgetProdType},-->
      <!--</if>-->
      <!--<if test="budgetCcy != null">-->
        <!--#{budgetCcy},-->
      <!--</if>-->
      <!--<if test="budgetSeqNo != null">-->
        <!--#{budgetSeqNo},-->
      <!--</if>-->
      <!--<if test="transitAcctNo != null">-->
        <!--#{transitAcctNo},-->
      <!--</if>-->
      <!--<if test="transitProdType != null">-->
        <!--#{transitProdType},-->
      <!--</if>-->
      <!--<if test="transitCcy != null">-->
        <!--#{transitCcy},-->
      <!--</if>-->
      <!--<if test="transitSeqNo != null">-->
        <!--#{transitSeqNo},-->
      <!--</if>-->
      <!--<if test="changeRegionName != null">-->
         <!--#{changeRegionName}-->
      <!--</if>-->
    <!--</trim>-->
  <!--</insert>-->

  <!-- 模糊查询行名、行号 -->
  <select id="selectChangeInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
     select <include refid="Base_Column"/>
      from OC_CHANGE_BRANCH
      where BRANCH_TYPE = 'O'
       and CHANGE_NO like CONCAT('%',CONCAT(#{changeNo},'%'))
       and BRANCH_NAME like CONCAT('%',CONCAT(#{changeName},'%'))
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 根据机构代号查询机构配置信息 -->
  <!--<select id="selectChangeBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">-->
    <!--select <include refid="Base_Column"/>-->
      <!--from OC_CHANGE_BRANCH-->
     <!--where TRAN_BRANCH = #{tranBranch}-->
       <!--and BRANCH_TYPE = 'I'-->
  <!--</select>-->
  <!-- 根据清算行查询机构信息-->
  <select id="selectSettleBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select distinct change_no
    from OC_CHANGE_BRANCH
    where BRANCH_TYPE = 'I'
    and  SETTLE_BRANCH = #{settleBranch}
    and  change_no is not null
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 根据清算行查询旗下所有交换行信息-->
  <select id="selectChangeBranchBySB" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select <include refid="Base_Column"/>
    from OC_CHANGE_BRANCH
    where BRANCH_TYPE = 'I'
    and  SETTLE_BRANCH = #{settleBranch}
    and  TRAN_BRANCH = CHANGE_BRANCH
    and  CHANGE_NO is not null
    and  CHANGE_REGION is not null
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 根据清算行查询旗下所有行信息-->
  <select id="getAllBranchBySB" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select <include refid="Base_Column"/>
    from OC_CHANGE_BRANCH
    where BRANCH_TYPE = 'I'
    and  SETTLE_BRANCH = #{settleBranch}
    and  CHANGE_NO is not null
    and  CHANGE_REGION is not null
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 根据交换号查询交换机构 -->
  <select id="selectOcChangeNo" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select distinct change_branch
    from OC_CHANGE_BRANCH
    where CHANGE_NO= #{changeNo}
    and BRANCH_TYPE = 'I'
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 根据交换号查询交换机构 -->
  <select id="selectOtherChangeNo" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select distinct change_branch
    from OC_CHANGE_BRANCH
    where CHANGE_NO= #{changeNo}
    and BRANCH_TYPE = 'O'
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!-- 根据清算机构查询清算账号 -->
  <select id="selectOcSettleAcct" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select distinct settle_acct_no
    from OC_CHANGE_BRANCH
    where SETTLE_BRANCH= #{settleBranch}
    and BRANCH_TYPE = 'I'
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 根据交换号查询甲乙类型 -->
  <select id="selectChangeType" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select distinct change_branch_type
    from OC_CHANGE_BRANCH
    where CHANGE_NO= #{changeNo}
    and CHANGE_REGION= #{changeRegion}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByChangeBranch" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select <include refid="Base_Column"/>
    from OC_CHANGE_BRANCH
    where CHANGE_BRANCH= #{changeBranch}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectBytranBranch" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select <include refid="Base_Column"/>
    from OC_CHANGE_BRANCH
    where TRAN_BRANCH= #{tranBranch}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 模糊查询行名、行号 支持翻页-->
  <!--<select id="selectChangeInfoByPage" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">-->
    <!--select TRAN_BRANCH,CHANGE_REGION,CHANGE_NO,BRANCH_TYPE,CHANGE_BRANCH_TYPE,BRANCH_NAME-->
    <!--from OC_CHANGE_BRANCH-->
    <!--where BRANCH_TYPE = 'O'-->
    <!--and CHANGE_REGION = #{baseParam.changeRegion}-->
    <!--and CHANGE_NO like CONCAT('%',CONCAT(#{baseParam.changeNo},'%'))-->
    <!--and BRANCH_NAME like CONCAT('%',CONCAT(#{baseParam.changeName},'%'))-->
  <!--</select>-->
  <!-- 模糊查询行名、行号 支持翻页-->
  <select id="selectChangeInfoByPage1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select TRAN_BRANCH,CHANGE_REGION,CHANGE_NO,BRANCH_TYPE,CHANGE_BRANCH_TYPE,BRANCH_NAME
    from OC_CHANGE_BRANCH
    where
    CHANGE_REGION = #{changeRegion}
    and CHANGE_NO =#{changeNo}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectInfoByChangeNoChangeName" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select <include refid="Base_Column"/>
    from OC_CHANGE_BRANCH
    where BRANCH_TYPE = 'O'
    <if test="changeNo != null and changeNo.length() > 0">
      and CHANGE_NO = #{changeNo}
    </if>
    <if test="changeName != null and changeName.length() > 0">
      and BRANCH_NAME = #{changeName}
    </if>
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getChangeBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select <include refid="Base_Column"/>
    from OC_CHANGE_BRANCH
    where CHANGE_BRANCH= #{changeBranch}
    and CHANGE_BRANCH = TRAN_BRANCH
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getChangeBranchN" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeBranch">
    select <include refid="Base_Column"/>
    from OC_CHANGE_BRANCH
    where TRAN_BRANCH= #{tranBranch}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
