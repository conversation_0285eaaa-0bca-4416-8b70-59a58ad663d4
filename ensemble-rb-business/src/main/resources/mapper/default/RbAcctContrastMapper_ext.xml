<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContrast">

	<select id="selectByBaseAcctNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContrast">
		select
		<include refid="Base_Column"/>
		from RB_ACCT_CONTRAST
		where BASE_ACCT_NO = #{baseAcctNo}
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<update id="updateByBaseAcctNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContrast">
    update RB_ACCT_CONTRAST
      set
      QUERY_TIME = #{queryTime}
    where
		BASE_ACCT_NO = #{baseAcctNo}
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
    </update>
</mapper>
