<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail">

  <select id="selectByDocType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" >
    select <include refid="Base_Column"/>
    from RB_VOUCHER_LOST_DETAIL
    where LOST_KEY = #{lostKey}
    <if test="docType != null" >
      and DOC_TYPE = #{docType}
    </if>
    <if test="prefix != null">
      and PREFIX = #{prefix}
    </if>
    <if test="voucherStartNo != null" >
      and VOUCHER_START_NO = #{voucherStartNo}
    </if>
    and VOUCHER_LOST_STATUS = 'USE'
    <if test="clientNo != null" >
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <update id="updateByInternal" parameterType="java.util.Map" >
    update RB_VOUCHER_LOST_DETAIL
    <set >
      <if test="status != null" >
        VOUCHER_LOST_STATUS = #{status},
      </if>
    </set>
    where LOST_KEY = #{lostKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="selectDetailList" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" >
    select <include refid="Base_Column"/>
    from RB_VOUCHER_LOST_DETAIL
    <where>
      <if test="lostKey != null" >
        LOST_KEY = #{lostKey}
      </if>
      <if test="lostNo != null" >
        and LOST_NO = #{lostNo}
      </if>
      <if test="seqNo != null" >
        and SEQ_NO = #{seqNo}
      </if>
      <if test="clientNo != null" >
        and CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <select id="getLostDetailByLostNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" >
    select <include refid="Base_Column"/>
    from RB_VOUCHER_LOST_DETAIL
    where  LOST_NO = #{lostNo}
    and VOUCHER_LOST_STATUS='USE'
    <if test="clientNo != null" >
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <update id="updateByLostNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" >
    update RB_VOUCHER_LOST_DETAIL
    <set >
      <if test="status != null" >
        VOUCHER_LOST_STATUS = #{status},
      </if>
    </set>
    where LOST_NO = #{lostNo}
    <if test="clientNo != null" >
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getLostDetailByDocType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail" >
    select <include refid="Base_Column"/>
    from RB_VOUCHER_LOST_DETAIL
    <where>
      <if test="lostKey != null" >
        LOST_KEY = #{lostKey}
      </if>
      <if test="docType != null" >
        and DOC_TYPE = #{docType}
      </if>
      <if test="seqNo != null" >
        and SEQ_NO = #{seqNo}
      </if>
      <if test="voucherLostStatus != null" >
        and VOUCHER_LOST_STATUS= #{voucherLostStatus}
      </if>
      <if test="clientNo != null" >
        and CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

</mapper>
