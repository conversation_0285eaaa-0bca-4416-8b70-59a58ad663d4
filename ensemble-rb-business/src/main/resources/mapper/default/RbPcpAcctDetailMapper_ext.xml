<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">

    <select id="selectByMainAcct" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE base_acct_no =#{baseAcctNo}
        <if test="prodType != null and prodType !=''">
            AND prod_type = #{prodType}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            AND acctCcy = #{acctCcy}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID+0 ASC
    </select>
    <select id="selectByAgreementId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE AGREEMENT_ID =#{agreementId}
        AND PCP_ACCT_STATUS != 'E'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY INTERNAL_KEY+0 ASC
    </select>
    <select id="selectByAgreementIdAndFlag" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE AGREEMENT_ID =#{agreementId}
        AND PCP_AGREEMENT_FLAG =#{pcpAgreementFlag}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY INTERNAL_KEY+0 ASC
    </select>
    <select id="selectByAgreementIdAndStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE AGREEMENT_ID =#{agreementId}
        <if test="status != null and status !=''">
            AND PCP_ACCT_STATUS = #{status}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY INTERNAL_KEY+0 ASC
    </select>
    <select id="getMbPcpAcctDetalbyIKeyAndAgId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE AGREEMENT_ID =#{agreementId}
        AND PCP_ACCT_STATUS in ('A','C','P')
        AND INTERNAL_KEY =#{internalKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectByAgreementIdAndAcct" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        select
        <include refid="Base_Column"/>
        from RB_PCP_ACCT_DETAIL
        where PCP_ACCT_STATUS = 'A'
        <if test="agreementId != null">
        AND  AGREEMENT_ID = #{agreementId}
        </if>
        <if test="baseAcctNo != null and baseAcctNo !=''">
            AND base_acct_no =#{baseAcctNo}
        </if>
        <if test="prodType != null and prodType !=''">
            AND prod_type = #{prodType}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            AND acctCcy = #{acctCcy}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectBatchDatePcp" parameterType="java.util.Map"
            resultType="java.util.Map">
        <if test="_databaseId == 'oracle'">
				SELECT   MIN (INTERNAL_KEY) START_KEY, MAX (INTERNAL_KEY) END_KEY, count(1) ROW_COUNT
				FROM   (  SELECT DISTINCT INTERNAL_KEY
                  FROM   RB_PCP_ACCT_DETAIL
                  WHERE PCP_ACCT_STATUS = 'A'
                  AND INNER_PRICE_FLAG = 'Y'
                    <if test="company != null and company != '' ">
                      AND COMPANY = #{company}
                    </if>
                  ORDER BY   INTERNAL_KEY)
				GROUP BY   TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
        <if test="_databaseId == 'mysql'">
			SELECT
			MIN(INTERNAL_KEY) START_KEY,
			MAX(INTERNAL_KEY) END_KEY
			FROM
			(
              SELECT
                DISTINCT INTERNAL_KEY INTERNAL_KEY,
                @rownum :=@rownum + 1 AS rownum
                FROM
                RB_PCP_ACCT_DETAIL mm,
                (SELECT @rownum := -1) t
                 WHERE PCP_ACCT_STATUS = 'A'
                 AND INNER_PRICE_FLAG = 'Y'
                <if test="company != null and company != '' ">
                  AND COMPANY = #{company}
                </if>
                ORDER BY
                mm.INTERNAL_KEY
			) tt
			GROUP BY
			FLOOR(
			tt.rownum /#{maxPerCount} )
        </if>
    </select>
    <select id="queryPcpInternalKey" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT DISTINCT INTERNAL_KEY
        FROM   RB_PCP_ACCT_DETAIL
        WHERE PCP_ACCT_STATUS = 'A'
        AND INNER_PRICE_FLAG = 'Y'
        AND INTERNAL_KEY between #{START_KEY} and #{END_KEY}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY  INTERNAL_KEY
    </select>
    <select id="selectEodPcpAcctDetail" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        select
        <include refid="Base_Column"/>
        from RB_PCP_ACCT_DETAIL
        where PCP_ACCT_STATUS IN ('A','P','C')
        AND INNER_PRICE_FLAG = 'Y'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY INTERNAL_KEY
    </select>
    <select id="selectByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM   RB_PCP_ACCT_DETAIL
        WHERE PCP_ACCT_STATUS = 'A'
        AND INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </select>
    <select id="selectByAcct" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE base_acct_no =#{baseAcctNo}
        AND prod_type = #{prodType}
        AND acctCcy = #{acctCcy}
        AND ACCT_SEQ_NO = #{acctSeqNo}
        AND PCP_ACCT_STATUS = 'A'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID+0 ASC
    </select>
    <select id="selectByAcctAll" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE PCP_ACCT_STATUS = 'A'
        AND base_acct_no =#{baseAcctNo}
        <if test="prodType != null">
            AND prod_type = #{prodType}
        </if>
        <if test="acctCcy != null">
            AND acctCcy = #{acctCcy}
        </if>
        <if test="acctSeqNo != null">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID+0 ASC
    </select>

    <select id="getPcpDetailByInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_PCP_ACCT_DETAIL
        WHERE PCP_ACCT_STATUS in ('A','C','P')
        AND base_acct_no =#{baseAcctNo}
        <if test="prodType != null and prodType !=''">
            AND prod_type = #{prodType}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            AND acct_ccy = #{acctCcy}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
