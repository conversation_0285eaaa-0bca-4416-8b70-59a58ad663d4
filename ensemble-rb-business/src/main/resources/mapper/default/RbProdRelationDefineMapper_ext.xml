<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine">
    select <include refid="Base_Column"/>
    from RB_PROD_RELATION_DEFINE
    where PROD_TYPE = #{prodType}
      <if test="subProdType != null">
        AND SUB_PROD_TYPE = #{subProdType}
      </if>
      <if test="eventType != null">
        AND EVENT_TYPE = #{eventType}
      </if>
      <if test="assembleId != null">
        AND ASSEMBLE_ID = #{assembleId}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND (COMPANY = #{company}  or  COMPANY =  'ALL')
    </if>
  </select>
  <select id="getSkipPart" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine">
    select <include refid="Base_Column"/>
    from RB_PROD_RELATION_DEFINE
    where PROD_TYPE = #{prodType}
    <if test="subProdType != null">
      AND SUB_PROD_TYPE = #{subProdType}
    </if>
    <if test="eventType != null">
      AND EVENT_TYPE = #{eventType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND (COMPANY = #{company}  or  COMPANY =  'ALL')
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine">
    delete from RB_PROD_RELATION_DEFINE
    where PROD_TYPE = #{prodType}
      <if test="subProdType != null">
        AND SUB_PROD_TYPE = #{subProdType}
      </if>
      <if test="eventType != null">
        AND EVENT_TYPE = #{eventType}
      </if>
      <if test="assembleId != null">
        AND ASSEMBLE_ID = #{assembleId}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND (COMPANY = #{company}  or  COMPANY =  'ALL')
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine">
    update RB_PROD_RELATION_DEFINE
    <set>
      <if test="partUseType != null">
        PART_USE_TYPE = #{partUseType},
      </if>

    </set>
    where PROD_TYPE = #{prodType}
        AND SUB_PROD_TYPE = #{subProdType}
        AND EVENT_TYPE = #{eventType}
        AND ASSEMBLE_ID = #{assembleId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND (COMPANY = #{company}  or  COMPANY =  'ALL')
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine">
    insert into RB_PROD_RELATION_DEFINE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="subProdType != null">
        SUB_PROD_TYPE,
      </if>
      <if test="eventType != null">
        EVENT_TYPE,
      </if>
      <if test="assembleId != null">
        ASSEMBLE_ID,
      </if>
      <if test="partUseType != null">
        PART_USE_TYPE,
      </if>
      <if test="company != null">
        COMPANY,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="subProdType != null">
        #{subProdType},
      </if>
      <if test="eventType != null">
        #{eventType},
      </if>
      <if test="assembleType != null">
        #{assembleType},
      </if>
      <if test="assembleId != null">
        #{assembleId},
      </if>
      <if test="partUseType != null">
        #{partUseType},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
</mapper>
