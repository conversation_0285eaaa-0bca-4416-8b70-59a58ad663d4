<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">

  <sql id="RB_ACCT_INT_DETAIL">
      NULL AS RL_SEQ_NO,
			NULL AS SYSTEM_ID,
			maid.INTERNAL_KEY,
			maid.INT_CLASS,
			NULL AS START_DATE,
			NULL AS END_DATE,
			NULL AS PERI_SPLIT_ID,
			NULL AS PERI_SEQ_NO,
			NULL AS AMT_SPLIT_ID,
			NULL AS AMT_SEQ_NO,
			0 AS NEAR_AMT,
			maid.int_accrued AS ACCR_AMT,
			NULL AS NEAR_PERIOD_TYPE,
			NULL AS NEAR_PERIOD,
			NULL AS ACCR_DAYS,
			maid.ACTUAL_RATE,
			maid.FLOAT_RATE,
			maid.REAL_RATE,
			maid.ACCT_SPREAD_RATE,
			maid.ACCT_PERCENT_RATE,
			maid.ACCT_FIXED_RATE,
			maid.INT_TYPE,
			NULL AS AMT_SPLIT_MODE,
			NULL AS PERI_SPLIT_MODE,
			maid.YEAR_BASIS,
			maid.MONTH_BASIS,
			NULL AS RECAL_METHOD,
            maid.COMPANY,
			maid.SPLIT_RATE_FLAG
  </sql>
  <sql id="RB_ACCT_INT_DETAIL_SPLIT">
     	mads.IRL_SEQ_NO,
				mads.SYSTEM_ID,
				mads.INTERNAL_KEY,
				mads.INT_CLASS,
				mads.START_DATE,
				mads.END_DATE,
				mads.PERI_SPLIT_ID,
				mads.PERI_SEQ_NO,
				mads.AMT_SPLIT_ID,
				mads.AMT_SEQ_NO,
				mads.NEAR_AMT,
				mads.ACCR_AMT,
				mads.NEAR_PERIOD_TYPE,
				mads.NEAR_PERIOD,
				mads.ACCR_DAYS,
				mads.ACTUAL_RATE,
				mads.FLOAT_RATE,
				mads.REAL_RATE,
				mads.ACCT_SPREAD_RATE,
				mads.ACCT_PERCENT_RATE,
				mads.ACCT_FIXED_RATE,
				mads.INT_TYPE,
				mads.AMT_SPLIT_MODE,
				mads.PERI_SPLIT_MODE,
				mads.YEAR_BASIS,
				mads.MONTH_BASIS,
				mads.RECAL_METHOD,

				mads.COMPANY,
				NULL AS SPLIT_RATE_FLAG
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    select <include refid="Base_Column"/>
    from RB_ACCT_INT_DETAIL_SPLIT
    where IRL_SEQ_NO = #{irlSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    delete from RB_ACCT_INT_DETAIL_SPLIT
    where IRL_SEQ_NO = #{irlSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    update RB_ACCT_INT_DETAIL_SPLIT
    <set>
      <if test="accrAmt != null">
        ACCR_AMT = #{accrAmt},
      </if>
      <if test="accrDays != null">
        ACCR_DAYS = #{accrDays},
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE = #{acctFixedRate},
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE = #{acctPercentRate},
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE = #{acctSpreadRate},
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE = #{actualRate},
      </if>
      <if test="amtSeqNo != null">
        AMT_SEQ_NO = #{amtSeqNo},
      </if>
      <if test="amtSplitId != null">
        AMT_SPLIT_ID = #{amtSplitId},
      </if>
      <if test="amtSplitMode != null">
        AMT_SPLIT_MODE = #{amtSplitMode},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate},
      </if>
      <if test="floatRate != null">
        FLOAT_RATE = #{floatRate},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="intClass != null">
        INT_CLASS = #{intClass},
      </if>
      <if test="intType != null">
        INT_TYPE = #{intType},
      </if>
      <if test="monthBasis != null">
        MONTH_BASIS = #{monthBasis},
      </if>
      <if test="nearAmt != null">
        NEAR_AMT = #{nearAmt},
      </if>
      <if test="nearPeriod != null">
        NEAR_PERIOD = #{nearPeriod},
      </if>
      <if test="nearPeriodType != null">
        NEAR_PERIOD_TYPE = #{nearPeriodType},
      </if>
      <if test="periSeqNo != null">
        PERI_SEQ_NO = #{periSeqNo},
      </if>
      <if test="periSplitId != null">
        PERI_SPLIT_ID = #{periSplitId},
      </if>
      <if test="periSplitMode != null">
        PERI_SPLIT_MODE = #{periSplitMode},
      </if>
      <if test="realRate != null">
        REAL_RATE = #{realRate},
      </if>
      <if test="recalMethod != null">
        RECAL_METHOD = #{recalMethod},
      </if>

      <if test="startDate != null">
        START_DATE = #{startDate},
      </if>
      <if test="systemId != null">
        SYSTEM_ID = #{systemId},
      </if>

      <if test="yearBasis != null">
        YEAR_BASIS = #{yearBasis},
      </if>
      <if test="agreeFixedRate != null">
        AGREE_FIXED_RATE = #{agreeFixedRate},
      </if>
      <if test="agreePercentRate != null">
        AGREE_PERCENT_RATE = #{agreePercentRate},
      </if>
      <if test="agreeSpreadRate != null">
        AGREE_SPREAD_RATE = #{agreeSpreadRate},
      </if>
      <if test="agreeChangeType != null">
        AGREE_CHANGE_TYPE = #{agreeChangeType},
      </if>
      <if test="agreeReduceAmt != null">
        AGREE_REDUCE_AMT = #{agreeReduceAmt},
      </if>
    </set>
    where IRL_SEQ_NO = #{irlSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    insert into RB_ACCT_INT_DETAIL_SPLIT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accrAmt != null">
        ACCR_AMT,
      </if>
      <if test="accrDays != null">
        ACCR_DAYS,
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE,
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE,
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="amtSeqNo != null">
        AMT_SEQ_NO,
      </if>
      <if test="amtSplitId != null">
        AMT_SPLIT_ID,
      </if>
      <if test="amtSplitMode != null">
        AMT_SPLIT_MODE,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="intClass != null">
        INT_CLASS,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="irlSeqNo != null">
        IRL_SEQ_NO,
      </if>
      <if test="monthBasis != null">
        MONTH_BASIS,
      </if>
      <if test="nearAmt != null">
        NEAR_AMT,
      </if>
      <if test="nearPeriod != null">
        NEAR_PERIOD,
      </if>
      <if test="nearPeriodType != null">
        NEAR_PERIOD_TYPE,
      </if>
      <if test="periSeqNo != null">
        PERI_SEQ_NO,
      </if>
      <if test="periSplitId != null">
        PERI_SPLIT_ID,
      </if>
      <if test="periSplitMode != null">
        PERI_SPLIT_MODE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="recalMethod != null">
        RECAL_METHOD,
      </if>

      <if test="startDate != null">
        START_DATE,
      </if>
      <if test="systemId != null">
        SYSTEM_ID,
      </if>

      <if test="yearBasis != null">
        YEAR_BASIS,
      </if>
      <if test="agreeFixedRate != null">
        AGREE_FIXED_RATE,
      </if>
      <if test="agreePercentRate != null">
        AGREE_PERCENT_RATE,
      </if>
      <if test="agreeSpreadRate != null">
        AGREE_SPREAD_RATE,
      </if>
      <if test="agreeChangeType != null">
        AGREE_CHANGE_TYPE,
      </if>
      <if test="agreeReduceAmt != null">
        AGREE_REDUCE_AMT,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accrAmt != null">
        #{accrAmt},
      </if>
      <if test="accrDays != null">
        #{accrDays},
      </if>
      <if test="acctFixedRate != null">
        #{acctFixedRate},
      </if>
      <if test="acctPercentRate != null">
        #{acctPercentRate},
      </if>
      <if test="acctSpreadRate != null">
        #{acctSpreadRate},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="amtSeqNo != null">
        #{amtSeqNo},
      </if>
      <if test="amtSplitId != null">
        #{amtSplitId},
      </if>
      <if test="amtSplitMode != null">
        #{amtSplitMode},
      </if>
      <if test="endDate != null">
        #{endDate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="intClass != null">
        #{intClass},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="irlSeqNo != null">
        #{irlSeqNo},
      </if>
      <if test="monthBasis != null">
        #{monthBasis},
      </if>
      <if test="nearAmt != null">
        #{nearAmt},
      </if>
      <if test="nearPeriod != null">
        #{nearPeriod},
      </if>
      <if test="nearPeriodType != null">
        #{nearPeriodType},
      </if>
      <if test="periSeqNo != null">
        #{periSeqNo},
      </if>
      <if test="periSplitId != null">
        #{periSplitId},
      </if>
      <if test="periSplitMode != null">
        #{periSplitMode},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="recalMethod != null">
        #{recalMethod},
      </if>

      <if test="startDate != null">
        #{startDate},
      </if>
      <if test="systemId != null">
        #{systemId},
      </if>

      <if test="yearBasis != null">
        #{yearBasis},
      </if>
      <if test="agreeFixedRate != null">
        #{agreeFixedRate},
      </if>
      <if test="agreePercentRate != null">
        #{agreePercentRate},
      </if>
      <if test="agreeSpreadRate != null">
        #{agreeSpreadRate},
      </if>
      <if test="agreeChangeType != null">
        #{agreeChangeType},
      </if>
      <if test="agreeReduceAmt != null">
        #{agreeReduceAmt},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
  <!--后期优化该处查询，暂时使用下方 chengweid 20200312-->
  <select id="getMbAcctIntDetailSplitBak" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    SELECT *
    FROM (SELECT <include refid="RB_ACCT_INT_DETAIL_SPLIT"/>
    FROM
    RB_ACCT_INT_DETAIL MAID right JOIN
    RB_ACCT_INT_DETAIL_SPLIT MADS
    ON
    (
    MAID.CLIENT_NO= MADS.CLIENT_NO AND
    MAID.INTERNAL_KEY= MADS.INTERNAL_KEY AND
    MAID.INT_CLASS = MADS.INT_CLASS
    )
    WHERE
    MAID.SPLIT_RATE_FLAG = 'Y'
    AND MAID.INTERNAL_KEY =#{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND MAID.COMPANY = #{company}
    </if>
    <if test="intClass != null">
      and MAID.INT_CLASS = #{intClass}
    </if>
    <if test="systemId != null">
      and MADS.SYSTEM_ID = #{systemId}
    </if>
    <if test="clientNo != null">
      and MAID.CLIENT_NO = #{clientNo}
    </if>
    UNION
    SELECT <include refid="RB_ACCT_INT_DETAIL"/>
    FROM RB_ACCT_INT_DETAIL MAID
    WHERE MAID.SPLIT_RATE_FLAG ='N'
    AND MAID.INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND MAID.COMPANY = #{company}
    </if>
    <if test="intClass != null">
      and MAID.INT_CLASS = #{intClass}
    </if>
    <if test="clientNo != null">
      and MAID.CLIENT_NO = #{clientNo}
    </if>
    ) a
  </select>
  <select id="getMbAcctIntDetailSplit" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    SELECT <include refid="RB_ACCT_INT_DETAIL"/>
    FROM RB_ACCT_INT_DETAIL MAID
    where MAID.INTERNAL_KEY = #{internalKey}
    <if test="intClass != null">
      and MAID.INT_CLASS = #{intClass}
    </if>
    <if test="clientNo != null">
      and MAID.CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND MAID.COMPANY = #{company}
    </if>
  </select>
  <update id="updateFourByIntKeyClass" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    update RB_ACCT_INT_DETAIL_SPLIT
    <set>
      <if test="agreeFixedRate != null">
        AGREE_FIXED_RATE = #{agreeFixedRate},
      </if>
      AGREE_PERCENT_RATE = #{agreePercentRate},
      AGREE_SPREAD_RATE = #{agreeSpreadRate},
      AGREE_CHANGE_TYPE = #{agreeChangeType}
    </set>
    where INTERNAL_KEY = #{internalKey} and INT_CLASS = #{intClass}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getSplitList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit">
    SELECT <include refid="Base_Column" />
    FROM RB_ACCT_INT_DETAIL_SPLIT
    WHERE INTERNAL_KEY = #{internalKey}
      AND INT_CLASS = #{intClass}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <![CDATA[
      AND START_DATE >= #{startDate}
      AND (END_DATE <= #{endDate} or END_DATE is null)
    ORDER BY START_DATE DESC
    ]]>
    </select>
</mapper>
