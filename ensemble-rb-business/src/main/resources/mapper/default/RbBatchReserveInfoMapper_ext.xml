<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveInfo">

    <select id="getReserveInfoByRDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveInfo">
        select *
        from Rb_Batch_Reserve_Info
        where REGISTER_DATE = #{registerDate}
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by ext_ref_no
    </select>

    <select id="getReserveInfoForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveInfo"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveInfo">
        select <include refid="Base_Column"/>
        from Rb_Batch_Reserve_Info
        where ext_ref_no = #{extRefNo}
        for update
    </select>

</mapper>