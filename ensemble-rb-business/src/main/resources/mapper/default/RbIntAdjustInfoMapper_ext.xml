<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntAdjustInfo">
	<select id="getSumIntAdj" parameterType="java.util.Map" resultType="java.math.BigDecimal">
		select
         SUM(INT_ADJ_CTD) AS INT_ADJ
		from
		<include refid="Table_Name" />
		where 1=1
		<if test="internalKey != null ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null ">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="intClass != null ">
			AND INT_CLASS = #{intClass}
		</if>
		<if test="startDate != null and endDate != null ">
			AND <![CDATA[
			ADJUST_DATE>=#{startDate,jdbcType=DATE} AND ADJUST_DATE<= #{endDate,jdbcType=DATE}
		     ]]>
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>

	</select>
	<select id="getIntAdjInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntAdjustInfo">
		select
		<include refid="Base_Column" />
		from
		<include refid="Table_Name" />
		where 1=1
		<if test="internalKey != null ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null ">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		order by TRAN_TIMESTAMP desc
	</select>

    <select id="getIntAdjInfoByDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntAdjustInfo">
        select
        <include refid="Base_Column" />
        from
        <include refid="Table_Name" />
        where 1=1
        <if test="internalKey != null ">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo != null ">
            AND CLIENT_NO = #{clientNo}
        </if>
		<if test="lastCycleDate != null ">
			AND  <![CDATA[ ADJUST_DATE>#{lastCycleDate,jdbcType=DATE} ]]>
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
        order by TRAN_TIMESTAMP desc
    </select>

	<select id="findRbIntAdjustInfoByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntAdjustInfo">
		select
		<include refid="Base_Column" />
		from
		<include refid="Table_Name" />
		where 1=1
		<if test="internalKey != null ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null ">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="reference != null ">
			AND REFERENCE = #{reference}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND REVERSAL_FLAG != 'Y'
		order by TRAN_TIMESTAMP desc
	</select>
	<select id="queryRbIntAdjustInfoReversal" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntAdjustInfo">
		select
		<include refid="Base_Column" />
		from
		<include refid="Table_Name" />
		where 1=1
		<if test="internalKey != null ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="reference != null ">
			AND REFERENCE = #{reference}
		</if>
		<if test="tranSource != null and tranSource != ''">
			AND TRAN_SOURCE = #{tranSource}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND REVERSAL_FLAG = 'Y'
		order by TRAN_TIMESTAMP desc
	</select>
</mapper>
