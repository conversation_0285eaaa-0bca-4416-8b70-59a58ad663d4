<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitSumInfo">

    <select id="getRbLimitSumInfoByPrimaryKeyForUpdate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitSumInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <if test="limitSumContent != null and  limitSumContent != '' ">
                AND LIMIT_SUM_CONTENT = #{limitSumContent,jdbcType=VARCHAR}
            </if>
            <if test="limitSceneNo != null and  limitSceneNo != '' ">
                AND LIMIT_SCENE_NO = #{limitSceneNo,jdbcType=VARCHAR}
            </if>
            <if test="preReference != null and preReference != '' ">
                AND PRE_REFERENCE = #{preReference,jdbcType=VARCHAR}
            </if>
            <if test="reference != null and reference != '' ">
                AND REFERENCE = #{reference,jdbcType=VARCHAR}
            </if>
            <if test="createDate != null ">
                AND CREATE_DATE = #{createDate,jdbcType=DATE}
            </if>
            <if test="checkObjVal != null and  checkObjVal != '' ">
                AND CHECK_OBJ_VAL = #{checkObjVal,jdbcType=VARCHAR}
            </if>
            <if test="company != null and  company != '' ">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
            <if test="limitSumAmt != null ">
                AND LIMIT_SUM_AMT = #{limitSumAmt,jdbcType=DECIMAL}
            </if>
            <if test="limitSumNum != null ">
                AND LIMIT_SUM_NUM = #{limitSumNum,jdbcType=VARCHAR}
            </if>
            <if test="updateDate != null ">
                AND UPDATE_DATE = #{updateDate,jdbcType=DATE}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="lastChangeDate != null ">
                AND LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE}
            </if>
            <if test="createTimestamp != null and  createTimestamp != '' ">
                AND CREATE_TIMESTAMP = #{createTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="tranTimestamp != null and  tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="expireDate != null ">
                AND #{expireDate,jdbcType=DATE} BETWEEN EFFECT_DATE AND EXPIRE_DATE
            </if>
            <if test="effectDate != null ">
                AND EFFECT_DATE = #{effectDate,jdbcType=DATE}
            </if>
        </where>
        FOR UPDATE
    </select>

    <select id="getRbLimitSumInfoList" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLimitSumInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <if test="limitSceneNo != null and  limitSceneNo != '' ">
                AND LIMIT_SCENE_NO = #{limitSceneNo,jdbcType=VARCHAR}
            </if>
            <if test="checkObjVal != null and  checkObjVal != '' ">
                AND CHECK_OBJ_VAL = #{checkObjVal,jdbcType=VARCHAR}
            </if>
            <if test="company != null and  company != '' ">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="limitSumAmt != null and limitSumAmt != '' ">
                AND LIMIT_SUM_AMT <![CDATA[  >=  ]]> #{limitSumAmt}
            </if>
            <if test="expireDate != null">
                AND #{expireDate,jdbcType=DATE} BETWEEN EFFECT_DATE AND EXPIRE_DATE
            </if>

        </where>
    </select>

    <select id="updateRbLimitSumInfoByParam" parameterType="java.util.Map">
        UPDATE RB_LIMIT_SUM_INFO
        <set>
            <if test="newLimitSceneNo != null and newLimitSceneNo != '' ">
                LIMIT_SCENE_NO = #{newLimitSceneNo},
            </if>
            <if test="newCheckObjVal != null and newCheckObjVal != '' ">
                CHECK_OBJ_VAL = #{newCheckObjVal},
            </if>
            <if test="newReference != null and newReference != '' ">
                REFERENCE = #{newReference}
            </if>
        </set>
        <where>
            LIMIT_SCENE_NO = #{limitSceneNo}
            AND CLIENT_NO = #{clientNo}
            AND CHECK_OBJ_VAL = #{checkObjVal}
        </where>
    </select>

</mapper>
