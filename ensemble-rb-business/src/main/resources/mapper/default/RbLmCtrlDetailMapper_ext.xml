<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail" >
  <sql id="Base_Column_List">
    CTRL_ID,CTRL_DETAIL_ID,CCY,DOC_TYPE,HIGH_LIMIT,LOW_LIMIT,COMPANY,TRAN_TIMESTAMP,HIGH_LIMIT_NIGHT
  </sql>
  <!--<select id="selectByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail">-->
    <!--select <include refid="Base_Column_List"/>-->
    <!--from RB_LM_CTRL_DETAIL-->
    <!--where CTRL_DETAIL_ID = #{ctrlDetailId,jdbcType=VARCHAR}-->
      <!--<if test="ctrlId != null">-->
        <!--AND CTRL_ID = #{ctrlId,jdbcType=VARCHAR}-->
      <!--</if>-->
  <!--</select>-->
  <!--<delete id="deleteByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail">-->
    <!--delete from RB_LM_CTRL_DETAIL-->
    <!--where CTRL_DETAIL_ID = #{ctrlDetailId,jdbcType=VARCHAR}-->
      <!--<if test="ctrlId != null">-->
        <!--AND CTRL_ID = #{ctrlId,jdbcType=VARCHAR}-->
      <!--</if>-->
  <!--</delete>-->
  <!--<update id="updateByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail">-->
    <!--update RB_LM_CTRL_DETAIL-->
    <!--<set>-->
      <!--<if test="acctCcy != null">-->
        <!--CCY = #{acctCcy,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="docType != null">-->
        <!--DOC_TYPE = #{docType,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="highLimit != null">-->
        <!--HIGH_LIMIT = #{highLimit,jdbcType=DECIMAL},-->
      <!--</if>-->
      <!--<if test="lowLimit != null">-->
        <!--LOW_LIMIT = #{lowLimit,jdbcType=DECIMAL},-->
      <!--</if>-->
      <!--<if test="company != null">-->
        <!--COMPANY = #{company,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="highLimitNight != null">-->
        <!--HIGH_LIMIT_NIGHT = #{highLimitNight,jdbcType=DECIMAL}-->
      <!--</if>-->
    <!--</set>-->
    <!--where CTRL_DETAIL_ID = #{ctrlDetailId,jdbcType=VARCHAR}-->
        <!--AND CTRL_ID = #{ctrlId,jdbcType=VARCHAR}-->
  <!--</update>-->
  <!--<insert id="insert" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail">-->
    <!--insert into RB_LM_CTRL_DETAIL-->
    <!--<trim prefix="(" suffix=")" suffixOverrides=",">-->
      <!--<if test="ctrlId != null">-->
        <!--CTRL_ID,-->
      <!--</if>-->
      <!--<if test="acctCcy != null">-->
        <!--CCY,-->
      <!--</if>-->
      <!--<if test="docType != null">-->
        <!--DOC_TYPE,-->
      <!--</if>-->
      <!--<if test="highLimit != null">-->
        <!--HIGH_LIMIT,-->
      <!--</if>-->
      <!--<if test="lowLimit != null">-->
        <!--LOW_LIMIT,-->
      <!--</if>-->
      <!--<if test="company != null">-->
        <!--COMPANY,-->
      <!--</if>-->
      <!--<if test="ctrlDetailId != null">-->
        <!--CTRL_DETAIL_ID,-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP,-->
      <!--</if>-->
      <!--<if test="highLimitNight != null">-->
        <!--HIGH_LIMIT_NIGHT-->
      <!--</if>-->
    <!--</trim>-->
    <!--<trim prefix="values (" suffix=")" suffixOverrides=",">-->
      <!--<if test="ctrlId != null">-->
        <!--#{ctrlId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="acctCcy != null">-->
        <!--#{acctCcy,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="docType != null">-->
        <!--#{docType,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="highLimit != null">-->
        <!--#{highLimit,jdbcType=DECIMAL},-->
      <!--</if>-->
      <!--<if test="lowLimit != null">-->
        <!--#{lowLimit,jdbcType=DECIMAL},-->
      <!--</if>-->
      <!--<if test="company != null">-->
        <!--#{company,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="ctrlDetailId != null">-->
        <!--#{ctrlDetailId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--#{tranTimestamp,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="highLimitNight != null">-->
        <!--#{highLimitNight,jdbcType=DECIMAL},-->
      <!--</if>-->
    <!--</trim>-->
  <!--</insert>-->

  <select id="getByCtrlId" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail" parameterType="map" >
    select <include refid="Base_Column_List"/>
    from RB_LM_CTRL_DETAIL
    where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <delete id="deleteByCtrlId" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail" >
    delete from RB_LM_CTRL_DETAIL
    where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>

  <select id="getByCtrlIdAndCcy" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail" parameterType="map" >
    select <include refid="Base_Column_List"/>
    from RB_LM_CTRL_DETAIL
    where CTRL_ID = #{ctrlId,jdbcType=VARCHAR} AND CCY = #{ccy,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

    <select id="getByCtrlIdAndDocType" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmCtrlDetail" parameterType="map" >
        select <include refid="Base_Column_List"/>
        from RB_LM_CTRL_DETAIL
        where CTRL_ID = #{ctrlId,jdbcType=VARCHAR} AND DOC_TYPE = #{docType,jdbcType=VARCHAR}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </select>
</mapper>
