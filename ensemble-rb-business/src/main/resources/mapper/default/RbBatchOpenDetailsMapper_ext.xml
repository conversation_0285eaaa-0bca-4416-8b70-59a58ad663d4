<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails">

  <select id="getByPrimaryKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DETAILS
    where SEQ_NO = #{seqNo} AND BATCH_NO = #{batchNo}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </select>

  <select id="getDetails" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DETAILS
    where BATCH_NO = #{batchNo} and REVERSAL_FLAG = 'N'
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
    order by SEQ_NO
  </select>

  <select id="queryDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_DETAILS
    where BATCH_NO = #{batchNo} and BATCH_STATUS=#{batchStatus}
      <!-- 多法人改造 by luocwa -->
      <if test="companyList != null">
          AND COMPANY in
          <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
    order by SEQ_NO
  </select>
  <select id="getSumByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM (1)
    from RB_BATCH_OPEN_DETAILS
    where BATCH_NO = #{batchNo}
    AND BATCH_STATUS = 'S'
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </select>

    <select id="getSumByPhone" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        <if test="_databaseId == 'mysql'">
            select ifnull(MAX(a.num),0) from (select count(*) num
            from RB_BATCH_OPEN_DETAILS
            where BATCH_NO = #{batchNo}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            group by MOBILE_NO having count(*)>1) a
        </if>
        <if test="_databaseId == 'oracle'">
            select nvl(max(count(*)),0)
            from RB_BATCH_OPEN_DETAILS
            where BATCH_NO = #{batchNo}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            group by MOBILE_NO having count(*)>1
        </if>
  </select>

    <select id="getSumByLocation" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        <if test="_databaseId == 'mysql'">
            select ifnull(MAX(a.num),0) from (select count(*) num
            from RB_BATCH_OPEN_DETAILS
            where BATCH_NO = #{batchNo} and LOCAL_MESSAGE is null
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            group by LOCATION having count(*)>1) a
        </if>
        <if test="_databaseId == 'oracle'">
            select nvl(max(count(*)),0)
            from RB_BATCH_OPEN_DETAILS
            where BATCH_NO = #{batchNo} and LOCAL_MESSAGE is null
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            group by LOCATION having count(*)>1
        </if>
  </select>
    <update id="updateExt">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set_Ext"/>
        <include refid="PrimaryKey_Where_Ext"/>
    </update>
    <sql id="PrimaryKey_Where_Ext">
        <where>
            <if test="batchNo != null and  batchNo != '' ">
                AND BATCH_NO = #{batchNo,jdbcType=VARCHAR}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </sql>
    <sql id="Base_Set_Ext">
        <set>
            <if test="jobRunId != null ">JOB_RUN_ID = #{jobRunId,jdbcType=VARCHAR},</if>
            <if test="batchNo != null ">BATCH_NO = #{batchNo,jdbcType=VARCHAR},</if>
            <if test="seqNo != null ">SEQ_NO = #{seqNo,jdbcType=VARCHAR},</if>
            <if test="channelSeqNo != null ">CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR},</if>
            <if test="subSeqNo != null ">SUB_SEQ_NO = #{subSeqNo,jdbcType=VARCHAR},</if>
            <if test="reference != null ">REFERENCE = #{reference,jdbcType=VARCHAR},</if>
            <if test="clientNo != null ">CLIENT_NO = #{clientNo,jdbcType=VARCHAR},</if>
            <if test="clientType != null ">CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},</if>
            <if test="categoryType != null ">CATEGORY_TYPE = #{categoryType,jdbcType=VARCHAR},</if>
            <if test="documentId != null ">DOCUMENT_ID = #{documentId,jdbcType=VARCHAR},</if>
            <if test="documentType != null ">DOCUMENT_TYPE = #{documentType,jdbcType=VARCHAR},</if>
            <if test="issDate != null ">ISS_DATE = #{issDate,jdbcType=DATE},</if>
            <if test="issCountry != null ">ISS_COUNTRY = #{issCountry,jdbcType=VARCHAR},</if>
            <if test="documentExpiryDate != null ">DOCUMENT_EXPIRY_DATE = #{documentExpiryDate,jdbcType=DATE},</if>
            <if test="birthday != null ">BIRTHDAY = #{birthday,jdbcType=DATE},</if>
            <if test="sex != null ">SEX = #{sex,jdbcType=VARCHAR},</if>
            <if test="race != null ">RACE = #{race,jdbcType=VARCHAR},</if>
            <if test="chClientName != null ">CH_CLIENT_NAME = #{chClientName,jdbcType=VARCHAR},</if>
            <if test="clientName != null ">CLIENT_NAME = #{clientName,jdbcType=VARCHAR},</if>
            <if test="contactType != null ">CONTACT_TYPE = #{contactType,jdbcType=VARCHAR},</if>
            <if test="occupationCode != null ">OCCUPATION_CODE = #{occupationCode,jdbcType=VARCHAR},</if>
            <if test="education != null ">EDUCATION = #{education,jdbcType=VARCHAR},</if>
            <if test="distCode != null ">DIST_CODE = #{distCode,jdbcType=VARCHAR},</if>
            <if test="email != null ">EMAIL = #{email,jdbcType=VARCHAR},</if>
            <if test="localMessage != null ">LOCAL_MESSAGE = #{localMessage,jdbcType=VARCHAR},</if>
            <if test="location != null ">LOCATION = #{location,jdbcType=VARCHAR},</if>
            <if test="mobileNo != null ">MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},</if>
            <if test="phoneNo != null ">PHONE_NO = #{phoneNo,jdbcType=VARCHAR},</if>
            <if test="postalCode != null ">POSTAL_CODE = #{postalCode,jdbcType=VARCHAR},</if>
            <if test="residentFlag != null ">RESIDENT_FLAG = #{residentFlag,jdbcType=VARCHAR},</if>
            <if test="socSecNo != null ">SOC_SEC_NO = #{socSecNo,jdbcType=VARCHAR},</if>
            <if test="guardian != null ">GUARDIAN = #{guardian,jdbcType=VARCHAR},</if>
            <if test="guardianPhone != null ">GUARDIAN_PHONE = #{guardianPhone,jdbcType=VARCHAR},</if>
            <if test="inlandOffshore != null ">INLAND_OFFSHORE = #{inlandOffshore,jdbcType=VARCHAR},</if>
            <if test="selfStatement != null ">SELF_STATEMENT = #{selfStatement,jdbcType=VARCHAR},</if>
            <if test="coprName != null ">COPR_NAME = #{coprName,jdbcType=VARCHAR},</if>
            <if test="coprSocSecNo != null ">COPR_SOC_SEC_NO = #{coprSocSecNo,jdbcType=VARCHAR},</if>
            <if test="guardianDocumentId != null ">GUARDIAN_DOCUMENT_ID = #{guardianDocumentId,jdbcType=VARCHAR},</if>
            <if test="guardianShip != null ">GUARDIAN_SHIP = #{guardianShip,jdbcType=VARCHAR},</if>
            <if test="openDate != null ">OPEN_DATE = #{openDate,jdbcType=DATE},</if>
            <if test="openBranch != null ">OPEN_BRANCH = #{openBranch,jdbcType=VARCHAR},</if>
            <if test="gainType != null ">GAIN_TYPE = #{gainType,jdbcType=VARCHAR},</if>
            <if test="openCcy != null ">OPEN_CCY = #{openCcy,jdbcType=VARCHAR},</if>
            <if test="fromCardNo != null ">FROM_CARD_NO = #{fromCardNo,jdbcType=VARCHAR},</if>
            <if test="toCardNo != null ">TO_CARD_NO = #{toCardNo,jdbcType=VARCHAR},</if>
            <if test="cardPbInd != null ">CARD_PB_IND = #{cardPbInd,jdbcType=VARCHAR},</if>
            <if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
            <if test="acctClass != null ">ACCT_CLASS = #{acctClass,jdbcType=VARCHAR},</if>
            <if test="acctNature != null ">ACCT_NATURE = #{acctNature,jdbcType=VARCHAR},</if>
            <if test="allDepInd != null ">ALL_DEP_IND = #{allDepInd,jdbcType=VARCHAR},</if>
            <if test="allDraInd != null ">ALL_DRA_IND = #{allDraInd,jdbcType=VARCHAR},</if>
            <if test="reasonCode != null ">REASON_CODE = #{reasonCode,jdbcType=VARCHAR},</if>
            <if test="docType != null ">DOC_TYPE = #{docType,jdbcType=VARCHAR},</if>
            <if test="prefix != null ">PREFIX = #{prefix,jdbcType=VARCHAR},</if>
            <if test="voucherNo != null ">VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},</if>
            <if test="tranAmt != null ">TRAN_AMT = #{tranAmt,jdbcType=DECIMAL},</if>
            <if test="withdrawalType != null ">WITHDRAWAL_TYPE = #{withdrawalType,jdbcType=VARCHAR},</if>
            <if test="reversalFlag != null ">REVERSAL_FLAG = #{reversalFlag,jdbcType=VARCHAR},</if>
            <if test="printCnt != null ">PRINT_CNT = #{printCnt,jdbcType=INTEGER},</if>
            <if test="batchStatus != null ">BATCH_STATUS = #{batchStatus,jdbcType=VARCHAR},</if>
            <if test="retMsg != null ">RET_MSG = #{retMsg,jdbcType=VARCHAR},</if>
            <if test="errorCode != null ">ERROR_CODE = #{errorCode,jdbcType=VARCHAR},</if>
            <if test="errorDesc != null ">ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},</if>
            <if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE},</if>
            <if test="company != null ">COMPANY = #{company,jdbcType=VARCHAR},</if>
        </set>
    </sql>

    <select id="getNotPrintCDbyBatchNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails">
        select distinct batch_no
        from RB_BATCH_OPEN_DETAILS
        where batch_status='S'
        and open_branch = #{openBranch}
        and open_date = #{openDate}
        and card_pb_ind = 'P'
        and print_cnt = '0'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectBatchOpenDetailsForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenDetails">
        select <include refid="Base_Column"/>
        from RB_BATCH_OPEN_DETAILS
        where BATCH_NO = #{batchNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        for update
    </select>
</mapper>
