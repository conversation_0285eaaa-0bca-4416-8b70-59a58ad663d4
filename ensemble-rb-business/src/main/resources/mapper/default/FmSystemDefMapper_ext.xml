<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.fm.model.FmSystemDef">
	<select id="selectSystemListByIds" parameterType="java.util.Map" resultType="com.dcits.ensemble.fm.model.FmSystemDef">
		select <include refid="Base_Column"/>
		from FM_SYSTEM_DEF
		where SYSTEM_ID IN
		<foreach collection="systemIds" item="systemId" index="index" open="(" close=")" separator=",">
			#{systemId}
		</foreach>
	</select>
</mapper>