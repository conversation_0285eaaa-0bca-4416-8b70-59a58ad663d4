<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSmsTypeDefine">

	<select id="getSmsTypeByAmt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSmsTypeDefine" parameterType="java.util.Map" >
		select <include refid="Base_Column"/>
		from  RB_SMS_TYPE_DEFINE
		where  SMS_MIN_AMT <![CDATA[>=]]> #{smsMinAmt}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getSmsTypeByTemplate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSmsTypeDefine" parameterType="java.util.Map" >
		select <include refid="Base_Column"/>
		from  RB_SMS_TYPE_DEFINE
		where  SMS_TYPE = #{smsType}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="getSmsTypeDefineBySmsTypeLanguageCode" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSmsTypeDefine" parameterType="java.util.Map" >
		select <include refid="Base_Column"/>
		from  RB_SMS_TYPE_DEFINE
		where  SMS_TYPE = #{smsType}
		and SMS_LANGUAGE_CODE = #{smsLanguageCode}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
