<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist" resultMap="mbAdRegisterHistMap">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER_HIST
    where HIST_SEQ_NO = #{histSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbAdHistOne" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist" resultMap="mbAdRegisterHistMap">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER_HIST
    <where>
      <if test="internalKey!= null">
        and INTERNAL_KEY = #{internalKey}
      </if>
      <if test="status!= null">
        and STATUS = #{status}
      </if>
      <if test="origReference!= null">
        and ORIG_REFERENCE = #{origReference}
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist">
    delete from RB_AD_REGISTER_HIST
    where HIST_SEQ_NO = #{histSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist">
    update RB_AD_REGISTER_HIST
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="crReference != null">
        CR_REFERENCE = #{crReference},
      </if>
      <if test="edReference != null">
        ED_REFERENCE = #{edReference},
      </if>
      <if test="ptReference != null">
        PT_REFERENCE = #{ptReference},
      </if>
      <if test="origSource != null">
        ORIG_SOURCE = #{origSource},
      </if>
      <if test="origAmt != null">
        ORIG_AMT = #{origAmt},
      </if>
      <if test="origTranType != null">
        ORIG_TRAN_TYPE = #{origTranType},
      </if>
      <if test="origReference != null">
        ORIG_REFERENCE = #{origReference},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE = #{sourceModule},
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO = #{channelSeqNo},
      </if>
      <if test="acceptContractNo != null">
        ACCEPT_CONTRACT_NO = #{acceptContractNo},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="feeAmt != null">
        FEE_AMT = #{feeAmt},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="openBranch != null">
        OPEN_BRANCH = #{openBranch},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientName != null">
        CLIENT_NAME = #{clientName},
      </if>
      <if test="nosVosNo != null">
        NOS_VOS_NO = #{nosVosNo},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="status != null">
        STATUS = #{status},
      </if>
      <if test="payerAcctNo != null">
        PAYER_ACCT_NO = #{payerAcctNo},
      </if>
      <if test="payerAcctSeqNo != null">
        PAYER_ACCT_SEQ_NO = #{payerAcctSeqNo},
      </if>
      <if test="payerProdeType != null">
        PAYER_PRODE_TYPE = #{payerProdeType},
      </if>
      <if test="payerAcctCcy != null">
        PAYER_ACCT_CCY = #{payerAcctCcy},
      </if>
      <if test="payerName != null">
        PAYER_NAME = #{payerName},
      </if>
      <if test="payerAddr != null">
        PAYER_ADDR = #{payerAddr},
      </if>
      <if test="payerBankCode != null">
        PAYER_BANK_CODE = #{payerBankCode},
      </if>
      <if test="payerBankName != null">
        PAYER_BANK_NAME = #{payerBankName},
      </if>
      <if test="payerBankAddr != null">
        PAYER_BANK_ADDR = #{payerBankAddr},
      </if>
      <if test="payeeBaseAcctNo != null">
        PAYEE_BASE_ACCT_NO = #{payeeBaseAcctNo},
      </if>
      <if test="payeeAcctSeqNo != null">
        PAYEE_ACCT_SEQ_NO = #{payeeAcctSeqNo},
      </if>
      <if test="payeeProdeType != null">
        PAYEE_PRODE_TYPE = #{payeeProdeType},
      </if>
      <if test="payeeAcctCcy != null">
        PAYEE_ACCT_CCY = #{payeeAcctCcy},
      </if>
      <if test="payeeAcctName != null">
        PAYEE_ACCT_NAME = #{payeeAcctName},
      </if>
      <if test="payeeAddr != null">
        PAYEE_ADDR = #{payeeAddr},
      </if>
      <if test="payeeAcctType != null">
        PAYEE_ACCT_TYPE = #{payeeAcctType},
      </if>
      <if test="payeeBankCode != null">
        PAYEE_BANK_CODE = #{payeeBankCode},
      </if>
      <if test="payeeBankName != null">
        PAYEE_BANK_NAME = #{payeeBankName},
      </if>
      <if test="payeeBankAddr != null">
        PAYEE_BANK_ADDR = #{payeeBankAddr},
      </if>
      <if test="acceptBaseAcctNo != null">
        ACCEPT_BASE_ACCT_NO = #{acceptBaseAcctNo},
      </if>
      <if test="acceptAcctSeqNo != null">
        ACCEPT_ACCT_SEQ_NO = #{acceptAcctSeqNo},
      </if>
      <if test="acceptProdType != null">
        ACCEPT_PROD_TYPE = #{acceptProdType},
      </if>
      <if test="acceptAcctCcy != null">
        ACCEPT_ACCT_CCY = #{acceptAcctCcy},
      </if>
      <if test="acceptName != null">
        ACCEPT_NAME = #{acceptName},
      </if>
      <if test="acceptAddr != null">
        ACCEPT_ADDR = #{acceptAddr},
      </if>
      <if test="acceptBankCode != null">
        ACCEPT_BANK_CODE = #{acceptBankCode},
      </if>
      <if test="acceptBankName != null">
        ACCEPT_BANK_NAME = #{acceptBankName},
      </if>
      <if test="acceptBankAddr != null">
        ACCEPT_BANK_ADDR = #{acceptBankAddr},
      </if>
      <if test="lastHolderBaseAcctNo != null">
        LAST_HOLDER_BASE_ACCT_NO = #{lastHolderBaseAcctNo},
      </if>
      <if test="lastHolderAcctSeqNo != null">
        LAST_HOLDER_ACCT_SEQ_NO = #{lastHolderAcctSeqNo},
      </if>
      <if test="lastHolderProdeType != null">
        LAST_HOLDER_PRODE_TYPE = #{lastHolderProdeType},
      </if>
      <if test="lastHolderAcctCcy != null">
        LAST_HOLDER_ACCT_CCY = #{lastHolderAcctCcy},
      </if>
      <if test="lastHolderNeme != null">
        LAST_HOLDER_NEME = #{lastHolderNeme},
      </if>
      <if test="lastHolderAddr != null">
        LAST_HOLDER_ADDR = #{lastHolderAddr},
      </if>
      <if test="lastHolderBankCode != null">
        LAST_HOLDER_BANK_CODE = #{lastHolderBankCode},
      </if>
      <if test="lastHolderBankName != null">
        LAST_HOLDER_BANK_NAME = #{lastHolderBankName},
      </if>
      <if test="lastHolderBankAddr != null">
        LAST_HOLDER_BANK_ADDR = #{lastHolderBankAddr},
      </if>
      <if test="billCode != null">
        BILL_CODE = #{billCode},
      </if>
      <if test="billTransferFlag != null">
        BILL_TRANSFER_FLAG = #{billTransferFlag},
      </if>
      <if test="billType != null">
        BILL_TYPE = #{billType},
      </if>
      <if test="billPrefix != null">
        BILL_PREFIX = #{billPrefix},
      </if>
      <if test="billNo != null">
        BILL_NO = #{billNo},
      </if>
      <if test="billAmt != null">
        BILL_AMT = #{billAmt},
      </if>
      <if test="billSignDate != null">
        BILL_SIGN_DATE = #{billSignDate},
      </if>
      <if test="billSignBank != null">
        BILL_SIGN_BANK = #{billSignBank},
      </if>
      <if test="billAcceptDate != null">
        BILL_ACCEPT_DATE = #{billAcceptDate},
      </if>
      <if test="billAcceptBank != null">
        BILL_ACCEPT_BANK = #{billAcceptBank},
      </if>
      <if test="billMaturityDate != null">
        BILL_MATURITY_DATE = #{billMaturityDate},
      </if>
      <if test="billExpireDate != null">
        BILL_EXPIRE_DATE = #{billExpireDate},
      </if>
      <if test="billMediumType != null">
        BILL_MEDIUM_TYPE = #{billMediumType},
      </if>
      <if test="operUserId != null">
        OPER_USER_ID = #{operUserId},
      </if>
      <if test="operDate != null">
        OPER_DATE = #{operDate},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="authDate != null">
        AUTH_DATE = #{authDate},
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="errorMsg != null">
        ERROR_MSG = #{errorMsg},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="printCnt != null">
        PRINT_CNT = #{printCnt},
      </if>
      <if test="acceptBranch != null">
        ACCEPT_BRANCH = #{acceptBranch},
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG = #{glPostedFlag},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="isOwnHolder != null">
        IS_OWN_HOLDER_FLAG = #{isOwnHolder},
      </if>
      <if test="checkupNo != null">
        CHECKUP_NO = #{checkupNo},
      </if>
      <if test="voucherType != null">
        VOUCHER_TYPE = #{voucherType},
      </if>
      <if test="voucherPrefix != null">
        VOUCHER_PREFIX = #{voucherPrefix},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo},
      </if>
      <if test="voucherTypeOld != null">
        VOUCHER_TYPE_OLD = #{voucherTypeOld},
      </if>
      <if test="voucherPrefixOld != null">
        VOUCHER_PREFIX_OLD = #{voucherPrefixOld},
      </if>
      <if test="voucherNoOld != null">
        VOUCHER_NO_OLD = #{voucherNoOld}
      </if>
    </set>
    where HIST_SEQ_NO = #{histSeqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist">
    insert into RB_AD_REGISTER_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="histSeqNo != null">
        HIST_SEQ_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="crReference != null">
        CR_REFERENCE,
      </if>
      <if test="edReference != null">
        ED_REFERENCE,
      </if>
      <if test="ptReference != null">
        PT_REFERENCE,
      </if>
      <if test="origSource != null">
        ORIG_SOURCE,
      </if>
      <if test="origAmt != null">
        ORIG_AMT,
      </if>
      <if test="origTranType != null">
        ORIG_TRAN_TYPE,
      </if>
      <if test="origReference != null">
        ORIG_REFERENCE,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="acceptContractNo != null">
        ACCEPT_CONTRACT_NO,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="feeAmt != null">
        FEE_AMT,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="openBranch != null">
        OPEN_BRANCH,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="nosVosNo != null">
        NOS_VOS_NO,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="payerAcctNo != null">
        PAYER_ACCT_NO,
      </if>
      <if test="payerAcctSeqNo != null">
        PAYER_ACCT_SEQ_NO,
      </if>
      <if test="payerProdeType != null">
        PAYER_PRODE_TYPE,
      </if>
      <if test="payerAcctCcy != null">
        PAYER_ACCT_CCY,
      </if>
      <if test="payerName != null">
        PAYER_NAME,
      </if>
      <if test="payerAddr != null">
        PAYER_ADDR,
      </if>
      <if test="payerBankCode != null">
        PAYER_BANK_CODE,
      </if>
      <if test="payerBankName != null">
        PAYER_BANK_NAME,
      </if>
      <if test="payerBankAddr != null">
        PAYER_BANK_ADDR,
      </if>
      <if test="payeeBaseAcctNo != null">
        PAYEE_BASE_ACCT_NO,
      </if>
      <if test="payeeAcctSeqNo != null">
        PAYEE_ACCT_SEQ_NO,
      </if>
      <if test="payeeProdeType != null">
        PAYEE_PRODE_TYPE,
      </if>
      <if test="payeeAcctCcy != null">
        PAYEE_ACCT_CCY,
      </if>
      <if test="payeeAcctName != null">
        PAYEE_ACCT_NAME,
      </if>
      <if test="payeeAddr != null">
        PAYEE_ADDR,
      </if>
      <if test="payeeAcctType != null">
        PAYEE_ACCT_TYPE,
      </if>
      <if test="payeeBankCode != null">
        PAYEE_BANK_CODE,
      </if>
      <if test="payeeBankName != null">
        PAYEE_BANK_NAME,
      </if>
      <if test="payeeBankAddr != null">
        PAYEE_BANK_ADDR,
      </if>
      <if test="acceptBaseAcctNo != null">
        ACCEPT_BASE_ACCT_NO,
      </if>
      <if test="acceptAcctSeqNo != null">
        ACCEPT_ACCT_SEQ_NO,
      </if>
      <if test="acceptProdType != null">
        ACCEPT_PROD_TYPE,
      </if>
      <if test="acceptAcctCcy != null">
        ACCEPT_ACCT_CCY,
      </if>
      <if test="acceptName != null">
        ACCEPT_NAME,
      </if>
      <if test="acceptAddr != null">
        ACCEPT_ADDR,
      </if>
      <if test="acceptBankCode != null">
        ACCEPT_BANK_CODE,
      </if>
      <if test="acceptBankName != null">
        ACCEPT_BANK_NAME,
      </if>
      <if test="acceptBankAddr != null">
        ACCEPT_BANK_ADDR,
      </if>
      <if test="lastHolderBaseAcctNo != null">
        LAST_HOLDER_BASE_ACCT_NO,
      </if>
      <if test="lastHolderAcctSeqNo != null">
        LAST_HOLDER_ACCT_SEQ_NO,
      </if>
      <if test="lastHolderProdeType != null">
        LAST_HOLDER_PRODE_TYPE,
      </if>
      <if test="lastHolderAcctCcy != null">
        LAST_HOLDER_ACCT_CCY,
      </if>
      <if test="lastHolderNeme != null">
        LAST_HOLDER_NEME,
      </if>
      <if test="lastHolderAddr != null">
        LAST_HOLDER_ADDR,
      </if>
      <if test="lastHolderBankCode != null">
        LAST_HOLDER_BANK_CODE,
      </if>
      <if test="lastHolderBankName != null">
        LAST_HOLDER_BANK_NAME,
      </if>
      <if test="lastHolderBankAddr != null">
        LAST_HOLDER_BANK_ADDR,
      </if>
      <if test="billCode != null">
        BILL_CODE,
      </if>
      <if test="billTransferFlag != null">
        BILL_TRANSFER_FLAG,
      </if>
      <if test="billType != null">
        BILL_TYPE,
      </if>
      <if test="billPrefix != null">
        BILL_PREFIX,
      </if>
      <if test="billNo != null">
        BILL_NO,
      </if>
      <if test="billAmt != null">
        BILL_AMT,
      </if>
      <if test="billSignDate != null">
        BILL_SIGN_DATE,
      </if>
      <if test="billSignBank != null">
        BILL_SIGN_BANK,
      </if>
      <if test="billAcceptDate != null">
        BILL_ACCEPT_DATE,
      </if>
      <if test="billAcceptBank != null">
        BILL_ACCEPT_BANK,
      </if>
      <if test="billMaturityDate != null">
        BILL_MATURITY_DATE,
      </if>
      <if test="billExpireDate != null">
        BILL_EXPIRE_DATE,
      </if>
      <if test="billMediumType != null">
        BILL_MEDIUM_TYPE,
      </if>
      <if test="operUserId != null">
        OPER_USER_ID,
      </if>
      <if test="operDate != null">
        OPER_DATE,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="authDate != null">
        AUTH_DATE,
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="errorMsg != null">
        ERROR_MSG,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="printCnt != null">
        PRINT_CNT,
      </if>
      <if test="acceptBranch != null">
        ACCEPT_BRANCH,
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="isOwnHolder != null">
        IS_OWN_HOLDER_FLAG,
      </if>
      <if test="checkupNo != null">
        CHECKUP_NO,
      </if>
      <if test="voucherType != null">
        VOUCHER_TYPE,
      </if>
      <if test="voucherPrefix != null">
        VOUCHER_PREFIX,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="voucherTypeOld != null">
        VOUCHER_TYPE_OLD,
      </if>
      <if test="voucherPrefixOld != null">
        VOUCHER_PREFIX_OLD,
      </if>
      <if test="voucherNoOld != null">
        VOUCHER_NO_OLD,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="histSeqNo != null">
        #{histSeqNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="crReference != null">
        #{crReference},
      </if>
      <if test="edReference != null">
        #{edReference},
      </if>
      <if test="ptReference != null">
        #{ptReference},
      </if>
      <if test="origSource != null">
        #{origSource},
      </if>
      <if test="origAmt != null">
        #{origAmt},
      </if>
      <if test="origTranType != null">
        #{origTranType},
      </if>
      <if test="origReference != null">
        #{origReference},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="sourceModule != null">
        #{sourceModule},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="acceptContractNo != null">
        #{acceptContractNo},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="feeAmt != null">
        #{feeAmt},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="openBranch != null">
        #{openBranch},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="nosVosNo != null">
        #{nosVosNo},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="payerAcctNo != null">
        #{payerAcctNo},
      </if>
      <if test="payerAcctSeqNo != null">
        #{payerAcctSeqNo},
      </if>
      <if test="payerProdeType != null">
        #{payerProdeType},
      </if>
      <if test="payerAcctCcy != null">
        #{payerAcctCcy},
      </if>
      <if test="payerName != null">
        #{payerName},
      </if>
      <if test="payerAddr != null">
        #{payerAddr},
      </if>
      <if test="payerBankCode != null">
        #{payerBankCode},
      </if>
      <if test="payerBankName != null">
        #{payerBankName},
      </if>
      <if test="payerBankAddr != null">
        #{payerBankAddr},
      </if>
      <if test="payeeBaseAcctNo != null">
        #{payeeBaseAcctNo},
      </if>
      <if test="payeeAcctSeqNo != null">
        #{payeeAcctSeqNo},
      </if>
      <if test="payeeProdeType != null">
        #{payeeProdeType},
      </if>
      <if test="payeeAcctCcy != null">
        #{payeeAcctCcy},
      </if>
      <if test="payeeAcctName != null">
        #{payeeAcctName},
      </if>
      <if test="payeeAddr != null">
        #{payeeAddr},
      </if>
      <if test="payeeAcctType != null">
        #{payeeAcctType},
      </if>
      <if test="payeeBankCode != null">
        #{payeeBankCode},
      </if>
      <if test="payeeBankName != null">
        #{payeeBankName},
      </if>
      <if test="payeeBankAddr != null">
        #{payeeBankAddr},
      </if>
      <if test="acceptBaseAcctNo != null">
        #{acceptBaseAcctNo},
      </if>
      <if test="acceptAcctSeqNo != null">
        #{acceptAcctSeqNo},
      </if>
      <if test="acceptProdType != null">
        #{acceptProdType},
      </if>
      <if test="acceptAcctCcy != null">
        #{acceptAcctCcy},
      </if>
      <if test="acceptName != null">
        #{acceptName},
      </if>
      <if test="acceptAddr != null">
        #{acceptAddr},
      </if>
      <if test="acceptBankCode != null">
        #{acceptBankCode},
      </if>
      <if test="acceptBankName != null">
        #{acceptBankName},
      </if>
      <if test="acceptBankAddr != null">
        #{acceptBankAddr},
      </if>
      <if test="lastHolderBaseAcctNo != null">
        #{lastHolderBaseAcctNo},
      </if>
      <if test="lastHolderAcctSeqNo != null">
        #{lastHolderAcctSeqNo},
      </if>
      <if test="lastHolderProdeType != null">
        #{lastHolderProdeType},
      </if>
      <if test="lastHolderAcctCcy != null">
        #{lastHolderAcctCcy},
      </if>
      <if test="lastHolderNeme != null">
        #{lastHolderNeme},
      </if>
      <if test="lastHolderAddr != null">
        #{lastHolderAddr},
      </if>
      <if test="lastHolderBankCode != null">
        #{lastHolderBankCode},
      </if>
      <if test="lastHolderBankName != null">
        #{lastHolderBankName},
      </if>
      <if test="lastHolderBankAddr != null">
        #{lastHolderBankAddr},
      </if>
      <if test="billCode != null">
        #{billCode},
      </if>
      <if test="billTransferFlag != null">
        #{billTransferFlag},
      </if>
      <if test="billType != null">
        #{billType},
      </if>
      <if test="billPrefix != null">
        #{billPrefix},
      </if>
      <if test="billNo != null">
        #{billNo},
      </if>
      <if test="billAmt != null">
        #{billAmt},
      </if>
      <if test="billSignDate != null">
        #{billSignDate},
      </if>
      <if test="billSignBank != null">
        #{billSignBank},
      </if>
      <if test="billAcceptDate != null">
        #{billAcceptDate},
      </if>
      <if test="billAcceptBank != null">
        #{billAcceptBank},
      </if>
      <if test="billMaturityDate != null">
        #{billMaturityDate},
      </if>
      <if test="billExpireDate != null">
        #{billExpireDate},
      </if>
      <if test="billMediumType != null">
        #{billMediumType},
      </if>
      <if test="operUserId != null">
        #{operUserId},
      </if>
      <if test="operDate != null">
        #{operDate},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="authDate != null">
        #{authDate},
      </if>
      <if test="lastChangeUserId != null">
        #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="errorMsg != null">
        #{errorMsg},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="printCnt != null">
        #{printCnt},
      </if>
      <if test="acceptBranch != null">
        #{acceptBranch},
      </if>
      <if test="glPostedFlag != null">
        #{glPostedFlag},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="isOwnHolder != null">
        #{isOwnHolder},
      </if>
      <if test="checkupNo != null">
        #{checkupNo},
      </if>
      <if test="voucherType != null">
        #{voucherType},
      </if>
      <if test="voucherPrefix != null">
        #{voucherPrefix},
      </if>
      <if test="voucherNo != null">
        #{voucherNo},
      </if>
      <if test="voucherTypeOld != null">
        #{voucherTypeOld},
      </if>
      <if test="voucherPrefixOld != null">
        #{voucherPrefixOld},
      </if>
      <if test="voucherNoOld != null">
        #{voucherNoOld},
      </if>
    </trim>
  </insert>
  <select id="getMbAdRegisterHist" parameterType="java.util.Map" resultMap="mbAdRegisterHistMap">
    SELECT <include refid="Base_Column"/> FROM RB_AD_REGISTER_HIST
    <where>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        COMPANY = #{company}
      </if>
      <if test="tranBranch != null">
        AND TRAN_BRANCH = #{tranBranch}
      </if>
      <if test="dateType != null and dateType == '1'">
        AND BILL_SIGN_DATE BETWEEN  #{startDate} AND #{endDate}
      </if>
      <if test="dateType != null and dateType == '2'">
        AND BILL_ACCEPT_DATE BETWEEN  #{startDate} AND #{endDate}
      </if>
      <if test="dateType != null and dateType == '3'">
        AND BILL_MATURITY_DATE BETWEEN  #{startDate} AND #{endDate}
      </if>
      <if test="dateType != null and dateType == '4'">
        AND BILL_EXPIRE_DATE BETWEEN  #{startDate} AND #{endDate}
      </if>
      <if test="tranDate != null">
        AND TRAN_DATE BETWEEN  #{startDate} AND #{endDate}
      </if>
      <if test="acceptBaseAcctNo != null">
        AND ACCEPT_BASE_ACCT_NO = #{acceptBaseAcctNo}
      </if>
      <if test="billType != null">
        AND BILL_TYPE = #{billType}
      </if>
      <if test="billPrefix != null">
        AND BILL_PREFIX = #{billPrefix}
      </if>
      <if test="billNo != null">
        AND BILL_NO = #{billNo}
      </if>
      <if test="status != null">
        AND STATUS = #{status}
      </if>
    </where>
    ORDER BY TRAN_DATE
  </select>
  <resultMap id="mbAdRegisterHistMap" type="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist">
    <result column="TRAN_DATE" property="tranDate" jdbcType="DATE" javaType="String" />
  </resultMap>
</mapper>
