<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmTranLimitDef">

  <select id="getLmTranLimitDefByMainType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmTranLimitDef"
          parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmTranLimitDef" >
    select <include refid="Base_Column"/>
    from RB_LM_TRAN_LIMIT_DEF where 1=1
    <if test="limitRef != null">
      AND LIMIT_REF = #{limitRef,jdbcType=VARCHAR}
    </if>
    <if test="limitMainType !=null and limitMainType != '' and limitMainType !='ALL' ">
      AND LIMIT_MAIN_TYPE= #{limitMainType}
    </if>
    <if test="limitMainType !=null and limitMainType != '' and limitMainType =='ALL' ">
      AND LIMIT_MAIN_TYPE IN ('01','02','03','04','06','07','08','09','10','11','12','13')
    </if>
    <if test="limitType != null and limitType != ''">
      AND LIMIT_TYPE = #{limitType,jdbcType=VARCHAR}
    </if>
    <if test="limitLevel != null and limitLevel != ''">
      AND LIMIT_LEVEL = #{limitLevel,jdbcType=VARCHAR}
    </if>
    <if test="limitStatus != null and limitStatus != ''">
      AND LIMIT_STATUS = #{limitStatus,jdbcType=VARCHAR}
    </if>
    ORDER BY LIMIT_MAIN_TYPE
  </select>

  <select id="getLmTranLimitDefByLimitMainType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmTranLimitDef"
          parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_LM_TRAN_LIMIT_DEF where 1=1
    <if test="limitRef != null and limitRef != ''">
      AND LIMIT_REF = #{limitRef,jdbcType=VARCHAR}
    </if>
    <if test="limitMainType !=null and limitMainType != ''">
      AND LIMIT_MAIN_TYPE = #{limitMainType}
    </if>
    <if test="limitType != null and limitType != ''">
      AND LIMIT_TYPE = #{limitType,jdbcType=VARCHAR}
    </if>
    <if test="limitLevel != null and limitLevel != ''">
      AND LIMIT_LEVEL = #{limitLevel,jdbcType=VARCHAR}
    </if>
    <if test="limitStatus != null and limitStatus != ''">
      AND LIMIT_STATUS = #{limitStatus,jdbcType=VARCHAR}
    </if>
    ORDER BY LIMIT_MAIN_TYPE
  </select>

  <select id="getAreaCashLimitInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmTranLimitDef"
          parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_LM_TRAN_LIMIT_DEF where 1=1
    <if test="limitRef != null and limitRef != ''">
      AND LIMIT_REF = #{limitRef,jdbcType=VARCHAR}
    </if>
    <if test="limitType != null and limitType != ''">
      AND LIMIT_TYPE = #{limitType,jdbcType=VARCHAR}
    </if>
      AND LIMIT_REF LIKE 'AreaCash%'
  </select>

</mapper>