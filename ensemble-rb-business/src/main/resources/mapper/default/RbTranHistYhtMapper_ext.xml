<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistYht">

  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistYht">
    insert into RB_TRAN_HIST_YHT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="oldReference != null">
        OLD_REFERENCE,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="othInternalKey != null">
        OTH_INTERNAL_KEY,
      </if>
      <if test="othBaseAcctNo != null">
        OTH_BASE_ACCT_NO,
      </if>
      <if test="othAcctCcy != null">
        OTH_ACCT_CCY,
      </if>
      <if test="othProdType != null">
        OTH_PROD_TYPE,
      </if>
      <if test="othAcctSeqNo != null">
        OTH_ACCT_SEQ_NO,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="oldReference != null">
        #{oldReference},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="othInternalKey != null">
        #{othInternalKey},
      </if>
      <if test="othBaseAcctNo != null">
        #{othBaseAcctNo},
      </if>
      <if test="othAcctCcy != null">
        #{othAcctCcy},
      </if>
      <if test="othProdType != null">
        #{othProdType},
      </if>
      <if test="othAcctSeqNo != null">
        #{othAcctSeqNo},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="issDealed" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistYht">
    select <include refid="Base_Column"/> from RB_TRAN_HIST_YHT
    where 1=1
    <if test="reference !=  null">
      AND REFERENCE= #{reference}
    </if>
    <if test="internalKey !=  null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="othInternalKey !=  null">
      AND OTH_INTERNAL_KEY = #{othInternalKey}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbTranHistYhtByRefClientNo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistYht">
    select <include refid="Base_Column"/> from RB_TRAN_HIST_YHT
    <where>  CLIENT_NO = #{clientNo}
      AND TRAN_STATUS != 'R'
      AND TRAN_STATUS != 'X'
      AND TRAN_STATUS != 'W'
      <if test="taeSubSeqNo != null and  taeSubSeqNo != '' ">
        AND TAE_SUB_SEQ_NO = #{taeSubSeqNo}
      </if>
      <if test="reference !=  null">
        AND REFERENCE= #{reference}
      </if>
      <if test="internalKey !=  null">
        AND INTERNAL_KEY = #{internalKey}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    order by TRAN_DATE desc,TRAN_TIMESTAMP desc,SEQ_NO+0 desc
  </select>
  
</mapper>
