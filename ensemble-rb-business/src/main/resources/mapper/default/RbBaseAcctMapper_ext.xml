<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
    <update id="updateBaseAcctByClientNo">
        UPDATE
		RB_BASE_ACCT
		<set>
			<if test="internalKey != null ">INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},</if>
			<if test="documentId != null ">DOCUMENT_ID = #{documentId,jdbcType=VARCHAR},</if>
			<if test="documentType != null ">DOCUMENT_TYPE = #{documentType,jdbcType=VARCHAR},</if>
			<if test="clientType != null ">CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},</if>
			<if test="issCountry != null ">ISS_COUNTRY = #{issCountry,jdbcType=VARCHAR},</if>
			<if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
			<if test="baseAcctNo != null ">BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},</if>
			<if test="acctCcy != null ">ACCT_CCY = #{acctCcy,jdbcType=VARCHAR},</if>
			<if test="acctSeqNo != null ">ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},</if>
			<if test="cardNo != null ">CARD_NO = #{cardNo,jdbcType=VARCHAR},</if>
			<if test="acctStatus != null ">ACCT_STATUS = #{acctStatus,jdbcType=VARCHAR},</if>
			<if test="acctType != null ">ACCT_TYPE = #{acctType,jdbcType=VARCHAR},</if>
			<if test="acctName != null ">ACCT_NAME = #{acctName,jdbcType=VARCHAR},</if>
			<if test="altAcctName != null ">ALT_ACCT_NAME = #{altAcctName,jdbcType=VARCHAR},</if>
			<if test="acctDesc != null ">ACCT_DESC = #{acctDesc,jdbcType=VARCHAR},</if>
			<if test="acctExec != null ">ACCT_EXEC = #{acctExec,jdbcType=VARCHAR},</if>
			<if test="acctOpenDate != null ">ACCT_OPEN_DATE = #{acctOpenDate,jdbcType=DATE},</if>
			<if test="acctResStatus != null ">ACCT_RES_STATUS = #{acctResStatus,jdbcType=VARCHAR},</if>
			<if test="fixedCall != null ">FIXED_CALL = #{fixedCall,jdbcType=VARCHAR},</if>
			<if test="acctBranch != null ">ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR},</if>
			<if test="docType != null ">DOC_TYPE = #{docType,jdbcType=VARCHAR},</if>
			<if test="prefix != null ">PREFIX = #{prefix,jdbcType=VARCHAR},</if>
			<if test="voucherStartNo != null ">VOUCHER_START_NO = #{voucherStartNo,jdbcType=VARCHAR},</if>
			<if test="voucherStatus != null ">VOUCHER_STATUS = #{voucherStatus,jdbcType=VARCHAR},</if>
			<if test="checkedFlag != null ">CHECKED_FLAG = #{checkedFlag,jdbcType=VARCHAR},</if>
			<if test="sourceType != null ">SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},</if>
			<if test="oldProdType != null ">OLD_PROD_TYPE = #{oldProdType,jdbcType=VARCHAR},</if>
			<if test="acctCloseDate != null ">ACCT_CLOSE_DATE = #{acctCloseDate,jdbcType=DATE},</if>
			<if test="acctCloseReason != null ">ACCT_CLOSE_REASON = #{acctCloseReason,jdbcType=VARCHAR},</if>
			<if test="acctStatusPrev != null ">ACCT_STATUS_PREV = #{acctStatusPrev,jdbcType=VARCHAR},</if>
			<if test="acctStatusUpdDate != null ">ACCT_STATUS_UPD_DATE = #{acctStatusUpdDate,jdbcType=DATE},</if>
			<if test="terminalId != null ">TERMINAL_ID = #{terminalId,jdbcType=VARCHAR},</if>
			<if test="acctCloseUserId != null ">ACCT_CLOSE_USER_ID = #{acctCloseUserId,jdbcType=VARCHAR},</if>
			<if test="userId != null ">USER_ID = #{userId,jdbcType=VARCHAR},</if>
			<if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},</if>
			<if test="lastChangeDate != null ">LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE},</if>
			<if test="lastChangeUserId != null ">LAST_CHANGE_USER_ID = #{lastChangeUserId,jdbcType=VARCHAR},</if>
			<if test="company != null ">COMPANY = #{company,jdbcType=VARCHAR},</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="internalKey != null ">
					internal_key = #{internalKey}  AND
				</if>
				<if test="clientNo != null and clientNo != ''">
				   CLIENT_NO = #{clientNo,jdbcType=VARCHAR} AND
				</if>
				<if test="cardNo != null and  cardNo != '' ">
					CARD_NO = #{cardNo,jdbcType=VARCHAR} AND
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

<!-- Parent account cancellation and reopening -->
	<update id="updateBaseAcctForReOpen" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		update RB_BASE_ACCT
		<set>
			ACCT_CLOSE_DATE = null,
			<if test="lastChangeDate != null ">
				LAST_CHANGE_DATE = #{lastChangeDate},
			</if>
			<if test="tranTimestamp != null ">
				TRAN_TIMESTAMP = #{tranTimestamp},
			</if>
			<if test="company != null and  company != '' ">
				COMPANY = #{company},
			</if>
			<if test="acctCloseUserId != null">
				ACCT_CLOSE_USER_ID = #{acctCloseUserId},
			</if>
			<if test="acctStatus != null and  acctStatus != '' ">
				ACCT_STATUS = #{acctStatus},
			</if>
			<if test="acctStatusPrev != null and  acctStatusPrev != '' ">
				ACCT_STATUS_PREV = #{acctStatusPrev},
			</if>
			<if test="acctStatusUpdDate != null ">
				ACCT_STATUS_UPD_DATE = #{acctStatusUpdDate},
			</if>
			<if test="lastChangeUserId != null and  lastChangeUserId != '' ">
				LAST_CHANGE_USER_ID = #{lastChangeUserId},
			</if>
		</set>
		where CLIENT_NO = #{clientNo}
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND INTERNAL_KEY = #{internalKey}
	</update>

	<select id="getCancelRbBaseAcct" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column" />
		FROM RB_BASE_ACCT
		WHERE (ACCT_STATUS != 'C' or (ACCT_STATUS = 'C' and ACCT_CLOSE_DATE = #{acctCloseDate,jdbcType=VARCHAR}))
		and BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
		and LEAD_ACCT_FLAG = 'Y'
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY ACCT_SEQ_NO ASC
	</select>
	<select id="getBaseAcctByClientNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column" />
		FROM RB_BASE_ACCT
		<where>
			AND ACCT_STATUS != 'C'
			<if test= "clientNo != null and clientNo != ''">
				AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
			</if>
			<if test= "acctStatus != null and acctStatus != ''">
				AND ACCT_STATUS = #{acctStatus},
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="baseAcctNo != null and baseAcctNo.length() > 0">
				AND (BASE_ACCT_NO =#{baseAcctNo} or CARD_NO =#{baseAcctNo})
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
		ORDER BY ACCT_SEQ_NO ASC
	</select>
	<select id="getBaseAcctByClientNoByStatusFlag1" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column" />
		FROM RB_BASE_ACCT
		WHERE CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
		<if test= "acctStatus != null and acctStatus != ''">
			ACCT_STATUS = #{acctStatus},
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY ACCT_SEQ_NO ASC
	</select>
	<select id="getBaseAcctByClientNoByStatusFlag2" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column" />
		FROM RB_BASE_ACCT
		WHERE ACCT_STATUS = 'C'
		and CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
		<if test= "acctStatus != null and acctStatus != ''">
			ACCT_STATUS = #{acctStatus},
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY ACCT_SEQ_NO ASC
	</select>
	<update id="updateMbBaseAcctNames" parameterType="java.util.Map">
		update RB_BASE_ACCT
		<set>
			<if test="acctName != null">
				ACCT_NAME = #{acctName},
			</if>
			<if test="acctDesc != null">
				ACCT_DESC = #{acctDesc},
			</if>
		</set>
		where  CLIENT_NO = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<update id="updateMbBaseAcctDocumentMsg" parameterType="java.util.Map">
		update RB_BASE_ACCT
		<set>
			<if test= "acctName != null and acctName != ''">
				ACCT_NAME = #{acctName},
			</if>
			<if test= "altAcctName != null and altAcctName != ''">
				ALT_ACCT_NAME = #{altAcctName},
			</if>
			<if test = "documentId !=null and documentId != ''">
				DOCUMENT_ID = #{documentId},
			</if>
			<if test = "documentType !=null and documentType != ''">
				DOCUMENT_TYPE = #{documentType},
			</if>
			<if test = "issCountry !=null and issCountry != ''">
				ISS_COUNTRY = #{issCountry},
			</if>
		</set>
		where  CLIENT_NO = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

<!-- Parent account cancellation and reversal account status change -->
	<update id="updateBaseAcctByClientNoForReversal">
		UPDATE
		RB_BASE_ACCT
		<set>
			ACCT_CLOSE_DATE = null,
			<if test="acctStatus != null ">ACCT_STATUS = #{acctStatus,jdbcType=VARCHAR},</if>
			<if test="acctStatusPrev != null ">ACCT_STATUS_PREV = #{acctStatusPrev,jdbcType=VARCHAR},</if>
			<if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="internalKey != null ">
					internal_key = #{internalKey}  AND
				</if>
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo,jdbcType=VARCHAR} AND
				</if>
				<if test="cardNo != null and  cardNo != '' ">
					CARD_NO = #{cardNo,jdbcType=VARCHAR} AND
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

	<update id="updateBaseAcctByInternalKeyAndClientNo">
		UPDATE
		RB_BASE_ACCT
		<set>
			<if test="internalKey != null ">INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},</if>
			<if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
			<if test="baseAcctNo != null ">BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},</if>
			<if test="acctCcy != null ">ACCT_CCY = #{acctCcy,jdbcType=VARCHAR},</if>
			<if test="acctSeqNo != null ">ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},</if>
			<if test="cardNo != null ">CARD_NO = #{cardNo,jdbcType=VARCHAR},</if>
			<if test="acctType != null ">ACCT_TYPE = #{acctType,jdbcType=VARCHAR},</if>
			<if test="docType != null ">DOC_TYPE = #{docType,jdbcType=VARCHAR},</if>
			<if test="prefix != null ">PREFIX = #{prefix,jdbcType=VARCHAR},</if>
			<if test="voucherStartNo != null ">VOUCHER_START_NO = #{voucherStartNo,jdbcType=VARCHAR},</if>
			<if test="voucherStatus != null ">VOUCHER_STATUS = #{voucherStatus,jdbcType=VARCHAR},</if>
			<if test="userId != null ">USER_ID = #{userId,jdbcType=VARCHAR},</if>
			<if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE},</if>
			<if test="lastChangeDate != null ">LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE},</if>
			<if test="lastChangeUserId != null ">LAST_CHANGE_USER_ID = #{lastChangeUserId,jdbcType=VARCHAR},</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="internalKey != null ">
					internal_key = #{internalKey}  AND
				</if>
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo,jdbcType=VARCHAR} AND
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

	<select id="getMbBaseAcctInfoByPrimaryKey" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		<where>
			<if test="statusFlag==null or statusFlag==''">
				AND acct_status != 'C'
			</if>
			<if test="baseAcctNo != null and baseAcctNo !=''">
				AND base_acct_no =#{baseAcctNo}
			</if>
			<if test="cardNo != null and cardNo !=''">
				AND card_no =#{cardNo}
			</if>
			<if test="prodType != null and prodType !=''">
				AND prod_type = #{prodType}
			</if>
			<if test="acctCcy != null and acctCcy !=''">
				AND ACCT_CCY = #{acctCcy}
			</if>
			<if test="acctSeqNo != null and acctSeqNo !=''">
				AND ACCT_SEQ_NO = #{acctSeqNo}
			</if>
			<if test="clientNo != null and clientNo !=''">
				AND CLIENT_NO = #{clientNo}
			</if>
<!-- 多法人改造 by LIYUANV -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
		ORDER BY base_acct_no,ACCT_SEQ_NO+0 ASC
	</select>

	<select id="getMbBaseAcctByBaseAcctNoOrCardNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE (BASE_ACCT_NO = #{baseAcctNo} or CARD_NO = #{baseAcctNo})
		<if test="sourceModule != null and sourceModule != ''">
			AND SOURCE_MODULE = #{sourceModule}
		</if>
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO = #{clientNo}
		</if>
		ORDER BY base_acct_no,ACCT_SEQ_NO+0 ASC
	</select>

	<select id="getMbBaseAcctByBaseAcctNoAndSeqno" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE (BASE_ACCT_NO = #{baseAcctNo} or CARD_NO = #{baseAcctNo})
	</select>

	<select id="getBaseAcctByAcctStatus" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE ACCT_STATUS != 'C'
	</select>

	<select id="getBaseAcctByParams" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE SOURCE_MODULE = 'GL'
		<if test="baseAcctNo != null and baseAcctNo !=''">
			AND BASE_ACCT_NO =#{baseAcctNo}
		</if>
		<if test="acctType != null and acctType !=''">
			AND ACCT_TYPE = #{acctType}
		</if>
		<if test="prodType != null and prodType !=''">
			AND PROD_TYPE = #{prodType}
		</if>
		<if test="branch != null and branch !=''">
			AND ACCT_BRANCH = #{branch}
		</if>
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getBaseAcctByInternalKey" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		<where>
		<if test="internalKey != null and internalKey !=''">
			AND INTERNAL_KEY =#{internalKey}
		</if>
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="sourceModule != null and sourceModule != ''">
			AND SOURCE_MODULE = #{sourceModule}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>
	<select id="getBaseAcctByProdType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column" />
		FROM RB_BASE_ACCT
		WHERE prod_type = #{prodType,jdbcType=VARCHAR}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY ACCT_SEQ_NO ASC
	</select>

	<select id="getBaseAcctByBaseAcctNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column" />
		FROM RB_BASE_ACCT
		WHERE INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
		  and CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY ACCT_SEQ_NO ASC
	</select>

	<select id="getBaseAcctByInternalKeys" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE
		INTERNAL_KEY IN
		<foreach collection="internalKeys" item="internalKey" index="index" open="(" separator="," close=")">
			#{internalKey}
		</foreach>
	</select>

	<update id="updateCardNoByInternalkey" parameterType="java.util.Map">
		update RB_BASE_ACCT
		<set>
			<if test="newCardNo != null  and newCardNo.length() > 0">
				CARD_NO = #{newCardNo}
			</if>
			<if test="newProdType != null  and newProdType.length() > 0 and newCardNo != null and newCardNo.length() > 0">
				,
			</if>
			<if test="newProdType != null  and newProdType.length() > 0">
				PROD_TYPE = #{newProdType}
			</if>
		</set>
		where  INTERNAL_KEY = #{internalKey}
<!-- 多法人改造 by luocwa -->
		<if test="cardNo != null and cardNo.length() > 0">
			AND CARD_NO = #{cardNo}
		</if>
	</update>

	<update id="updateCardNoByInternalkey1" parameterType="java.util.Map">
		update RB_BASE_ACCT
		<set>
			<if test="newCardNo != null  and newCardNo.length() > 0">
				CARD_NO = #{newCardNo}
			</if>
			<if test="newProdType != null  and newProdType.length() > 0 and newCardNo != null and newCardNo.length() > 0">
				,
			</if>
			<if test="newProdType != null  and newProdType.length() > 0">
				PROD_TYPE = #{newProdType}
			</if>
		</set>
		where  INTERNAL_KEY = #{internalKey}
<!-- 多法人改造 by luocwa -->
		<if test="cardNo != null and cardNo.length() > 0">
			AND CARD_NO = #{cardNo}
		</if>
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO = #{clientNo}
		</if>
	</update>

	<update id="updateBaseAcctNoByInternalkey" parameterType="java.util.Map">
		update RB_BASE_ACCT
		<set>
			<if test="newCardNo != null  and newCardNo.length() > 0">
				BASE_ACCT_NO = #{newCardNo}
			</if>
		</set>
		where  INTERNAL_KEY = #{internalKey}
	</update>
	<update id="updateRbBaseAcctClientNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		update RB_BASE_ACCT
		<set>
			<if test="clientNo != null">
				CLIENT_NO = #{clientNo},
			</if>
			<if test="tranTimestamp != null">
				TRAN_TIMESTAMP = #{tranTimestamp},
			</if>
		</set>
		where INTERNAl_KEY = #{internalKey}
	</update>
	<select id="getMbBaseAcctAllByClientNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE acct_status != 'C'
		and client_no = #{clientNo}
	</select>
<!-- Organization-wide change information query -->
	<select id="getRbBaseAcctInfoForInternalKeyL" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		select
		<include refid="Base_Column"/>
		from RB_BASE_ACCT
		WHERE
		INTERNAL_KEY  between #{startKey} and #{endKey}
		and ACCT_BRANCH = #{acctBranch}
	</select>
	<select id="getMbBaseAcctByAcctNoForUpdate" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE BASE_ACCT_NO = #{baseAcctNo}
		FOR UPDATE
	</select>

	<update id="updateAcctStatusByBaseAcctNoAndClientNo">
		UPDATE
		RB_BASE_ACCT
		<set>
			<if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
			<if test="cardNo != null ">CARD_NO = #{cardNo,jdbcType=VARCHAR},</if>
			<if test="acctStatus != null ">ACCT_STATUS = #{acctStatus,jdbcType=VARCHAR},</if>
			<if test="acctStatusPrev != null ">ACCT_STATUS_PREV = #{acctStatusPrev,jdbcType=VARCHAR},</if>
			<if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE},</if>
			<if test="lastChangeDate != null ">LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE},</if>
			<if test="lastChangeUserId != null ">LAST_CHANGE_USER_ID = #{lastChangeUserId,jdbcType=VARCHAR},</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo,jdbcType=VARCHAR} AND
				</if>
				<if test="baseAcctNo != null and baseAcctNo != ''">
					BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR} AND
				</if>
			</trim>
		</where>
	</update>

	<update id="updateAcctStatusByBaseAcctNoAndVoucher">
		UPDATE
		RB_BASE_ACCT
		<set>
			<if test="docType == null ">DOC_TYPE = null,</if>
			<if test="voucherStartNo == null ">VOUCHER_START_NO = null,</if>
			<if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE},</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="clientNo != null and clientNo != ''">
					CLIENT_NO = #{clientNo,jdbcType=VARCHAR} AND
				</if>
				<if test="baseAcctNo != null and baseAcctNo != ''">
					BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR} AND
				</if>
			</trim>
		</where>
	</update>
	<select id="getBaseAcctByNoNew1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BASE_ACCT
		WHERE 1 = 1
		<if test="baseAcctNo != null and baseAcctNo !=''">
			AND (base_acct_no =#{baseAcctNo} OR card_no = #{baseAcctNo})
		</if>
		<if test="acctStatus != null and acctStatus !=''">
			AND acct_status =#{acctStatus}
		</if>
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO =#{clientNo}
		</if>
		<if test="acctSeqNo != null and acctSeqNo !=''">
			AND ACCT_SEQ_NO =#{acctSeqNo}
		</if>
	</select>
</mapper>
