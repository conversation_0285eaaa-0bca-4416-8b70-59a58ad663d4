<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER
        where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="java.util.Map">
    delete from RB_APPR_LETTER
    where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </delete>
    <update id="updateByPrimaryKeyExt" parameterType="java.util.Map">
        update RB_APPR_LETTER
        <set>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="clientNo != null">
                CLIENT_NO = #{clientNo},
            </if>
            <if test="openDate != null">
                OPEN_DATE = #{openDate},
            </if>
            <if test="maturityDate != null">
                MATURITY_DATE = #{maturityDate},
            </if>
            <if test="userId != null">
                USER_ID = #{userId},
            </if>
            <if test="apprType != null">
                APPR_TYPE = #{apprType},
            </if>
            <if test="fundSource != null">
                FUND_SOURCE = #{fundSource},
            </if>
            <if test="fundPurpose != null">
                FUND_PURPOSE = #{fundPurpose},
            </if>
            <if test="apprAcctInd != null">
                APPR_ACCT_IND = #{apprAcctInd},
            </if>
            <if test="acctTypeDesc != null">
                ACCT_TYPE_DESC = #{acctTypeDesc},
            </if>
            <if test="incomeScope != null">
                INCOME_SCOPE = #{incomeScope},
            </if>
            <if test="expendScope != null">
                EXPEND_SCOPE = #{expendScope},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="capitalAmt != null">
                CAPITAL_AMT = #{capitalAmt},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">
        insert into RB_APPR_LETTER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="apprLetterNo != null">
                APPR_LETTER_NO,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="openDate != null">
                OPEN_DATE,
            </if>
            <if test="maturityDate != null">
                MATURITY_DATE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="apprType != null">
                APPR_TYPE,
            </if>
            <if test="fundSource != null">
                FUND_SOURCE,
            </if>
            <if test="fundPurpose != null">
                FUND_PURPOSE,
            </if>
            <if test="apprAcctInd != null">
                APPR_ACCT_IND,
            </if>
            <if test="acctTypeDesc != null">
                ACCT_TYPE_DESC,
            </if>
            <if test="incomeScope != null">
                INCOME_SCOPE,
            </if>
            <if test="expendScope != null">
                EXPEND_SCOPE,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="capitalAmt != null">
                CAPITAL_AMT,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="apprLetterNo != null">
                #{apprLetterNo},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="openDate != null">
                #{openDate},
            </if>
            <if test="maturityDate != null">
                #{maturityDate},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="apprType != null">
                #{apprType},
            </if>
            <if test="fundSource != null">
                #{fundSource},
            </if>
            <if test="fundPurpose != null">
                #{fundPurpose},
            </if>
            <if test="apprAcctInd != null">
                #{apprAcctInd},
            </if>
            <if test="acctTypeDesc != null">
                #{acctTypeDesc},
            </if>
            <if test="incomeScope != null">
                #{incomeScope},
            </if>
            <if test="expendScope != null">
                #{expendScope},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="capitalAmt != null">
                #{capitalAmt},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
    <select id="selectByInternalKey" parameterType="java.util.Map" resultType="String">
  select APPR_LETTER_NO
  from RB_APPR_LETTER
  where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
</select>
    <select id="selectApprByNoAndCompany" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER
        where APPR_LETTER_NO = #{apprLetterNo}
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="companyList != null">
            AND COMPANY in
            <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectByApprLetterNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER
        where APPR_LETTER_NO = #{apprLetterNo}
        <if test="clientNo != null and  clientNo != '' ">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectByApprLetterInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER
        <where>
        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="startDate != null">
            AND  <![CDATA[OPEN_DATE>= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            AND  <![CDATA[ MATURITY_DATE <= ]]>#{endDate}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
        ORDER BY OPEN_DATE desc,tran_timestamp DESC
    </select>

    <select id="selectByApprLetterInfo1" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER
        <where>
        <if test="apprLetterNo != null and apprLetterNo !=''">
            AND APPR_LETTER_NO = #{apprLetterNo}
        </if>

        <if test="startDate != null">
            AND  <![CDATA[OPEN_DATE>= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            AND  <![CDATA[ MATURITY_DATE <= ]]>#{endDate}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
        ORDER BY OPEN_DATE desc,tran_timestamp DESC
    </select>

    <select id="queryByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY OPEN_DATE
    </select>
</mapper>
