<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPreCloseChangeReg">

	<update id="updateByUseStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPreCloseChangeReg">
		update RB_PRE_CLOSE_CHANGE_REG
		<set>
			<if test="useStatus != null">
				USE_STATUS = #{useStatus},
			</if>
		</set>
		where INTERNAL_KEY = #{internalKey}
		AND client_no = #{clientNo}
		AND USE_STATUS = 'Y'
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<select id="selectByInternalKeyAndUseStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPreCloseChangeReg"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPreCloseChangeReg">
		SELECT <include refid="Base_Column"/>
		FROM RB_PRE_CLOSE_CHANGE_REG
		WHERE INTERNAL_KEY =  #{internalKey}
		AND CLIENT_NO = #{clientNo}
		AND USE_STATUS = #{useStatus}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
