<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherChangeInfo">

  <select id="getMbVocherChangeInfos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherChangeInfo">
    select <include refid="Base_Column"/>
    from RB_VOUCHER_CHANGE_INFO
    <where>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo}
      </if>
      <if test="startDate != null">
        AND TRAN_DATE = #{startDate}
      </if>
      <if test="endDate != null">
        AND TRAN_DATE &lt;= #{endDate}
      </if>
      <if test="acctSeqNo != null">
        AND  ACCT_SEQ_NO = #{acctSeqNo}
      </if>
      <if test="docType != null">
        AND  DOC_TYPE = #{docType}
      </if>
      <if test="baseAcctNo != null">
        AND (BASE_ACCT_NO = #{baseAcctNo}
        <!--54664 凭证更换查询用新卡号查询 add by liuqzd 2017/12/19-->
        OR NEW_CARD_NO = #{baseAcctNo})
      </if>
      <if test="voucherChangeType != null">
        AND  VOUCHER_CHANGE_TYPE = #{voucherChangeType}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    order BY TRAN_TIMESTAMP DESC
  </select>
  <select id="getMbVocherChangeReturnInfos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherChangeInfo">
    select <include refid="Base_Column"/>
    from RB_VOUCHER_CHANGE_INFO
    <where>
      <if test="docType != null">
        DOC_TYPE = #{docType}
      </if>
      <if test="baseAcctNo != null">
        AND (BASE_ACCT_NO = #{baseAcctNo}
        <!--54664 凭证更换查询用新卡号查询 add by liuqzd 2017/12/19-->
        OR NEW_CARD_NO = #{baseAcctNo})
      </if>
      <if test="voucherChangeType != null">
        AND  VOUCHER_CHANGE_TYPE = #{voucherChangeType}
      </if>
      <if test="voucherNo != null">
        AND  VOUCHER_NO = #{voucherNo}
      </if>
      <if test="newVoucherNo != null">
        AND  NEW_VOUCHER_NO = #{newVoucherNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    order BY TRAN_TIMESTAMP DESC
  </select>

  <select id="getChangeInfoByNewDoc" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherChangeInfo">
    select <include refid="Base_Column"/>
    from RB_VOUCHER_CHANGE_INFO
    <where>
      <if test="newDocType != null and newDocType != ''">
        NEW_DOC_TYPE = #{newDocType}
      </if>
      <if test="baseAcctNo != null and baseAcctNo != ''">
        AND (BASE_ACCT_NO = #{baseAcctNo}
        OR NEW_CARD_NO = #{baseAcctNo})
      </if>
      <if test="voucherChangeType != null">
        AND  VOUCHER_CHANGE_TYPE = #{voucherChangeType}
      </if>
      <if test="newVoucherNo != null">
        AND  NEW_VOUCHER_NO = #{newVoucherNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    order BY TRAN_TIMESTAMP DESC
  </select>

</mapper>
