<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbNotranList">

	<update id="updateByBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbNotranList" >
		UPDATE RB_NOTRAN_LIST
		<set>
			<if test="s.asynDate != null and s.asynDate != '' ">
				ASYN_DATE = #{s.asynDate},
			</if>
			<if test="s.remark1 != null and s.remark1 != '' ">
				REMARK1 = #{s.remark1},
			</if>
			<if test="s.blackNo != null and s.blackNo != '' ">
				BLACK_NO = #{s.blackNo},
			</if>
			<if test="s.clientName != null and s.clientName != '' ">
				CLIENT_NAME = #{s.clientName},
			</if>
			<if test="s.field3 != null and s.field3 != '' ">
				FIELD3 = #{s.field3},
			</if>
			<if test="s.asynId != null and s.asynId != '' ">
				ASYN_ID = #{s.asynId},
			</if>
			<if test="s.retMsg != null and s.retMsg != '' ">
				RET_MSG = #{s.retMsg},
			</if>
			<if test="s.batchStatus != null and s.batchStatus != '' ">
				BATCH_STATUS = #{s.batchStatus},
			</if>
			<if test="s.batchNo != null and s.batchNo != '' ">
				BATCH_NO = #{s.batchNo},
			</if>
			<if test="s.blackDesc != null and s.blackDesc != '' ">
				BLACK_DESC = #{s.blackDesc},
			</if>
			<if test="s.examineTeller != null and s.examineTeller != '' ">
				EXAMINE_TELLER = #{s.examineTeller},
			</if>
			<if test="s.remark2 != null and s.remark2 != '' ">
				REMARK2 = #{s.remark2},
			</if>
			<if test="s.errorDesc != null and s.errorDesc != '' ">
				ERROR_DESC = #{s.errorDesc},
			</if>
			<if test="s.tranTimestamp != null ">
				TRAN_TIMESTAMP = #{s.tranTimestamp},
			</if>
			<if test="s.expireDate != null and s.expireDate != '' ">
				EXPIRE_DATE = #{s.expireDate},
			</if>
			<if test="s.isOurBankFlag != null and s.isOurBankFlag != '' ">
				IS_OUR_BANK_FLAG = #{s.isOurBankFlag},
			</if>
			<if test="s.listSource != null and s.listSource != '' ">
				LIST_SOURCE = #{s.listSource},
			</if>
			<if test="s.checkFlag != null and s.checkFlag != '' ">
				CHECK_FLAG = #{s.checkFlag},
			</if>
			<if test="s.field1 != null and s.field1 != '' ">
				FIELD1 = #{s.field1},
			</if>
			<if test="s.jobRunId != null and s.jobRunId != '' ">
				JOB_RUN_ID = #{s.jobRunId},
			</if>
			<if test="s.baseAcctNo != null and s.baseAcctNo != '' ">
				BASE_ACCT_NO = #{s.baseAcctNo},
			</if>
			<if test="s.acctStatus != null and s.acctStatus != '' ">
				ACCT_STATUS = #{s.acctStatus},
			</if>
			<if test="s.documentType != null and s.documentType != '' ">
				DOCUMENT_TYPE = #{s.documentType},
			</if>
			<if test="s.effectDate != null and s.effectDate != '' ">
				EFFECT_DATE = #{s.effectDate},
			</if>
			<if test="s.inputBranch != null and s.inputBranch != '' ">
				INPUT_BRANCH = #{s.inputBranch},
			</if>
			<if test="s.examineFlag != null and s.examineFlag != '' ">
				EXAMINE_FLAG = #{s.examineFlag},
			</if>
			<if test="s.operUserId != null and s.operUserId != '' ">
				OPER_USER_ID = #{s.operUserId},
			</if>
			<if test="s.field2 != null and s.field2 != '' ">
				FIELD2 = #{s.field2},
			</if>
			<if test="s.errorCode != null and s.errorCode != '' ">
				ERROR_CODE = #{s.errorCode},
			</if>
			<if test="s.acctBranch != null and s.acctBranch != '' ">
				ACCT_BRANCH = #{s.acctBranch},
			</if>
			<if test="s.blackCheckTime != null and s.blackCheckTime != '' ">
				BLACK_CHECK_TIME = #{s.blackCheckTime},
			</if>
			<if test="s.listOperateType != null and s.listOperateType != '' ">
				LIST_OPERATE_TYPE = #{s.listOperateType},
			</if>
			<if test="s.pushFlag != null and s.pushFlag != '' ">
				PUSH_FLAG = #{s.pushFlag},
			</if>
			<if test="s.remark3 != null and s.remark3 != '' ">
				REMARK3 = #{s.remark3},
			</if>
			<if test="s.clientNo != null and s.clientNo != '' ">
				CLIENT_NO = #{s.clientNo},
			</if>
			<if test="s.documentId != null and s.documentId != '' ">
				DOCUMENT_ID = #{s.documentId},
			</if>
			<if test="s.uncounterDesc != null and s.uncounterDesc != '' ">
				UNCOUNTER_DESC = #{s.uncounterDesc},
			</if>
		</set>
		where BATCH_NO = #{batchNo}
		<if test="baseAcctNo != null">
			AND BASE_ACCT_NO = #{baseAcctNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>


</mapper>
