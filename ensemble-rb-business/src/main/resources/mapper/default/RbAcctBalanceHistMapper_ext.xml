<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">
    <sql id="Base_Where_By_Date">
        <trim suffixOverrides="AND">
            <if test="internalKey != null ">
                internal_key = #{internalKey} AND
            </if>
            <if test="startDate != null  ">
                tran_date <![CDATA[ >= ]]> #{startDate} AND
            </if>
            <if test="endDate != null  ">
                tran_date <![CDATA[ < ]]> #{endDate} AND
            </if>
            <if test="totalAmount != null ">
                total_amount = #{totalAmount} AND
            </if>
            <if test="totalAmountPrev != null ">
                total_amount_prev = #{totalAmountPrev} AND
            </if>
            <if test="pldAmount != null ">
                pld_amount = #{pldAmount} AND
            </if>
            <if test="odAmount != null ">
                od_amount = #{odAmount} AND
            </if>
            <if test="oddAmount != null ">
                odd_amount = #{oddAmount} AND
            </if>
            <if test="dosAmount != null ">
                dos_amount = #{dosAmount} AND
            </if>
            <if test="lastChangeUserId != null and  lastChangeUserId != '' ">
                last_change_user_id = #{lastChangeUserId} AND
            </if>
            <if test="lastChangeDate != null ">
                last_change_date = #{lastChangeDate} AND
            </if>
            <if test="dacValue != null and  dacValue != '' ">
                dac_value = #{dacValue} AND
            </if>
            <if test="company != null and  company != '' ">
                company = #{company} AND
            </if>
            <if test="tranTimestamp != null ">
                tran_timestamp = #{tranTimestamp} AND
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                client_no = #{clientNo} AND
            </if>
            <if test="finRegAmount != null and  finRegAmount != '' ">
                finreg_amount = #{clientNo} AND
            </if>
        </trim>
    </sql>

    <sql id="getAllBalanceHist">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <include refid="Base_Where_By_Date"/>
        </where>
    </sql>

    <select id="getRbAcctBalanceHistByClientNo"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist"
            parameterType="java.util.Map" useCache="false">
        select
        <include refid="Base_Column"/>
        from
        <include refid="Table_Name"/>
        where CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        <!--多法人改造 by LIYUANV-->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

        <if test="endDate != null">
            <![CDATA[
			AND date_format(TRAN_DATE,'%Y%m%d') <= #{endDate}
                ]]>
        </if>
        order by tran_date ASC
    </select>
</mapper>
