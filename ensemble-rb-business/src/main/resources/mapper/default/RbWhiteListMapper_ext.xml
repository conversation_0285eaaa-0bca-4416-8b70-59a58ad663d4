<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbWhiteList">
	<select id="selectByAcctSourceType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbWhiteList">
		select <include refid="Base_Column"/>
		 from RB_WHITE_LIST
		where  EFFECT_FLAG='Y'
		<if test="sourceType != null and  sourceType != '' ">
			AND	SOURCE_TYPE = #{sourceType}
		</if>
		<if test="actualAcctNo != null and  actualAcctNo != '' ">
			AND ACTUAL_ACCT_NO = #{actualAcctNo}
		</if>
		<if test="clientNo != null and  clientNo != '' ">
			AND	CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
