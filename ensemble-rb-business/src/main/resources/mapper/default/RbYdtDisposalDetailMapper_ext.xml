<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbYdtDisposalDetail">


    <delete id="deleteBySeqNoAndClientNo"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbYdtDisposalDetail">
        DELETE FROM
        <include refid="Table_Name"/>
        <where>
            SEQ_NO = #{seqNo} and CLIENT_NO=#{clientNo}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </delete>

    <select id="getSettleSumByBatchNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(DISPOSAL_AMT) AS SUM_AMT
        FROM <include refid="Table_Name"/>
        WHERE BATCH_NO = #{batchNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getTranSumByBatchNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(ACTUAL_TRAN_AMT) AS SUM_AMT
        FROM <include refid="Table_Name"/>
        WHERE BATCH_NO = #{batchNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getTranSum" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(ACTUAL_TRAN_AMT) AS SUM_AMT
        FROM <include refid="Table_Name"/>
        where 1=1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getTranSumByTranDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(ACTUAL_TRAN_AMT) AS SUM_AMT, TRAN_DATE
        FROM <include refid="Table_Name"/>
        where 1=1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        GROUP BY TRAN_DATE
    </select>

    <select id="getSettleSumByTranDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(DISPOSAL_AMT) AS SUM_AMT
        FROM <include refid="Table_Name"/>
        WHERE TRAN_DATE = #{tranDate}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
