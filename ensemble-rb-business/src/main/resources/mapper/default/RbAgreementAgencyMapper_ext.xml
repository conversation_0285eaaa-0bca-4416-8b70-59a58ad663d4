<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAgency">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAgency" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAgency">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_AGENCY
    where AGREEMENT_ID = #{agreementId}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAgency">
    delete from RB_AGREEMENT_AGENCY
    where AGREEMENT_ID = #{agreementId}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAgency">
    update RB_AGREEMENT_AGENCY
    <set>
      <if test="agencyType != null">
        AGENCY_TYPE = #{agencyType},
      </if>
      <if test="runTime != null">
        RUN_TIME = #{runTime}
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where AGREEMENT_ID = #{agreementId}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAgency">
    insert into RB_AGREEMENT_AGENCY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="agreementId != null">
        AGREEMENT_ID,
      </if>
      <if test="agencyType != null">
        AGENCY_TYPE,
      </if>
      <if test="runTime != null">
        RUN_TIME,
      </if>
      <if test="tranTimestamp != null">
         TRAN_TIMESTAMP,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="agreementId != null">
        #{agreementId},
      </if>
      <if test="agencyType != null">
        #{agencyType},
      </if>
      <if test="runTime != null">
        #{runTime},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>

  <select id="getAgencyAgrtByAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.agr.MbAgreementAgencyModel">
    select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY, ma.BASE_ACCT_NO, ma.PROD_TYPE,
        ma.CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.BRANCH, ma.AGREEMENT_OPEN_DATE, ma.USER_ID, ma.LAST_CHANGE_USER_ID,
        ma.LAST_CHANGE_DATE, ma.COMPANY, maa.AGENCY_TYPE, maa.RUN_TIME
    from RB_AGREEMENT ma, RB_AGREEMENT_AGENCY maa
    where ma.AGREEMENT_ID = #{agreementId}
    and ma.AGREEMENT_ID = maa.AGREEMENT_ID
    and ma.AGREEMENT_TYPE = 'AGC'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND ma.COMPANY = #{company}
    </if>
  </select>
</mapper>
