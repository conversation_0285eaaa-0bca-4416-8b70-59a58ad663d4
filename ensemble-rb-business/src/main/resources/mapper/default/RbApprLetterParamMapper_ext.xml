<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterParam">
  <!-- Created by helong<PERSON> on 2017/08/22 14:12:21. -->
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterParam">
    insert into RB_APPR_LETTER_PARAM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paraKey != null">
        PARA_KEY,
      </if>
      <if test="paraValue != null">
        PARA_VALUE,
      </if>
      <if test="paraDesc != null">
        PARA_DESC,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null">
        #{company},
      </if>
      <if test="paraKey != null">
        #{paraKey},
      </if>
      <if test="paraValue != null">
        #{paraValue},
      </if>
      <if test="paraDesc != null">
        #{paraDesc},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <select id="selectParaInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterParam" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterParam">
    select *
    from RB_APPR_LETTER_PARAM
    where PARA_KEY = #{paraKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>
</mapper>
