<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RcListNotCheckRange">
	<sql id="Base_Select_Map">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
			<include refid="Base_Where_Map" />
		</where>
	</sql>
	<sql id="Base_Where_Map">
		<trim suffixOverrides="AND">
			<if test="remark != null and  remark != '' ">
				REMARK = #{remark}  AND
			</if>
			<if test="seqNo != null ">
				SEQ_NO = #{seqNo}  AND
			</if>
			<if test="listType != null ">
				LIST_TYPE IN
				<foreach item="item" index="index" collection="listType" open="(" separator="," close=")">
					#{item}
				</foreach>
				AND
			</if>
			<if test="messageType != null ">
				MESSAGE_TYPE IN
				<foreach item="item" index="index" collection="messageType" open="(" separator="," close=")">
					#{item}
				</foreach>
				AND
			</if>
			<if test="messageCode != null ">
				MESSAGE_CODE IN
				<foreach item="item" index="index" collection="messageCode" open="(" separator="," close=")">
					#{item}
				</foreach>
				AND
			</if>
			<if test="serviceCode != null ">
				SERVICE_CODE IN
				<foreach item="item" index="index" collection="messageCode" open="(" separator="," close=")">
					#{item}
				</foreach>
				AND
			</if>
			<if test="programId != null ">
				PROGRAM_ID IN
				<foreach item="item" index="index" collection="programId" open="(" separator="," close=")">
					#{item}
				</foreach>
				AND
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				COMPANY = #{company} AND
			</if>
		</trim>
	</sql>

	<select id="getSelectListByMap"  resultMap="Base_Result_Map">
		<include refid="Base_Select_Map" />
	</select>

	<select id="getListByCondition" resultMap="Base_Result_Map"
			parameterType="java.util.Map">
		select
		<include refid="Base_Column"/>
		FROM
		<include refid="Table_Name" />
		<where>
			<if test="listType != null and listType != '' ">
				AND LIST_TYPE in (#{listType}, "ALL")
			</if>
			<if test="messageType != null and messageType != '' ">
				AND MESSAGE_TYPE in (#{messageType}, "ALL")
			</if>
			<if test="programId != null and programId != '' ">
				AND PROGRAM_ID in (#{programId}, "ALL")
			</if>
			<if test="messageCode != null and messageCode != '' ">
				AND MESSAGE_CODE in (#{messageCode}, "ALL")
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>

	</select>

</mapper>
