<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">

    <select id="selectAmendByAcct" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        WHERE amend_key = #{amendKey,jdbcType=VARCHAR}
        <if test="amendDate != null ">
            AND AMEND_DATE = #{amendDate}
        </if>
        <if test="amendType != null and amendType.length() > 0">
            AND AMEND_TYPE = #{amendType,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY amend_seq_no DESC
    </select>
    <select id="getAcctAmendInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        WHERE base_acct_no = #{baseAcctNo,jdbcType=VARCHAR}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY amend_seq_no ASC
    </select>
    <select id="getAmendChangeInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        M.BASE_ACCT_NO,M.AMEND_TYPE,M.AMEND_DATE,M.CLIENT_NO,M.AFTER_VAL,M.BEFORE_VAL,M.AFTER_VAL_1,M.BEFORE_VAL_1,M.USER_ID,M.PROD_TYPE,M.TRAN_BRANCH,
        M.ACCT_SEQ_NO,M.APPR_USER_ID,M.ACCT_CCY,A.CLIENT_SHORT as CLIENT_NAME,C.CONTRACT_NO
        FROM RB_AMEND M,MB_AGREEMENT A,MB_AGREEMENT_LOAN C
        WHERE M.BASE_ACCT_NO = A.BASE_ACCT_NO AND A.AGREEMENT_ID = C.AGREEMENT_ID
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND  M.COMPANY = #{company}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND M.base_acct_no = #{baseAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="acctSeqNo != null and acctSeqNo.length() > 0">
            AND M.acct_seq_no = #{acctSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="amendType != null and amendType.length() > 0">
            AND M.amend_type = #{amendType,jdbcType=VARCHAR}
        </if>
        <if test="amendDateStart != null  and amendDateStart.length() > 0 ">
            AND M.AMEND_DATE >= #{amendDateStart,jdbcType=VARCHAR}
        </if>
        <if test=" amendDateEnd != null and amendDateEnd.length() > 0 ">
            AND #{amendDateEnd,jdbcType=VARCHAR} >= M.AMEND_DATE
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            AND M.client_no = #{clientNo,jdbcType=VARCHAR}
        </if>
        <if test="contractNo != null and contractNo.length() > 0">
            AND C.contract_no = #{contractNo,jdbcType=VARCHAR}
        </if>
        <if test="clientName != null and clientName.length() > 0">
            AND A.CLIENT_SHORT = #{clientName,jdbcType=VARCHAR}
        </if>
        ORDER BY amend_seq_no ASC
    </select>

    <select id="getAcctAmendInfoByDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        WHERE BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        <if test="amendDate != null ">
            AND AMEND_DATE = #{amendDate}
        </if>
        <if test="amendType != null and amendType.length() > 0">
            AND AMEND_TYPE = #{amendType,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AMEND_SEQ_NO ASC
    </select>

    <select id="getClientAmendInfoByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        WHERE CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AMEND_SEQ_NO ASC
    </select>

    <select id="getClientAmendInfoByDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        WHERE CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="amendDate != null ">
            AND AMEND_DATE = #{amendDate}
        </if>
        <if test="amendType != null and amendType.length() > 0">
            AND AMEND_TYPE = #{amendType,jdbcType=VARCHAR}
        </if>
        ORDER BY AMEND_SEQ_NO ASC
    </select>

    <select id="getAmendInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        <where>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="prodType != null and prodType.length()>0">
            AND prod_type = #{prodType,jdbcType=VARCHAR}
        </if>
        <if test="acctCcy != null and acctCcy.length()>0">
            AND ACCT_CCY = #{acctCcy,jdbcType=VARCHAR}
        </if>
        <if test="acctSeqNo != null and acctSeqNo.length()>0">
            AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="amendDate != null ">
            AND AMEND_DATE = #{amendDate}
        </if>
        <if test="amendType != null and amendType.length() > 0">
            AND AMEND_TYPE = #{amendType,jdbcType=VARCHAR}
        </if>
        <if test="amendBusiSort != null and amendBusiSort.length() > 0">
            AND AMEND_BUSI_SORT = #{amendBusiSort,jdbcType=VARCHAR}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company,jdbcType=VARCHAR}
        </if>
        </where>
        ORDER BY AMEND_SEQ_NO ASC
    </select>
    <select id="queryExtendTimes" parameterType="java.util.Map"
            resultType="java.math.BigDecimal">
                  SELECT
                        COUNT(*)
                    FROM
                        mb_amend
                    WHERE
                    amend_Key =  #{amendKey,jdbcType=VARCHAR}
                    <!-- 多法人改造 by LIYUANV -->
                    <if test="company != null and company != '' ">
                        AND COMPANY = #{company}
                    </if>
                        AND amend_type in ('MATS','MATE')
                        AND APPR_FLAG = 'A'
  </select>

    <select id="selectForAmend" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        select
        <include refid="Base_Column"/>
        from RB_AMEND
        where BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        <if test="acctSeqNo != null and acctSeqNo.length() > 0">
            AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="amendType != null and amendType.length() > 0">
            AND AMEND_TYPE = #{amendType,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AMEND_DATE DESC
    </select>
    <select id="getCifAmendInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        select
        <include refid="Base_Column"/>
        from RB_AMEND
        <where>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        <if test="amendType != null and amendType.length() > 0">
            AND AMEND_TYPE = #{amendType,jdbcType=VARCHAR}
        </if>
        <if test="amendDate != null ">
            AND AMEND_DATE = #{amendDate}
        </if>
        </where>
        ORDER BY AMEND_DATE DESC
    </select>

    <select id="getAmendByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        select
        <include refid="Base_Column"/>
        from RB_AMEND
        <where>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        <if test="reference != null ">
            AND REFERENCE = #{reference}
        </if>
        </where>
    </select>
    <select id="getAmendAttrInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AMEND
        <where>
            <!-- Query the history of changes in corporate account attributes by LIYUANV -->
            <if test="baseAcctNo != null and baseAcctNo.length() > 0">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="acctSeqNo != null and acctSeqNo.length()>0">
                AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null">
                AND
                <![CDATA[  #{startDate}<= AMEND_DATE ]]>
            </if>
            <if test="endDate != null ">
                AND
                <![CDATA[ AMEND_DATE <= #{endDate}]]>
            </if>
        </where>
        ORDER BY AMEND_SEQ_NO ASC
    </select>
</mapper>
