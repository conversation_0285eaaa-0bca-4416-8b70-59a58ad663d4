<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
  <!--多法人改造 by LIYUANV-->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/>
    from CD_CARD_CHG
    where OLD_CARD_NO = #{oldCardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCardChgInfoByLostNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/>
    from CD_CARD_CHG
    where LOST_NO = #{lostNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    delete from CD_CARD_CHG
    where OLD_CARD_NO = #{oldCardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <!--多法人改造 by LIYUANV-->
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    update CD_CARD_CHG
    <set>
      <if test="newCardNo != null">
        NEW_CARD_NO = #{newCardNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="changeStatus != null">
        CHANGE_STATUS = #{changeStatus},
      </if>
      <if test="applyDate != null">
        APPLY_DATE = #{applyDate},
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID = #{applyUserId},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="sameNoFlag != null">
        SAME_NO_FLAG = #{sameNoFlag},
      </if>
      <if test="cardChangeReason != null">
        CARD_CHANGE_REASON = #{cardChangeReason},
      </if>
      <if test="contactTel != null">
        CONTACT_TEL = #{contactTel},
      </if>
      <if test="lostNo != null">
        LOST_NO = #{lostNo},
      </if>
      <if test="promissoryDate != null">
        PROMISSORY_DATE = #{promissoryDate},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="gainType != null">
        GAIN_TYPE = #{gainType},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="urgentFlag != null">
        URGENT_FLAG = #{urgentFlag},
      </if>
      <if test="postalCode != null">
        POSTAL_CODE = #{postalCode},
      </if>
      <if test="address != null">
        ADDRESS = #{address}
      </if>
    </set>
    where OLD_CARD_NO = #{oldCardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    insert into CD_CARD_CHG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="oldCardNo != null">
        OLD_CARD_NO,
      </if>
      <if test="newCardNo != null">
        NEW_CARD_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="applyDate != null">
        APPLY_DATE,
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID,
      </if>
      <if test="changeStatus != null">
        CHANGE_STATUS,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="sameNoFlag != null">
        SAME_NO_FLAG,
      </if>
      <if test="cardChangeReason != null">
        CARD_CHANGE_REASON,
      </if>
      <if test="contactTel != null">
        CONTACT_TEL,
      </if>
      <if test="lostNo != null">
        LOST_NO,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="promissoryDate != null">
        PROMISSORY_DATE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="gainType != null">
        GAIN_TYPE,
      </if>
      <if test="urgentFlag != null">
        URGENT_FLAG,
      </if>
      <if test="postalCode != null">
        POSTAL_CODE,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="oldCardNo != null">
        #{oldCardNo},
      </if>
      <if test="newCardNo != null">
        #{newCardNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="applyDate != null">
        #{applyDate},
      </if>
      <if test="applyUserId != null">
        #{applyUserId},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="sameNoFlag != null">
        #{sameNoFlag},
      </if>
      <if test="cardChangeReason != null">
        #{cardChangeReason},
      </if>
      <if test="contactTel != null">
        #{contactTel},
      </if>
      <if test="lostNo != null">
        #{lostNo},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="promissoryDate != null">
        #{promissoryDate},
      </if>
      <if test="gainType != null">
        #{gainType},
      </if>
      <if test="urgentFlag != null">
        #{urgentFlag},
      </if>
      <if test="postalCode != null">
        #{postalCode},
      </if>
      <if test="address != null">
        #{address},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <!--多法人改造 by LIYUANV-->
  <select id="selectCardChgInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/>
    from CD_CARD_CHG
    <where>
      <trim suffixOverrides="AND">
        <if test="oldCardNo != null and  oldCardNo != '' ">
          OLD_CARD_NO = #{oldCardNo}  AND
        </if>
        <if test="clientNo != null and  clientNo != '' ">
          CLIENT_NO = #{clientNo}  AND
        </if>
        <if test="newCardNo != null and  newCardNo != '' ">
          NEW_CARD_NO = #{newCardNo}  AND
        </if>
        <if test="company != null and company != '' ">
         COMPANY = #{company} AND
        </if>
      </trim>
    </where>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="selectCardChgInfoS" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/>
    from CD_CARD_CHG
    <where>
      <trim suffixOverrides="AND">
        <if test="oldCardNo != null and  oldCardNo != '' ">
          OLD_CARD_NO = #{oldCardNo}  AND
        </if>
        <if test="clientNo != null and  clientNo != '' ">
          CLIENT_NO = #{clientNo}  AND
        </if>
        <if test="newCardNo != null and  newCardNo != '' ">
          NEW_CARD_NO = #{newCardNo}  AND
        </if>
        <if test="oldCardNo != null and  oldCardNo != '' ">
          CHANGE_STATUS = '1'  AND
        </if>
        <if test="company != null and company != '' ">
          COMPANY = #{company} AND
        </if>
      </trim>
    </where>
  </select>
  <select id="getLastCardChgInfoByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/>
    from CD_CARD_CHG
    where OLD_CARD_NO = #{oldCardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="clientNo != null and clientNo.length() > 0">
      AND CLIENT_NO = #{clientNo}
    </if>
    ORDER BY APPLY_DATE desc
  </select>
  <select id="getCardChgInfoByCardNo2" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/>
    from CD_CARD_CHG
    where OLD_CARD_NO = #{oldCardNo}
    <if test="applyDate != null">
      AND APPLY_DATE = #{applyDate}
    </if>
  </select>
  <select id="selectCardChgInfoByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/> from (
      select <include refid="Base_Column"/>
      from CD_CARD_CHG START WITH OLD_CARD_NO = #{oldCardNo} AND SAME_NO_FLAG = '1' CONNECT BY PRIOR OLD_CARD_NO = NEW_CARD_NO AND SAME_NO_FLAG = '1'
    ) order by TRAN_TIMESTAMP DESC
  </select>

  <select id="getNewCardNoByOldCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardChg">
    select <include refid="Base_Column"/>
    from CD_CARD_CHG
    where OLD_CARD_NO = #{oldCardNo}
    AND SAME_NO_FLAG = '1'
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
