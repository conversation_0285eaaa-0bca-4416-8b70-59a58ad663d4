<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">

    <select id="selectAgrtAllInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY,
        ma.BASE_ACCT_NO, ma.PROD_TYPE,
        ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.<PERSON>, ma.AGREEMENT_OPEN_DATE,
        ma.USER_ID, ma.LAST_CHANGE_USER_ID,
        ma.LAST_CHANGE_DATE, ma.COMPANY, mao.LOAN_PROD_TYPE, mao.LOAN_INTERNAL_KEY,
        mao.LOAN_BASE_ACCT_NO, mao.LOAN_ACCT_CCY, mao.LOAN_SEQ_NO, mao.OD_METHOD, mao.OD_CCY, mao.OD_AMT, mao.OD_TERM,
        mao.OD_TERM_TYPE, mao.OD_GRACE_PERIOD
        from RB_AGREEMENT ma, RB_AGREEMENT_OVERDRAFT mao
        where ma.AGREEMENT_ID = mao.AGREEMENT_ID
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
        <if test="agreementId != null">
            and ma.AGREEMENT_ID = #{agreementId}
        </if>
        <if test="agreementType != null">
            and ma.AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="startDate != null">
            and ma.START_DATE = #{startDate}
        </if>
        <if test="endDate != null">
            and ma.END_DATE = #{endDate}
        </if>
        <if test="agreementKeyType != null">
            and ma.AGREEMENT_KEY_TYPE = #{agreementKeyType}
        </if>
        <if test="agreementKey != null">
            and ma.AGREEMENT_KEY = #{agreementKey}
        </if>
        <if test="baseAcctNo != null">
            and ma.BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="prodType != null">
            and ma.PROD_TYPE = #{prodType}
        </if>
        <if test="acctCcy != null">
            and ma.ACCT_CCY = #{acctCcy}
        </if>
        <if test="acctSeqNo != null">
            and ma.ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="clientNo != null">
            and ma.CLIENT_NO = #{clientNo}
        </if>
        <if test="clientShort != null">
            and ma.CLIENT_SHORT = #{clientShort}
        </if>
        <if test="agreementStatus != null">
            and ma.AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <if test="branch != null">
            and ma.BRANCH = #{branch}
        </if>
        <if test="agreementOpenDate != null">
            and ma.AGREEMENT_OPEN_DATE = #{agreementOpenDate}
        </if>
        <if test="userId != null">
            and ma.USER_ID = #{userId}
        </if>
        <if test="lastChangeUserId != null">
            and ma.LAST_CHANGE_USER_ID = #{lastChangeUserId}
        </if>
        <if test="lastChangeDate != null">
            and ma.LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        <if test="company != null">
            and ma.COMPANY = #{company}
        </if>
        <if test="loanProdType != null">
            and mao.LOAN_PROD_TYPE = #{loanProdType}
        </if>
        <if test="loanInternalKey != null">
            and mao.LOAN_INTERNAL_KEY = #{loanInternalKey}
        </if>
        <if test="loanBaseAcctNo != null">
            and mao.LOAN_BASE_ACCT_NO = #{loanBaseAcctNo}
        </if>
        <if test="loanAcctCcy != null">
            and mao.LOAN_ACCT_CCY = #{loanAcctCcy}
        </if>
        <if test="loanSeqNo != null">
            and mao.LOAN_SEQ_NO = #{loanSeqNo}
        </if>
        <if test="odMethod != null">
            and mao.OD_METHOD = #{odMethod}
        </if>
        <if test="odCcy != null">
            and mao.OD_CCY = #{odCcy}
        </if>
        <if test="odAmt != null">
            and mao.OD_AMT = #{odAmt}
        </if>
        <if test="odTerm != null">
            and mao.OD_TERM = #{odTerm}
        </if>
        <if test="odTermType != null">
            and mao.OD_TERM_TYPE = #{odTermType}
        </if>
        <if test="odGracePeriod != null">
            and mao.OD_GRACE_PERIOD = #{odGracePeriod}
        </if>
    </select>

    <select id="selectAgrtAllInfoByRouter" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY,
        ma.BASE_ACCT_NO, ma.PROD_TYPE,
        ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.BRANCH, ma.AGREEMENT_OPEN_DATE,
        ma.USER_ID, ma.LAST_CHANGE_USER_ID,
        ma.LAST_CHANGE_DATE, ma.COMPANY,mao.LOAN_PROD_TYPE, mao.LOAN_INTERNAL_KEY,
        mao.LOAN_BASE_ACCT_NO, mao.LOAN_ACCT_CCY, mao.LOAN_SEQ_NO, mao.OD_METHOD, mao.OD_CCY, mao.OD_AMT, mao.OD_TERM,
        mao.OD_TERM_TYPE, mao.OD_GRACE_PERIOD
        from RB_AGREEMENT ma, RB_AGREEMENT_OVERDRAFT mao
        where ma.AGREEMENT_ID = mao.AGREEMENT_ID
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
        <if test="agreementId != null">
            and ma.AGREEMENT_ID = #{agreementId}
        </if>
        <if test="agreementType != null">
            and ma.AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="startDate != null">
            and ma.START_DATE = #{startDate}
        </if>
        <if test="endDate != null">
            and ma.END_DATE = #{endDate}
        </if>
        <if test="agreementKeyType != null">
            and ma.AGREEMENT_KEY_TYPE = #{agreementKeyType}
        </if>
        <if test="agreementKey != null">
            and ma.AGREEMENT_KEY = #{agreementKey}
        </if>
        <if test="baseAcctNo != null">
            and ma.BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="prodType != null">
            and ma.PROD_TYPE = #{prodType}
        </if>
        <if test="acctCcy != null">
            and ma.ACCT_CCY = #{acctCcy}
        </if>
        <if test="acctSeqNo != null">
            and ma.ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="clientNo != null">
            and ma.CLIENT_NO = #{clientNo}
        </if>
        <if test="clientShort != null">
            and ma.CLIENT_SHORT = #{clientShort}
        </if>
        <if test="agreementStatus != null">
            and ma.AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <if test="branch != null">
            and ma.BRANCH = #{branch}
        </if>
        <if test="agreementOpenDate != null">
            and ma.AGREEMENT_OPEN_DATE = #{agreementOpenDate}
        </if>
        <if test="userId != null">
            and ma.USER_ID = #{userId}
        </if>
        <if test="lastChangeUserId != null">
            and ma.LAST_CHANGE_USER_ID = #{lastChangeUserId}
        </if>
        <if test="lastChangeDate != null">
            and ma.LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        <if test="company != null">
            and ma.COMPANY = #{company}
        </if>
        <if test="loanProdType != null">
            and mao.LOAN_PROD_TYPE = #{loanProdType}
        </if>
        <if test="loanInternalKey != null">
            and mao.LOAN_INTERNAL_KEY = #{loanInternalKey}
        </if>
        <if test="loanBaseAcctNo != null">
            and mao.LOAN_BASE_ACCT_NO = #{loanBaseAcctNo}
        </if>
        <if test="loanAcctCcy != null">
            and mao.LOAN_ACCT_CCY = #{loanAcctCcy}
        </if>
        <if test="loanSeqNo != null">
            and mao.LOAN_SEQ_NO = #{loanSeqNo}
        </if>
        <if test="odMethod != null">
            and mao.OD_METHOD = #{odMethod}
        </if>
        <if test="odCcy != null">
            and mao.OD_CCY = #{odCcy}
        </if>
        <if test="odAmt != null">
            and mao.OD_AMT = #{odAmt}
        </if>
        <if test="odTerm != null">
            and mao.OD_TERM = #{odTerm}
        </if>
        <if test="odTermType != null">
            and mao.OD_TERM_TYPE = #{odTermType}
        </if>
        <if test="odGracePeriod != null">
            and mao.OD_GRACE_PERIOD = #{odGracePeriod}
        </if>
    </select>

    <select id="selectAgrtByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY,
        ma.BASE_ACCT_NO, ma.PROD_TYPE,
        ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.BRANCH, ma.AGREEMENT_OPEN_DATE,
        ma.USER_ID, ma.LAST_CHANGE_USER_ID,
        ma.LAST_CHANGE_DATE, ma.COMPANY, mao.LOAN_PROD_TYPE, mao.LOAN_INTERNAL_KEY,
        mao.LOAN_BASE_ACCT_NO, mao.LOAN_ACCT_CCY, mao.LOAN_SEQ_NO, mao.OD_METHOD, mao.OD_CCY, mao.OD_AMT, mao.OD_TERM,
        mao.OD_TERM_TYPE, mao.OD_GRACE_PERIOD
        from RB_AGREEMENT ma, RB_AGREEMENT_OVERDRAFT mao
        where ma.AGREEMENT_KEY_TYPE = 'IK'
        and ma.AGREEMENT_KEY = #{internalKey}
        and mao.AGREEMENT_ID = ma.AGREEMENT_ID
        <if test="agreementStatus != null">
            and ma.AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>

    <select id="selectOverdraftByLoanInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY,
        ma.BASE_ACCT_NO, ma.PROD_TYPE,
        ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.BRANCH, ma.AGREEMENT_OPEN_DATE,
        ma.USER_ID, ma.LAST_CHANGE_USER_ID,
        ma.LAST_CHANGE_DATE, ma.COMPANY,mao.LOAN_PROD_TYPE, mao.LOAN_INTERNAL_KEY,
        mao.LOAN_BASE_ACCT_NO, mao.LOAN_ACCT_CCY, mao.LOAN_SEQ_NO, mao.OD_METHOD, mao.OD_CCY, mao.OD_AMT, mao.OD_TERM,
        mao.OD_TERM_TYPE, mao.OD_GRACE_PERIOD
        from RB_AGREEMENT ma, RB_AGREEMENT_OVERDRAFT mao
        where ma.AGREEMENT_KEY_TYPE = 'IK'
        and mao.LOAN_INTERNAL_KEY = #{loanInternalKey}
        and mao.AGREEMENT_ID = ma.AGREEMENT_ID
        <if test="agreementStatus != null">
            and ma.AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>

    <select id="selectAgrtByLoanInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where LOAN_INTERNAL_KEY = #{loanInternalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getAgreeOverdraftByAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY,
        ma.BASE_ACCT_NO, ma.PROD_TYPE,
        ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.BRANCH, ma.AGREEMENT_OPEN_DATE,
        ma.USER_ID, ma.LAST_CHANGE_USER_ID,
        ma.LAST_CHANGE_DATE, ma.COMPANY, mao.LOAN_PROD_TYPE, mao.LOAN_INTERNAL_KEY,
        mao.LOAN_BASE_ACCT_NO, mao.LOAN_ACCT_CCY, mao.LOAN_SEQ_NO, mao.OD_METHOD, mao.OD_CCY, mao.OD_AMT, mao.OD_TERM,
        mao.OD_TERM_TYPE, mao.OD_GRACE_PERIOD
        from RB_AGREEMENT ma, RB_AGREEMENT_OVERDRAFT mao
        where ma.AGREEMENT_KEY_TYPE = 'IK'
        and ma.BASE_ACCT_NO = #{baseAcctNo}
        and mao.AGREEMENT_ID = ma.AGREEMENT_ID
        <if test="agreementStatus != null">
            and ma.AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <if test="clientNo != null">
            and ma.CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>


<!--    <select id="selectMaturityOdf" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"-->
<!--            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">-->
<!--        select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY, ma.BASE_ACCT_NO, ma.PROD_TYPE,-->
<!--        ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.BRANCH, ma.AGREEMENT_OPEN_DATE, ma.USER_ID, ma.LAST_CHANGE_USER_ID,-->
<!--        ma.LAST_CHANGE_DATE, ma.COMPANY, mao.OD_PROD_TYPE, mao.LOAN_PROD_TYPE, mao.LOAN_INTERNAL_KEY, mao.LOAN_BASE_ACCT_NO, mao.LOAN_ACCT_CCY, mao.LOAN_SEQ_NO, mao.OD_METHOD, mao.OD_CCY, mao.OD_AMT, mao.OD_TERM, mao.OD_TERM_TYPE, mao.OD_GRACE_PERIOD-->
<!--        from RB_AGREEMENT ma, RB_AGREEMENT_OVERDRAFT mao-->
<!--        where ma.AGREEMENT_KEY_TYPE = 'IK'-->
<!--        and mao.AGREEMENT_ID = ma.AGREEMENT_ID-->
<!--        and  ma.AGREEMENT_STATUS = 'A'-->
<!--        <![CDATA[-->
<!--            and END_DATE < #{lastRunDate}-->

<!--        ]]>-->
<!--    </select>-->
    <select id="getAgreementsByAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        <where>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementStatus != null and agreementStatus.length() > 0">
            and AGREEMENT_STATUS= #{agreementStatus}
        </if>
        <if test="agreementStatus =='A'.toString()">
            and AGREEMENT_STATUS !='E'
        </if>
        <if test="agreementId != null and agreementId.length() > 0">
            and AGREEMENT_ID= #{agreementId}
        </if>
        <if test="agreementType != null and agreementType.length() > 0">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>

    <select id="getOdfAgrtByBaesAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where 1=1
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementType != null and agreementType.length() > 0">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getOdfAgrtByBaseAcctNoAndStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where AGREEMENT_STATUS in ('A','P')
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementType != null and agreementType.length() > 0">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getOdfAgrtByBaseAcctNoByAgreementStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        <where>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementStatus =='A'.toString()">
            and AGREEMENT_STATUS !='E'
        </if>
        <if test="agreementId != null and agreementId.length() > 0">
            and AGREEMENT_ID= #{agreementId}
        </if>
        <if test="agreementType != null and agreementType.length() > 0">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>
    <select id="getAgreementsByInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="agreementStatus != null and agreementStatus != ''">
            and AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <if test="agreementType != null and agreementType != ''">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getOdfAgrtByBaseAcctNoByStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="agreementType != null">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="agreementStatus != null">
            and AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAgreementsByBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.agr.MbAgreementOverdraftModel">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where BASE_ACCT_NO = #{baseAcctNo}
        and AGREEMENT_STATUS IN ('A','P')
        <if test="agreementType != null">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getOdfAgrtByAgreementId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where AGREEMENT_ID = #{agreementId}
        and AGREEMENT_STATUS in('A','P','E')
        <if test="agreementType != null">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>


    <select id="getOdfAgrtByBaseAcctNoIncludeE" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where BASE_ACCT_NO = #{baseAcctNo}
        and AGREEMENT_STATUS in('A','P','E')
        <if test="agreementType != null">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY TRAN_TIMESTAMP desc
    </select>

    <update id="updteStatus" parameterType="java.util.Map">
        update RB_AGREEMENT_OVERDRAFT
        <set>
            <if test="agreementStatus != null">
                AGREEMENT_STATUS = #{agreementStatus},
            </if>
            <if test="remark != ''">
                REMARK = #{remark},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where AGREEMENT_ID = #{agreementId}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and CLIENT_NO = #{clientNo}
    </update>
    <update id="updteStatusP" parameterType="java.util.Map">
        update RB_AGREEMENT_OVERDRAFT
        <set>
            <if test="agreementStatus != null">
                AGREEMENT_STATUS = #{agreementStatus},
            </if>
            <if test="remark != ''">
                REMARK = #{remark},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where AGREEMENT_ID = #{agreementId}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and CLIENT_NO = #{clientNo}
    </update>

    <select id="getOverdraftByParams" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_OVERDRAFT
        where AGREEMENT_STATUS IN ('A', 'P')
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementId != null and agreementId.length() > 0">
            and AGREEMENT_ID = #{agreementId}
        </if>
        <if test="agreementType != null and agreementType.length() > 0">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="effectDate != null">
            and END_DATE <![CDATA[ >= ]]> #{effectDate}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
