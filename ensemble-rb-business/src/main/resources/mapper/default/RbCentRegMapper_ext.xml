<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCentReg">

    <select id="getCentRegList" resultType="java.util.Map"
            parameterType="java.util.Map">
        SELECT A.*,
        B.SEQ_NO         ORIG_SEQ_NO,
        B.CHANNEL_SEQ_NO ORIG_CHANNEL_SEQ_NO,
        B.SUB_SEQ_NO     ORIG_SUB_SEQ_NO,
        B.TRAN_DATE      ORIG_TRAN_DATE,
        B.BASE_ACCT_NO,
        B.ACCT_DESC      ACCT_NAME,
        B.TRAN_AMT       ORIG_TRAN_AMT
        FROM RB_CENT_REG A, RB_TRAN_HIST B
        WHERE
        A.CLIENT_NO = B.CLIENT_NO AND
        <!--A.ORIG_SUB_SEQ_NO = B.SUB_SEQ_NO AND
        A.CLIENT_NO = B.CLIENT_NO
        AND--> A.ORIG_CHANNEL_SEQ_NO = B.CHANNEL_SEQ_NO
        <!--AND A.ORIG_SUB_SEQ_NO = B.SUB_SEQ_NO
        AND A.EVENT_TYPE=B.EVENT_TYPE-->
        <if test="centDealType!=null and centDealType.length() > 0">
            AND	A.CENT_DEAL_TYPE = #{centDealType}
        </if>
        <if test="userId!=null and userId.length() > 0">
            AND	A.USER_ID = #{userId}
        </if>
        <if test="baseAcctNo!=null and baseAcctNo.length() > 0">
            AND	B.BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="centAmt!=null">
            AND	A.CENT_AMT = #{centAmt}
        </if>
        <if test="startDate != null ">
            AND <![CDATA[ A.TRAN_DATE >= #{startDate,jdbcType=DATE}]]>
        </if>
        <if test="endDate != null ">
            AND <![CDATA[ A.TRAN_DATE <= #{endDate,jdbcType=DATE}]]>
        </if>
        <if test="branchIds != null and branchIds.size() > 0">
            AND B.TRAN_BRANCH in
            <foreach collection="branchIds" item="branchId" open="(" close=")" separator=",">
                #{branchId}
            </foreach>
        </if>
        and  A.CLIENT_NO = #{clientNo}
    </select>

    <select id="getCentRegList2" resultType="java.util.Map"
            parameterType="java.util.Map">
        SELECT A.*,
        B.SEQ_NO ORIG_SEQ_NO,
        B.CHANNEL_SEQ_NO ORIG_CHANNEL_SEQ_NO,
        B.SUB_SEQ_NO ORIG_SUB_SEQ_NO,
        B.TRAN_DATE ORIG_TRAN_DATE,
        B.CHARGE_TO_BASE_ACCT_NO BASE_ACCT_NO,
        '手续费收取' ACCT_NAME,
        B.FEE_AMT ORIG_TRAN_AMT
        FROM RB_CENT_REG A, RB_SERV_CHARGE_HIST B
        WHERE A.CLIENT_NO = B.CLIENT_NO
        AND A.ORIG_SUB_SEQ_NO = B.SUB_SEQ_NO
        AND A.ORIG_CHANNEL_SEQ_NO = B.CHANNEL_SEQ_NO
        <if test="centDealType!=null and centDealType.length() > 0">
            AND	A.CENT_DEAL_TYPE = #{centDealType}
        </if>
        <if test="userId!=null and userId.length() > 0">
            AND	A.USER_ID = #{userId}
        </if>
        <if test="baseAcctNo!=null and baseAcctNo.length() > 0">
            AND	B.CHARGE_TO_BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="centAmt!=null">
            AND	A.CENT_AMT = #{centAmt}
        </if>
        <if test="startDate != null ">
            AND <![CDATA[ A.TRAN_DATE >= #{startDate,jdbcType=DATE}]]>
        </if>
        <if test="endDate != null">
            AND <![CDATA[ A.TRAN_DATE <= #{endDate,jdbcType=DATE}]]>
        </if>
        <if test="branchIds != null and branchIds.size() > 0">
            AND B.TRAN_BRANCH in
            <foreach collection="branchIds" item="branchId" open="(" close=")" separator=",">
                #{branchId}
            </foreach>
        </if>
        and  A.CLIENT_NO = #{clientNo}
    </select>

    <select id="getCentRegList3" resultType="java.util.Map"
            parameterType="java.util.Map">
        SELECT A.*,
        B.SEQ_NO           ORIG_SEQ_NO,
        B.CHANNEL_SEQ_NO   ORIG_CHANNEL_SEQ_NO,
        B.SUB_SEQ_NO       ORIG_SUB_SEQ_NO,
        B.TRAN_DATE        ORIG_TRAN_DATE,
        B.OTH_BASE_ACCT_NO,
        '现金收取'    ACCT_NAME ,
        B.TRAN_AMT         ORIG_TRAN_AMT
        FROM RB_CENT_REG A, RB_MISC_HIST B
        WHERE A.CLIENT_NO = B.CLIENT_NO
        AND A.ORIG_SUB_SEQ_NO = B.SUB_SEQ_NO
        AND A.ORIG_CHANNEL_SEQ_NO = B.CHANNEL_SEQ_NO
        <if test="centDealType!=null and centDealType.length() > 0">
            AND	A.CENT_DEAL_TYPE = #{centDealType}
        </if>
        <if test="userId!=null and userId.length() > 0">
            AND	A.USER_ID = #{userId}
        </if>
        <if test="baseAcctNo!=null and baseAcctNo.length() > 0">
            AND	B.OTH_BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="centAmt!=null">
            AND	A.CENT_AMT = #{centAmt}
        </if>
        <if test="startDate != null ">
            AND <![CDATA[ A.TRAN_DATE >= #{startDate,jdbcType=DATE}]]>
        </if>
        <if test="endDate != null">
            AND <![CDATA[ A.TRAN_DATE <= #{endDate,jdbcType=DATE}]]>
        </if>
        <if test="branchIds != null and branchIds.size() > 0">
            AND B.TRAN_BRANCH in
            <foreach collection="branchIds" item="branchId" open="(" close=")" separator=",">
                #{branchId}
            </foreach>
        </if>
        and  A.CLIENT_NO = #{clientNo}
    </select>


    <select id="getCentRegByRefAndClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCentReg">
        select
        <include refid="Base_Column"/>
        from RB_CENT_REG
        where REFERENCE = #{reference}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        AND STATUS != 'R'
        AND STATUS != 'X'
        AND STATUS != 'W'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by TRAN_DATE desc,TRAN_TIMESTAMP desc,SEQ_NO+0 desc
    </select>

    <select id="getByCentReg" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCentReg">
        select
        <include refid="Base_Column"/>
        from RB_CENT_REG
        <where>
            <if test="reference != null and reference != ''">
                AND REFERENCE = #{reference}
            </if>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="origChannelSeqNo != null and origChannelSeqNo != ''">
                AND ORIG_CHANNEL_SEQ_NO = #{origChannelSeqNo}
            </if>
            AND STATUS != 'R'
            AND STATUS != 'X'
            AND STATUS != 'W'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>



        order by TRAN_DATE desc,TRAN_TIMESTAMP desc,SEQ_NO+0 desc
    </select>

</mapper>
