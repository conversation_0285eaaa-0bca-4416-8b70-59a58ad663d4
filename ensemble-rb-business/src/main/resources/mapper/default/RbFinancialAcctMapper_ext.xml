<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">

  <select id="getMbFinAcctByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    select <include refid="Base_Column"/>
    from RB_FINANCIAL_ACCT
    where FIN_STATUS != 'E'
    and FIN_INTERNAL_KEY = #{finInternalKey}
    <if test="clientNo != null and  clientNo != ''  ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company.length() > 0">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbFinAcctByFinInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    select <include refid="Base_Column"/>
    from RB_FINANCIAL_ACCT
    where FIN_STATUS = 'A'
    and FIN_INTERNAL_KEY = #{finInternalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbFinAcctsBySignDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    <!-- 此排序有专用交易使用，请勿更改  查询未销户的理财子账户-->
    select
    mb.ACCT_CCY,
    mb.ACCT_SEQ_NO,
    mb.BASE_ACCT_NO,
    mb.FIN_ACCT_CCY,
    mb.FIN_ACCT_SEQ_NO,
    mb.FIN_BASE_ACCT_NO,
    mb.FIN_INTERNAL_KEY,
    mb.FIN_PROD_TYPE,
    mb.FIN_STATUS,
    mb.INTERNAL_KEY,
    mb.PROD_TYPE,
    mb.TRAN_TIMESTAMP
    from RB_FINANCIAL_ACCT mb,RB_ACCT ma
    where ma.CLIENT_NO=mb.CLIENT_NO
    and ma.CLIENT_NO=#{clientNo}
    AND mb.FIN_STATUS != 'E'
    and mb.INTERNAL_KEY = #{internalKey}
    and ma.INTERNAL_KEY = mb.FIN_INTERNAL_KEY
    and ma.ACCT_STATUS !='C'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND ma.COMPANY = #{company}
    </if>
    order by mb.FIN_ACCT_SEQ_NO DESC
  </select>

  <select id="getMbFinByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    select <include refid="Base_Column"/>
    from RB_FINANCIAL_ACCT
    where FIN_STATUS != 'E'
    and INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbFinAccts" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    select <include refid="Base_Column"/>
    from RB_FINANCIAL_ACCT
    where INTERNAL_KEY = #{internalKey}
    <!--and FIN_TYPE != 'U'-->
    and FIN_STATUS != 'E'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbFinAcctByBaseNoFinType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    <!--只要有结果就返回一条-->
    select <include refid="Base_Column"/>
    from RB_FINANCIAL_ACCT
    where FIN_STATUS != 'E'
    and FIN_PROD_TYPE = #{finProdType}
    and INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY INTERNAL_KEY
    LIMIT 1
  </select>
  <select id="getMbFinAcctsByInternalKeyAndClient" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    select
      mb.ACCT_CCY,
      mb.ACCT_SEQ_NO,
      mb.BASE_ACCT_NO,
      mb.FIN_ACCT_CCY,
      mb.FIN_ACCT_SEQ_NO,
      mb.FIN_BASE_ACCT_NO,
      mb.FIN_INTERNAL_KEY,
      mb.FIN_PROD_TYPE,
      mb.FIN_STATUS,
      mb.INTERNAL_KEY,
      mb.PROD_TYPE,
      mb.TRAN_TIMESTAMP
    from RB_FINANCIAL_ACCT mb,RB_ACCT ma
    where ma.CLIENT_NO=mb.CLIENT_NO
      and ma.CLIENT_NO=#{clientNo}
      and mb.INTERNAL_KEY = #{internalKey}
      and ma.INTERNAL_KEY = mb.FIN_INTERNAL_KEY
      and ma.ACCT_STATUS !='C'
    and ma.PROD_TYPE not in ('14033','13037','13038','13039','13040','13041')
    order by mb.FIN_ACCT_SEQ_NO+0
  </select>
  <select id="getMbFinAcctsByInternalkeyAndClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct">
    select <include refid="Base_Column"/>
    from RB_FINANCIAL_ACCT
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != '' ">
      AND  CLIENT_NO = #{clientNo}
    </if>
    and FIN_STATUS != 'E'
  </select>

</mapper>
