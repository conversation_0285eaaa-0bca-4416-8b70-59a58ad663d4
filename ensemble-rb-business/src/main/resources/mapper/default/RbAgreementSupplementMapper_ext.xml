<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">

    <select id="selectActiveByAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and PROD_TYPE = #{prodType}
        and ACCT_CCY = #{ccy}
        and ACCT_SEQ_NO = #{acctSeqNo}
        and AGREEMENT_STATUS='A'
        and end_date <![CDATA[  >=  ]]> #{runDate}
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID DESC

    </select>

    <select id="selectByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_STATUS in ('A','T')
        ORDER BY AGREEMENT_ID DESC
    </select>

    <select id="selectActiveByCardNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and AGREEMENT_STATUS='A'
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID DESC

    </select>

    <select id="selectAOrTStatusByCardNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and AGREEMENT_STATUS in ('A','T')
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID DESC

    </select>


    <select id="selectByAcctNoList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and PROD_TYPE = #{prodType}
        and ACCT_CCY = #{ccy}
        and ACCT_SEQ_NO = #{acctSeqNo}
        <if test="agreementStatus != null ">
            AND AGREEMENT_STATUS IN
            <foreach collection="agreementStatus" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID DESC

    </select>

    <select id="getActiveAgreementBySlContractNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where LOAN_NO = #{loanNo}
        and AGREEMENT_STATUS='A'
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID DESC
    </select>

    <select id="getAgreementBySlContractNoList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where LOAN_NO = #{loanNo}
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY AGREEMENT_ID DESC
    </select>

    <select id="selectExpiryByAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SUPPLEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and PROD_TYPE = #{prodType}
        and ACCT_CCY = #{ccy}
        and ACCT_SEQ_NO = #{acctSeqNo}
        and AGREEMENT_STATUS='E'
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY TRAN_TIMESTAMP DESC
    </select>

</mapper>
