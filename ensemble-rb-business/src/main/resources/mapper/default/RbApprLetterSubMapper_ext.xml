<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <if test="mainSubInd != null">
            AND MAIN_SUB_IND = #{mainSubInd}
        </if>
        <if test="ccy != null">
            AND CCY = #{ccy}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>
    <select id="selectByMainSubInd" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        select <include refid="Base_Column"/>
        from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <if test="mainSubInd != null">
            AND MAIN_SUB_IND = #{mainSubInd}
        </if>
        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        delete from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <if test="mainSubInd != null">
            AND MAIN_SUB_IND = #{mainSubInd}
        </if>
        <if test="ccy != null">
            AND CCY = #{ccy}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        update RB_APPR_LETTER_SUB
        <set>
            <if test="apprLimitAmt != null">
                APPR_LIMIT_AMT = #{apprLimitAmt},
            </if>
            <if test="crTotalAmt != null">
                CR_TOTAL_AMT = #{crTotalAmt},
            </if>
            <if test="drTotalAmt != null">
                DR_TOTAL_AMT = #{drTotalAmt},
            </if>
            <if test="graceAmt != null">
                GRACE_AMT = #{graceAmt},
            </if>
            <if test="graceProportion != null">
                GRACE_PROPORTION = #{graceProportion},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where APPR_LETTER_NO = #{apprLetterNo}
        AND MAIN_SUB_IND = #{mainSubInd}
        AND CCY = #{ccy}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        insert into RB_APPR_LETTER_SUB
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="apprLetterNo != null">
                APPR_LETTER_NO,
            </if>
            <if test="mainSubInd != null">
                MAIN_SUB_IND,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="apprLimitAmt != null">
                APPR_LIMIT_AMT,
            </if>
            <if test="crTotalAmt != null">
                CR_TOTAL_AMT,
            </if>
            <if test="drTotalAmt != null">
                DR_TOTAL_AMT,
            </if>
            <if test="graceAmt != null">
                GRACE_AMT,
            </if>
            <if test="graceProportion != null">
                GRACE_PROPORTION,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="apprLetterNo != null">
                #{apprLetterNo},
            </if>
            <if test="mainSubInd != null">
                #{mainSubInd},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="apprLimitAmt != null">
                #{apprLimitAmt},
            </if>
            <if test="crTotalAmt != null">
                #{crTotalAmt},
            </if>
            <if test="drTotalAmt != null">
                #{drTotalAmt},
            </if>
            <if test="graceAmt != null">
                #{graceAmt},
            </if>
            <if test="graceProportion != null">
                #{graceProportion},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
    <select id="selectByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub"
            resultType="String">
    select  APPR_LETTER_NO
    from RB_APPR_LETTER_SUB
    where APPR_LETTER_NO = #{apprLetterNo}
      AND MAIN_SUB_IND = #{mainSubInd}
      AND CCY = #{ccy}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>
    <select id="selectByAPPRSubLetterNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO = #{clientNo}
        </if>
    </select>
    <select id="qryApprInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

        <if test="mainSubInd != null">
            AND MAIN_SUB_IND = #{mainSubInd}
        </if>
        <if test="ccy!= null">
            AND CCY = #{ccy}
        </if>
        <if test="clientNo!= null">
            AND CLIENT_NO = #{clientNo}
        </if>
    </select>

    <select id="selectByMainSub" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        select <include refid="Base_Column"/>
        from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <if test="mainSubInd != null">
            AND MAIN_SUB_IND = #{mainSubInd}
        </if>
        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO = #{clientNo}
        </if>
    </select>

    <select id="getApprLetterSubByNoAndCompany" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub">
        select
        <include refid="Base_Column"/>
        from RB_APPR_LETTER_SUB
        where APPR_LETTER_NO = #{apprLetterNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="companyList != null">
            AND COMPANY in
            <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO = #{clientNo}
        </if>
    </select>

</mapper>
