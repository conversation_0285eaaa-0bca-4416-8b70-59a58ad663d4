<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistBook">
  <resultMap id="MbTranHistBookMap" type="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistBook">
    <result column="TRAN_DATE" property="tranDate" jdbcType="DATE" javaType="DATE" />
  </resultMap>

  <select id="getPrintTranHist" parameterType="java.util.Map" resultMap="MbTranHistBookMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_book
    where INTERNAL_KEY IN
    (SELECT INTERNAL_KEY FROM RB_ACCT WHERE BASE_ACCT_NO = #{baseAcctNo} AND ACCT_TYPE = #{acctType})
    <if test="bookPrintFlag != null and bookPrintFlag != ''">
      and BOOK_PRINT_FLAG = #{bookPrintFlag}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO= #{clientNo}
    </if>
    <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
      and TRAN_STATUS != 'W'
    </if>
    order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO+0 asc
  </select>

  <update id="updateBookUpdFlagBySeqNo" parameterType="java.util.Map">
    update RB_tran_hist_book set BOOK_PRINT_FLAG ='Y'
    where SEQ_NO = #{seqNo}
    AND BOOK_PRINT_FLAG = 'N'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
  </update>

  <update id="updateBookUpdFlagBySeqNo1" parameterType="java.util.Map">
    update RB_tran_hist_book set BOOK_PRINT_FLAG ='N'
    where SEQ_NO = #{seqNo}
    AND BOOK_PRINT_FLAG = 'Y'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
  </update>

  <update id="updateMergeBookUpdFlag" parameterType="java.util.Map">
    update RB_tran_hist_book set BOOK_PRINT_FLAG ='Y'
    where BOOK_PRINT_FLAG = 'N'
    <if test="seqNos != null ">
      AND SEQ_NO IN
      <foreach collection="seqNos" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
  </update>
</mapper>
