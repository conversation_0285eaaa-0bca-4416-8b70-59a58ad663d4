<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">

    <update id="updRbAgreementMatureInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        update Rb_Agreement_Mature_Info
        <set>
            <if test="retMsg != null and  retMsg != '' ">
                Ret_Msg = #{retMsg, jdbcType=VARCHAR},
            </if>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null and  errorCode != '' ">
                Error_Code = #{errorCode, jdbcType=VARCHAR},
            </if>
            <if test="errorDesc != null and  errorDesc != '' ">
                Error_Desc = #{errorDesc, jdbcType=VARCHAR},
            </if>
            <if test="frozenSeqNo != null and  frozenSeqNo != '' ">
                frozen_Seq_No = #{frozenSeqNo, jdbcType=VARCHAR},
            </if>
            <if test="bondAcctSeqNo != null and  bondAcctSeqNo != '' ">
                bond_Acct_Seq_No = #{bondAcctSeqNo, jdbcType=VARCHAR},
            </if>
            <if test="reference != null and  reference != '' ">
                reference = #{reference, jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="batchNo != null and  batchNo != '' ">
               and Batch_No = #{batchNo, jdbcType=VARCHAR}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                and Seq_No = #{seqNo, jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                and client_no = #{clientNo, jdbcType=VARCHAR}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </update>

    <update id="updRbAgreementMatureInfoStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        update Rb_Agreement_Mature_Info
        <set>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="batchNo != null and  batchNo != '' ">
                and Batch_No = #{batchNo, jdbcType=VARCHAR}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                and Seq_No = #{seqNo, jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                and client_no = #{clientNo, jdbcType=VARCHAR}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </update>

    <select id="selectAgreementMatureInfoByContractNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        SELECT <include refid="Base_Column"/>
        FROM <include refid="Table_Name" />
        <where>
        <if test="contractNo != null and contractNo != '' ">
            AND  CONTRACT_NO = #{contractNo,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>

<!--    <select id="selectAgreementMatureInfoBybatchStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        SELECT <include refid="Base_Column"/>
        FROM <include refid="Table_Name" />
        WHERE 1=1
        <if test="batchStatus != null and batchStatus != '' ">
            AND  BATCH_STATUS = #{batchStatus,jdbcType=VARCHAR}
        </if>
        AND <![CDATA[rownum < 501]]>
    </select>-->

    <select id="selectAgreementMatureInfoByContractNo1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        SELECT <include refid="Base_Column"/>
        FROM Rb_Agreement_Mature_Info
        WHERE BATCH_STATUS != 'S'
        <if test="batchNo != null and  batchNo != '' ">
            and Batch_No = #{batchNo, jdbcType=VARCHAR}
        </if>

    </select>
    <select id="selectAgreementMatureInfoCountByBatchNo" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) success FROM RB_AGREEMENT_MATURE_INFO
        <where>
            <if test="batchStatus != null and  batchStatus != '' ">
                and BATCH_STATUS = #{batchStatus, jdbcType=VARCHAR}
            </if>
            <if test="batchNo != null and  batchNo != '' ">
                and BATCH_NO = #{batchNo, jdbcType=VARCHAR}
            </if>
        </where>

    </select>

    <select id="selectAgreementMatureInfoBybond" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        SELECT <include refid="Base_Column"/>
        FROM Rb_Agreement_Mature_Info
        WHERE AGREEMENT_OPERATE_TYPE in ('45','46')
        <if test="batchNo != null and  batchNo != '' ">
            and Batch_No = #{batchNo, jdbcType=VARCHAR}
        </if>

        AND BATCH_STATUS = 'S'
    </select>

    <select id="selectAgreementMatureInfoBybondAll" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        SELECT <include refid="Base_Column"/>
        FROM Rb_Agreement_Mature_Info
        WHERE AGREEMENT_OPERATE_TYPE in ('45','46')
        <if test="batchNo != null and  batchNo != '' ">
            and Batch_No = #{batchNo, jdbcType=VARCHAR}
        </if>

    </select>

    <select id="selectAgreementMatureInfoForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMatureInfo">
        SELECT <include refid="Base_Column"/>
        FROM Rb_Agreement_Mature_Info
        WHERE BATCH_STATUS ='P'
        <if test="batchNo != null and  batchNo != '' ">
            and Batch_No = #{batchNo, jdbcType=VARCHAR}
        </if>
        <if test="channelSeqNo != null and  channelSeqNo != '' ">
            and CHANNEL_SEQ_NO = #{channelSeqNo, jdbcType=VARCHAR}
        </if>
        for update

    </select>

</mapper>
