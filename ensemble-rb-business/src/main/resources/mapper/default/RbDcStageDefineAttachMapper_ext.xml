<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefineAttach">

<!--  <update id="updateRbDcStageDefineAttach" parameterType="java.util.Map">-->
<!--    update RB_DC_STAGE_DEFINE_ATTACH-->
<!--    <set>-->
<!--      FLOAT_RATE = #{floatRate},-->
<!--      SG_MAX_AMT = #{sgMaxAmt},-->
<!--      INT_START_DATE = #{intStartDate},-->
<!--      REDEMPTION_INT_TYPE = #{redemptionIntType},-->
<!--      TOHONOR_RATE = #{tohonorRate},-->
<!--      DIRECTION_CHARGE_INT_FLAG = #{directionChargeIntFlag},-->
<!--      TRF_OUT_FEE_AMT = #{trfOutFeeAmt},-->
<!--      EMAIL = #{email},-->
<!--      CALM_DAYS = #{calmDays},-->
<!--      CHANGE_MIN_AMT = #{changeMinAmt},-->
<!--      MATURITY_DATE = #{maturityDate},-->
<!--      PROMISSORY_REDEEM_DATE = #{promissoryRedeemDate},-->
<!--      TRF_IN_FEE_AMT = #{trfInFeeAmt},-->
<!--      ON_SALE_CHANNEL = #{onSaleChannel},-->
<!--      REAL_RATE = #{realRate},-->
<!--      TRAN_TIMESTAMP = #{tranTimestamp}-->
<!--    </set>-->
<!--    where STAGE_CODE = #{stageCode}-->
<!--  </update>-->

  <select id="queryStageDefineAttachMap" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefineAttach">
    select <include refid="Base_Column"/>
    FROM RB_DC_STAGE_DEFINE_ATTACH
    <where>
      <if test="stageCodes != null">
        STAGE_CODE in
        <foreach collection="stageCodes" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <update id="updateRbDcStageDefineAttach" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefineAttach">
    UPDATE RB_DC_STAGE_DEFINE_ATTACH
    <set>
      <if test="availableLimit != null">
        AVAILABLE_LIMIT=#{availableLimit},
      </if>
      <if test="keepMinBal != null">
        KEEP_MIN_BAL=#{keepMinBal},
      </if>
      <if test="isTrfEnabled != null and isTrfEnabled != ''">
        IS_TRF_ENABLED=#{isTrfEnabled},
      </if>

      <if test="inlandOffshore != null and inlandOffshore != ''">
        INLAND_OFFSHORE=#{inlandOffshore},
      </if>

      <if test="spreadPercent != null and spreadPercent != ''">
        SPREAD_PERCENT=#{spreadPercent},
      </if>

      <if test="intStartFlag != null and intStartFlag != ''">
        INT_START_FLAG=#{intStartFlag},
      </if>

      <if test="allowFundSourceInnerFlag != null and allowFundSourceInnerFlag != ''">
        ALLOW_FUND_SOURCE_INNER_FLAG=#{allowFundSourceInnerFlag},
      </if>
      <if test="prodDescAddress != null and prodDescAddress != ''">
        PROD_DESC_ADDRESS=#{prodDescAddress},
      </if>

      <if test="redemptionIntFlag != null and redemptionIntFlag != ''">
        REDEMPTION_INT_FLAG=#{redemptionIntFlag},
      </if>

      <if test="userId != null and userId != ''">
        USER_ID=#{userId},
      </if>
      <if test="company != null and company != ''">
        COMPANY=#{company},
      </if>
      <if test="trfInFeeType != null and trfInFeeType != ''">
        TRF_IN_FEE_TYPE=#{trfInFeeType},
      </if>
      <if test="sgMinAmt != null">
        SG_MIN_AMT=#{sgMinAmt},
      </if>
      <if test="trfOutFeeType != null and trfOutFeeType != ''">
        TRF_OUT_FEE_TYPE=#{trfOutFeeType},
      </if>
      <if test="sellBranch != null and sellBranch != ''">
        SELL_BRANCH=#{sellBranch},
      </if>
      <if test="settleAcctType != null and settleAcctType != ''">
        SETTLE_ACCT_TYPE=#{settleAcctType},
      </if>
      FLOAT_RATE=#{floatRate},
      SG_MAX_AMT=#{sgMaxAmt},
      INT_START_DATE=#{intStartDate},
      REDEMPTION_INT_TYPE=#{redemptionIntType},
      TOHONOR_RATE=#{tohonorRate},
      DIRECTION_CHARGE_INT_FLAG=#{directionChargeIntFlag},
      TRF_OUT_FEE_AMT=#{trfOutFeeAmt},
      EMAIL=#{email},
      CALM_DAYS=#{calmDays},
      CHANGE_MIN_AMT=#{changeMinAmt},
      MATURITY_DATE=#{maturityDate},
      PROMISSORY_REDEEM_DATE=#{promissoryRedeemDate},
      TRF_IN_FEE_AMT=#{trfInFeeAmt},
      ON_SALE_CHANNEL=#{onSaleChannel},
      REAL_RATE=#{realRate},
      TRAN_TIMESTAMP=#{tranTimestamp}
    </set>
    WHERE STAGE_CODE=#{stageCode};

  </update>
</mapper>
