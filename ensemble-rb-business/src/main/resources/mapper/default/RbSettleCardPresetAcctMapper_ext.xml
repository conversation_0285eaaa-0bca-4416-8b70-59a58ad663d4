<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardPresetAcct">

	<select id="getByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardPresetAcct">
		select <include refid="Base_Column"/>
		from RB_SETTLE_CARD_PRESET_ACCT
		where CARD_NO= #{cardNo}
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
	</select>

	<delete id="deleteByCardNo" parameterType="java.util.Map">
		delete from RB_SETTLE_CARD_PRESET_ACCT
		where CARD_NO = #{cardNo}
		<if test="clientNo != null">
			AND CLIENT_NO = #{clientNo}
		</if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
	</delete>
    <update id="updateCardNoByCardNo" parameterType="java.util.Map">
        update RB_SETTLE_CARD_PRESET_ACCT
        <set>
            <if test="newCardNo != null">
                CARD_NO=#{newCardNo},
            </if>
        </set>
        where  CARD_NO = #{cardNo}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="selectRbSettleCardPresetAcctList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardPresetAcct">
        select <include refid="Base_Column"/>
        from RB_SETTLE_CARD_PRESET_ACCT
        where CARD_NO= #{cardNo}
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
