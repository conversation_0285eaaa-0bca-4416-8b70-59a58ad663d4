<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctRiskChangeRegister">

    <select id="getRbAcctRiskChange" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctRiskChangeRegister">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_RISK_CHANGE_REGISTER
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="prodType != null">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="Ccy != null">
            AND CCY = #{Ccy}
        </if>
        <if test="acctSeqNo != null">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY TRAN_TIMESTAMP
    </select>

    <select id="selectRiskChangeRegister" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctRiskChangeRegister"
            parameterType="java.util.Map" >
        select <include refid="Base_Column"/>
        from RB_ACCT_RISK_CHANGE_REGISTER
        WHERE
        1=1
        <if test="startDate != null and endDate != null  " >
            AND TRAN_DATE between #{startDate} and #{endDate}
        </if>
        <if test="baseAcctNo != null and baseAcctNo !=''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctSeqNo!=null and acctSeqNo != ''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="prodType!=null and prodType != ''">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="ccy!=null and ccy != ''">
            AND CCY = #{ccy}
        </if>
        <if test="acctRiskLevel!=null and acctRiskLevel != ''">
            AND ACCT_RISK_LEVEL = #{acctRiskLevel}
        </if>
        <if test="oldacctRiskLevel!=null and oldacctRiskLevel != ''">
            AND OLD_ACCT_RISK_LEVEL = #{oldacctRiskLevel}
        </if>

    </select>

    <update id="updateBaseAcctNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctRiskChangeRegister">
        update RB_ACCT_RISK_CHANGE_REGISTER
        SET BASE_ACCT_NO = #{cardNo}
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="prodType != null and prodType !=''">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="ccy != null and ccy !=''">
            AND CCY = #{ccy}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO = #{clientNo}
        </if>
    </update>

</mapper>
