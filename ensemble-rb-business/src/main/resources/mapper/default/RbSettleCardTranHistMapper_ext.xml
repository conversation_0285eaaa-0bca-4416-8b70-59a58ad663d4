<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardTranHist">

  <!-- Created by admin on 2019/01/10 11:12:03. -->
  <select id="getMbSettleCardTranHistByMap" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardTranHist">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_TRAN_HIST
    where CARD_NO = #{cardNo}
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP desc
  </select>
  <select id="getMbSettleCardTranHistWithMap" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardTranHist">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_TRAN_HIST
    <where>
    <if test="reference != null and reference != ''">
      and REFERENCE = #{reference}
    </if>
    <if test="tranType != null and tranType != ''">
      and TRAN_TYPE = #{tranType}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="getMbSettleCardTranHistByMapTranDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardTranHist">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_TRAN_HIST
    where CARD_NO = #{cardNo}
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="startDate != null and endDate != null ">
      and TRAN_DATE BETWEEN #{startDate} and #{endDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP desc
  </select>

  <select id="getMbSettleCardTranHistByMapTranDateAndCardList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardTranHist">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_TRAN_HIST
    where CARD_NO in
    <foreach item="item" index="index" collection="cardNoList" open="(" separator="," close=")">
      #{item.cardNo}
    </foreach>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="startDate != null and endDate != null ">
      and TRAN_DATE BETWEEN #{startDate} and #{endDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP desc
  </select>

  <select id="getMbSettleCardTranHistByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardTranHist">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_TRAN_HIST
    where BASE_ACCT_NO = #{baseAcctNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="cardNo != null and cardNo != ''">
      AND CARD_NO = #{cardNo}
    </if>
    <if test="startDate != null and endDate != null ">
      and TRAN_DATE BETWEEN #{startDate} and #{endDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP desc
  </select>
  <select id="getMbSettleCardTranHistByRefNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardTranHist">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_TRAN_HIST
    where REFERENCE = #{reference}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
