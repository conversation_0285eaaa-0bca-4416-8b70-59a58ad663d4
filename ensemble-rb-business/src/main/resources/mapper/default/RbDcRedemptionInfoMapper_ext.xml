<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcRedemptionInfo">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcRedemptionInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcRedemptionInfo">
    select <include refid="Base_Column"/>
    from RB_DC_REDEMPTION_INFO
    where ISSUE_YEAR = #{issueYear}
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null">
      AND CCY = #{ccy}
    </if>
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcRedemptionInfo">
    delete from RB_DC_REDEMPTION_INFO
    where ISSUE_YEAR = #{issueYear}
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null">
      AND CCY = #{ccy}
    </if>
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcRedemptionInfo">
    update RB_DC_REDEMPTION_INFO
    <set>
      <if test="tohonorRate != null">
        TOHONOR_RATE = #{tohonorRate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="redemptionStatus != null">
        REDEMPTION_STATUS = #{redemptionStatus},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS = #{stageProdClass}
      </if>
    </set>
    where ISSUE_YEAR = #{issueYear}
    AND PROD_TYPE = #{prodType}
    AND CCY = #{ccy}
    AND STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcRedemptionInfo">
    insert into RB_DC_REDEMPTION_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        ISSUE_YEAR,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="tohonorRate != null">
        TOHONOR_RATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="redemptionStatus != null">
        REDEMPTION_STATUS,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        #{issueYear},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="tohonorRate != null">
        #{tohonorRate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
  <select id="getDcRedemptionInfoByStageCode" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcRedemptionInfo">
    select *
    from RB_DC_REDEMPTION_INFO
    where
     STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <update id="updateByTranDate" parameterType="java.util.Map">
    update RB_DC_REDEMPTION_INFO
    <set>
      REDEMPTION_STATUS = 'P'
    </set>
    where
    <![CDATA[
    TRAN_DATE <= #{tranDate}
    ]]>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
