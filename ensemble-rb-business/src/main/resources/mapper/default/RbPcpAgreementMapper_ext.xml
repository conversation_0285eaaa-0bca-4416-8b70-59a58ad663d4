<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">

  <select id="selectByPrimaryKeyByStatusA" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    select <include refid="Base_Column"/>
    from RB_PCP_AGREEMENT
    where AGREEMENT_ID = #{agreementId}
    AND AGREEMENT_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByMainAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>  FROM RB_PCP_AGREEMENT
    WHERE base_acct_no =#{baseAcctNo}
    <if test="prodType != null and prodType !=''">
      AND prod_type = #{prodType}
    </if>
    <if test="acctCcy != null and acctCcy !=''">
      AND acctCcy = #{acctCcy}
    </if>
    <if test="acctSeqNo != null and acctSeqNo !=''">
      AND acct_seq_no = #{acctSeqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY AGREEMENT_ID ASC
  </select>
  <select id="selectEffectAgreementByAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>  FROM RB_PCP_AGREEMENT
    WHERE AGREEMENT_STATUS != 'E'
    AND base_acct_no =#{baseAcctNo}
    <if test="prodType != null and prodType !=''">
      AND prod_type = #{prodType}
    </if>
    <if test="acctCcy != null and acctCcy !=''">
      AND acctCcy = #{acctCcy}
    </if>
    <if test="acctSeqNo != null and acctSeqNo !=''">
      AND acct_seq_no = #{acctSeqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY AGREEMENT_ID ASC
  </select>
  <select id="selectByAcctAndId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>  FROM RB_PCP_AGREEMENT
    WHERE AGREEMENT_STATUS='A'
    AND base_acct_no =#{baseAcctNo}
    <if test="prodType != null and prodType !=''">
      AND prod_type = #{prodType}
    </if>
    <if test="acctCcy != null and acctCcy !=''">
      AND acctCcy = #{acctCcy}
    </if>
    <if test="acctSeqNo != null and acctSeqNo !=''">
      AND acct_seq_no = #{acctSeqNo}
    </if>
    <if test="agreementId != null and agreementId !=''">
      AND AGREEMENT_ID = #{agreementId}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY AGREEMENT_ID ASC
  </select>
  <select id="selectPcpInfoByApprovalNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>  FROM RB_PCP_AGREEMENT
    WHERE AGREEMENT_STATUS='A'
    AND APPROVAL_NO =#{approvalNo}
  </select>
  <select id="selectByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>  FROM RB_PCP_AGREEMENT
    WHERE INTERNAL_KEY =#{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY AGREEMENT_ID ASC
  </select>
  <select id="selectByGroupId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>
    FROM RB_PCP_AGREEMENT
    WHERE PCP_GROUP_ID =#{pcpGroupId}
    AND AGREEMENT_STATUS  in ('A','P')
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByGroupIdInStatusAPC" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>
    FROM RB_PCP_AGREEMENT
    WHERE PCP_GROUP_ID =#{pcpGroupId}
    AND AGREEMENT_STATUS  in ('A','P','C')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByGroupIdInStatusAPCE" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>
    FROM RB_PCP_AGREEMENT
    WHERE PCP_GROUP_ID =#{pcpGroupId}
    AND AGREEMENT_STATUS  in ('A','P','C','E')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByStatus" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement">
    SELECT <include refid="Base_Column"/>
    FROM RB_PCP_AGREEMENT
    WHERE AGREEMENT_STATUS  = 'P'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <update id="updatePcpAgreementRateForm" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement" >
    update RB_PCP_AGREEMENT
    <set>
      APPROVAL_NO = #{approvalNo,jdbcType=VARCHAR},
      AGREE_INT_RATE = #{agreeIntRate,jdbcType=DECIMAL}
    </set>
    <include refid="PrimaryKey_Where"/>
  </update>

  <update id="updateRateFormInfo" parameterType="java.util.Map" >
    update RB_PCP_AGREEMENT set AGREE_INT_RATE = '' WHERE AGREEMENT_ID = #{agreementId}
  </update>
</mapper>
