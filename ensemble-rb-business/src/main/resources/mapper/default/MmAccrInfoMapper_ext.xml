<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAccrInfo">
  <!-- Created by admin on 2019/01/14 16:23:15. -->
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAccrInfo">
    insert into MM_ACCR_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="intClass != null">
        INT_CLASS,
      </if>
      <if test="accrDate != null">
        ACCR_DATE,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="branch != null">
        BRANCH,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="intAccrued != null">
        INT_ACCRUED,
      </if>
      <if test="intAccruedCtd != null">
        INT_ACCRUED_CTD,
      </if>
      <if test="intAccruedCalcCtd != null">
        INT_ACCRUED_CALC_CTD,
      </if>
      <if test="intAccruedDiff != null">
        INT_ACCRUED_DIFF,
      </if>
      <if test="taxAccrued != null">
        TAX_ACCRUED,
      </if>
      <if test="taxAccruedCtd != null">
        TAX_ACCRUED_CTD,
      </if>
      <if test="taxAccruedCalcCtd != null">
        TAX_ACCRUED_CALC_CTD,
      </if>
      <if test="taxAccruedDiff != null">
        TAX_ACCRUED_DIFF,
      </if>
      <if test="taxType != null">
        TAX_TYPE,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="tranSource != null">
        TRAN_SOURCE,
      </if>
      <if test="accountingStatus != null">
        ACCOUNTING_STATUS,
      </if>
      <if test="profitCenter != null">
        PROFIT_CENTRE,
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE,
      </if>
      <if test="businessUnit != null">
        BUSINESS_UNIT,
      </if>
      <if test="agg != null">
        AGG,
      </if>
      <if test="intAmt != null">
        INT_AMT,
      </if>
      <if test="monthBasis != null">
        MONTH_BASIS,
      </if>
      <if test="yearBasis != null">
        YEAR_BASIS,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="reversal != null">
        REVERSAL,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="intCalcBal != null">
        INT_CALC_BAL,
      </if>
      <if test="glPosted != null">
        GL_POSTED,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="othReference != null">
        OTH_REFERENCE,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="acctSpreadRate != null">
        ACCT_SPREAD_RATE,
      </if>
      <if test="acctPercentRate != null">
        ACCT_PERCENT_RATE,
      </if>
      <if test="acctFixedRate != null">
        ACCT_FIXED_RATE,
      </if>
      <if test="agreeChangeType != null">
        AGREE_CHANGE_TYPE,
      </if>
      <if test="agreeFixedRate != null">
        AGREE_FIXED_RATE,
      </if>
      <if test="agreePercentRate != null">
        AGREE_PERCENT_RATE,
      </if>
      <if test="agreeSpreadRate != null">
        AGREE_SPREAD_RATE,
      </if>
      <if test="splitRateFlag != null">
        SPLIT_RATE_FLAG,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="tdIntNumDays != null">
        TD_INT_NUM_DAYS,
      </if>
      <if test="tdLastAccrDate != null">
        TD_LAST_ACCR_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="systemId != null">
        #{systemId},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="intClass != null">
        #{intClass},
      </if>
      <if test="accrDate != null">
        #{accrDate},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="intAccrued != null">
        #{intAccrued},
      </if>
      <if test="intAccruedCtd != null">
        #{intAccruedCtd},
      </if>
      <if test="intAccruedCalcCtd != null">
        #{intAccruedCalcCtd},
      </if>
      <if test="intAccruedDiff != null">
        #{intAccruedDiff},
      </if>
      <if test="taxAccrued != null">
        #{taxAccrued},
      </if>
      <if test="taxAccruedCtd != null">
        #{taxAccruedCtd},
      </if>
      <if test="taxAccruedCalcCtd != null">
        #{taxAccruedCalcCtd},
      </if>
      <if test="taxAccruedDiff != null">
        #{taxAccruedDiff},
      </if>
      <if test="taxType != null">
        #{taxType},
      </if>
      <if test="taxRate != null">
        #{taxRate},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="tranSource != null">
        #{tranSource},
      </if>
      <if test="accountingStatus != null">
        #{accountingStatus},
      </if>
      <if test="profitCenter != null">
        #{profitCenter},
      </if>
      <if test="sourceModule != null">
        #{sourceModule},
      </if>
      <if test="businessUnit != null">
        #{businessUnit},
      </if>
      <if test="agg != null">
        #{agg},
      </if>
      <if test="intAmt != null">
        #{intAmt},
      </if>
      <if test="monthBasis != null">
        #{monthBasis},
      </if>
      <if test="yearBasis != null">
        #{yearBasis},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="reversal != null">
        #{reversal},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="intCalcBal != null">
        #{intCalcBal},
      </if>
      <if test="glPosted != null">
        #{glPosted},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="othReference != null">
        #{othReference},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="acctSpreadRate != null">
        #{acctSpreadRate},
      </if>
      <if test="acctPercentRate != null">
        #{acctPercentRate},
      </if>
      <if test="acctFixedRate != null">
        #{acctFixedRate},
      </if>
      <if test="agreeChangeType != null">
        #{agreeChangeType},
      </if>
      <if test="agreeFixedRate != null">
        #{agreeFixedRate},
      </if>
      <if test="agreePercentRate != null">
        #{agreePercentRate},
      </if>
      <if test="agreeSpreadRate != null">
        #{agreeSpreadRate},
      </if>
      <if test="splitRateFlag != null">
        #{splitRateFlag},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="tdIntNumDays != null">
        #{tdIntNumDays},
      </if>
      <if test="tdLastAccrDate != null">
        #{tdLastAccrDate},
      </if>
    </trim>
  </insert>


  <insert id="insertMbAccrInfo" parameterType="java.util.List">
    <![CDATA[
    insert into MM_ACCR_INFO (
	SEQ_NO,
	INTERNAL_KEY,
	INT_CLASS,
	ACCR_DATE,
	BASE_ACCT_NO,
	PROD_TYPE,
	BRANCH,
	SOURCE_TYPE,
	CCY,
	INT_ACCRUED,
	INT_ACCRUED_CTD,
	CLIENT_NO,
	CLIENT_TYPE,
	TRAN_SOURCE,
	ACCOUNTING_STATUS,
	PROFIT_CENTRE,
	SOURCE_MODULE,
	BUSINESS_UNIT,
	MONTH_BASIS,
	YEAR_BASIS,
	REVERSAL,
	ACCT_SEQ_NO,
	ACTUAL_RATE,
	FLOAT_RATE,
	REAL_RATE,
	ACCT_SPREAD_RATE,
	ACCT_PERCENT_RATE,
	ACCT_FIXED_RATE,
	SPLIT_RATE_FLAG,
	GL_POSTED,
	REFERENCE,
	SYSTEM_ID
    )
    values(
	#{seqNo},
	#{internalKey},
	#{intClass},
	#{accrDate},
	#{baseAcctNo},
	#{prodType},
	#{branch},
	#{sourceType},
	#{ccy},
	#{intAccrued},
	#{intAccruedCtd},
	#{clientNo},
	#{clientType},
	#{tranSource},
	#{accountingStatus},
	#{profitCenter},
	#{sourceModule},
	#{businessUnit},
	#{monthBasis},
	#{yearBasis},
	#{reversal},
	#{acctSeqNo},
	#{actualRate},
	#{floatRate},
	#{realRate},
	#{acctSpreadRate},
	#{acctPercentRate},
	#{acctFixedRate},
	#{splitRateFlag},
	#{glPosted},
	#{reference},
	#{systemId})
        ]]>
  </insert>


<!--  <select id="getMbAccrInfoForPost" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"  resultMap= "IrlAccrInfoMainBean">-->
<!--    select *-->
<!--    from MM_ACCR_INFO-->
<!--    where (ACCR_DATE BETWEEN #{lastRunDate} AND #{yesterday})-->
<!--    and GL_POSTED  = 'N'-->
<!--    ORDER BY internal_key-->
<!--  </select>-->

  <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAccrInfo">
    update MM_ACCR_INFO
      set GL_POSTED = 'Y'
    where SEQ_NO = #{seqNo}
  </update>


  <resultMap id="IrlAccrInfoMainBean" type="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmAccrInfo">
    <result column="ACCR_DATE" property="accrDate" jdbcType="DATE" javaType="String" />
  </resultMap>
  <resultMap id="IrlAccrInfoMainMap" type="java.util.Map">
    <result column="ACCR_DATE" property="accrDate" jdbcType="DATE" javaType="String" />
  </resultMap>

</mapper>
