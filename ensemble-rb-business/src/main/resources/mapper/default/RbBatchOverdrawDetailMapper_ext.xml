<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOverdrawDetail">
	<select id="selectOverdrawDetailForUpdate"
			parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOverdrawDetail"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOverdrawDetail">
		SELECT <include refid="Base_Column"/>
		FROM RB_BATCH_OVERDRAW_DETAIL
		WHERE BATCH_STATUS='P' AND SEQ_NO= #{seqNo} FOR UPDATE
	</select>
</mapper>