<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackage">

  <select id="selectByClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackage">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_PACKAGE
    where
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <![CDATA[ CLIENT_NO = #{clientNo} and EFFECT_DATE <= #{runDate} and END_DATE > #{runDate} order by AGREEMENT_ID+0
    ]]>
  </select>
<!--  <select id="selectNextDealDateMature" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackage">-->
<!--    select <include refid="Base_Column"/>-->
<!--    <![CDATA[-->
<!--      from RB_AGREEMENT_PACKAGE-->
<!--      where EFFECT_DATE <= #{baseParam.runDate}-->
<!--      and END_DATE > #{baseParam.runDate}-->
<!--      and NEXT_DEAL_DATE > #{baseParam.lastRunDate}-->
<!--      order by AGREEMENT_ID+0-->
<!--    ]]>-->
<!--  </select>-->
  <select id="selectNextDealDateMatureSplit" parameterType="java.util.Map"  resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      <![CDATA[
            SELECT
            MIN(AGREEMENT_ID) START_KEY,
            MAX(AGREEMENT_ID) END_KEY,COUNT(1) ROW_COUNT
            FROM
            (
            SELECT
            DISTINCT AGREEMENT_ID,
            @rownum :=@rownum + 1 AS rownum
            FROM
            RB_AGREEMENT_PACKAGE,
            (SELECT @rownum := -1) t
            where EFFECT_DATE <= #{runDate}
            and END_DATE > #{runDate}
            and NEXT_DEAL_DATE > #{lastRunDate}
            ORDER BY AGREEMENT_ID
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
             ]]>
    </if>
    <if test="_databaseId == 'oracle'">
            SELECT MIN (AGREEMENT_ID) START_KEY, MAX (AGREEMENT_ID) END_KEY,COUNT(1) ROW_COUNT
            FROM (SELECT DISTINCT AGREEMENT_ID
            FROM RB_AGREEMENT_PACKAGE
            where
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
              AND COMPANY = #{company}
            </if>
            <![CDATA[EFFECT_DATE <= #{runDate}
            and END_DATE > #{runDate}
            and NEXT_DEAL_DATE > #{lastRunDate}
            ORDER BY AGREEMENT_ID)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
               ]]>
    </if>
    <!--<![CDATA[
     select COUNT(1) ROW_COUNT
      from RB_AGREEMENT_PACKAGE
      where EFFECT_DATE <= #{runDate}
      and END_DATE > #{runDate}
      and NEXT_DEAL_DATE > #{lastRunDate}
      
    ]]>-->
  </select>
<!--  <select id="selectNextChargeDateMature" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackage">-->
<!--    select <include refid="Base_Column"/>-->
<!--    <![CDATA[-->
<!--      from RB_AGREEMENT_PACKAGE-->
<!--      where FEE_CHARGE_TYPE = 'P'-->
<!--      and EFFECT_DATE <= #{baseParam.runDate}-->
<!--      and END_DATE > #{baseParam.runDate}-->
<!--      and NEXT_CHARGE_DATE > #{baseParam.lastRunDate}-->
<!--      order by AGREEMENT_ID+0-->
<!--    ]]>-->
<!--  </select>-->
  <select id="getArrearAgreementFeePackage" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackage">
    select
    <include refid="Base_Column"/>
        from RB_AGREEMENT_PACKAGE
        where
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        <![CDATA[ AGREEMENT_ID = #{agreementId}
        and AGREEMENT_TYPE = 'PKG'
        and AGREEMENT_STATUS !='E'
        and START_DATE <= #{runDate}
        and END_DATE > #{runDate}  order by AGREEMENT_ID+0
        ]]>
        <if test="clientNo != null and clientNo != ''">
			AND CLIENT_NO = #{clientNo}
		</if>

  </select>
  <select id="selectFeePackageByPage" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.model.fee.MbFeePackageQueryModel">
    select pkg.PACKAGE_ID, pkg.AGREEMENT_ID, pkg.CLIENT_NO, doc.DOCUMENT_TYPE, doc.DOCUMENT_ID, pkg.CHARGE_TO_BASE_ACCT_NO, a.AGREEMENT_STATUS,
    pkg.AVAIL_NUM, pkg.AVAIL_AMT, pkg.EFFECT_DATE, a.END_DATE, pkg.AVAIL_AMT, pkg.AVAIL_NUM
    from mb_agreement_package pkg, mb_agreement a, cif_client_document doc
    where pkg.agreement_id = a.agreement_id and doc.client_no = pkg.client_no
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND pkg.COMPANY = #{company}
    </if>
    <if test="packageId != null">
      and pkg.package_id = #{packageId}
    </if>
    <if test="clientNo != null">
      and pkg.client_no = #{clientNo}
    </if>
    <if test="documentType != null">
      and doc.document_type = #{documentType}
    </if>
    <if test="documentId != null">
      and doc.document_id = #{documentId}
    </if>
    <if test="issCountry != null">
      and doc.iss_country = #{issCountry}
    </if>
    <if test="startDate != null">
      and a.agreement_open_date <![CDATA[ >= ]]> #{startDate}
    </if>
    <if test="endDate != null">
      and a.agreement_open_date <![CDATA[ <= ]]> #{endDate}
    </if>
    <if test="agreementStatus != null">
      and a.agreement_status = #{agreementStatus}
    </if>
    order by agreement_open_date desc, package_id desc
  </select>
</mapper>
