<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcCustomerLimit">
    <select id="selectOneStageLimit" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcCustomerLimit">
        select <include refid="Base_Column"/>
        from RB_DC_CUSTOMER_LIMIT
        where CUSTOMER_BASE = #{customerBase}
        AND MULT_STAGE_FLAG = #{multStageFlag}
        AND STAGE_CODE = #{stageCode}
    </select>
    <select id="selectMoreStageLimit" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcCustomerLimit">
        select <include refid="Base_Column"/>
        from RB_DC_CUSTOMER_LIMIT
        where CUSTOMER_BASE = #{customerBase}
        AND MULT_STAGE_FLAG = #{multStageFlag}
    </select>
    <update id="updateRbDcCustomerLimit" parameterType="java.util.Map">
        update RB_DC_CUSTOMER_LIMIT
        <set>
            <if test="multStageFlag != null and  multStageFlag != '' ">
                mult_stage_flag = #{multStageFlag},
            </if>
            <if test="customerLimit != null ">
                customer_limit = #{customerLimit},
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="company != null and  company != '' ">
                COMPANY = #{company}
            </if>
        </set>
        <where>
            <if test="customerBase != null and customerBase != ''">
                AND CUSTOMER_BASE = #{customerBase}
            </if>
            <if test="stageCode != null and stageCode != ''">
                AND STAGE_CODE = #{stageCode}
            </if>
            <if test="multStageFlag != null and multStageFlag != ''">
                AND MULT_STAGE_FLAG = #{multStageFlag}
            </if>
        </where>
    </update>

    <select id="selectStageLimitList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcCustomerLimit">
        select <include refid="Base_Column"/>
        from RB_DC_CUSTOMER_LIMIT
        <where>
            <if test="stageCode != null and stageCode != ''">
                AND STAGE_CODE = #{stageCode}
            </if>
            <if test="multStageFlag != null and multStageFlag != ''">
                AND MULT_STAGE_FLAG = #{multStageFlag}
            </if>
        </where>
    </select>

    <delete id="delStageLimitByStageCodeMultFlag" parameterType="java.util.Map">
        delete from RB_DC_CUSTOMER_LIMIT
        where STAGE_CODE = #{stageCode} and mult_stage_flag = #{multStageFlag}
    </delete>
</mapper>
