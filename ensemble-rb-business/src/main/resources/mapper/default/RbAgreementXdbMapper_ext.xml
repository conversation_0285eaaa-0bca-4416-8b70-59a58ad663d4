<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementXdb">







	<select id="selectAgrByType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementXdb">
		select *
		from RB_AGREEMENT_XDB
		where AGREEMENT_STATUS='A'
		<if test="baseAcctNo != null and baseAcctNo != ''">
			and BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="prodType != null and prodType != ''">
			and PROD_TYPE = #{prodType}
		</if>
		<if test="acctCcy != null and acctCcy != ''">
			and ACCT_CCY = #{acctCcy}
		</if>
		<if test="acctSeqNo != null and acctSeqNo != ''">
			and ACCT_SEQ_NO = #{acctSeqNo}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>

<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
