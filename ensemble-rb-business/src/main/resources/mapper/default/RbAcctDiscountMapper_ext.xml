<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.repository.agr.RbAcctDiscountDao">
  <!-- Created by daiduan on 2016/05/10 17:15:36. -->
<sql id="Base_Column_List">
    ADD_DAYS,
    BASE_ACCT_NO,
    BILL_AMT,
    BILL_NO,
    BOOK_BRANCH,
    CCY,
    CLIENT_NAME,
    CLIENT_NO,
    DISCOUNT_DATE,
    DISC_BASE_RATE,
    DISC_STATUS,
    DRAFT_MATURE_DATE,
    INTERNAL_KEY,
    INT_RATE,
    ISSUE_ACCT_NO,
    ISSUE_CLIENT_NAME,
    ISSUE_DATE,
    LAST_CHANGE_DATE,
    PAYER_ACCT_NO,
    PAYER_BANK,
    PAYER_NAME,
    PAY_BRANCH,
    PAY_BRANCH_NAME,
    SELL_INT,
    SELL_NOT,
    SELL_OWN_DRAFT,
    TRAN_BRANCH,
    COMPANY,
    TRAN_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    select <include refid="Base_Column_List"/>
    from RB_ACCT_DISCOUNT
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>



  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    delete from RB_ACCT_DISCOUNT
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    update RB_ACCT_DISCOUNT
    <set>
      <if test="billNo != null">
        BILL_NO = #{billNo},
      </if>
      <if test="payBranch != null">
        PAY_BRANCH = #{payBranch},
      </if>
      <if test="payBranchName != null">
        PAY_BRANCH_NAME = #{payBranchName},
      </if>
      <if test="draftMatureDate != null">
        DRAFT_MATURE_DATE = #{draftMatureDate},
      </if>
      <if test="issueClientName != null">
        ISSUE_CLIENT_NAME = #{issueClientName},
      </if>
      <if test="issueAcctNo != null">
        ISSUE_ACCT_NO = #{issueAcctNo},
      </if>
      <if test="issueDate != null">
        ISSUE_DATE = #{issueDate},
      </if>
      <if test="payerName != null">
        PAYER_NAME = #{payerName},
      </if>
      <if test="payerAcctNo != null">
        PAYER_ACCT_NO = #{payerAcctNo},
      </if>
      <if test="payerBank != null">
        PAYER_BANK = #{payerBank},
      </if>
      <if test="discBaseRate != null">
        DISC_BASE_RATE = #{discBaseRate},
      </if>
      <if test="discStatus != null">
        DISC_STATUS = #{discStatus},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="addDays != null">
        ADD_DAYS = #{addDays},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="discountDate != null">
        DISCOUNT_DATE = #{discountDate},
      </if>
      <if test="billAmt != null">
        BILL_AMT = #{billAmt},
      </if>
      <if test="intRate != null">
        INT_RATE = #{intRate},
      </if>
      <if test="bookBranch != null">
        BOOK_BRANCH = #{bookBranch},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientName != null">
        CLIENT_NAME = #{clientName},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="sellNot != null">
        SELL_NOT = #{sellNot},
      </if>
      <if test="sellInt != null">
        SELL_INT = #{sellInt},
      </if>
      <if test="sellOwnDraft != null">
        SELL_OWN_DRAFT = #{sellOwnDraft},
      </if>
        <if test="company != null">
            COMPANY = #{company},
        </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    insert into RB_ACCT_DISCOUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="billNo != null">
        BILL_NO,
      </if>
      <if test="payBranch != null">
        PAY_BRANCH,
      </if>
      <if test="payBranchName != null">
        PAY_BRANCH_NAME,
      </if>
      <if test="draftMatureDate != null">
        DRAFT_MATURE_DATE,
      </if>
      <if test="issueClientName != null">
        ISSUE_CLIENT_NAME,
      </if>
      <if test="issueAcctNo != null">
        ISSUE_ACCT_NO,
      </if>
      <if test="issueDate != null">
        ISSUE_DATE,
      </if>
      <if test="payerName != null">
        PAYER_NAME,
      </if>
      <if test="payerAcctNo != null">
        PAYER_ACCT_NO,
      </if>
      <if test="payerBank != null">
        PAYER_BANK,
      </if>
      <if test="discBaseRate != null">
        DISC_BASE_RATE,
      </if>
      <if test="discStatus != null">
        DISC_STATUS,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="addDays != null">
        ADD_DAYS,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="discountDate != null">
        DISCOUNT_DATE,
      </if>
      <if test="billAmt != null">
        BILL_AMT,
      </if>
      <if test="intRate != null">
        INT_RATE,
      </if>
      <if test="bookBranch != null">
        BOOK_BRANCH,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="sellNot != null">
        SELL_NOT,
      </if>
      <if test="sellInt != null">
        SELL_INT,
      </if>
      <if test="sellOwnDraft != null">
        SELL_OWN_DRAFT,
      </if>
        <if test="company != null">
            COMPANY,
        </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="billNo != null">
        #{billNo},
      </if>
      <if test="payBranch != null">
        #{payBranch},
      </if>
      <if test="payBranchName != null">
        #{payBranchName},
      </if>
      <if test="draftMatureDate != null">
        #{draftMatureDate},
      </if>
      <if test="issueClientName != null">
        #{issueClientName},
      </if>
      <if test="issueAcctNo != null">
        #{issueAcctNo},
      </if>
      <if test="issueDate != null">
        #{issueDate},
      </if>
      <if test="payerName != null">
        #{payerName},
      </if>
      <if test="payerAcctNo != null">
        #{payerAcctNo},
      </if>
      <if test="payerBank != null">
        #{payerBank},
      </if>
      <if test="discBaseRate != null">
        #{discBaseRate},
      </if>
      <if test="discStatus != null">
        #{discStatus},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="addDays != null">
        #{addDays},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="discountDate != null">
        #{discountDate},
      </if>
      <if test="billAmt != null">
        #{billAmt},
      </if>
      <if test="intRate != null">
        #{intRate},
      </if>
      <if test="bookBranch != null">
        #{bookBranch},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="sellNot != null">
        #{sellNot},
      </if>
      <if test="sellInt != null">
        #{sellInt},
      </if>
      <if test="sellOwnDraft != null">
        #{sellOwnDraft},
      </if>
        <if test="company != null">
            #{company},
        </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>

  <select id="getDiscByPayBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    select <include refid="Base_Column_List"/>
    from RB_ACCT_DISCOUNT
    <where>
      <if test="billNo != null and billNo.length() > 0">
        bill_No = #{billNo}
      </if>
      <if test="payBranch != null and payBranch.length() > 0">
        and pay_branch = #{payBranch}
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <select id="getDiscByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    select <include refid="Base_Column_List"/>
    from RB_ACCT_DISCOUNT
    where internal_key = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbDiscount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    select <include refid="Base_Column_List"/>
    from RB_ACCT_DISCOUNT
    <where>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      <if test="baseAcctNo != null and baseAcctNo.length() > 0">
        and base_acct_no = #{baseAcctNo}
      </if>
      <if test="clientNo != null and clientNo.length() > 0">
        and client_no = #{clientNo}
      </if>
      <if test="billNo != null and billNo.length() > 0">
        and bill_No = #{billNo}
      </if>
      <if test="payBranch != null and payBranch.length() > 0">
        and pay_branch = #{payBranch}
      </if>
      <if test="internalKeys != null">
        AND internal_key IN
        <foreach item="item" index="index" collection="internalKeys" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY RB_ACCT_DISCOUNT.TRAN_TIMESTAMP+0 ASC
  </select>

  <select id="getMbDisCountByBillNo" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount">
    select <include refid="Base_Column_List"/>
    from RB_ACCT_DISCOUNT
    WHERE BILL_NO = #{billNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
