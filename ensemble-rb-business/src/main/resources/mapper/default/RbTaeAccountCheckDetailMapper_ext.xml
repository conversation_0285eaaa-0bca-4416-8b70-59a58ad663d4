<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTaeAccountCheckDetail">
    <select id="getDetailBySessionIdAndFlag" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTaeAccountCheckDetail">
        select
        *
        from RB_TAE_ACCOUNT_CHECK_DETAIL
        <where>
            <if test="sessionId != null and sessionId !=''">
                SESSION_ID = #{sessionId}
            </if>
            <if test="flag != null">
                AND DR_CR_FLAG= #{flag}
            </if>
        </where>
    </select>
</mapper>
