<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist">
    <!-- Created by <PERSON><PERSON><PERSON><PERSON> on 2016/07/12 17:40:29. -->
    <resultMap id="MbGlHistBean" type="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist">
        <result column="TRAN_DATE" property="tranDate" jdbcType="DATE" javaType="String"/>
    </resultMap>
    <resultMap id="MbGlHistMap" type="java.util.Map">
        <result column="TRAN_DATE" property="tranDate" jdbcType="DATE" javaType="String"/>
    </resultMap>
    <sql id="comet_step_whereExt">
        <if test="cometStart != null and cometStart.length() > 0 and cometEnd != null and cometEnd.length() > 0">and
            ${cometKeyField} between #{cometStart} and #{cometEnd}
        </if>
    </sql>

    <delete id="deleteAll" parameterType="java.util.Map">

        delete from rb_gl_hist
        where <![CDATA[ tran_date < #{lastRunDate,jdbcType=DATE}]]>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <select id="backupMbGlHist" parameterType="java.util.Map">

        insert into rb_gl_hist_backup select * from rb_gl_hist where
        <![CDATA[tran_date < #{lastRunDate,jdbcType=DATE}]]>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectMbGlHist" parameterType="java.util.Map" resultMap="MbGlHistBean">
        select
        <include refid="Base_Column"/>
        from rb_gl_hist
        where tran_date BETWEEN #{lastRunDate,jdbcType=DATE} and #{yesterday,jdbcType=DATE}
        AND (gl_posted_flag = 'N' or gl_posted_flag is null)
        AND gl_seq_no BETWEEN #{startKey} and #{endKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY gl_seq_no
    </select>

    <select id="selectListWithNoPosted" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <include refid="Base_Where"/>
            AND (gl_posted_flag = 'N' or gl_posted_flag is null)
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>

    <update id="updateBatch" parameterType="java.util.Map">
        update rb_gl_hist set gl_posted_flag='Y' where gl_SEQ_NO = #{glSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="getTranSumAmtInfo" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">
        select
        count(1) int_amt,sum(amount) amount
        from RB_GL_HIST
        where tran_date <![CDATA[ < ]]> #{runDate,jdbcType=DATE}
        AND in_status ='O'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <include refid="comet_step_where"/>
    </select>

    <select id="getTranSumAmtInfoBackInp" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">
        select
        count(1) int_amt,sum(amount) amount
        from RB_GL_HIST_BACKUP
        where tran_date <![CDATA[ < ]]> #{runDate,jdbcType=DATE}
        AND tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
        AND in_status ='O'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <include refid="comet_step_where"/>
    </select>

    <select id="getTranSumAmtInfoEod" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">
        select
        count(1) int_amt,sum(amount) amount
        from RB_GL_HIST
        where tran_date <![CDATA[ < ]]> #{runDate,jdbcType=DATE}
        AND in_status ='B'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <include refid="comet_step_where"/>
    </select>

    <select id="getTranSumAmtInfoBackEod" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">
        select
        count(1) int_amt,sum(amount) amount
        from RB_GL_HIST_BACKUP
        where tran_date <![CDATA[ < ]]> #{runDate,jdbcType=DATE}
        AND tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
        AND in_status ='B'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <include refid="comet_step_where"/>
    </select>
    <select id="getTranSumAmtInfos" parameterType="java.util.Map" resultMap="MbGlHistBean" useCache="false">
        select
        <include refid="Base_Column"/>
        from RB_GL_HIST
        where tran_date <![CDATA[ < ]]> #{runDate,jdbcType=DATE}
        AND gl_posted_flag = 'Y'
        AND in_status !='S'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <include refid="comet_step_where"/>
    </select>

    <insert id="insertEod">
        insert into
        RB_GL_HIST_BACKUP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reversalDate != null ">
                REVERSAL_DATE,
            </if>
            <if test="odpAmt != null ">
                ODP_AMT,
            </if>
            <if test="acct_branck != null and  acct_branck != '' ">
                ACCT_BRANCH,
            </if>
            <if test="clientType != null and  clientType != '' ">
                CLIENT_TYPE,
            </if>
            <if test="systemId != null and  systemId != '' ">
                SYSTEM_ID,
            </if>
            <if test="odiAmt != null ">
                ODI_AMT,
            </if>
            <if test="glCode != null and  glCode != '' ">
                GL_CODE,
            </if>
            <if test="reserved1 != null and  reserved1 != '' ">
                RESERVED1,
            </if>
            <if test="reference != null and  reference != '' ">
                REFERENCE,
            </if>
            <if test="ccy != null and  ccy != '' ">
                CCY,
            </if>
            <if test="tranDate != null ">
                TRAN_DATE,
            </if>
            <if test="company != null and  company != '' ">
                COMPANY,
            </if>
            <if test="inStatus != null and  inStatus != '' ">
                IN_STATUS,
            </if>
            <if test="glSeqNo != null and  glSeqNo != '' ">
                GL_SEQ_NO,
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                SOURCE_TYPE,
            </if>
            <if test="marketingProd != null and  marketingProd != '' ">
                MARKETING_PROD,
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                SOURCE_MODULE,
            </if>
            <if test="amount != null ">
                AMOUNT,
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                BASE_ACCT_NO,
            </if>
            <if test="acct_ccy != null and  acct_ccy != '' ">
                ACCT_CCY,
            </if>
            <if test="reversalFlag != null and  reversalFlag != '' ">
                REVERSAL_FLAG,
            </if>
            <if test="intAmt != null ">
                INT_AMT,
            </if>
            <if test="tranProfitCenter != null and  tranProfitCenter != '' ">
                TRAN_PROFIT_CENTRE,
            </if>
            <if test="eventType != null and  eventType != '' ">
                EVENT_TYPE,
            </if>
            <if test="reversalSeqNo != null and  reversalSeqNo != '' ">
                REVERSAL_SEQ_NO,
            </if>
            <if test="spreadPercent != null ">
                SPREAD_PERCENT,
            </if>
            <if test="marketingProdDesc != null and  marketingProdDesc != '' ">
                MARKETING_PROD_DESC,
            </if>
            <if test="businessUnit != null and  businessUnit != '' ">
                BUSINESS_UNIT,
            </if>
            <if test="prodType != null and  prodType != '' ">
                PROD_TYPE,
            </if>
            <if test="narrative != null and  narrative != '' ">
                NARRATIVE,
            </if>
            <if test="crDrInd != null and  crDrInd != '' ">
                CR_DR_IND,
            </if>
            <if test="reserved2 != null and  reserved2 != '' ">
                RESERVED2,
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                TRAN_BRANCH,
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                PROFIT_CENTRE,
            </if>
            <if test="priAmt != null ">
                PRI_AMT,
            </if>
            <if test="bankSeqNo != null and  bankSeqNo != '' ">
                BANK_SEQ_NO,
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE,
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                CLIENT_NO,
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                CHANNEL_SEQ_NO,
            </if>
            <if test="tranCategory != null and  tranCategory != '' ">
                TRAN_CATEGORY,
            </if>
            <if test="amtType != null and  amtType != '' ">
                AMT_TYPE,
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                ACCOUNTING_STATUS,
            </if>
            <if test="tranType != null and  tranType != '' ">
                TRAN_TYPE,
            </if>
            <if test="channelDate != null ">
                CHANNEL_DATE,
            </if>
            <if test="taxAmt != null ">
                TAX_AMT,
            </if>
            <if test="glPosted != null and  glPosted != '' ">
                gl_posted_flag,
            </if>
            <if test="subSeqNo != null and  subSeqNo != '' ">
                SUB_SEQ_NO,
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP,
            </if>
            <if test="acctSeqNo != null ">
                ACCT_SEQ_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reversalDate != null ">
                #{reversalDate},
            </if>
            <if test="odpAmt != null ">
                #{odpAmt},
            </if>
            <if test="acct_branch != null and  acct_branch != '' ">
                #{acct_branch},
            </if>
            <if test="clientType != null and  clientType != '' ">
                #{clientType},
            </if>
            <if test="systemId != null and  systemId != '' ">
                #{systemId},
            </if>
            <if test="odiAmt != null ">
                #{odiAmt},
            </if>
            <if test="glCode != null and  glCode != '' ">
                #{glCode},
            </if>
            <if test="reserved1 != null and  reserved1 != '' ">
                #{reserved1},
            </if>
            <if test="reference != null and  reference != '' ">
                #{reference},
            </if>
            <if test="ccy != null and  ccy != '' ">
                #{ccy},
            </if>
            <if test="tranDate != null ">
                #{tranDate},
            </if>
            <if test="company != null and  company != '' ">
                #{company},
            </if>
            <if test="inStatus != null and  inStatus != '' ">
                #{inStatus},
            </if>
            <if test="glSeqNo != null and  glSeqNo != '' ">
                #{glSeqNo},
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                #{sourceType},
            </if>
            <if test="marketingProd != null and  marketingProd != '' ">
                #{marketingProd},
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                #{sourceModule},
            </if>
            <if test="amount != null ">
                #{amount},
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                #{baseAcctNo},
            </if>
            <if test="acct_ccy != null and  acct_ccy != '' ">
                #{acct_ccy},
            </if>
            <if test="reversalFlag != null and  reversalFlag != '' ">
                #{reversalFlag},
            </if>
            <if test="intAmt != null ">
                #{intAmt},
            </if>
            <if test="tranProfitCenter != null and  tranProfitCenter != '' ">
                #{tranProfitCenter},
            </if>
            <if test="eventType != null and  eventType != '' ">
                #{eventType},
            </if>
            <if test="reversalSeqNo != null and  reversalSeqNo != '' ">
                #{reversalSeqNo},
            </if>
            <if test="spreadPercent != null ">
                #{spreadPercent},
            </if>
            <if test="marketingProdDesc != null and  marketingProdDesc != '' ">
                #{marketingProdDesc},
            </if>
            <if test="businessUnit != null and  businessUnit != '' ">
                #{businessUnit},
            </if>
            <if test="prodType != null and  prodType != '' ">
                #{prodType},
            </if>
            <if test="narrative != null and  narrative != '' ">
                #{narrative},
            </if>
            <if test="crDrInd != null and  crDrInd != '' ">
                #{crDrInd},
            </if>
            <if test="reserved2 != null and  reserved2 != '' ">
                #{reserved2},
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                #{tranBranch},
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                #{profitCenter},
            </if>
            <if test="priAmt != null ">
                #{priAmt},
            </if>
            <if test="bankSeqNo != null and  bankSeqNo != '' ">
                #{bankSeqNo},
            </if>
            <if test="effectDate != null ">
                #{effectDate},
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                #{clientNo},
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                #{channelSeqNo},
            </if>
            <if test="tranCategory != null and  tranCategory != '' ">
                #{tranCategory},
            </if>
            <if test="amtType != null and  amtType != '' ">
                #{amtType},
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                #{accountingStatus},
            </if>
            <if test="tranType != null and  tranType != '' ">
                #{tranType},
            </if>
            <if test="channelDate != null ">
                #{channelDate},
            </if>
            <if test="taxAmt != null ">
                #{taxAmt},
            </if>
            <if test="glPosted != null and  glPosted != '' ">
                #{glPosted},
            </if>
            <if test="subSeqNo != null and  subSeqNo != '' ">
                #{subSeqNo},
            </if>
            <if test="tranTimestamp != null ">
                #{tranTimestamp},
            </if>
            <if test="acctSeqNo != null ">
                #{acctSeqNo},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist">
        delete from RB_GL_HIST
        where GL_SEQ_NO = #{glSeqNo}
        AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>


    <update id="updateGlHistByReference">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="glPostedFlag != null and  glPostedFlag != '' ">
                GL_POSTED_FLAG = #{glPostedFlag},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="glSeqNo != null and  glSeqNo != '' ">
                    GL_SEQ_NO = #{glSeqNo} AND
                </if>
                <if test="reference != null and reference != '' ">
                    REFERENCE = #{reference}
                    AND
                </if>
                <if test="clientNo != null and clientNo != '' ">
                    CLIENT_NO = #{clientNo}
                    AND
                </if>
                <if test="reversalFlag != null and reversalFlag != '' ">
                    REVERSAL_FLAG = #{reversalFlag}
                    AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>
    <update id="updateClientNoByForKeys" parameterType="java.util.Map">
        update RB_GL_HIST
        <set>
            CLIENT_NO = #{newClientNo}
        </set>
        where CLIENT_NO = #{oldClientNo}
        and BASE_ACCT_NO = #{baseAcctNo}
        and ACCT_SEQ_NO = #{acctSeqNo}
        and CCY = #{ccy}
        and PROD_TYPE = #{prodType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <update id="updateEventTypeForGlSeqNo" parameterType="java.util.Map">
        update RB_GL_HIST
        <set>
            EVENT_TYPE = #{eventType},
            narrative = #{narrative}
        </set>
        where GL_SEQ_NO = #{glSeqNo}
        AND CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="selectRbGlHistByPrimary" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_GL_HIST
        <where>
            <if test="glSeqNo != null and  glSeqNo != '' ">
                AND GL_SEQ_NO = #{glSeqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <!--更新rb_gl_hist过账标记-->
    <update id="updateRbGLHistBySeqNo" parameterType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            update
            RB_GL_HIST
            SET GL_POSTED_FLAG='Y'
            WHERE ifnull(GL_POSTED_FLAG,'N')='N'
            and GL_SEQ_NO IN
            <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="_databaseId == 'oracle'">
            update
            RB_GL_HIST
            SET GL_POSTED_FLAG='Y'
            WHERE nvl(GL_POSTED_FLAG,'N')='N'
            and GL_SEQ_NO IN
            <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateDealFlagByGlSeqNos" parameterType="java.util.Map">
        update RB_GL_HIST
        SET deal_flag = #{dealFlag}
        WHERE internal_key IN
        <foreach collection="internalKeys" index="index" item="internalKey" open="(" close=")" separator=",">
            #{internalKey}
        </foreach>
    </update>

    <!--更新rb_gl_hist过账标记-->
    <update id="updateGlPostedYByGlSeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist">
        <if test="_databaseId == 'mysql'">
            update RB_GL_HIST
            SET GL_POSTED_FLAG='Y'
            WHERE ifnull(GL_POSTED_FLAG,'N')='N'
            and GL_SEQ_NO = #{glSeqNo}
        </if>
        <if test="_databaseId == 'oracle'">
            update RB_GL_HIST
            SET GL_POSTED_FLAG='Y'
            WHERE nvl(GL_POSTED_FLAG,'N')='N'
            and GL_SEQ_NO = #{glSeqNo}
        </if>
    </update>

    <delete id="deleteByReference" parameterType="java.util.Map">
        DELETE FROM RB_GL_HIST
        where REFERENCE = #{reference}
        and TRAN_DATE = #{tranDate}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
</mapper>
