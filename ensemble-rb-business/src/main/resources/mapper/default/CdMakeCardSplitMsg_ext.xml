<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardSplitMsg">

	<select id="getCdMakeCardSplitMsgByNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardSplitMsg">

		select <include refid="Base_Column"/>
		from Cd_Make_Card_Split_Msg
		where APPLY_NO = #{applyNo}
		and SUBSTR(START_NO, 1, 18) <![CDATA[<=]]> #{startNo}
		and SUBSTR(END_NO, 1, 18) <![CDATA[>=]]> #{endNo}
		and (CARD_VOUCHER_STATUS != '2' or CARD_VOUCHER_STATUS is null)
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>


	<update id="updateCdMakeCardSplitMsgByNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardSplitMsg">
		update Cd_Make_Card_Split_Msg
		<set>
			<if test="startNoReal != null and startNoReal !=''">
				START_NO = #{startNoReal},
			</if>
			<if test="endNoReal != null and endNoReal !=''">
				END_NO = #{endNoReal},
			</if>
			<if test="cardNum != null and cardNum !=''">
				Card_Num = #{cardNum},
			</if>
			CARD_VOUCHER_STATUS = '2'
		</set>
		where
		APPLY_NO = #{applyNo}
		and SUBSTR(START_NO, 1, 18) = #{startNo}
		and SUBSTR(END_NO, 1, 18) = #{endNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<select id="getCdMakeCardSplitMsgMaxSeqNo" parameterType="java.util.Map" resultType="java.util.Map">

		select max(SEQ_NO) MAX_SEQ_NO
		from Cd_Make_Card_Split_Msg
		where APPLY_NO = #{applyNo}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getCdMakeCardSplitMsgForSize" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardSplitMsg">
		select <include refid="Base_Column"/>
		from Cd_Make_Card_Split_Msg
		where APPLY_NO = #{applyNo}
		and (CARD_VOUCHER_STATUS != '2' or CARD_VOUCHER_STATUS is null)
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getCardMsgByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardSplitMsg">
		select <include refid="Base_Column"/>
		from Cd_Make_Card_Split_Msg
		where APPLY_NO = #{applyNo}
		and DOC_TYPE = #{docType}
		<![CDATA[
		and SUBSTR(START_NO, 0, 18) <= #{startNo}
		and SUBSTR(END_NO, 0, 18) >= #{endNo}
		]]>

	</select>
</mapper>