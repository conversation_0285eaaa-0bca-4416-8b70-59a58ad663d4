<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabInternalAcct">

	<select id="selectByClientNo"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabInternalAcct">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_BAB_INTERNAL_ACCT
		<where>
			<trim suffixOverrides="AND">
				<if test="babBranch != null and  babBranch != '' ">
					BAB_BRANCH = #{babBranch}  AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>
</mapper>
