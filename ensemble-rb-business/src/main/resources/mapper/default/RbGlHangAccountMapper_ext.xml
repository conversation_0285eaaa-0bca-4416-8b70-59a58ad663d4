<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
    <!-- Created by admin on 2017/07/21 11:34:08. -->
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        insert into RB_GL_HANG_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="hangSeqNo != null">
                HANG_SEQ_NO,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="hangStatus != null">
                HANG_STATUS,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="lastChangeTime != null">
                LAST_CHANGE_TIME,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="hangEndDate != null">
                HANG_END_DATE,
            </if>
            <if test="mwriteOffSeqNo != null">
                MWRITE_OFF_SEQ_NO,
            </if>
            <if test="hangTotalAmt != null">
                HANG_TOTAL_AMT,
            </if>
            <if test="hangBal != null">
                HANG_BAL,
            </if>
            <if test="crDrInd != null">
                CR_DR_IND,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="othBankFlag != null">
                OTH_BANK_FLAG,
            </if>
            <if test="othBaseAcctNo != null">
                OTH_BASE_ACCT_NO,
            </if>
            <if test="othAcctName != null">
                OTH_ACCT_NAME,
            </if>
            <if test="othBranch != null">
                OTH_BRANCH,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="authUserId != null">
                AUTH_USER_ID,
            </if>
            <if test="settleBaseAcctNo != null">
                SETTLE_BASE_ACCT_NO,
            </if>
            <if test="settleAcctName != null">
                SETTLE_ACCT_NAME,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="hangSeqNo != null">
                #{hangSeqNo},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="hangStatus != null">
                #{hangStatus},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate},
            </if>
            <if test="lastChangeTime != null">
                #{lastChangeTime},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="hangEndDate != null">
                #{hangEndDate},
            </if>
            <if test="mwriteOffSeqNo != null">
                #{mwriteOffSeqNo},
            </if>
            <if test="hangTotalAmt != null">
                #{hangTotalAmt},
            </if>
            <if test="hangBal != null">
                #{hangBal},
            </if>
            <if test="crDrInd != null">
                #{crDrInd},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="othBankFlag != null">
                #{othBankFlag},
            </if>
            <if test="othBaseAcctNo != null">
                #{othBaseAcctNo},
            </if>
            <if test="othAcctName != null">
                #{othAcctName},
            </if>
            <if test="othBranch != null">
                #{othBranch},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="authUserId != null">
                #{authUserId},
            </if>
            <if test="settleBaseAcctNo != null">
                #{settleBaseAcctNo},
            </if>
            <if test="settleAcctName != null">
                #{settleAcctName},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
    <select id="queryByCondition" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE
        hang_bal !=0
        and HANG_STATUS!='R'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND ma.base_acct_no = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo != ''">
            AND ma.sub_hang_seq_no = #{subHangSeqNo}
        </if>
        <if test="clientNo !=null and clientNo != ''">
            AND ma.CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
        order by HANG_END_DATE
    </select>

    <select id="queryByConditionForFeeQuery" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE hang_bal =0
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND ma.base_acct_no = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <if test="hangStatus != null and hangStatus != ''">
            and ma.HANG_STATUS =#{hangStatus}
        </if>
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>

    </select>

    <select id="queryByConditionForFeeQuery1" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE hang_bal =0
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND ma.base_acct_no = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <if test="hangStatus != null and hangStatus != ''">
            and ma.HANG_STATUS =#{hangStatus}
        </if>
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>

    </select>

    <select id="queryByMultiCondition" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE
        hang_bal !=0
        and HANG_STATUS!='R'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND ma.base_acct_no = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo != ''">
            AND ma.sub_hang_seq_no = #{subHangSeqNo}
        </if>
        <if test="clientName != null and clientName != ''">
            AND ma.client_name = #{clientName}
        </if>
        <if test="documentType != null and documentType != '' and documentID != null and documentID != ''">
            AND ma.document_type = #{documentType} AND ma.document_id = #{documentID}
        </if>
        <if test="pledgeBusiNo != null and pledgeBusiNo != ''">
            AND ma.pledge_busi_no = #{pledgeBusiNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>
    <!--add by taowh 用于查询不完全销账记录 -->
     <select id="inCompleteWriteOff" parameterType="java.util.Map"
             resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
         SELECT *
         FROM RB_GL_HANG_ACCOUNT ma
         WHERE ma.base_acct_no = #{baseAcctNo}
         and ma.HANG_STATUS !='W' and ma.HANG_STATUS != 'R'
         <if test="hangSeqNo != null and hangSeqNo != ''">
             AND ma.hang_seq_no = #{hangSeqNo}
         </if>
         <if test="clientNo != null and clientNo != ''">
             AND ma.client_no = #{clientNo}
         </if>
         <if test="company != null and company != '' ">
             AND ma.COMPANY = #{company}
         </if>
     </select>

    <select id="queryHangList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT  <include refid="Base_Column"/>
        FROM RB_GL_HANG_ACCOUNT b
        WHERE 1=1
        <if test="hangSeqNo != null and hangSeqNo != '' ">
            and b.HANG_SEQ_NO =#{hangSeqNo}
        </if>
        <if test="documentId != null and documentId != '' ">
            and b.DOCUMENT_ID =#{documentId}
        </if>
        <if test="documentType != null and documentType !='' ">
            and b.DOCUMENT_TYPE = #{documentType}
        </if>
        <if test="clientType != null and clientType != '' ">
            and b.CLIENT_TYPE =#{clientType}
        </if>
        <if test="startDate != null and endDate != null ">
            and b.TRAN_DATE BETWEEN #{startDate} and #{endDate}
        </if>
        <if test="baseAcctNo != null and baseAcctNo !='' ">
            and b.BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="tranAmt != null and tranAmt !='' ">
            and b.HANG_AMT = #{tranAmt}
        </if>
        <if test="pledgeBusiNo != null and pledgeBusiNo != ''">
            AND b.PLEDGE_BUSI_NO = #{pledgeBusiNo}
        </if>
        <if test="hangStatus != null and hangStatus != ''">
            AND b.HANG_STATUS IN (${hangStatus})
        </if>
        <if test="reference != null and reference != ''">
            AND b.REFERENCE = #{reference}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo != ''">
            AND b.SUB_HANG_SEQ_NO = #{subHangSeqNo}
        </if>
        <if test="ccy != null and ccy != ''">
            AND b.CCY = #{ccy}
        </if>
       <!-- and b.HANG_STATUS !='W'-->
        order by b.HANG_SEQ_NO, b.SUB_HANG_SEQ_NO
    </select>

    <select id="getMaxHangAccount" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT
        WHERE hang_seq_no = #{hangSeqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="hangStatus != null and hangStatus.length() > 0">
            AND HANG_STATUS = #{hangStatus}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by SUB_HANG_SEQ_NO desc
    </select>

    <select id="getMinHangAccount" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT
        WHERE hang_seq_no = #{hangSeqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="hangStatus != null and hangStatus.length() > 0">
            AND HANG_STATUS = #{hangStatus}
        </if>
        AND HANG_STATUS != 'R'
        AND HANG_BAL > 0
        order by SUB_HANG_SEQ_NO ASC
    </select>
    <select id="getMinHangEndDate" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT
        WHERE hang_seq_no = #{hangSeqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        AND HANG_STATUS != 'R'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by HANG_END_DATE
    </select>
    <select id="queryRbGlHangAccountRev" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT
        WHERE hang_seq_no = #{hangSeqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test='isReversal != null and isReversal == "Y"'>
            AND HANG_STATUS = 'R'
        </if>
        <if test='isReversal != null and isReversal == "N"'>
            AND HANG_STATUS != 'R'
        </if>
    </select>

    <select id="getCountHangAccount" parameterType="java.util.HashMap"
            resultType="Integer">
        SELECT COUNT (SUB_HANG_SEQ_NO)
        FROM RB_GL_HANG_ACCOUNT
        WHERE hang_seq_no = #{hangSeqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="hangStatus != null and hangStatus.length() > 0">
            AND HANG_STATUS = #{hangStatus}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="queryByConditionAll" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE ma.base_acct_no = #{baseAcctNo}
        and ma.HANG_STATUS !='R'
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo != ''">
            AND ma.sub_hang_seq_no = #{subHangSeqNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>

     <select id="queryUnFinishedByTranBranch" parameterType="java.util.Map"
             resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
         SELECT *
         FROM RB_GL_HANG_ACCOUNT ma
         WHERE ma.TRAN_BRANCH = #{tranBranch}
         and ma.HANG_STATUS in ('H','B')
         <if test="clientNo != null and clientNo != ''">
             AND ma.client_no = #{clientNo}
         </if>
         <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>

    <select id="queryByBranchAndStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE ma.hang_end_date = #{runDate}
        <if test="hangStatus != null and hangStatus != ''">
            AND ma.hang_status != #{hangStatus}
        </if>
        <if test="tranBranch != null and tranBranch != ''">
            AND ma.tran_branch = #{tranBranch}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>


    <select id="queryByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
    SELECT *
    FROM RB_GL_HANG_ACCOUNT
    WHERE reference = #{reference}
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </select>

    <select id="queryByHangStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT
        WHERE 1=1
        <if test="tranBranch != null">
            AND tran_branch = #{tranBranch}
        </if>
        <if test="runDate  != null">
            AND hang_end_date <![CDATA[ <=]]> #{runDate}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND hang_status IN('H','B')
    </select>

    <select id="getList" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT *
        FROM RB_GL_HANG_ACCOUNT
            <if test="hangSeqNoList !=null and hangSeqNoList.size > 0">
                <where>
                hang_seq_no in
                     <foreach collection="hangSeqNoList" item="hangSeqNo" separator="," open="(" close=")">
                         #{hangSeqNo}
                     </foreach>
                </where>
            </if>
    </select>

    <update id="updateHangAccount" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        update RB_GL_HANG_ACCOUNT
        <set>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="lastChangeTime != null">
                LAST_CHANGE_TIME = #{lastChangeTime},
            </if>
            <if test="hangStatus != null">
                HANG_STATUS = #{hangStatus},
            </if>
            <if test="hangBal != null">
                HANG_BAL = #{hangBal},
            </if>
        </set>
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="hangSeqNo != null">
            and HANG_SEQ_NO = #{hangSeqNo}
        </if>
        <if test="subHangSeqNo != null">
            and SUB_HANG_SEQ_NO = #{subHangSeqNo}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <update id="updateByRefNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        update RB_GL_HANG_ACCOUNT
        <set>
            <if test="hangBal != null">
                HANG_BAL = #{hangBal},
            </if>
            <if test="hangStatus != null">
                HANG_STATUS = #{hangStatus},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="lastChangeTime != null">
                LAST_CHANGE_TIME = #{lastChangeTime},
            </if>
        </set>
        where 1=1
        <if test="reference != null">
            and REFERENCE = #{reference}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <delete id="deleteByRefNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
    DELETE from RB_GL_HANG_ACCOUNT
    where REFERENCE =#{reference}
    and CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </delete>

    <select id="getGlHangAccountClose" parameterType="java.util.HashMap"
            resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column" />
        FROM
        <include refid="Table_Name" />
        WHERE 1=1
        <if test="hangStatusList != null ">
            AND HANG_STATUS IN
            <foreach collection="hangStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <include refid="comet_step_where2"/>
    </select>
    <select id="getGlHangAccountCloseTwo" parameterType="java.util.HashMap"
            resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column" />
        FROM
        <include refid="Table_Name" />
        WHERE 1=1
        <if test="hangStatus != null and hangStatus !=''">
            AND HANG_STATUS !=#{hangStatus}
        </if>
        <if test="tranDate != null ">
            AND HANG_END_DATE<![CDATA[<=]]> #{tranDate}
        </if>
        <if test="branch != null  and branch != '' ">and
            TRAN_BRANCH = #{branch}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <sql id="comet_step_where2">
        <if test="tranDate != null  ">
            AND HANG_END_DATE <![CDATA[>]]> #{tranDate}
        </if>
        <if test="branch != null  and branch != '' ">and
            TRAN_BRANCH = #{branch}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </sql>

    <select id="getmaxSubHangInfo" parameterType="java.util.HashMap"
            resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column" />
        FROM
        <include refid="Table_Name" />
        WHERE 1=1
        <if test="hangSeqNo != null and hangSeqNo !=''">
            AND HANG_STATUS =#{hangSeqNo}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo !=''">
            AND SUB_HANG_SEQ_NO =#{subHangSeqNo}
        </if>
        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO =#{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY SUB_HANG_SEQ_NO DESC
    </select>

    <select id="getRbGlHangAccountCount" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        <include refid="Base_Column" />
        FROM
        <include refid="Table_Name" />
        WHERE 1=1
        <if test="hangSeqNo != null and hangSeqNo != null ">
            and HANG_SEQ_NO =#{hangSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo != null ">
            and SUB_HANG_SEQ_NO =#{subHangSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="branch != null and branch != null ">
            and TRAN_BRANCH =#{branch,jdbcType=VARCHAR}
        </if>
        <if test="startDate != null and endDate != null ">
            and TRAN_DATE BETWEEN #{startDate} and #{endDate}
        </if>
        <if test="ccy != null and ccy !='' ">
            and CCY = #{ccy,jdbcType=VARCHAR}
        </if>
        <if test="baseAcctNo != null and baseAcctNo !='' ">
            and BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="hangWriteOffStatus != null and hangWriteOffStatus !='' ">
            and HANG_STATUS = #{hangWriteOffStatus,jdbcType=VARCHAR}
        </if>
        <if test="tranAmt != null and tranAmt !='' ">
            and HANG_AMT = #{tranAmt}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND client_no = #{clientNo}
        </if>
        and HANG_STATUS !='C'
    </select>

    <select id="getActiveRbGlHangAccount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT
        <include refid="Base_Column" />
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE HANG_BAL > 0
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND ma.base_acct_no = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="subHangSeqNo != null and subHangSeqNo != ''">
            AND ma.sub_hang_seq_no = #{subHangSeqNo}
        </if>
        <if test="clientNo !=null and clientNo != ''">
            AND ma.CLIENT_NO = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
        order by HANG_END_DATE
    </select>

    <select id="getHangAccountByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT
        <include refid="Base_Column" />
        FROM RB_GL_HANG_ACCOUNT ma
        WHERE 1=1
        <if test="reference != null and reference != ''">
            AND ma.reference = #{reference}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>

    </select>

    <select id="getHangAccountListPage" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangAccount">
        SELECT * FROM (
        SELECT HANG_SEQ_NO,BASE_ACCT_NO,'H' AS COMPANY,HANG_STATUS,HANG_AMT,TRAN_DATE,HANG_WRITE_OFF_TIME,
        HANG_DEAL_TYPE,OTH_BASE_ACCT_NO,OTH_ACCT_NAME,USER_ID,AUTH_USER_ID,NARRATIVE,PLEDGE_BUSI_NO,
        CLIENT_TYPE,DOCUMENT_ID,DOCUMENT_TYPE,REFERENCE
        FROM RB_GL_HANG_ACCOUNT
        <where>
            <if test="hangSeqNo != null and hangSeqNo !=''">
                AND HANG_SEQ_NO= #{hangSeqNo}
            </if>
            <if test="baseAcctNo != null and baseAcctNo !=''">
                AND BASE_ACCT_NO= #{baseAcctNo}
            </if>
            <if test="tranAmt != null and tranAmt !=''">
                AND HANG_AMT = #{tranAmt}
            </if>
            <if test="reference != null and reference != ''">
                AND REFERENCE = #{reference}
            </if>
            <if test="startDate != null ">
                AND TRAN_DATE <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null ">
                AND TRAN_DATE <![CDATA[<=]]> #{endDate}
            </if>
            <if test="hangStatus != null and hangStatus != ''">
                AND HANG_STATUS IN (${hangStatus})
            </if>
            <if test="ccy != null and ccy !='' ">
                and CCY = #{ccy,jdbcType=VARCHAR}
            </if>
        </where>
        UNION ALL
        SELECT HANG_SEQ_NO,BASE_ACCT_NO,'C' AS COMPANY,
        (CASE WHEN WRITE_OFF_STATUS = 'C' THEN 'W' ELSE WRITE_OFF_STATUS END) AS HANG_STATUS,
        WRITE_OFF_AMT AS HANG_AMT,TRAN_DATE,HANG_WRITE_OFF_TIME,
        HANG_DEAL_TYPE,OTH_BASE_ACCT_NO,OTH_ACCT_NAME,USER_ID,AUTH_USER_ID,NARRATIVE,NULL AS PLEDGE_BUSI_NO,NULL AS CLIENT_TYPE,
        NULL AS DOCUMENT_ID,NULL AS DOCUMENT_TYPE,REFERENCE
        FROM RB_GL_WRITE_OFF_ACCOUNT
        <where>
            <if test="hangSeqNo != null and hangSeqNo !=''">
                AND HANG_SEQ_NO= #{hangSeqNo}
            </if>
            <if test="baseAcctNo != null and baseAcctNo !=''">
                AND BASE_ACCT_NO= #{baseAcctNo}
            </if>
            <if test="tranAmt != null and tranAmt !=''">
                AND WRITE_OFF_AMT = #{tranAmt}
            </if>
            <if test="reference != null and reference != ''">
                AND REFERENCE = #{reference}
            </if>
            <if test="startDate != null  ">
                AND TRAN_DATE <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null  ">
                AND TRAN_DATE <![CDATA[<=]]> #{endDate}
            </if>
            <if test="writeOffStatus != null and writeOffStatus !='' ">
                and WRITE_OFF_STATUS = #{writeOffStatus}
            </if>
            <if test="ccy != null and ccy !='' ">
                and CCY = #{ccy,jdbcType=VARCHAR}
            </if>
        </where>
        ) t
        ORDER BY HANG_STATUS DESC
    </select>

</mapper>
