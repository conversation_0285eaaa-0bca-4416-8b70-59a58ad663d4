<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmendBranch">
	<select id="selectMbAmendBranchByMoreTerm" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmendBranch">
		select *
		from RB_AMEND_BRANCH
		<where>
		<if test="baseAcctNo != null and  baseAcctNo != '' ">
			AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
		</if>
		<if test="clientNo != null and  clientNo != '' ">
			AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
		</if>
		<if test="oldBranch != null and  oldBranch != '' ">
			AND OLD_BRANCH = #{oldBranch,jdbcType=VARCHAR}
		</if>
		<if test="newBranch != null and  newBranch != '' ">
			AND NEW_BRANCH = #{newBranch,jdbcType=VARCHAR}
		</if>
		<if test="startDate != null and endDate!= null ">
			AND AMEND_DATE BETWEEN  #{startDate} AND #{endDate}
		</if>
		<if test="prodType != null and  prodType != '' ">
			AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>

	<select id="getBranchChangeAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmendBranch">
		select *
		from RB_AMEND_BRANCH
		where
		OB_AMEND_SEQ_NO = #{obAmendSeqNo}
		<if test="companyList != null">
			AND COMPANY in
			<foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by  PROD_TYPE
	</select>


	<select id="getBranchChangeAcctNoBy" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAmendBranch">
		select *
		from RB_AMEND_BRANCH
		<where>
		<if test="oldBranch != null and  oldBranch != '' ">
			AND OLD_BRANCH = #{oldBranch,jdbcType=VARCHAR}
		</if>
		<if test="clientNo != null and  clientNo != '' ">
			AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
		</if>
		<if test="branchChangeType != null and  branchChangeType != '' ">
			AND BRANCH_CHANGE_TYPE = #{branchChangeType,jdbcType=VARCHAR}
		</if>
		<if test="companyList != null">
			AND COMPANY in
			<foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		</where>
		order by  PROD_TYPE
	</select>


</mapper>
