<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RcListCheckRange">

    <select id="getListByCondition" resultMap="Base_Result_Map"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name" />
        <where>
            <if test="listType != null and listType != '' ">
                AND LIST_TYPE in (#{listType}, "ALL")
            </if>
            <if test="messageType != null and messageType != '' ">
                AND MESSAGE_TYPE in (#{messageType}, "ALL")
            </if>
            <if test="programId != null and programId != '' ">
                AND PROGRAM_ID in (#{programId}, "ALL")
            </if>
            <if test="messageCode != null and messageCode != '' ">
                AND MESSAGE_CODE in (#{messageCode}, "ALL")
            </if>
            <if test="sourceType != null and sourceType != '' ">
                AND SOURCE_TYPE in (#{sourceType}, "ALL")
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>

    </select>


</mapper>