<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">

    <select id="selectByPrimaryKeyExt" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_ATTACH
        where INTERNAL_KEY = #{internalKey}
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        insert into RB_ACCT_ATTACH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="autoDep != null and  autoDep != '' ">
                AUTO_DEP,
            </if>
            <if test="writeOffWay != null and  writeOffWay != '' ">
                WRITE_OFF_WAY,
            </if>
            <if test="acctProofStatus != null and  acctProofStatus != '' ">
                ACCT_PROOF_STATUS,
            </if>
            <if test="balUpdType != null and  balUpdType != '' ">
                BAL_UPD_TYPE,
            </if>
            <if test="hangWriteOffFlag != null and  hangWriteOffFlag != '' ">
                HANG_WRITE_OFF_FLAG,
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                CLIENT_NO,
            </if>
            <if test="internalKey != null ">
                INTERNAL_KEY,
            </if>
            <if test="acctProofReason != null and  acctProofReason != '' ">
                ACCT_PROOF_REASON,
            </if>
            <if test="odFacility != null and  odFacility != '' ">
                OD_FACILITY,
            </if>
            <if test="company != null and  company != '' ">
                COMPANY,
            </if>
            <if test="hangTerm != null and  hangTerm != '' ">
                HANG_TERM,
            </if>
            <if test="annualStatus != null and  annualStatus != '' ">
                ANNUAL_STATUS,
            </if>
            <if test="acctProperty != null and  acctProperty != '' ">
                ACCT_PROPERTY,
            </if>
            <if test="acctProperty2 != null and acctProperty2 != '' ">
                ACCT_PROPERTY2 = #{acctProperty2,jdbcType=VARCHAR},
            </if>
            <if test="cycleIntFlag != null and  cycleIntFlag != '' ">
                CYCLE_INT_FLAG,
            </if>
            <if test="agreementStatus != null and  agreementStatus != '' ">
                AGREEMENT_STATUS,
            </if>
            <if test="stageCode != null and  stageCode != '' ">
                STAGE_CODE,
            </if>
            <if test="balanceWay != null and  balanceWay != '' ">
                BALANCE_WAY,
            </if>
            <if test="lastResetDate != null ">
                LAST_RESET_DATE,
            </if>
            <if test="lastStopDate != null ">
                LAST_STOP_DATE,
            </if>
            <if test="balChgInd != null and  balChgInd != '' ">
                BAL_CHG_IND,
            </if>
            <if test="ftaAcctFlag != null and  ftaAcctFlag != '' ">
                FTA_ACCT_FLAG,
            </if>
            <if test="contraBaseAcctNo != null and  contraBaseAcctNo != '' ">
                CONTRA_BASE_ACCT_NO,
            </if>
            <if test="contraAcctName != null and  contraAcctName != '' ">
                CONTRA_ACCT_NAME,
            </if>
            <if test="annualFlag != null and  annualFlag != '' ">
                ANNUAL_FLAG,
            </if>
            <if test="autoSettleFlag != null and  autoSettleFlag != '' ">
                AUTO_SETTLE_FLAG,
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP,
            </if>
            <if test="ftaCode != null and  ftaCode != '' ">
                FTA_CODE,
            </if>
            <if test="glCode != null and  glCode != '' ">
                GL_CODE,
            </if>
            <if test="specialProdClass != null and  specialProdClass != '' ">
                SPECIAL_PROD_CLASS,
            </if>
            <if test="msgStatus != null and  msgStatus != '' ">
                MSG_STATUS,
            </if>
            <if test="blacklistStatus != null and  blacklistStatus != '' ">
                BLACKLIST_STATUS,
            </if>
            <if test="lastBlacklistDate != null ">
                LAST_BLACKLIST_DATE,
            </if>
            <if test="freeSum != null and  freeSum != '' ">
                FREE_SUM,
            </if>
            <if test="manualAccountFlag != null and  manualAccountFlag != '' ">
                MANUAL_ACCOUNT_FLAG,
            </if>
            <if test="prodClass != null and  prodClass != '' ">
                PROD_CLASS,
            </if>
            <if test="contraBranch != null and  contraBranch != '' ">
                CONTRA_BRANCH,
            </if>
            <if test="contraBranchName != null and  contraBranchName != '' ">
                CONTRA_BRANCH_NAME,
            </if>
            <if test="autoRenewTerm != null and  autoRenewTerm != '' ">
                AUTO_RENEW_TERM,
            </if>
            <if test="autoRenewTermType != null and  autoRenewTermType != '' ">
                AUTO_RENEW_TERM_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="autoDep != null and  autoDep != '' ">
                #{autoDep},
            </if>
            <if test="writeOffWay != null and  writeOffWay != '' ">
                #{writeOffWay},
            </if>
            <if test="acctProofStatus != null and  acctProofStatus != '' ">
                #{acctProofStatus},
            </if>
            <if test="balUpdType != null and  balUpdType != '' ">
                #{balUpdType},
            </if>
            <if test="hangWriteOffFlag != null and  hangWriteOffFlag != '' ">
                #{hangWriteOffFlag},
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                #{clientNo},
            </if>
            <if test="internalKey != null ">
                #{internalKey},
            </if>
            <if test="acctProofReason != null and  acctProofReason != '' ">
                #{acctProofReason},
            </if>
            <if test="odFacility != null and  odFacility != '' ">
                #{odFacility},
            </if>
            <if test="company != null and  company != '' ">
                #{company},
            </if>
            <if test="hangTerm != null and  hangTerm != '' ">
                #{hangTerm},
            </if>
            <if test="annualStatus != null and  annualStatus != '' ">
                #{annualStatus},
            </if>
            <if test="acctProperty != null and  acctProperty != '' ">
                #{acctProperty},
            </if>
            <if test="cycleIntFlag != null and  cycleIntFlag != '' ">
                #{cycleIntFlag},
            </if>
            <if test="agreementStatus != null and  agreementStatus != '' ">
                #{agreementStatus},
            </if>
            <if test="stageCode != null and  stageCode != '' ">
                #{stageCode},
            </if>
            <if test="balanceWay != null and  balanceWay != '' ">
                #{balanceWay},
            </if>
            <if test="lastResetDate != null ">
                #{lastResetDate},
            </if>
            <if test="lastStopDate != null ">
                #{lastStopDate},
            </if>
            <if test="balChgInd != null and  balChgInd != '' ">
                #{balChgInd},
            </if>
            <if test="ftaAcctFlag != null and  ftaAcctFlag != '' ">
                #{ftaAcctFlag},
            </if>
            <if test="contraBaseAcctNo != null and  contraBaseAcctNo != '' ">
                #{contraBaseAcctNo},
            </if>
            <if test="contraAcctName != null and  contraAcctName != '' ">
                #{contraAcctName},
            </if>
            <if test="annualFlag != null and  annualFlag != '' ">
                #{annualFlag},
            </if>
            <if test="autoSettleFlag != null and  autoSettleFlag != '' ">
                #{autoSettleFlag},
            </if>
            <if test="tranTimestamp != null ">
                #{tranTimestamp},
            </if>
            <if test="ftaCode != null and  ftaCode != '' ">
                #{ftaCode},
            </if>
            <if test="glCode != null and  glCode != '' ">
                #{glCode},
            </if>
            <if test="specialProdClass != null and  specialProdClass != '' ">
                #{specialProdClass},
            </if>
            <if test="msgStatus != null and  msgStatus != '' ">
                #{msgStatus},
            </if>
            <if test="blacklistStatus != null and  blacklistStatus != '' ">
                #{blacklistStatus},
            </if>
            <if test="lastBlacklistDate != null ">
                #{lastBlacklistDate},
            </if>
            <if test="freeSum != null and  freeSum != '' ">
                #{freeSum},
            </if>
            <if test="manualAccountFlag != null and  manualAccountFlag != '' ">
                #{manualAccountFlag},
            </if>
            <if test="prodClass != null and  prodClass != '' ">
                #{prodClass},
            </if>
            <if test="contraBranch != null and  contraBranch != '' ">
                #{contraBranch},
            </if>
            <if test="contraBranchName != null and  contraBranchName != '' ">
                #{contraBranchName},
            </if>
            <if test="autoRenewTerm != null and  autoRenewTerm != '' ">
                #{autoRenewTerm},
            </if>
            <if test="autoRenewTermType != null and  autoRenewTermType != '' ">
                #{autoRenewTermType},
            </if>
        </trim>
    </insert>

    <!-- 查询对手账号 -->
    <select id="selectcontraBaseAcctNo" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT CONTRA_BASE_ACCT_NO
        FROM RB_ACCT_ATTACH
        where INTERNAL_KEY=#{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <!-- 查询internalKey -->
    <select id="selectInternalKey" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT INTERNAL_KEY
        FROM RB_ACCT_ATTACH
        where CONTRA_BASE_ACCT_NO=#{contraBaseAcctNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <!-- 查询账户验证结果 -->
    <select id="getAcctVerifyResult" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_ATTACH
        where TRAN_TIMESTAMP between #{startDate} AND #{endDate}
        <if test="internalKey !=  null">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by TRAN_TIMESTAMP asc
    </select>

    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        update RB_ACCT_ATTACH
        <set>
            <if test="autoDep != null and  autoDep != '' ">
                AUTO_DEP = #{autoDep},
            </if>
            <if test="writeOffWay != null and  writeOffWay != '' ">
                WRITE_OFF_WAY = #{writeOffWay},
            </if>
            <if test="acctProofStatus != null and  acctProofStatus != '' ">
                ACCT_PROOF_STATUS = #{acctProofStatus},
            </if>
            <if test="balUpdType != null and  balUpdType != '' ">
                BAL_UPD_TYPE = #{balUpdType},
            </if>
            <if test="hangWriteOffFlag != null and  hangWriteOffFlag != '' ">
                HANG_WRITE_OFF_FLAG = #{hangWriteOffFlag},
            </if>
            <if test="acctProofReason != null and  acctProofReason != '' ">
                ACCT_PROOF_REASON = #{acctProofReason},
            </if>
            <if test="odFacility != null and  odFacility != '' ">
                OD_FACILITY = #{odFacility},
            </if>
            <if test="company != null and  company != '' ">
                COMPANY = #{company},
            </if>
            <if test="hangTerm != null and  hangTerm != '' ">
                HANG_TERM = #{hangTerm},
            </if>
            <if test="annualStatus != null and  annualStatus != '' ">
                ANNUAL_STATUS = #{annualStatus},
            </if>
            <if test="acctProperty != null and  acctProperty != '' ">
                ACCT_PROPERTY = #{acctProperty},
            </if>
            <if test="acctProperty2 != null and acctProperty2 != '' ">
                ACCT_PROPERTY2 = #{acctProperty2,jdbcType=VARCHAR},
            </if>
            <if test="cycleIntFlag != null and  cycleIntFlag != '' ">
                CYCLE_INT_FLAG = #{cycleIntFlag},
            </if>
            <if test="agreementStatus != null and  agreementStatus != '' ">
                AGREEMENT_STATUS = #{agreementStatus},
            </if>
            <if test="stageCode != null and  stageCode != '' ">
                STAGE_CODE = #{stageCode},
            </if>
            <if test="balanceWay != null and  balanceWay != '' ">
                BALANCE_WAY = #{balanceWay},
            </if>
            <if test="lastResetDate != null ">
                LAST_RESET_DATE = #{lastResetDate},
            </if>
            <if test="lastStopDate != null ">
                LAST_STOP_DATE = #{lastStopDate},
            </if>
            <if test="balChgInd != null and  balChgInd != '' ">
                BAL_CHG_IND = #{balChgInd},
            </if>
            <if test="ftaAcctFlag != null and  ftaAcctFlag != '' ">
                FTA_ACCT_FLAG = #{ftaAcctFlag},
            </if>
            <if test="contraBaseAcctNo != null and  contraBaseAcctNo != '' ">
                CONTRA_BASE_ACCT_NO = #{contraBaseAcctNo},
            </if>
            <if test="contraAcctName != null and  contraAcctName != '' ">
                CONTRA_ACCT_NAME = #{contraAcctName},
            </if>
            <if test="annualFlag != null and  annualFlag != '' ">
                ANNUAL_FLAG = #{annualFlag},
            </if>
            <if test="autoSettleFlag != null and  autoSettleFlag != '' ">
                AUTO_SETTLE_FLAG = #{autoSettleFlag},
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="ftaCode != null and  ftaCode != '' ">
                FTA_CODE = #{ftaCode},
            </if>
            <if test="glCode != null and  glCode != '' ">
                GL_CODE = #{glCode},
            </if>
            <if test="specialProdClass != null and  specialProdClass != '' ">
                SPECIAL_PROD_CLASS = #{specialProdClass},
            </if>
            <if test="msgStatus != null and  msgStatus != '' ">
                MSG_STATUS = #{msgStatus},
            </if>
            <if test="blacklistStatus != null and  blacklistStatus != '' ">
                BLACKLIST_STATUS = #{blacklistStatus},
            </if>
            <if test="lastBlacklistDate != null ">
                LAST_BLACKLIST_DATE = #{lastBlacklistDate},
            </if>
            <if test="freeSum != null and  freeSum != '' ">
                FREE_SUM = #{freeSum},
            </if>
            <if test="manualAccountFlag != null and  manualAccountFlag != '' ">
                MANUAL_ACCOUNT_FLAG = #{manualAccountFlag},
            </if>
            <if test="prodClass != null and  prodClass != '' ">
                PROD_CLASS = #{prodClass},
            </if>
            <if test="contraBranch != null and  contraBranch != '' ">
                CONTRA_BRANCH = #{contraBranch},
            </if>
            <if test="contraBranchName != null and  contraBranchName != '' ">
                CONTRA_BRANCH_NAME = #{contraBranchName},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <update id="updateBalChgIndByPrimaryKey" parameterType="java.util.Map">
        update RB_ACCT_ATTACH
        <set>
            <if test="balChgInd != null">
                BAL_CHG_IND = #{balChgInd},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey} and client_no = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <update id="updateProofStatusByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        update RB_ACCT_ATTACH
        <set>
            <if test="acctProofStatus != null">
                ACCT_PROOF_STATUS = #{acctProofStatus},
            </if>
            <if test="amountNature != null">
                AMOUNT_NATURE = #{amountNature},
            </if>
            <if test="acctVerifyStatus != null">
                ACCT_VERIFY_STATUS = #{acctVerifyStatus},
            </if>
            <if test="acctVerifyStatusPrev != null">
                ACCT_VERIFY_STATUS_PREV = #{acctVerifyStatusPrev},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null and clientNo !='' ">
            AND CLIENT_NO = #{clientNo}
        </if>
    </update>

    <update id="updateFourByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        update RB_ACCT_ATTACH
        <set>
            FIR_PERIOD = #{firPeriod},
            MID_PERIOD = #{midPeriod},
            ADD_AMT = #{addAmt},
            ADD_RATIO = #{addRatio},
            MANUAL_CHANGE_SCHEDULE = #{manualChangeSchedule}
        </set>
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="getMbAcctAttachByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_ATTACH
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectInternalKeyBycontraBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_ATTACH
        where CONTRA_BASE_ACCT_NO = #{contraBaseAcctNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateSignStatusByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        update RB_ACCT_ATTACH
        <set>
            <if test="agreementStatus != null">
                AGREEMENT_STATUS = #{agreementStatus},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        AND CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="queryInternalKeyByStageCode" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_ATTACH
        where STAGE_CODE = #{stageCode}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectAttachByPrimaryKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_ATTACH
        where INTERNAL_KEY = #{internalKey}
        AND CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>


    <update id="updateByPrimaryKeyClinet" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        update RB_ACCT_ATTACH
        <set>
            <if test="autoDep != null and  autoDep != '' ">
                AUTO_DEP = #{autoDep},
            </if>
            <if test="writeOffWay != null and  writeOffWay != '' ">
                WRITE_OFF_WAY = #{writeOffWay},
            </if>
            <if test="acctProofStatus != null and  acctProofStatus != '' ">
                ACCT_PROOF_STATUS = #{acctProofStatus},
            </if>
            <if test="balUpdType != null and  balUpdType != '' ">
                BAL_UPD_TYPE = #{balUpdType},
            </if>
            <if test="hangWriteOffFlag != null and  hangWriteOffFlag != '' ">
                HANG_WRITE_OFF_FLAG = #{hangWriteOffFlag},
            </if>
            <if test="acctProofReason != null and  acctProofReason != '' ">
                ACCT_PROOF_REASON = #{acctProofReason},
            </if>
            <if test="odFacility != null and  odFacility != '' ">
                OD_FACILITY = #{odFacility},
            </if>
            <if test="company != null and  company != '' ">
                COMPANY = #{company},
            </if>
            <if test="hangTerm != null and  hangTerm != '' ">
                HANG_TERM = #{hangTerm},
            </if>
            <if test="annualStatus != null and  annualStatus != '' ">
                ANNUAL_STATUS = #{annualStatus},
            </if>
            <if test="acctProperty != null and  acctProperty != '' ">
                ACCT_PROPERTY = #{acctProperty},
            </if>
            <if test="acctProperty2 != null and acctProperty2 != '' ">
                ACCT_PROPERTY2 = #{acctProperty2,jdbcType=VARCHAR},
            </if>
            <if test="cycleIntFlag != null and  cycleIntFlag != '' ">
                CYCLE_INT_FLAG = #{cycleIntFlag},
            </if>
            <if test="agreementStatus != null and  agreementStatus != '' ">
                AGREEMENT_STATUS = #{agreementStatus},
            </if>
            <if test="stageCode != null and  stageCode != '' ">
                STAGE_CODE = #{stageCode},
            </if>
            <if test="balanceWay != null and  balanceWay != '' ">
                BALANCE_WAY = #{balanceWay},
            </if>
            <if test="lastResetDate != null ">
                LAST_RESET_DATE = #{lastResetDate},
            </if>
            <if test="lastStopDate != null ">
                LAST_STOP_DATE = #{lastStopDate},
            </if>
            <if test="balChgInd != null and  balChgInd != '' ">
                BAL_CHG_IND = #{balChgInd},
            </if>
            <if test="ftaAcctFlag != null and  ftaAcctFlag != '' ">
                FTA_ACCT_FLAG = #{ftaAcctFlag},
            </if>
            <if test="contraBaseAcctNo != null and  contraBaseAcctNo != '' ">
                CONTRA_BASE_ACCT_NO = #{contraBaseAcctNo},
            </if>
            <if test="contraAcctName != null and  contraAcctName != '' ">
                CONTRA_ACCT_NAME = #{contraAcctName},
            </if>
            <if test="annualFlag != null and  annualFlag != '' ">
                ANNUAL_FLAG = #{annualFlag},
            </if>
            <if test="autoSettleFlag != null and  autoSettleFlag != '' ">
                AUTO_SETTLE_FLAG = #{autoSettleFlag},
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="ftaCode != null and  ftaCode != '' ">
                FTA_CODE = #{ftaCode},
            </if>
            <if test="glCode != null and  glCode != '' ">
                GL_CODE = #{glCode},
            </if>
            <if test="specialProdClass != null and  specialProdClass != '' ">
                SPECIAL_PROD_CLASS = #{specialProdClass},
            </if>
            <if test="msgStatus != null and  msgStatus != '' ">
                MSG_STATUS = #{msgStatus},
            </if>
            <if test="blacklistStatus != null and  blacklistStatus != '' ">
                BLACKLIST_STATUS = #{blacklistStatus},
            </if>
            <if test="lastBlacklistDate != null ">
                LAST_BLACKLIST_DATE = #{lastBlacklistDate},
            </if>
            <if test="manualAccountFlag != null ">
                MANUAL_ACCOUNT_FLAG = #{manualAccountFlag},
            </if>
            <if test="prodClass != null ">
                PROD_CLASS = #{prodClass},
            </if>
            <if test="contraBranch != null ">
                CONTRA_BRANCH = #{contraBranch},
            </if>
            <if test="contraBranchName != null ">
                CONTRA_BRANCH_NAME = #{contraBranchName},
            </if>
            <if test="interestDue != null ">
                INTEREST_DUE = #{interestDue},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}  AND CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>


    <!-- 查询核实过的账户-->
    <select id="selectBlackCheck" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_ATTACH
        where  BLACKLIST_STATUS = #{blacklistStatus} AND
        LAST_BLACKLIST_DATE = #{lastBlacklistDate}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <!-- 查询未核实过的账户-->
    <select id="selectBlackUncheck" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_ATTACH
        where  CLIENT_NO = #{clientNo}
        AND BLACKLIST_STATUS in ('02','03','04','05','06','07')
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectBalList" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_ATTACH
        where  INTERNAL_KEY in
        <foreach collection="internalKey" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateMbAcctAttachDbList1">
        UPDATE
        RB_ACCT_ATTACH
        <set>
            <if test="internalKey != null ">
                INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},
            </if>
            <if test="glCode != null and glCode != '' ">
                GL_CODE = #{glCode,jdbcType=VARCHAR},
            </if>
            <if test="acctProofStatus != null and acctProofStatus != '' ">
                ACCT_PROOF_STATUS = #{acctProofStatus,jdbcType=VARCHAR},
            </if>
            <if test="acctProofReason != null and acctProofReason != '' ">
                ACCT_PROOF_REASON = #{acctProofReason,jdbcType=VARCHAR},
            </if>
            <if test="acctProperty != null and acctProperty != '' ">
                ACCT_PROPERTY = #{acctProperty,jdbcType=VARCHAR},
            </if>
            <if test="acctProperty2 != null and acctProperty2 != '' ">
                ACCT_PROPERTY2 = #{acctProperty2,jdbcType=VARCHAR},
            </if>
            <if test="balChgInd != null and balChgInd != '' ">
                BAL_CHG_IND = #{balChgInd,jdbcType=VARCHAR},
            </if>
            <if test="balUpdType != null and balUpdType != '' ">
                BAL_UPD_TYPE = #{balUpdType,jdbcType=VARCHAR},
            </if>
            <if test="balanceWay != null and balanceWay != '' ">
                BALANCE_WAY = #{balanceWay,jdbcType=VARCHAR},
            </if>
            <if test="odFacility != null and odFacility != '' ">
                OD_FACILITY = #{odFacility,jdbcType=VARCHAR},
            </if>
            <if test="cycleIntFlag != null and cycleIntFlag != '' ">
                CYCLE_INT_FLAG = #{cycleIntFlag,jdbcType=VARCHAR},
            </if>
            <if test="autoSettleFlag != null and autoSettleFlag != '' ">
                AUTO_SETTLE_FLAG = #{autoSettleFlag,jdbcType=VARCHAR},
            </if>
            <if test="autoDep != null and autoDep != '' ">
                AUTO_DEP = #{autoDep,jdbcType=VARCHAR},
            </if>
            <if test="manualAccountFlag != null and manualAccountFlag != '' ">
                MANUAL_ACCOUNT_FLAG = #{manualAccountFlag,jdbcType=VARCHAR},
            </if>
            <if test="ftaAcctFlag != null and ftaAcctFlag != '' ">
                FTA_ACCT_FLAG = #{ftaAcctFlag,jdbcType=VARCHAR},
            </if>
            <if test="ftaCode != null and ftaCode != '' ">
                FTA_CODE = #{ftaCode,jdbcType=VARCHAR},
            </if>
            <if test="contraBaseAcctNo != null and contraBaseAcctNo != '' ">
                CONTRA_BASE_ACCT_NO = #{contraBaseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="contraAcctName != null and contraAcctName != '' ">
                CONTRA_ACCT_NAME = #{contraAcctName,jdbcType=VARCHAR},
            </if>
            <if test="contraBranch != null and contraBranch != '' ">
                CONTRA_BRANCH = #{contraBranch,jdbcType=VARCHAR},
            </if>
            <if test="contraBranchName != null and contraBranchName != '' ">
                CONTRA_BRANCH_NAME = #{contraBranchName,jdbcType=VARCHAR},
            </if>
            <if test="contraAcctOpenDate != null">
                CONTRA_ACCT_OPEN_DATE = #{contraAcctOpenDate, jdbcType = DATE},
            </if>
            <if test="hangWriteOffFlag != null and hangWriteOffFlag != '' ">
                HANG_WRITE_OFF_FLAG = #{hangWriteOffFlag,jdbcType=VARCHAR},
            </if>
            <if test="hangTerm != null and hangTerm != '' ">
                HANG_TERM = #{hangTerm,jdbcType=VARCHAR},
            </if>
            <if test="writeOffWay != null and writeOffWay != '' ">
                WRITE_OFF_WAY = #{writeOffWay,jdbcType=VARCHAR},
            </if>
            <if test="agreementStatus != null and agreementStatus != '' ">
                AGREEMENT_STATUS = #{agreementStatus,jdbcType=VARCHAR},
            </if>
            <if test="prodClass != null and prodClass != '' ">
                PROD_CLASS = #{prodClass,jdbcType=VARCHAR},
            </if>
            <if test="specialProdClass != null and specialProdClass != '' ">
                SPECIAL_PROD_CLASS = #{specialProdClass,jdbcType=VARCHAR},
            </if>
            <if test="stageCode != null and stageCode != '' ">
                STAGE_CODE = #{stageCode,jdbcType=VARCHAR},
            </if>
            <if test="annualFlag != null and annualFlag != '' ">
                ANNUAL_FLAG = #{annualFlag,jdbcType=VARCHAR},
            </if>
            <if test="annualStatus != null and annualStatus != '' ">
                ANNUAL_STATUS = #{annualStatus,jdbcType=VARCHAR},
            </if>
            <if test="lastResetDate != null ">
                LAST_RESET_DATE = #{lastResetDate,jdbcType=DATE},
            </if>
            <if test="lastStopDate != null ">
                LAST_STOP_DATE = #{lastStopDate,jdbcType=DATE},
            </if>
            <if test="blacklistStatus != null and blacklistStatus != '' ">
                BLACKLIST_STATUS = #{blacklistStatus,jdbcType=VARCHAR},
            </if>
            <if test="lastBlacklistDate != null ">
                LAST_BLACKLIST_DATE = #{lastBlacklistDate,jdbcType=DATE},
            </if>
            <if test="freeSum != null ">
                FREE_SUM = #{freeSum,jdbcType=INTEGER},
            </if>
            <if test="impoundFad != null and impoundFad != '' ">
                IMPOUND_FAD = #{impoundFad,jdbcType=VARCHAR},
            </if>
            <if test="msgStatus != null and msgStatus != '' ">
                MSG_STATUS = #{msgStatus,jdbcType=VARCHAR},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>
            <if test="autoRenewTerm != null and autoRenewTerm != '' ">
                AUTO_RENEW_TERM = #{autoRenewTerm,jdbcType=VARCHAR},
            </if>
            <if test="autoRenewTermType != null and autoRenewTermType != '' ">
                AUTO_RENEW_TERM_TYPE = #{autoRenewTermType,jdbcType=VARCHAR},
            </if>
            <if test="totalDrawAmt != null ">
                TOTAL_DRAW_AMT = #{totalDrawAmt,jdbcType=DECIMAL},
            </if>
            <if test="checkCertificateAmt != null ">
                CHECK_CERTIFICATE_AMT = #{checkCertificateAmt,jdbcType=DECIMAL},
            </if>
            <if test="checkCertificateType != null and checkCertificateType != '' ">
                CHECK_CERTIFICATE_TYPE = #{checkCertificateType,jdbcType=VARCHAR},
            </if>
            <if test="allowSuspendFlag != null and allowSuspendFlag != '' ">
                ALLOW_SUSPEND_FLAG = #{allowSuspendFlag,jdbcType=VARCHAR},
            </if>
            <if test="intTaxLevy != null and intTaxLevy != '' ">
                INT_TAX_LEVY = #{intTaxLevy,jdbcType=VARCHAR},
            </if>
            <if test="taxRate != null">
                TAX_RATE = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="allDraIntBranch != null and allDraIntBranch != '' ">
                ALL_DRA_INT_BRANCH = #{allDraIntBranch,jdbcType=VARCHAR},
            </if>
            <if test="acctVerifyStatus != null and acctVerifyStatus != '' ">
                ACCT_VERIFY_STATUS = #{acctVerifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="depositNature != null and depositNature != '' ">
                DEPOSIT_NATURE = #{depositNature,jdbcType=VARCHAR},
            </if>
            <if test="acctVerifyStatusPrev != null and acctVerifyStatusPrev != '' ">
                ACCT_VERIFY_STATUS_PREV = #{acctVerifyStatusPrev,jdbcType=VARCHAR},
            </if>
            <if test="isSellCheque != null and isSellCheque != '' ">
                IS_SELL_CHEQUE = #{isSellCheque,jdbcType=VARCHAR},
            </if>
            <if test="caseInvolvedFlag != null and caseInvolvedFlag != '' ">
                CASE_INVOLVED_FLAG = #{caseInvolvedFlag,jdbcType=VARCHAR},
            </if>
            <if test="contraClientNo != null and contraClientNo != '' ">
                CONTRA_CLIENT_NO = #{contraClientNo,jdbcType=VARCHAR},
            </if>
            <if test="contraAreaCode != null and contraAreaCode != '' ">
                CONTRA_AREA_CODE = #{contraAreaCode,jdbcType=VARCHAR},
            </if>
            <if test="contraCountry != null and contraCountry != '' ">
                CONTRA_COUNTRY = #{contraCountry,jdbcType=VARCHAR},
            </if>
            <if test="swiftId != null and swiftId != '' ">
                SWIFT_ID = #{swiftId,jdbcType=VARCHAR},
            </if>
            <if test="approvalNo != null and approvalNo != '' ">
                APPROVAL_NO = #{approvalNo,jdbcType=VARCHAR},
            </if>
            <if test="counterDepFlag != null and counterDepFlag != '' ">
                COUNTER_DEP_FLAG = #{counterDepFlag,jdbcType=VARCHAR},
            </if>
            <if test="counterDebtFlag != null and counterDebtFlag != '' ">
                COUNTER_DEBT_FLAG = #{counterDebtFlag,jdbcType=VARCHAR},
            </if>
            <if test="acctRiskLevel != null and acctRiskLevel != '' ">
                ACCT_RISK_LEVEL = #{acctRiskLevel,jdbcType=VARCHAR},
            </if>
            <if test="lineOwnerShip != null and lineOwnerShip != '' ">
                LINE_OWNER_SHIP = #{lineOwnerShip,jdbcType=VARCHAR},
            </if>
            <if test="acctExecCode != null and acctExecCode != '' ">
                ACCT_EXEC_CODE = #{acctExecCode,jdbcType=VARCHAR},
            </if>
            <if test="acctExecName != null and acctExecName != '' ">
                ACCT_EXEC_NAME = #{acctExecName,jdbcType=VARCHAR},
            </if>
            <if test="promoterCode != null and promoterCode != '' ">
                PROMOTER_CODE = #{acctExecCode,jdbcType=VARCHAR},
            </if>
            <if test="promoterName != null and promoterName != '' ">
                PROMOTER_NAME = #{promoterName,jdbcType=VARCHAR},
            </if>
            <if test="termLack != null ">
                TERM_LACK = #{termLack,jdbcType=BIGINT}
            </if>
        </set>
        <where>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <update id="updateMbAcctAttachByInternalKeyAndClientNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        UPDATE RB_ACCT_ATTACH
        SET CASE_INVOLVED_FLAG = #{caseInvolvedFlag,jdbcType=VARCHAR},
        ACCT_VERIFY_STATUS_PREV = #{acctVerifyStatusPrev,jdbcType=VARCHAR},
        ACCT_VERIFY_STATUS = #{acctVerifyStatus,jdbcType=VARCHAR}
        <where>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <delete id="deleteByInternalKeyAndClientNo"  parameterType="java.util.Map" >
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Jun 02 11:23:06 CST 2015.
        -->
        delete
        FROM RB_ACCT_ATTACH
        WHERE internal_key =
        #{internalKey}
        AND client_no =  #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <!-- 查询核实过的账户-->
    <select id="queryByContraBaseAcctNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_ATTACH
        where  CONTRA_BASE_ACCT_NO =#{contraBaseAcctNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateClientNo" parameterType="java.util.Map">
        update RB_ACCT_ATTACH set CLIENT_NO = #{clientNo},TRAN_TIMESTAMP = #{tranTimestamp}
        where internal_key = #{internalKey} and CLIENT_NO = #{oldClientNo}
    </update>
    <update id="updateRiskByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        update RB_ACCT_ATTACH
        <set>
            <if test="acctRiskLevel != null">
                ACCT_RISK_LEVEL = #{acctRiskLevel},
            </if>
            <!--
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            -->
        </set>
        where INTERNAL_KEY = #{internalKey}
        AND CLIENT_NO = #{clientNo}
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="updAttachInfoSettleFlag" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach">
        update RB_ACCT_ATTACH
        <set>
            <if test="autoSettleFlag != null">
                AUTO_SETTLE_FLAG = #{autoSettleFlag},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        AND CLIENT_NO = #{clientNo}
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
</mapper>
