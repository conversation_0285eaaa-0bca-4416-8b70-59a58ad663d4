<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="restraintType != null">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <if test="resSeqNo != null">
      AND RES_SEQ_NO = #{resSeqNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByPrimaryKeyForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where RES_SEQ_NO = #{resSeqNo}
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    and RESTRAINTS_STATUS != 'E'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    for update
  </select>

  <select id="selectByPrimaryKeyByE" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="resSeqNo != null">
      AND RES_SEQ_NO = #{resSeqNo}
    </if>
    <if test="restraintType != null">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    and RESTRAINTS_STATUS != 'E'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectRestraint" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="restraintType != null">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <if test="resSeqNo != null">
      AND RES_SEQ_NO = #{resSeqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    and restraints_Status != 'E'
  </select>
  <select id="selectRestraintForA" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="restraintType != null">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <if test="resSeqNo != null">
      AND RES_SEQ_NO = #{resSeqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    and restraints_Status = 'A'
  </select>

  <select id="getRestraintForAAndNotrestraintType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    and RESTRAINT_TYPE not in ('006','007','021','022','023')
    and restraints_Status = 'A'
  </select>

  <select id="selectRestraintList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where  RES_SEQ_NO = #{resSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectRestraintByClientNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="restraintType != null">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <if test="resSeqNo != null">
      AND RES_SEQ_NO = #{resSeqNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and RESTRAINTS_STATUS != 'E'
    order by TRAN_TIMESTAMP desc
  </select>
  <select id="selectMbRestraintsOld" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="restraintType != null">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <if test="resSeqNo != null">
      AND RES_SEQ_NO = #{resSeqNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    delete from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="restraintType != null">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <if test="resSeqNo != null">
      AND RES_SEQ_NO = #{resSeqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>

  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    update RB_RESTRAINTS
    <set>
      <if test="programId != null and  programId != '' ">
        PROGRAM_ID = #{programId},
      </if>
      <if test="resAcctRange != null and  resAcctRange != '' ">
        RES_ACCT_RANGE = #{resAcctRange},
      </if>
      <if test="thawDocumentType != null and  thawDocumentType != '' ">
        THAW_DOCUMENT_TYPE = #{thawDocumentType},
      </if>
      <if test="thawDocumentId2 != null and  thawDocumentId2 != '' ">
        THAW_DOCUMENT_ID2 = #{thawDocumentId2},
      </if>
      <if test="tranBranch != null and  tranBranch != '' ">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="endAmt != null ">
        END_AMT = #{endAmt},
      </if>
      <if test="stlSeqNo != null and  stlSeqNo != '' ">
        STL_SEQ_NO = #{stlSeqNo},
      </if>
      <if test="paymentMade != null and  paymentMade != '' ">
        PAYMENT_MADE = #{paymentMade},
      </if>
      <if test="pledgedBaseAcctNo != null and  pledgedBaseAcctNo != '' ">
        PLEDGED_BASE_ACCT_NO = #{pledgedBaseAcctNo},
      </if>
      <if test="deductionJudiciaryName != null and  deductionJudiciaryName != '' ">
        DEDUCTION_JUDICIARY_NAME = #{deductionJudiciaryName},
      </if>
      <if test="thawOfficerName != null and  thawOfficerName != '' ">
        THAW_OFFICER_NAME = #{thawOfficerName},
      </if>
      <if test="pledgedAmt != null ">
        PLEDGED_AMT = #{pledgedAmt},
      </if>
      <if test="pledgedAcctType != null and  pledgedAcctType != '' ">
        PLEDGED_ACCT_TYPE = #{pledgedAcctType},
      </if>
      <if test="lastChangeDate != null ">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="restraintsStatus != null and  restraintsStatus != '' ">
        RESTRAINTS_STATUS = #{restraintsStatus},
      </if>
      <if test="othAcctNo != null and  othAcctNo != '' ">
        OTH_ACCT_NO = #{othAcctNo},
      </if>
      <if test="reference != null and  reference != '' ">
        REFERENCE = #{reference},
      </if>
      <if test="tranAmt != null ">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="interruptFlag != null and  interruptFlag != '' ">
        INTERRUPT_FLAG = #{interruptFlag},
      </if>
      <if test="apprUserId != null and  apprUserId != '' ">
        APPR_USER_ID = #{apprUserId},
      </if>
      <if test="othAcctCcy != null and  othAcctCcy != '' ">
        OTH_ACCT_CCY = #{othAcctCcy},
      </if>
      <if test="resLawNo != null and  resLawNo != '' ">
        RES_LAW_NO = #{resLawNo},
      </if>
      <if test="thawOthOfficerName != null and  thawOthOfficerName != '' ">
        THAW_OTH_OFFICER_NAME = #{thawOthOfficerName},
      </if>
      <if test="company != null and  company != '' ">
        COMPANY = #{company},
      </if>
      <if test="subRestraintClass != null and  subRestraintClass != '' ">
        SUB_RESTRAINT_CLASS = #{subRestraintClass},
      </if>
      <if test="msgClient != null and  msgClient != '' ">
        MSG_CLIENT = #{msgClient},
      </if>
      <if test="toPayAmt != null ">
        TO_PAY_AMT = #{toPayAmt},
      </if>
      <if test="approvalDate != null ">
        APPROVAL_DATE = #{approvalDate},
      </if>
      <if test="docType != null and  docType != '' ">
        DOC_TYPE = #{docType},
      </if>
      <if test="deductionLawNo != null and  deductionLawNo != '' ">
        DEDUCTION_LAW_NO = #{deductionLawNo},
      </if>
      <if test="judiciaryOfficerName != null and  judiciaryOfficerName != '' ">
        JUDICIARY_OFFICER_NAME = #{judiciaryOfficerName},
      </if>
      <if test="thawDocumentId != null and  thawDocumentId != '' ">
        THAW_DOCUMENT_ID = #{thawDocumentId},
      </if>
      <if test="thawOthDocumentId != null and  thawOthDocumentId != '' ">
        THAW_OTH_DOCUMENT_ID = #{thawOthDocumentId},
      </if>
      <if test="endChequeNo != null and  endChequeNo != '' ">
        END_CHEQUE_NO = #{endChequeNo},
      </if>
      <if test="lastChangeUserId != null and  lastChangeUserId != '' ">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="sourceModule != null and  sourceModule != '' ">
        SOURCE_MODULE = #{sourceModule},
      </if>
      <if test="fullFreezeInd != null and  fullFreezeInd != '' ">
        FULL_FREEZE_IND = #{fullFreezeInd},
      </if>
      <if test="releaseJudiciaryName != null and  releaseJudiciaryName != '' ">
        RELEASE_JUDICIARY_NAME = #{releaseJudiciaryName},
      </if>
      <if test="judiciaryDocumentId != null and  judiciaryDocumentId != '' ">
        JUDICIARY_DOCUMENT_ID = #{judiciaryDocumentId},
      </if>

      <if test="thawDocumentType != null and  thawDocumentType != '' ">
        THAW_DOCUMENT_TYPE = #{thawDocumentType},
      </if>
      <if test="thawOthDocumentId2 != null and  thawOthDocumentId2 != '' ">
        THAW_OTH_DOCUMENT_ID2 = #{thawOthDocumentId2},
      </if>
      <if test="startAmt != null ">
        START_AMT = #{startAmt},
      </if>
      <if test="narrative != null">
        NARRATIVE = #{narrative},
      </if>
      <if test="othBankCode != null and  othBankCode != '' ">
        OTH_BANK_CODE = #{othBankCode},
      </if>
      <if test="term != null and  term != '' ">
        TERM = #{term},
      </if>
      <if test="restraintJudiciaryName != null and  restraintJudiciaryName != '' ">
        RESTRAINT_JUDICIARY_NAME = #{restraintJudiciaryName},
      </if>
      <if test="judiciaryDocumentType != null and  judiciaryDocumentType != '' ">
        JUDICIARY_DOCUMENT_TYPE = #{judiciaryDocumentType},
      </if>
      <if test="startChequeNo != null and  startChequeNo != '' ">
        START_CHEQUE_NO = #{startChequeNo},
      </if>
      <if test="startDate != null ">
        START_DATE = #{startDate},
      </if>
      <if test="specCode != null and  specCode != '' ">
        SPEC_CODE = #{specCode},
      </if>
      <if test="authUserId != null and  authUserId != '' ">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="othProdType != null and  othProdType != '' ">
        OTH_PROD_TYPE = #{othProdType},
      </if>
      <if test="othBaseAcctNo != null and  othBaseAcctNo != '' ">
        OTH_BASE_ACCT_NO = #{othBaseAcctNo},
      </if>
      <if test="thawOthDocumentType2 != null and  thawOthDocumentType2 != '' ">
        THAW_OTH_DOCUMENT_TYPE2 = #{thawOthDocumentType2},
      </if>
      <if test="underLien != null and  underLien != '' ">
        UNDER_LIEN = #{underLien},
      </if>
      <if test="endDate != null ">
        END_DATE = #{endDate},
      </if>
      <if test="resPriority != null and  resPriority != '' ">
        RES_PRIORITY = #{resPriority},
      </if>
      <if test="paidAmt != null ">
        PAID_AMT = #{paidAmt},
      </if>
      <if test="releaseLawNo != null and  releaseLawNo != '' ">
        RELEASE_LAW_NO = #{releaseLawNo},
      </if>
      <if test="thawOthDocumentType != null and  thawOthDocumentType != '' ">
        THAW_OTH_DOCUMENT_TYPE = #{thawOthDocumentType},
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="maintainType != null and  maintainType != '' ">
        MAINTAIN_TYPE = #{maintainType},
      </if>
      <if test="pledgedAcctNo != null and  pledgedAcctNo != '' ">
        PLEDGED_ACCT_NO = #{pledgedAcctNo},
      </if>
      <if test="channelSeqNo != null and  channelSeqNo != '' ">
        CHANNEL_SEQ_NO = #{channelSeqNo},
      </if>
      <if test="tranType != null and  tranType != '' ">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="othAcctDesc != null and  othAcctDesc != '' ">
        OTH_ACCT_DESC = #{othAcctDesc},
      </if>
      <if test="msgBank != null and  msgBank != '' ">
        MSG_BANK = #{msgBank},
      </if>
      <if test="noOfPayment != null and  noOfPayment != '' ">
        NO_OF_PAYMENT = #{noOfPayment},
      </if>
      <if test="prefix != null and  prefix != '' ">
        PREFIX = #{prefix},
      </if>
      <if test="apprFlag != null and  apprFlag != '' ">
        APPR_FLAG = #{apprFlag},
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID = #{userId},
      </if>
      <if test="termType != null and  termType != '' ">
        TERM_TYPE = #{termType},
      </if>
      <if test="pledgedAcctCcy != null and  pledgedAcctCcy != '' ">
        PLEDGED_ACCT_CCY = #{pledgedAcctCcy},
      </if>
      <if test="isFrozen != null and  isFrozen != '' ">
        IS_FROZEN = #{isFrozen},
      </if>
      <if test="waitSeq != null and  waitSeq != '' ">
        WAIT_SEQ = #{waitSeq},
      </if>
      <if test="helpOption != null and  helpOption != '' ">
        HELP_OPTION = #{helpOption},
      </if>
      <if test="judiciaryDocumentId2 != null ">
        JUDICIARY_DOCUMENT_ID2 = #{judiciaryDocumentId2},
      </if>
      <if test="judiciaryDocumentType2 != null ">
        JUDICIARY_DOCUMENT_TYPE2 = #{judiciaryDocumentType2},
      </if>
      <if test="judiciaryOthDocumentType != null ">
        JUDICIARY_OTH_DOCUMENT_TYPE = #{judiciaryOthDocumentType},
      </if>
      <if test="judiciaryOthDocumentType2 != null ">
        JUDICIARY_OTH_DOCUMENT_TYPE2 = #{judiciaryOthDocumentType2},
      </if>
      <if test="judiciaryOthDocumentId != null ">
        JUDICIARY_OTH_DOCUMENT_ID = #{judiciaryOthDocumentId},
      </if>
      <if test="judiciaryOthOfficerName != null ">
        JUDICIARY_OTH_OFFICER_NAME = #{judiciaryOthOfficerName},
      </if>
      <if test="judiciaryOthDocumentId2 != null ">
        JUDICIARY_OTH_DOCUMENT_ID2 = #{judiciaryOthDocumentId2}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND RESTRAINT_TYPE = #{restraintType}
    AND RES_SEQ_NO = #{resSeqNo}
    AND CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    insert into RB_RESTRAINTS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="programId != null and  programId != '' ">
        PROGRAM_ID = #{programId},
      </if>
      <if test="resAcctRange != null and  resAcctRange != '' ">
        RES_ACCT_RANGE = #{resAcctRange},
      </if>
      <if test="thawDocumentType != null and  thawDocumentType != '' ">
        THAW_DOCUMENT_TYPE,
      </if>
      <if test="thawDocumentId2 != null and  thawDocumentId2 != '' ">
        THAW_DOCUMENT_ID2,
      </if>
      <if test="tranBranch != null and  tranBranch != '' ">
        TRAN_BRANCH,
      </if>
      <if test="restraintType != null and  restraintType != '' ">
        RESTRAINT_TYPE,
      </if>
      <if test="endAmt != null ">
        END_AMT,
      </if>
      <if test="stlSeqNo != null and  stlSeqNo != '' ">
        STL_SEQ_NO,
      </if>
      <if test="paymentMade != null and  paymentMade != '' ">
        PAYMENT_MADE,
      </if>
      <if test="pledgedBaseAcctNo != null and  pledgedBaseAcctNo != '' ">
        PLEDGED_BASE_ACCT_NO,
      </if>
      <if test="deductionJudiciaryName != null and  deductionJudiciaryName != '' ">
        DEDUCTION_JUDICIARY_NAME,
      </if>
      <if test="thawOfficerName != null and  thawOfficerName != '' ">
        THAW_OFFICER_NAME,
      </if>
      <if test="clientNo != null and  clientNo != '' ">
        CLIENT_NO,
      </if>
      <if test="internalKey != null ">
        INTERNAL_KEY,
      </if>
      <if test="pledgedAmt != null ">
        PLEDGED_AMT,
      </if>
      <if test="pledgedAcctType != null and  pledgedAcctType != '' ">
        PLEDGED_ACCT_TYPE,
      </if>
      <if test="lastChangeDate != null ">
        LAST_CHANGE_DATE,
      </if>
      <if test="restraintsStatus != null and  restraintsStatus != '' ">
        RESTRAINTS_STATUS,
      </if>
      <if test="othAcctNo != null and  othAcctNo != '' ">
        OTH_ACCT_NO,
      </if>
      <if test="reference != null and  reference != '' ">
        REFERENCE,
      </if>
      <if test="tranAmt != null ">
        TRAN_AMT,
      </if>
      <if test="interruptFlag != null and  interruptFlag != '' ">
        INTERRUPT_FLAG,
      </if>
      <if test="apprUserId != null and  apprUserId != '' ">
        APPR_USER_ID,
      </if>
      <if test="othAcctCcy != null and  othAcctCcy != '' ">
        OTH_ACCT_CCY,
      </if>
      <if test="resLawNo != null and  resLawNo != '' ">
        RES_LAW_NO,
      </if>
      <if test="judiciaryOthDocumentId2 != null and  judiciaryOthDocumentId2 != '' ">
        JUDICIARY_OTH_DOCUMENT_ID2,
      </if>
      <if test="thawOthOfficerName != null and  thawOthOfficerName != '' ">
        THAW_OTH_OFFICER_NAME,
      </if>
      <if test="company != null and  company != '' ">
        COMPANY,
      </if>
      <if test="subRestraintClass != null and  subRestraintClass != '' ">
        SUB_RESTRAINT_CLASS,
      </if>
      <if test="msgClient != null and  msgClient != '' ">
        MSG_CLIENT,
      </if>
      <if test="toPayAmt != null ">
        TO_PAY_AMT,
      </if>
      <if test="approvalDate != null ">
        APPROVAL_DATE,
      </if>
      <if test="docType != null and  docType != '' ">
        DOC_TYPE,
      </if>
      <if test="deductionLawNo != null and  deductionLawNo != '' ">
        DEDUCTION_LAW_NO,
      </if>
      <if test="deductionLawType != null and  deductionLawType != '' ">
        DEDUCTION_LAW_TYPE,
      </if>
      <if test="judiciaryOfficerName != null and  judiciaryOfficerName != '' ">
        JUDICIARY_OFFICER_NAME,
      </if>
      <if test="thawDocumentId != null and  thawDocumentId != '' ">
        THAW_DOCUMENT_ID,
      </if>
      <if test="thawOthDocumentId != null and  thawOthDocumentId != '' ">
        THAW_OTH_DOCUMENT_ID,
      </if>
      <if test="resSeqNo != null and  resSeqNo != '' ">
        RES_SEQ_NO,
      </if>
      <if test="endChequeNo != null and  endChequeNo != '' ">
        END_CHEQUE_NO,
      </if>
      <if test="lastChangeUserId != null and  lastChangeUserId != '' ">
        LAST_CHANGE_USER_ID,
      </if>
      <if test="sourceModule != null and  sourceModule != '' ">
        SOURCE_MODULE,
      </if>
      <if test="fullFreezeInd != null and  fullFreezeInd != '' ">
        FULL_FREEZE_IND,
      </if>
      <if test="releaseJudiciaryName != null and  releaseJudiciaryName != '' ">
        RELEASE_JUDICIARY_NAME,
      </if>
      <if test="judiciaryDocumentId != null and  judiciaryDocumentId != '' ">
        JUDICIARY_DOCUMENT_ID,
      </if>
      <if test="judiciaryOthOfficerName != null and  judiciaryOthOfficerName != '' ">
        JUDICIARY_OTH_OFFICER_NAME,
      </if>
      <if test="thawDocumentType != null and  thawDocumentType != '' ">
        THAW_DOCUMENT_TYPE,
      </if>
      <if test="thawOthDocumentId2 != null and  thawOthDocumentId2 != '' ">
        THAW_OTH_DOCUMENT_ID2,
      </if>
      <if test="startAmt != null ">
        START_AMT,
      </if>
      <if test="narrative != null and  narrative != '' ">
        NARRATIVE,
      </if>
      <if test="othBankCode != null and  othBankCode != '' ">
        OTH_BANK_CODE,
      </if>
      <if test="term != null and  term != '' ">
        TERM,
      </if>
      <if test="restraintJudiciaryName != null and  restraintJudiciaryName != '' ">
        RESTRAINT_JUDICIARY_NAME,
      </if>
      <if test="judiciaryDocumentType != null and  judiciaryDocumentType != '' ">
        JUDICIARY_DOCUMENT_TYPE,
      </if>
      <if test="startChequeNo != null and  startChequeNo != '' ">
        START_CHEQUE_NO,
      </if>
      <if test="startDate != null ">
        START_DATE,
      </if>
      <if test="specCode != null and  specCode != '' ">
        SPEC_CODE,
      </if>
      <if test="authUserId != null and  authUserId != '' ">
        AUTH_USER_ID,
      </if>
      <if test="othProdType != null and  othProdType != '' ">
        OTH_PROD_TYPE,
      </if>
      <if test="othBaseAcctNo != null and  othBaseAcctNo != '' ">
        OTH_BASE_ACCT_NO,
      </if>
      <if test="judiciaryDocumentId2 != null and  judiciaryDocumentId2 != '' ">
        JUDICIARY_DOCUMENT_ID2,
      </if>
      <if test="thawOthDocumentType2 != null and  thawOthDocumentType2 != '' ">
        THAW_OTH_DOCUMENT_TYPE2,
      </if>
      <if test="underLien != null and  underLien != '' ">
        UNDER_LIEN,
      </if>
      <if test="endDate != null ">
        END_DATE,
      </if>
      <if test="resPriority != null and  resPriority != '' ">
        RES_PRIORITY,
      </if>
      <if test="paidAmt != null ">
        PAID_AMT,
      </if>
      <if test="releaseLawNo != null and  releaseLawNo != '' ">
        RELEASE_LAW_NO,
      </if>
      <if test="judiciaryDocumentType2 != null and  judiciaryDocumentType2 != '' ">
        JUDICIARY_DOCUMENT_TYPE2,
      </if>
      <if test="judiciaryOthDocumentType != null and  judiciaryOthDocumentType != '' ">
        JUDICIARY_OTH_DOCUMENT_TYPE,
      </if>
      <if test="judiciaryOthDocumentType2 != null and  judiciaryOthDocumentType2 != '' ">
        JUDICIARY_OTH_DOCUMENT_TYPE2,
      </if>
      <if test="thawOthDocumentType != null and  thawOthDocumentType != '' ">
        THAW_OTH_DOCUMENT_TYPE,
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP,
      </if>
      <if test="maintainType != null and  maintainType != '' ">
        MAINTAIN_TYPE,
      </if>
      <if test="pledgedAcctNo != null and  pledgedAcctNo != '' ">
        PLEDGED_ACCT_NO,
      </if>
      <if test="channelSeqNo != null and  channelSeqNo != '' ">
        CHANNEL_SEQ_NO,
      </if>
      <if test="tranType != null and  tranType != '' ">
        TRAN_TYPE,
      </if>
      <if test="othAcctDesc != null and  othAcctDesc != '' ">
        OTH_ACCT_DESC,
      </if>
      <if test="msgBank != null and  msgBank != '' ">
        MSG_BANK,
      </if>
      <if test="noOfPayment != null and  noOfPayment != '' ">
        NO_OF_PAYMENT,
      </if>
      <if test="prefix != null and  prefix != '' ">
        PREFIX,
      </if>
      <if test="apprFlag != null and  apprFlag != '' ">
        APPR_FLAG,
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID,
      </if>
      <if test="termType != null and  termType != '' ">
        TERM_TYPE,
      </if>
      <if test="pledgedAcctCcy != null and  pledgedAcctCcy != '' ">
        PLEDGED_ACCT_CCY,
      </if>
      <if test="judiciaryOthDocumentId != null and  judiciaryOthDocumentId != '' ">
        JUDICIARY_OTH_DOCUMENT_ID,
      </if>
      <if test="isFrozen != null and  isFrozen != '' ">
        IS_FROZEN,
      </if>
      <if test="helpOption != null and  helpOption != '' ">
        HELP_OPTION,
      </if>
      <if test="waitSeq != null and  waitSeq != '' ">
        WAIT_SEQ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="thawDocumentType != null and  thawDocumentType != '' ">
        #{thawDocumentType},
      </if>
      <if test="thawDocumentId2 != null and  thawDocumentId2 != '' ">
        #{thawDocumentId2},
      </if>
      <if test="tranBranch != null and  tranBranch != '' ">
        #{tranBranch},
      </if>
      <if test="restraintType != null and  restraintType != '' ">
        #{restraintType},
      </if>
      <if test="endAmt != null ">
        #{endAmt},
      </if>
      <if test="stlSeqNo != null and  stlSeqNo != '' ">
        #{stlSeqNo},
      </if>
      <if test="paymentMade != null and  paymentMade != '' ">
        #{paymentMade},
      </if>
      <if test="pledgedBaseAcctNo != null and  pledgedBaseAcctNo != '' ">
        #{pledgedBaseAcctNo},
      </if>
      <if test="deductionJudiciaryName != null and  deductionJudiciaryName != '' ">
        #{deductionJudiciaryName},
      </if>
      <if test="thawOfficerName != null and  thawOfficerName != '' ">
        #{thawOfficerName},
      </if>
      <if test="clientNo != null and  clientNo != '' ">
        #{clientNo},
      </if>
      <if test="internalKey != null ">
        #{internalKey},
      </if>
      <if test="pledgedAmt != null ">
        #{pledgedAmt},
      </if>
      <if test="pledgedAcctType != null and  pledgedAcctType != '' ">
        #{pledgedAcctType},
      </if>
      <if test="lastChangeDate != null ">
        #{lastChangeDate},
      </if>
      <if test="restraintsStatus != null and  restraintsStatus != '' ">
        #{restraintsStatus},
      </if>
      <if test="othAcctNo != null and  othAcctNo != '' ">
        #{othAcctNo},
      </if>
      <if test="reference != null and  reference != '' ">
        #{reference},
      </if>
      <if test="tranAmt != null ">
        #{tranAmt},
      </if>
      <if test="interruptFlag != null and  interruptFlag != '' ">
        #{interruptFlag},
      </if>
      <if test="apprUserId != null and  apprUserId != '' ">
        #{apprUserId},
      </if>
      <if test="programId != null and  programId != '' ">
        #{programId},
      </if>
      <if test="othAcctCcy != null and  othAcctCcy != '' ">
        #{othAcctCcy},
      </if>
      <if test="resLawNo != null and  resLawNo != '' ">
        #{resLawNo},
      </if>
      <if test="judiciaryOthDocumentId2 != null and  judiciaryOthDocumentId2 != '' ">
        #{judiciaryOthDocumentId2},
      </if>
      <if test="thawOthOfficerName != null and  thawOthOfficerName != '' ">
        #{thawOthOfficerName},
      </if>
      <if test="company != null and  company != '' ">
        #{company},
      </if>
      <if test="subRestraintClass != null and  subRestraintClass != '' ">
        #{subRestraintClass},
      </if>
      <if test="msgClient != null and  msgClient != '' ">
        #{msgClient},
      </if>
      <if test="toPayAmt != null ">
        #{toPayAmt},
      </if>
      <if test="approvalDate != null ">
        #{approvalDate},
      </if>
      <if test="docType != null and  docType != '' ">
        #{docType},
      </if>
      <if test="deductionLawNo != null and  deductionLawNo != '' ">
        #{deductionLawNo},
      </if>
      <if test="deductionLawType != null and  deductionLawType != '' ">
        #{deductionLawType},
      </if>
      <if test="judiciaryOfficerName != null and  judiciaryOfficerName != '' ">
        #{judiciaryOfficerName},
      </if>
      <if test="thawDocumentId != null and  thawDocumentId != '' ">
        #{thawDocumentId},
      </if>
      <if test="thawOthDocumentId != null and  thawOthDocumentId != '' ">
        #{thawOthDocumentId},
      </if>
      <if test="resSeqNo != null and  resSeqNo != '' ">
        #{resSeqNo},
      </if>
      <if test="endChequeNo != null and  endChequeNo != '' ">
        #{endChequeNo},
      </if>
      <if test="lastChangeUserId != null and  lastChangeUserId != '' ">
        #{lastChangeUserId},
      </if>
      <if test="sourceModule != null and  sourceModule != '' ">
        #{sourceModule},
      </if>
      <if test="fullFreezeInd != null and  fullFreezeInd != '' ">
        #{fullFreezeInd},
      </if>
      <if test="releaseJudiciaryName != null and  releaseJudiciaryName != '' ">
        #{releaseJudiciaryName},
      </if>
      <if test="judiciaryDocumentId != null and  judiciaryDocumentId != '' ">
        #{judiciaryDocumentId},
      </if>
      <if test="judiciaryOthOfficerName != null and  judiciaryOthOfficerName != '' ">
        #{judiciaryOthOfficerName},
      </if>
      <if test="thawDocumentType != null and  thawDocumentType != '' ">
        #{thawDocumentType},
      </if>
      <if test="thawOthDocumentId2 != null and  thawOthDocumentId2 != '' ">
        #{thawOthDocumentId2},
      </if>
      <if test="startAmt != null ">
        #{startAmt},
      </if>
      <if test="resAcctRange != null and  resAcctRange != '' ">
        #{resAcctRange},
      </if>
      <if test="narrative != null and  narrative != '' ">
        #{narrative},
      </if>
      <if test="othBankCode != null and  othBankCode != '' ">
        #{othBankCode},
      </if>
      <if test="purposeCode != null and  purposeCode != '' ">
        #{purposeCode},
      </if>
      <if test="term != null and  term != '' ">
        #{term},
      </if>
      <if test="restraintJudiciaryName != null and  restraintJudiciaryName != '' ">
        #{restraintJudiciaryName},
      </if>
      <if test="judiciaryDocumentType != null and  judiciaryDocumentType != '' ">
        #{judiciaryDocumentType},
      </if>
      <if test="startChequeNo != null and  startChequeNo != '' ">
        #{startChequeNo},
      </if>
      <if test="startDate != null ">
        #{startDate},
      </if>
      <if test="specCode != null and  specCode != '' ">
        #{specCode},
      </if>
      <if test="authUserId != null and  authUserId != '' ">
        #{authUserId},
      </if>
      <if test="othProdType != null and  othProdType != '' ">
        #{othProdType},
      </if>
      <if test="othBaseAcctNo != null and  othBaseAcctNo != '' ">
        #{othBaseAcctNo},
      </if>
      <if test="judiciaryDocumentId2 != null and  judiciaryDocumentId2 != '' ">
        #{judiciaryDocumentId2},
      </if>
      <if test="thawOthDocumentType2 != null and  thawOthDocumentType2 != '' ">
        #{thawOthDocumentType2},
      </if>
      <if test="underLien != null and  underLien != '' ">
        #{underLien},
      </if>
      <if test="endDate != null ">
        #{endDate},
      </if>
      <if test="resPriority != null and  resPriority != '' ">
        #{resPriority},
      </if>
      <if test="paidAmt != null ">
        #{paidAmt},
      </if>
      <if test="releaseLawNo != null and  releaseLawNo != '' ">
        #{releaseLawNo},
      </if>
      <if test="judiciaryDocumentType2 != null and  judiciaryDocumentType2 != '' ">
        #{judiciaryDocumentType2},
      </if>
      <if test="judiciaryOthDocumentType != null and  judiciaryOthDocumentType != '' ">
        #{judiciaryOthDocumentType},
      </if>
      <if test="judiciaryOthDocumentType2 != null and  judiciaryOthDocumentType2 != '' ">
        #{judiciaryOthDocumentType2},
      </if>
      <if test="thawOthDocumentType != null and  thawOthDocumentType != '' ">
        #{thawOthDocumentType},
      </if>
      <if test="tranTimestamp != null ">
        #{tranTimestamp},
      </if>
      <if test="maintainType != null and  maintainType != '' ">
        #{maintainType},
      </if>
      <if test="pledgedAcctNo != null and  pledgedAcctNo != '' ">
        #{pledgedAcctNo},
      </if>
      <if test="channelSeqNo != null and  channelSeqNo != '' ">
        #{channelSeqNo},
      </if>
      <if test="tranType != null and  tranType != '' ">
        #{tranType},
      </if>
      <if test="othAcctDesc != null and  othAcctDesc != '' ">
        #{othAcctDesc},
      </if>
      <if test="msgBank != null and  msgBank != '' ">
        #{msgBank},
      </if>
      <if test="noOfPayment != null and  noOfPayment != '' ">
        #{noOfPayment},
      </if>
      <if test="prefix != null and  prefix != '' ">
        #{prefix},
      </if>
      <if test="apprFlag != null and  apprFlag != '' ">
        #{apprFlag},
      </if>
      <if test="userId != null and  userId != '' ">
        #{userId},
      </if>
      <if test="termType != null and  termType != '' ">
        #{termType},
      </if>
      <if test="pledgedAcctCcy != null and  pledgedAcctCcy != '' ">
        #{pledgedAcctCcy},
      </if>
      <if test="judiciaryOthDocumentId != null and  judiciaryOthDocumentId != '' ">
        #{judiciaryOthDocumentId},
      </if>
      <if test="isFrozen != null and  isFrozen != '' ">
        #{isFrozen},
      </if>
      <if test="helpOption != null and  helpOption != '' ">
        #{helpOption},
      </if>
      <if test="waitSeq != null and  waitSeq != '' ">
        #{waitSeq},
      </if>
    </trim>
  </insert>

  <select id="selectByInternalKey" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where INTERNAL_KEY = #{internalKey}
    and client_No = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RES_SEQ_NO+0 DESC
  </select>















  <!-- <select id="getRestraintsForPage" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints"> select <include refid="Base_Column"/> from RB_restraints where INTERNAL_KEY = #{internalKey} AND RESTRAINTS_STATUS != 'E' <if test="clientNo != null and clientNo != ''"> AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR} </if> &lt;!& ndash; 多法人改造 by luocwa&ndash;&gt;<if test="company != null and company != '' ">AND COMPANY = #{company}</if> ORDER BY RES_SEQ_NO DESC</select>-&gt; -->

  <sql id="Base_Column_a">
    <trim suffixOverrides=",">
      a.internal_key,
      a.restraint_type,
      a.res_seq_no,
      a.channel_date,
      a.channel_seq_no,
      a.client_no,
      a.start_date,
      a.term,
      a.term_type,
      a.end_date,
      a.approval_date,
      a.real_restraint_amt,
      a.pledged_amt,
      a.msg_bank,
      a.wait_seq,
      a.tran_branch,
      a.doc_type,
      a.prefix,
      a.msg_client,
      a.stl_seq_no,
      a.reference,
      a.tran_type,
      a.end_amt,
      a.paid_amt,
      a.start_amt,
      a.to_pay_amt,
      a.tran_amt,
      a.appr_flag,
      a.interrupt_flag,
      a.judiciary_document_type,
      a.judiciary_document_type2,
      a.judiciary_oth_document_type,
      a.judiciary_oth_document_type2,
      a.maintain_type,
      a.spec_code,
      a.oth_prod_type,
      a.oth_acct_no,
      a.oth_acct_desc,
      a.oth_base_acct_no,
      a.oth_acct_ccy,
      a.oth_bank_code,
      a.pledged_acct_no,
      a.pledged_base_acct_no,
      a.pledged_acct_ccy,
      a.pledged_acct_type,
      a.deduction_judiciary_name,
      a.deduction_law_no,
      a.end_cheque_no,
      a.full_freeze_ind,
      a.help_option,
      a.is_frozen,
      a.judiciary_document_id,
      a.judiciary_document_id2,
      a.judiciary_oth_document_id,
      a.judiciary_oth_document_id2,
      a.narrative,
      a.no_of_payment,
      a.payment_made,
      a.release_judiciary_name,
      a.release_law_no,
      a.restraint_judiciary_name,
      a.res_law_no,
      a.res_acct_range,
      a.res_priority,
      a.restraints_status,
      a.source_module,
      a.start_cheque_no,
      a.sub_restraint_class,
      a.under_lien,
      a.appr_user_id,
      a.auth_user_id,
      a.tran_timestamp,
      a.user_id,
      a.program_id,
      a.last_change_date,
      a.last_change_user_id,
      a.judiciary_officer_name,
      a.judiciary_oth_officer_name,
      a.thaw_document_id,
      a.thaw_document_id2,
      a.thaw_officer_name,
      a.thaw_oth_document_id,
      a.thaw_oth_document_id2,
      a.thaw_oth_document_type,
      a.thaw_oth_document_type2,
      a.thaw_oth_officer_name,
      a.thaw_document_type2,
      a.thaw_document_type,
      a.company,
      a.reaccount_cd,
      a.reserve,
      a.deduction_law_type,
    </trim>
  </sql>

  <select id="getRestraintsForPage" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select distinct<include refid="Base_Column_a"/>
    from rb_restraints a
    where a.INTERNAL_KEY= #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resSeqNo != null and resSeqNo != ''">
      AND a.RES_SEQ_NO = #{resSeqNo, jdbcType=VARCHAR}
    </if>
    <if test="restraintsStatus != null and restraintsStatus != ''">
      AND a.RESTRAINTS_STATUS = #{restraintsStatus, jdbcType=VARCHAR}
    </if>
    ORDER BY a.TRAN_TIMESTAMP DESC
  </select>

  <select id="selectAcctRestraintByInternalKey" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where INTERNAL_KEY = #{internalKey}
    <if test="subResClass != null and subResClass != ''">
      AND SUB_RESTRAINT_CLASS = #{subResClass}
    </if>
    AND RESTRAINTS_STATUS !='E'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RES_SEQ_NO DESC
  </select>

  <!--  <select id="selectByInternalKeyByPage" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"-->
  <!--          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">-->
  <!--    select <include refid="Base_Column"/>-->
  <!--    from RB_restraints-->
  <!--    where INTERNAL_KEY = #{baseParam.internalKey}-->
  <!--  </select>-->
  <select id="selectNotEndRestraints" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where 1=1
    <if test="internalKey != null and internalKey!=''">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="clientNo!=null and clientNo !=''">
      AND  CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND RESTRAINTS_STATUS = 'A' ORDER BY RES_SEQ_NO ASC
  </select>

  <select id="getJudicialFreezeRestraints" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT <include refid="Base_Column"/>
    FROM (SELECT t1.*
    FROM RB_restraints t1
    LEFT JOIN FM_RESTRAINT_TYPE t2 ON t1.restraint_type = t2.restraint_type
    WHERE t1.INTERNAL_KEY = #{internalKey}
    AND t1.CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    AND t2.ah_bu_flag = 'Y'
    AND RESTRAINTS_STATUS = 'A') C
  </select>

  <select id="selectNotEndNatureRestraints" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where INTERNAL_KEY = #{internalKey}
    AND RESTRAINTS_STATUS = 'A'
    AND SUB_RESTRAINT_CLASS = 'N'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RES_SEQ_NO ASC
  </select>
  <select id="selectExpirtResForDeal" parameterType="map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT <include refid="Base_Column"/>

    FROM RB_restraints
    WHERE (
    <![CDATA[
            (end_date IS NOT NULL AND end_date <= #{baseParam.runDate} AND end_date >= #{baseParam.lastRunDate}) or
            (start_date IS NOT NULL AND start_date <= #{baseParam.runDate,jdbcType=DATE} AND start_date > #{baseParam.lastRunDate,jdbcType=DATE})
          )
      ]]>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY internal_key

  </select>
  <!-- <select id="selectExpirtResForDealSplit" parameterType="java.util.Map"  resultType="java.util.Map">
       <![CDATA[
     SELECT COUNT(1) ROW_COUNT
      FROM RB_restraints
     WHERE ((end_date IS NOT NULL AND end_date <= #{runDate} AND end_date >= #{lastRunDate}) or
           (start_date IS NOT NULL AND start_date <= #{runDate} AND start_date > #{lastRunDate}))

   ]]>
   </select>  -->
  <!-- optimize SQL -->
  <select id="selectNotEndResByChannelSeqNo" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where CHANNEL_SEQ_NO = #{channelSeqNo}
    AND RESTRAINTS_STATUS != 'E'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectNotEndResJustByChannelSeqNo" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where CHANNEL_SEQ_NO = #{channelSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getAcctNatureRes" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select mr.*
    FROM
    RB_acct mb,
    RB_restraints mr,
    RB_acct_nature_restraints mn
    WHERE
    mb.INTERNAL_KEY = mr.INTERNAL_KEY
    AND mb.ACCT_NATURE = mn.ACCT_NATURE
    AND mr.RESTRAINT_TYPE = mn.RESTRAINT_TYPE
    AND mb.INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectManualNotEndRestraints" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0">
      AND RESTRAINT_TYPE in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND RESTRAINTS_STATUS != 'E' ORDER BY RES_PRIORITY DESC ,RES_SEQ_NO+0 ASC
  </select>
  <select id="selectRestraintsByStatus" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0 ">
      AND RESTRAINT_TYPE in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="restraintsStatus != null and restraintsStatus.length > 0">
      AND RESTRAINTS_STATUS in
      <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RES_PRIORITY DESC,RES_SEQ_NO+0 ASC
  </select>
  <select id="selectRestraintsByStatusButNature" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0 ">
      AND RESTRAINT_TYPE in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="restraintsStatus != null and restraintsStatus.length > 0 ">
      AND RESTRAINTS_STATUS in
      <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="startDate != null and endDate != null">
      AND   (APPROVAL_DATE between  #{startDate} and  #{endDate})
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND (RESTRAINT_JUDICIARY_NAME is null OR RESTRAINT_JUDICIARY_NAME not like '%国安局%')
    AND SUB_RESTRAINT_CLASS != 'N'
    ORDER BY START_DATE DESC ,RES_PRIORITY DESC,RES_SEQ_NO ASC
  </select>

  <select id="getRestraintsByInternalKey" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0">
      AND RESTRAINT_TYPE in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="restraintsStatus != null and restraintsStatus.length > 0">
      AND RESTRAINTS_STATUS in
      <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RES_PRIORITY DESC,RES_SEQ_NO+0 ASC
  </select>

  <select id="getRestraintsByInternalKeyByPage" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0">
      AND RESTRAINT_TYPE in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="restraintsStatus != null and restraintsStatus.length > 0">
      AND RESTRAINTS_STATUS in
      <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RES_PRIORITY DESC,RES_SEQ_NO+0 ASC
  </select>

  <select id="getRestraintsByInternalKeysByPage" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY in
    <foreach item="item" index="index" collection="internalKeys" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0">
      AND RESTRAINT_TYPE in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="restraintsStatus != null and restraintsStatus.length > 0 ">
      AND RESTRAINTS_STATUS in
      <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RES_PRIORITY DESC,RES_SEQ_NO+0 ASC
  </select>

  <select id="getRestraint" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where RES_SEQ_NO = #{resSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    and RESTRAINTS_STATUS != 'E'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getRestraintLock" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where RES_SEQ_NO = #{resSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and RESTRAINTS_STATUS != 'E'
    for update
  </select>

  <select id="getRestraintE" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where RES_SEQ_NO = #{resSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getRestraintAll" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where RES_SEQ_NO = #{resSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectBefSeqAmtBySeqNoA" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT <include refid="Base_Column"/>
    FROM RB_restraints
    WHERE RESTRAINTS_STATUS =  'A'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    AND restraint_type in
    <foreach item="item" index="index" collection="resType" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND res_priority = (select res_priority
    from RB_restraints
    where res_seq_no	= #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
    AND WAIT_SEQ &lt; (select WAIT_SEQ
    from RB_restraints
    where res_seq_no	= #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
    AND internal_key = (select internal_key
    from RB_restraints
    where res_seq_no = #{resSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
  </select>
  <!-- Query account restriction information through internalKey, constraintType, status -->
  <select id="getRbRestraintsByThreeParam" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT <include refid="Base_Column"/>
    FROM RB_restraints
    <where>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey}
      </if>
      <if test="restraintType != null and restraintType != ''">
        AND RESTRAINT_TYPE = #{restraintType}
      </if>
      <if test="restraintsStatus != null and restraintsStatus != ''">
        AND RESTRAINTS_STATUS = #{restraintsStatus}
      </if>
      <if test="clientNo != null and clientNo != ''">
        AND CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <select id="getRbRestraints" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM RB_restraints
    WHERE
    INTERNAL_KEY in (SELECT INTERNAL_KEY from RB_acct where CLIENT_NO= #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
  </select>

  <select id="queryRestraintsByResSeqNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT <include refid="Base_Column"/>
    FROM RB_restraints
    <where>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo}
      </if>
      <if test="restraintType != null and restraintType != ''">
        AND RESTRAINT_TYPE = #{restraintType}
      </if>
      <if test="restraintsStatus != null and restraintsStatus != ''">
        AND RESTRAINTS_STATUS = #{restraintsStatus}
      </if>
      <if test="clientNo != null and clientNo != ''">
        AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <select id="queryRestraintsByInternalKey" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT <include refid="Base_Column"/>
    FROM RB_restraints
    <where>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey}
      </if>
      <if test="restraintType != null and restraintType != ''">
        AND RESTRAINT_TYPE = #{restraintType}
      </if>
      <if test="restraintsStatus != null and restraintsStatus != ''">
        AND RESTRAINTS_STATUS = #{restraintsStatus}
      </if>
      <if test="clientNo != null and clientNo != ''">
        AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>
  <select id="querySFRestraintsByBranch" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select mr.*
    FROM
    RB_restraints mr
    <where>
      <if test="tranBranch != null and tranBranch != ''">
        AND mr.TRAN_BRANCH = #{tranBranch}
      </if>
      <if test="restraintType != null and restraintType .length > 0 ">
        AND mr.RESTRAINT_TYPE  in
        <foreach item="item" index="index" collection="restraintType" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="restraintsStatus != null and restraintsStatus.length > 0 ">
        AND mr.RESTRAINTS_STATUS  in
        <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="startDate != null and endDate != null">
        AND   (APPROVAL_DATE between  #{startDate} and  #{endDate})
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND mr.COMPANY = #{company}
      </if>
      AND ((mr.RESTRAINT_JUDICIARY_NAME is not null and mr.RESTRAINT_JUDICIARY_NAME  not like '%国安局%') or mr.RESTRAINT_JUDICIARY_NAME is  null)
      ORDER BY mr.START_DATE DESC ,mr.RES_PRIORITY DESC ,mr.RES_SEQ_NO ASC
    </where>
  </select>


  <select id="selectSfRestraintsStart" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE RESTRAINTS_STATUS =  'A'
    <if test="startDate != null ">
      and START_DATE =#{startDate,jdbcType=DATE}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND restraint_type in
    <foreach item="item" index="index" collection="resType" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>


  <select id="selectSfRestraintsStartAndInternalKey" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE RESTRAINTS_STATUS =  'A'
    <if test="startDate != null ">
      and START_DATE =#{startDate}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    AND restraint_type in
    <foreach item="item" index="index" collection="resType" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>


  <select id="selectSfRestraintsEnd" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE RESTRAINTS_STATUS =  'E'
    and END_DATE =#{endDate}
    AND restraint_type in
    <foreach item="item" index="index" collection="resType" open="(" separator="," close=")">
      #{item}
    </foreach>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectRestraintsByReference" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE RESTRAINTS_STATUS != 'E'
    <if test="reference != null and reference != ''">
      AND REFERENCE= #{reference}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getNumRestraints" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE restraintsStatus != 'E'
    <if test="channelDate != null">
      AND CHANNEL_DATE= #{channelDate,jdbcType=DATE}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectBabRestraints" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE RESTRAINTS_STATUS != 'E'
    and internal_key =#{internalKey}
    and client_no = #{clientNo}
    AND res_seq_no in
    <foreach item="item" index="index" collection="resSeqNo" open="(" separator="," close=")">
      #{item}
    </foreach>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectRestraintsByType" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0">
      AND RESTRAINT_TYPE in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="restraintsStatus != null and restraintsStatus.length > 0">
      AND RESTRAINTS_STATUS in
      <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY RESTRAINT_TYPE ASC
  </select>

  <select id="selectAcctRestraintByKey" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE internal_key BETWEEN #{startKey} and #{endKey}
    AND end_date IS NOT NULL
    AND end_date <![CDATA[  <=  ]]> #{runDate,jdbcType=DATE}
    AND RESTRAINTS_STATUS IN ('A','F')
    AND RESTRAINT_TYPE NOT IN ('YC2')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectAcctRestraintByKey1" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE  internal_key BETWEEN #{startKey} and #{endKey}
    AND start_date IS NOT NULL
    AND start_date <![CDATA[ <= ]]> #{runDate,jdbcType=DATE}
    AND start_date <![CDATA[ > ]]> #{lastRunDate,jdbcType=DATE}
    AND RESTRAINTS_STATUS ='F'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getResByChannelSeqNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE  CHANNEL_SEQ_NO = #{channelSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <update id="updateMbRestraintsRealRestraintAmt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    update RB_RESTRAINTS
    <set>
      <if test="realRestraintAmt != null ">
        REAL_RESTRAINT_AMT  = #{realRestraintAmt},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND RESTRAINT_TYPE = #{restraintType}
    AND RES_SEQ_NO = #{resSeqNo}
    AND CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updResStraintsStatusByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    update RB_RESTRAINTS
    <set>
      <if test="restraintsStatus != null ">
        RESTRAINTS_STATUS  = #{restraintsStatus},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND RESTRAINT_TYPE = #{restraintType}
    AND RES_SEQ_NO = #{resSeqNo}
    AND CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getSeqInfoByMainSeq" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_restraints
    where INTERNAL_KEY = #{internalKey}
    AND CLIENT_NO = #{clientNo}
    AND STL_SEQ_NO = #{stlSeqNo}
    AND RESTRAINTS_STATUS != 'E'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getAhBuAcctCountByClient" parameterType="java.util.HashMap" resultType="int">
    SELECT
    count(1)
    FROM
    RB_RESTRAINTS
    WHERE  CLIENT_NO = #{clientNo}
    AND restraints_status = 'A'
    <if test="ahBuList != null and ahBuList.size() > 0">
      AND RESTRAINT_TYPE IN
      <foreach collection="ahBuList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="getRestraintsCountByInternalKey" parameterType="java.util.HashMap" resultType="int">
    SELECT
    count(1)
    FROM
    RB_RESTRAINTS
    WHERE  CLIENT_NO = #{clientNo}
    and internal_key = #{internalKey}
    AND restraints_status = 'A'
  </select>

  <select id="getRestraintsByYc" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE
    RESTRAINT_TYPE = 'YC2'
    AND RESTRAINTS_STATUS = 'A'
    AND START_DATE = #{runDate}
    <if test="internalKey != null">
      AND INTERNAL_KEY= #{internalKey}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getRestraintsByDoc" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select b.* from rb_acct a inner join RB_RESTRAINTS b
    on  a.INTERNAL_KEY = b.INTERNAL_KEY
    where a.DOCUMENT_ID = #{documentId} and a.DOCUMENT_TYPE = #{documentType} and a.ACCT_STATUS !='C'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    and b.RESTRAINT_TYPE = 'YC2' and RESTRAINTS_STATUS = 'A'
  </select>

  <select id="getRestraintsNotE" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT *
    FROM rb_restraints
    WHERE
    RESTRAINTS_STATUS != 'E'
    <if test="internalKey != null">
      AND INTERNAL_KEY= #{internalKey}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND (RESTRAINT_TYPE != 'YC2' OR (RESTRAINT_TYPE = 'YC2' AND START_DATE = #{runDate}))
  </select>

  <select id="selectRestraintListByStlSeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where STL_SEQ_NO = #{stlSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    AND RESTRAINTS_STATUS != 'E'
  </select>

  <select id="selectYCByAllAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    SELECT <include refid="Base_Column"/>
    FROM rb_restraints
    WHERE
    RESTRAINT_TYPE = 'YC2'
    AND RESTRAINTS_STATUS = 'A'
    AND START_DATE = #{runDate}
    <if test="internalKey != null">
      AND INTERNAL_KEY= #{internalKey}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!--多法人改造 by luocwa  -->
    <if test="company != null and company.length() > 0">
      AND COMPANY = #{company}
    </if>
  </select>

  <!-- WLMQ unit confirmation distribution begin -->
  <select id="selectRestraintsAhInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where RESTRAINTS_STATUS = 'A'
    AND INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- WLMQ unit confirmation distribution end -->
  <select id="selectNoResRestraints" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="resTypes != null and resTypes.length > 0">
      AND RESTRAINT_TYPE NOT in
      <foreach item="item" index="index" collection="resTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="restraintsStatus != null and restraintsStatus.length > 0">
      AND RESTRAINTS_STATUS in
      <foreach item="item" index="index" collection="restraintsStatus" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <update id="updateByWaitSeq" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
    update RB_RESTRAINTS
    <set>
      <if test="waitSeq != null ">
        WAIT_SEQ  = #{waitSeq},
      </if>
      <if test = "lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND RESTRAINT_TYPE = #{restraintType}
    AND RES_SEQ_NO = #{resSeqNo}
    AND CLIENT_NO = #{clientNo}
    <!--多法人改造 by luocwa  -->
    <if test="company != null and company.length() > 0">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
