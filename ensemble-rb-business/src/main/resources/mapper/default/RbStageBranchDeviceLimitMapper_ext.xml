<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchDeviceLimit">

	<select id="selectLimitsBranchAndStageCode"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchDeviceLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_BRANCH_DEVICE_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
				<if test="branch != null and  branch != '' ">
					BRANCH = #{branch}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="selectLimitsBranchStageCodeAndDevice"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchDeviceLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_BRANCH_DEVICE_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
				<if test="branch != null and  branch != '' ">
					BRANCH = #{branch}  AND
				</if>
				<if test="sourceType != null and  sourceType != '' ">
					SOURCE_TYPE = #{sourceType}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="selectLimitsByStageCode"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchDeviceLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_BRANCH_DEVICE_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="deleteStageBranchDeviceLimitsByStageCode"  parameterType="java.util.Map" >
		DELETE FROM
		RB_STAGE_BRANCH_DEVICE_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<update id="updateLimitsBranchStageCodeAndDevice"  parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchDeviceLimit" >
		UPDATE
		RB_STAGE_BRANCH_DEVICE_LIMIT
		<include refid="Base_Set"/>
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
				<if test="branch != null and  branch != '' ">
					BRANCH = #{branch}  AND
				</if>
				<if test="sourceType != null and  sourceType != '' ">
					SOURCE_TYPE = #{sourceType}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

</mapper>
