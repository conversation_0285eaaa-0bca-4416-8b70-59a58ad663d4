<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabLost">

	<select id="selectRbBabLostByVoucherNo"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabLost">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_BAB_LOST
		<where>
			<trim suffixOverrides="AND">
				<if test="billPrefix != null and  billPrefix != '' ">
					BILL_PREFIX = #{billPrefix}  AND
				</if>
				<if test="billType != null and  billType != '' ">
					BILL_TYPE = #{billType}  AND
				</if>
				<if test="billVoucherNo != null and  billVoucherNo != '' ">
					BILL_VOUCHER_NO = #{billVoucherNo}  AND
				</if>
				<if test="billDocType != null and  billDocType != '' ">
					BILL_DOC_TYPE = #{billDocType}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>


<!-- Query based on primary key -->
	<select id="selectByLostNo"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabLost">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_BAB_LOST
		<where>
		  LOST_STATUS != 'C'
			<trim suffixOverrides="AND">
				<if test="lostNo != null and  lostNo != '' ">
					AND	LOST_NO = #{lostNo}
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					AND COMPANY = #{company}
				</if>
			</trim>
		</where>
	</select>


	<update id="updateRbBabLostStatus"  parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabLost" >
		UPDATE RB_BAB_LOST
		<set>
			<if test="lostStatus != null and  lostStatus != '' ">
				LOST_STATUS = #{lostStatus},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="lostNo != null and  lostNo != '' ">
						LOST_NO = #{lostNo} AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					 CLIENT_NO = #{clientNo} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

</mapper>
