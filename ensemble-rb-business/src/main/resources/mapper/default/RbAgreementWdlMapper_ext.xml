<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWdl">

	<select id="selectAgreementWdlInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWdl">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_WDL
		where AGREEMENT_STATUS in('A','N')
		<if test="internalKey != null and internalKey != ''">
			and INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>
		<if test="agreementType != null and agreementType != ''">
			and AGREEMENT_TYPE = #{agreementType}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>

	</select>

	<select id="selectUnsignAgreementWdlInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWdl">
	select
	<include refid="Base_Column"/>
	from RB_AGREEMENT_WDL
	where AGREEMENT_STATUS='N'
	<if test="internalKey != null and internalKey != ''">
		and INTERNAL_KEY = #{internalKey}
	</if>
	<if test="clientNo != null and clientNo != ''">
		and CLIENT_NO = #{clientNo}
	</if>
	<if test="agreementType != null and agreementType != ''">
		and AGREEMENT_TYPE = #{agreementType}
	</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>

</select>

	<select id="selectByAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWdl">
		select
		<include refid="Base_Column"/>
		from RB_AGREEMENT_WDL
		<where>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		<if test="baseAcctNo != null and  baseAcctNo != '' ">
			and BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="prodType != null and  prodType != '' ">
			and PROD_TYPE = #{prodType}
		</if>
		<if test="acctCcy != null and  acctCcy != '' ">
			and ACCT_CCY = #{acctCcy}
		</if>
		<if test="acctSeqNo != null and  acctSeqNo != '' ">
			and ACCT_SEQ_NO = #{acctSeqNo}
		</if>
		<if test="clientNo != null and clientNo != ''">
			and CLIENT_NO = #{clientNo}
		</if>
		<if test="agreementType != null and agreementType != ''">
			and AGREEMENT_TYPE = #{agreementType}
		</if>
		<if test="agreementStatus != null and agreementStatus != ''">
			and AGREEMENT_STATUS = #{agreementStatus}
		</if>
		<if test="finProdType != null and  finProdType != '' ">
			and FIN_PROD_TYPE = #{finProdType}
		</if>
		</where>
	</select>

</mapper>
