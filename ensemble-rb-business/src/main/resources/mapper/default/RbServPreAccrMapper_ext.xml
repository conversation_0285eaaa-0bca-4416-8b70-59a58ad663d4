<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbServPreAccr">

    
  <select id="selectBySeDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServPreAccr" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_PRE_ACCR
    WHERE  PRE_ACCR_STATUS != 'D'
<!--    <if test="startDate != null and endDate != ''">-->
    <if test="startDate != null and endDate != null">
      and PRE_ACCR_DATE BETWEEN #{startDate} and #{endDate}
    </if>
    <if test="preAccrNo != null and preAccrNo != ''">
      and PRE_ACCR_NO = #{preAccrNo}
    </if>
    <if test="branch != null and branch != ''">
      and BRANCH = #{branch}
    </if>
    <if test="userId != null and userId != ''">
      and OPER_USER_ID = #{userId}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by PRE_ACCR_NO desc
  </select>

  <select id="selectByPreAccrNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServPreAccr" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_PRE_ACCR
    WHERE  1=1
    <if test="preAccrNo != null and preAccrNo != ''">
    and PRE_ACCR_NO = #{preAccrNo}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
