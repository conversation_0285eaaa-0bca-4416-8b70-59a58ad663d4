<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.fm.model.FmChannel">
	<select id="selectChannelListByChannels" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.fm.model.FmChannel">
		select <include refid="Base_Column"/>
		from FM_CHANNEL
		where CHANNEL IN
		<foreach collection="channels" item="channel" index="index" open="(" close=")" separator=",">
			#{channel,jdbcType=VARCHAR}
		</foreach>
	</select>
</mapper>