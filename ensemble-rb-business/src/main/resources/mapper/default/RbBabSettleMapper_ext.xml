<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabSettle">

	<select id="selectListByAcceptContractNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabSettle" parameterType="java.util.Map" >

		select <include refid="Base_Column"/>
		from RB_BAB_SETTLE
		where ACCEPT_CONTRACT_NO = #{acceptContractNo,jdbcType=DECIMAL}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>


	<select id="selectRbBabSettleBySettleClass" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabSettle" parameterType="java.util.Map" >

		select <include refid="Base_Column"/>
		from RB_BAB_SETTLE
		where ACCEPT_CONTRACT_NO = #{acceptContractNo}
		AND  SETTLE_CLASS = #{settleClass}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="selectSettleListBySettleClass" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabSettle" parameterType="java.util.Map" >

		select <include refid="Base_Column"/>
		from RB_BAB_SETTLE
		where ACCEPT_CONTRACT_NO = #{acceptContractNo}
		AND  SETTLE_CLASS = #{settleClass}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<delete id="deleteByacceptContractNo" parameterType="java.util.Map">
		DELETE FROM RB_BAB_SETTLE
		<where>
			<trim suffixOverrides="AND">
				<if test="acceptContractNo != null and  acceptContractNo != '' ">
					ACCEPT_CONTRACT_NO = #{acceptContractNo}  AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</delete>


</mapper>
