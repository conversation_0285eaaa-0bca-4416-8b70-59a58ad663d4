<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.cm.reg.EgRbFeeAmortizeAgr">

    <sql id="Table_Name">
        ENS_EG.EG_RB_FEE_AMORTIZE_AGR
    </sql>

    <sql id="Base_Column">
        <trim suffixOverrides=",">
            SC_SEQ_NO,
            TRAN_BRANCH,
            FEE_TYPE,
            TRAN_DATE,
            EFFECT_DATE,
            END_DATE,
            CHARGE_MODE,
            PROD_TYPE,
            CLIENT_NO,
            AMORTIZE_CCY,
            AMORTIZE_FREQUENCY,
            AMORTIZE_TOTAL_AMT,
            BUSI_NO,
            DEAL_FLAG,
            REFERENCE,
        </trim>
    </sql>

    <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.model.cm.reg.EgRbFeeAmortizeAgr">
        <result property="scSeqNo" column="SC_SEQ_NO"/>
        <result property="tranBranch" column="TRAN_BRANCH"/>
        <result property="feeType" column="FEE_TYPE"/>
        <result property="tranDate" column="TRAN_DATE"/>
        <result property="effectDate" column="EFFECT_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="chargeMode" column="CHARGE_MODE"/>
        <result property="prodType" column="PROD_TYPE"/>
        <result property="clientNo" column="CLIENT_NO"/>
        <result property="amortizeCcy" column="AMORTIZE_CCY"/>
        <result property="amortizeFrequency" column="AMORTIZE_FREQUENCY"/>
        <result property="amortizeTotalAmt" column="AMORTIZE_TOTAL_AMT"/>
        <result property="busiNo" column="BUSI_NO"/>
        <result property="dealFlag" column="DEAL_FLAG"/>
        <result property="reference" column="REFERENCE"/>
    </resultMap>

    <sql id="Base_Where">
        <where>
            <if test="scSeqNo != null and  scSeqNo != '' ">
                AND SC_SEQ_NO = #{scSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="feeType != null and  feeType != '' ">
                AND FEE_TYPE = #{feeType,jdbcType=VARCHAR}
            </if>
            <if test="tranDate != null ">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE}
            </if>
            <if test="effectDate != null ">
                AND EFFECT_DATE = #{effectDate,jdbcType=DATE}
            </if>
            <if test="endDate != null ">
                AND END_DATE = #{endDate,jdbcType=DATE}
            </if>
            <if test="chargeMode != null and  chargeMode != '' ">
                AND CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="amortizeCcy != null and  amortizeCcy != '' ">
                AND AMORTIZE_CCY = #{amortizeCcy,jdbcType=VARCHAR}
            </if>
            <if test="amortizeFrequency != null and  amortizeFrequency != '' ">
                AND AMORTIZE_FREQUENCY = #{amortizeFrequency,jdbcType=VARCHAR}
            </if>
            <if test="amortizeTotalAmt != null ">
                AND AMORTIZE_TOTAL_AMT = #{amortizeTotalAmt,jdbcType=DECIMAL}
            </if>
            <if test="busiNo != null and  busiNo != '' ">
                AND BUSI_NO = #{busiNo,jdbcType=VARCHAR}
            </if>
            <if test="dealFlag != null and  dealFlag != '' ">
                AND DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="reference != null and  reference != '' ">
                AND REFERENCE = #{reference,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="PrimaryKey_Where">
        <where>
            <if test="scSeqNo != null and  scSeqNo != '' ">
                AND SC_SEQ_NO = #{scSeqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="Base_Set">
        <set>
            <if test="scSeqNo != null and scSeqNo != '' ">
                SC_SEQ_NO = #{scSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="tranBranch != null and tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="feeType != null and feeType != '' ">
                FEE_TYPE = #{feeType,jdbcType=VARCHAR},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE = #{effectDate,jdbcType=DATE},
            </if>
            <if test="endDate != null ">
                END_DATE = #{endDate,jdbcType=DATE},
            </if>
            <if test="chargeMode != null and chargeMode != '' ">
                CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
            </if>
            <if test="prodType != null and prodType != '' ">
                PROD_TYPE = #{prodType,jdbcType=VARCHAR},
            </if>
            <if test="amortizeCcy != null and amortizeCcy != '' ">
                AMORTIZE_CCY = #{amortizeCcy,jdbcType=VARCHAR},
            </if>
            <if test="amortizeFrequency != null and amortizeFrequency != '' ">
                AMORTIZE_FREQUENCY = #{amortizeFrequency,jdbcType=VARCHAR},
            </if>
            <if test="amortizeTotalAmt != null ">
                AMORTIZE_TOTAL_AMT = #{amortizeTotalAmt,jdbcType=DECIMAL},
            </if>
            <if test="busiNo != null and busiNo != '' ">
                BUSI_NO = #{busiNo,jdbcType=VARCHAR},
            </if>
            <if test="dealFlag != null and dealFlag != '' ">
                DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="reference != null and reference != '' ">
                REFERENCE = #{reference,jdbcType=VARCHAR},
            </if>
        </set>
    </sql>

    <sql id="Base_Select">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </sql>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'">${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'">*</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}
        </if>
    </sql>

    <sql id="comet_row_num_step_column">
        <if test="cometSqlType == 'total'">count(*) AS TOTAL</if>
        <if test="cometSqlType == 'offset'">*</if>
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scSeqNo != null ">SC_SEQ_NO,</if>
            <if test="tranBranch != null ">TRAN_BRANCH,</if>
            <if test="feeType != null ">FEE_TYPE,</if>
            <if test="tranDate != null ">TRAN_DATE,</if>
            <if test="effectDate != null ">EFFECT_DATE,</if>
            <if test="endDate != null ">END_DATE,</if>
            <if test="chargeMode != null ">CHARGE_MODE,</if>
            <if test="prodType != null ">PROD_TYPE,</if>
            <if test="clientNo != null ">CLIENT_NO,</if>
            <if test="amortizeCcy != null ">AMORTIZE_CCY,</if>
            <if test="amortizeFrequency != null ">AMORTIZE_FREQUENCY,</if>
            <if test="amortizeTotalAmt != null ">AMORTIZE_TOTAL_AMT,</if>
            <if test="busiNo != null ">BUSI_NO,</if>
            <if test="dealFlag != null ">DEAL_FLAG,</if>
            <if test="reference != null ">REFERENCE,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scSeqNo != null ">#{scSeqNo,jdbcType=VARCHAR},</if>
            <if test="tranBranch != null ">#{tranBranch,jdbcType=VARCHAR},</if>
            <if test="feeType != null ">#{feeType,jdbcType=VARCHAR},</if>
            <if test="tranDate != null ">#{tranDate,jdbcType=DATE},</if>
            <if test="effectDate != null ">#{effectDate,jdbcType=DATE},</if>
            <if test="endDate != null ">#{endDate,jdbcType=DATE},</if>
            <if test="chargeMode != null ">#{chargeMode,jdbcType=VARCHAR},</if>
            <if test="prodType != null ">#{prodType,jdbcType=VARCHAR},</if>
            <if test="clientNo != null ">#{clientNo,jdbcType=VARCHAR},</if>
            <if test="amortizeCcy != null ">#{amortizeCcy,jdbcType=VARCHAR},</if>
            <if test="amortizeFrequency != null ">#{amortizeFrequency,jdbcType=VARCHAR},</if>
            <if test="amortizeTotalAmt != null ">#{amortizeTotalAmt,jdbcType=DECIMAL},</if>
            <if test="busiNo != null ">#{busiNo,jdbcType=VARCHAR},</if>
            <if test="dealFlag != null ">#{dealFlag,jdbcType=VARCHAR},</if>
            <if test="reference != null ">#{reference,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>

    <update id="updateByPrimaryKey">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>


    <update id="updateByEntity">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="s.scSeqNo != null and s.scSeqNo != '' ">
                SC_SEQ_NO = #{s.scSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.tranBranch != null and s.tranBranch != '' ">
                TRAN_BRANCH = #{s.tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.feeType != null and s.feeType != '' ">
                FEE_TYPE = #{s.feeType,jdbcType=VARCHAR},
            </if>
            <if test="s.tranDate != null ">
                TRAN_DATE = #{s.tranDate,jdbcType=DATE},
            </if>
            <if test="s.effectDate != null ">
                EFFECT_DATE = #{s.effectDate,jdbcType=DATE},
            </if>
            <if test="s.endDate != null ">
                END_DATE = #{s.endDate,jdbcType=DATE},
            </if>
            <if test="s.chargeMode != null and s.chargeMode != '' ">
                CHARGE_MODE = #{s.chargeMode,jdbcType=VARCHAR},
            </if>
            <if test="s.prodType != null and s.prodType != '' ">
                PROD_TYPE = #{s.prodType,jdbcType=VARCHAR},
            </if>
            <if test="s.amortizeCcy != null and s.amortizeCcy != '' ">
                AMORTIZE_CCY = #{s.amortizeCcy,jdbcType=VARCHAR},
            </if>
            <if test="s.amortizeFrequency != null and s.amortizeFrequency != '' ">
                AMORTIZE_FREQUENCY = #{s.amortizeFrequency,jdbcType=VARCHAR},
            </if>
            <if test="s.amortizeTotalAmt != null ">
                AMORTIZE_TOTAL_AMT = #{s.amortizeTotalAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.busiNo != null and s.busiNo != '' ">
                BUSI_NO = #{s.busiNo,jdbcType=VARCHAR},
            </if>
            <if test="s.dealFlag != null and s.dealFlag != '' ">
                DEAL_FLAG = #{s.dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.reference != null and s.reference != '' ">
                REFERENCE = #{s.reference,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="w.scSeqNo != null and w.scSeqNo != '' ">
                AND SC_SEQ_NO = #{w.scSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.tranBranch != null and w.tranBranch != '' ">
                AND TRAN_BRANCH = #{w.tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.feeType != null and w.feeType != '' ">
                AND FEE_TYPE = #{w.feeType,jdbcType=VARCHAR}
            </if>
            <if test="w.tranDate != null ">
                AND TRAN_DATE = #{w.tranDate,jdbcType=DATE}
            </if>
            <if test="w.effectDate != null ">
                AND EFFECT_DATE = #{w.effectDate,jdbcType=DATE}
            </if>
            <if test="w.endDate != null ">
                AND END_DATE = #{w.endDate,jdbcType=DATE}
            </if>
            <if test="w.chargeMode != null and w.chargeMode != '' ">
                AND CHARGE_MODE = #{w.chargeMode,jdbcType=VARCHAR}
            </if>
            <if test="w.prodType != null and w.prodType != '' ">
                AND PROD_TYPE = #{w.prodType,jdbcType=VARCHAR}
            </if>
            <if test="w.clientNo != null and w.clientNo != '' ">
                AND CLIENT_NO = #{w.clientNo,jdbcType=VARCHAR}
            </if>
            <if test="w.amortizeCcy != null and w.amortizeCcy != '' ">
                AND AMORTIZE_CCY = #{w.amortizeCcy,jdbcType=VARCHAR}
            </if>
            <if test="w.amortizeFrequency != null and w.amortizeFrequency != '' ">
                AND AMORTIZE_FREQUENCY = #{w.amortizeFrequency,jdbcType=VARCHAR}
            </if>
            <if test="w.amortizeTotalAmt != null ">
                AND AMORTIZE_TOTAL_AMT = #{w.amortizeTotalAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.busiNo != null and w.busiNo != '' ">
                AND BUSI_NO = #{w.busiNo,jdbcType=VARCHAR}
            </if>
            <if test="w.dealFlag != null and w.dealFlag != '' ">
                AND DEAL_FLAG = #{w.dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.reference != null and w.reference != '' ">
                AND REFERENCE = #{w.reference,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <delete id="delete">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </delete>

    <select id="count" parameterType="java.util.Map" resultType="int">
        SELECT count(1) FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </select>

    <select id="selectOne" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectList" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
        <include refid="Base_Select"/>
        FOR UPDATE
    </select>

    <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </delete>
</mapper>
