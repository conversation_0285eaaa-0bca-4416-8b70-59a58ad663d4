<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintControlDetails">
	<select id="getByExact" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintControlDetails">
		select <include refid="Base_Column"/>
		from RB_RESTRAINT_CONTROL_DETAILS
		where ( PROD_TYPE = #{prodType} OR PROD_TYPE = 'ALL' )
		AND ( TRAN_TYPE = #{tranType} OR TRAN_TYPE = 'ALL' )
		AND ( EVENT_TYPE = #{eventType} OR EVENT_TYPE = 'ALL' )
		AND ( RES_CLASS = #{resClas} OR RES_CLASS = 'ALL' )
		AND ( RESTRAINT_TYPE = #{restraintType} OR RESTRAINT_TYPE = 'ALL' )

		<if test="resCtlMod != null and resCtlMod != ''">
			AND EXPRESSION = #{resCtlMod}
		</if>
		<if test="servId != null and servId != ''">
			AND SERV_ID = #{servId}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
