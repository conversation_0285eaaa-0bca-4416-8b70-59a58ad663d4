<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgencyInfo">

<!--  <select id="selectValueMbAgencyInfo" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgencyInfo">-->

<!--      SELECT <include refid="Base_Column"/>-->
<!--    <![CDATA[FROM RB_AGENCY_INFO mai-->
<!--      WHERE  mai.EFFECT_DATE = #{baseParam.runDate}-->
<!--      AND mai.BATCH_STATUS = 'N'-->
<!--      ORDER BY mai.BATCH_NO-->
<!--    ]]>-->
<!--  </select>-->
  <select id="selectValueMbAgencyInfoSplit" parameterType="java.util.Map"  resultType="java.util.Map">
    SELECT COUNT(1) ROW_COUNT
    FROM RB_AGENCY_INFO mai
    WHERE mai.EFFECT_DATE = #{runDate}
    AND mai.BATCH_STATUS = 'N'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mai.COMPANY = #{company}
    </if>
  </select>
<!--  <select id="selectValidateMbAgencyInfo" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgencyInfo">-->

<!--      SELECT <include refid="Base_Column"/>-->
<!--    <![CDATA[FROM RB_AGENCY_INFO mai-->
<!--      WHERE  mai.EFFECT_DATE = #{baseParam.runDate}-->
<!--      AND mai.BATCH_STATUS = 'V'-->
<!--      ORDER BY mai.BATCH_NO-->
<!--    ]]>-->
<!--  </select>-->
  <select id="selectValidateMbAgencyInfoSplit" parameterType="java.util.Map"  resultType="java.util.Map">
    SELECT COUNT(1) ROW_COUNT
    FROM RB_AGENCY_INFO mai
    WHERE mai.EFFECT_DATE = #{runDate}
    AND mai.BATCH_STATUS = 'V'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mai.COMPANY = #{company}
    </if>
  </select>
</mapper>
