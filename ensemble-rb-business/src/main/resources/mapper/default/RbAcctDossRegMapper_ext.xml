<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg">

	<select id="selectDossAcctAgreementList" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg">
		SELECT <include refid="Base_Column"/>
		from  RB_ACCT_DOSS_REG
		WHERE  DOSS_STATUS = #{dossStatus}
		AND DOSS_OPERATE_TYPE = #{dossOperateType}
		AND DOSS_DATE = #{dossDate,jdbcType=DATE}
		AND INDIVIDUAL_FLAG = 'N'
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="selectByinternalKeyArray" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg">
		SELECT <include refid="Base_Column"/>
		FROM RB_ACCT_DOSS_REG
		WHERE INTERNAL_KEY IN  ${internalKey}
		AND DOSS_STATUS NOT IN ('C','SD')
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY DOSS_DATE DESC
	</select>

	<select id="selectDossCloseToDrawList" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg">
		SELECT <include refid="Base_Column"/>
		from  RB_ACCT_DOSS_REG
		WHERE  DOSS_STATUS = #{dossStatus}
		<if test="baseAcctNo != null and  baseAcctNo != '' ">
			AND BASE_ACCT_NO = #{baseAcctNo}
		</if>
	</select>
</mapper>
