<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">

    <select id="selectByPrimaryKeyExt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        select
        <include refid="Base_Column"/>
        from RB_MISC_HIST
        where SEQ_NO = #{seqNo}
        <if test="tranDate != null">
            and TRAN_DATE = #{tranDate,jdbcType=DATE}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        insert into RB_MISC_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="voucherNo != null and  voucherNo != '' ">
                VOUCHER_NO,
            </if>
            <if test="systemId != null and  systemId != '' ">
                SYSTEM_ID,
            </if>
            <if test="mediumType != null and  mediumType != '' ">
                MEDIUM_TYPE,
            </if>
            <if test="othSeqNo != null and  othSeqNo != '' ">
                OTH_SEQ_NO,
            </if>
            <if test="othBranch != null and  othBranch != '' ">
                OTH_BRANCH,
            </if>
            <if test="othRealDocumentType != null and  othRealDocumentType != '' ">
                OTH_REAL_DOCUMENT_TYPE,
            </if>
            <if test="ccy != null and  ccy != '' ">
                CCY,
            </if>
            <if test="reference != null and  reference != '' ">
                REFERENCE,
            </if>
            <if test="tranHistSeqNo != null and  tranHistSeqNo != '' ">
                TRAN_HIST_SEQ_NO,
            </if>
            <if test="authUserId != null and  authUserId != '' ">
                AUTH_USER_ID,
            </if>
            <if test="floatDays != null and  floatDays != '' ">
                FLOAT_DAYS,
            </if>
            <if test="postDate != null ">
                POST_DATE,
            </if>
            <if test="mainSeqNo != null and  mainSeqNo != '' ">
                MAIN_SEQ_NO,
            </if>
            <if test="costumerId != null and  costumerId != '' ">
                COSTUMER_ID,
            </if>
            <if test="amtType != null and  amtType != '' ">
                AMT_TYPE,
            </if>
            <if test="tranDate != null ">
                TRAN_DATE,
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE,
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                SOURCE_TYPE,
            </if>
            <if test="primaryTranSeqNo != null and  primaryTranSeqNo != '' ">
                PRIMARY_TRAN_SEQ_NO,
            </if>
            <if test="country != null and  country != '' ">
                COUNTRY,
            </if>
            <if test="deptCode != null and  deptCode != '' ">
                DEPT_CODE,
            </if>
            <if test="company != null and  company != '' ">
                COMPANY,
            </if>
            <if test="subSeqNo != null and  subSeqNo != '' ">
                SUB_SEQ_NO,
            </if>
            <if test="othRealDocumentId != null and  othRealDocumentId != '' ">
                OTH_REAL_DOCUMENT_ID,
            </if>
            <if test="othRealTranAddr != null and  othRealTranAddr != '' ">
                OTH_REAL_TRAN_ADDR,
            </if>
            <if test="tranType != null and  tranType != '' ">
                TRAN_TYPE,
            </if>
            <if test="rateType != null and  rateType != '' ">
                RATE_TYPE,
            </if>
            <if test="contraEquivAmt != null ">
                CONTRA_EQUIV_AMT,
            </if>
            <if test="tfrBranch != null and  tfrBranch != '' ">
                TFR_BRANCH,
            </if>
            <if test="convBase != null and  convBase != '' ">
                CONV_BASE,
            </if>
            <if test="docType != null and  docType != '' ">
                DOC_TYPE,
            </if>
            <if test="tranStatus != null and  tranStatus != '' ">
                TRAN_STATUS,
            </if>
            <if test="reversalFlag != null and  reversalFlag != '' ">
                REVERSAL_FLAG,
            </if>
            <if test="primaryEventType != null and  primaryEventType != '' ">
                PRIMARY_EVENT_TYPE,
            </if>
            <if test="contrastBatNo != null and  contrastBatNo != '' ">
                CONTRAST_BAT_NO,
            </if>
            <if test="mediumFlag != null and  mediumFlag != '' ">
                MEDIUM_FLAG,
            </if>
            <if test="cashItem != null and  cashItem != '' ">
                CASH_ITEM,
            </if>
            <if test="othDocumentType != null and  othDocumentType != '' ">
                OTH_DOCUMENT_TYPE,
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                SEQ_NO,
            </if>
            <if test="tranAmt != null ">
                TRAN_AMT,
            </if>
            <if test="tranDesc != null and  tranDesc != '' ">
                TRAN_DESC,
            </if>
            <if test="clientName != null and  clientName != '' ">
                CLIENT_NAME,
            </if>
            <if test="recPayFlag != null and  recPayFlag != '' ">
                REC_PAY_FLAG,
            </if>
            <if test="settlementDate != null ">
                SETTLEMENT_DATE,
            </if>
            <if test="spreadPercent != null ">
                SPREAD_PERCENT,
            </if>
            <if test="othBankName != null and  othBankName != '' ">
                OTH_BANK_NAME,
            </if>
            <if test="othBankCode != null and  othBankCode != '' ">
                OTH_BANK_CODE,
            </if>
            <if test="othAcctSeqNo != null and  othAcctSeqNo != '' ">
                OTH_ACCT_SEQ_NO,
            </if>
            <if test="othDocumentId != null and  othDocumentId != '' ">
                OTH_DOCUMENT_ID,
            </if>
            <if test="othRealBankCode != null and  othRealBankCode != '' ">
                OTH_REAL_BANK_CODE,
            </if>
            <if test="crDrInd != null and  crDrInd != '' ">
                CR_DR_IND,
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                PROFIT_CENTRE,
            </if>
            <if test="contraAcctCcy != null and  contraAcctCcy != '' ">
                CONTRA_ACCT_CCY,
            </if>
            <if test="baseEquivAmt != null ">
                BASE_EQUIV_AMT,
            </if>
            <if test="crossRate != null ">
                CROSS_RATE,
            </if>
            <if test="traceId != null and  traceId != '' ">
                TRACE_ID,
            </if>
            <if test="userId != null and  userId != '' ">
                USER_ID,
            </if>
            <if test="brSeqNo != null and  brSeqNo != '' ">
                BR_SEQ_NO,
            </if>
            <if test="businessUnit != null and  businessUnit != '' ">
                BUSINESS_UNIT,
            </if>
            <if test="systemCode != null and  systemCode != '' ">
                SYSTEM_CODE,
            </if>
            <if test="creditCardNo != null and  creditCardNo != '' ">
                CREDIT_CARD_NO,
            </if>
            <if test="prodType != null and  prodType != '' ">
                PROD_TYPE,
            </if>
            <if test="othBaseAcctNo != null and  othBaseAcctNo != '' ">
                OTH_BASE_ACCT_NO,
            </if>
            <if test="othProdType != null and  othProdType != '' ">
                OTH_PROD_TYPE,
            </if>
            <if test="othBranchRegionalismCode != null and  othBranchRegionalismCode != '' ">
                OTH_BRANCH_REGIONALISM_CODE,
            </if>
            <if test="othRealTranName != null and  othRealTranName != '' ">
                OTH_REAL_TRAN_NAME,
            </if>
            <if test="othRealBranchRegionCode != null and  othRealBranchRegionCode != '' ">
                OTH_REAL_BRANCH_REGION_CODE,
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                TRAN_BRANCH,
            </if>
            <if test="narrative != null and  narrative != '' ">
                NARRATIVE,
            </if>
            <if test="documentId != null and  documentId != '' ">
                DOCUMENT_ID,
            </if>
            <if test="reversalTranDate != null ">
                REVERSAL_TRAN_DATE,
            </if>
            <if test="bankSeqNo != null and  bankSeqNo != '' ">
                BANK_SEQ_NO,
            </if>
            <if test="prefix != null and  prefix != '' ">
                PREFIX,
            </if>
            <if test="servCharge != null and  servCharge != '' ">
                SERV_CHARGE,
            </if>
            <if test="tranNote != null and  tranNote != '' ">
                TRAN_NOTE,
            </if>
            <if test="othAcctCcy != null and  othAcctCcy != '' ">
                OTH_ACCT_CCY,
            </if>
            <if test="othTranName != null and  othTranName != '' ">
                OTH_TRAN_NAME,
            </if>
            <if test="othTranAddr != null and  othTranAddr != '' ">
                OTH_TRAN_ADDR,
            </if>
            <if test="othRealBaseAcctNo != null and  othRealBaseAcctNo != '' ">
                OTH_REAL_BASE_ACCT_NO,
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                SOURCE_MODULE,
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                CLIENT_NO,
            </if>
            <if test="glPostedFlag != null and  glPostedFlag != '' ">
                GL_POSTED_FLAG,
            </if>
            <if test="printCnt != null and  printCnt != '' ">
                PRINT_CNT,
            </if>
            <if test="taeSubSeqNo != null and  taeSubSeqNo != '' ">
                TAE_SUB_SEQ_NO,
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                ACCOUNTING_STATUS,
            </if>
            <if test="channelDate != null ">
                CHANNEL_DATE,
            </if>
            <if test="othInternalKey != null ">
                OTH_INTERNAL_KEY,
            </if>
            <if test="othAcctDesc != null and  othAcctDesc != '' ">
                OTH_ACCT_DESC,
            </if>
            <if test="othReference != null and  othReference != '' ">
                OTH_REFERENCE,
            </if>
            <if test="othRealBankName != null and  othRealBankName != '' ">
                OTH_REAL_BANK_NAME,
            </if>
            <if test="terminalId != null and  terminalId != '' ">
                TERMINAL_ID,
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                CHANNEL_SEQ_NO,
            </if>
            <if test="documentType != null and  documentType != '' ">
                DOCUMENT_TYPE,
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="voucherNo != null and  voucherNo != '' ">
                #{voucherNo},
            </if>
            <if test="systemId != null and  systemId != '' ">
                #{systemId},
            </if>
            <if test="mediumType != null and  mediumType != '' ">
                #{mediumType},
            </if>
            <if test="othSeqNo != null and  othSeqNo != '' ">
                #{othSeqNo},
            </if>
            <if test="othBranch != null and  othBranch != '' ">
                #{othBranch},
            </if>
            <if test="othRealDocumentType != null and  othRealDocumentType != '' ">
                #{othRealDocumentType},
            </if>
            <if test="ccy != null and  ccy != '' ">
                #{ccy},
            </if>
            <if test="reference != null and  reference != '' ">
                #{reference},
            </if>
            <if test="tranHistSeqNo != null and  tranHistSeqNo != '' ">
                #{tranHistSeqNo},
            </if>
            <if test="authUserId != null and  authUserId != '' ">
                #{authUserId},
            </if>
            <if test="floatDays != null and  floatDays != '' ">
                #{floatDays},
            </if>
            <if test="postDate != null ">
                #{postDate},
            </if>
            <if test="mainSeqNo != null and  mainSeqNo != '' ">
                #{mainSeqNo},
            </if>
            <if test="costumerId != null and  costumerId != '' ">
                #{costumerId},
            </if>
            <if test="amtType != null and  amtType != '' ">
                #{amtType},
            </if>
            <if test="tranDate != null ">
                #{tranDate},
            </if>
            <if test="effectDate != null ">
                #{effectDate},
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                #{sourceType},
            </if>
            <if test="primaryTranSeqNo != null and  primaryTranSeqNo != '' ">
                #{primaryTranSeqNo},
            </if>
            <if test="country != null and  country != '' ">
                #{country},
            </if>
            <if test="deptCode != null and  deptCode != '' ">
                #{deptCode},
            </if>
            <if test="company != null and  company != '' ">
                #{company},
            </if>
            <if test="subSeqNo != null and  subSeqNo != '' ">
                #{subSeqNo},
            </if>
            <if test="othRealDocumentId != null and  othRealDocumentId != '' ">
                #{othRealDocumentId},
            </if>
            <if test="othRealTranAddr != null and  othRealTranAddr != '' ">
                #{othRealTranAddr},
            </if>
            <if test="tranType != null and  tranType != '' ">
                #{tranType},
            </if>
            <if test="rateType != null and  rateType != '' ">
                #{rateType},
            </if>
            <if test="contraEquivAmt != null ">
                #{contraEquivAmt},
            </if>
            <if test="tfrBranch != null and  tfrBranch != '' ">
                #{tfrBranch},
            </if>
            <if test="convBase != null and  convBase != '' ">
                #{convBase},
            </if>
            <if test="docType != null and  docType != '' ">
                #{docType},
            </if>
            <if test="tranStatus != null and  tranStatus != '' ">
                #{tranStatus},
            </if>
            <if test="reversalFlag != null and  reversalFlag != '' ">
                #{reversalFlag},
            </if>
            <if test="primaryEventType != null and  primaryEventType != '' ">
                #{primaryEventType},
            </if>
            <if test="contrastBatNo != null and  contrastBatNo != '' ">
                #{contrastBatNo},
            </if>
            <if test="mediumFlag != null and  mediumFlag != '' ">
                #{mediumFlag},
            </if>
            <if test="cashItem != null and  cashItem != '' ">
                #{cashItem},
            </if>
            <if test="othDocumentType != null and  othDocumentType != '' ">
                #{othDocumentType},
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                #{seqNo},
            </if>
            <if test="tranAmt != null ">
                #{tranAmt},
            </if>
            <if test="tranDesc != null and  tranDesc != '' ">
                #{tranDesc},
            </if>
            <if test="clientName != null and  clientName != '' ">
                #{clientName},
            </if>
            <if test="recPayFlag != null and  recPayFlag != '' ">
                #{recPayFlag},
            </if>
            <if test="settlementDate != null ">
                #{settlementDate},
            </if>
            <if test="spreadPercent != null ">
                #{spreadPercent},
            </if>
            <if test="othBankName != null and  othBankName != '' ">
                #{othBankName},
            </if>
            <if test="othBankCode != null and  othBankCode != '' ">
                #{othBankCode},
            </if>
            <if test="othAcctSeqNo != null and  othAcctSeqNo != '' ">
                #{othAcctSeqNo},
            </if>
            <if test="othDocumentId != null and  othDocumentId != '' ">
                #{othDocumentId},
            </if>
            <if test="othRealBankCode != null and  othRealBankCode != '' ">
                #{othRealBankCode},
            </if>
            <if test="crDrInd != null and  crDrInd != '' ">
                #{crDrInd},
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                #{profitCenter},
            </if>
            <if test="contraAcctCcy != null and  contraAcctCcy != '' ">
                #{contraAcctCcy},
            </if>
            <if test="baseEquivAmt != null ">
                #{baseEquivAmt},
            </if>
            <if test="crossRate != null ">
                #{crossRate},
            </if>
            <if test="traceId != null and  traceId != '' ">
                #{traceId},
            </if>
            <if test="userId != null and  userId != '' ">
                #{userId},
            </if>
            <if test="brSeqNo != null and  brSeqNo != '' ">
                #{brSeqNo},
            </if>
            <if test="businessUnit != null and  businessUnit != '' ">
                #{businessUnit},
            </if>
            <if test="systemCode != null and  systemCode != '' ">
                #{systemCode},
            </if>
            <if test="creditCardNo != null and  creditCardNo != '' ">
                #{creditCardNo},
            </if>
            <if test="prodType != null and  prodType != '' ">
                #{prodType},
            </if>
            <if test="othBaseAcctNo != null and  othBaseAcctNo != '' ">
                #{othBaseAcctNo},
            </if>
            <if test="othProdType != null and  othProdType != '' ">
                #{othProdType},
            </if>
            <if test="othBranchRegionalismCode != null and  othBranchRegionalismCode != '' ">
                #{othBranchRegionalismCode},
            </if>
            <if test="othRealTranName != null and  othRealTranName != '' ">
                #{othRealTranName},
            </if>
            <if test="othRealBranchRegionCode != null and  othRealBranchRegionCode != '' ">
                #{othRealBranchRegionCode},
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                #{tranBranch},
            </if>
            <if test="narrative != null and  narrative != '' ">
                #{narrative},
            </if>
            <if test="documentId != null and  documentId != '' ">
                #{documentId},
            </if>
            <if test="reversalTranDate != null ">
                #{reversalTranDate},
            </if>
            <if test="bankSeqNo != null and  bankSeqNo != '' ">
                #{bankSeqNo},
            </if>
            <if test="prefix != null and  prefix != '' ">
                #{prefix},
            </if>
            <if test="servCharge != null and  servCharge != '' ">
                #{servCharge},
            </if>
            <if test="tranNote != null and  tranNote != '' ">
                #{tranNote},
            </if>
            <if test="othAcctCcy != null and  othAcctCcy != '' ">
                #{othAcctCcy},
            </if>
            <if test="othTranName != null and  othTranName != '' ">
                #{othTranName},
            </if>
            <if test="othTranAddr != null and  othTranAddr != '' ">
                #{othTranAddr},
            </if>
            <if test="othRealBaseAcctNo != null and  othRealBaseAcctNo != '' ">
                #{othRealBaseAcctNo},
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                #{sourceModule},
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                #{clientNo},
            </if>
            <if test="glPostedFlag != null and  glPostedFlag != '' ">
                #{glPostedFlag},
            </if>
            <if test="printCnt != null and  printCnt != '' ">
                #{printCnt},
            </if>
            <if test="taeSubSeqNo != null and  taeSubSeqNo != '' ">
                #{taeSubSeqNo},
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                #{accountingStatus},
            </if>
            <if test="channelDate != null ">
                #{channelDate},
            </if>
            <if test="othInternalKey != null ">
                #{othInternalKey},
            </if>
            <if test="othAcctDesc != null and  othAcctDesc != '' ">
                #{othAcctDesc},
            </if>
            <if test="othReference != null and  othReference != '' ">
                #{othReference},
            </if>
            <if test="othRealBankName != null and  othRealBankName != '' ">
                #{othRealBankName},
            </if>
            <if test="terminalId != null and  terminalId != '' ">
                #{terminalId},
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                #{channelSeqNo},
            </if>
            <if test="documentType != null and  documentType != '' ">
                #{documentType},
            </if>
            <if test="tranTimestamp != null ">
                #{tranTimestamp},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        update RB_MISC_HIST
        <set>
            <if test="voucherNo != null and  voucherNo != '' ">
                VOUCHER_NO = #{voucherNo},
            </if>
            <if test="systemId != null and  systemId != '' ">
                SYSTEM_ID = #{systemId},
            </if>
            <if test="mediumType != null and  mediumType != '' ">
                MEDIUM_TYPE = #{mediumType},
            </if>
            <if test="othSeqNo != null and  othSeqNo != '' ">
                OTH_SEQ_NO = #{othSeqNo},
            </if>
            <if test="othBranch != null and  othBranch != '' ">
                OTH_BRANCH = #{othBranch},
            </if>
            <if test="othRealDocumentType != null and  othRealDocumentType != '' ">
                OTH_REAL_DOCUMENT_TYPE = #{othRealDocumentType},
            </if>
            <if test="ccy != null and  ccy != '' ">
                CCY = #{ccy},
            </if>
            <if test="reference != null and  reference != '' ">
                REFERENCE = #{reference},
            </if>
            <if test="tranHistSeqNo != null and  tranHistSeqNo != '' ">
                TRAN_HIST_SEQ_NO = #{tranHistSeqNo},
            </if>
            <if test="authUserId != null and  authUserId != '' ">
                AUTH_USER_ID = #{authUserId},
            </if>
            <if test="floatDays != null and  floatDays != '' ">
                FLOAT_DAYS = #{floatDays},
            </if>
            <if test="postDate != null ">
                POST_DATE = #{postDate},
            </if>
            <if test="mainSeqNo != null and  mainSeqNo != '' ">
                MAIN_SEQ_NO = #{mainSeqNo},
            </if>
            <if test="costumerId != null and  costumerId != '' ">
                COSTUMER_ID = #{costumerId},
            </if>
            <if test="amtType != null and  amtType != '' ">
                AMT_TYPE = #{amtType},
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE = #{effectDate},
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                SOURCE_TYPE = #{sourceType},
            </if>
            <if test="primaryTranSeqNo != null and  primaryTranSeqNo != '' ">
                PRIMARY_TRAN_SEQ_NO = #{primaryTranSeqNo},
            </if>
            <if test="country != null and  country != '' ">
                COUNTRY = #{country},
            </if>
            <if test="deptCode != null and  deptCode != '' ">
                DEPT_CODE = #{deptCode},
            </if>
            <if test="company != null and  company != '' ">
                COMPANY = #{company},
            </if>
            <if test="subSeqNo != null and  subSeqNo != '' ">
                SUB_SEQ_NO = #{subSeqNo},
            </if>
            <if test="othRealDocumentId != null and  othRealDocumentId != '' ">
                OTH_REAL_DOCUMENT_ID = #{othRealDocumentId},
            </if>
            <if test="othRealTranAddr != null and  othRealTranAddr != '' ">
                OTH_REAL_TRAN_ADDR = #{othRealTranAddr},
            </if>
            <if test="tranType != null and  tranType != '' ">
                TRAN_TYPE = #{tranType},
            </if>
            <if test="rateType != null and  rateType != '' ">
                RATE_TYPE = #{rateType},
            </if>
            <if test="contraEquivAmt != null ">
                CONTRA_EQUIV_AMT = #{contraEquivAmt},
            </if>
            <if test="tfrBranch != null and  tfrBranch != '' ">
                TFR_BRANCH = #{tfrBranch},
            </if>
            <if test="convBase != null and  convBase != '' ">
                CONV_BASE = #{convBase},
            </if>
            <if test="docType != null and  docType != '' ">
                DOC_TYPE = #{docType},
            </if>
            <if test="tranStatus != null and  tranStatus != '' ">
                TRAN_STATUS = #{tranStatus},
            </if>
            <if test="reversalFlag != null and  reversalFlag != '' ">
                REVERSAL_FLAG = #{reversalFlag},
            </if>
            <if test="primaryEventType != null and  primaryEventType != '' ">
                PRIMARY_EVENT_TYPE = #{primaryEventType},
            </if>
            <if test="contrastBatNo != null and  contrastBatNo != '' ">
                CONTRAST_BAT_NO = #{contrastBatNo},
            </if>
            <if test="mediumFlag != null and  mediumFlag != '' ">
                MEDIUM_FLAG = #{mediumFlag},
            </if>
            <if test="cashItem != null and  cashItem != '' ">
                CASH_ITEM = #{cashItem},
            </if>
            <if test="othDocumentType != null and  othDocumentType != '' ">
                OTH_DOCUMENT_TYPE = #{othDocumentType},
            </if>
            <if test="tranAmt != null ">
                TRAN_AMT = #{tranAmt},
            </if>
            <if test="tranDesc != null and  tranDesc != '' ">
                TRAN_DESC = #{tranDesc},
            </if>
            <if test="clientName != null and  clientName != '' ">
                CLIENT_NAME = #{clientName},
            </if>
            <if test="recPayFlag != null and  recPayFlag != '' ">
                REC_PAY_FLAG = #{recPayFlag},
            </if>
            <if test="settlementDate != null ">
                SETTLEMENT_DATE = #{settlementDate},
            </if>
            <if test="spreadPercent != null ">
                SPREAD_PERCENT = #{spreadPercent},
            </if>
            <if test="othBankName != null and  othBankName != '' ">
                OTH_BANK_NAME = #{othBankName},
            </if>
            <if test="othBankCode != null and  othBankCode != '' ">
                OTH_BANK_CODE = #{othBankCode},
            </if>
            <if test="othAcctSeqNo != null and  othAcctSeqNo != '' ">
                OTH_ACCT_SEQ_NO = #{othAcctSeqNo},
            </if>
            <if test="othDocumentId != null and  othDocumentId != '' ">
                OTH_DOCUMENT_ID = #{othDocumentId},
            </if>
            <if test="othRealBankCode != null and  othRealBankCode != '' ">
                OTH_REAL_BANK_CODE = #{othRealBankCode},
            </if>
            <if test="crDrInd != null and  crDrInd != '' ">
                CR_DR_IND = #{crDrInd},
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                PROFIT_CENTRE = #{profitCenter},
            </if>
            <if test="contraAcctCcy != null and  contraAcctCcy != '' ">
                CONTRA_ACCT_CCY = #{contraAcctCcy},
            </if>
            <if test="baseEquivAmt != null ">
                BASE_EQUIV_AMT = #{baseEquivAmt},
            </if>
            <if test="crossRate != null ">
                CROSS_RATE = #{crossRate},
            </if>
            <if test="traceId != null and  traceId != '' ">
                TRACE_ID = #{traceId},
            </if>
            <if test="userId != null and  userId != '' ">
                USER_ID = #{userId},
            </if>
            <if test="brSeqNo != null and  brSeqNo != '' ">
                BR_SEQ_NO = #{brSeqNo},
            </if>
            <if test="businessUnit != null and  businessUnit != '' ">
                BUSINESS_UNIT = #{businessUnit},
            </if>
            <if test="systemCode != null and  systemCode != '' ">
                SYSTEM_CODE = #{systemCode},
            </if>
            <if test="creditCardNo != null and  creditCardNo != '' ">
                CREDIT_CARD_NO = #{creditCardNo},
            </if>
            <if test="prodType != null and  prodType != '' ">
                PROD_TYPE = #{prodType},
            </if>
            <if test="othBaseAcctNo != null and  othBaseAcctNo != '' ">
                OTH_BASE_ACCT_NO = #{othBaseAcctNo},
            </if>
            <if test="othProdType != null and  othProdType != '' ">
                OTH_PROD_TYPE = #{othProdType},
            </if>
            <if test="othBranchRegionalismCode != null and  othBranchRegionalismCode != '' ">
                OTH_BRANCH_REGIONALISM_CODE = #{othBranchRegionalismCode},
            </if>
            <if test="othRealTranName != null and  othRealTranName != '' ">
                OTH_REAL_TRAN_NAME = #{othRealTranName},
            </if>
            <if test="othRealBranchRegionCode != null and  othRealBranchRegionCode != '' ">
                OTH_REAL_BRANCH_REGION_CODE = #{othRealBranchRegionCode},
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="narrative != null and  narrative != '' ">
                NARRATIVE = #{narrative},
            </if>
            <if test="documentId != null and  documentId != '' ">
                DOCUMENT_ID = #{documentId},
            </if>
            <if test="reversalTranDate != null ">
                REVERSAL_TRAN_DATE = #{reversalTranDate},
            </if>
            <if test="bankSeqNo != null and  bankSeqNo != '' ">
                BANK_SEQ_NO = #{bankSeqNo},
            </if>
            <if test="prefix != null and  prefix != '' ">
                PREFIX = #{prefix},
            </if>
            <if test="servCharge != null and  servCharge != '' ">
                SERV_CHARGE = #{servCharge},
            </if>
            <if test="tranNote != null and  tranNote != '' ">
                TRAN_NOTE = #{tranNote},
            </if>
            <if test="othAcctCcy != null and  othAcctCcy != '' ">
                OTH_ACCT_CCY = #{othAcctCcy},
            </if>
            <if test="othTranName != null and  othTranName != '' ">
                OTH_TRAN_NAME = #{othTranName},
            </if>
            <if test="othTranAddr != null and  othTranAddr != '' ">
                OTH_TRAN_ADDR = #{othTranAddr},
            </if>
            <if test="othRealBaseAcctNo != null and  othRealBaseAcctNo != '' ">
                OTH_REAL_BASE_ACCT_NO = #{othRealBaseAcctNo},
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                SOURCE_MODULE = #{sourceModule},
            </if>
            <if test="glPostedFlag != null and  glPostedFlag != '' ">
                GL_POSTED_FLAG = #{glPostedFlag},
            </if>
            <if test="printCnt != null and  printCnt != '' ">
                PRINT_CNT = #{printCnt},
            </if>
            <if test="taeSubSeqNo != null and  taeSubSeqNo != '' ">
                TAE_SUB_SEQ_NO = #{taeSubSeqNo},
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                ACCOUNTING_STATUS = #{accountingStatus},
            </if>
            <if test="channelDate != null ">
                CHANNEL_DATE = #{channelDate},
            </if>
            <if test="othInternalKey != null ">
                OTH_INTERNAL_KEY = #{othInternalKey},
            </if>
            <if test="othAcctDesc != null and  othAcctDesc != '' ">
                OTH_ACCT_DESC = #{othAcctDesc},
            </if>
            <if test="othReference != null and  othReference != '' ">
                OTH_REFERENCE = #{othReference},
            </if>
            <if test="othRealBankName != null and  othRealBankName != '' ">
                OTH_REAL_BANK_NAME = #{othRealBankName},
            </if>
            <if test="terminalId != null and  terminalId != '' ">
                TERMINAL_ID = #{terminalId},
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{channelSeqNo},
            </if>
            <if test="documentType != null and  documentType != '' ">
                DOCUMENT_TYPE = #{documentType},
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
        </set>
        where SEQ_NO = #{seqNo}
        and TRAN_DATE = #{tranDate,jdbcType=DATE}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="queryMiscHist" parameterType="map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        select
        <include refid="Base_Column"/>
        from RB_MISC_HIST
        <where>
            <if test="tranBranch != null">
                AND TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="clientNo != null">
                AND CLIENT_NO = #{clientNo},
            </if>
            <if test="sourceType != null">
                AND SOURCE_TYPE = #{sourceType},
            </if>
            <if test="tranDate != null">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="tranType != null">
                AND TRAN_TYPE = #{tranType},
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>

    <select id="selectMbMiscHistSplitOB" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(seq_no) START_KEY,
            MAX(seq_no) END_KEY,COUNT(*) ROW_COUNT
            FROM
            (
            SELECT
            seq_no,
            @rownum :=@rownum + 1 AS rownum
            FROM (SELECT DISTINCT seq_no FROM RB_MISC_HIST,
            (SELECT @rownum := -1) t
            where tran_date = #{runDate,jdbcType=DATE}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            AND (gl_posted = 'N' or gl_posted is null)) t1
            ORDER BY seq_no
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (seq_no) START_KEY, MAX (seq_no) END_KEY,COUNT(*) ROW_COUNT
            FROM (SELECT DISTINCT seq_no
            from RB_MISC_HIST
            where tran_date = #{runDate}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            AND (gl_posted = 'N' or gl_posted is null)
            ORDER BY seq_no)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>
    <select id="selectMbMiscHistByRef" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        select
        <include refid="Base_Column"/>
        from RB_MISC_HIST
        where reference=#{reference} AND TRAN_STATUS != 'X'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY seq_no
    </select>

    <select id="selectMbMiscHistByRefAndSeq" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        select
        <include refid="Base_Column"/>
        from RB_MISC_HIST
        <where>
            <trim suffixOverrides="AND">
                <if test="reference != null and reference != '' ">
                    reference=#{reference} and
                </if>
                <if test="channelSeqNo != null and channelSeqNo != '' ">
                    CHANNEL_SEQ_NO =#{channelSeqNo} and
                </if>
                <if test="subSeqNo != null and subSeqNo != '' ">
                    SUB_SEQ_NO LIKE '${subSeqNo}%' and
                </if>
                <if test="clientNo != null and clientNo != '' ">
                    CLIENT_NO =#{clientNo} and
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} and
                </if>
            </trim>
        </where>
        ORDER BY seq_no
    </select>
    <select id="selectMbMiscHistByRefAndClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        select
        <include refid="Base_Column"/>
        from RB_MISC_HIST
        where reference=#{reference} AND TRAN_STATUS != 'X'
        <if test="clientNo != null and clientNo != '' ">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY seq_no
    </select>
    <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
		update RB_MISC_HIST set gl_posted='Y'
		where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
	</update>
    <select id="getMbMiscHistByChanSeqNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        select
        <include refid="Base_Column"/>
        from RB_MISC_HIST
        where channel_Seq_No=#{channelSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY seq_no
    </select>
<!--    <select id="selectMbMiscHistByRefForReverse"-->
<!--            parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"-->
<!--            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">-->
<!--        select-->
<!--        <include refid="Base_Column"/>-->
<!--        from RB_MISC_HIST where 1=1-->
<!--        <if test="baseParam.reference != null">-->
<!--            AND REFERENCE = #{baseParam.reference}-->
<!--        </if>-->
<!--        <if test="baseParam.startDate !=  null">-->
<!--            AND TRAN_DATE between #{baseParam.startDate} AND #{baseParam.endDate}-->
<!--        </if>-->
<!--        <if test="baseParam.startAmt != null">-->
<!--            AND TRAN_AMT between #{baseParam.startAmt} AND #{baseParam.endAmt}-->
<!--        </if>-->
<!--        <if test="baseParam.userId != null">-->
<!--            AND USER_ID = #{baseParam.userId}-->
<!--        </if>-->
<!--        <if test="baseParam.acctCcy != null">-->
<!--            AND CCY = #{baseParam.acctCcy}-->
<!--        </if>-->
<!--        <if test="baseParam.programId != null">-->
<!--            AND PROGRAM_ID = #{baseParam.programId}-->
<!--        </if>-->
<!--        AND TRAN_STATUS != 'X'-->
<!--        ORDER BY seq_no-->
<!--    </select>-->

    <select id="selectMbMiscHistByRefForAllReverse"
            parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        select
        <include refid="Base_Column"/>
        from RB_MISC_HIST
        WHERE REVERSAL_FLAG != 'Y'
        <if test="reference !=  null">
            AND REFERENCE = #{reference}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SOURCE_TYPE = #{sourceType}
        </if>
        <if test="startDate != null">
            AND <![CDATA[ TRAN_DATE >= #{startDate} ]]>
        </if>
        <if test="endDate != null">
            AND <![CDATA[ TRAN_DATE <= #{endDate} ]]>
        </if>
        <if test="startAmt != null">
            AND  <![CDATA[ TRAN_AMT >=  #{startAmt} ]]>
        </if>
        <if test="endAmt != null">
            AND  <![CDATA[ TRAN_AMT <=  #{endAmt} ]]>
        </if>
        <if test="userId != null">
            AND USER_ID = #{userId}
        </if>
        <if test="ccy != null">
            AND CCY = #{ccy}
        </if>
        <if test="channelSeqNo != null">
            AND CHANNEL_SEQ_NO = #{channelSeqNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY seq_no
    </select>

    <select id="getRbMiscHistReversalWipe" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMiscHist">
        SELECT <include refid="Base_Column"/>
        FROM  RB_MISC_HIST
        WHERE 1=1
        <if test="userId != null and  userId != '' ">
            AND USER_ID = #{userId}
        </if>
        <if test="ccy != null and  ccy != '' ">
            AND CCY = #{ccy}
        </if>
        <if test="branchList != null and branchList.size() > 0 ">
            AND TRAN_BRANCH IN
            <foreach collection="branchList" item="tranBranch" open="(" separator="," close=")">
                #{tranBranch}
            </foreach>
        </if>
        <if test="startDate !=  null">
            AND  <![CDATA[ TRAN_DATE >= #{startDate}]]>
        </if>
        <if test="endDate !=  null">
            AND  <![CDATA[ TRAN_DATE <= #{endDate}]]>
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND REVERSAL_FLAG = 'Y'
        ORDER BY REVERSAL_TRAN_DATE
    </select>
</mapper>
