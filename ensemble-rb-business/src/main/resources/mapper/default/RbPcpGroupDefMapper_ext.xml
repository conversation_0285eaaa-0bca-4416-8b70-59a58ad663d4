<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">

  <select id="selectByInternalkay" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">
    select
    <include refid="Base_Column"/>
    from RB_PCP_GROUP_DEF
    where INTERNAL_KEY = #{internalkey}
    and ACCT_GROUP_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbPcpGroupDefs" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">
    select
    <include refid="Base_Column"/>
    from RB_PCP_GROUP_DEF
    where ACCT_GROUP_STATUS = 'A'
    <if test="baseAcctNo != null">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null">
      and PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      and ACCT_CCY = #{acctCcy}
    </if>
    <if test="acctSeqNo != null">
      and ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="upperGroupId != null">
      and UPPER_GROUP_ID = #{upperGroupId}
    </if>
    <if test="internalKey != null">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectAllInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">
    select
    <include refid="Base_Column"/>
    from RB_PCP_GROUP_DEF
    <where>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>
  <select id="selectByGroupId" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">
    select
    <include refid="Base_Column"/>
    from RB_PCP_GROUP_DEF
    where PCP_GROUP_ID = #{pcpGroupId}
    and ACCT_GROUP_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByBaseInter" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">
    select
    <include refid="Base_Column"/>
    from RB_PCP_GROUP_DEF
    where ACCT_GROUP_STATUS = 'A'
    <if test="baseAcctNo != null and baseAcctNo!=''">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null and prodType!=''">
      and PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null and acctCcy!=''">
      and ACCT_CCY = #{acctCcy}
    </if>
    <if test="acctSeqNo != null and acctSeqNo!=''">
      and ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="pcpGroupId != null and pcpGroupId!=''">
      and PCP_GROUP_ID = #{pcpGroupId}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectTopByClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_PCP_GROUP_DEF t
    WHERE t.CLIENT_NO = #{clientNo}
    AND t.ACCT_GROUP_STATUS = 'A'
    AND t.UPPER_GROUP_ID is NULL
    <if test="branchList != null and branchList.size() > 0">
      AND EXISTS (SELECT 1 from RB_ACCT WHERE internal_key = t.internal_key
      and acct_branch in
      <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
        #{branch}
      </foreach>
      )
    </if>
  </select>

  <select id="selectTopByPcpGroupId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_PCP_GROUP_DEF t
    WHERE t.PCP_GROUP_ID = #{pcpGroupId}
    AND t.ACCT_GROUP_STATUS = 'A'
    AND t.UPPER_GROUP_ID is NULL
    <if test="branchList != null and branchList.size() > 0">
      AND EXISTS (SELECT 1 from RB_ACCT WHERE internal_key = t.internal_key
      and acct_branch in
      <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
        #{branch}
      </foreach>
      )
    </if>
  </select>
</mapper>
