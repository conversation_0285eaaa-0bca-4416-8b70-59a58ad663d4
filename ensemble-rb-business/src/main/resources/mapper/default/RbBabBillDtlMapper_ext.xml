<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillDtl">

    <select id="selectDtlByAcceptContractNoTC" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillDtl">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_BAB_BILL_DTL
        <where>
            <trim suffixOverrides="AND">
                <if test="acceptContractNo != null and  acceptContractNo != '' ">
                    ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
                </if>
                <if test="babFlag != null and  babFlag != '' ">
                    BAB_FLAG = #{babFlag} AND
                </if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
            </trim>
        </where>
    </select>
	<select id="selectDtlByReferenceTC" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillDtl">
		SELECT
		<include refid="Base_Column"/>
		FROM
		RB_BAB_BILL_DTL
		<where>
			<trim suffixOverrides="AND">
				<if test="reference != null and  reference != '' ">
					REFERENCE = #{reference} AND
				</if>
				<if test="babFlag != null and  babFlag != '' ">
					BAB_FLAG = #{babFlag} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>
	<select id="selectDtlByAcceptContractNoD" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillDtl">
		SELECT
		<include refid="Base_Column"/>
		FROM
		RB_BAB_BILL_DTL
		<where>
			<trim suffixOverrides="AND">
				<if test="acceptContractNo != null and  acceptContractNo != '' ">
					ACCEPT_CONTRACT_NO = #{acceptContractNo} AND
				</if>
				<if test="babFlag != null and  babFlag != '' ">
					BAB_FLAG = #{babFlag} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>
<!-- Replacement of bank-backed bills -->
	<update id="updateByAcceptContractNoD" parameterType="java.util.Map">
		UPDATE RB_BAB_BILL_DTL set advance_reference = #{advanceReference}
		where accept_contract_no = #{acceptContractNo}
		and babFlag = #{babFlag}
		and client_no = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
</mapper>
