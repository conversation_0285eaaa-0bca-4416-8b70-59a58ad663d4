<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteQuota">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteQuota" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteQuota">
        select <include refid="Base_Column"/>
        from RB_DC_CHANNEL_AMT
        where STAGE_CODE = #{stageCode}
        <if test="channel != null">
            AND CHANNEL = #{channel}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <update id="updateWhiteQuota">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <where>
            <if test="stageCode != null">
                AND STAGE_CODE = #{stageCode}
            </if>
            <if test="issueYear != null">
                AND ISSUE_YEAR = #{issueYear}
            </if>
            <if test="ccy != null">
                AND CCY = #{ccy}
            </if>
            <if test="clientNo != null">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </update>

    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteQuota">
        delete from RB_DC_CHANNEL_AMT
        where STAGE_CODE = #{stageCode}
        <if test="channel != null">
            AND CHANNEL = #{channel}
        </if>
        <if test="prodType != null">
            AND PROD_TYPE = #{prodType}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <select id="geSumByLeaveLimt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteQuota" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcWhiteQuota">
        select
        sum(DISTRIBUTE_LIMIT) as DISTRIBUTE_LIMIT,
        sum(HOLDING_LIMIT) as HOLDING_LIMIT,
        sum(LEAVE_LIMIT) as LEAVE_LIMIT
        from RB_DC_CHANNEL_AMT
        where STAGE_CODE = #{stageCode}
        <if test="issueYear != null">
            AND ISSUE_YEAR = #{issueYear}
        </if>
        <if test="prodType != null">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="acctCcy != null">
            AND CCY = #{acctCcy}
        </if>
        <if test="channel != null">
            AND CHANNEL = #{channel}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

</mapper>
