<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeAcct">
  <!-- Created by admin on 2017/05/15 11:47:17. -->
  <!--<insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeAcct">-->
    <!--insert into OC_CHANGE_ACCT-->
    <!--<trim prefix="(" suffix=")" suffixOverrides=",">-->
      <!--<if test="tranBranch != null">-->
        <!--BRANCH,-->
      <!--</if>-->
      <!--<if test="changeAcctNo != null">-->
        <!--CHANGE_ACCT_NO,-->
      <!--</if>-->
      <!--<if test="changeAcctName != null">-->
        <!--CHANGE_ACCT_NAME,-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP,-->
      <!--</if>-->
    <!--</trim>-->
    <!--<trim prefix="values (" suffix=")" suffixOverrides=",">-->
      <!--<if test="tranBranch != null">-->
        <!--#{tranBranch},-->
      <!--</if>-->
      <!--<if test="changeAcctNo != null">-->
        <!--#{changeAcctNo},-->
      <!--</if>-->
      <!--<if test="changeAcctName != null">-->
        <!--#{changeAcctName},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--#{tranTimestamp},-->
      <!--</if>-->
    <!--</trim>-->
  <!--</insert>-->

  <!-- 根据机构查常用账户 -->
  <!--<select id="selectChangeAcctInfo" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeAcct">-->
    <!--select tranBranch,change_acct_no,change_acct_name  from oc_change_acct-->
    <!--where tranBranch= #{tranBranch}-->
    <!--and change_acct_no =#{acctno}-->
  <!--</select>-->

  <!-- 根据机构更新帐户名acct_name -->

  <!--<update id="updateChangeAcctInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeAcct">-->

    <!--update oc_change_acct-->
    <!--<set>-->
      <!--<if test="acctname != null">-->
        <!--change_acct_name = #{acctname},-->
      <!--</if>-->
    <!--</set>-->
    <!--where tranBranch= #{tranBranch}-->
    <!--and change_acct_no =#{acctno}-->
  <!--</update>-->

</mapper>
