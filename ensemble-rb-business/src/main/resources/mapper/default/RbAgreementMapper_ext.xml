<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
    <sql id="Base_Column_A">
        A
        .
        ACCT_SEQ_NO
        ,
        A
        .
        AGREEMENT_ID,
        A
        .
        AGRE_PROD_TYPE,
        A
        .
        AGREEMENT_KEY,
        A
        .
        AGREEMENT_KEY_TYPE,
        A
        .
        AGREEMENT_OPEN_DATE,
        A
        .
        AGREEMENT_STATUS,
        A
        .
        AGREEMENT_TYPE,
        A
        .
        BASE_ACCT_NO,
        A
        .
        ACCT_NAME,
        A
        .
        TRAN_BRANCH,
        A
        .
        ACCT_CCY,
        A
        .
        CLIENT_NO,
        A
        .
        CLIENT_SHORT,
        A
        .
        AGREEMENT_CLOSE_ACCT_FLAG,
        A
        .
        COMPANY,
        <PERSON>
        <PERSON>
        <PERSON><PERSON>_DATE,
        A
        .
        LAST_CHANGE_DATE,
        A
        <PERSON>ST_<PERSON>_USER_ID,
        A
        .
        O<PERSON>OSITE_INTERNAL_KEY,
        A
        .
        PROD_TYPE,
        A
        .
        START_DATE,
        A
        .
        TRAN_TIMESTAMP,
        A
        .
        USER_ID
    </sql>



    <insert id="insertByMap" parameterType="java.util.Map">
        insert into RB_AGREEMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bean.agreementId != null">
                AGREEMENT_ID,
            </if>
            <if test="bean.agreProdType != null">
                AGRE_PROD_TYPE,
            </if>
            <if test="bean.agreementType != null">
                AGREEMENT_TYPE,
            </if>
            <if test="bean.startDate != null">
                START_DATE,
            </if>
            <if test="bean.endDate != null">
                END_DATE,
            </if>
            <if test="bean.agreementKeyType != null">
                AGREEMENT_KEY_TYPE,
            </if>
            <if test="bean.agreementKey != null">
                AGREEMENT_KEY,
            </if>
            <if test="bean.baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="bean.prodType != null">
                PROD_TYPE,
            </if>
            <if test="bean.ccy != null">
                ACCT_CCY,
            </if>
            <if test="bean.acctSeqNo != null">
                ACCT_SEQ_NO,
            </if>
            <if test="bean.acctName != null">
                ACCT_NAME,
            </if>
            <if test="bean.clientNo != null">
                CLIENT_NO,
            </if>
            <if test="bean.clientShort != null">
                CLIENT_SHORT,
            </if>
            <if test="bean.agreementStatus != null">
                AGREEMENT_STATUS,
            </if>
            <if test="bean.tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="bean.agreementOpenDate != null">
                AGREEMENT_OPEN_DATE,
            </if>
            <if test="bean.userId != null">
                USER_ID,
            </if>
            <if test="bean.lastChangeUserId != null">
                LAST_CHANGE_USER_ID,
            </if>
            <if test="bean.lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="bean.company != null">
                COMPANY,
            </if>
            <if test="bean.agreementCloseAcctFlag != null">
                AGREEMENT_CLOSE_ACCT_FLAG,
            </if>
            <if test="bean.oppositeInternalKey != null">
                OPPOSITE_INTERNAL_KEY,
            </if>
            <if test="bean.tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bean.agreementId != null">
                #{bean.agreementId},
            </if>
            <if test="bean.agreProdType != null">
                #{bean.agreProdType},
            </if>
            <if test="bean.agreementType != null">
                #{bean.agreementType},
            </if>
            <if test="bean.startDate != null">
                #{bean.startDate},
            </if>
            <if test="bean.endDate != null">
                #{bean.endDate},
            </if>
            <if test="bean.agreementKeyType != null">
                #{bean.agreementKeyType},
            </if>
            <if test="bean.agreementKey != null">
                #{bean.agreementKey},
            </if>
            <if test="bean.baseAcctNo != null">
                #{bean.baseAcctNo},
            </if>
            <if test="bean.prodType != null">
                #{bean.prodType},
            </if>
            <if test="bean.ccy != null">
                #{bean.ccy},
            </if>
            <if test="bean.acctSeqNo != null">
                #{bean.acctSeqNo},
            </if>
            <if test="bean.acctName != null">
                #{bean.acctName},
            </if>
            <if test="bean.clientNo != null">
                #{bean.clientNo},
            </if>
            <if test="bean.clientShort != null">
                #{bean.clientShort},
            </if>
            <if test="bean.agreementStatus != null">
                #{bean.agreementStatus},
            </if>
            <if test="bean.tranBranch != null">
                #{bean.tranBranch},
            </if>
            <if test="bean.agreementOpenDate != null">
                #{bean.agreementOpenDate},
            </if>
            <if test="bean.userId != null">
                #{bean.userId},
            </if>
            <if test="bean.lastChangeUserId != null">
                #{bean.lastChangeUserId},
            </if>
            <if test="bean.lastChangeDate != null">
                #{bean.lastChangeDate},
            </if>
            <if test="bean.company != null">
                #{bean.company},
            </if>
            <if test="bean.agreementCloseAcctFlag != null">
                #{bean.agreementCloseAcctFlag},
            </if>
            <if test="bean.oppositeInternalKey != null">
                #{bean.oppositeInternalKey},
            </if>
            <if test="bean.tranTimestamp != null">
                #{bean.tranTimestamp},
            </if>
        </trim>
    </insert>

    <select id="selectAgreementByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_KEY_TYPE = 'IK'
        and AGREEMENT_KEY = #{agreementKey}
        and AGREEMENT_STATUS in('A','N')
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectAgreementForXD" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_KEY = #{agreementKey}
        and AGREEMENT_TYPE in('XDB','ACC','BXD')
        and AGREEMENT_STATUS = 'A'
        AND START_DATE <![CDATA[ <=]]> #{startDate,jdbcType=DATE}
        AND END_DATE <![CDATA[ >= ]]> #{startDate,jdbcType=DATE}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectActiveAgreement" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_KEY = #{agreementKey}
        and AGREEMENT_STATUS = 'A'
        AND START_DATE <![CDATA[ <=]]> #{startDate,jdbcType=DATE}
        AND END_DATE <![CDATA[ >= ]]> #{startDate,jdbcType=DATE}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <delete id="deleteMbAgreementModel" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        delete
        from RB_AGREEMENT
        where AGREEMENT_KEY_TYPE = 'IK'
        and AGREEMENT_KEY = #{agreementKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <select id="getAgreementByAgreementId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_ID = #{agreementId}
        and AGREEMENT_STATUS in ('A','S')
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getActiveAgreementByIdAndStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_ID = #{agreementId}
        <if test="agreementStatus != null and agreementStatus != ''">
            AND AGREEMENT_STATUS = #{agreementStatus}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAgreementByAgreementId2" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_ID = #{agreementId}
        and AGREEMENT_STATUS in ('A','N')
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectMbAgreement" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_KEY_TYPE = 'IK'
        and AGREEMENT_KEY = #{agreementKey}
        and AGREEMENT_TYPE = #{agreementType}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_STATUS='A'
    </select>

    <select id="selectMbAgreementAll" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_KEY_TYPE = 'IK'
        and AGREEMENT_KEY = #{agreementKey}
        and AGREEMENT_TYPE = #{agreementType}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and (AGREEMENT_STATUS='A' or AGREEMENT_STATUS='N')
    </select>

    <select id="selectMbAgreementNteAll" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_KEY_TYPE = 'IK'
        and AGREEMENT_TYPE = #{agreementType}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_STATUS='A'
    </select>

    <select id="getAgreementByBaseAcctNo" parameterType="java.util.Map" resultType="String">
        select AGREEMENT_TYPE
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and AGREEMENT_STATUS = 'A'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAgrtMainInfoByBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select AGREEMENT_TYPE,START_DATE,END_DATE
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_STATUS = 'A'
    </select>
    <select id="selectMbAgreementByfive" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="acctSeqNo != null and acctSeqNo != ''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="prodType != null and prodType != ''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="ccy != null and ccy != ''">
            and ACCT_CCY = #{ccy}
        </if>
        and AGREEMENT_TYPE = #{agreementType}
        and AGREEMENT_STATUS in('A','N')
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectMbAgreementByCondition" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        <where>
            <if test="agreementType != null and agreementType != ''">
                and AGREEMENT_TYPE = #{agreementType}
            </if>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="agreementId != null and agreementId != ''">
                AND AGREEMENT_ID =#{agreementId}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
        and AGREEMENT_STATUS in('A','N')
    </select>

    <select id="getMbAgreementByAgreementStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="acctSeqNo != null and acctSeqNo != ''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="prodType != null and prodType != ''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="ccy != null and ccy != ''">
            and ACCT_CCY = #{ccy}
        </if>
        <if test="agreementStatus != null and agreementStatus != ''">
            and AGREEMENT_STATUS = #{agreementStatus}
        </if>
        and AGREEMENT_TYPE = #{agreementType}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectMbAgreementByfiveOne" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and PROD_TYPE = #{prodType}
        and ACCT_SEQ_NO = #{acctSeqNo}
        and ACCT_CCY = #{ccy}
        and AGREEMENT_STATUS in('A','N')
        <if test="agreementType != null and agreementType != ''">
            AND AGREEMENT_TYPE = #{agreementType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
    </select>

    <select id="selectIsMbAgreement" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        and PROD_TYPE = #{prodType}
        and ACCT_SEQ_NO = #{acctSeqNo}
        and ACCT_CCY = #{ccy}
        and AGREEMENT_STATUS in('A','N')
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectMbAgreementByfour" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        <if test="baseAcctNo != null and baseAcctNo != ''">
            where BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="prodType != null and prodType != ''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo != ''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="acctCcy != null and acctCcy != ''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_STATUS != 'E'
    </select>

    <select id="selectCosSignInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_TYPE in ('08','09','11','14','17','ZXY','SL')
        and LAST_CHANGE_DATE = #{lastChangeDate}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbFinAcctsForEod2" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where <![CDATA[ START_DATE <= #{runDate} ]]>
        and AGREEMENT_TYPE='FIN'
        and AGREEMENT_STATUS='A'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by START_DATE DESC
    </select>
    <select id="getAllMaturity" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where <![CDATA[ END_DATE <= #{lastRunDate} ]]>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_STATUS='A'
    </select>
    <select id="getMbFinAcctsForEodSplit" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(AGREEMENT_ID) START_KEY,
            MAX(AGREEMENT_ID) END_KEY,COUNT(1) ROW_COUNT
            FROM
            (
            SELECT
            DISTINCT AGREEMENT_ID,
            @rownum :=@rownum + 1 AS rownum
            FROM
            RB_AGREEMENT,
            (SELECT @rownum := -1) t
            where  <![CDATA[ START_DATE <= #{runDate}  ]]>
            and AGREEMENT_TYPE='FIN'
            and AGREEMENT_STATUS='A'
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            ORDER BY AGREEMENT_ID
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (AGREEMENT_ID) START_KEY, MAX (AGREEMENT_ID) END_KEY,COUNT(1) ROW_COUNT
            FROM (SELECT DISTINCT AGREEMENT_ID
            FROM RB_AGREEMENT
            where <![CDATA[ START_DATE <= #{runDate}  ]]>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            and AGREEMENT_TYPE='FIN'
            and AGREEMENT_STATUS='A'
            ORDER BY AGREEMENT_ID)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>
    <select id="getAgreementsByAcctNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='A'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="agreementType != null">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAgreementFeePackageByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_TYPE = 'PKG'
        and AGREEMENT_STATUS in('A','S','T','N')
        <![CDATA[ and START_DATE <= #{runDate}
        and END_DATE > #{runDate}  order by AGREEMENT_ID+0
        ]]>
    </select>
    <select id="getArrearAgreementFeePackage" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_ID = #{agreementId}
        and AGREEMENT_TYPE = 'PKG'
        and AGREEMENT_STATUS !='E'
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND START_DATE <![CDATA[<=]]> #{runDate}
        and END_DATE > #{runDate}  order by AGREEMENT_ID
    </select>

    <select id="selectAgreementByOppositeInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where OPPOSITE_INTERNAL_KEY = #{oppositeInternalKey}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and AGREEMENT_STATUS='A'
    </select>
    <select id="getAgreementByLoanBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='A'
        <if test="agreementId != null and agreementId != ''">
            and AGREEMENT_ID = #{agreementId}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>
    <select id="getAgreementByLoanBaseAcctNo1" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        <where>
            <if test="startDate != null">
                and START_DATE &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and END_DATE &lt;= #{endDate}
            </if>
            <if test="agreProdType != null and agreProdType != ''">
                and AGRE_PROD_TYPE = #{agreProdType}
            </if>
            <if test="agreementType != null and agreementType != ''">
                and AGREEMENT_TYPE = #{agreementType}
            </if>
            <if test="agreementId != null and agreementId != ''">
                and AGREEMENT_ID = #{agreementId}
            </if>
            <if test="baseAcctNo != null and baseAcctNo != ''">
                and BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="acctSeqNo != null and acctSeqNo != ''">
                and ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
    <select id="getAgreementByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='A'
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="prodType != null and prodType != ''">
            and AGRE_PROD_TYPE = #{prodType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>

    <select id="getAgreementByBaseAcctNoAgreementType" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='A'
        and AGREEMENT_TYPE = 'FAC'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAgreementByBaseAcctNoAgreementTypeE" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='E'
        and AGREEMENT_TYPE = 'FAC'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getAgreementByClientNo1" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbDdcEodSplit" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(AGREEMENT_KEY) START_KEY,
            MAX(AGREEMENT_KEY) END_KEY,COUNT(1) ROW_COUNT
            FROM
            (
            SELECT
            DISTINCT AGREEMENT_KEY,
            @rownum :=@rownum + 1 AS rownum
            FROM
            RB_AGREEMENT,
            (SELECT @rownum := -1) t
            where
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <![CDATA[START_DATE <= #{runDate}
            and END_DATE >= #{runDate}
            and AGREEMENT_TYPE='DDC'
            and AGREEMENT_STATUS='A'
            ]]>
            ORDER BY AGREEMENT_KEY
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (AGREEMENT_KEY) START_KEY, MAX (AGREEMENT_KEY) END_KEY,COUNT(1) ROW_COUNT
            FROM (SELECT DISTINCT AGREEMENT_KEY
            FROM RB_AGREEMENT
            where
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <![CDATA[ START_DATE <= #{runDate}
            and END_DATE >= #{runDate}
            and AGREEMENT_TYPE='DDC'
            and AGREEMENT_STATUS='A'
            ]]>
            ORDER BY AGREEMENT_KEY)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>
    <select id="queryDdcAcctForKeys" parameterType="java.util.Map" resultType="String">
        select DISTINCT AGREEMENT_KEY
        from RB_AGREEMENT
        where
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <![CDATA[ START_DATE <= #{runDate}
          and END_DATE >= #{runDate}
          and AGREEMENT_KEY BETWEEN #{startKey} and #{endKey}
          and AGREEMENT_TYPE = 'DDC'
          and AGREEMENT_STATUS = 'A'
        ]]>
    </select>
    <select id="queryDdcForDetails" parameterType="java.util.Map" resultType="String">
        select AGREEMENT_KEY
        from RB_AGREEMENT
        where
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <![CDATA[ START_DATE <= #{runDate}
          and END_DATE >= #{runDate}
          and AGREEMENT_KEY BETWEEN #{startKey} and #{endKey}
          and AGREEMENT_TYPE = 'DDC'
          and AGREEMENT_STATUS = 'A'
        ]]>
    </select>

    <select id="fuzzyQuery" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column_A"/>
        from RB_AGREEMENT A
        <where>
            <if test="agreementId != null and agreementId != ''">
                A.AGREEMENT_ID = #{agreementId}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <if test="baseAcctNo != null and baseAcctNo != ''">
                AND A.BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="startDate != null">
                <![CDATA[
            AND A.START_DATE >= #{startDate}
            ]]>
            </if>
            <if test="endDate != null">
                <![CDATA[
            AND A.START_DATE <= #{endDate}
            ]]>
            </if>
            <if test="agreementProdType != null and agreementProdType != ''">
                AND AGREEMENT_ID in
                (
                select AGREEMENT_ID
                from RB_AGREEMENT_IDEP
                where IDEP_PROD_TYPE = #{agreementProdType}
                )
            </if>
            <if test="agreementStatus != null and agreementStatus != ''">
                AND A.AGREEMENT_STATUS = #{agreementStatus}
            </if>
            <if test="clientNo != null and clientNo != ''">
                and A.CLIENT_NO = #{clientNo}
            </if>
        </where>
        ORDER BY A.AGREEMENT_ID,A.START_DATE ASC
    </select>

    <select id="getAgreementByAgrType" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='A'
        <if test="agreementType != null and agreementType != ''">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="agreementKey != null and agreementKey != ''">
            and AGREEMENT_KEY = #{agreementKey}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>

    <select id="selectAgrByType" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='A'
        <if test="agreementType != null and agreementType != ''">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <if test="agreementKey != null and agreementKey != ''">
            and AGREEMENT_KEY = #{agreementKey}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>


    <select id="selectAgrByTypeAndStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        <where>
            <if test="agreementType != null and agreementType != ''">
                and AGREEMENT_TYPE = #{agreementType}
            </if>
            <if test="agreementKey != null and agreementKey != ''">
                and AGREEMENT_KEY = #{agreementKey}
            </if>
            <if test="clientNo != null and clientNo != ''">
                and CLIENT_NO = #{clientNo}
            </if>
            <if test="agreementStatus != null and agreementStatus != ''">
                and AGREEMENT_STATUS = #{agreementStatus}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>

    <select id="selectAgrJdlWdlHxl" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS in ('A','N')
        and AGREEMENT_TYPE in ('JDL', 'WDL', 'HXL')
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectAllAgreement" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        <where>
            <if test="baseAcctNo != null and baseAcctNo != ''">
                BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="prodType != null and prodType != ''">
                AND PROD_TYPE = #{prodType}
            </if>
            <if test="ccy != null and ccy != ''">
                AND ACCT_CCY = #{ccy}
            </if>
            <if test="acctSeqNo != null and acctSeqNo != ''">
                AND ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="agreementStatus != null and agreementStatus != ''">
                AND AGREEMENT_STATUS = #{agreementStatus}
            </if>
            <if test="clientNo != null and clientNo != ''">
                and CLIENT_NO = #{clientNo}
            </if>
            <if test="agreementClass != null and agreementClass != ''">
                and AGREEMENT_CLASS = #{agreementClass}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>

    <select id="getAgreementBasic" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select <include refid="Base_Column"/>
        from MB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        <if test="channel != null and channel != ''">
            and CHANNEL = #{channel,jdbcType=VARCHAR}
        </if>
        <if test="businessId != null and businessId != ''">
            and BUSINESS_ID = #{businessId,jdbcType=VARCHAR}
        </if>
        <if test="signId != null and signId != ''">
            and SIGN_ID = #{signId,jdbcType=VARCHAR}
        </if>
        <if test="contractNo != null and contractNo != ''">
            and CONTRACT_NO = #{contractNo,jdbcType=VARCHAR}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="companyList != null">
            AND COMPANY IN
            <foreach item="item"  index="index" collection="companyList"  open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        and AGREEMENT_STATUS='A'
    </select>

    <select id="selectAgrByClass" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS='A'
        <if test="agreementClass != null and agreementClass != ''">
            and AGREEMENT_CLASS = #{agreementClass}
        </if>
        <if test="agreementKey != null and agreementKey != ''">
            and AGREEMENT_KEY = #{agreementKey}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="agreementClass != null and agreementClass != ''">
            and AGREEMENT_CLASS = #{agreementClass}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>

    <select id="selectAgreemetnsByFourElement" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="prodType != null and prodType != ''">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="ccy != null and ccy != ''">
            AND ACCT_CCY = #{ccy}
        </if>
        <if test="acctSeqNo != null and acctSeqNo != ''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectAgreemetnsByFourElement0163" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="prodType != null and prodType != ''">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="ccy != null and ccy != ''">
            AND ACCT_CCY = #{ccy}
        </if>
        <if test="acctSeqNo != null and acctSeqNo != ''">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="agreementClass != null and agreementClass != ''">
            and AGREEMENT_CLASS = #{agreementClass}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND AGREEMENT_TYPE IN('XDB','ACC','WDL','ZZB')
    </select>
    <select id="selectAllAgreementOther" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        <where>
            <if test="baseAcctNo != null and baseAcctNo != ''">
                BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="prodType != null and prodType != ''">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and ccy != ''">
                AND ACCT_CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="acctSeqNo != null and acctSeqNo != ''">
                AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
            </if>
            AND AGREEMENT_STATUS IN('A','N')
            AND AGREEMENT_TYPE IN('XDB','ACC','WDL','ZZB')
            <if test="clientNo != null and clientNo != ''">
                and CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
    <select id="getLyeRbAgreementInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT where AGREEMENT_STATUS IN('A','N')
        AND AGREEMENT_TYPE = 'LYE'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="prodType != null and prodType != ''">
            AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
        </if>
        <if test="ccy != null and ccy != ''">
            AND ACCT_CCY = #{ccy,jdbcType=VARCHAR}
        </if>
        <if test="acctSeqNo != null and acctSeqNo != ''">
            AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectExcludeAgreement" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_STATUS in('A','N')
        <if test="agreementKey != null and agreementKey != ''">
            and AGREEMENT_KEY = #{agreementKey}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="agreementType != null and agreementType != ''">
            and AGREEMENT_TYPE = #{agreementType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getAgreementForII" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        WHERE AGREEMENT_STATUS in('A','N')
        <if test="agreementKey != null and agreementKey != ''">
            and AGREEMENT_KEY = #{agreementKey}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND AGREEMENT_TYPE in
        <foreach item="item" index="index" collection="agreementType" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateCloseAgreementByClient"  >
        UPDATE <include refid="Table_Name" />
        <set>
            AGREEMENT_STATUS = 'E'
        </set>
        <where>
            CLIENT_NO = #{clientNo}
            AND AGREEMENT_KEY = #{agreementKey}
            AND AGREEMENT_CLOSE_ACCT_FLAG = 'Y'
            AND AGREEMENT_STATUS = 'A'
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </update>
    <select id="getMaxEndDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        MAX(END_DATE) END_DATE
        from RB_AGREEMENT
        WHERE AGREEMENT_TYPE in('XDB','ZXY','ZZB','WDL')
        and AGREEMENT_KEY = #{agreementKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CLIENT_NO = #{clientNo}
    </select>

    <select id="getRbAgreementByBaseAcctNoAndClientNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectFinMbAgreement" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where BASE_ACCT_NO = #{baseAcctNo}

        and ACCT_SEQ_NO = #{acctSeqNo}
        and ACCT_CCY = #{ccy}
        and AGREEMENT_STATUS = 'A'
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="prodType != null and prodType != ''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="agreementClass != null and agreementClass != ''">
            and AGREEMENT_CLASS = #{agreementClass}
        </if>
    </select>

    <select id="selectAgreementByStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        <where>
            <if test="baseAcctNo != null and baseAcctNo != ''">
                AND BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="agreementKey != null and agreementKey != ''">
                AND AGREEMENT_KEY = #{agreementKey}
            </if>
            <if test="prodType != null and prodType != ''">
                and PROD_TYPE = #{prodType}
            </if>
            <if test="acctSeqNo != null and acctSeqNo != ''">
                and ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="ccy != null and ccy != ''">
                and ACCT_CCY = #{ccy}
            </if>
            <if test="agreementType != null and agreementType != ''">
                and AGREEMENT_TYPE = #{agreementType}
            </if>
            <if test="agreementStatus != null and agreementStatus != ''">
                and AGREEMENT_STATUS = #{agreementStatus}
            </if>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
    <select id="getRbAgreementByBaseAcctNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        and AGREEMENT_STATUS = 'A'

        AND agreement_type in ('LYE','P2P','JZG','ZQS','ZCG')

        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getRbAgreementByBaseAcctNo1" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        and AGREEMENT_STATUS = 'E'

        AND agreement_type in ('LYE','P2P','JZG','ZQS','ZCG')

        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getRbAgreementByBaseAcctNoAndLYE" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        and agreement_type = 'LYE'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getAgreementById" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT WHERE AGREEMENT_ID = #{agreementId}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getAgreementByAgreementIdAll" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_ID = #{agreementId}
        and AGREEMENT_STATUS in ('A','S','E')
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getAgreementByAgreementEId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT
        where AGREEMENT_ID = #{agreementId}
        and AGREEMENT_STATUS in ('E')
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <!-- 根据客户号、协议类型、协议开始时间、协议结束时间、协议状态、内部键查询协议信息 -->
    <select id="selectAgreementByEle" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT
        <where>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="agreementType != null and agreementType != ''">
                AND AGREEMENT_TYPE = #{agreementType}
            </if>
            <if test="agreementStartDate != null and agreementStartDate != ''">
                AND START_DATE &gt;= #{agreementStartDate}
            </if>
            <if test="agreementEndDate != null and agreementEndDate != ''">
                AND END_DATE &lt;= #{agreementEndDate}
            </if>
            <if test="agreementStatus != null and agreementStatus != ''">
                AND AGREEMENT_STATUS = #{agreementStatus}
            </if>
            <if test="internalKey != null">
                AND AGREEMENT_KEY = #{internalKey}
            </if>
        </where>
        ORDER BY AGREEMENT_OPEN_DATE DESC
    </select>


    <!-- 根据账户四要素、协议类型、客户号、协议状态、开始时间、结束时间查询协议信息 -->
    <select id="getMbAgreementByEleAndStatus" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_AGREEMENT
       <where>
            <if test="baseAcctNo != null and baseAcctNo != ''">
                AND BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="acctSeqNo != null and acctSeqNo != ''">
                AND ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="acctCcy != null and acctCcy != ''">
                AND ACCT_CCY = #{acctCcy}
            </if>
            <if test="prodType != null and prodType != ''">
                AND PROD_TYPE = #{prodType}
            </if>
            <if test="agreementType != null and agreementType != ''">
                AND AGREEMENT_TYPE = #{agreementType}
            </if>
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="agreementStatus != null and agreementStatus != ''">
                AND AGREEMENT_STATUS = #{agreementStatus}
            </if>
            <if test="startDate != null and startDate != ''">
                AND START_DATE &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND END_DATE &lt;= #{endDate}
            </if>
       </where>
        ORDER BY AGREEMENT_OPEN_DATE DESC
    </select>
</mapper>
