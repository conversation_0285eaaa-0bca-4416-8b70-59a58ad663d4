<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    select <include refid="Base_Column"/>
    from RB_COMMISSION_REGISTER
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbCommissionRegisterByCommDocInfo"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    select <include refid="Base_Column"/>
    from RB_COMMISSION_REGISTER
    <where>
      <if test="commissionDocumentType != null">
        COMMISSION_DOCUMENT_TYPE = #{commissionDocumentType}
      </if>
      <if test="commissionDocumentId != null">
        AND COMMISSION_DOCUMENT_ID = #{commissionDocumentId}
      </if>
      <if test="clientNo != null and clientNo !='' " >
        AND CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>
  <select id="getMbCommissionRegisterByInterkey"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    select <include refid="Base_Column"/>
    from RB_COMMISSION_REGISTER
    <where>
      <if test="internalKey != null">
        AND  INTERNAL_KEY = #{internalKey}
      </if>
      <if test="programId != null">
        AND PROGRAM_ID = #{programId}
      </if>
      <if test="commissionDocType != null">
        AND COMMISSION_DOCUMENT_TYPE = #{commissionDocType}
      </if>
      <if test="commissionDocumentId != null">
        AND COMMISSION_DOCUMENT_ID = #{commissionDocumentId}
      </if>
      <if test="commissionClientName != null">
        AND COMMISSION_CLIENT_NAME like '%${commissionClientName}%'
      </if>
      <if test="clientNo != null and clientNo !='' " >
        AND CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    delete from RB_COMMISSION_REGISTER
    where INTERNAL_KEY = #{internalKey}
        <if test="clientNo != null and clientNo !='' " >
          AND CLIENT_NO = #{clientNo}
        </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    update RB_COMMISSION_REGISTER
    <set>
      <if test="eventType != null">
        EVENT_TYPE = #{eventType},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        ACCT_CCY = #{acctCcy},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="acctName != null">
        ACCT_NAME = #{acctName},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType},
      </if>
      <if test="prefix != null">
        PREFIX = #{prefix},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo},
      </if>
      <if test="commissionDocumentType != null">
        COMMISSION_DOCUMENT_TYPE = #{commissionDocumentType},
      </if>
      <if test="commissionDocumentId != null">
        COMMISSION_DOCUMENT_ID = #{commissionDocumentId},
      </if>
      <if test="commissionClientName != null">
        COMMISSION_CLIENT_NAME = #{commissionClientName},
      </if>
      <if test="commissionClientNo != null">
        COMMISSION_CLIENT_NO = #{commissionClientNo},
      </if>
      <if test="country != null">
        COUNTRY = #{country},
      </if>
      <if test="programId != null">
        PROGRAM_ID = #{programId},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
          <if test="clientNo != null and clientNo !='' " >
            AND CLIENT_NO = #{clientNo}
          </if>
          <!-- 多法人改造 by luocwa -->
          <if test="company != null and company != '' ">
            AND COMPANY = #{company}
          </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    insert into RB_COMMISSION_REGISTER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="eventType != null">
        EVENT_TYPE,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="acctName != null">
        ACCT_NAME,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="prefix != null">
        PREFIX,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="commissionDocumentType != null">
        COMMISSION_DOCUMENT_TYPE,
      </if>
      <if test="commissionDocumentId != null">
        COMMISSION_DOCUMENT_ID,
      </if>
      <if test="commissionClientName != null">
        COMMISSION_CLIENT_NAME,
      </if>
      <if test="commissionClientNo != null">
        COMMISSION_CLIENT_NO,
      </if>
      <if test="country != null">
        COUNTRY,
      </if>
      <if test="programId != null">
        PROGRAM_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="eventType != null">
        #{eventType},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="acctName != null">
        #{acctName},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
      <if test="prefix != null">
        #{prefix},
      </if>
      <if test="voucherNo != null">
        #{voucherNo},
      </if>
      <if test="commissionDocType != null">
        #{commissionDocType},
      </if>
      <if test="commissionDocId != null">
        #{commissionDocId},
      </if>
      <if test="commissionClientName != null">
        #{commissionClientName},
      </if>
      <if test="commissionClientNo != null">
        #{commissionClientNo},
      </if>
      <if test="country != null">
        #{country},
      </if>
      <if test="programId != null">
        #{programId},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
  <select id="getMbCommissionRegisterByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    SELECT <include refid="Base_Column"/>
    FROM
        RB_COMMISSION_REGISTER
    WHERE
        REFERENCE=#{reference }
        <if test="clientNo != null and clientNo !='' " >
          AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
  </select>

  <select id="getMbCommissionRegisterByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionRegister">
    SELECT <include refid="Base_Column"/>
    FROM
        RB_COMMISSION_REGISTER
    <where>
        <if test="baseAcctNo != null">
          AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctSeqNo != null">
          AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="clientNo != null and clientNo !='' " >
          AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
    </where>
  </select>
</mapper>
