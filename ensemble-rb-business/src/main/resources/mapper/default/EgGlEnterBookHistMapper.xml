<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.cm.reg.EgGlEnterBookHist">

    <sql id="Table_Name">
        ENS_EG.EG_GL_ENTER_BOOK_HIST
    </sql>

    <sql id="Base_Column">
        <trim suffixOverrides=",">
            SYSTEM_ID,
            TRAN_BRANCH,
            SOURCE_MODULE,
            AMOUNT_NATURE,
            INT_IND_FLAG,
            ATTR,
            TRAN_TYPE,
            CCY,
            BAL_DIRECT_TYPE,
            AMT_TYPE,
            TRAN_AMT,
            PROD_TYPE,
            AUX_FRE_FIELD,
            DEAL_FLAG,
            TRAN_DATE,
            ACCT_BRANCH,
            GL_CODE,
        </trim>
    </sql>

    <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.model.cm.reg.EgGlEnterBookHist">
        <result property="systemId" column="SYSTEM_ID"/>
        <result property="tranBranch" column="TRAN_BRANCH"/>
        <result property="sourceModule" column="SOURCE_MODULE"/>
        <result property="amountNature" column="AMOUNT_NATURE"/>
        <result property="intIndFlag" column="INT_IND_FLAG"/>
        <result property="attr" column="ATTR"/>
        <result property="tranType" column="TRAN_TYPE"/>
        <result property="ccy" column="CCY"/>
        <result property="balDirectType" column="BAL_DIRECT_TYPE"/>
        <result property="amtType" column="AMT_TYPE"/>
        <result property="tranAmt" column="TRAN_AMT"/>
        <result property="prodType" column="PROD_TYPE"/>
        <result property="auxFreField" column="AUX_FRE_FIELD"/>
        <result property="dealFlag" column="DEAL_FLAG"/>
        <result property="tranDate" column="TRAN_DATE"/>
        <result property="acctBranch" column="ACCT_BRANCH"/>
        <result property="glCode" column="GL_CODE"/>
    </resultMap>

    <sql id="Base_Where">
        <where>
            <if test="systemId != null and  systemId != '' ">
                AND SYSTEM_ID = #{systemId,jdbcType=VARCHAR}
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                AND SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="amountNature != null and  amountNature != '' ">
                AND AMOUNT_NATURE = #{amountNature,jdbcType=VARCHAR}
            </if>
            <if test="intIndFlag != null and  intIndFlag != '' ">
                AND INT_IND_FLAG = #{intIndFlag,jdbcType=VARCHAR}
            </if>
            <if test="attr != null and  attr != '' ">
                AND ATTR = #{attr,jdbcType=VARCHAR}
            </if>
            <if test="tranType != null and  tranType != '' ">
                AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="balDirectType != null and  balDirectType != '' ">
                AND BAL_DIRECT_TYPE = #{balDirectType,jdbcType=VARCHAR}
            </if>
            <if test="amtType != null and  amtType != '' ">
                AND AMT_TYPE = #{amtType,jdbcType=VARCHAR}
            </if>
            <if test="tranAmt != null ">
                AND TRAN_AMT = #{tranAmt,jdbcType=DECIMAL}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="auxFreField != null and  auxFreField != '' ">
                AND AUX_FRE_FIELD = #{auxFreField,jdbcType=VARCHAR}
            </if>
            <if test="dealFlag != null and  dealFlag != '' ">
                AND DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="tranDate != null ">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE}
            </if>
            <if test="acctBranch != null and  acctBranch != '' ">
                AND ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR}
            </if>
            <if test="glCode != null and  glCode != '' ">
                AND GL_CODE = #{glCode,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="PrimaryKey_Where">
        <where>
        </where>
    </sql>

    <sql id="Base_Set">
        <set>
            <if test="systemId != null and systemId != '' ">
                SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="tranBranch != null and tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="sourceModule != null and sourceModule != '' ">
                SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="amountNature != null and amountNature != '' ">
                AMOUNT_NATURE = #{amountNature,jdbcType=VARCHAR},
            </if>
            <if test="intIndFlag != null and intIndFlag != '' ">
                INT_IND_FLAG = #{intIndFlag,jdbcType=VARCHAR},
            </if>
            <if test="attr != null and attr != '' ">
                ATTR = #{attr,jdbcType=VARCHAR},
            </if>
            <if test="tranType != null and tranType != '' ">
                TRAN_TYPE = #{tranType,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null and ccy != '' ">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="balDirectType != null and balDirectType != '' ">
                BAL_DIRECT_TYPE = #{balDirectType,jdbcType=VARCHAR},
            </if>
            <if test="amtType != null and amtType != '' ">
                AMT_TYPE = #{amtType,jdbcType=VARCHAR},
            </if>
            <if test="tranAmt != null ">
                TRAN_AMT = #{tranAmt,jdbcType=DECIMAL},
            </if>
            <if test="prodType != null and prodType != '' ">
                PROD_TYPE = #{prodType,jdbcType=VARCHAR},
            </if>
            <if test="auxFreField != null and auxFreField != '' ">
                AUX_FRE_FIELD = #{auxFreField,jdbcType=VARCHAR},
            </if>
            <if test="dealFlag != null and dealFlag != '' ">
                DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="acctBranch != null and acctBranch != '' ">
                ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR},
            </if>
            <if test="glCode != null and glCode != '' ">
                GL_CODE = #{glCode,jdbcType=VARCHAR},
            </if>
        </set>
    </sql>

    <sql id="Base_Select">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </sql>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'"> ${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'"> *</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}</if>
    </sql>

    <sql id="comet_row_num_step_column">
        <if test="cometSqlType == 'total'"> count(*) AS TOTAL</if>
        <if test="cometSqlType == 'offset'"> *</if>
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemId != null ">SYSTEM_ID,</if>
            <if test="tranBranch != null ">TRAN_BRANCH,</if>
            <if test="sourceModule != null ">SOURCE_MODULE,</if>
            <if test="amountNature != null ">AMOUNT_NATURE,</if>
            <if test="intIndFlag != null ">INT_IND_FLAG,</if>
            <if test="attr != null ">ATTR,</if>
            <if test="tranType != null ">TRAN_TYPE,</if>
            <if test="ccy != null ">CCY,</if>
            <if test="balDirectType != null ">BAL_DIRECT_TYPE,</if>
            <if test="amtType != null ">AMT_TYPE,</if>
            <if test="tranAmt != null ">TRAN_AMT,</if>
            <if test="prodType != null ">PROD_TYPE,</if>
            <if test="auxFreField != null ">AUX_FRE_FIELD,</if>
            <if test="dealFlag != null ">DEAL_FLAG,</if>
            <if test="tranDate != null ">TRAN_DATE,</if>
            <if test="acctBranch != null ">ACCT_BRANCH,</if>
            <if test="glCode != null ">GL_CODE,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemId != null ">#{systemId,jdbcType=VARCHAR},</if>
            <if test="tranBranch != null ">#{tranBranch,jdbcType=VARCHAR},</if>
            <if test="sourceModule != null ">#{sourceModule,jdbcType=VARCHAR},</if>
            <if test="amountNature != null ">#{amountNature,jdbcType=VARCHAR},</if>
            <if test="intIndFlag != null ">#{intIndFlag,jdbcType=VARCHAR},</if>
            <if test="attr != null ">#{attr,jdbcType=VARCHAR},</if>
            <if test="tranType != null ">#{tranType,jdbcType=VARCHAR},</if>
            <if test="ccy != null ">#{ccy,jdbcType=VARCHAR},</if>
            <if test="balDirectType != null ">#{balDirectType,jdbcType=VARCHAR},</if>
            <if test="amtType != null ">#{amtType,jdbcType=VARCHAR},</if>
            <if test="tranAmt != null ">#{tranAmt,jdbcType=DECIMAL},</if>
            <if test="prodType != null ">#{prodType,jdbcType=VARCHAR},</if>
            <if test="auxFreField != null ">#{auxFreField,jdbcType=VARCHAR},</if>
            <if test="dealFlag != null ">#{dealFlag,jdbcType=VARCHAR},</if>
            <if test="tranDate != null ">#{tranDate,jdbcType=DATE},</if>
            <if test="acctBranch != null ">#{acctBranch,jdbcType=VARCHAR},</if>
            <if test="glCode != null ">#{glCode,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>

    <update id="updateByPrimaryKey">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>


    <update id="updateByEntity">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="s.systemId != null and s.systemId != '' ">
                SYSTEM_ID = #{s.systemId,jdbcType=VARCHAR},
            </if>
            <if test="s.tranBranch != null and s.tranBranch != '' ">
                TRAN_BRANCH = #{s.tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceModule != null and s.sourceModule != '' ">
                SOURCE_MODULE = #{s.sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="s.amountNature != null and s.amountNature != '' ">
                AMOUNT_NATURE = #{s.amountNature,jdbcType=VARCHAR},
            </if>
            <if test="s.intIndFlag != null and s.intIndFlag != '' ">
                INT_IND_FLAG = #{s.intIndFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.attr != null and s.attr != '' ">
                ATTR = #{s.attr,jdbcType=VARCHAR},
            </if>
            <if test="s.tranType != null and s.tranType != '' ">
                TRAN_TYPE = #{s.tranType,jdbcType=VARCHAR},
            </if>
            <if test="s.ccy != null and s.ccy != '' ">
                CCY = #{s.ccy,jdbcType=VARCHAR},
            </if>
            <if test="s.balDirectType != null and s.balDirectType != '' ">
                BAL_DIRECT_TYPE = #{s.balDirectType,jdbcType=VARCHAR},
            </if>
            <if test="s.amtType != null and s.amtType != '' ">
                AMT_TYPE = #{s.amtType,jdbcType=VARCHAR},
            </if>
            <if test="s.tranAmt != null ">
                TRAN_AMT = #{s.tranAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.prodType != null and s.prodType != '' ">
                PROD_TYPE = #{s.prodType,jdbcType=VARCHAR},
            </if>
            <if test="s.auxFreField != null and s.auxFreField != '' ">
                AUX_FRE_FIELD = #{s.auxFreField,jdbcType=VARCHAR},
            </if>
            <if test="s.dealFlag != null and s.dealFlag != '' ">
                DEAL_FLAG = #{s.dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.tranDate != null ">
                TRAN_DATE = #{s.tranDate,jdbcType=DATE},
            </if>
            <if test="s.acctBranch != null and s.acctBranch != '' ">
                ACCT_BRANCH = #{s.acctBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.glCode != null and s.glCode != '' ">
                GL_CODE = #{s.glCode,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="w.systemId != null and w.systemId != '' ">
                AND SYSTEM_ID = #{w.systemId,jdbcType=VARCHAR}
            </if>
            <if test="w.tranBranch != null and w.tranBranch != '' ">
                AND TRAN_BRANCH = #{w.tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceModule != null and w.sourceModule != '' ">
                AND SOURCE_MODULE = #{w.sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="w.amountNature != null and w.amountNature != '' ">
                AND AMOUNT_NATURE = #{w.amountNature,jdbcType=VARCHAR}
            </if>
            <if test="w.intIndFlag != null and w.intIndFlag != '' ">
                AND INT_IND_FLAG = #{w.intIndFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.attr != null and w.attr != '' ">
                AND ATTR = #{w.attr,jdbcType=VARCHAR}
            </if>
            <if test="w.tranType != null and w.tranType != '' ">
                AND TRAN_TYPE = #{w.tranType,jdbcType=VARCHAR}
            </if>
            <if test="w.ccy != null and w.ccy != '' ">
                AND CCY = #{w.ccy,jdbcType=VARCHAR}
            </if>
            <if test="w.balDirectType != null and w.balDirectType != '' ">
                AND BAL_DIRECT_TYPE = #{w.balDirectType,jdbcType=VARCHAR}
            </if>
            <if test="w.amtType != null and w.amtType != '' ">
                AND AMT_TYPE = #{w.amtType,jdbcType=VARCHAR}
            </if>
            <if test="w.tranAmt != null ">
                AND TRAN_AMT = #{w.tranAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.prodType != null and w.prodType != '' ">
                AND PROD_TYPE = #{w.prodType,jdbcType=VARCHAR}
            </if>
            <if test="w.auxFreField != null and w.auxFreField != '' ">
                AND AUX_FRE_FIELD = #{w.auxFreField,jdbcType=VARCHAR}
            </if>
            <if test="w.dealFlag != null and w.dealFlag != '' ">
                AND DEAL_FLAG = #{w.dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.tranDate != null ">
                AND TRAN_DATE = #{w.tranDate,jdbcType=DATE}
            </if>
            <if test="w.acctBranch != null and w.acctBranch != '' ">
                AND ACCT_BRANCH = #{w.acctBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.glCode != null and w.glCode != '' ">
                AND GL_CODE = #{w.glCode,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <delete id="delete">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </delete>

    <select id="count" parameterType="java.util.Map" resultType="int">
        SELECT count(1) FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </select>

    <select id="selectOne" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectList" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
        <include refid="Base_Select"/>
        FOR UPDATE
    </select>

    <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </select>

</mapper>
