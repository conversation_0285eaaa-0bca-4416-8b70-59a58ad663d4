<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBlackName">


    <select id="selectByRbBlackName" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBlackName">
   select *
    from RB_BLACK_NAME
    where BASE_ACCT_NO = #{baseAcctNo}
      AND DOCUMENT_ID = #{documentId}
		  AND DOCUMENT_TYPE = #{documentType}
		  AND BLACK_NO =#{blackNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>


    <update id="updateBlackNameBySeq" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBlackName" >
        UPDATE RB_BLACK_NAME
        <set>
            <if test="blackNo != null and  blackNo != '' ">
                BLACK_NO= #{blackNo},
            </if>
            <if test="clientName != null and  clientName != '' ">
                CLIENT_NAME = #{clientName},
            </if>
            <if test="remark1 != null and  remark1 != '' ">
                REMARK1 = #{remark1},
            </if>
            <if test="isOurBankFlag != null and  isOurBankFlag != '' ">
                IS_OUR_BANK_FLAG = #{isOurBankFlag},
            </if>
            <if test="remark2 != null and  remark2 != '' ">
                REMARK2 = #{remark2},
            </if>
            <if test="acctSourceType != null and  acctSourceType != '' ">
                LIST_TYPE = #{acctSourceType},
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo},
            </if>
            <if test="acctStatus != null and  acctStatus != '' ">
                ACCT_STATUS = #{acctStatus},
            </if>
            <if test="globalIdType != null and  globalIdType != '' ">
                DOCUMENT_TYPE = #{globalIdType},
            </if>
            <if test="createDate != null ">
                CREATE_DATE = #{createDate},
            </if>
            <if test="expireDate != null ">
                EXPIRE_DATE = #{expireDate},
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                CLIENT_NO = #{clientNo},
            </if>
            <if test="documentId != null and  documentId != '' ">
                DOCUMENT_ID = #{documentId},
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE = #{effectDate},
            </if>
            <if test="uncounterDesc != null and  uncounterDesc != '' ">
                UNCOUNTER_DESC = #{uncounterDesc},
            </if>
            <if test="remark3 != null and  remark3 != '' ">
                REMARK3 = #{remark3},
            </if>
        </set>
        where BLACK_SEQ = #{blackSeq}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

</mapper>
