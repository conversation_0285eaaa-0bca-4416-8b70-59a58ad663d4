<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionParameter">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionParameter"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionParameter">
    select
    <include refid="Base_Column"/>
    from RB_COMMISSION_PARAMETER
    where PROGRAM_ID = #{programId}
    <if test="clientType != null">
      AND CLIENT_TYPE = #{clientType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionParameter">
    delete from RB_COMMISSION_PARAMETER
    where PROGRAM_ID = #{programId}
    <if test="clientType != null">
      AND CLIENT_TYPE = #{clientType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionParameter">
    update RB_COMMISSION_PARAMETER
    <set>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where PROGRAM_ID = #{programId}
        AND CLIENT_TYPE = #{clientType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionParameter">
    insert into RB_COMMISSION_PARAMETER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="programId != null">
        PROGRAM_ID,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="programId != null">
        #{programId},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="getCommissionParameter" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionParameter">
    select
    <include refid="Base_Column"/>
    from RB_COMMISSION_PARAMETER
    <where>
    <if test="programId != null and programId !=''">
      AND PROGRAM_ID = #{programId}
    </if>
    <if test="clientType != null and clientType !=''">
      AND CLIENT_TYPE = #{clientType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
</mapper>
