<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdBalanceChangeRule">

	<select id="queryChangeRule" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdBalanceChangeRule" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdBalanceChangeRule">
		select
		<include refid="Base_Column"/>
		from rb_prod_balance_change_rule
		where (event_type =  #{eventType} or event_type = 'ALL')
		  and (tran_type = #{tranType} or tran_type = 'ALL')
		  and (prod_type = #{prodType} or prod_type = 'ALL')
		  and (amt_type = #{amtType} or amt_type = 'ALL')
		  and (accounting_status = #{accountingStatus} or accounting_status = 'ALL')
		  and (cr_dr_ind = #{crDrInd} or cr_dr_ind is null or cr_dr_ind = '')
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
		 and company = #{company}
		</if>
	</select>

</mapper>
