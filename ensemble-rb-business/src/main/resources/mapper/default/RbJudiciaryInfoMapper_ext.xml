<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.cm.batch.RbJudiciaryInfo">

  <insert id="insert" parameterType="com.dcits.ensemble.rb.business.model.cm.batch.RbJudiciaryInfo">
    insert into RB_JUDICIARY_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="documentId != null">
        DOCUMENT_ID,
      </if>
      <if test="documentType != null">
        DOCUMENT_TYPE,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="deductionJudiciaryName != null">
        DEDUCTION_JUDICIARY_NAME,
      </if>
      <if test="lawNo != null">
        LAW_NO,
      </if>
      <if test="judiciaryOfficerName != null">
        JUDICIARY_OFFICER_NAME,
      </if>
      <if test="judiciaryDocumentType != null">
        JUDICIARY_DOCUMENT_TYPE,
      </if>
      <if test="judiciaryDocumentId != null">
        JUDICIARY_DOCUMENT_ID,
      </if>
      <if test="judiciaryOthOfficerName != null">
        JUDICIARY_OTH_OFFICER_NAME,
      </if>
      <if test="judiciaryOthDocumentType != null">
        JUDICIARY_OTH_DOCUMENT_TYPE,
      </if>
      <if test="judiciaryOthDocumentId != null">
        JUDICIARY_OTH_DOCUMENT_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="judiciaryDocumentType2 != null">
        JUDICIARY_DOCUMENT_TYPE2,
      </if>
      <if test="judiciaryDocumentId2 != null">
        JUDICIARY_DOCUMENT_ID2,
      </if>
      <if test="judiciaryOthDocumentType2 != null">
        JUDICIARY_OTH_DOCUMENT_TYPE2,
      </if>
      <if test="judiciaryOthDocumentId2 != null">
        JUDICIARY_OTH_DOCUMENT_ID2,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="documentId != null">
        #{documentId},
      </if>
      <if test="documentType != null">
        #{documentType},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="deductionJudiciaryName != null">
        #{deductionJudiciaryName},
      </if>
      <if test="lawNo != null">
        #{lawNo},
      </if>
      <if test="judiciaryOfficerName != null">
        #{judiciaryOfficerName},
      </if>
      <if test="judiciaryDocumentType != null">
        #{judiciaryDocumentType},
      </if>
      <if test="judiciaryDocumentId != null">
        #{judiciaryDocumentId},
      </if>
      <if test="judiciaryOthOfficerName != null">
        #{judiciaryOthOfficerName},
      </if>
      <if test="judiciaryOthDocumentType != null">
        #{judiciaryOthDocumentType},
      </if>
      <if test="judiciaryOthDocumentId != null">
        #{judiciaryOthDocumentId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="judiciaryDocumentType2 != null">
        #{judiciaryDocumentType2},
      </if>
      <if test="judiciaryDocumentId2 != null">
        #{judiciaryDocumentId2},
      </if>
      <if test="judiciaryOthDocumentType2 != null">
        #{judiciaryOthDocumentType2},
      </if>
      <if test="judiciaryOthDocumentId2 != null">
        #{judiciaryOthDocumentId2},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

</mapper>