<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeedbackDetail">


	<select id="getFeedbackDetail" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeedbackDetail">
		SELECT <include refid="Base_Column"/>
		FROM  RB_FEEDBACK_DETAIL
		WHERE RESERVE5 = #{branch} and FEEDBACK_DATE between  #{startDate} and  #{endDate}
		<!--多法人改造 by luocwa  -->
		<if test="companyList != null">
			AND COMPANY in
			<foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>



</mapper>