<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">

  <select id="selectByCondition" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_AGR
    where AMORTIZE_STATUS in ('P' , 'A')
    <if test="agreementId != null">
      AND AGREEMENT_ID = #{agreementId}
    </if>
      <if test="internalKey != null">
        AND INTERNAL_KEY = #{internalKey}
      </if>
      <if test="clientNo != null">
        AND CLIENT_NO = #{clientNo}
      </if>
      <if test="tranType != null">
        AND TRAN_TYPE = #{tranType}
      </if>
      <if test="feeType != null">
        AND FEE_TYPE = #{feeType}
      </if>
      <if test="amortizeName != null">
        AND AMORTIZE_NAME = #{amortizeName}
      </if>
      <if test="amortizePeriodType != null">
        AND AMORTIZE_PERIOD_TYPE = #{amortizePeriodType}
      </if>
      <if test="amortizeTimeType != null">
        AND AMORTIZE_TIME_TYPE = #{amortizeTimeType}
      </if>
      <if test="amortizeDay != null">
        AND AMORTIZE_DAY = #{amortizeDay}
      </if>
      <if test="lastAmortizeDate != null">
        AND LAST_AMORTIZE_DATE = #{lastAmortizeDate}
      </if>
      <if test="nextAmortizeDate != null">
        AND NEXT_AMORTIZE_DATE = #{nextAmortizeDate}
      </if>
      <if test="amortizeCcy != null">
        AND AMORTIZE_CCY = #{amortizeCcy}
      </if>
      <if test="amortizeTotalAmt != null">
        AND AMORTIZE_TOTAL_AMT = #{amortizeTotalAmt}
      </if>
      <if test="amortizedAmt != null">
        AND AMORTIZED_AMT = #{amortizedAmt}
      </if>
      <if test="remainAmortizeAmt != null">
        AND REMAIN_AMORTIZE_AMT = #{remainAmortizeAmt}
      </if>
      <if test="amortizeTotalCnt != null">
        AND AMORTIZE_TOTAL_CNT = #{amortizeTotalCnt}
      </if>
      <if test="amortizedCnt != null">
        AND AMORTIZED_CNT = #{amortizedCnt}
      </if>
      <if test="remainAmortizeCnt != null">
        AND REMAIN_AMORTIZE_CNT = #{remainAmortizeCnt}
      </if>
      <if test="effectDate != null">
        AND EFFECT_DATE = #{effectDate,jdbcType=VARCHAR}
      </if>
      <if test="endDate != null">
        AND END_DATE = #{endDate}
      </if>
      <if test="amortizeStatus != null">
        AND AMORTIZE_STATUS = #{amortizeStatus}
      </if>
      <if test="tranDate != null">
        AND TRAN_DATE = #{tranDate}
      </if>
      <if test="tranBranch != null">
        AND TRAN_BRANCH = #{tranBranch}
      </if>
      <if test="userId != null">
        AND USER_ID = #{userId}
      </if>
      <if test="authUserId != null">
        AND AUTH_USER_ID = #{authUserId}
      </if>
      <if test="reference != null">
        AND REFERENCE = #{reference}
      </if>
      <if test="channelSeqNo != null">
        AND CHANNEL_SEQ_NO = #{channelSeqNo}
      </if>
      <if test="bankSeqNo != null">
        AND BANK_SEQ_NO = #{bankSeqNo}
      </if>
      <if test="sourceModule != null">
        AND SOURCE_MODULE = #{sourceModule}
      </if>
      <if test="sourceType != null">
        AND SOURCE_TYPE = #{sourceType}
      </if>
      <if test="osdSeqNo != null">
        AND OSD_SEQ_NO = #{osdSeqNo}
      </if>
      <if test="batchSeqNo != null">
        AND BATCH_SEQ_NO = #{batchSeqNo}
      </if>
      <if test="reversalFlag != null">
        AND REVERSAL_FLAG = #{reversalFlag}
      </if>
      <if test="reversalUserId != null">
        AND REVERSAL_USER_ID = #{reversalUserId}
      </if>
      <if test="reversalAuthUserId != null">
        AND REVERSAL_AUTH_USER_ID = #{reversalAuthUserId}
      </if>
      <if test="reversalBranch != null">
        AND REVERSAL_BRANCH = #{reversalBranch}
      </if>
      <if test="tranTimestamp != null">
        AND TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
      <if test="fromDate != null">
        <![CDATA[AND EFFECT_DATE >= #{fromDate}]]>
      </if>
      <if test="toDate != null">
        <![CDATA[AND EFFECT_DATE <= #{toDate}]]>
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
      order by EFFECT_DATE , AMORTIZE_STATUS desc
  </select>
<!--  <select id="selectMbFeeAmortizeAgr" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">-->
<!--    select <include refid="Base_Column"/>-->
<!--    <![CDATA[-->
<!--      from RB_FEE_AMORTIZE_AGR-->
<!--      where EFFECT_DATE <= #{baseParam.runDate}-->
<!--      and END_DATE >= #{baseParam.runDate}-->
<!--      and NEXT_AMORTIZE_DATE >= #{baseParam.lastRunDate}-->
<!--      order by AGREEMENT_ID+0-->
<!--    ]]>-->
<!--  </select>-->
  <select id="selectAmoByPayModel" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_AGR
    <where>
      <if test="preAccrNo != null and preAccrNo != ''">
        AGREEMENT_ID = #{preAccrNo}
      </if>
      <if test="startDate != null and startDate != ''">
        and EFFECT_DATE <![CDATA[ >= ]]> #{startDate,jdbcType=DATE}
      </if>
      <if test="endDate != null and endDate != ''">
        and EFFECT_DATE <![CDATA[ <= ]]> #{endDate,jdbcType=DATE}
      </if>
      <if test="internalKey != null and internalKey != ''">
        and INTERNAL_KEY = #{internalKey}
      </if>
      <if test="branch != null and branch != ''">
        and TRAN_BRANCH = #{branch}
      </if>
      <if test="userId != null and userId != ''">
        and USER_ID = #{userId}
      </if>
      <if test="clientNo != null and clientNo != ''">
        and CLIENT_NO = #{clientNo}
      </if>
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    order by EFFECT_DATE desc
  </select>

  <select id="selectFeeAmortizeInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_AGR
    <where>
      <if test="agreementId != null">
        AGREEMENT_ID = #{agreementId}
      </if>
      <if test="amortizeStatus != null and amortizeStatus != ''">
        AND AMORTIZE_STATUS = #{amortizeStatus}
      </if>
      <if test="fromDate != null and fromDate != ''">
        <![CDATA[AND EFFECT_DATE >= #{fromDate}]]>
      </if>
      <if test="toDate != null and toDate != ''">
        <![CDATA[AND EFFECT_DATE <= #{toDate}]]>
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="companyList != null">
        AND COMPANY in
        <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by EFFECT_DATE desc, AMORTIZE_STATUS desc
  </select>

  <select id="getMbFeeAmortizeAgrByReferenceAndClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_AGR
    where REFERENCE = #{reference}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbFeeAmortizeAgrByAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_AGR
    where agreement_Id = #{agreementId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbFeeAmortizeAgrByReferenceAndCompany" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_AGR
    where REFERENCE = #{reference}
    <!-- 多法人改造 by luocwa -->
    <if test="companyList != null">
      AND COMPANY in
      <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectRbFeeAmortizeAgrByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_AGR
    where
    REFERENCE in
    <foreach collection="list" item="reference" index="index" open="(" close=")" separator=",">
      #{references}
    </foreach>
  </select>

  <select id="getRbFeeAmortizeAgrCount" parameterType="java.util.Map" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM RB_FEE_AMORTIZE_AGR
    WHERE internal_key =#{internalKey} AND <![CDATA[EFFECT_DATE <= #{runDate}]]> AND AMORTIZE_STATUS in ('A','P')
  </select>
</mapper>
