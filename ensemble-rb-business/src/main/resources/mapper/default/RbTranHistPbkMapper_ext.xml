<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistPbk">

  <resultMap id="MbTranHistPbkMap" type="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistPbk">
    <result column="TRAN_DATE" property="tranDate" jdbcType="DATE" javaType="DATE" />
  </resultMap>
  <select id="issDealed" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistPbk">
    select
    <include refid="Base_Column"/>
    from RB_TRAN_HIST_PBK
    <where>
    <if test="reference !=  null">
      AND REFERENCE= #{reference}
    </if>
    <if test="internalKey !=  null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="othInternalKey !=  null">
      AND OTH_INTERNAL_KEY = #{othInternalKey}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <!-- Please try not to modify the sql below to print current passbook and settle interest -->
  <select id="getUnprintTranHistForHqi" parameterType="java.util.Map"
          resultMap="MbTranHistPbkMap">
    select
      <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    where INTERNAL_KEY = #{internalKey}
    and PBK_UPD_FLAG = 'N'
    and client_no= #{clientNo}
    <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
      and TRAN_STATUS != 'W'
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO asc
  </select>

  <select id="getUnprintTranHistByPage" parameterType="java.util.Map"
          resultMap="MbTranHistPbkMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    where INTERNAL_KEY = #{internalKey}
    and PBK_UPD_FLAG = 'N'
    and CLIENT_NO=#{clientNo}
    and EVENT_TYPE != 'CYCLE'
    <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
      and TRAN_STATUS != 'W'
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO asc
  </select>

  <select id="getInternalkeyByAcctSeqNo" parameterType="java.util.Map"
          resultMap="MbTranHistPbkMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    where
    BASE_ACCT_NO = #{baseAcctNo}
    AND CLIENT_NO=#{clientNo}
    AND ACCT_SEQ_NO = #{acctSeqNo}
    AND SOURCE_TYPE IS NOT NULL
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP asc,SEQ_NO asc
  </select>

  <select id="getUnprintTranHistByPageNotNI" parameterType="java.util.Map"
          resultMap="MbTranHistPbkMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    where INTERNAL_KEY = #{internalKey}
    and PBK_UPD_FLAG = 'N'
    and SOURCE_TYPE != 'NI'
    and CLIENT_NO=#{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO asc
  </select>
  <select id="getPrintTranHist" parameterType="java.util.Map"
          resultMap="MbTranHistPbkMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    where INTERNAL_KEY = #{internalKey}
    and PBK_UPD_FLAG = 'Y'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_DATE,SEQ_NO
  </select>
    <select id="getPrintTranHist1" parameterType="java.util.Map"
            resultMap="MbTranHistPbkMap">
        select
        <include refid="Base_Column"/>
        from RB_tran_hist_pbk
        where INTERNAL_KEY in (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo} and acct_status != 'C' )
        and PBK_UPD_FLAG = 'Y'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by TRAN_DATE,SEQ_NO
    </select>

  <select id="getPrintTranHistByPbkUpdFlag" parameterType="java.util.Map"
          resultMap="MbTranHistPbkMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    where INTERNAL_KEY = #{internalKey}
    and PBK_UPD_FLAG = 'Y'
    and CR_DR_IND='C'
    and EVENT_TYPE not in ('CYCLE','CAPT')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_DATE desc
  </select>

  <update id="updatePbkUpdFlagBySeqNo" parameterType="java.util.Map">
    update RB_tran_hist_pbk set pbk_upd_flag ='Y'
    where SEQ_NO = #{seqNo}
    AND pbk_upd_flag = 'N'
    <if test="clientNo !=  null">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updatePbkUpdFlagBySeqNo1" parameterType="java.util.Map">
    update RB_tran_hist_pbk set pbk_upd_flag ='N'
    where pbk_upd_flag = 'Y'
    AND INTERNAL_KEY = #{internalKey}
    <if test="clientNo !=  null">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updatePbkUpdFlagByCloseInternalKey" parameterType="java.util.Map">
    update RB_tran_hist_pbk set pbk_upd_flag ='Y'
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo !=  null">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updatePbkUpdFlagBySeqNoAndInternalKey" parameterType="java.util.Map">
    update RB_tran_hist_pbk set pbk_upd_flag ='N'
    where pbk_upd_flag = 'Y'
    AND INTERNAL_KEY = #{internalKey}
    and SEQ_NO=#{seqNo}
    <if test="clientNo !=  null">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getPrintTranHistInfo" parameterType="java.util.Map" resultMap="MbTranHistPbkMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    <where>
      <trim suffixOverrides="AND">
        <if test="acctType != null and acctType == 'C'.toString()">
          INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo}
          <if test="closeFlag == null or closeFlag == ''">
            and acct_status != 'C'
          </if>
          ) AND
        </if>
        <if test="acctType != null and acctType == 'T'.toString()">
          INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo})
          AND EVENT_TYPE != 'CYCLE' AND
        </if>
        <if test="pbkUdpFlag != null and pbkUdpFlag != ''">
          PBK_UPD_FLAG = #{pbkUdpFlag} AND
        </if>
        <if test="clientNo != null and clientNo != ''">
          CLIENT_NO=#{clientNo} AND
        </if>
        <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
          TRAN_STATUS != 'W' AND
        </if>
      </trim>
    </where>
    order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO asc
  </select>
    <select id="getPrintTranHistInfoForNum" parameterType="java.util.Map" resultType="int">
        select
        count(1)
        from RB_tran_hist_pbk
        <where>
            <trim suffixOverrides="AND">
                <if test="acctType != null and acctType == 'C'.toString()">
                    INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo}
                    <if test="closeFlag == null or closeFlag == ''">
                        and acct_status != 'C'
                    </if>
                    ) AND
                </if>
                <if test="acctType != null and acctType == 'T'.toString()">
                    INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo})
                    AND EVENT_TYPE != 'CYCLE' AND
                </if>
                <if test="pbkUdpFlag != null and pbkUdpFlag != ''">
                    PBK_UPD_FLAG = #{pbkUdpFlag} AND
                </if>
                <if test="clientNo != null and clientNo != ''">
                    CLIENT_NO=#{clientNo} AND
                </if>
                <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
                    TRAN_STATUS != 'W' AND
                </if>
            </trim>
        </where>
        order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO asc
    </select>

  <update id="updateMergePbkUpdFlag" parameterType="java.util.Map">
    update RB_tran_hist_pbk set PBK_UPD_FLAG ='Y'
    where PBK_UPD_FLAG = 'N'
    <if test="seqNos != null ">
      AND SEQ_NO IN
      <foreach collection="seqNos" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </if>
  </update>


  <select id="getMbTranHistPbkByRef" parameterType="java.util.Map" resultMap="MbTranHistPbkMap">
    select
    <include refid="Base_Column"/>
    from RB_tran_hist_pbk
    where reference = #{reference}
  </select>
</mapper>
