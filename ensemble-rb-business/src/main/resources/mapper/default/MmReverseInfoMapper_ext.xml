<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">

<sql id="Base_Column_List">
    REVERSAL_NO,
    REFERENCE,
    INTERNAL_KEY,
    DEAL_NO,
    CLIENT_NO,
    TAKE_ACCT_NO,
    CCY,
    PRINCIPAL_AMT,
    LINK_DEAL_INTERNAL_KEY,
    OTH_DEAL_NO,
    COUNTERPARTY,
    PLACE_ACCT_NO,
    EFFECT_DATE,
    MATURITY_DATE,
    REVERSAL_STATUS,
    APPR_USER_ID,
    APPROVAL_DATE,
    TRAN_DATE,
    REVERSE_USER_ID,
    LAST_CHANGE_DATE,
    RENEW_DATE,
    REVERS<PERSON>_REASON,
    TRAN_TIMESTAMP,
    R<PERSON><PERSON><PERSON>_REASON,
    REVERSE_DATE,
    RE<PERSON><PERSON><PERSON>_BRANCH,
    APPR_BRANCH
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    select <include refid="Base_Column_List"/>
    from MM_REVERSE_INFO
    where REVERSAL_NO = #{reversalNo}
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    delete from MM_REVERSE_INFO
    where REVERSAL_NO = #{reversalNo}
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    update MM_REVERSE_INFO
    <set>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="dealNo != null">
        DEAL_NO = #{dealNo},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="othDealNo != null">
        OTH_DEAL_NO = #{othDealNo},
      </if>
      <if test="counterparty != null">
        COUNTERPARTY = #{counterparty},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO = #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO = #{placeAcctNo},
      </if>
      <if test="reversalStatus != null">
        REVERSAL_STATUS = #{reversalStatus},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT = #{principalAmt},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="reverseUserId != null">
        REVERSE_USER_ID = #{reverseUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID = #{apprUserId},
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE = #{approvalDate},
      </if>
      <if test="reversalReason != null">
        REVERSAL_REASON = #{reversalReason},
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY = #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        RENEW_DATE = #{renewDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP= #{tranTimestamp},
      </if>
      <if test="refuseReason != null">
        REFUSE_REASON= #{refuseReason},
      </if>
      <if test="reverseDate != null">
        REVERSE_DATE= #{reverseDate},
      </if>
      <if test="reverseBranch != null">
        REVERSE_BRANCH= #{reverseBranch},
      </if>
      <if test="apprBranch != null">
        APPR_BRANCH= #{apprBranch}
      </if>
    </set>
    where REVERSAL_NO = #{reversalNo}
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    insert into MM_REVERSE_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reversalNo != null">
        REVERSAL_NO,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="dealNo != null">
        DEAL_NO,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="othDealNo != null">
        OTH_DEAL_NO,
      </if>
      <if test="counterparty != null">
        COUNTERPARTY,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="takeAcctNo != null">
        TAKE_ACCT_NO,
      </if>
      <if test="placeAcctNo != null">
        PLACE_ACCT_NO,
      </if>
      <if test="reversalStatus != null">
        REVERSAL_STATUS,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="principalAmt != null">
        PRINCIPAL_AMT,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="reverseUserId != null">
        REVERSE_USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="apprUserId != null">
        APPR_USER_ID,
      </if>
      <if test="approvalDate != null">
        APPROVAL_DATE,
      </if>
      <if test="reversalReason != null">
        REVERSAL_REASON,
      </if>
      <if test="linkDealInternalKey != null">
        LINK_DEAL_INTERNAL_KEY,
      </if>
      <if test="renewDate != null">
        RENEW_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="refuseReason != null">
        REFUSE_REASON,
      </if>
      <if test="reverseDate != null">
        REVERSE_DATE,
      </if>
      <if test="reverseBranch != null">
        REVERSE_BRANCH,
      </if>
      <if test="apprBranch != null">
        APPR_BRANCH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reversalNo != null">
        #{reversalNo},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="dealNo != null">
        #{dealNo},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="othDealNo != null">
        #{othDealNo},
      </if>
      <if test="counterparty != null">
        #{counterparty},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="takeAcctNo != null">
        #{takeAcctNo},
      </if>
      <if test="placeAcctNo != null">
        #{placeAcctNo},
      </if>
      <if test="reversalStatus != null">
        #{reversalStatus},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="principalAmt != null">
        #{principalAmt},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="reverseUserId != null">
        #{reverseUserId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="apprUserId != null">
        #{apprUserId},
      </if>
      <if test="approvalDate != null">
        #{approvalDate},
      </if>
      <if test="reversalReason != null">
        #{reversalReason},
      </if>
      <if test="linkDealInternalKey != null">
        #{linkDealInternalKey},
      </if>
      <if test="renewDate != null">
        #{renewDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="refuseReason != null">
        #{refuseReason},
      </if>
      <if test="reverseDate != null">
        #{reverseDate},
      </if>
      <if test="reverseBranch != null">
        #{reverseBranch},
      </if>
      <if test="apprBranch != null">
        #{apprBranch},
      </if>
    </trim>
  </insert>
  <select id="getMmReverseByDealNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_REVERSE_INFO
    WHERE DEAL_NO = #{dealNo}
    AND REVERSAL_STATUS in ('V')
  </select>
  <select id="getMmReverseByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_REVERSE_INFO
    WHERE REFERENCE = #{reference}
    AND COUNTERPARTY = #{counterparty}
    AND REVERSAL_STATUS in ('V')
  </select>
  <select id="getMmPrepaymentByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_REVERSE_INFO
    WHERE REVERSAL_STATUS='V'
  </select>
  <select id="getMmReverseByBranchNow" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReverseInfo">
    SELECT <include refid="Base_Column_List"/>
    FROM MM_REVERSE_INFO
    WHERE COUNTERPARTY = #{counterparty}
    AND LAST_CHANGE_DATE = (#{lastChangeDate})
    AND REVERSAL_STATUS='V'
  </select>
</mapper>
