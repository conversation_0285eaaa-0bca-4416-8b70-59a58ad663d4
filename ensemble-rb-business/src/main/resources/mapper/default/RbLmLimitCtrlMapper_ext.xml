<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitCtrl" >
  <sql id="Base_Column_List">
    CTRL_ID,CTRL_DESC,CTRL_TYPE,DEAL_FLOW,USER_ID,STATUS,REMARK,COMPANY,TRAN_TIMESTAMP,IS_CHECK,DEAL_FLOW_NIGHT
  </sql>
  <!--<select id="selectByPrimaryKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitCtrl" parameterType="java.lang.String" >-->
    <!--select <include refid="Base_Column_List"/>-->
    <!--from rb_lm_limit_ctrl-->
    <!--where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}-->
  <!--</select>-->
  <!--<insert id="insert" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitCtrl" >-->
    <!--insert into rb_lm_limit_ctrl-->
    <!--<trim prefix="(" suffix=")" suffixOverrides="," >-->
      <!--<if test="ctrlId != null" >-->
        <!--CTRL_ID,-->
      <!--</if>-->
      <!--<if test="ctrlDesc != null" >-->
        <!--CTRL_DESC,-->
      <!--</if>-->
      <!--<if test="ctrlType != null" >-->
        <!--CTRL_TYPE,-->
      <!--</if>-->
      <!--<if test="rateType != null" >-->
        <!--RATE_TYPE,-->
      <!--</if>-->
      <!--<if test="dealFlow != null" >-->
        <!--DEAL_FLOW,-->
      <!--</if>-->
      <!--<if test="userId != null" >-->
        <!--USER_ID,-->
      <!--</if>-->
      <!--<if test="status != null" >-->
        <!--STATUS,-->
      <!--</if>-->
      <!--<if test="remark != null" >-->
        <!--REMARK,-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP,-->
      <!--</if>-->
      <!--<if test="isCheck != null">-->
        <!--IS_CHECK,-->
      <!--</if>-->
      <!--<if test="dealFlowNight != null">-->
        <!--DEAL_FLOW_NIGHT-->
      <!--</if>-->
    <!--</trim>-->
    <!--<trim prefix="values (" suffix=")" suffixOverrides="," >-->
      <!--<if test="ctrlId != null" >-->
        <!--#{ctrlId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="ctrlDesc != null" >-->
        <!--#{ctrlDesc,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="ctrlType != null" >-->
        <!--#{ctrlType,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="rateType != null" >-->
        <!--#{rateType,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="dealFlow != null" >-->
        <!--#{dealFlow,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="userId != null" >-->
        <!--#{userId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="status != null" >-->
        <!--#{status,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="remark != null" >-->
        <!--#{remark,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--#{tranTimestamp,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="isCheck != null">-->
        <!--#{isCheck,jdbcType=DECIMAL},-->
      <!--</if>-->
      <!--<if test="dealFlowNight != null" >-->
        <!--#{dealFlowNight,jdbcType=VARCHAR},-->
      <!--</if>-->
    <!--</trim>-->
  <!--</insert>-->
  <!--<update id="updateByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitCtrl" >-->
    <!--update rb_lm_limit_ctrl-->
    <!--<set >-->
      <!--<if test="ctrlDesc != null" >-->
        <!--CTRL_DESC = #{ctrlDesc,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="ctrlType != null" >-->
        <!--CTRL_TYPE = #{ctrlType,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="rateType != null" >-->
        <!--RATE_TYPE = #{rateType,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="dealFlow != null" >-->
        <!--DEAL_FLOW = #{dealFlow,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="userId != null" >-->
        <!--USER_ID = #{userId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="status != null" >-->
        <!--STATUS = #{status,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="remark != null" >-->
        <!--REMARK = #{remark,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="isCheck != null">-->
        <!--IS_CHECK = #{isCheck,jdbcType=DECIMAL},-->
      <!--</if>-->
      <!--<if test="dealFlowNight != null" >-->
        <!--DEAL_FLOW_NIGHT = #{dealFlowNight,jdbcType=VARCHAR}-->
      <!--</if>-->
    <!--</set>-->
    <!--where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}-->
  <!--</update>-->
  <select id="selectLmCtrl" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitCtrl" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitCtrl" >
    select <include refid="Base_Column_List"/>
    from rb_lm_limit_ctrl
    where 1=1
    <if test="ctrlId != null">
      AND CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    </if>
    <if test="ctrlType != null">
      AND CTRL_TYPE = #{ctrlType,jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY CTRL_ID+0
  </select>

  <!--<delete id="deleteByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitCtrl">-->
    <!--delete from rb_lm_limit_ctrl-->
    <!--where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}-->
  <!--</delete>-->
  <select id="selectByIdAndStatus" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitCtrl" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitCtrl" >
    select <include refid="Base_Column_List"/>
    from rb_lm_limit_ctrl
    where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    <if test="status != null" >
    AND  STATUS = #{status,jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>

  <select id="selectByIdAndStatus1" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitCtrl" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitCtrl" >
    select <include refid="Base_Column_List"/>
    from rb_lm_limit_ctrl
    where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    AND IS_CHECK = 'Y'
    <if test="status != null" >
      AND  STATUS = #{status,jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
