<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">

  <!--  <select id="selectBy4Keys" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" >-->
  <!--    &lt;!&ndash;-->
  <!--      WARNING - @mbggenerated-->
  <!--      This element is automatically generated by Galaxy Tools Generator, do not modify.-->
  <!--      This element was generated on Mon Jun 29 15:17:40 CST 2015.-->
  <!--    &ndash;&gt;-->
  <!--    SELECT <include refid="Base_Column"/>-->
  <!--    FROM RB_ACCT_CLIENT_RELATION-->
  <!--    where 1=1-->
  <!--    <if test="baseAcctNo != null">-->
  <!--      AND	BASE_ACCT_NO = #{baseAcctNo} or-->
  <!--    </if>-->
  <!--    <if test="internalKey != null">-->
  <!--      AND INTERNAL_KEY = #{internalKey}-->
  <!--    </if>-->
  <!--    <if test="cardNo != null">-->
  <!--      AND CARD_NO = #{cardNo}-->
  <!--    </if>-->
  <!--    <if test="leadAcctFlag != null">-->
  <!--      AND LEAD_ACCT_FLAG = #{leadAcctFlag}-->
  <!--    </if>-->
  <!--  </select>-->

  <sql id="Base_Set_A">
    <set>
      <if test="internalKey != null ">INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},</if>
      <if test="clientType != null ">CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},</if>
      <if test="documentId != null ">DOCUMENT_ID = #{documentId,jdbcType=VARCHAR},</if>
      <if test="documentType != null ">DOCUMENT_TYPE = #{documentType,jdbcType=VARCHAR},</if>
      <if test="individualFlag != null ">INDIVIDUAL_FLAG = #{individualFlag,jdbcType=VARCHAR},</if>
      <if test="actualAcctNo != null ">ACTUAL_ACCT_NO = #{actualAcctNo,jdbcType=VARCHAR},</if>
      <if test="sourceType != null ">SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},</if>
      <if test="isCard != null ">IS_CARD = #{isCard,jdbcType=VARCHAR},</if>
      <if test="cardNo != null ">CARD_NO = #{cardNo,jdbcType=VARCHAR},</if>
      <if test="baseAcctNo != null ">BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},</if>
      <if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
      <if test="acctCcy != null ">ACCT_CCY = #{acctCcy,jdbcType=VARCHAR},</if>
      <if test="acctSeqNo != null ">ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},</if>
      <if test="clientNo != null ">CLIENT_NO = #{clientNo,jdbcType=VARCHAR},</if>
      <if test="acctBranch != null ">ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR},</if>
      <if test="acctName != null ">ACCT_NAME = #{acctName,jdbcType=VARCHAR},</if>
      <if test="acctClass != null ">ACCT_CLASS = #{acctClass,jdbcType=VARCHAR},</if>
      <if test="acctStatus != null ">ACCT_STATUS = #{acctStatus,jdbcType=VARCHAR},</if>
      <if test="acctNature != null ">ACCT_NATURE = #{acctNature,jdbcType=VARCHAR},</if>
      <if test="acctRealFlag != null ">ACCT_REAL_FLAG = #{acctRealFlag,jdbcType=VARCHAR},</if>
      <if test="defaultSettleAcct != null ">DEFAULT_SETTLE_ACCT = #{defaultSettleAcct,jdbcType=VARCHAR},</if>
      <if test="leadAcctFlag != null ">LEAD_ACCT_FLAG = #{leadAcctFlag,jdbcType=VARCHAR},</if>
      <if test="parentInternalKey != null ">PARENT_INTERNAL_KEY = #{parentInternalKey,jdbcType=BIGINT},</if>
      <if test="reasonCode != null ">REASON_CODE = #{reasonCode,jdbcType=VARCHAR},</if>
      <if test="appFlag != null ">APP_FLAG = #{appFlag,jdbcType=VARCHAR},</if>
      <if test="isCorpSettleCard != null ">IS_CORP_SETTLE_CARD = #{isCorpSettleCard,jdbcType=VARCHAR},</if>
      <if test="shardId != null ">SHARD_ID = #{shardId,jdbcType=VARCHAR},</if>
      <if test="reasonCodeDesc != null ">REASON_CODE_DESC = #{reasonCodeDesc,jdbcType=VARCHAR},</if>
      <if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE},</if>
      <if test="company != null ">COMPANY = #{company,jdbcType=VARCHAR},</if>
    </set>
  </sql>

  <sql id="Base_Column_a">
    <trim suffixOverrides=",">
      a.INTERNAL_KEY,
      a.CLIENT_NO,
      a.CLIENT_TYPE,
      a.DOCUMENT_ID,
      a.DOCUMENT_TYPE,
      a.INDIVIDUAL_FLAG,
      a.ACTUAL_ACCT_NO,
      a.SOURCE_TYPE,
      a.IS_CARD,
      a.CARD_NO,
      a.BASE_ACCT_NO,
      a.PROD_TYPE,
      a.ACCT_CCY,
      a.ACCT_SEQ_NO,
      a.ACCT_BRANCH,
      a.ACCT_NAME,
      a.ACCT_CLASS,
      a.ACCT_STATUS,
      a.ACCT_NATURE,
      a.ACCT_REAL_FLAG,
      a.DEFAULT_SETTLE_ACCT,
      a.LEAD_ACCT_FLAG,
      a.PARENT_INTERNAL_KEY,
      a.REASON_CODE,
      a.APP_FLAG,
      a.IS_CORP_SETTLE_CARD,
      a.SHARD_ID,
      a.REASON_CODE_DESC,
      a.TRAN_TIMESTAMP,
      a.COMPANY,
    </trim>
  </sql>

  <select id="getClientNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Mon Jun 29 15:17:40 CST 2015.
    -->
    SELECT DISTINCT CLIENT_NO FROM RB_ACCT_CLIENT_RELATION
    <where>
      <if test="baseAcctNo != null">
        AND BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test="internalKey != null">
        AND INTERNAL_KEY = #{internalKey}
      </if>
      <if test="cardNo != null">
        AND CARD_NO = #{cardNo}
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <select id="getClientNoAndBaseAcctNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >

    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    <where>
      <if test="baseAcctNo != null">
        (BASE_ACCT_NO = #{baseAcctNo} or card_no=#{baseAcctNo})
      </if>
      <if test="acctSeqNo != null">
        AND acct_seq_no=#{acctSeqNo}
      </if>
      <if test="acctCcy != null">
        AND acct_ccy=#{acctCcy}
      </if>
      <if test="prodType != null">
        AND prod_type=#{prodType}
      </if>
      AND (IS_CORP_SETTLE_CARD IS NULL OR IS_CORP_SETTLE_CARD = 'N')
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <select id="updateFmAcctClientRelation" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="updateFmAcctClientRelationAll" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="parentInternalKey != ''">
        PARENT_INTERNAL_KEY = #{parentInternalKey},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="cardNo != null">
        CARD_NO = #{cardNo},
      </if>
      <if test="actualAcctNo != null and actualAcctNo != '' ">
        ACTUAL_ACCT_NO = #{actualAcctNo},
      </if>
      <if test="leadAcctFlag != null and leadAcctFlag != '' ">
        LEAD_ACCT_FLAG = #{leadAcctFlag},
      </if>
      <if test="acctStatus != null and acctStatus != '' ">
        ACCT_STATUS = #{acctStatus},
      </if>
      <if test="isCard != null and isCard != '' ">
        IS_CARD = #{isCard},
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="updateCardNoAndActual" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="newCardNo != null">
        CARD_NO = #{newCardNo},
      </if>
      <if test="actualAcctNo != null and actualAcctNo != '' ">
        ACTUAL_ACCT_NO = #{actualAcctNo},
      </if>
      <if test="prodType != null and prodType != '' ">
        PROD_TYPE = #{prodType},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <if test="cardNo != null">
      AND CARD_NO = #{cardNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="updateRbAcctRelationName" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="acctName != null and acctName != '' ">
        ACCT_NAME = #{acctName},
      </if>
      <if test="documentType != null and documentType != '' ">
        DOCUMENT_TYPE = #{documentType},
      </if>
      <if test="documentId != null and documentId != '' ">
        DOCUMENT_ID = #{documentId},
      </if>
      <if test="clientType != null and clientType != '' ">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="acctNature != null and acctNature != '' ">
        ACCT_NATURE = #{acctNature},
      </if>
    </set>
    where CLIENT_NO = #{clientNo}
    <if test="baseAcctNo != null and baseAcctNo.length() > 0">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="acctSeqNo != null and acctSeqNo.length() > 0">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    insert into RB_ACCT_CLIENT_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clientNo != null and  clientNo != '' ">
        CLIENT_NO,
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        BASE_ACCT_NO,
      </if>
      <if test="internalKey != null ">
        INTERNAL_KEY,
      </if>
      <if test="cardNo != null and  cardNo != '' ">
        CARD_NO,
      </if>
      <if test="acctBranch != null and  acctBranch != '' ">
        ACCT_BRANCH,
      </if>
      <if test="company != null and  company != '' ">
        COMPANY,
      </if>
      <if test="shardId != null and  shardId != '' ">
        SHARD_ID,
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP,
      </if>
      <if test="prodType != null and  prodType != '' ">
        PROD_TYPE,
      </if>
      <if test="acctCcy != null and  acctCcy != '' ">
        ACCT_CCY,
      </if>
      <if test="acctSeqNo != null and  acctSeqNo != '' ">
        ACCT_SEQ_NO,
      </if>
      <if test="clientType != null and  clientType != '' ">
        CLIENT_TYPE,
      </if>
      <if test="acctNature != null and  acctNature != '' ">
        ACCT_NATURE,
      </if>
      <if test="acctClass != null and  acctClass != '' ">
        ACCT_CLASS,
      </if>
      <if test="sourceType != null and  sourceType != '' ">
        SOURCE_TYPE,
      </if>
      <if test="acctStatus != null and  acctStatus != '' ">
        ACCT_STATUS,
      </if>
      <if test="acctRealFlag != null and  acctRealFlag != '' ">
        ACCT_REAL_FLAG,
      </if>
      <if test="acctName != null and  acctName != '' ">
        ACCT_NAME,
      </if>
      <if test="reasonCode != null and  reasonCode != '' ">
        REASON_CODE,
      </if>
      <if test="isCard != null and  isCard != '' ">
        IS_CARD,
      </if>
      <if test="leadAcctFlag != null and  leadAcctFlag != '' ">
        LEAD_ACCT_FLAG,
      </if>
      <if test="parentInternalKey != null ">
        PARENT_INTERNAL_KEY,
      </if>
      <if test="defaultSettleAcct != null and  defaultSettleAcct != '' ">
        DEFAULT_SETTLE_ACCT,
      </if>
      <if test="documentType != null and  documentType != '' ">
        DOCUMENT_TYPE,
      </if>
      <if test="IndividualFlag != null and  IndividualFlag != '' ">
        INDIVIDUAL_FLAG,
      </if>
      <if test="documentId != null and  documentId != '' ">
        DOCUMENT_ID,
      </if>
      <if test="actualAcctNo != null and actualAcctNo != '' ">
        ACTUAL_ACCT_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="clientNo != null and  clientNo != '' ">
        #{clientNo},
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        #{baseAcctNo},
      </if>
      <if test="internalKey != null ">
        #{internalKey},
      </if>
      <if test="cardNo != null and  cardNo != '' ">
        #{cardNo},
      </if>
      <if test="acctBranch != null and  acctBranch != '' ">
        #{acctBranch},
      </if>
      <if test="company != null and  company != '' ">
        #{company},
      </if>
      <if test="shardId != null and  shardId != '' ">
        #{shardId},
      </if>
      <if test="tranTimestamp != null ">
        #{tranTimestamp},
      </if>
      <if test="prodType != null and  prodType != '' ">
        #{prodType},
      </if>
      <if test="acctCcy != null and  acctCcy != '' ">
        #{acctCcy},
      </if>
      <if test="acctSeqNo != null and  acctSeqNo != '' ">
        #{acctSeqNo},
      </if>
      <if test="clientType != null and  clientType != '' ">
        #{clientType},
      </if>
      <if test="acctNature != null and  acctNature != '' ">
        #{acctNature},
      </if>
      <if test="acctClass != null and  acctClass != '' ">
        #{acctClass},
      </if>
      <if test="sourceType != null and  sourceType != '' ">
        #{sourceType},
      </if>
      <if test="acctStatus != null and  acctStatus != '' ">
        #{acctStatus},
      </if>
      <if test="acctRealFlag != null and  acctRealFlag != '' ">
        #{acctRealFlag},
      </if>
      <if test="acctName != null and  acctName != '' ">
        #{acctName},
      </if>
      <if test="reasonCode != null and  reasonCode != '' ">
        #{reasonCode},
      </if>
      <if test="isCard != null and  isCard != '' ">
        #{isCard},
      </if>
      <if test="leadAcctFlag != null and  leadAcctFlag != '' ">
        #{leadAcctFlag},
      </if>
      <if test="parentInternalKey != null ">
        #{parentInternalKey},
      </if>
      <if test="defaultSettleAcct != null and  defaultSettleAcct != '' ">
        #{defaultSettleAcct},
      </if>
      <if test="documentType != null and  documentType != '' ">
        #{documentType},
      </if>
      <if test="IndividualFlag != null and  IndividualFlag != '' ">
        #{IndividualFlag},
      </if>
      <if test="documentId != null and  documentId != '' ">
        #{documentId},
      </if>
      <if test="actualAcctNo != null and actualAcctNo != '' ">
        #{actualAcctNo},
      </if>
    </trim>
  </insert>
  <delete id="deleteByCardNo" parameterType="java.util.Map" >
    delete from RB_ACCT_CLIENT_RELATION
    where CARD_NO = #{cardNo}
    AND CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <select id="settleCardGetClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    WHERE
    CARD_NO = #{cardNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getClientRelationByClientNoAndC" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    <if test="_databaseId == 'mysql'">
      SELECT
      <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      WHERE
      client_no = #{clientNo}
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      limit 1
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT
      <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      WHERE
      client_no = #{clientNo}
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      and rownum=1
    </if>
  </select>

  <select id="settleCardGetBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    WHERE
    CARD_NO = #{cardNo} and DEFAULT_SETTLE_ACCT ='Y'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectListAcctAll" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    <if test="_databaseId == 'mysql'">
      SELECT
      <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      WHERE
      <if test="cardNo != null and cardNo != ''">
        CARD_NO = #{cardNo}
      </if>
      <if test="baseAcctNo != null and baseAcctNo != ''">
        base_acct_no = #{baseAcctNo}
      </if>
      limit 1
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT
      <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      WHERE
      <if test="cardNo != null and cardNo != ''">
        CARD_NO = #{cardNo}
      </if>
      <if test="baseAcctNo != null and baseAcctNo != ''">
        base_acct_no = #{baseAcctNo}
      </if>
      and rownum=1
    </if>

  </select>

  <select id="getLeadInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    WHERE IS_CARD = #{isCard}
    <if test="baseAcctNo != null">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="cardNo != null">
      AND CARD_NO = #{cardNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="getRelationsByActualBaseAcctNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >

    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    <where>
      <if test="actualAcctNo != null">
        AND	ACTUAL_ACCT_NO = #{actualAcctNo}
      </if>
      <if test="baseAcctNo != null">
        AND BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test="prodType != null">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="acctCcy != null">
        AND ACCT_CCY = #{acctCcy}
      </if>
      <if test="acctSeqNo != null">
        AND ACCT_SEQ_NO = #{acctSeqNo}
      </if>
    </where>
  </select>

  <select id="getRelationRowCount" parameterType="java.util.Map" resultType="java.lang.Integer">
    SELECT COUNT(1) ROW_COUNT FROM RB_ACCT_CLIENT_RELATION
    <where>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
      </if>
      <if test="prodType != null">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="acctCcy != null">
        AND ACCT_CCY = #{acctCcy}
      </if>
      <if test="acctSeqNo != null">
        AND ACCT_SEQ_NO = #{acctSeqNo}
      </if>
      <if test="clientNo != null">
        AND CLIENT_NO = #{clientNo}
      </if>
    </where>
  </select>

  <select id="getAllProdTypes" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT DISTINCT PROD_TYPE, ACCT_CCY
    FROM RB_ACCT_CLIENT_RELATION
    WHERE ACCT_STATUS != 'C'
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="companyList != null">
      AND COMPANY in
      <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    ORDER BY PROD_TYPE, ACCT_CCY
  </select>

  <select id="selectDefaultSettleAcctByActualAcctNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >

    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    where (CARD_NO = #{actualAcctNo} or BASE_ACCT_NO=#{actualAcctNo})
    and DEFAULT_SETTLE_ACCT = 'Y'
    and PARENT_INTERNAL_KEY is null
    and ACCT_STATUS != 'C'
  </select>

  <sql id="Primary_Where">
    <trim suffixOverrides="AND">
      <if test="baseAcctNo != null and baseAcctNo != '' ">(BASE_ACCT_NO = #{baseAcctNo} OR CARD_NO = #{baseAcctNo}) AND </if>
      <if test="parentInternalKey != null ">PARENT_INTERNAL_KEY = #{parentInternalKey} AND </if>
      <if test="actualAcctNo != null and actualAcctNo != '' ">ACTUAL_ACCT_NO = #{actualAcctNo} AND </if>
      <if test="company != null and company != '' ">COMPANY = #{company} AND </if>
      <if test="ccy != null and ccy != '' ">ACCT_CCY = #{ccy} AND </if>
      <if test="leadAcctFlag != null and leadAcctFlag != '' ">LEAD_ACCT_FLAG = #{leadAcctFlag} AND </if>
      <if test="internalKey != null ">INTERNAL_KEY = #{internalKey} AND </if>
      <if test="acctSeqNo != null and acctSeqNo != '' ">ACCT_SEQ_NO = #{acctSeqNo} AND </if>
      <if test="clientNo != null and clientNo != '' ">CLIENT_NO = #{clientNo} AND </if>
      <if test="prodType != null and prodType != '' ">PROD_TYPE = #{prodType} AND </if>
    </trim>
  </sql>

  <sql id="Primary_Select">
    SELECT
    <include refid="Base_Column"/>
    FROM
    <include refid="Table_Name"/>
    <where>
      <include refid="Primary_Where"/>
    </where>
  </sql>

  <sql id="BranchList_Where">
    <trim suffixOverrides="AND">
      <if test="branchList != null and branchList.size() > 0">AND ACCT_BRANCH IN
        <foreach collection="branchList" item="branchList" index="index" open="(" separator="," close=")">
          #{branchList}
        </foreach>
      </if>
    </trim>
  </sql>
  <sql id="BranchList_Join_Where">
    <trim suffixOverrides="AND">
      <if test="branchList != null and branchList.size() > 0">AND a.ACCT_BRANCH IN
        <foreach collection="branchList" item="branchList" index="index" open="(" separator="," close=")">
          #{branchList}
        </foreach>
      </if>
    </trim>
  </sql>

  <select id="selectBy4Keys" useCache="false" flushCache="true" resultMap="Base_Result_Map">
    <include refid="Primary_Select"/>
  </select>
  <select id="selectBy4KeysLimit1000" useCache="false" flushCache="true" resultMap="Base_Result_Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      <include refid="Base_Column"/>
      FROM
      <include refid="Table_Name"/>
      <where>
        <include refid="Primary_Where"/>
        limit 1000
      </where>
      order by acct_seq_no
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT
      <include refid="Base_Column"/>
      FROM
      <include refid="Table_Name"/>
      <where>
        <include refid="Primary_Where"/>
        AND  rownum &lt;= 1000
      </where>
      order by acct_seq_no
    </if>
  </select>

  <select id="selectBy4KeysLimitLeadAcctFlag" useCache="false" flushCache="true" resultMap="Base_Result_Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      <include refid="Base_Column"/>
      FROM
      <include refid="Table_Name"/>
      <where>
        <include refid="Primary_Where"/>
        and LEAD_ACCT_FLAG='Y'
        limit 1000
      </where>
      order by acct_seq_no
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT
      <include refid="Base_Column"/>
      FROM
      <include refid="Table_Name"/>
      <where>
        <include refid="Primary_Where"/>
        and LEAD_ACCT_FLAG='Y'
        AND  rownum &lt;= 1000
      </where>
      order by acct_seq_no
    </if>
  </select>

  <select id="selectBy4KeysLimitdefault" useCache="false" flushCache="true" resultMap="Base_Result_Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      <include refid="Base_Column"/>
      FROM
      <include refid="Table_Name"/>
      <where>
        <include refid="Primary_Where"/>
        and default_settle_acct='Y'
        limit 1000
      </where>
      order by acct_seq_no
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT
      <include refid="Base_Column"/>
      FROM
      <include refid="Table_Name"/>
      <where>
        <include refid="Primary_Where"/>
        and default_settle_acct='Y'
        AND  rownum &lt;= 1000
      </where>
      order by acct_seq_no
    </if>
  </select>
  <select id="selectBy3Keys" useCache="false" flushCache="true" resultMap="Base_Result_Map">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    WHERE ACCT_STATUS != 'C'
    <if test="baseAcctNo != null and baseAcctNo != '' ">
      AND (BASE_ACCT_NO = #{baseAcctNo} OR CARD_NO = #{baseAcctNo})
    </if>
    <if test="ccy != null and ccy != '' ">
      AND ACCT_CCY = #{ccy}
    </if>
    <if test="prodType != null and prodType != '' ">
      AND PROD_TYPE = #{prodType}
    </if>
  </select>
  <update id="updateByFiveKeys" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    UPDATE
    <include refid="Table_Name"/>
    <include refid="Base_Set_A"/>
    <where>
      ACTUAL_ACCT_NO = #{actualAcctNo, jdbcType=VARCHAR}
      AND  PROD_TYPE = #{prodType, jdbcType=VARCHAR}
      AND  ACCT_CCY = #{acctCcy, jdbcType=VARCHAR}
      AND  ACCT_SEQ_NO = #{acctSeqNo, jdbcType=VARCHAR}
      <if test="cardNo != null and cardNo != ''">
        AND CARD_NO = #{cardNo, jdbcType=VARCHAR}
      </if>
    </where>
  </update>

  <update id="updateBySixKeys" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    UPDATE
    <include refid="Table_Name"/>
    <include refid="Base_Set_A"/>
    <where>
      ACTUAL_ACCT_NO = #{actualAcctNo, jdbcType=VARCHAR}
      AND  PROD_TYPE = #{prodType, jdbcType=VARCHAR}
      AND  BASE_ACCT_NO = #{baseAcctNo, jdbcType=VARCHAR}
      AND  ACCT_CCY = #{acctCcy, jdbcType=VARCHAR}
      AND  ACCT_SEQ_NO = #{acctSeqNo, jdbcType=VARCHAR}
      AND  CARD_NO = #{cardNo, jdbcType=VARCHAR}
    </where>
  </update>
  <update id="updateClientNoByInternalKey" parameterType="java.util.Map">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="newClientNo != null " >
        CLIENT_NO = #{newClientNo},
      </if>
      <if test = "documentId !=null and documentId != ''">
        DOCUMENT_ID = #{documentId},
      </if>
      <if test = "documentType !=null and documentType != ''">
        DOCUMENT_TYPE = #{documentType},
      </if>
    </set>
    where  CLIENT_NO = #{oldClientNo}
    and  INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="updateFmAcctClientRelationDocumentMsg" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test= "acctName != null and acctName != ''">
        ACCT_NAME = #{acctName},
      </if>
      <if test = "documentId !=null and documentId != ''">
        DOCUMENT_ID = #{documentId},
      </if>
      <if test = "documentType !=null and documentType != ''">
        DOCUMENT_TYPE = #{documentType},
      </if>
    </set>
    where CLIENT_NO = #{clientNo}
  </select>
  <select id="getFmAcctClientRelationByBaseAcctNoOrCardNo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    WHERE (CARD_NO = #{baseAcctNo} or BASE_ACCT_NO = #{baseAcctNo})
    <if test="acctSeqNo != null and acctSeqNo != ''">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
  </select>

  <select id="getFmAcctClientRelationByBaseAcctNoOrCardNo1" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    <if test="_databaseId == 'mysql'">
      SELECT
      <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      WHERE (CARD_NO = #{baseAcctNo} or BASE_ACCT_NO = #{baseAcctNo})
      <if test="acctSeqNo != null and acctSeqNo != ''">
        AND ACCT_SEQ_NO = #{acctSeqNo}
      </if>
      limit 1
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT
      <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      WHERE (CARD_NO = #{baseAcctNo} or BASE_ACCT_NO = #{baseAcctNo})
      <if test="acctSeqNo != null and acctSeqNo != ''">
        AND ACCT_SEQ_NO = #{acctSeqNo}
      </if>
      AND ROWNUM = 1
    </if>
  </select>

  <select id="getFmAcctClientRelationByBaseAcctNoOrCardNo2" parameterType="java.util.Map"
          resultType="java.lang.String">
    SELECT
    DISTINCT ACTUAL_ACCT_NO
    FROM RB_ACCT_CLIENT_RELATION
    WHERE (CARD_NO = #{baseAcctNo} or BASE_ACCT_NO = #{baseAcctNo})
  </select>

  <update id="updateByFourKeys" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">

    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="reasonCode != null and  reasonCode != '' ">
        REASON_CODE = #{reasonCode},
      </if>
      <if test="newProdType != null and  newProdType != '' ">
        PROD_TYPE = #{newProdType},
      </if>
    </set>
    <where>
      PROD_TYPE = #{prodType, jdbcType=VARCHAR}
      AND  BASE_ACCT_NO = #{baseAcctNo, jdbcType=VARCHAR}
      AND  ACCT_CCY = #{acctCcy, jdbcType=VARCHAR}
      AND  ACCT_SEQ_NO = #{acctSeqNo, jdbcType=VARCHAR}
      AND  CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
    </where>
  </update>

  <select id="selectListAcctStatusFlagByPage"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT <include refid="Base_Column"/>
    FROM  RB_ACCT_CLIENT_RELATION
    <where>
      <if test= "clientNo != null and clientNo.length() > 0 ">
        AND CLIENT_NO = #{clientNo}
      </if>
      <if test= "baseAcctNo != null and baseAcctNo.length() > 0 ">
        AND BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test= "acctStatus != null and acctStatus.length() > 0 ">
        AND ACCT_STATUS = #{acctStatus}
      </if>
      <if test= "acctClass != null and acctClass.length() > 0 ">
        AND ACCT_CLASS = #{acctClass}
      </if>
      <if test= "acctCcy != null and acctCcy.length() > 0 ">
        AND ACCT_CCY = #{acctCcy}
      </if>
      <choose>
        <when test='statusFlag != "1" and statusFlag != "2"'>
          AND ACCT_STATUS != 'C'
        </when>
        <when test='statusFlag == "2"'>
          AND ACCT_STATUS = 'C'
        </when>
        <otherwise>
        </otherwise>
      </choose>

      <if test="notInProdTypes != null">
        AND PROD_TYPE NOT IN
        <foreach collection="notInProdTypes" item="notInProdType" index="index" open="(" separator="," close=")">
          #{notInProdType}
        </foreach>
      </if>
      <include refid="BranchList_Where"/>
    </where>
    ORDER BY base_acct_no,ACCT_SEQ_NO+0 ASC
  </select>

  <select id="selectListAcctStatusFlagByPage2"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT <include refid="Base_Column"/>
    FROM  RB_ACCT_CLIENT_RELATION
    WHERE CLIENT_NO = #{clientNo}
    <if test= "baseAcctNo != null and baseAcctNo.length() > 0 ">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test= "acctStatus != null and acctStatus.length() > 0 ">
      AND ACCT_STATUS = #{acctStatus}
    </if>
    <if test= "acctClass != null and acctClass.length() > 0 ">
      AND ACCT_CLASS = #{acctClass}
    </if>
    <if test= "acctCcy != null and acctCcy.length() > 0 ">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <if test= "branchId != null and branchId.length() > 0 ">
      AND ACCT_BRANCH = #{branchId}
    </if>
    <choose>
      <when test='statusFlag != "1" and statusFlag != "2"'>
        AND ACCT_STATUS != 'C'
      </when>
      <when test='statusFlag == "2"'>
        AND ACCT_STATUS = 'C'
      </when>
      <otherwise>
      </otherwise>
    </choose>

    <if test="notInProdTypes != null">
      AND PROD_TYPE NOT IN
      <foreach collection="notInProdTypes" item="notInProdType" index="index" open="(" separator="," close=")">
        #{notInProdType}
      </foreach>
    </if>
    <include refid="BranchList_Where"/>
    ORDER BY base_acct_no,ACCT_SEQ_NO+0 ASC
  </select>

  <select id="selectListAcctStatusFlagAcctTypeByPage"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT <include refid="Base_Column_a"/>
    FROM RB_ACCT_CLIENT_RELATION a
    left join RB_ACCT b on a.internal_key = b.internal_key
    <where>
      <if test= "clientNo != null and clientNo.length() > 0 ">
        AND a.CLIENT_NO = #{clientNo}
      </if>
      <if test= "baseAcctNo != null and baseAcctNo.length() > 0 ">
        AND a.BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test= "acctStatus != null and acctStatus.length() > 0 ">
        AND a.ACCT_STATUS = #{acctStatus}
      </if>
      <if test= "acctClass != null and acctClass.length() > 0 ">
        AND a.ACCT_CLASS = #{acctClass}
      </if>
      <if test= "acctCcy != null and acctCcy.length() > 0 ">
        AND a.ACCT_CCY = #{acctCcy}
      </if>
      <choose>
        <when test='statusFlag != "1" and statusFlag != "2"'>
          AND a.ACCT_STATUS != 'C'
        </when>
        <when test='statusFlag == "2"'>
          AND a.ACCT_STATUS = 'C'
        </when>
        <otherwise>
        </otherwise>
      </choose>

      <if test="notInProdTypes != null">
        AND a.PROD_TYPE NOT IN
        <foreach collection="notInProdTypes" item="notInProdType" index="index" open="(" separator="," close=")">
          #{notInProdType}
        </foreach>
      </if>
      <if test="acctType != null and acctType.length() > 0">
        AND b.acct_type = #{acctType}
      </if>
      <include refid="BranchList_Join_Where"></include>
    </where>
    ORDER BY a.base_acct_no,a.ACCT_SEQ_NO+0 ASC
  </select>


  <select id="selectListAcctStatusFlagAcctTypeByPage2"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT <include refid="Base_Column_a"/>
    FROM  RB_ACCT_CLIENT_RELATION a
    left join RB_ACCT b on a.internal_key = b.internal_key
    WHERE a.CLIENT_NO = #{clientNo}
    <if test= "baseAcctNo != null and baseAcctNo.length() > 0 ">
      AND a.BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test= "acctStatus != null and acctStatus.length() > 0 ">
      AND a.ACCT_STATUS = #{acctStatus}
    </if>
    <if test= "acctClass != null and acctClass.length() > 0 ">
      AND a.ACCT_CLASS = #{acctClass}
    </if>
    <if test= "acctCcy != null and acctCcy.length() > 0 ">
      AND a.ACCT_CCY = #{acctCcy}
    </if>
    <if test= "branchId != null and branchId.length() > 0 ">
      AND a.ACCT_BRANCH = #{branchId}
    </if>
    <choose>
      <when test='statusFlag != "1" and statusFlag != "2"'>
        AND a.ACCT_STATUS != 'C'
      </when>
      <when test='statusFlag == "2"'>
        AND a.ACCT_STATUS = 'C'
      </when>
      <otherwise>
      </otherwise>
    </choose>

    <if test="notInProdTypes != null">
      AND a.PROD_TYPE NOT IN
      <foreach collection="notInProdTypes" item="notInProdType" index="index" open="(" separator="," close=")">
        #{notInProdType}
      </foreach>
    </if>
    <if test="acctType != null and acctType.length() > 0">
      AND b.acct_type = #{acctType}
    </if>
    <include refid="BranchList_Join_Where"></include>
    ORDER BY a.base_acct_no,a.ACCT_SEQ_NO+0 ASC
  </select>

  <select id="getListByAcctStatusByPage"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT <include refid="Base_Column"/>
    FROM  RB_ACCT_CLIENT_RELATION r
    WHERE (IS_CORP_SETTLE_CARD = 'N' or IS_CORP_SETTLE_CARD is null)
    <if test= "baseAcctNo != null and baseAcctNo.length() > 0 ">
      AND (BASE_ACCT_NO = #{baseAcctNo} or card_no = #{baseAcctNo})
    </if>
    <if test= "acctCcy != null and acctCcy.length() > 0 ">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <!--    <if test="leadAcctFlag != null and leadAcctFlag.length() > 0 ">-->
    <!--      AND LEAD_ACCT_FLAG = #{leadAcctFlag}-->
    <!--    </if>-->
    <if test= "prodType != null and prodType.length() > 0 ">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test= "acctSeqNo != null and acctSeqNo.length() > 0 ">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <choose>
      <when test='statusFlag != "1" and statusFlag != "2"'>
        AND ACCT_STATUS != 'C'
      </when>
      <when test='statusFlag == "2"'>
        AND ACCT_STATUS = 'C'
      </when>
      <otherwise>
      </otherwise>
    </choose>
    <if test="notInProdTypes != null">
      AND PROD_TYPE NOT IN
      <foreach collection="notInProdTypes" item="notInProdType" index="index" open="(" separator="," close=")">
        #{notInProdType}
      </foreach>
    </if>
    <if test="term != null and term != '' and termType != null and termType != ''">
      and exists (
      select 1 from RB_ACCT a
      WHERE BASE_ACCT_NO = #{baseAcctNo} AND TERM = #{term}  AND TERM_TYPE = #{termType} and r.internal_key = a.internal_key
      )
    </if>
    ORDER BY base_acct_no,ACCT_SEQ_NO+0 ASC
  </select>

  <update id="updateClassByInternalKey" parameterType="java.util.Map">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="acctClass != null " >
        ACCT_CLASS = #{acctClass}
      </if>
    </set>
    where  CLIENT_NO = #{ClientNo}
    and  INTERNAL_KEY = #{internalKey}
    and ACCT_SEQ_NO = #{acctSeqNo}
  </update>

  <select id="getListByInternalKeyByPage" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT <include refid="Base_Column"/>
    FROM  RB_ACCT_CLIENT_RELATION
    <where>
      <if test="internalKeys.size > 0">
        internal_key in
        <foreach collection="internalKeys" item="internalKey" index="index" separator="," open="(" close=")">
          #{internalKey}
        </foreach>
      </if>
    </where>
    order by base_acct_no, acct_seq_no+0
  </select>

  <update id="updateClassByInternalKeyAndActualCardNo" parameterType="java.util.Map">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="acctStatus != null " >
        ACCT_STATUS = #{acctStatus}
      </if>
    </set>
    where  CLIENT_NO = #{clientNo}
    and  INTERNAL_KEY = #{internalKey}
    and ACTUAL_ACCT_NO = #{actualAcctNo}
  </update>

  <select id="selectAcctInfoByPage"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT <include refid="Base_Column"/>
    FROM  RB_ACCT_CLIENT_RELATION
    <where>
      <choose>
        <when test='outFlag != "11"'>
          ACCT_STATUS NOT IN('C', 'R')
        </when>
      </choose>
      <if test= "cardNo != null and cardNo.length() > 0 ">
        AND (BASE_ACCT_NO = #{cardNo} or CARD_NO = #{cardNo})
      </if>
      <if test= "acctStatus != null and acctStatus.length() > 0 ">
        AND ACCT_STATUS = #{acctStatus}
      </if>

      <if test="branchList != null and branchList.size() > 0">
        AND ACCT_BRANCH IN
        <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
          #{branch}
        </foreach>
      </if>
    </where>
    ORDER BY base_acct_no,ACCT_SEQ_NO+0 ASC
  </select>

  <select id="getClientRelationByBaseAcctNoOrCardNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    where (CARD_NO = #{cardNo} or BASE_ACCT_NO=#{baseAcctNo})
    <!--    and ACCT_CLASS = '1'-->
    and ACCT_STATUS != 'C'
  </select>

  <select id="getClientRelationByCardNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >
    <if test="_databaseId == 'mysql'">
      SELECT <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      where (CARD_NO = #{cardNo} or BASE_ACCT_NO=#{baseAcctNo})
      limit 1
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT <include refid="Base_Column"/>
      FROM RB_ACCT_CLIENT_RELATION
      where (CARD_NO = #{cardNo} or BASE_ACCT_NO=#{baseAcctNo})
      and rownum=1
    </if>
  </select>

  <select id="getUseClientRelationByClientNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    where CLIENT_NO = #{clientNo}
    and ACCT_STATUS != 'C'
  </select>

  <select id="getMaxSeqNoByBaseAcctNo" parameterType="java.util.Map" resultType="java.util.Map">
    SELECT max(ACCT_SEQ_NO+0) MAX_SEQ_NO
    FROM RB_ACCT_CLIENT_RELATION
    WHERE base_acct_no = #{baseAcctNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getClientRelationByBaseAcctNoAndClientNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    where ACCT_STATUS != 'C'
    <if test="baseAcctNo != null and baseAcctNo.length() > 0">
      AND (BASE_ACCT_NO = #{baseAcctNo} or card_no=#{baseAcctNo})
    </if>
    <if test="clientNo != null and clientNo.length() > 0">
      AND CLIENT_NO = #{clientNo}
    </if>
    ORDER BY ACCT_SEQ_NO+0 ASC
  </select>

  <select id="getClientRelationByClientNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation" parameterType="java.util.Map" >
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    <where>
      <if test="clientNo != null and clientNo.length() > 0">
        CLIENT_NO = #{clientNo}
      </if>
    </where>
    ORDER BY ACCT_SEQ_NO+0 ASC
  </select>
  <update id="updateClientNo" parameterType="java.util.Map">
    update RB_ACCT_CLIENT_RELATION set CLIENT_NO = #{clientNo},TRAN_TIMESTAMP = #{tranTimestamp}
    where internal_key = #{internalKey} and CLIENT_NO = #{oldClientNo}
  </update>
  <select id="queryListByInternalKey" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    where  INTERNAL_KEY in
    <foreach collection="internalKeys" item="internalKey" index="index" open="(" separator="," close=")">
      #{internalKey}
    </foreach>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="updateActualAcctNoByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    update RB_ACCT_CLIENT_RELATION
    set ACTUAL_ACCT_NO = #{actualAcctNo},
    IS_CARD = #{isCard}
    where internal_key = #{internalKey}
  </select>
  <select id="getDefaultSettleFlag" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation">
    SELECT
    <include refid="Base_Column"/>
    FROM RB_ACCT_CLIENT_RELATION
    where DEFAULT_SETTLE_ACCT='Y'
    <if test="baseAcctNo != null and baseAcctNo.length() > 0">
      and base_acct_no = #{baseAcctNo} or card_no=#{baseAcctNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMaxSeqNo" parameterType="java.util.Map" resultType="java.util.Map">
    SELECT max(ACCT_SEQ_NO+0) MAX_SEQ_NO
    FROM RB_ACCT_CLIENT_RELATION
    WHERE base_acct_no = #{baseAcctNo}
    <if test="clientNo != null and clientNo != '' ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <update id="updateRbAcctClientRelationSingle" parameterType="java.util.Map">
    update RB_ACCT_CLIENT_RELATION
    <set>
      <if test="acctStatus != null " >
        ACCT_STATUS = #{acctStatus}
      </if>
    </set>
    where  CLIENT_NO = #{clientNo}
    and  INTERNAL_KEY = #{internalKey}
  </update>
</mapper>
