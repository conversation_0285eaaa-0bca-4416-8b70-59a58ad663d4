<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardRealHist">
    <select id="getMbSettleCardRealHistsByMap" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardRealHist">
        select <include refid="Base_Column"/>
        from RB_SETTLE_CARD_REAL_HIST
        <where>
            <if test="baseAcctNo != null and baseAcctNo.length() > 0">
                BASE_ACCT_NO= #{baseAcctNo}
            </if>
            <if test="cardNo != null and cardNo.length() > 0">
                AND CARD_NO = #{cardNo}
            </if>
            <if test="clientNo != null and clientNo.length() > 0">
                AND CLIENT_NO = #{clientNo}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <if test="branchList != null and branchList.size() > 0">
                and tran_branch in
                <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
                    #{branch}
                </foreach>
            </if>
        </where>

    </select>

</mapper>
