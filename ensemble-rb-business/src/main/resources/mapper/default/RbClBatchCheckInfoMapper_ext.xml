<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClBatchCheckInfo">
	<select id="getNoPassByClassAndDate" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClBatchCheckInfo">
		SELECT *
		FROM RB_CL_BATCH_CHECK_INFO
		WHERE TRAN_DATE = #{tranDate}
		AND BATCH_CLASS = #{batchClass}
		AND (CHECK_RESULT != 'S' or CHECK_RESULT is null)
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
