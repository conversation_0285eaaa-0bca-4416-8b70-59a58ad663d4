<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctPreferenceHist">
    <!-- Created by admin on 2017/09/01 13:29:33. -->

    <select id="getAcctPreferenceHistByInternalKey"
            parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctPreferenceHist"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctPreferenceHist">
        select *
        from RB_ACCT_PREFERENCE_HIST
        <where>
            <if test="internalKey!=null">
                AND INTERNAL_KEY = #{internalKey}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
</mapper>
