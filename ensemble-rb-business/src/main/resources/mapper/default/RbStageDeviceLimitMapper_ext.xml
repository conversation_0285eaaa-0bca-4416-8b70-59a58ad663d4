<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageDeviceLimit">

	<select id="selectStageDeviceLimitListByStageCode"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageDeviceLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_DEVICE_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="selectLimitByStageCodeAndDevice"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageDeviceLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_DEVICE_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
				<if test="sourceType != null and  sourceType != '' ">
					SOURCE_TYPE = #{sourceType}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="deleteDeviceLimitListByStageCode"  parameterType="java.util.Map" >
		DELETE FROM
		RB_STAGE_DEVICE_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>


	<update id="updateDeviceLimitListByStageCodeAndDevice"  parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageDeviceLimit" >
		UPDATE
		RB_STAGE_DEVICE_LIMIT
		<set>
			<if test="stageCode != null ">STAGE_CODE = #{stageCode,jdbcType=VARCHAR},</if>
			<if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
			<if test="ccy != null ">CCY = #{ccy,jdbcType=VARCHAR},</if>
			<if test="leaveLimit != null ">LEAVE_LIMIT = #{leaveLimit,jdbcType=DECIMAL},</if>
			<if test="sourceType != null ">SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},</if>
			<if test="backStatus != null ">BACK_STATUS = #{backStatus,jdbcType=VARCHAR},</if>
			<if test="distributeLimit != null ">DISTRIBUTE_LIMIT = #{distributeLimit,jdbcType=DECIMAL},</if>
			<if test="holdingLimit != null ">HOLDING_LIMIT = #{holdingLimit,jdbcType=DECIMAL},</if>
			<if test="issueYear != null ">ISSUE_YEAR = #{issueYear,jdbcType=VARCHAR},</if>
			<if test="totalLimit != null ">TOTAL_LIMIT = #{totalLimit,jdbcType=DECIMAL},</if>
			<if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE},</if>
			<if test="company != null ">COMPANY = #{company,jdbcType=VARCHAR},</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
				<if test="sourceType != null and  sourceType != '' ">
					SOURCE_TYPE = #{sourceType}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

</mapper>
