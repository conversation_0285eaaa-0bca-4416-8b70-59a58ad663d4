<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdAcct">

<!--	<select id="selectNextChargeDateMature" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">-->
<!--		     select <include refid="Base_Column"/>-->
<!--        <![CDATA[-->
<!--		      from RB_CLIENT_CHARGE_AGRT-->
<!--		      where NEXT_CHARGE_DATE <= #{baseParam.runDate} and STATUS='A'-->
<!--		      order by AGREEMENT_ID-->
<!--    	]]>-->
<!--	</select>-->
    <delete id="deleteBySubAcctNoAndBaseAcctNo">
        DELETE FROM RB_CM_CD_ACCT
        <where>
            SUB_ACCT_NO = #{subAcctNo}  AND
            BASE_ACCT_NO = #{baseAcctNo}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </delete>

    <select id="selectRbCmCdAcctBySubAcctNoOne" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdAcct">
        select
        <include refid="Base_Column"/>
        from RB_CM_CD_ACCT
        where SUB_ACCT_NO = #{subAcctNo}
        and XG_SIGN_STATUS = 'A'
        and rownum = 1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectRbCmCdAcctByStatusFlag1" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdAcct">
        select
        <include refid="Base_Column"/>
        from RB_CM_CD_ACCT
        where SUB_ACCT_NO = #{subAcctNo}
        and XG_SIGN_STATUS != 'C'
        and rownum = 1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectRbCmCdAcctByStatusFlag2" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdAcct">
        select
        <include refid="Base_Column"/>
        from RB_CM_CD_ACCT
        where SUB_ACCT_NO = #{subAcctNo}
        and rownum = 1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectRbCmCdAcctByStatusFlag3" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdAcct">
        select
        <include refid="Base_Column"/>
        from RB_CM_CD_ACCT
        where SUB_ACCT_NO = #{subAcctNo}
        and XG_SIGN_STATUS = 'C'
        and rownum = 1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectRbCmCdAcctByBaseAcctNoOne" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdAcct">
        select
        <include refid="Base_Column"/>
        from RB_CM_CD_ACCT
        where BASE_ACCT_NO = #{baseAcctNo}
        and XG_SIGN_STATUS = 'A'
        and rownum = 1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>




</mapper>
