<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementRec">

    <select id="getActiveAgreementByClientNoAndId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementRec">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_REC
        where AGREEMENT_STATUS = 'A'
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="documentId != null and documentId != ''">
            AND DOCUMENT_ID = #{documentId}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getActiveAgreementByPhoneNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementRec">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_REC
        where AGREEMENT_STATUS = 'A'
        <if test="phoneNo != null and phoneNo != ''">
            AND PHONE_NO = #{phoneNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
