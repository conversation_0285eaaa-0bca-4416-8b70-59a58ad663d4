<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRefMapping">
    <update id="updateByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRefMapping">
        update RB_REF_MAPPING
        <set>
            <if test="eventType != null and  eventType != '' ">
                EVENT_TYPE = #{eventType}  AND
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey} and
        CLIENT_NO = #{clientNo}  AND
        REFERENCE = #{reference}  AND
        RESTRAINT_TYPE = #{restraintType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

</mapper>
