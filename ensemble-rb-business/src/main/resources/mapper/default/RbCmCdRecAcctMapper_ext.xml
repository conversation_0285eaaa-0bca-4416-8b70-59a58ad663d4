<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdRecAcct">

	<delete id="deleteBySubAcctNoAndBaseAcctNo">
		DELETE FROM RB_CM_CD_REC_ACCT
		<where>
			BASE_ACCT_NO = #{baseAcctNo}
		</where>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</delete>


	<select id="selectListBySubAcctNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdRecAcct">
		select
		<include refid="Base_Column"/>
		from RB_CM_CD_REC_ACCT
		where SUB_ACCT_NO = #{subAcctNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

</mapper>
