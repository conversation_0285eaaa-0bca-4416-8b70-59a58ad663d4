<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcYearPlanInfo">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcYearPlanInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcYearPlanInfo">
    select <include refid="Base_Column"/>
    from RB_DC_YEAR_PLAN_INFO
    where ISSUE_YEAR = #{issueYear}
      <if test="ccy != null">
        AND CCY = #{ccy}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcYearPlanInfo">
    delete from RB_DC_YEAR_PLAN_INFO
    where ISSUE_YEAR = #{issueYear}
      <if test="ccy != null">
        AND CCY = #{ccy}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcYearPlanInfo">
    update RB_DC_YEAR_PLAN_INFO
    <set>
      <if test="adjustLimit != null">
        ADJUST_LIMIT = #{adjustLimit},
      </if>
      <if test="oldRecordLimit != null">
        OLD_RECORD_LIMIT = #{oldRecordLimit},
      </if>
      <if test="recordLimit != null">
        RECORD_LIMIT = #{recordLimit},
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT = #{distributeLimit},
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT = #{leaveLimit},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where ISSUE_YEAR = #{issueYear}
        AND CCY = #{ccy}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcYearPlanInfo">
    insert into RB_DC_YEAR_PLAN_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        ISSUE_YEAR,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="adjustLimit != null">
        ADJUST_LIMIT,
      </if>
      <if test="oldRecordLimit != null">
        OLD_RECORD_LIMIT,
      </if>
      <if test="recordLimit != null">
        RECORD_LIMIT,
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT,
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        #{issueYear},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="adjustLimit != null">
        #{adjustLimit},
      </if>
      <if test="oldRecordLimit != null">
        #{oldRecordLimit},
      </if>
      <if test="recordLimit != null">
        #{recordLimit},
      </if>
      <if test="distributeLimit != null">
        #{distributeLimit},
      </if>
      <if test="leaveLimit != null">
        #{leaveLimit},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
</mapper>
