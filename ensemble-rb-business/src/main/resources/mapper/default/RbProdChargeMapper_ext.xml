<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCharge">

    <select id="selectNextChargeDateMature" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCharge">
        select
        <include refid="Base_Column"/>

	    from RB_PROD_CHARGE
	    where <![CDATA[NEXT_CHARGE_DATE <= #{runDate}]]>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectProdChargeByProdType" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCharge">
        select
        <include refid="Base_Column"/>
	    from RB_PROD_CHARGE
	    where PROD_TYPE = #{prodType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getProdChargeByProdAndFee" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCharge">
        select
        <include refid="Base_Column"/>
        from RB_PROD_CHARGE
        where PROD_TYPE = #{prodType}
        <if test="feeTypeList != null and feeTypeList.size() > 0">
            AND FEE_TYPE IN
            <foreach collection="feeTypeList" item="feeType" open="(" separator="," close=")">
                #{feeType}
            </foreach>
        </if>
        <!-- Multi-legal transformation by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

</mapper>
