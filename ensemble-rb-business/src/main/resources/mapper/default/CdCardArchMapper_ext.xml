<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where CARD_NO = #{cardNo}
    AND CARD_STATUS!='4'
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

<!--多法人改造 by LIYUANV-->
  <select id="getCardByBatchJobNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where BATCH_JOB_NO = #{batchJobNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY CARD_NO ASC
  </select>
<!--多法人改造 by LIYUANV-->
  <select id="getMaxCardNoByBatchJobNo" parameterType="java.util.Map"  resultType="java.util.Map">
    select max(CARD_NO) as MAX_CARD_NO,min(CARD_NO) as MIN_CARD_NO
    from CD_CARD_ARCH
    where BATCH_JOB_NO = #{batchJobNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    delete from CD_CARD_ARCH
    where CARD_NO = #{cardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <!--多法人改造 by LIYUANV-->
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    update CD_CARD_ARCH
    <set>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="cardStatus != null">
        CARD_STATUS = #{cardStatus},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="appFlag != null">
        APP_FLAG = #{appFlag},
      </if>
      <if test="mainCardNo != null">
        MAIN_CARD_NO = #{mainCardNo},
      </if>
      <if test="validFromDate != null">
        VALID_FROM_DATE = #{validFromDate},
      </if>
      <if test="validThruDate != null">
        VALID_THRU_DATE = #{validThruDate},
      </if>
      <if test="cardCvn != null">
        CARD_CVN = #{cardCvn},
      </if>
      <if test="treadPwd != null">
        TREAD_PWD = #{treadPwd},
      </if>
      <if test="queryPwd != null">
        QUERY_PWD = #{queryPwd},
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID = #{applyUserId},
      </if>
      <if test="issueUserId != null">
        ISSUE_USER_ID = #{issueUserId},
      </if>
      <if test="issueDate != null">
        ISSUE_DATE = #{issueDate},
      </if>
      <if test="closeUserId != null">
        CLOSE_USER_ID = #{closeUserId},
      </if>
      <if test="cardCloseDate != null">
        CARD_CLOSE_DATE = #{cardCloseDate},
      </if>
      <if test="cardCloseReason != null">
        CARD_CLOSE_REASON = #{cardCloseReason},
      </if>
      <if test="changeCardNum != null">
        CHANGE_CARD_NUM = #{changeCardNum},
      </if>
      <if test="cardMediumType != null">
        CARD_MEDIUM_TYPE = #{cardMediumType},
      </if>
      <if test="applyNo != null">
        APPLY_NO = #{applyNo},
      </if>
      <if test="batchJobNo != null">
        BATCH_JOB_NO = #{batchJobNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="dacValue != null">
        DAC_VALUE = #{dacValue},
      </if>

      <if test="cardPbUnionFlag != null">
        CARD_PB_UNION_FLAG = #{cardPbUnionFlag}
      </if>
    </set>
    where CARD_NO = #{cardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <!--多法人改造 by LIYUANV-->
  <update id="updateByBatchJobNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    update CD_CARD_ARCH
    <set>
      <if test="cardStatus != null">
        CARD_STATUS = #{cardStatus},
      </if>
    </set>
    where BATCH_JOB_NO = #{batchJobNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <!--多法人改造 by LIYUANV-->
  <update id="updateByApplyNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    update CD_CARD_ARCH
    <set>
      <if test="batchJobNo != null">
        BATCH_JOB_NO = #{batchJobNo},
      </if>
    </set>
    where APPLY_NO = #{applyNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    insert into CD_CARD_ARCH
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cardNo != null">
        CARD_NO,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="cardStatus != null">
        CARD_STATUS,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="appFlag != null">
        APP_FLAG,
      </if>
      <if test="mainCardNo != null">
        MAIN_CARD_NO,
      </if>
      <if test="validFromDate != null">
        VALID_FROM_DATE,
      </if>
      <if test="validThruDate != null">
        VALID_THRU_DATE,
      </if>
      <if test="cardCvn != null">
        CARD_CVN,
      </if>
      <if test="treadPwd != null">
        TREAD_PWD,
      </if>
      <if test="queryPwd != null">
        QUERY_PWD,
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID,
      </if>
      <if test="issueUserId != null">
        ISSUE_USER_ID,
      </if>
      <if test="issueDate != null">
        ISSUE_DATE,
      </if>
      <if test="closeUserId != null">
        CLOSE_USER_ID,
      </if>
      <if test="cardCloseDate != null">
        CARD_CLOSE_DATE,
      </if>
      <if test="cardCloseReason != null">
        CARD_CLOSE_REASON,
      </if>
      <if test="changeCardNum != null">
        CHANGE_CARD_NUM,
      </if>
      <if test="cardMediumType != null">
        CARD_MEDIUM_TYPE,
      </if>
      <if test="applyNo != null">
        APPLY_NO,
      </if>
      <if test="batchJobNo != null">
        BATCH_JOB_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="dacValue != null">
        DAC_VALUE,
      </if>

      <if test="cardPbUnionFlag != null">
        CARD_PB_UNION_FLAG,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cardNo != null">
        #{cardNo},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="cardStatus != null">
        #{cardStatus},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="appFlag != null">
        #{appFlag},
      </if>
      <if test="mainCardNo != null">
        #{mainCardNo},
      </if>
      <if test="validFromDate != null">
        #{validFromDate},
      </if>
      <if test="validThruDate != null">
        #{validThruDate},
      </if>
      <if test="cardCvn != null">
        #{cardCvn},
      </if>
      <if test="treadPwd != null">
        #{treadPwd},
      </if>
      <if test="queryPwd != null">
        #{queryPwd},
      </if>
      <if test="applyUserId != null">
        #{applyUserId},
      </if>
      <if test="issueUserId != null">
        #{issueUserId},
      </if>
      <if test="issueDate != null">
        #{issueDate},
      </if>
      <if test="closeUserId != null">
        #{closeUserId},
      </if>
      <if test="cardCloseDate != null">
        #{cardCloseDate},
      </if>
      <if test="cardCloseReason != null">
        #{cardCloseReason},
      </if>
      <if test="changeCardNum != null">
        #{changeCardNum},
      </if>
      <if test="isSignFlag != null">
        #{isSignFlag},
      </if>
      <if test="cardMediumType != null">
        #{cardMediumType},
      </if>
      <if test="applyNo != null">
        #{applyNo},
      </if>
      <if test="batchJobNo != null">
        #{batchJobNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="dacValue != null">
        #{dacValue},
      </if>

      <if test="cardPbUnionFlag != null">
        #{cardPbUnionFlag},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
  <!--多法人改造 by LIYUANV-->
  <select id="getCardByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where CARD_NO = #{cardNo}
    <if test="clientNo != null and  clientNo != '' ">
      AND  CLIENT_NO = #{clientNo}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCardArchByCardNoAndClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where CARD_NO = #{cardNo}
    <if test="clientNo != null and  clientNo != '' ">
      AND  CLIENT_NO = #{clientNo}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getSumCardByClientNo" parameterType="java.util.Map" resultType="java.lang.Integer">
    SELECT COUNT(CLIENT_NO)
    from CD_CARD_ARCH
    where CLIENT_NO = #{clientNo} and CARD_STATUS not in ('1','2','4')
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getSumCardInfosByClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    SELECT <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where CLIENT_NO = #{clientNo}
    and CARD_STATUS not in ('1','2','4')
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getAppCardNum" parameterType="java.util.Map" resultType="java.lang.Integer">
    SELECT COUNT(*)
    from CD_CARD_ARCH
    where MAIN_CARD_NO = #{mainCardNo} and CARD_STATUS not in ('1','2','4')
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCardByMainCardNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where MAIN_CARD_NO = #{mainCardNo}
    AND CARD_STATUS = '3'
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY CARD_NO ASC
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCardByVoucherNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where CARD_NO like '${voucherNo}%'
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getAllCardInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardArch">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH
    where CARD_NO = #{cardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCardByApplyNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.bc.unit.card.base.busimodel.CdCardModel">
    select <include refid="Base_Column"/>
    from CD_CARD_ARCH,CD_MAKE_CARD_REG
    where CD_CARD_ARCH.APPLY_NO = CD_MAKE_CARD_REG.APPLY_NO
    AND CD_CARD_ARCH.APPLY_NO = #{applyNo}
    AND CD_MAKE_CARD_REG.APPLY_DATE = #{applyDate}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCompanyByCard"  parameterType="java.util.Map"  resultType="java.lang.String">
    select COMPANY
    from  CD_CARD_ARCH
    where  CARD_NO = #{cardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getRegularCardByApplyNo" parameterType="java.util.Map" resultType="java.util.Map">
    select
     min(substr(ca.CARD_NO,0,18)) minCardNoKey ,
     max(substr(ca.CARD_NO,0,18)) maxCardNoKey,
     max(substr(ca.CARD_NO,0,18))-min(substr(ca.CARD_NO,0,18))+1 totalNum,
     cr.DOC_TYPE docType
    from CD_CARD_ARCH ca,CD_MAKE_CARD_REG  cr
    where ca.APPLY_NO = cr.APPLY_NO
    AND ca.APPLY_NO = #{applyNo}
    AND cr.APPLY_DATE = #{applyDate}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    GROUP BY cr.DOC_TYPE
  </select>
</mapper>
