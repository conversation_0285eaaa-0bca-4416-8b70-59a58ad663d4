<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_RULE_DEFINE
    where 1=1
    <if test="prodType != null and prodType!=''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="stageCode != null and stageCode!=''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="queryAll" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_RULE_DEFINE
    where
    <if test="stageCode != null">
      STAGE_CODE IN
      <foreach item="item" index="index" collection="stageCode" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getStageForStRealRateStep" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine">
    select a.*
    from rb_dc_stage_rule_define a,rb_dc_stage_define b
    where a.STAGE_CODE = b.STAGE_CODE
    and b.STAGE_STATUS IN ('D','G')
    and (a.MATURITY_DATE  <![CDATA[ >= ]]>  #{runDate})
    and a.TOUCH_FLAG = 'N'
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
  </select>

  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine">
    delete from RB_DC_STAGE_RULE_DEFINE
    where PROD_TYPE = #{prodType}
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine">
    update RB_DC_STAGE_RULE_DEFINE
    <set>
      <if test="structClass != null">
        STRUCT_CLASS = #{structClass},
      </if>
      <if test="stageInitPrice != null">
        STAGE_INIT_PRICE = #{stageInitPrice},
      </if>
      <if test="openRate != null">
        OPEN_RATE = #{openRate},
      </if>
      <if test="accrRate != null">
        ACCR_RATE = #{accrRate},
      </if>
      <if test="preRate != null">
        PRE_RATE = #{preRate},
      </if>
      <if test="highGradeRate != null">
        HIGH_GRADE_RATE = #{highGradeRate},
      </if>
      <if test="lowEndRate != null">
        LOW_END_RATE = #{lowEndRate},
      </if>
      <if test="yearsRate != null">
        YEARS_RATE = #{yearsRate},
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE = #{actualRate},
      </if>
      <if test="floatRate != null">
        FLOAT_RATE = #{floatRate},
      </if>
      <if test="stageFixedRate != null">
        STAGE_FIXED_RATE = #{stageFixedRate},
      </if>
      <if test="stageSpreadRate != null">
        STAGE_SPREAD_RATE = #{stageSpreadRate},
      </if>
      <if test="stagePercentRate != null">
        STAGE_PERCENT_RATE = #{stagePercentRate},
      </if>
      <if test="realRate != null">
        REAL_RATE = #{realRate},
      </if>
      <if test="acrRateType != null">
        ACR_RATE_TYPE = #{acrRateType},
      </if>
      <if test="underlyingId != null">
        UNDERLYING_ID = #{underlyingId},
      </if>
      <if test="touchType != null">
        TOUCH_TYPE = #{touchType},
      </if>
      <if test="touchPercent != null">
        TOUCH_PERCENT = #{touchPercent},
      </if>
      <if test="lowThreshold != null">
        LOW_THRESHOLD = #{lowThreshold},
      </if>
      <if test="highThreshold != null">
        HIGH_THRESHOLD = #{highThreshold},
      </if>
      <if test="inSectionDays != null">
        IN_SECTION_DAYS = #{inSectionDays},
      </if>
      <if test="outSectionDays != null">
        OUT_SECTION_DAYS = #{outSectionDays},
      </if>
      <if test="touchFlag != null">
        TOUCH_FLAG = #{touchFlag},
      </if>
      <if test="retryFlag != null">
        RETRY_FLAG = #{retryFlag},
      </if>
      <if test="observeFlag != null">
        OBSERVE_FLAG = #{observeFlag},
      </if>
      <if test="touchStopFlag != null">
        TOUCH_STOP_FLAG = #{touchStopFlag},
      </if>
      <if test="stageLowLimit != null">
        STAGE_LOW_LIMIT = #{stageLowLimit},
      </if>
      <if test="initAmt != null">
        INIT_AMT = #{initAmt},
      </if>
      <if test="sgMaxAmt != null">
        SG_MAX_AMT = #{sgMaxAmt},
      </if>
      <if test="amtUnit != null">
        AMT_UNIT = #{amtUnit},
      </if>
      <if test="stageRiskLevel != null">
        STAGE_RISK_LEVEL = #{stageRiskLevel},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE = #{maturityDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="ruleDesc != null">
        RULE_DESC = #{ruleDesc},
      </if>
      <if test="observeStartDate != null">
        OBSERVE_START_DATE = #{observeStartDate},
      </if>
      <if test="observeEndDate != null">
        OBSERVE_END_DATE = #{observeEndDate},
      </if>
      <if test="settleDays != null">
        SETTLE_DAYS = #{settleDays},
      </if>
      <if test="settleDate != null">
        SETTLE_DATE = #{settleDate}
      </if>
    </set>
    where PROD_TYPE = #{prodType}
    AND STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageRuleDefine">
    insert into RB_DC_STAGE_RULE_DEFINE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="structClass != null">
        STRUCT_CLASS,
      </if>
      <if test="stageInitPrice != null">
        STAGE_INIT_PRICE,
      </if>
      <if test="openRate != null">
        OPEN_RATE,
      </if>
      <if test="accrRate != null">
        ACCR_RATE,
      </if>
      <if test="preRate != null">
        PRE_RATE,
      </if>
      <if test="highGradeRate != null">
        HIGH_GRADE_RATE,
      </if>
      <if test="lowEndRate != null">
        LOW_END_RATE,
      </if>
      <if test="yearsRate != null">
        YEARS_RATE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="stageFixedRate != null">
        STAGE_FIXED_RATE,
      </if>
      <if test="stageSpreadRate != null">
        STAGE_SPREAD_RATE,
      </if>
      <if test="stagePercentRate != null">
        STAGE_PERCENT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="acrRateType != null">
        ACR_RATE_TYPE,
      </if>
      <if test="underlyingId != null">
        UNDERLYING_ID,
      </if>
      <if test="touchType != null">
        TOUCH_TYPE,
      </if>
      <if test="touchPercent != null">
        TOUCH_PERCENT,
      </if>
      <if test="lowThreshold != null">
        LOW_THRESHOLD,
      </if>
      <if test="highThreshold != null">
        HIGH_THRESHOLD,
      </if>
      <if test="inSectionDays != null">
        IN_SECTION_DAYS,
      </if>
      <if test="outSectionDays != null">
        OUT_SECTION_DAYS,
      </if>
      <if test="touchFlag != null">
        TOUCH_FLAG,
      </if>
      <if test="retryFlag != null">
        RETRY_FLAG,
      </if>
      <if test="observeFlag != null">
        OBSERVE_FLAG,
      </if>
      <if test="touchStopFlag != null">
        TOUCH_STOP_FLAG,
      </if>
      <if test="stageLowLimit != null">
        STAGE_LOW_LIMIT,
      </if>
      <if test="initAmt != null">
        INIT_AMT,
      </if>
      <if test="sgMaxAmt != null">
        SG_MAX_AMT,
      </if>
      <if test="amtUnit != null">
        AMT_UNIT,
      </if>
      <if test="stageRiskLevel != null">
        STAGE_RISK_LEVEL,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="maturityDate != null">
        MATURITY_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="ruleDesc != null">
        RULE_DESC,
      </if>
      <if test="observeStartDate != null">
        OBSERVE_START_DATE,
      </if>
      <if test="observeEndDate != null">
        OBSERVE_END_DATE,
      </if>
      <if test="settleDays != null">
        SETTLE_DAYS,
      </if>
      <if test="settleDate != null">
        SETTLE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="structClass != null">
        #{structClass},
      </if>
      <if test="stageInitPrice != null">
        #{stageInitPrice},
      </if>
      <if test="openRate != null">
        #{openRate},
      </if>
      <if test="accrRate != null">
        #{accrRate},
      </if>
      <if test="preRate != null">
        #{preRate},
      </if>
      <if test="highGradeRate != null">
        #{highGradeRate},
      </if>
      <if test="lowEndRate != null">
        #{lowEndRate},
      </if>
      <if test="yearsRate != null">
        #{yearsRate},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="stageFixedRate != null">
        #{stageFixedRate},
      </if>
      <if test="stageSpreadRate != null">
        #{stageSpreadRate},
      </if>
      <if test="stagePercentRate != null">
        #{stagePercentRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="acrRateType != null">
        #{acrRateType},
      </if>
      <if test="corporeId != null">
        #{corporeId},
      </if>
      <if test="touchType != null">
        #{touchType},
      </if>
      <if test="touchPercent != null">
        #{touchPercent},
      </if>
      <if test="lowThreshold != null">
        #{lowThreshold},
      </if>
      <if test="highThreshold != null">
        #{highThreshold},
      </if>
      <if test="inSectionDays != null">
        #{inSectionDays},
      </if>
      <if test="outSectionDays != null">
        #{outSectionDays},
      </if>
      <if test="isTouch != null">
        #{isTouch},
      </if>
      <if test="isRetry != null">
        #{isRetry},
      </if>
      <if test="observeFlag != null">
        #{observeFlag},
      </if>
      <if test="touchStopFlag != null">
        #{touchStopFlag},
      </if>
      <if test="stageLowLimit != null">
        #{stageLowLimit},
      </if>
      <if test="initAmt != null">
        #{initAmt},
      </if>
      <if test="sgMaxAmt != null">
        #{sgMaxAmt},
      </if>
      <if test="amtUnit != null">
        #{amtUnit},
      </if>
      <if test="stageRiskLevel != null">
        #{stageRiskLevel},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="maturityDate != null">
        #{maturityDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="authOfficerId != null">
        #{authOfficerId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="ruleDesc != null">
        #{ruleDesc},
      </if>
      <if test="observeStartDate != null">
        #{observeStartDate},
      </if>
      <if test="observeEndDate != null">
        #{observeEndDate},
      </if>
      <if test="settleDays != null">
        #{settleDays},
      </if>
      <if test="settleDate != null">
        #{settleDate},
      </if>
    </trim>
  </insert>
</mapper>
