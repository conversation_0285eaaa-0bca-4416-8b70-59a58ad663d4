<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcChangeApplyHist">

    <select id="selectChangeApplyHistByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcChangeApplyHist">
        select
        <include refid="Base_Column"/>
        from
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
        order by TRAN_TIMESTAMP desc
    </select>
</mapper>