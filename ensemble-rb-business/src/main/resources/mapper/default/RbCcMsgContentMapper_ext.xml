<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCcMsgContent">
	<select id="queryMinTranDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCcMsgContent"
			useCache="false">
        select MIN(TRAN_DATE) TRAN_DATE
        from RB_CC_MSG_CONTENT
        <where>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>
    <!-- Delete based on primary key -->
    <delete id="deleteByPrimaryKeyExt">
        DELETE FROM <include refid="Table_Name" />
        <where>
            <trim suffixOverrides="AND">
                <if test="seqNo != null and  seqNo != '' ">
                    SEQ_NO = #{seqNo}  AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo}  AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </delete>

    <select id="getRbCcMsgContentForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCcMsgContent"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCcMsgContent">
        SELECT <include refid="Base_Column"/>
        FROM RB_CC_MSG_CONTENT
        WHERE SEQ_NO = #{seqNo,jdbcType=VARCHAR}
        AND MSG_STATUS != 'S'
        AND MSG_STATUS != 'R'
        FOR UPDATE
    </select>

</mapper>
