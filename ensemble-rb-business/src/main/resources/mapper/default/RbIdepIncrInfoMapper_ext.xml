<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrInfo">

    <select id="queryIncrInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_IDEP_INCR_INFO
        WHERE AGREEMENT_ID = #{agreementId}
        AND INCR_STATUS = 'A'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

        ORDER BY TRAN_DATE DESC
    </select>

    <select id="getMbIDepIncrInfoByDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_IDEP_INCR_INFO
        WHERE AGREEMENT_ID = #{agreementId}
        <if test="startDate != null">
            AND
            <![CDATA[
          TRAN_DATE <= #{startDate}
        ]]>
        </if>
        <if test="endDate != null">
            AND
            <![CDATA[
          TRAN_DATE >= #{endDate}
            ]]>
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="companyList != null">
            AND COMPANY in
            <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY TRAN_DATE ASC,SEQ_NO+0 DESC
    </select>

    <select id="getAllIncrInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrInfo">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_IDEP_INCR_INFO
        <where>
            <if test="seqNo != null">
                SEQ_NO = #{seqNo}
            </if>
            <if test="internalKey != null">
                AND INTERNAL_KEY = #{internalKey}
            </if>
            <if test="agreementId != null">
                AND AGREEMENT_ID = #{agreementId}
            </if>
            <if test="iDepSubType != null">
                AND IDEP_SUB_TYPE = #{iDepSubType}
            </if>
            <if test="tranDate != null">
                AND TRAN_DATE = #{tranDate}
            </if>
            <if test="tranAmt != null">
                AND TRAN_AMT = #{tranAmt}
            </if>
            <if test="incrStatus != null">
                AND INCR_STATUS = #{incrStatus}
            </if>
            <if test="originalSeqNo != null">
                AND ORIGINAL_SEQ_NO = #{originalSeqNo}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
</mapper>
