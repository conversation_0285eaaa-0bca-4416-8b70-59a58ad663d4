<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalance">

  <select id="getPcpAcctBalanceForUpdate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalance"
          parameterType="java.util.Map" flushCache="true" useCache="false">
    select
    <include refid="Base_Column"/>
    from RB_PCP_ACCT_BALANCE
    where PCP_GROUP_ID = #{pcpGroupId} AND CLIENT_NO = #{clientNo} AND INTERNAL_KEY = #{internal_key}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    for update
  </select>
  <select id="getMbPcpAcctBalance" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalance"
          parameterType="java.util.Map">
    select *
    from  RB_PCP_ACCT_BALANCE
    where PCP_GROUP_ID = #{pcpGroupId} AND CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
    <select id="getAllMbAcctBalByGroupId" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalance"
            parameterType="java.util.Map">
    select *
    from  RB_PCP_ACCT_BALANCE
    where PCP_GROUP_ID = #{pcpGroupId}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
  </select>

    <select id="getMbAcctBalAllNotForLock" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalance"
            parameterType="java.util.Map" flushCache="true" useCache="false">
        select
        <include refid="Base_Column"/>
        from RB_PCP_ACCT_BALANCE
        where PCP_GROUP_ID = #{pcpGroupId} AND CLIENT_NO = #{clientNo} AND INTERNAL_KEY = #{internal_key}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </select>
<!--by zhuxyw-->
  <select id="getAcctBalByClientNo" resultType="java.lang.String"
          parameterType="java.util.Map">
    select LAST_PCP_BALANCE
    from  RB_PCP_ACCT_BALANCE
    where CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
