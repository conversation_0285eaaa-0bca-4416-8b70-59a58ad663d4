<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRate">
	<select id="getSplitRateList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRate">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_INT_LAYER_RATE t
		WHERE t.INTERNAL_KEY = #{internalKey}
		AND t.INT_CLASS = #{intClass}
        AND t.START_DATE <![CDATA[ <=]]> #{startDate,jdbcType=DATE}
        AND t.END_DATE <![CDATA[ >= ]]> #{startDate,jdbcType=DATE}
		AND EXISTS (SELECT 1 FROM RB_AGREEMENT WHERE AGREEMENT_ID = t.AGREEMENT_ID and AGREEMENT_STATUS = 'A')
		<if test="clientNo != null and  clientNo != '' ">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>


	<select id="getLayerRateList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRate">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_INT_LAYER_RATE
		WHERE INTERNAL_KEY = #{internalKey}
		AND AGREEMENT_ID = #{agreementId}
		<if test="clientNo != null and  clientNo != '' ">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getLayerRateByIdAndAmt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRate">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_INT_LAYER_RATE
		WHERE INTERNAL_KEY = #{internalKey}
		AND AGREEMENT_ID = #{agreementId}
		AND NEAR_AMT = #{nearAmt}
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
