<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBackupCleanParam">
<!--    &lt;!&ndash;分区不分段备份&ndash;&gt;-->
<!--    <select id="partitionNoSegmentBackupStep" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">-->
<!--        SELECT-->
<!--        <include refid="comet_step_column"></include>-->
<!--        FROM-->
<!--        <include refid="Table_Name"></include>-->
<!--        WHERE BACKUP_TYPE = '01' AND BACKUP_SEGMENT_FLAG = 'N' AND NEXT_CLEAN_DATE = #{runDate}-->
<!--        <include refid="comet_step_where"></include>-->
<!--    </select>-->

<!--    &lt;!&ndash;条件不分段备份&ndash;&gt;-->
<!--    <select id="conditionBackupStep" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">-->
<!--        SELECT-->
<!--        <include refid="comet_step_column"></include>-->
<!--        FROM-->
<!--        <include refid="Table_Name"></include>-->
<!--        WHERE BACKUP_TYPE = '02' AND BACKUP_SEGMENT_FLAG = 'N' AND NEXT_CLEAN_DATE = #{runDate}-->
<!--        <include refid="comet_step_where"></include>-->
<!--    </select>-->

<!--    &lt;!&ndash;条件不分段清理&ndash;&gt;-->
<!--    <select id="conditionNoSegmentCleanStep" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">-->
<!--        SELECT-->
<!--        <include refid="comet_step_column"></include>-->
<!--        FROM-->
<!--        <include refid="Table_Name"></include>-->
<!--        WHERE CLEAN_TYPE = '02' AND CLEAN_SEGMENT_FLAG = 'N' AND NEXT_CLEAN_DATE = #{runDate}-->
<!--        <include refid="comet_step_where"></include>-->
<!--    </select>-->

<!--    &lt;!&ndash;分区不分段清理&ndash;&gt;-->
<!--    <select id="partitionCleanStep" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">-->
<!--        SELECT-->
<!--        <include refid="comet_step_column"></include>-->
<!--        FROM-->
<!--        <include refid="Table_Name"></include>-->
<!--        WHERE CLEAN_TYPE = '01' AND CLEAN_SEGMENT_FLAG = 'N' AND NEXT_CLEAN_DATE = #{runDate}-->
<!--        <include refid="comet_step_where"></include>-->
<!--    </select>-->

    <select id="getBackupTodayParam" resultMap="Base_Result_Map" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBackupCleanParam">
        SELECT
        <include refid="Base_Column"></include>
        FROM
        <include refid="Table_Name"></include>
        WHERE BACKUP_TYPE != '03' AND NEXT_CLEAN_DATE = #{nextCleanDate}
    </select>

    <select id="getCleanTodayParam" resultMap="Base_Result_Map" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBackupCleanParam">
        SELECT
        <include refid="Base_Column"></include>
        FROM
        <include refid="Table_Name"></include>
        WHERE CLEAN_TYPE != '03' AND NEXT_CLEAN_DATE = #{nextCleanDate}
    </select>
</mapper>
