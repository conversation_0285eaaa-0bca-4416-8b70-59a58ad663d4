<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertVoucherDtl">

  <update id="updateByDepositCertNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertVoucherDtl">
    <!-- WARNING - @mbggenerated This element is automatically generated by
        Galaxy Tools Generator, do not modify. This element was generated on Thu
        Jan 29 10:48:04 CST 2015. -->
    update RB_DEPOSIT_CERT_VOUCHER_DTL
    <set>
      <if test="delUserId != null">
        DEL_USER_ID = #{delUserId},
      </if>
      <if test="delAuthUserId != null">
        DEL_AUTH_USER_ID = #{delAuthUserId},
      </if>
    </set>
    where DEPOSIT_CERT_NO = #{depositCertNo}
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateByVoucherNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertVoucherDtl">
    <!-- WARNING - @mbggenerated This element is automatically generated by
        Galaxy Tools Generator, do not modify. This element was generated on Thu
        Jan 29 10:48:04 CST 2015. -->
    update RB_DEPOSIT_CERT_VOUCHER_DTL
    <set>
      <if test="delUserId != null">
        DEL_USER_ID = #{delUserId},
      </if>
      <if test="delAuthUserId != null">
        DEL_AUTH_USER_ID = #{delAuthUserId},
      </if>
    </set>
    where DEPOSIT_CERT_NO = #{depositCertNo}
    and VOUCHER_NO = #{voucherNo}
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="getDepositCertVoucher" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertVoucherDtl">
    select <include refid="Base_Column"/>
    from RB_DEPOSIT_CERT_VOUCHER_DTL
    where DEPOSIT_CERT_NO = #{mbDepositCertNo}
    <if test="clientNo != null">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- Multi-legal entity fields by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by voucher_no asc
  </select>
</mapper>
