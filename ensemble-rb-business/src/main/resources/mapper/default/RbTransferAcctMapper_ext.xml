<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTransferAcct">
	<select id="getRbTransferAcctList"  resultMap="Base_Result_Map">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />
		<where>
			<trim suffixOverrides="AND">
				<if test="tranBranch != null and  tranBranch != '' ">
					AND TRAN_BRANCH = #{tranBranch}
				</if>
				<if test="startDate != null">
					AND TRAN_DATE <![CDATA[>=]]>  #{startDate}
				</if>
				<if test="endDate != null">
					AND	TRAN_DATE <![CDATA[<=]]>  #{endDate}
				</if>
				<if test="tdInoutOperateType != null and  tdInoutOperateType != '' ">
					AND TD_INOUT_OPERATE_TYPE = #{tdInoutOperateType}
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					AND (TRANSFER_OUT_AFTER_ACCT = #{baseAcctNo} or TRANSFER_OUT_BEFORE_ACCT= #{baseAcctNo}
					or TRANSFER_IN_AFTER_ACCT= #{baseAcctNo} or TRANSFER_IN_BEFOR_ACCT = #{baseAcctNo})
				</if>
				<!--<if test="branchList != null and branchList.size() > 0">
					AND TRAN_BRANCH IN
					<foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
						#{branch}
					</foreach>
				</if>-->
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					AND	COMPANY = #{company}
				</if>
			</trim>
		</where>
	</select>
</mapper>
