<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementTxy">

  <select id="getAgreementTxyBySignId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementTxy">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_TXY
    where SIGN_ID = #{signId}
  </select>

  <select id="getAgreementTxyByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementTxy">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_TXY
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

  <select id="getAgreementTxyByMainAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementTxy">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_TXY
    where MAIN_AGREEMENT_ID = #{mainAgreementId}
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

  <select id="getAgreementTxyByAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementTxy">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_TXY
    where AGREEMENT_ID = #{agreementId}
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

  <update id="updateWithNullByPrimary" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementTxy">
    UPDATE RB_AGREEMENT_TXY
    <include refid="Base_SetNull"/>
    <include refid="PrimaryKey_Where"/>
  </update>

  <sql id="Base_SetNull">
    <set>
      <if test="agreementId != null and agreementId != '' ">
        AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR},
      </if>
      <if test="mainAgreementId != null and mainAgreementId != '' ">
        MAIN_AGREEMENT_ID = #{mainAgreementId,jdbcType=VARCHAR},
      </if>
      <if test="baseAcctNo != null and baseAcctNo != '' ">
        BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="prodType != null and prodType != '' ">
        PROD_TYPE = #{prodType,jdbcType=VARCHAR},
      </if>
      <if test="acctCcy != null and acctCcy != '' ">
        ACCT_CCY = #{acctCcy,jdbcType=VARCHAR},
      </if>
      <if test="acctSeqNo != null and acctSeqNo != '' ">
        ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="agreeIntRate != null ">
        AGREE_INT_RATE = #{agreeIntRate,jdbcType=DECIMAL},
      </if>
      <if test="overGradeRate != null ">
        OVER_GRADE_RATE = #{overGradeRate,jdbcType=DECIMAL},
      </if>
      <if test="pastFadRate != null ">
        PAST_FAD_RATE = #{pastFadRate,jdbcType=DECIMAL},
      </if>
        CYCLE_FREQ = #{cycleFreq,jdbcType=VARCHAR},
      <if test="agreProdType != null and agreProdType != '' ">
        AGRE_PROD_TYPE = #{agreProdType,jdbcType=VARCHAR},
      </if>
      <if test="mainFlag != null and mainFlag != '' ">
        MAIN_FLAG = #{mainFlag,jdbcType=VARCHAR},
      </if>
      <if test="signId != null and signId != '' ">
        SIGN_ID = #{signId,jdbcType=VARCHAR},
      </if>
      <if test="internalKey != null ">
        INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},
      </if>
      <if test="tranTimestamp != null and tranTimestamp != '' ">
        TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="userId != null and userId != '' ">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="branch != null and branch != '' ">
        BRANCH = #{branch,jdbcType=VARCHAR},
      </if>
      <if test="channel != null and channel != '' ">
        CHANNEL = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="company != null and company != '' ">
        COMPANY = #{company,jdbcType=VARCHAR},
      </if>
      <if test="agg != null ">
        AGG = #{agg,jdbcType=DECIMAL},
      </if>

    </set>
  </sql>

</mapper>
