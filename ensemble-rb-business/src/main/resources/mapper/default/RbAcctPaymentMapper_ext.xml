<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctPayment">

    <select id="selectRbAcctPaymentInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctPayment">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_PAYMENT
        where INTERNAL_KEY = #{internalKey}
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="channel != null and channel != ''">
            AND CHANNEL = #{channel}
        </if>
        <if test="blacklistIndFlag != null and blacklistIndFlag != ''">
            AND BLACKLIST_IND_FLAG = #{blacklistIndFlag}
        </if>
    </select>
</mapper>
