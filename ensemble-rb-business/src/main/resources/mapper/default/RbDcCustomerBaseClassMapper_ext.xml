<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcCustomerBaseClass">

    <delete id="delCustomerBaseClassByStageCode" parameterType="java.util.Map">
        delete from RB_DC_CUSTOMER_BASE_CLASS
        where STAGE_CODE = #{stageCode}
    </delete>

    <select id="selCustomerBaseClassByStageCode" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcCustomerBaseClass">
        select <include refid="Base_Column"/>
        from RB_DC_CUSTOMER_BASE_CLASS
        where STAGE_CODE = #{stageCode}
    </select>

    <select id="queryCustomerBaseClassMap" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcCustomerBaseClass">
        select <include refid="Base_Column"/>
        FROM RB_DC_CUSTOMER_BASE_CLASS
        <where>
            <if test="stageCodes != null">
                AND STAGE_CODE in
                <foreach collection="stageCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerBase != null and customerBase != ''">
                AND CUSTOMER_SON = #{customerBase}
            </if>
        </where>
    </select>
</mapper>
