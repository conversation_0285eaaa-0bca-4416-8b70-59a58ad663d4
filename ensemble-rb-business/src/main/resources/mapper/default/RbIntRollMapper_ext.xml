<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll">
    <select id="get" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll">
        SELECT  *
        FROM RB_INT_ROLL WHERE
        INTERNAL_KEY = #{internalKey}
        <if test="intClass!=null">
            AND INT_CLASS = #{intClass}
        </if>
        <if test="tranDate!=null">
            AND TRAN_DATE = #{tranDate}
        </if>
        <if test="taxFlag!=null">
            AND TAX_FLAG = #{taxFlag}
        </if>
        <if test="clientNo!=null">
            AND CLIENT_NO = #{clientNo}
        </if>
        AND EFFECT_DATE = #{effectDate,jdbcType=DATE}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getByTwoKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll">

        SELECT  *
        FROM RB_INT_ROLL
        <where>
            INTERNAL_KEY = #{internalKey}
            <if test="intClass!=null">
                AND INT_CLASS = #{intClass}
            </if>
            <if test="effectFlag!=null">
                AND EFFECT_FLAG = #{effectFlag}
            </if>
            <if test="effectDate!=null">
                <![CDATA[AND EFFECT_DATE <= #{effectDate,jdbcType=DATE}]]>
            </if>
            <if test="clientNo!=null">
                AND CLIENT_NO = #{clientNo}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
        ORDER BY INT_CLASS DESC,EFFECT_DATE DESC


    </select>
    <select id="getByNoDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll">
        SELECT  * FROM RB_INT_ROLL
        WHERE INTERNAL_KEY = #{internalKey}
        AND INT_CLASS = #{intClass}
        <if test="clientNo!=null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY EFFECT_DATE DESC
    </select>

    <select id="selectBatchDateCount" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'oracle'">
			SELECT   MIN (INTERNAL_KEY) START_KEY, MAX (INTERNAL_KEY) END_KEY
			FROM   (  SELECT DISTINCT INTERNAL_KEY
			FROM   RB_INT_ROLL
			WHERE EFFECT_DATE = #{runDate,jdbcType=DATE}
			<if test="clientNo!=null">
            AND CLIENT_NO = #{clientNo}
            </if>
            <if test="company != null and company != '' ">
              AND COMPANY = #{company}
            </if>
			ORDER BY   INTERNAL_KEY)
			GROUP BY   TRUNC (ROWNUM-1 / #{maxPerCount}) order by START_KEY

        </if>
        <if test="_databaseId == 'mysql'">
			SELECT
			MIN(INTERNAL_KEY) START_KEY,
			MAX(INTERNAL_KEY) END_KEY
			FROM
			(
			SELECT
			DISTINCT INTERNAL_KEY INTERNAL_KEY,
			@rownum :=@rownum + 1 AS rownum
			FROM
			RB_INT_ROLL mm,
			(SELECT @rownum := -1) t
			WHERE mm.EFFECT_DATE = #{runDate}
			<if test="clientNo!=null">
            AND mm.CLIENT_NO = #{clientNo}
            </if>
            <if test="company != null and company != '' ">
              AND mm.COMPANY = #{company}
            </if>
            ORDER BY   mm.INTERNAL_KEY
			) tt
			GROUP BY
			FLOOR(
			tt.rownum /#{maxPerCount} )
        </if>
    </select>
    <select id="selectBatchDateCountForKeys" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT  distinct internal_key
        FROM RB_INT_ROLL
        WHERE EFFECT_DATE = #{EFFECT_DATE,jdbcType=DATE}
          <![CDATA[
          AND INTERNAL_KEY >= #{START_KEY}
          AND INTERNAL_KEY <= #{END_KEY}
          ]]>
          AND IS_EFFECT_FLAG = #{IS_EFFECT_FLAG}
          AND TAX_FLAG= #{IS_TAX_FLAG}
        <if test="clientNo!=null">
          AND CLIENT_NO = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        ORDER BY INTERNAL_KEY
    </select>

    <select id="selectBatchDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll">
        SELECT  * FROM RB_INT_ROLL
        WHERE EFFECT_DATE = #{EFFECT_DATE,jdbcType=DATE}
        <![CDATA[
          AND INTERNAL_KEY >= #{START_KEY}
          AND INTERNAL_KEY <= #{END_KEY}
         ]]>
          AND IS_EFFECT_FLAG = #{IS_EFFECT_FLAG}
          AND TAX_FLAG= #{IS_TAX_FLAG}
        <if test="clientNo!=null">
          AND CLIENT_NO = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        ORDER BY INTERNAL_KEY
	</select>

    <update id="updateStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll">
        UPDATE RB_INT_ROLL
        SET EFFECT_FLAG = 'Y'
        WHERE INTERNAL_KEY = #{internalKey}
        AND EFFECT_DATE = #{effectDate,jdbcType=DATE}
        AND INT_CLASS = #{intClass}
        AND TAX_FLAG= #{taxFlag}
        <if test="clientNo!=null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <update id="updateRbIntRoll"  >
        UPDATE <include refid="Table_Name" />
        <set>
            <if test="newRollDay != null and  newRollDay != '' ">
                NEW_ROLL_DAY = #{newRollDay},
            </if>
            <if test="calcByInt != null and  calcByInt != '' ">
                CALC_BY_INT = #{calcByInt},
            </if>
            <if test="apprUserId != null and  apprUserId != '' ">
                APPR_USER_ID = #{apprUserId},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="systemId != null and  systemId != '' ">
                SYSTEM_ID = #{systemId},
            </if>
            <if test="newIntType != null and  newIntType != '' ">
                NEW_INT_TYPE = #{newIntType},
            </if>
            <if test="newIntApplType != null and  newIntApplType != '' ">
                NEW_INT_APPL_TYPE = #{newIntApplType},
            </if>
            <if test="company != null and  company != '' ">
                COMPANY = #{company},
            </if>
            <if test="newNextRollDate != null ">
                NEW_NEXT_ROLL_DATE = #{newNextRollDate},
            </if>
            <if test="isRetryFlag != null and  isRetryFlag != '' ">
                IS_RETRY_FLAG = #{isRetryFlag},
            </if>
            <if test="applyId != null and  applyId != '' ">
                APPLY_ID = #{applyId},
            </if>
            <if test="newRealTaxRate != null ">
                NEW_REAL_TAX_RATE = #{newRealTaxRate},
            </if>
            <if test="newRollFreq != null and  newRollFreq != '' ">
                NEW_ROLL_FREQ = #{newRollFreq},
            </if>
            <if test="userId != null and  userId != '' ">
                USER_ID = #{userId},
            </if>
            <if test="newSpreadTaxPercent != null ">
                NEW_SPREAD_TAX_PERCENT = #{newSpreadTaxPercent},
            </if>
            <if test="newRateEffectType != null and  newRateEffectType != '' ">
                NEW_RATE_EFFECT_TYPE = #{newRateEffectType},
            </if>
            <if test="newSpreadTaxRate != null ">
                NEW_SPREAD_TAX_RATE = #{newSpreadTaxRate},
            </if>
            <if test="apprFlag != null and  apprFlag != '' ">
                APPR_FLAG = #{apprFlag},
            </if>
            <if test="effectFlag != null and  effectFlag != '' ">
                EFFECT_FLAG = #{effectFlag},
            </if>
            NEW_SPREAD_RATE = #{newSpreadRate},
            NEW_SPREAD_PERCENT = #{newSpreadPercent},
            NEW_REAL_RATE = #{newRealRate}
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="internalKey != null ">
                    INTERNAL_KEY = #{internalKey}  AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo}  AND
                </if>
                <if test="intClass != null and  intClass != '' ">
                    INT_CLASS = #{intClass}  AND
                </if>
                <if test="effectDate != null ">
                    EFFECT_DATE = #{effectDate,jdbcType=DATE}  AND
                </if>
                <if test="tranTimestamp != null ">
                    TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE}  AND
                </if>
                <if test="taxFlag != null and  taxFlag != '' ">
                    TAX_FLAG = #{taxFlag}  AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>

    <select id="getActiveAllRollList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRoll">

        SELECT
        <include refid="Base_Column" />
        FROM
        <include refid="Table_Name" />
        <where>
            <trim suffixOverrides="AND">
                <if test="effectDate != null ">
                    <![CDATA[
                    EFFECT_DATE <= #{effectDate}  AND
                ]]>
                </if>
                <if test="effectFlag != null">
                    EFFECT_FLAG = #{effectFlag} AND
                </if>
                <if test="clientNo != null">
                    CLIENT_NO = #{clientNo}  AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
                <if test="internalkey != null and internalkey.length() > 0">
                    INTERNAL_KEY = #{internalkey}
                </if>
            </trim>
        </where>
    </select>
</mapper>
