<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClBatchTranDetail">

	<select id="getSettleSum" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
		SELECT count(*) AS SUM_NUM, sum(TRAN_AMT) AS SUM_AMT
		FROM RB_CL_BATCH_TRAN_DETAIL
		<where>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>
	<select id="selectRbClBatchTranDetailForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClBatchTranDetail"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClBatchTranDetail">
		SELECT <include refid="Base_Column"/>
		FROM RB_CL_BATCH_TRAN_DETAIL
		WHERE SEQ_NO = #{seqNo}
		AND RET_STATUS = #{retStatus} FOR UPDATE
	</select>

</mapper>
