<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommission">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommission" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommission">
    select <include refid="Base_Column"/>
    from RB_COMMISSION
    where COMMISSION_DOCUMENT_TYPE = #{commissionDocumentType}
      <if test="commissionDocumentId != null">
        AND COMMISSION_DOCUMENT_ID = #{commissionDocumentId}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommission">
    delete from RB_COMMISSION
    where COMMISSION_DOCUMENT_TYPE = #{commissionDocumentType}
      <if test="commissionDocumentId != null">
        AND COMMISSION_DOCUMENT_ID = #{commissionDocumentId}
      </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommission">
    update RB_COMMISSION
    <set>
      <if test="commissionClientName != null">
        COMMISSION_CLIENT_NAME = #{commissionClientName},
      </if>
      <if test="commissionClientNo != null">
        COMMISSION_CLIENT_NO = #{commissionClientNo},
      </if>
      <if test="country != null">
        COUNTRY = #{country},
      </if>
      <if test="checkDate != null">
        CHECK_DATE = #{checkDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where COMMISSION_DOCUMENT_TYPE = #{commissionDocumentType}
        AND COMMISSION_DOCUMENT_ID = #{commissionDocumentId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommission">
    insert into RB_COMMISSION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="commissionDocumentType != null">
        COMMISSION_DOCUMENT_TYPE,
      </if>
      <if test="commissionDocumentId != null">
        COMMISSION_DOCUMENT_ID,
      </if>
      <if test="commissionClientName != null">
        COMMISSION_CLIENT_NAME,
      </if>
      <if test="commissionClientNo != null">
        COMMISSION_CLIENT_NO,
      </if>
      <if test="country != null">
        COUNTRY,
      </if>
      <if test="checkDate != null">
        CHECK_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="commissionDocType != null">
        #{commissionDocType},
      </if>
      <if test="commissionDocId != null">
        #{commissionDocId},
      </if>
      <if test="commissionClientName != null">
        #{commissionClientName},
      </if>
      <if test="commissionClientNo != null">
        #{commissionClientNo},
      </if>
      <if test="country != null">
        #{country},
      </if>
      <if test="checkDate != null">
        #{checkDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="getCommission" parameterType="java.util.Map"
          resultType="java.lang.String">
    select 1
    from RB_COMMISSION
    WHERE
    <if test="checkDate != null">
       CHECK_DATE = #{checkDate}
    </if>
    <if test="commissionDocumentType != null and commissionDocumentType !=''">
      AND COMMISSION_DOCUMENT_TYPE = #{commissionDocumentType}
    </if>
    <if test="commissionDocumentId != null and commissionDocumentId !=''">
      AND COMMISSION_DOCUMENT_ID = #{commissionDocumentId}
    </if>
    <if test="commissionClientName != null and commissionClientName !=''">
      AND COMMISSION_CLIENT_NAME = #{commissionClientName}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
