<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss">

  <select id="selectByinternalKey" parameterType="java.lang.Long" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss">
    select <include refid="Base_Column"/>
    from RB_ACCT_DOSS
    where INTERNAL_KEY = #{INTERNAL_KEY}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss">
    select <include refid="Base_Column"/>
    from RB_ACCT_DOSS
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss">
    delete from RB_ACCT_DOSS
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss">
    update RB_ACCT_DOSS
    <set>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctName != null">
        ACCT_NAME = #{acctName},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="acctCcy != null">
        ACCT_CCY = #{acctCcy},
      </if>
      <if test="taxSc != null">
        TAX_SC = #{taxSc},
      </if>
      <if test="porIntTot != null">
        POR_INT_TOT = #{porIntTot},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="acctStatus != null">
        ACCT_STATUS = #{acctStatus},
      </if>
      <if test="amtType != null">
        AMT_TYPE = #{amtType},
      </if>
      <if test="balance != null">
        BALANCE = #{balance},
      </if>
      <if test="intAmt != null">
        INT_AMT = #{intAmt},
      </if>
      <if test="dossOperateType != null">
        DOSS_OPERATE_TYPE = #{dossOperateType},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="dormantDate != null">
        DORMANT_DATE = #{dormantDate},
      </if>
      <if test="dossDate != null">
        DOSS_DATE = #{dossDate},
      </if>
      <if test="outDate != null">
        OUT_DATE = #{outDate}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss">
    insert into RB_ACCT_DOSS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="acctName != null">
        ACCT_NAME,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="taxSc != null">
        TAX_SC,
      </if>
      <if test="porIntTot != null">
        POR_INT_TOT,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="acctStatus != null">
        ACCT_STATUS,
      </if>
      <if test="amtType != null">
        AMT_TYPE,
      </if>
      <if test="balance != null">
        BALANCE,
      </if>
      <if test="intAmt != null">
        INT_AMT,
      </if>
      <if test="dossOperateType != null">
        DOSS_OPERATE_TYPE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="dormantDate != null">
        DORMANT_DATE,
      </if>
      <if test="dossDate != null">
        DOSS_DATE,
      </if>
      <if test="outDate != null">
        OUT_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="acctName != null">
        #{acctName},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="taxSc != null">
        #{taxSc},
      </if>
      <if test="porIntTot != null">
        #{porIntTot},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="acctStatus != null">
        #{acctStatus},
      </if>
      <if test="amtType != null">
        #{amtType},
      </if>
      <if test="balance != null">
        #{balance},
      </if>
      <if test="intAmt != null">
        #{intAmt},
      </if>
      <if test="dossOperateType != null">
        #{dossOperateType},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="dormantDate != null">
        #{dormantDate},
      </if>
      <if test="dossDate != null">
        #{dossDate},
      </if>
      <if test="outDate != null">
        #{outDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <select id="selectByinternalKeyArray" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDoss">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_DOSS
    WHERE INTERNAL_KEY IN  ${internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
      ORDER BY TRAN_DATE DESC
  </select>
</mapper>
