<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.fm.model.FmLocHoliday">
    <sql id="Base_Column_Distinct">
        HOLIDAY_TYPE,
        HOLIDAY_DESC,
        COUNTRY,
        STATE,
        HOLIDAY_DATE,
        WORKING_HOLIDAY,
        APPLY_IND,
        COMPANY,
        TRAN_TIMESTAMP
    </sql>

    <select id="getLastWorkDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.fm.model.FmLocHoliday">
        SELECT
        <include refid="Base_Column_Distinct"/>
        FROM FM_LOC_HOLIDAY
        WHERE HOLIDAY_TYPE='N'
        <if test="country != null and country !=''">
            AND country = #{country}
        </if>
        <if test="state != null and state !=''">
            AND state = #{state}
        </if>
        <if test="holidayDate != null">
            AND HOLIDAY_DATE <![CDATA[ < ]]> #{holidayDate}
        </if>
        and ROWNUM=1
        ORDER BY HOLIDAY_DATE desc
    </select>

    <select id="getNextWorkDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.fm.model.FmLocHoliday">
        SELECT
        <include refid="Base_Column_Distinct"/>
        FROM FM_LOC_HOLIDAY
        WHERE HOLIDAY_TYPE='N'
        <if test="country != null and country !=''">
            AND country = #{country}
        </if>
        <if test="state != null and state !=''">
            AND state = #{state}
        </if>
        <if test="holidayDate != null ">
            AND HOLIDAY_DATE <![CDATA[ > ]]> #{holidayDate}
        </if>
        and ROWNUM=1
        ORDER BY HOLIDAY_DATE
    </select>
</mapper>