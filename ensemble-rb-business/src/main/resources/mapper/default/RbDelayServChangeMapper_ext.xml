<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayServChange">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayServChange" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayServChange">
    select <include refid="Base_Column"/>
    from RB_DELAY_SERV_CHANGE
    where SC_SEQ_NO = #{scSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayServChange">
    delete from RB_DELAY_SERV_CHANGE
    where SC_SEQ_NO = #{scSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayServChange">
    update RB_DELAY_SERV_CHANGE
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="feeType != null">
        FEE_TYPE = #{feeType},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="feeCcy != null">
        FEE_CCY = #{feeCcy},
      </if>
      <if test="origFeeAmt != null">
        ORIG_FEE_AMT = #{origFeeAmt},
      </if>
      <if test="discFeeAmt != null">
        DISC_FEE_AMT = #{discFeeAmt},
      </if>
      <if test="feeAmt != null">
        FEE_AMT = #{feeAmt},
      </if>
      <if test="scDiscountType != null">
        SC_DISCOUNT_TYPE = #{scDiscountType},
      </if>
      <if test="scDiscountRate != null">
        SC_DISCOUNT_RATE = #{scDiscountRate},
      </if>
      <if test="boInd != null">
        BO_IND = #{boInd},
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO = #{tranSeqNo},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY = #{chargeWay},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="chargeToInternalKey != null">
        CHARGE_TO_INTERNAL_KEY = #{chargeToInternalKey},
      </if>
      <if test="chargeToBaseAcctNo != null">
        CHARGE_TO_BASE_ACCT_NO = #{chargeToBaseAcctNo},
      </if>
      <if test="chargeToProdType != null">
        CHARGE_TO_PROD_TYPE = #{chargeToProdType},
      </if>
      <if test="chargeToCcy != null">
        CHARGE_TO_CCY = #{chargeToCcy},
      </if>
      <if test="chargeToAcctSeqNo != null">
        CHARGE_TO_ACCT_SEQ_NO = #{chargeToAcctSeqNo},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType},
      </if>
      <if test="prefix != null">
        PREFIX = #{prefix},
      </if>
      <if test="voucherSum != null">
        VOUCHER_SUM = #{voucherSum},
      </if>
      <if test="voucherStartNo != null">
        VOUCHER_START_NO = #{voucherStartNo},
      </if>
      <if test="endNo != null">
        END_NO = #{endNo},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="taxType != null">
        TAX_TYPE = #{taxType},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate},
      </if>
      <if test="taxAmt != null">
        TAX_AMT = #{taxAmt},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO = #{bankSeqNo},
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO = #{channelSeqNo},
      </if>
      <if test="tranFeeAmt != null">
        TRAN_FEE_AMT = #{tranFeeAmt},
      </if>
      <if test="osdSeqNo != null">
        OSD_SEQ_NO = #{osdSeqNo},
      </if>
      <if test="reversalFlag != null">
        REVERSAL_FLAG = #{reversalFlag},
      </if>
      <if test="reversalBranch != null">
        REVERSAL_BRANCH = #{reversalBranch},
      </if>
      <if test="reversalUserId != null">
        REVERSAL_USER_ID = #{reversalUserId},
      </if>
      <if test="reversalAuthUserId != null">
        REVERSAL_AUTH_USER_ID = #{reversalAuthUserId},
      </if>
      <if test="reversalScSeqNo != null">
        REVERSAL_SC_SEQ_NO = #{reversalScSeqNo},
      </if>
      <if test="agreementId != null">
        AGREEMENT_ID = #{agreementId},
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG = #{glPostedFlag},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where SC_SEQ_NO = #{scSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayServChange">
    insert into RB_DELAY_SERV_CHANGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scSeqNo != null">
        SC_SEQ_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="feeType != null">
        FEE_TYPE,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="feeCcy != null">
        FEE_CCY,
      </if>
      <if test="origFeeAmt != null">
        ORIG_FEE_AMT,
      </if>
      <if test="discFeeAmt != null">
        DISC_FEE_AMT,
      </if>
      <if test="feeAmt != null">
        FEE_AMT,
      </if>
      <if test="scDiscountType != null">
        SC_DISCOUNT_TYPE,
      </if>
      <if test="scDiscountRate != null">
        SC_DISCOUNT_RATE,
      </if>
      <if test="boInd != null">
        BO_IND,
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="chargeToInternalKey != null">
        CHARGE_TO_INTERNAL_KEY,
      </if>
      <if test="chargeToBaseAcctNo != null">
        CHARGE_TO_BASE_ACCT_NO,
      </if>
      <if test="chargeToProdType != null">
        CHARGE_TO_PROD_TYPE,
      </if>
      <if test="chargeToCcy != null">
        CHARGE_TO_CCY,
      </if>
      <if test="chargeToAcctSeqNo != null">
        CHARGE_TO_ACCT_SEQ_NO,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="prefix != null">
        PREFIX,
      </if>
      <if test="voucherSum != null">
        VOUCHER_SUM,
      </if>
      <if test="voucherStartNo != null">
        VOUCHER_START_NO,
      </if>
      <if test="endNo != null">
        END_NO,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="taxType != null">
        TAX_TYPE,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="taxAmt != null">
        TAX_AMT,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="tranFeeAmt != null">
        TRAN_FEE_AMT,
      </if>
      <if test="osdSeqNo != null">
        OSD_SEQ_NO,
      </if>
      <if test="reversalFlag != null">
        REVERSAL_FLAG,
      </if>
      <if test="reversalBranch != null">
        REVERSAL_BRANCH,
      </if>
      <if test="reversalUserId != null">
        REVERSAL_USER_ID,
      </if>
      <if test="reversalAuthUserId != null">
        REVERSAL_AUTH_USER_ID,
      </if>
      <if test="reversalScSeqNo != null">
        REVERSAL_SC_SEQ_NO,
      </if>
      <if test="agreementId != null">
        AGREEMENT_ID,
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scSeqNo != null">
        #{scSeqNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="feeType != null">
        #{feeType},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="feeCcy != null">
        #{feeCcy},
      </if>
      <if test="origFeeAmt != null">
        #{origFeeAmt},
      </if>
      <if test="discFeeAmt != null">
        #{discFeeAmt},
      </if>
      <if test="feeAmt != null">
        #{feeAmt},
      </if>
      <if test="scDiscountType != null">
        #{scDiscountType},
      </if>
      <if test="scDiscountRate != null">
        #{scDiscountRate},
      </if>
      <if test="primaryTranSeqNo != null">
        #{primaryTranSeqNo},
      </if>
      <if test="boInd != null">
        #{boInd},
      </if>
      <if test="tranSeqNo != null">
        #{tranSeqNo},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="chargeWay != null">
        #{chargeWay},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="chargeToInternalKey != null">
        #{chargeToInternalKey},
      </if>
      <if test="chargeToBaseAcctNo != null">
        #{chargeToBaseAcctNo},
      </if>
      <if test="chargeToProdType != null">
        #{chargeToProdType},
      </if>
      <if test="chargeToCcy != null">
        #{chargeToCcy},
      </if>
      <if test="chargeToAcctSeqNo != null">
        #{chargeToAcctSeqNo},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
      <if test="prefix != null">
        #{prefix},
      </if>
      <if test="voucherSum != null">
        #{voucherSum},
      </if>
      <if test="startNo != null">
        #{startNo},
      </if>
      <if test="endNo != null">
        #{endNo},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="taxType != null">
        #{taxType},
      </if>
      <if test="taxRate != null">
        #{taxRate},
      </if>
      <if test="taxAmt != null">
        #{taxAmt},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="bankSeqNo != null">
        #{bankSeqNo},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="tranFeeAmt != null">
        #{tranFeeAmt},
      </if>
      <if test="osdSeqNo != null">
        #{osdSeqNo},
      </if>
      <if test="reversal != null">
        #{reversal},
      </if>
      <if test="reversalDate != null">
        #{reversalDate},
      </if>
      <if test="reversalBranch != null">
        #{reversalBranch},
      </if>
      <if test="reversalUserId != null">
        #{reversalUserId},
      </if>
      <if test="reversalAuthUserId != null">
        #{reversalAuthUserId},
      </if>
      <if test="reversalScSeqNo != null">
        #{reversalScSeqNo},
      </if>
      <if test="agreementId != null">
        #{agreementId},
      </if>
      <if test="glPosted != null">
        #{glPosted},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <select id="getDelayServByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayServChange">
    select <include refid="Base_Column"/>
    from RB_DELAY_SERV_CHANGE
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
