<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbInvestDayInfo">

    <update id="updRbInvestDayInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInvestDayInfo">
        update Rb_Invest_Day_Info
        <set>
            <if test="retMsg != null and  retMsg != '' ">
                Ret_Msg = #{retMsg, jdbcType=VARCHAR},
            </if>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null and  errorCode != '' ">
                Error_Code = #{errorCode, jdbcType=VARCHAR},
            </if>
            <if test="errorDesc != null and  errorDesc != '' ">
                Error_Desc = #{errorDesc, jdbcType=VARCHAR},
            </if>
        </set>
        where
        1=1
        <if test="batchNo != null and  batchNo != '' ">
           and Batch_No = #{batchNo, jdbcType=VARCHAR}
        </if>
        <if test="seqNo != null and  seqNo != '' ">
            and Seq_No = #{seqNo, jdbcType=VARCHAR}
        </if>
    </update>

    <select id="selectInvestDayInfoForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInvestDayInfo"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbInvestDayInfo">
        SELECT <include refid="Base_Column"/>
        FROM RB_INVEST_DAY_INFO
        WHERE BATCH_NO = #{batchNo} and SEQ_NO = #{seqNo} and BATCH_STATUS = 'P' FOR UPDATE
    </select>

</mapper>
