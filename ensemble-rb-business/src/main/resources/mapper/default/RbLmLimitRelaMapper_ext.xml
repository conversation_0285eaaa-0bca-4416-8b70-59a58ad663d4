<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitRela" >
  <sql id="Base_Column_List">
    CTRL_ID,
    LM_TYPE_ID,
    LM_TYPE,
    EFFECT_DATE,
    REMARK,
    CV_FLAG,
    COMPANY,
    TRAN_TIMESTAMP
  </sql>
  <!--<select id="selectByPrimaryKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitRela" parameterType="map" >-->
    <!--select <include refid="Base_Column_List"/>-->
    <!--from rb_lm_limit_rela-->
    <!--where LM_TYPE_ID = #{lmTypeId,jdbcType=VARCHAR}-->
      <!--and LM_TYPE = #{lmType,jdbcType=VARCHAR}-->
      <!--and CV_FLAG = #{cvFlag,jdbcType=VARCHAR}-->
  <!--</select>-->
  <!--<insert id="insert" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitRela" >-->
    <!--insert into rb_lm_limit_rela-->
    <!--<trim prefix="(" suffix=")" suffixOverrides="," >-->
      <!--<if test="lmTypeId != null" >-->
        <!--LM_TYPE_ID,-->
      <!--</if>-->
      <!--<if test="lmType != null" >-->
        <!--LM_TYPE,-->
      <!--</if>-->
      <!--<if test="cvFlag != null" >-->
        <!--CV_FLAG,-->
      <!--</if>-->
      <!--<if test="ctrlId != null" >-->
        <!--CTRL_ID,-->
      <!--</if>-->
      <!--<if test="effectDate != null" >-->
        <!--EFFECT_DATE,-->
      <!--</if>-->
      <!--<if test="remark != null" >-->
        <!--REMARK,-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP,-->
      <!--</if>-->
    <!--</trim>-->
    <!--<trim prefix="values (" suffix=")" suffixOverrides="," >-->
      <!--<if test="lmTypeId != null" >-->
        <!--#{lmTypeId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="lmType != null" >-->
        <!--#{lmType,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="cvFlag != null" >-->
        <!--#{cvFlag,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="ctrlId != null" >-->
        <!--#{ctrlId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="effectDate != null" >-->
        <!--#{effectDate,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="remark != null" >-->
        <!--#{remark,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--#{tranTimestamp,jdbcType=VARCHAR},-->
      <!--</if>-->
    <!--</trim>-->
  <!--</insert>-->
  <!--<update id="updateByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitRela" >-->
    <!--update rb_lm_limit_rela-->
    <!--<set >-->
      <!--<if test="ctrlId != null" >-->
        <!--CTRL_ID = #{ctrlId,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="effectDate != null" >-->
        <!--EFFECT_DATE = #{effectDate,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="remark != null" >-->
        <!--REMARK = #{remark,jdbcType=VARCHAR},-->
      <!--</if>-->
      <!--<if test="tranTimestamp != null">-->
        <!--TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}-->
      <!--</if>-->
    <!--</set>-->
    <!--where LM_TYPE_ID = #{lmTypeId,jdbcType=VARCHAR}-->
      <!--and LM_TYPE = #{lmType,jdbcType=VARCHAR}-->
      <!--and CV_FLAG = #{cvFlag,jdbcType=VARCHAR}-->
  <!--</update>-->
  <select id="selectByCtrlId" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitRela" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitRela" >
    select <include refid="Base_Column_List"/>
    from rb_lm_limit_rela
    where CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectDela" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitRela" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitRela" >
    select <include refid="Base_Column_List"/>
    from rb_lm_limit_rela
    where LM_TYPE = #{lmType,jdbcType=VARCHAR}
    <if test="ctrlId != null">
      AND CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    </if>
    <if test="effectDate != null">
     <![CDATA[AND EFFECT_DATE <= #{effectDate,jdbcType=VARCHAR}]]>
    </if>
    <if test="lmTypeId != null">
      AND LM_TYPE_ID = #{lmTypeId,jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectLimitList" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitRela" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbLmLimitRela" >
    select <include refid="Base_Column_List"/>
    from rb_lm_limit_rela
    where LM_TYPE = #{lmType,jdbcType=VARCHAR}
    <if test="ctrlId != null">
      AND CTRL_ID = #{ctrlId,jdbcType=VARCHAR}
    </if>
    <if test="lmTypeId != null">
      AND LM_TYPE_ID = #{lmTypeId,jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY CTRL_ID ASC
  </select>
  <!--<delete id="deleteByPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbLmLimitRela">-->
    <!--delete from rb_lm_limit_rela-->
    <!--where LM_TYPE_ID = #{lmTypeId,jdbcType=VARCHAR}-->
    <!--<if test="lmType != null">-->
      <!--AND LM_TYPE = #{lmType,jdbcType=VARCHAR}-->
    <!--</if>-->
    <!--<if test="cvFlag != null">-->
      <!--AND CV_FLAG = #{cvFlag,jdbcType=VARCHAR}-->
    <!--</if>-->
  <!--</delete>-->

</mapper>
