<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
    <select id="selectByBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        select
        <include refid="Base_Column"/>
        from RB_BILL_REGISTER
        where DOC_TYPE = #{docType}
        AND BILL_NO = #{billNo}
    </select>

    <select id="selectByBillType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        select
        <include refid="Base_Column"/>
        from RB_BILL_REGISTER
        where BILL_NO = #{billNo}
        <if test="billType != null and billType != ''">
            and BILL_TYPE = #{billType}
        </if>
        <if test="docType != null and docType != ''">
            and DOC_TYPE = #{docType}
        </if>
    </select>


    <select id="selectMbPdRegisterList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_BILL_REGISTER
        WHERE 1=1
        <if test="branch != null and branch !=''">
            AND BILL_SIGN_BRANCH = #{branch}
        </if>
        <if test="billStatus != null and billStatus !=''">
            AND BILL_STATUS = #{billStatus}
        </if>
        <if test="startDate != null and startDate !=''">
            <![CDATA[AND LAST_TRAN_DATE >= #{startDate}]]>
        </if>
        <if test="endDate != null and endDate !=''">
            <![CDATA[AND LAST_TRAN_DATE <= #{endDate}]]>
        </if>
        <if test="billNo != null and billNo !=''">
            AND BILL_NO = #{billNo}
        </if>
        <if test="serialNo != null and serialNo !=''">
            AND SERIAL_NO = #{serialNo}
        </if>
    </select>


    <select id="selectRegisterByBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        select
        <include refid="Base_Column"/>
        from RB_BILL_REGISTER
        where 1=1
        <if test="billNo != null and billNo !=''">
            AND BILL_NO = #{billNo}
        </if>
        <if test="billStatus != null and billStatus !=''">
            AND BILL_STATUS = #{billStatus}
        </if>
    </select>

    <select id="getPromissoryNote" parameterType="java.util.Map" resultType="java.util.Map">
        select mptd.REFERENCE
        from RB_BILL_REGISTER mpr,MB_BILL_TRAN_DETAIL mptd
        where mpr.BILL_STATUS = #{billStatus}
        AND mpr.BILL_APPLY_DATE=#{billApplyDate}
        AND mpr.BILL_SIGN_BRANCH=#{billSignBranch}
        AND mpr.SERIAL_NO = mptd.ORIG_SERIAL_NO
    </select>

    <select id="selectMbPdRegisterListByBillNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_BILL_REGISTER
        WHERE 1=1
        <if test="billNo != null and billNo !=''">
            AND BILL_NO = #{billNo}
        </if>
        <if test="docClass != null and docClass !=''">
            AND DOC_CLASS = #{docClass}
        </if>
    </select>

    <select id="selectMbPdRegisterListBySerialNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_BILL_REGISTER
        WHERE 1=1
        <if test="serialNo != null and serialNo !=''">
            AND SERIAL_NO = #{serialNo}
        </if>
    </select>

    <select id="getUnApprBillList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <trim suffixOverrides="AND">
                <if test="billSignBranch != null and  billSignBranch != '' ">
                    BILL_SIGN_BRANCH = #{billSignBranch} AND
                </if>
                <if test="billSignUserId != null and  billSignUserId != '' ">
                    BILL_SIGN_USER_ID = #{billSignUserId} AND
                </if>
                <if test="billSignDate != null ">
                    BILL_SIGN_DATE = #{billSignDate} AND
                </if>
                <if test="billStatus != null and  billStatus != '' ">
                    BILL_STATUS = #{billStatus} AND
                </if>
            </trim>
        </where>
    </select>

    <select id="getMbPdRegisterList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_BILL_REGISTER
        WHERE 1=1
        <if test="billSignBranch != null and billSignBranch !=''">
            AND BILL_SIGN_BRANCH = #{billSignBranch}
        </if>
        <if test="billSignUserId != null and billSignUserId !=''">
            AND BILL_SIGN_USER_ID = #{billSignUserId}
        </if>
        <if test="billStatus != null and billStatus !=''">
            AND BILL_STATUS = #{billStatus}
        </if>
        <if test="billNo != null and billNo !=''">
            AND BILL_NO = #{billNo}
        </if>
        <if test="serialNo != null and serialNo !=''">
            AND SERIAL_NO = #{serialNo}
        </if>
        <if test="billType != null and billType !=''">
            AND BILL_TYPE = #{billType}
        </if>
        <if test="payerBaseAcctNo != null and payerBaseAcctNo !=''">
            AND PAYER_BASE_ACCT_NO = #{payerBaseAcctNo}
        </if>
        <if test="signBeginDate != null">
            <![CDATA[AND BILL_SIGN_DATE >= #{signBeginDate}]]>
        </if>
        <if test="signEndDate != null">
            <![CDATA[AND BILL_SIGN_DATE <= #{signEndDate}]]>
        </if>
        <if test="maturityBeginDate != null">
            <![CDATA[AND PROMPT_DATE >= #{maturityBeginDate}]]>
        </if>
        <if test="maturityEndDate != null">
            <![CDATA[AND PROMPT_DATE <= #{maturityEndDate}]]>
        </if>
        <if test="docType != null and docType !=''">
            AND DOC_TYPE = #{docType}
        </if>
        <if test="docClass != null and docClass !=''">
            AND DOC_CLASS = #{docClass}
        </if>
        <if test="billTranAmt != null and billTranAmt !=''">
            AND BILL_TRAN_AMT = #{billTranAmt}
        </if>
        <if test="agentFlag != null and agentFlag !=''">
            AND AGENT_FLAG = #{agentFlag}
        </if>
        <if test="branches != null and branches.size()>0">
            AND BILL_SIGN_BRANCH IN
            <foreach item="item" index="index" collection="branches" open="(" separator="," close=")">
                #{item.branch}
            </foreach>
        </if>
        <if test="expireFlag != null and expireFlag !=''">
            AND EXPIRE_FLAG = #{expireFlag}
        </if>
        <if test="paymentDate != null">
            <![CDATA[AND PAYMENT_DATE = #{paymentDate}]]>
        </if>
    </select>

    <update id="updMbPnRegisterByBillNo">
        update
        <include refid="Table_Name"/>
        <set>
            <if test="billStatus != null and billStatus != '' ">
                BILL_STATUS = #{billStatus},
            </if>
            <if test="lastTranDate != null ">
                LAST_TRAN_DATE = #{lastTranDate},
            </if>
        </set>
        <where>
            <if test="billNo != null and billNo != '' ">
                BILL_NO = #{billNo}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                And CLIENT_NO = #{clientNo}
            </if>
        </where>
    </update>

    <select id="getMbPdRegisterCount" parameterType="java.util.Map"
            resultType="int">
        SELECT
        count(1)
        FROM RB_BILL_REGISTER
        WHERE 1=1
        <if test="billSignBranch != null and billSignBranch !=''">
            AND BILL_SIGN_BRANCH = #{billSignBranch}
        </if>
        <if test="billSignUserId != null and billSignUserId !=''">
            AND BILL_SIGN_USER_ID = #{billSignUserId}
        </if>
        <if test="billStatus != null and billStatus !=''">
            AND BILL_STATUS = #{billStatus}
        </if>
        <if test="billNo != null and billNo !=''">
            AND BILL_NO = #{billNo}
        </if>
        <if test="serialNo != null and serialNo !=''">
            AND SERIAL_NO = #{serialNo}
        </if>
        <if test="billType != null and billType !=''">
            AND BILL_TYPE = #{billType}
        </if>
        <if test="payerBaseAcctNo != null and payerBaseAcctNo !=''">
            AND PAYER_BASE_ACCT_NO = #{payerBaseAcctNo}
        </if>
        <if test="signBeginDate != null">
            <![CDATA[AND BILL_SIGN_DATE >= #{signBeginDate}]]>
        </if>
        <if test="signEndDate != null">
            <![CDATA[AND BILL_SIGN_DATE <= #{signEndDate}]]>
        </if>
        <if test="maturityBeginDate != null">
            <![CDATA[AND PROMPT_DATE >= #{maturityBeginDate}]]>
        </if>
        <if test="maturityEndDate != null">
            <![CDATA[AND PROMPT_DATE <= #{maturityEndDate}]]>
        </if>
        <if test="docType != null and docType !=''">
            AND DOC_TYPE = #{docType}
        </if>
        <if test="docClass != null and docClass !=''">
            AND DOC_CLASS = #{docClass}
        </if>
        <if test="billTranAmt != null and billTranAmt !=''">
            AND BILL_TRAN_AMT = #{billTranAmt}
        </if>
        <if test="agentFlag != null and agentFlag !=''">
            AND AGENT_FLAG = #{agentFlag}
        </if>
        <if test="branches != null and branches.size()>0">
            AND BILL_SIGN_BRANCH IN
            <foreach item="item" index="index" collection="branches" open="(" separator="," close=")">
                #{item.branch}
            </foreach>
        </if>
    </select>

    <select id="queryOverdueBillList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            BILL_STATUS != '02'
            AND (EXPIRE_FLAG = #{expireFlag} OR EXPIRE_FLAG IS NULL)
        </where>
    </select>

    <select id="checkBillStatusByBranch" parameterType="java.util.Map"
            resultType="int">
        SELECT
        count(1)
        FROM
        <include refid="Table_Name"/>
        <where>
            BILL_SIGN_BRANCH = #{signbranch}
            AND BILL_STATUS NOT IN
            <foreach item="item" index="index" collection="billstatuses" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <!--<select id="sumBillAmt" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.MbProdAcctBal">
		select
		    a.BILL_SIGN_BRANCH as BRANCH,
		    a.CCY_SIGN as CCY,
		    a.DOC_CLASS as PROD_TYPE,
		    'ZHC' as STATUS,
		    'BAL' as AMT_TYPE,
		    '99' as PROFIT_CENTRE,
		    CAST(SUM(BILL_TRAN_AMT) AS decimal(30,2)) as BALANCE,
		    'RB' as SYSTEM_ID,
			'' as TERM
		from
		    RB_BILL_REGISTER a
		where
		    a.SERIAL_NO BETWEEN #{startKey} and #{endKey}
		AND
			a.BILL_STATUS in('00','01','04','05','11')
		group by
			a.BILL_SIGN_BRANCH,a.CCY_SIGN,a.DOC_CLASS
	</select>-->
</mapper>