<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTicketDetail">

	<update id="updateSum"  >
		UPDATE RB_ACCT_TICKET_DETAIL
		<set>
			<if test="intAccruedCtd != null ">
				INT_ACCRUED_CTD = #{intAccruedCtd},
			</if>
			<if test="intAccrued != null ">
				INT_ACCRUED = INT_ACCRUED+#{intAccrued},
			</if>
		</set>
			INTERNAL_KEY = #{internalKey}  AND TICKET_NO=#{ticketNo}
	</update>

	<select id="getRbAcctTicketDetail" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTicketDetail">
		SELECT *
		FROM  RB_ACCT_TICKET_DETAIL
		WHERE INTERNAL_KEY = #{internalKey}
	</select>

	<select id="getRbAcctTicketDetailByacct" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTicketDetail">
		SELECT *
		FROM  RB_ACCT_TICKET_DETAIL
		WHERE INTERNAL_KEY = #{internalKey}
		<if test="useDate !=  null ">
			AND EFFECT_DATE <![CDATA[ <= ]]>  #{useDate}  AND EXPIRY_DATE <![CDATA[ >= ]]> #{useDate}
		</if>
		<if test="ticketNo !=  null and ticketNo != ''">
			AND TICKET_NO = #{ticketNo}
		</if>
		<if test="ticketType !=  null and ticketType != ''">
			AND TICKET_TYPE = #{ticketType}
		</if>
	</select>

	<select id="getRbAcctTicketDetailByticket" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTicketDetail">
		SELECT *
		FROM  RB_ACCT_TICKET_DETAIL
		WHERE TICKET_NO = #{ticketNo}
		<if test="useDate !=  null ">
			AND EFFECT_DATE <![CDATA[ <= ]]>  #{useDate}  AND EXPIRY_DATE <![CDATA[ >= ]]> #{useDate}
		</if>
		<if test="internalKey !=  null and internalKey != ''">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="ticketType !=  null and ticketType != ''">
			AND TICKET_TYPE = #{ticketType}
		</if>
	</select>

	<select id="getRbAcctTicketDetailInt" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTicketDetail">
		SELECT *
		FROM  RB_ACCT_TICKET_DETAIL
		WHERE INTERNAL_KEY = #{internalKey}
		<if test="accrDate !=  null ">
			AND EFFECT_DATE <![CDATA[ <= ]]>  #{accrDate}  AND EXPIRY_DATE <![CDATA[ >= ]]> #{accrDate}
		</if>
		<if test="ticketType !=  null and ticketType != ''">
			AND TICKET_TYPE = #{ticketType}
		</if>
		<if test="agreementId !=  null and agreementId != ''">
			AND AGREEMENT_ID = #{agreementId}
		</if>
	</select>

	<select id="getRbAcctTicketDetailNote" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTicketDetail">
		SELECT *
		FROM  RB_ACCT_TICKET_DETAIL
		WHERE INTERNAL_KEY = #{internalKey}
		<if test="accrDate !=  null ">
			AND EFFECT_DATE <![CDATA[ <= ]]>  #{accrDate}  AND EXPIRY_DATE <![CDATA[ >= ]]> #{accrDate}
		</if>
		<if test="ticketType !=  null and ticketType != ''">
			AND TICKET_TYPE = #{ticketType}
		</if>
		<if test="agreementId !=  null and agreementId != ''">
			AND AGREEMENT_ID = #{agreementId}
		</if>
	</select>
</mapper>