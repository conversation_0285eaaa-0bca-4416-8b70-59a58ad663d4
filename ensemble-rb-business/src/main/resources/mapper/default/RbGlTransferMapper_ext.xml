<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer">
    select <include refid="Base_Column"/>
    from RB_GL_TRANSFER
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByReference" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer">
    select <include refid="Base_Column"/>
    from RB_GL_TRANSFER
      where REFERENCE = #{reference} and REVERSAL_FLAG != 'Y'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer">
    delete from RB_GL_TRANSFER
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer">
    update RB_GL_TRANSFER
    <set>
      <if test="glClientNo != null">
        GL_CLIENT_NO = #{glClientNo},
      </if>
      <if test="glType != null">
        GL_TYPE = #{glType},
      </if>
      <if test="glCode != null">
        GL_CODE = #{glCode},
      </if>
      <if test="glCcy != null">
        GL_CCY = #{glCcy},
      </if>
      <if test="glSeqNo != null">
        GL_SEQ_NO = #{glSeqNo},
      </if>
      <if test="glProfitCenter != null">
        GL_PROFIT_CENTRE = #{glProfitCenter},
      </if>
      <if test="glBranch != null">
        GL_BRANCH = #{glBranch},
      </if>
      <if test="glBaseAcctNo != null">
        GL_BASE_ACCT_NO = #{glBaseAcctNo},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="valueDate != null">
        VALUE_DATE = #{valueDate},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="narrative != null">
        NARRATIVE = #{narrative},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO = #{bankSeqNo},
      </if>
      <if test="reversal != null">
        REVERSAL_FLAG = #{reversal},
      </if>
      <if test="reversalTranType != null">
        REVERSAL_TRAN_TYPE = #{reversalTranType},
      </if>
      <if test="reversalDate != null">
        REVERSAL_DATE = #{reversalDate},
      </if>
      <if test="reversalUserId != null">
        REVERSAL_USER_ID = #{reversalUserId},
      </if>
      <if test="reversalAuthUserId != null">
        REVERSAL_AUTH_USER_ID = #{reversalAuthUserId},
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO = #{tranSeqNo},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG = #{glPostedFlag},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO = #{channelSeqNo}
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer">
    insert into RB_GL_TRANSFER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="glClientNo != null">
        GL_CLIENT_NO,
      </if>
      <if test="glType != null">
        GL_TYPE,
      </if>
      <if test="glCode != null">
        GL_CODE,
      </if>
      <if test="glCcy != null">
        GL_CCY,
      </if>
      <if test="glSeqNo != null">
        GL_SEQ_NO,
      </if>
      <if test="glProfitCenter != null">
        GL_PROFIT_CENTRE,
      </if>
      <if test="glBranch != null">
        GL_BRANCH,
      </if>
      <if test="glBaseAcctNo != null">
        GL_BASE_ACCT_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="valueDate != null">
        VALUE_DATE,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="narrative != null">
        NARRATIVE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO,
      </if>
      <if test="reversal != null">
        REVERSAL_FLAG,
      </if>
      <if test="reversalTranType != null">
        REVERSAL_TRAN_TYPE,
      </if>
      <if test="reversalDate != null">
        REVERSAL_DATE,
      </if>
      <if test="reversalUserId != null">
        REVERSAL_USER_ID,
      </if>
      <if test="reversalAuthUserId != null">
        REVERSAL_AUTH_USER_ID,
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO,
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="glClientNo != null">
        #{glClientNo},
      </if>
      <if test="glType != null">
        #{glType},
      </if>
      <if test="glCode != null">
        #{glCode},
      </if>
      <if test="glCcy != null">
        #{glCcy},
      </if>
      <if test="glSeqNo != null">
        #{glSeqNo},
      </if>
      <if test="glProfitCenter != null">
        #{glProfitCenter},
      </if>
      <if test="glBranch != null">
        #{glBranch},
      </if>
      <if test="glBaseAcctNo != null">
        #{glBaseAcctNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="valueDate != null">
        #{valueDate},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="narrative != null">
        #{narrative},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="bankSeqNo != null">
        #{bankSeqNo},
      </if>
      <if test="reversal != null">
        #{reversal},
      </if>
      <if test="reversalTranType != null">
        #{reversalTranType},
      </if>
      <if test="reversalDate != null">
        #{reversalDate},
      </if>
      <if test="reversalUserId != null">
        #{reversalUserId},
      </if>
      <if test="reversalAuthUserId != null">
        #{reversalAuthUserId},
      </if>
      <if test="tranSeqNo != null">
        #{tranSeqNo},
      </if>
      <if test="glPostedFlag != null">
        #{glPostedFlag},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
    <select id="selectMbGlTransferSplitOB" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(SEQ_NO) START_KEY,
            MAX(SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
            FROM
            (
            SELECT
            SEQ_NO,
            @rownum :=@rownum + 1 AS rownum
            FROM (SELECT
            DISTINCT SEQ_NO FROM RB_GL_TRANSFER,
            (SELECT @rownum := -1) t
            where tran_date =  #{runDate}
          <!-- 多法人改造 by luocwa -->
          <if test="company != null and company != '' ">
            AND COMPANY = #{company}
          </if>
            AND (gl_posted = 'N' or gl_posted is null))t1
            ORDER BY SEQ_NO
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (SEQ_NO) START_KEY, MAX (SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
            FROM (SELECT DISTINCT SEQ_NO
            from RB_GL_TRANSFER
            where tran_date =  #{runDate}
          <!-- 多法人改造 by luocwa -->
          <if test="company != null and company != '' ">
            AND COMPANY = #{company}
          </if>
            AND (gl_posted = 'N' or gl_posted is null)
            ORDER BY SEQ_NO)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>
    <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlTransfer" >
      update RB_GL_TRANSFER set gl_posted='Y'
      where SEQ_NO = #{seqNo}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </update>
</mapper>
