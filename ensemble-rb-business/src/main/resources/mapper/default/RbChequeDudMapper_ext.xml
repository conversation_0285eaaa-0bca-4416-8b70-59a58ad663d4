<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDud">

    <select id="quiryByClientNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDud">
        select
        <include refid="Base_Column"/>
        from RB_CHEQUE_DUD
        where CLIENT_NO=#{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY tran_date DESC, dud_seq_no+0 DESC
    </select>
    <select id="quiryByBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDud">
        select
        <include refid="Base_Column"/>
        from RB_CHEQUE_DUD
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="clientNo!=null">
            AND CLIENT_NO=#{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY tran_date DESC, dud_seq_no+0 DESC
    </select>

    <select id="quiryByVoucher" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDud">
        select
        <include refid="Base_Column"/>
        from RB_CHEQUE_DUD
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="prodType!=null and prodType!= ''">
            AND PROD_TYPE=#{prodType}
        </if>
        <if test="acctSeqNo!=null and acctSeqNo!= ''">
            AND ACCT_SEQ_NO=#{acctSeqNo}
        </if>
        <if test="voucherNo!=null and voucherNo!= ''">
            AND VOUCHER_NO=#{voucherNo}
        </if>
        <if test="clientNo!=null and clientNo!= ''">
            AND CLIENT_NO=#{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY TRAN_DATE ASC
    </select>
</mapper>
