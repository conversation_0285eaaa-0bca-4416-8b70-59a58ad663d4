<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctVerifyBatch">
    <select id="getRbAcctVerifys" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctVerifyBatch" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctVerifyBatch">
        select <include refid="Base_Column"/>
        from RB_ACCT_VERIFY_BATCH
        <where>
            <trim suffixOverrides="AND">
                <if test="internalKey != null">
                    INTERNAL_KEY = #{internalKey} AND
                </if>
                <if test="verifyAcctListType != null and verifyAcctListType != '' ">
                    VERIFY_ACCT_LIST_TYPE = #{verifyAcctListType} AND
                </if>
                <if test="checkStatus != null and checkStatus != '' ">
                    CHECK_STATUS = #{checkStatus} AND
                </if>
                <if test="tranDate != null">
                    TRAN_DATE = #{tranDate} AND
                </if>
                RESTRAINT_TYPE IS NULL
            </trim>
        </where>
    </select>
</mapper>