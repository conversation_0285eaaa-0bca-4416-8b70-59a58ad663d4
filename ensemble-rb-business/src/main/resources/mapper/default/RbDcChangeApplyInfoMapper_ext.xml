<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcChangeApplyInfo">

    <sql id="Base_Column_a">
        <trim suffixOverrides=",">
            a.TRF_NO,
            a.STAGE_CODE,
            a.BASE_ACCT_NO,
            a.ACCT_SEQ_NO,
            a.TRF_IN_FEE_AMT,
            a.TRAN_TIMESTAMP,
            a.RES_SEQ_NO,
            a.INTERNAL_KEY,
            a.ORDER_END_DATE,
            a.TRF_RATE,
            a.TRF_PRI_AMT,
            a.TRF_TOTAL_SETTLE_AMT,
            a.TRF_TYPE,
            a.TRF_STATUS,
            a.TRF_DATE,
            a.TRAN_DATE,
            a.TRF_END_DATE,
            a.DIRECTION_TRF_FLAG,
            a.BENEFICIARY_CLIENT_NO,
            a.BENEFICIARY_PROFIT_RATE,
            a.CLIENT_NO,
            a.USER_ID,
            a.TRF_OUT_FEE_AMT,
            a.COMPANY,
            a.ORDER_START_DATE,
            a.DEP_KEEP_DAYS,
            a.INT_REM_DAYS,
            a.CLIENT_NAME,
            a.LAST_CHANGE_DATE,
            a.TRF_COMMAND,
            a.SETTLE_BASE_ACCT_NO,
            a.SETTLE_ACCT_SEQ_NO,
            a.PROD_TYPE,
        </trim>
    </sql>
    <select id="selectDcChangeApplyRegisterList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcChangeApplyInfo">
        select * from ( select <include refid="Base_Column_a"/>, (trf_pri_amt +trf_pri_amt * trf_rate * dep_keep_days / 36000) TRF_AMT
        from RB_DC_CHANGE_APPLY_INFO a
        <where>
        <if test="trfStatus != null and trfStatus != ''">
            <if test='trfStatus != "H"'>
                AND a.TRF_STATUS = #{trfStatus}
            </if>
            <if test='trfStatus == "H"'>
                AND (a.TRF_STATUS = 'O' OR a.TRF_STATUS = 'P')
            </if>
        </if>
        <if test="directionTrfFlag != null and directionTrfFlag != ''">
            AND a.DIRECTION_TRF_FLAG = #{directionTrfFlag}
        </if>
        <if test="trfNo != null and trfNo != ''">
            AND a.TRF_NO = #{trfNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND a.BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND a.CLIENT_NO = #{clientNo}
        </if>
        <if test="settleBaseAcctNo != null">
            AND a.SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo}
        </if>
        <if test="settleAcctSeqNo != null">
            AND a.SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo}
        </if>
        <if test="prodType != null">
            AND a.PROD_TYPE = #{prodType}
        </if>
        <if test="trfCommand != null and trfCommand != ''">
            AND a.TRF_COMMAND = #{trfCommand}
        </if>
        <if test="beneficiaryClientNo != null and beneficiaryClientNo != '' ">
            AND a.BENEFICIARY_CLIENT_NO = #{beneficiaryClientNo}
        </if>
        <if test="orderStartDate != null  ">
            AND a.ORDER_START_DATE <![CDATA[ >= ]]> #{orderStartDate}
        </if>
        <if test="orderEndDate != null ">
            AND a.ORDER_START_DATE <![CDATA[ <= ]]> #{orderEndDate}
        </if>
        <!--企业网银改造-->
        <if test="trfPriAmtGreaterThanEqual != null and trfPriAmtGreaterThanEqual != ''  ">
            AND a.TRF_PRI_AMT <![CDATA[ >= ]]> #{trfPriAmtGreaterThanEqual}
        </if>
        <if test="trfPriAmtLessThanEqual != null and trfPriAmtLessThanEqual != ''  ">
            AND a.TRF_PRI_AMT <![CDATA[ <= ]]> #{trfPriAmtLessThanEqual}
        </if>
        <if test="trfEndDateGreaterThanEqual != null and trfEndDateGreaterThanEqual != ''  ">
            AND a.TRF_END_DATE <![CDATA[ >= ]]> #{trfEndDateGreaterThanEqual}
        </if>
        <if test="trfEndDateLessThanEqual != null and trfEndDateLessThanEqual != ''  ">
            AND a.TRF_END_DATE <![CDATA[ <= ]]> #{trfEndDateLessThanEqual}
        </if>
        <!--个人网银改造-->
        <if test="beneficiaryProfitRateGreaterThanEqual != null and beneficiaryProfitRateGreaterThanEqual != ''  ">
            AND a.BENEFICIARY_PROFIT_RATE <![CDATA[ >= ]]> #{beneficiaryProfitRateGreaterThanEqual}
        </if>
        <if test="beneficiaryProfitRateLessThanEqual != null and beneficiaryProfitRateLessThanEqual != ''  ">
            AND a.BENEFICIARY_PROFIT_RATE <![CDATA[ <= ]]> #{beneficiaryProfitRateLessThanEqual}
        </if>
        <if test="intRemDaysGreaterThanEqual != null and intRemDaysGreaterThanEqual != ''  ">
            AND a.INT_REM_DAYS <![CDATA[ >= ]]> #{intRemDaysGreaterThanEqual}
        </if>
        <if test="intRemDaysLessThanEqual != null and intRemDaysLessThanEqual != ''  ">
            AND a.INT_REM_DAYS <![CDATA[ <= ]]> #{intRemDaysLessThanEqual}
        </if>
        </where>
        ) t
        <where>
            t.internal_key in (select internal_key
            from rb_acct r
            <where>
                <if test="branchList != null and branchList.size() > 0">
                    and r.acct_branch in
                    <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
                        #{branch}
                    </foreach>
                </if>
            </where>
            )
            <if test="trfAmtGreaterThanEqual != null and trfAmtGreaterThanEqual != ''  ">
                AND t.TRF_AMT <![CDATA[ >= ]]> #{trfAmtGreaterThanEqual}
            </if>
            <if test="trfAmtLessThanEqual != null and trfAmtLessThanEqual != ''  ">
                AND t.TRF_AMT <![CDATA[ <= ]]> #{trfAmtLessThanEqual}
            </if>
        </where>
        <if test="sortCode != null and sortCode != ''">
            <trim prefix="order by" suffixOverrides=",">
                <if test="sortCode.contains('00'.toString())">
                    <!--根据年化利率降序排序-->
                    t.BENEFICIARY_PROFIT_RATE desc,
                </if>
                <if test="sortCode.contains('01'.toString())">
                    <!--根据年化利率升序排序-->
                    t.BENEFICIARY_PROFIT_RATE asc,
                </if>
                <if test="sortCode.contains('10'.toString())">
                    <!--根据转让价格降序排序-->
                    t.TRF_TOTAL_SETTLE_AMT desc,
                </if>
                <if test="sortCode.contains('11'.toString())">
                    <!--根据转让价格升序排序-->
                    t.TRF_TOTAL_SETTLE_AMT asc,
                </if>
                <if test="sortCode.contains('20'.toString())">
                    <!--根据剩余期限降序排序-->
                    t.INT_REM_DAYS desc,
                </if>
                <if test="sortCode.contains('21'.toString())">
                    <!--根据剩余期限升序排序-->
                    t.INT_REM_DAYS asc,
                </if>
                <if test="sortCode.contains('30'.toString())">
                    <!--根据转让状态降序排序-->
                    t.TRF_STATUS desc,
                </if>
                <if test="sortCode.contains('31'.toString())">
                    <!--根据转让状态升序排序-->
                    t.TRF_STATUS asc,
                </if>
                <if test="sortCode.contains('40'.toString())">
                    <!--根据转让日期降序排序-->
                    t.TRF_DATE desc,
                </if>
                <if test="sortCode.contains('41'.toString())">
                    <!--根据转让日期升序排序-->
                    t.TRF_DATE asc,
                </if>
            </trim>
        </if>
        <if test="sortCode == null or sortCode == ''">
            order by t.BENEFICIARY_PROFIT_RATE desc,t.ORDER_END_DATE desc
        </if>
    </select>

    <select id="getDcChangeApplyInfoByStatusP" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcChangeApplyInfo">
    select <include refid="Base_Column"/>
    from RB_DC_CHANGE_APPLY_INFO
    where 1=1
    <if test="trfStatus != null and trfStatus != ''">
        AND TRF_STATUS = #{trfStatus}
    </if>
    <if test="baseAcctNo != null and baseAcctNo != ''">
        AND BASE_ACCT_NO like CONCAT('%',CONCAT(#{baseAcctNo},'%'))
    </if>
    <if test="clientNo != null and clientNo != ''">
        AND CLIENT_NO like CONCAT('%',CONCAT(#{clientNo},'%'))
    </if>
    ORDER BY TRF_DATE DESC
    </select>

    <select id="getDcChangeInnerAcctInfoByStatusP" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcChangeApplyInfo">
        select <include refid="Base_Column"/>
        from RB_DC_CHANGE_APPLY_INFO
        where 1=1
        <if test="trfStatus != null and trfStatus != ''">
            AND TRF_STATUS = #{trfStatus}
        </if>
        <if test="innerBaseAcctNo != null and innerBaseAcctNo != ''">
            AND INNER_BASE_ACCT_NO like CONCAT('%',CONCAT(#{innerBaseAcctNo},'%'))
        </if>
        <if test="beneficiaryClientNo != null and beneficiaryClientNo != ''">
            AND BENEFICIARY_CLIENT_NO like CONCAT('%',CONCAT(#{beneficiaryClientNo},'%'))
        </if>
        ORDER BY TRF_DATE DESC
    </select>

</mapper>