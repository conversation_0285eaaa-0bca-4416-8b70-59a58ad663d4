<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcParameter">
  <!-- Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/05/14 10:49:51. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcParameter" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcParameter">
    select *
    from OC_PARAMETER
    where PARA_KEY = #{paraKey}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcParameter">
    delete from OC_PARAMETER
    where PARA_KEY = #{paraKey}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcParameter">
    update OC_PARAMETER
    <set>
      <if test="paraValue != null">
        PARA_VALUE = #{paraValue},
      </if>
      <if test="paraDesc != null">
        PARA_DESC = #{paraDesc},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where PARA_KEY = #{paraKey}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcParameter">
    insert into OC_PARAMETER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paraKey != null">
        PARA_KEY,
      </if>
      <if test="paraValue != null">
        PARA_VALUE,
      </if>
      <if test="paraDesc != null">
        PARA_DESC,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="paraKey != null">
        #{paraKey},
      </if>
      <if test="paraValue != null">
        #{paraValue},
      </if>
      <if test="paraDesc != null">
        #{paraDesc},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <select id="getFmParamterValue" parameterType="String"
          resultType="String">
    SELECT para_value
    FROM oc_parameter
    where para_key = #{paraKey}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
