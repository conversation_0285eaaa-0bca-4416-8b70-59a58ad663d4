<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftReg">

    <select id="getRbOverdraftRegs" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftReg"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column"/>
        from rb_overdraft_reg
        <where>
            overdraft_outstanding > 0
            <if test="internalKey != null ">
                and internal_key = #{internalKey}
            </if>
            <if test="clientNo != null and clientNo != ''">
                and client_no = #{clientNo}
            </if>
            <if test="tranDate != null ">
                and tran_date = #{tranDate}
            </if>
            <!-- Multi-legal transformation by luocwa -->
            <if test="company != null and company != ''">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>


</mapper>
