<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.bc.unit.cm.backupClean.SegmentService">
    <select id="isPartitionTable" parameterType="map" resultType="string">
        SELECT TABLE_NAME FROM USER_TABLES WHERE PARTITIONED = 'YES' AND TABLE_NAME = #{tableName}
    </select>

    <select id="getPartitionInfo" parameterType="map" resultType="map">
        SELECT PARTITION_NAME PARTITIONNAME,HIGH_VALUE HIGHVALUE FROM USER_TAB_PARTITIONS WHERE TABLE_NAME = #{tableName}
    </select>

    <select id="selectSegment" parameterType="java.util.Map" resultType="java.util.Map" useCache="false">
        select
        <include refid="comet_step_column"/>
        from ${cometTableName}
        <if test="partitionName != null"> PARTITION (${partitionName})
        </if>
        where 1=1
        <include refid="comet_step_where"/>
    </select>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'">${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'">*</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null "> and ${cometKeyField} between #{cometStart} and #{cometEnd} </if>
    </sql>
</mapper>
