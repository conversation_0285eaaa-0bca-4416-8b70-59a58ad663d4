<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl">
  <!-- Created by wuszb on 2017/03/22 22:20:14. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl">
    select *
    from RB_OPEN_CTL
    where CLIENT_TYPE = #{clientType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByClientType" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl">
    select *
    from RB_OPEN_CTL
    where CLIENT_TYPE = #{clientType}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl">
    delete from RB_OPEN_CTL
    where CLIENT_TYPE = #{clientType}
    <if test="ctrlAttr != null">
      AND CTRL_ATTR = #{ctrlAttr}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl">
    update RB_OPEN_CTL
    <set>
      <if test="ctrlValue != null">
        CTRL_VALUE = #{ctrlValue},
      </if>
      <if test="quantity != null">
        QUANTITY = #{quantity},
      </if>
      <if test="dealFlow != null">
        DEAL_FLOW = #{dealFlow},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="company != null">
        COMPANY = #{company}
      </if>
    </set>
    where CLIENT_TYPE = #{clientType}
        AND CTRL_ATTR = #{ctrlAttr}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl">
    insert into RB_OPEN_CTL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="ctrlAttr != null">
        CTRL_ATTR,
      </if>
      <if test="ctrlValue != null">
        CTRL_VALUE,
      </if>
      <if test="quantity != null">
        QUANTITY,
      </if>
      <if test="dealFlow != null">
        DEAL_FLOW,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="ctrlAttr != null">
        #{ctrlAttr},
      </if>
      <if test="ctrlValue != null">
        #{ctrlValue},
      </if>
      <if test="quantity != null">
        #{quantity},
      </if>
      <if test="dealFlow != null">
        #{dealFlow},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>
</mapper>
