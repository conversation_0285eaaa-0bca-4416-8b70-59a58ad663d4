<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">

  <select id="selectByStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    SELECT <include refid="Base_Column"/>
    FROM RB_batch_tran a
    WHERE a.batch_status = 'P'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getByPrimaryKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_batch_tran
    where batch_no = #{batchNo,jdbcType=VARCHAR}
    <if test="openClass != null">
      and batch_class = #{batchClass,jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getBatchTrans" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_batch_tran
    <where>
    <if test="batchNo != null and batchNo.length() > 0">
      and batch_no = #{batchNo,jdbcType=VARCHAR}
    </if>
    <if test="fileType != null and fileType.length() > 0" >
      and batch_class = #{fileType,jdbcType=VARCHAR}
    </if>
    <if test="beginDate != null and beginDate.length() > 0">
      and tran_date between #{beginDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
    </if>
    <!-- Batch query new additions -->
    <if test="batchClass != null and batchClass.length() > 0">
      and batch_class = #{batchClass,jdbcType=VARCHAR}
    </if>
    <if test="branchId != null and branchId.length() > 0">
      and branch_Id = #{branchId,jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="getFileName" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_batch_tran
    where file_name= #{fileName,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByBatchClass1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    SELECT <include refid="Base_Column"/>
    FROM RB_batch_tran a
    WHERE a.batch_status = 'Z'
    AND batch_class  = #{batchClass,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY BEGIN_TIME ASC
  </select>
  <select id="getMbBatchTransForJob" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select
    <include refid="Base_Column"/>
    from RB_batch_tran
    where batch_no between #{startKey,jdbcType=VARCHAR} and #{endKey,jdbcType=VARCHAR}
    and batch_status IN
    <foreach item="item" index="index" collection="batchStatus" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <update id="updateBatchStatus" parameterType="java.util.Map">
    update RB_BATCH_TRAN
    <set>
      <if test="batchStatus != null and batchStatus !='' ">
        BATCH_STATUS = #{batchStatus,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null and filePath !='' ">
        FILE_PATH = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null  ">
        TOTAL_NUM = #{totalNum,jdbcType=VARCHAR},
      </if>
      <if test="succNum != null  ">
        SUCC_NUM = #{succNum,jdbcType=VARCHAR},
      </if>
      <if test="failureNumber != null ">
        FAILURE_NUMBER = #{failureNumber,jdbcType=VARCHAR},
      </if>
      <if test="errorDesc != null">
        ERROR_DESC = #{errorDesc, jdbcType = VARCHAR}
      </if>
    </set>
     <where>
       <if test="batchNo != null and batchNo !='' ">
         AND BATCH_NO = #{batchNo,jdbcType=VARCHAR}
       </if>
       <!-- 多法人改造 by luocwa -->
       <if test="company != null and company != '' ">
         AND COMPANY = #{company}
       </if>
     </where>
  </update>
  <update id="updateBatchStatusByNarrative" parameterType="java.util.Map">
    update RB_BATCH_TRAN
    <set>
      <if test="batchStatus != null and batchStatus !='' ">
        BATCH_STATUS = #{batchStatus,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null  ">
        TOTAL_NUM = #{totalNum,jdbcType=VARCHAR},
      </if>
      <if test="succNum != null  ">
        SUCC_NUM = #{succNum,jdbcType=VARCHAR},
      </if>
      <if test="failureNumber != null ">
        FAILURE_NUMBER = #{failureNumber,jdbcType=VARCHAR},
      </if>
      <if test="errorDesc != null  and errorDesc !=''">
        ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},
      </if>
    </set>
     where NARRATIVE = #{narrative,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getBatchTransByRef" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRAN
    where REFERENCE= #{ref,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectCompanyBatchNo" parameterType="java.util.Map" resultType="java.lang.String">
    select COMPANY from RB_BATCH_TRAN where BATCH_NO = #{contrastBatNo,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getBatchNoByApprovalNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRAN
    where APPROVAL_NO = #{approvalNo,jdbcType=VARCHAR}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectSumInfoForBatchOpen" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    SELECT distinct
        ( SELECT count( * ) TOTAL_NUM FROM rb_batch_open_details WHERE BATCH_NO = #{batchNo,jdbcType=VARCHAR}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        ) TOTAL_NUM,
        ( SELECT count( * ) FAIL_NUM FROM rb_batch_open_details WHERE BATCH_NO = #{batchNo,jdbcType=VARCHAR} AND TRAN_STATUS = 'F'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        ) FAIL_NUM,
        ( SELECT count( * ) SUCC_NUM FROM rb_batch_open_details WHERE BATCH_NO = #{batchNo,jdbcType=VARCHAR} AND TRAN_STATUS = 'S'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        ) SUCC_NUM
    FROM
        DUAL
  </select>

  <select id="selectSumInfoForBatchDctOpen" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    SELECT distinct
        ( SELECT count( * ) TOTAL_NUM FROM rb_batch_open_dct_details WHERE BATCH_NO = #{batchNo,jdbcType=VARCHAR}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        ) TOTAL_NUM,
        ( SELECT count( * ) FAIL_NUM FROM rb_batch_open_dct_details WHERE BATCH_NO = #{batchNo,jdbcType=VARCHAR} AND STATUS = 'F'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
        ) FAIL_NUM,
        ( SELECT count( * ) SUCC_NUM FROM rb_batch_open_dct_details WHERE BATCH_NO = #{batchNo,jdbcType=VARCHAR} AND STATUS = 'S'
          <!-- 多法人改造 by luocwa -->
          <if test="company != null and company != '' ">
            AND COMPANY = #{company}
          </if>
        ) SUCC_NUM
    FROM
        DUAL
  </select>

  <select id="getBatchTranByBatchNos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRAN
    WHERE BATCH_NO in
    <foreach collection="batchNos" open="(" separator="," close=")" item="batchNo">
        #{batchNo}
    </foreach>
  </select>

  <select id="getBatchNoByApprovalNoAndBatchClass" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRAN
    where APPROVAL_NO = #{approvalNo,jdbcType=VARCHAR}
    <if test="batchClass != null and batchClass.length() > 0">
      AND BATCH_CLASS = #{batchClass}
    </if>
    <!-- Multi-legal entity transformation -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getBatchByBatchNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTran">
    select <include refid="Base_Column"/>
    from RB_BATCH_TRAN
    where BATCH_NO = #{batchNo}
  </select>
</mapper>
