<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
  <!-- Created by admin on 2017/09/27 10:02:00. -->
  <sql id="Base_Column_List">
    INTERNAL_KEY,
    USER_ID,
    BRANCH,
    ENTRUST_NO,
    SEND_RECEIVE_FLAG,
    ENTRUST_TYPE,
    ENTRUST_AMT,
    CCY,
    PAY_ACCT_NO,
    PAY_ACCT_SEQ_NO,
    PAY_PROD_TYPE,
    PAY_ACCT_NAME,
    PAY_BRANCH,
    ACCEPT_ACCT_NO,
    ACCEPT_ACCT_SEQ_NO,
    ACCEPT_PROD_TYPE,
    ACCEPT_ACCT_NAME,
    ACCEPT_<PERSON>ANCH,
    OPEN_NUMBER,
    OPEN_NAME,
    ENTRUST_STATUS,
    PAPER_TYPE,
    PAPER_NUMBER,
    PAPER_DUE_DATE,
    COPIES_NUM,
    HINT_BACKOUT_DATE,
    EMS_NO,
    REMARK,
    PAY_TYPE,
    BACKOUT_DATE,
    DELIVERY_AMT,
    REFUSE_REASON,
    BATCH,
    REGISTER_DATE,
    TRAN_TIMESTAMP,
    PAY_NAME,
    ACCEPT_NAME
  </sql>
  <select id="getByEntrustNo" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
    select <include refid="Base_Column_List"/> from MB_ENTRUST_COLLECT
    where ENTRUST_NO = #{baseParam.entrustNo}
    <if test="baseParam.eventType == 'ENTRUSTCANCEL'">
      and ENTRUST_STATUS = #{baseParam.entrustStatus}
    </if>
    order by INTERNAL_KEY desc
  </select>

  <select id="getByRegisterDate" parameterType="java.util.Map"
          resultType="java.lang.String">
    select ENTRUST_NO from MB_ENTRUST_COLLECT where REGISTER_DATE = #{registerDate}
    order by INTERNAL_KEY desc
  </select>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
    select *
    from MB_ENTRUST_COLLECT
    where INTERNAL_KEY = #{internalKey}
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
    delete from MB_ENTRUST_COLLECT
    where INTERNAL_KEY = #{internalKey}
  </delete>
  <update id="updateByEntrustNo" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
    update MB_ENTRUST_COLLECT
    <set>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="branch != null">
        BRANCH = #{branch},
      </if>
      <if test="sendReceiveFlag != null">
        SEND_RECEIVE_FLAG = #{sendReceiveFlag},
      </if>
      <if test="entrustType != null">
        ENTRUST_TYPE = #{entrustType},
      </if>
      <if test="entrustAmt != null">
        ENTRUST_AMT = #{entrustAmt},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="payAcctNo != null">
        PAY_ACCT_NO = #{payAcctNo},
      </if>
      <if test="payAcctSeqNo != null">
        PAY_ACCT_SEQ_NO = #{payAcctSeqNo},
      </if>
      <if test="payProdType != null">
        PAY_PROD_TYPE = #{payProdType},
      </if>
      <if test="payAcctName != null">
        PAY_ACCT_NAME = #{payAcctName},
      </if>
      <if test="payBranch != null">
        PAY_BRANCH = #{payBranch},
      </if>
      <if test="acceptAcctNo != null">
        ACCEPT_ACCT_NO = #{acceptAcctNo},
      </if>
      <if test="acceptAcctSeqNo != null">
        ACCEPT_ACCT_SEQ_NO = #{acceptAcctSeqNo},
      </if>
      <if test="acceptProdType != null">
        ACCEPT_PROD_TYPE = #{acceptProdType},
      </if>
      <if test="acceptAcctName != null">
        ACCEPT_ACCT_NAME = #{acceptAcctName},
      </if>
      <if test="acceptBranch != null">
        ACCEPT_BRANCH = #{acceptBranch},
      </if>
      <if test="openNumber != null">
        OPEN_NUMBER = #{openNumber},
      </if>
      <if test="openName != null">
        OPEN_NAME = #{openName},
      </if>
      <if test="entrustStatus != null">
        ENTRUST_STATUS = #{entrustStatus},
      </if>
      <if test="paperType != null">
        PAPER_TYPE = #{paperType},
      </if>
      <if test="paperNumber != null">
        PAPER_NUMBER = #{paperNumber},
      </if>
      <if test="paperDueDate != null">
        PAPER_DUE_DATE = #{paperDueDate},
      </if>
      <if test="copiesNum != null">
        COPIES_NUM = #{copiesNum},
      </if>
      <if test="hintBackoutDate != null">
        HINT_BACKOUT_DATE = #{hintBackoutDate},
      </if>
      <if test="emsNo != null">
        EMS_NO = #{emsNo},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="payType != null">
        PAY_TYPE = #{payType},
      </if>
      <if test="backoutDate != null">
        BACKOUT_DATE = #{backoutDate},
      </if>
      <if test="deliveryAmt != null">
        DELIVERY_AMT = #{deliveryAmt},
      </if>
      <if test="refuseReason != null">
        REFUSE_REASON = #{refuseReason},
      </if>
      <if test="batch != null">
        BATCH = #{batch},
      </if>
      <if test="registerDate != null">
        REGISTER_DATE = #{registerDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where ENTRUST_NO = #{entrustNo}
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
    insert into MB_ENTRUST_COLLECT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="branch != null">
        BRANCH,
      </if>
      <if test="entrustNo != null">
        ENTRUST_NO,
      </if>
      <if test="sendReceiveFlag != null">
        SEND_RECEIVE_FLAG,
      </if>
      <if test="entrustType != null">
        ENTRUST_TYPE,
      </if>
      <if test="entrustAmt != null">
        ENTRUST_AMT,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="payAcctNo != null">
        PAY_ACCT_NO,
      </if>
      <if test="payAcctSeqNo != null">
        PAY_ACCT_SEQ_NO,
      </if>
      <if test="payProdType != null">
        PAY_PROD_TYPE,
      </if>
      <if test="payAcctName != null">
        PAY_ACCT_NAME,
      </if>
      <if test="payBranch != null">
        PAY_BRANCH,
      </if>
      <if test="acceptAcctNo != null">
        ACCEPT_ACCT_NO,
      </if>
      <if test="acceptAcctSeqNo != null">
        ACCEPT_ACCT_SEQ_NO,
      </if>
      <if test="acceptProdType != null">
        ACCEPT_PROD_TYPE,
      </if>
      <if test="acceptAcctName != null">
        ACCEPT_ACCT_NAME,
      </if>
      <if test="acceptBranch != null">
        ACCEPT_BRANCH,
      </if>
      <if test="openNumber != null">
        OPEN_NUMBER,
      </if>
      <if test="openName != null">
        OPEN_NAME,
      </if>
      <if test="entrustStatus != null">
        ENTRUST_STATUS,
      </if>
      <if test="paperType != null">
        PAPER_TYPE,
      </if>
      <if test="paperNumber != null">
        PAPER_NUMBER,
      </if>
      <if test="paperDueDate != null">
        PAPER_DUE_DATE,
      </if>
      <if test="copiesNum != null">
        COPIES_NUM,
      </if>
      <if test="hintBackoutDate != null">
        HINT_BACKOUT_DATE,
      </if>
      <if test="emsNo != null">
        EMS_NO,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="payType != null">
        PAY_TYPE,
      </if>
      <if test="backoutDate != null">
        BACKOUT_DATE,
      </if>
      <if test="deliveryAmt != null">
        DELIVERY_AMT,
      </if>
      <if test="refuseReason != null">
        REFUSE_REASON,
      </if>
      <if test="batch != null">
        BATCH,
      </if>
      <if test="registerDate != null">
        REGISTER_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="payName != null">
        PAY_NAME,
      </if>
      <if test="acceptName != null">
        ACCEPT_NAME,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="entrustNo != null">
        #{entrustNo},
      </if>
      <if test="sendReceiveFlag != null">
        #{sendReceiveFlag},
      </if>
      <if test="entrustType != null">
        #{entrustType},
      </if>
      <if test="entrustAmt != null">
        #{entrustAmt},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="payAcctNo != null">
        #{payAcctNo},
      </if>
      <if test="payAcctSeqNo != null">
        #{payAcctSeqNo},
      </if>
      <if test="payProdType != null">
        #{payProdType},
      </if>
      <if test="payAcctName != null">
        #{payAcctName},
      </if>
      <if test="payBranch != null">
        #{payBranch},
      </if>
      <if test="acceptAcctNo != null">
        #{acceptAcctNo},
      </if>
      <if test="acceptAcctSeqNo != null">
        #{acceptAcctSeqNo},
      </if>
      <if test="acceptProdType != null">
        #{acceptProdType},
      </if>
      <if test="acceptAcctName != null">
        #{acceptAcctName},
      </if>
      <if test="acceptBranch != null">
        #{acceptBranch},
      </if>
      <if test="openNumber != null">
        #{openNumber},
      </if>
      <if test="openName != null">
        #{openName},
      </if>
      <if test="entrustStatus != null">
        #{entrustStatus},
      </if>
      <if test="paperType != null">
        #{paperType},
      </if>
      <if test="paperNumber != null">
        #{paperNumber},
      </if>
      <if test="paperDueDate != null">
        #{paperDueDate},
      </if>
      <if test="copiesNum != null">
        #{copiesNum},
      </if>
      <if test="hintBackoutDate != null">
        #{hintBackoutDate},
      </if>
      <if test="emsNo != null">
        #{emsNo},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="payType != null">
        #{payType},
      </if>
      <if test="backoutDate != null">
        #{backoutDate},
      </if>
      <if test="deliveryAmt != null">
        #{deliveryAmt},
      </if>
      <if test="refuseReason != null">
        #{refuseReason},
      </if>
      <if test="batch != null">
        #{batch},
      </if>
      <if test="registerDate != null">
        #{registerDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="payName != null">
        #{payName},
      </if>
      <if test="acceptName != null">
        #{acceptName},
      </if>
    </trim>
  </insert>
  <!-- id="getMbEntrustCollect" add by wangcea 2017-09-28 -->
<!--  <select id="getMbEntrustCollect" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.MbEntrustCollect">-->
<!--      select-->
<!--      <include refid="Base_Column_List"/>-->
<!--      from MB_ENTRUST_COLLECT-->
<!--      where 1=1-->
<!--      <if test="baseParam.tranBranch != null">-->
<!--        AND BRANCH = #{baseParam.tranBranch}-->
<!--      </if>-->
<!--      <if test="baseParam.payAcctNo != null">-->
<!--        AND PAY_ACCT_NO = #{baseParam.payAcctNo}-->
<!--      </if>-->
<!--      <if test="baseParam.paperNumber != null">-->
<!--        AND PAPER_NUMBER = #{baseParam.paperNumber}-->
<!--      </if>-->
<!--      <if test="baseParam.sendReceiveFlag != null">-->
<!--        AND SEND_RECEIVE_FLAG = #{baseParam.sendReceiveFlag}-->
<!--      </if>-->
<!--      <if test="baseParam.batch != null">-->
<!--        AND BATCH = #{baseParam.batch}-->
<!--      </if>-->
<!--      <if test="baseParam.entrustStatus != null">-->
<!--        AND ENTRUST_STATUS = #{baseParam.entrustStatus}-->
<!--      </if>-->
<!--      <if test="baseParam.entrustNo != null">-->
<!--        AND ENTRUST_NO = #{baseParam.entrustNo}-->
<!--      </if>-->
<!--      <if test="baseParam.entrustAmt != null">-->
<!--        AND ENTRUST_AMT = #{baseParam.entrustAmt}-->
<!--      </if>-->
<!--      <if test="baseParam.acceptAcctNo != null">-->
<!--        AND ACCEPT_ACCT_NO = #{baseParam.acceptAcctNo}-->
<!--      </if>-->
<!--      <if test="baseParam.startDate != null and baseParam.endDate != null">-->
<!--        AND REGISTER_DATE BETWEEN #{baseParam.startDate} AND #{baseParam.endDate}-->
<!--      </if>-->
<!--  </select>-->

  <!-- id="getMbEntrustCollect" add by wangcea 2017-09-28 -->
  <select id="getEntrustbyNumber" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
    select
    <include refid="Base_Column_List"/>
    from MB_ENTRUST_COLLECT
    where ENTRUST_STATUS in ('01','03')
    <if test="paperNumber != null">
      AND PAPER_NUMBER = #{paperNumber}
    </if>
  </select>
  <select id="getEntrustByEntrustNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect">
    select <include refid="Base_Column_List"/>
    FROM MB_ENTRUST_COLLECT
    where ENTRUST_STATUS = '01'
    <if test="entrustNo != null">
      AND ENTRUST_NO = #{entrustNo}
    </if>
  </select>
</mapper>
