<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingPrice">

    <select id="getUnderlyingPrice" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingPrice" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingPrice">
        select <include refid="Base_Column"/>
        from RB_DC_UNDERLYING_PRICE
        where 1=1
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="queryUnderlyingPrice" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingPrice">
        select <include refid="Base_Column"/>
        from RB_DC_UNDERLYING_PRICE
        where UNDERLYING_ID = #{underlyingId}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingPrice">
        delete from RB_DC_UNDERLYING_PRICE
        where UNDERLYING_ID = #{underlyingId} and UNDERLYING_PRICE_DATE = #{priceDate}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingPrice">
        update RB_DC_UNDERLYING_PRICE
        <set>
            <if test="underlyingId != null">
                UNDERLYING_ID = #{underlyingId},
            </if>
            <if test="underlyingPrice != null">
                UNDERLYING_PRICE = #{underlyingPrice},
            </if>
            <if test="priceDate != null">
                UNDERLYING_PRICE_DATE = #{priceDate},
            </if>
            <if test="company != null">
                COMPANY = #{company}
            </if>
        </set>
        where UNDERLYING_ID = #{underlyingId}
        and UNDERLYING_PRICE_DATE = #{priceDate}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>


    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingPrice">
        insert into RB_DC_UNDERLYING_PRICE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="underlyingId != null">
                UNDERLYING_ID,
            </if>
            <if test="priceDate != null">
                UNDERLYING_PRICE_DATE,
            </if>
            <if test="underlyingPrice != null">
                UNDERLYING_PRICE,
            </if>
            <if test="company != null">
                COMPANY
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="underlyingId != null">
                #{underlyingId},
            </if>
            <if test="priceDate != null">
                #{priceDate},
            </if>
            <if test="underlyingPrice != null">
                #{underlyingPrice},
            </if>
            <if test="company != null">
                #{company}
            </if>
        </trim>
    </insert>


</mapper>
