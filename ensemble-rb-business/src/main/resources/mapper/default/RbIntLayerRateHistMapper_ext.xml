<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRateHist">
	<resultMap id="getSumAccrAmt" type="map">
		<result column="real_rate" property="realRate"></result>
		<result column="sum(accr_amt)" property="sum(accr_amt)"></result>
	</resultMap>
	<select id="getLayerRateHistList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRateHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRateHist">
		select *
		from RB_INT_LAYER_RATE_HIST
		where INTERNAL_KEY = #{internalKey}
		and CLIENT_NO = #{clientNo}
		<if test="tranDate!=null">
			<![CDATA[AND TRAN_DATE >= #{tranDate}]]>
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY TRAN_DATE,NEAR_AMT
	</select>

<!-- Add fragmentation key conditions for multi-library and single-table queries -->
	<select id="getLayerRateHistList1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntLayerRateHist">
		select real_rate,sum(accr_amt) as accr_amt from rb_int_layer_rate_hist a
		where
		internal_key= #{internalKey}
		and CLIENT_NO = #{clientNo}
		group by real_rate
	</select>
</mapper>
