<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbMsaTransferDetail">
    <select id="selectBy4keysTranDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbMsaTransferDetail">
        select
        <include refid="Base_Column"/>
        from RB_MSA_TRANSFER_DETAIL
        <where>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="acctCcy != null and  acctCcy != '' ">
                AND ACCT_CCY = #{acctCcy,jdbcType=VARCHAR}
            </if>
            <if test="acctSeqNo != null and  acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="transferStatus != null and transferStatus != '' ">
                AND TRAN_STATUS = #{transferStatus,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            AND TRAN_DATE between #{startDate} AND #{endDate}
        </where>
    </select>
</mapper>
