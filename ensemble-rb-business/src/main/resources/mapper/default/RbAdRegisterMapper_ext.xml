<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">

  <sql id="Ad_Base_Column">
    MB.SERIAL_NO,
    MB.INTERNAL_KEY,
    MB.REFERENCE,
    MB.SETTLE_ACCT_TYPE,
    MB.NEED_SETTLE_AMT,
    MB.REAL_SETTLT_AMT,
    MB.TOTAL_AMT,
    MB.RES_SEQ_NO,
    MB.BASE_ACCT_NO,
    MB.ACCT_SEQ_NO,
    MB.PROD_TYPE,
    MB.CCY,
    MB.ACCT_INTERNAL_KEY,
    MB.ACCEPT_CONTRACT_NO
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbAdRegister" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="reference!= null"><!--交易参考号-->
      and REFERENCE = #{reference}
    </if>
    <if test="billMediumType!= null"><!--票据介质类型-->
      and BILL_MEDIUM_TYPE = #{billMediumType}
    </if>
    <if test="acceptStatus!= null"><!--票据状态-->
      and ACCEPT_STATUS = #{acceptStatus}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="getByBillNoAndType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="billType!= null and billType != ''"><!--票据类型-->
      and BILL_TYPE = #{billType}
    </if>
    <if test="billNo!= null and billNo != ''" ><!--票据号码-->
      and BILL_NO = #{billNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    delete from RB_AD_REGISTER
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <select id="updateAcceptStatus" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    update RB_AD_REGISTER
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="acceptStatus != null">
        ACCEPT_STATUS = #{acceptStatus},
      </if>
      <if test="isOwnHolderFlag != null">
        IS_OWN_HOLDER_FLAG = #{isOwnHolderFlag},
      </if>
      <if test="advanceFlag != null">
        ADVANCE_FLAG = #{advanceFlag},
      </if>
      <if test="paymentDate != null">
        PAYMENT_DATE = #{paymentDate},
      </if>
      <if test="paymentBankNo != null">
        PAYMENT_BANK_NO = #{paymentBankNo},
      </if>
      <if test="billCashUser != null">
        BILL_CASH_USER = #{billCashUser}
      </if>
    </set>
    <where>
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="updateBookAcceptStatus" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    update RB_AD_REGISTER
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="billType != null">
        BILL_TYPE = #{billType},
        VOUCHER_TYPE_OLD = #{billType},
      </if>
      <if test="billNo != null">
        BILL_NO = #{billNo},
        VOUCHER_NO_OLD = #{billNo},
      </if>
      <if test="acceptStatus != null">
        ACCEPT_STATUS = #{acceptStatus},
      </if>
      <if test="feeWay != null">
        TRAN_TYPE = #{feeWay},
      </if>
      <if test="acceptBranch != null">
        ACCEPT_BRANCH = #{acceptBranch},
      </if>
      <if test="acceptBaseAcctNo != null">
        ACCEPT_BASE_ACCT_NO = #{acceptBaseAcctNo},
      </if>
      <if test="acceptAcctSeqNo != null">
        ACCEPT_ACCT_SEQ_NO = #{acceptAcctSeqNo},
      </if>
      <if test="acceptAcctCcy != null">
        ACCEPT_ACCT_CCY = #{acceptAcctCcy},
      </if>
      <if test="acceptProdType != null">
        ACCEPT_PROD_TYPE = #{acceptProdType},
      </if>
      <if test="acceptName != null">
        ACCEPT_NAME = #{acceptName},
      </if>
      <if test="acceptBankCode != null">
        ACCEPT_BANK_CODE = #{acceptBankCode},
      </if>
      <if test="acceptBankName != null">
        ACCEPT_BANK_NAME = #{acceptBankName},
      </if>
      <if test="payeeBankCode != null">
        PAYEE_BANK_CODE = #{payeeBankCode},
      </if>
      <if test="payeeBankName != null">
        PAYEE_BANK_NAME = #{payeeBankName},
      </if>
      <if test="acceptBankAddr != null">
        ACCEPT_BANK_ADDR = #{acceptBankAddr},
      </if>
      <if test="billAcceptDate != null">
        BILL_ACCEPT_DATE = #{billAcceptDate},
      </if>
      <if test="billAcceptBank != null">
        BILL_ACCEPT_BANK = #{billAcceptBank},
      </if>
      <if test="billStatus != null">
        BILL_STATUS = #{billStatus},
      </if>
      <if test="operUserId != null">
        OPER_USER_ID = #{operUserId},
      </if>
      <if test="acceptUserId != null">
        ACCEPT_USER_ID = #{acceptUserId},
      </if>
    </set>
    <where>
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="updateVoucherStatus"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    update RB_AD_REGISTER
    <set>
      <if test="voucherType != null">
        VOUCHER_TYPE = #{voucherType},
        BILL_CODE = #{voucherType},
        BILL_TYPE = #{voucherType},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo},
        BILL_NO = #{voucherNo},
      </if>
      <if test="acceptStatus != null">
        ACCEPT_STATUS = #{acceptStatus}
      </if>
    </set>
    <where>
    <if test="voucherTypeOld != null">
      AND VOUCHER_TYPE = #{voucherTypeOld}
    </if>
    <if test="voucherNoOld != null">
      AND VOUCHER_NO = #{voucherNoOld}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="updateBillStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    update RB_AD_REGISTER
    <set>
      <if test="billStatus != null">
        BILL_STATUS = #{billStatus},
      </if>
      LOST_NO = #{lostNo},
    </set>
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    update RB_AD_REGISTER
    <set>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="crReference != null">
        CR_REFERENCE = #{crReference},
      </if>
      <if test="edReference != null">
        ED_REFERENCE = #{edReference},
      </if>
      <if test="ptReference != null">
        PT_REFERENCE = #{ptReference},
      </if>
      <if test="origSource != null">
        ORIG_SOURCE = #{origSource},
      </if>
      <if test="origAmt != null">
        ORIG_AMT = #{origAmt},
      </if>
      <if test="origTranType != null">
        ORIG_TRAN_TYPE = #{origTranType},
      </if>
      <if test="origReference != null">
        ORIG_REFERENCE = #{origReference},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE = #{sourceModule},
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO = #{channelSeqNo},
      </if>
      <if test="acceptContractNo != null">
        ACCEPT_CONTRACT_NO = #{acceptContractNo},
      </if>
      <if test="cmisloanNo != null">
        CMISLOAN_NO = #{cmisloanNo},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="feeAmt != null">
        FEE_AMT = #{feeAmt},
      </if>
      <if test="openBranch != null">
        OPEN_BRANCH = #{openBranch},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientName != null">
        CLIENT_NAME = #{clientName},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="acceptStatus != null">
        ACCEPT_STATUS = #{acceptStatus},
      </if>
      <if test="payerAcctNo != null">
        PAYER_ACCT_NO = #{payerAcctNo},
      </if>
      <if test="payerAcctSeqNo != null">
        PAYER_ACCT_SEQ_NO = #{payerAcctSeqNo},
      </if>
      <if test="payerProdeType != null">
        PAYER_PRODE_TYPE = #{payerProdeType},
      </if>
      <if test="payerAcctCcy != null">
        PAYER_ACCT_CCY = #{payerAcctCcy},
      </if>
      <if test="payerName != null">
        PAYER_NAME = #{payerName},
      </if>
      <if test="payerAddr != null">
        PAYER_ADDR = #{payerAddr},
      </if>
      <if test="payerBankCode != null">
        PAYER_BANK_CODE = #{payerBankCode},
      </if>
      <if test="payerBankName != null">
        PAYER_BANK_NAME = #{payerBankName},
      </if>
      <if test="payerBankAddr != null">
        PAYER_BANK_ADDR = #{payerBankAddr},
      </if>
      <if test="payeeBaseAcctNo != null">
        PAYEE_BASE_ACCT_NO = #{payeeBaseAcctNo},
      </if>
      <if test="payeeAcctSeqNo != null">
        PAYEE_ACCT_SEQ_NO = #{payeeAcctSeqNo},
      </if>
      <if test="payeeProdeType != null">
        PAYEE_PRODE_TYPE = #{payeeProdeType},
      </if>
      <if test="payeeAcctCcy != null">
        PAYEE_ACCT_CCY = #{payeeAcctCcy},
      </if>
      <if test="payeeAcctName != null">
        PAYEE_ACCT_NAME = #{payeeAcctName},
      </if>
      <if test="payeeAddr != null">
        PAYEE_ADDR = #{payeeAddr},
      </if>
      <if test="payeeBankCode != null">
        PAYEE_BANK_CODE = #{payeeBankCode},
      </if>
      <if test="payeeBankName != null">
        PAYEE_BANK_NAME = #{payeeBankName},
      </if>
      <if test="payeeBankAddr != null">
        PAYEE_BANK_ADDR = #{payeeBankAddr},
      </if>
      <if test="acceptAcctCcy != null">
        ACCEPT_ACCT_CCY = #{acceptAcctCcy},
      </if>
      <if test="acceptBaseAcctNo != null">
        ACCEPT_BASE_ACCT_NO = #{acceptBaseAcctNo},
      </if>
      <if test="acceptAcctSeqNo != null">
        ACCEPT_ACCT_SEQ_NO = #{acceptAcctSeqNo},
      </if>
      <if test="acceptProdType != null">
        ACCEPT_PROD_TYPE = #{acceptProdType},
      </if>
      <if test="acceptName != null">
        ACCEPT_NAME = #{acceptName},
      </if>
      <if test="acceptAddr != null">
        ACCEPT_ADDR = #{acceptAddr},
      </if>
      <if test="acceptBankCode != null">
        ACCEPT_BANK_CODE = #{acceptBankCode},
      </if>
      <if test="acceptBankName != null">
        ACCEPT_BANK_NAME = #{acceptBankName},
      </if>
      <if test="acceptBankAddr != null">
        ACCEPT_BANK_ADDR = #{acceptBankAddr},
      </if>
      <if test="lastHolderBaseAcctNo != null">
        LAST_HOLDER_BASE_ACCT_NO = #{lastHolderBaseAcctNo},
      </if>
      <if test="lastHolderAcctSeqNo != null">
        LAST_HOLDER_ACCT_SEQ_NO = #{lastHolderAcctSeqNo},
      </if>
      <if test="lastHolderProdeType != null">
        LAST_HOLDER_PRODE_TYPE = #{lastHolderProdeType},
      </if>
      <if test="lastHolderAcctCcy != null">
        LAST_HOLDER_ACCT_CCY = #{lastHolderAcctCcy},
      </if>
      <if test="lastHolderAcctType != null">
        LAST_HOLDER_ACCT_TYPE = #{lastHolderAcctType},
      </if>
      <if test="lastHolderNeme != null">
        LAST_HOLDER_NEME = #{lastHolderNeme},
      </if>
      <if test="lastHolderAddr != null">
        LAST_HOLDER_ADDR = #{lastHolderAddr},
      </if>
      <if test="lastHolderBankCode != null">
        LAST_HOLDER_BANK_CODE = #{lastHolderBankCode},
      </if>
      <if test="lastHolderBankName != null">
        LAST_HOLDER_BANK_NAME = #{lastHolderBankName},
      </if>
      <if test="lastHolderBankAddr != null">
        LAST_HOLDER_BANK_ADDR = #{lastHolderBankAddr},
      </if>
      <if test="billCode != null">
        BILL_CODE = #{billCode},
      </if>
      <if test="billType != null">
        BILL_TYPE = #{billType},
      </if>
      <if test="billPrefix != null">
        BILL_PREFIX = #{billPrefix},
      </if>
      <if test="billNo != null">
        BILL_NO = #{billNo},
      </if>
      <if test="billAmt != null">
        BILL_AMT = #{billAmt},
      </if>
      <if test="billStatus != null">
        BILL_STATUS = #{billStatus},
      </if>
      <if test="billSignDate != null">
        BILL_SIGN_DATE = #{billSignDate},
      </if>
      <if test="billSignBank != null">
        BILL_SIGN_BANK = #{billSignBank},
      </if>
      <if test="paymentDate != null">
        PAYMENT_DATE = #{paymentDate},
      </if>
      <if test="paymentBankNo != null">
        PAYMENT_BANK_NO = #{paymentBankNo},
      </if>
      <if test="billAcceptDate != null">
        BILL_ACCEPT_DATE = #{billAcceptDate},
      </if>
      <if test="billAcceptBank != null">
        BILL_ACCEPT_BANK = #{billAcceptBank},
      </if>
      <if test="billMaturityDate != null">
        BILL_MATURITY_DATE = #{billMaturityDate},
      </if>
      <if test="billExpireDate != null">
        BILL_EXPIRE_DATE = #{billExpireDate},
      </if>
      <if test="billMediumType != null">
        BILL_MEDIUM_TYPE = #{billMediumType},
      </if>
      <if test="billTransferFlag != null">
        BILL_TRANSFER_FLAG = #{billTransferFlag},
      </if>
      <if test="voucherType != null">
        VOUCHER_TYPE = #{voucherType},
      </if>
      <if test="voucherPrefix != null">
        VOUCHER_PREFIX = #{voucherPrefix},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo},
      </if>
      <if test="voucherTypeOld != null">
        VOUCHER_TYPE_OLD = #{voucherTypeOld},
      </if>
      <if test="voucherPrefixOld != null">
        VOUCHER_PREFIX_OLD = #{voucherPrefixOld},
      </if>
      <if test="voucherNoOld != null">
        VOUCHER_NO_OLD = #{voucherNoOld},
      </if>
      <if test="operUserId != null">
        OPER_USER_ID = #{operUserId},
      </if>
      <if test="operDate != null">
        OPER_DATE = #{operDate},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="authDate != null">
        AUTH_DATE = #{authDate},
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="acceptBranch != null">
        ACCEPT_BRANCH = #{acceptBranch},
      </if>
      <if test="acceptUserId != null">
        ACCEPT_USER_ID = #{acceptUserId},
      </if>
      <if test="lostNo != null">
        LOST_NO = #{lostNo},
      </if>
      <if test="billCashUser != null">
        BILL_CASH_USER = #{billCashUser},
      </if>
      <if test="backTranType != null">
        BACK_TRAN_TYPE = #{backTranType},
      </if>
      <if test="backCheckNo != null">
        BACK_CHECK_NO = #{backCheckNo},
      </if>
      <if test="errorMsg != null">
        ERROR_MSG = #{errorMsg},
      </if>
      <if test="printCnt != null">
        PRINT_CNT = #{printCnt},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    insert into RB_AD_REGISTER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="crReference != null">
        CR_REFERENCE,
      </if>
      <if test="edReference != null">
        ED_REFERENCE,
      </if>
      <if test="ptReference != null">
        PT_REFERENCE,
      </if>
      <if test="origSource != null">
        ORIG_SOURCE,
      </if>
      <if test="origAmt != null">
        ORIG_AMT,
      </if>
      <if test="origTranType != null">
        ORIG_TRAN_TYPE,
      </if>
      <if test="origReference != null">
        ORIG_REFERENCE,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="sourceModule != null">
        SOURCE_MODULE,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="acceptContractNo != null">
        ACCEPT_CONTRACT_NO,
      </if>
      <if test="cmisloanNo != null">
        CMISLOAN_NO,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="feeAmt != null">
        FEE_AMT,
      </if>
      <if test="openBranch != null">
        OPEN_BRANCH,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="acceptStatus != null">
        ACCEPT_STATUS,
      </if>
      <if test="payerAcctNo != null">
        PAYER_ACCT_NO,
      </if>
      <if test="payerAcctSeqNo != null">
        PAYER_ACCT_SEQ_NO,
      </if>
      <if test="payerProdeType != null">
        PAYER_PRODE_TYPE,
      </if>
      <if test="payerAcctCcy != null">
        PAYER_ACCT_CCY,
      </if>
      <if test="payerName != null">
        PAYER_NAME,
      </if>
      <if test="payerAddr != null">
        PAYER_ADDR,
      </if>
      <if test="payerBankCode != null">
        PAYER_BANK_CODE,
      </if>
      <if test="payerBankName != null">
        PAYER_BANK_NAME,
      </if>
      <if test="payerBankAddr != null">
        PAYER_BANK_ADDR,
      </if>
      <if test="payeeBaseAcctNo != null">
        PAYEE_BASE_ACCT_NO,
      </if>
      <if test="payeeAcctSeqNo != null">
        PAYEE_ACCT_SEQ_NO,
      </if>
      <if test="payeeProdeType != null">
        PAYEE_PRODE_TYPE,
      </if>
      <if test="payeeAcctCcy != null">
        PAYEE_ACCT_CCY,
      </if>
      <if test="payeeAcctName != null">
        PAYEE_ACCT_NAME,
      </if>
      <if test="payeeAddr != null">
        PAYEE_ADDR,
      </if>
      <if test="payeeBankCode != null">
        PAYEE_BANK_CODE,
      </if>
      <if test="payeeBankName != null">
        PAYEE_BANK_NAME,
      </if>
      <if test="payeeBankAddr != null">
        PAYEE_BANK_ADDR,
      </if>
      <if test="acceptAcctCcy != null">
        ACCEPT_ACCT_CCY,
      </if>
      <if test="acceptBaseAcctNo != null">
        ACCEPT_BASE_ACCT_NO,
      </if>
      <if test="acceptAcctSeqNo != null">
        ACCEPT_ACCT_SEQ_NO,
      </if>
      <if test="acceptProdType != null">
        ACCEPT_PROD_TYPE,
      </if>
      <if test="acceptName != null">
        ACCEPT_NAME,
      </if>
      <if test="acceptAddr != null">
        ACCEPT_ADDR,
      </if>
      <if test="acceptBankCode != null">
        ACCEPT_BANK_CODE,
      </if>
      <if test="acceptBankName != null">
        ACCEPT_BANK_NAME,
      </if>
      <if test="acceptBankAddr != null">
        ACCEPT_BANK_ADDR,
      </if>
      <if test="lastHolderBaseAcctNo != null">
        LAST_HOLDER_BASE_ACCT_NO,
      </if>
      <if test="lastHolderAcctSeqNo != null">
        LAST_HOLDER_ACCT_SEQ_NO,
      </if>
      <if test="lastHolderProdeType != null">
        LAST_HOLDER_PRODE_TYPE,
      </if>
      <if test="lastHolderAcctCcy != null">
        LAST_HOLDER_ACCT_CCY,
      </if>
      <if test="lastHolderAcctType != null">
        LAST_HOLDER_ACCT_TYPE,
      </if>
      <if test="lastHolderNeme != null">
        LAST_HOLDER_NEME,
      </if>
      <if test="lastHolderAddr != null">
        LAST_HOLDER_ADDR,
      </if>
      <if test="lastHolderBankCode != null">
        LAST_HOLDER_BANK_CODE,
      </if>
      <if test="lastHolderBankName != null">
        LAST_HOLDER_BANK_NAME,
      </if>
      <if test="lastHolderBankAddr != null">
        LAST_HOLDER_BANK_ADDR,
      </if>
      <if test="billCode != null">
        BILL_CODE,
      </if>
      <if test="billType != null">
        BILL_TYPE,
      </if>
      <if test="billPrefix != null">
        BILL_PREFIX,
      </if>
      <if test="billNo != null">
        BILL_NO,
      </if>
      <if test="billAmt != null">
        BILL_AMT,
      </if>
      <if test="billStatus != null">
        BILL_STATUS,
      </if>
      <if test="billSignDate != null">
        BILL_SIGN_DATE,
      </if>
      <if test="billSignBank != null">
        BILL_SIGN_BANK,
      </if>
      <if test="paymentDate != null">
        PAYMENT_DATE,
      </if>
      <if test="paymentBankNo != null">
        PAYMENT_BANK_NO,
      </if>
      <if test="billAcceptDate != null">
        BILL_ACCEPT_DATE,
      </if>
      <if test="billAcceptBank != null">
        BILL_ACCEPT_BANK,
      </if>
      <if test="billMaturityDate != null">
        BILL_MATURITY_DATE,
      </if>
      <if test="billExpireDate != null">
        BILL_EXPIRE_DATE,
      </if>
      <if test="billMediumType != null">
        BILL_MEDIUM_TYPE,
      </if>
      <if test="billTransferFlag != null">
        BILL_TRANSFER_FLAG,
      </if>
      <if test="voucherType != null">
        VOUCHER_TYPE,
      </if>
      <if test="voucherPrefix != null">
        VOUCHER_PREFIX,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="voucherTypeOld != null">
        VOUCHER_TYPE_OLD,
      </if>
      <if test="voucherPrefixOld != null">
        VOUCHER_PREFIX_OLD,
      </if>
      <if test="voucherNoOld != null">
        VOUCHER_NO_OLD,
      </if>
      <if test="operUserId != null">
        OPER_USER_ID,
      </if>
      <if test="operDate != null">
        OPER_DATE,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="authDate != null">
        AUTH_DATE,
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="acceptBranch != null">
        ACCEPT_BRANCH,
      </if>
      <if test="acceptUserId != null">
        ACCEPT_USER_ID,
      </if>
      <if test="lostNo != null">
        LOST_NO,
      </if>
      <if test="billCashUser != null">
        BILL_CASH_USER,
      </if>
      <if test="backTranType != null">
        BACK_TRAN_TYPE,
      </if>
      <if test="backCheckNo != null">
        BACK_CHECK_NO,
      </if>
      <if test="errorMsg != null">
        ERROR_MSG,
      </if>
      <if test="printCnt != null">
        PRINT_CNT,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="advanceFlag != null">
        ADVANCE_FLAG,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="crReference != null">
        #{crReference},
      </if>
      <if test="edReference != null">
        #{edReference},
      </if>
      <if test="ptReference != null">
        #{ptReference},
      </if>
      <if test="origSource != null">
        #{origSource},
      </if>
      <if test="origAmt != null">
        #{origAmt},
      </if>
      <if test="origTranType != null">
        #{origTranType},
      </if>
      <if test="origReference != null">
        #{origReference},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="sourceModule != null">
        #{sourceModule},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="acceptContractNo != null">
        #{acceptContractNo},
      </if>
      <if test="cmisloanNo != null">
        #{cmisloanNo},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="feeAmt != null">
        #{feeAmt},
      </if>
      <if test="openBranch != null">
        #{openBranch},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="acceptStatus != null">
        #{acceptStatus},
      </if>
      <if test="payerAcctNo != null">
        #{payerAcctNo},
      </if>
      <if test="payerAcctSeqNo != null">
        #{payerAcctSeqNo},
      </if>
      <if test="payerProdeType != null">
        #{payerProdeType},
      </if>
      <if test="payerAcctCcy != null">
        #{payerAcctCcy},
      </if>
      <if test="payerName != null">
        #{payerName},
      </if>
      <if test="payerAddr != null">
        #{payerAddr},
      </if>
      <if test="payerBankCode != null">
        #{payerBankCode},
      </if>
      <if test="payerBankName != null">
        #{payerBankName},
      </if>
      <if test="payerBankAddr != null">
        #{payerBankAddr},
      </if>
      <if test="payeeBaseAcctNo != null">
        #{payeeBaseAcctNo},
      </if>
      <if test="payeeAcctSeqNo != null">
        #{payeeAcctSeqNo},
      </if>
      <if test="payeeProdeType != null">
        #{payeeProdeType},
      </if>
      <if test="payeeAcctCcy != null">
        #{payeeAcctCcy},
      </if>
      <if test="payeeAcctName != null">
        #{payeeAcctName},
      </if>
      <if test="payeeAddr != null">
        #{payeeAddr},
      </if>
      <if test="payeeBankCode != null">
        #{payeeBankCode},
      </if>
      <if test="payeeBankName != null">
        #{payeeBankName},
      </if>
      <if test="payeeBankAddr != null">
        #{payeeBankAddr},
      </if>
      <if test="acceptAcctCcy != null">
        #{acceptAcctCcy},
      </if>
      <if test="acceptBaseAcctNo != null">
        #{acceptBaseAcctNo},
      </if>
      <if test="acceptAcctSeqNo != null">
        #{acceptAcctSeqNo},
      </if>
      <if test="acceptProdType != null">
        #{acceptProdType},
      </if>
      <if test="acceptName != null">
        #{acceptName},
      </if>
      <if test="acceptAddr != null">
        #{acceptAddr},
      </if>
      <if test="acceptBankCode != null">
        #{acceptBankCode},
      </if>
      <if test="acceptBankName != null">
        #{acceptBankName},
      </if>
      <if test="acceptBankAddr != null">
        #{acceptBankAddr},
      </if>
      <if test="lastHolderBaseAcctNo != null">
        #{lastHolderBaseAcctNo},
      </if>
      <if test="lastHolderAcctSeqNo != null">
        #{lastHolderAcctSeqNo},
      </if>
      <if test="lastHolderProdeType != null">
        #{lastHolderProdeType},
      </if>
      <if test="lastHolderAcctCcy != null">
        #{lastHolderAcctCcy},
      </if>
      <if test="lastHolderAcctType != null">
        #{lastHolderAcctType},
      </if>
      <if test="lastHolderNeme != null">
        #{lastHolderNeme},
      </if>
      <if test="lastHolderAddr != null">
        #{lastHolderAddr},
      </if>
      <if test="lastHolderBankCode != null">
        #{lastHolderBankCode},
      </if>
      <if test="lastHolderBankName != null">
        #{lastHolderBankName},
      </if>
      <if test="lastHolderBankAddr != null">
        #{lastHolderBankAddr},
      </if>
      <if test="billCode != null">
        #{billCode},
      </if>
      <if test="billType != null">
        #{billType},
      </if>
      <if test="billPrefix != null">
        #{billPrefix},
      </if>
      <if test="billNo != null">
        #{billNo},
      </if>
      <if test="billAmt != null">
        #{billAmt},
      </if>
      <if test="billStatus != null">
        #{billStatus},
      </if>
      <if test="billSignDate != null">
        #{billSignDate},
      </if>
      <if test="billSignBank != null">
        #{billSignBank},
      </if>
      <if test="paymentDate != null">
        #{paymentDate},
      </if>
      <if test="paymentBankNo != null">
        #{paymentBankNo},
      </if>
      <if test="billAcceptDate != null">
        #{billAcceptDate},
      </if>
      <if test="billAcceptBank != null">
        #{billAcceptBank},
      </if>
      <if test="billMaturityDate != null">
        #{billMaturityDate},
      </if>
      <if test="billExpireDate != null">
        #{billExpireDate},
      </if>
      <if test="billMediumType != null">
        #{billMediumType},
      </if>
      <if test="billTransferFlag != null">
        #{billTransferFlag},
      </if>
      <if test="voucherType != null">
        #{voucherType},
      </if>
      <if test="voucherPrefix != null">
        #{voucherPrefix},
      </if>
      <if test="voucherNo != null">
        #{voucherNo},
      </if>
      <if test="voucherTypeOld != null">
        #{voucherTypeOld},
      </if>
      <if test="voucherPrefixOld != null">
        #{voucherPrefixOld},
      </if>
      <if test="voucherNoOld != null">
        #{voucherNoOld},
      </if>
      <if test="operUserId != null">
        #{operUserId},
      </if>
      <if test="operDate != null">
        #{operDate},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="authDate != null">
        #{authDate},
      </if>
      <if test="lastChangeUserId != null">
        #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="acceptBranch != null">
        #{acceptBranch},
      </if>
      <if test="acceptUserId != null">
        #{acceptUserId},
      </if>
      <if test="lostNo != null">
        #{lostNo},
      </if>
      <if test="billCashUser != null">
        #{billCashUser},
      </if>
      <if test="backTranType != null">
        #{backTranType},
      </if>
      <if test="backCheckNo != null">
        #{backCheckNo},
      </if>
      <if test="errorMsg != null">
        #{errorMsg},
      </if>
      <if test="printCnt != null">
        #{printCnt},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="advanceFlag != null">
        #{advanceFlag},
      </if>
    </trim>
  </insert>
  <select id="queryAdRegisterByCrReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="crReference !=null">
      AND CR_REFERENCE = #{crReference}
    </if>
    <if test="reference !=null">
      AND REFERENCE = #{reference}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>

  </select>
  <select id="queryAdRegisterParam" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="tranType !=null">
      AND TRAN_TYPE = #{tranType}
    </if>
    <if test="billType !=null">
      AND BILL_TYPE = #{billType}
    </if>
    <if test="billNo !=null">
      AND BILL_NO = #{billNo}
    </if>
    <if test="billMediumType !=null">
      AND BILL_MEDIUM_TYPE = #{billMediumType}
    </if>
    <if test="acceptBranch !=null">
      AND TRAN_BRANCH = #{acceptBranch}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="queryAdRegisterByBillInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="crReference !=null">
      AND CR_REFERENCE = #{crReference}
    </if>
    <if test="billType !=null">
      AND BILL_TYPE = #{billType}
    </if>
    <if test="billNo !=null">
      AND BILL_NO = #{billNo}
    </if>
    <if test="billMediumType !=null">
      AND BILL_MEDIUM_TYPE = #{billMediumType}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="selectAdRegisterList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="acceptBranch !=null and acceptBranch!=''">
      AND ACCEPT_BRANCH = #{acceptBranch}
    </if>
    <if test="billType !=null and billType!=''">
      AND BILL_TYPE = #{billType}
    </if>
    <if test="billNo !=null and billNo!=''">
      AND BILL_NO = #{billNo}
    </if>
    <if test="billPrefix !=null and billPrefix!=''">
      AND BILL_PREFIX = #{billPrefix}
    </if>
    <if test="payerAcctNo !=null and payerAcctNo!=''">
      AND PAYER_ACCT_NO = #{payerAcctNo}
    </if>
    <if test="acceptStatus !=null and acceptStatus!=''">
      AND ACCEPT_STATUS = #{acceptStatus}
    </if>
    <if test="billStatus !=null and billStatus!=''">
      AND BILL_STATUS = #{billStatus}
    </if>
    <if test="billMediumType !=null and billMediumType!=''">
      AND BILL_MEDIUM_TYPE = #{billMediumType}
    </if>
    <if test="clientNo !=null and clientNo!=''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="clientName !=null and clientName!=''">
      AND CLIENT_NAME = #{clientName}
    </if>
    <if test="payerName !=null and payerName!=''">
      AND PAYER_NAME = #{payerName}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>

    ORDER BY LAST_CHANGE_DATE,BILL_NO ASC
  </select>

  <select id="queryAdRegisterList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    where LOST_NO IS NOT NULL
    <if test="billNo !=null">
      AND BILL_NO = #{billNo}
    </if>
    <if test="status !=null">
      AND STATUS = #{status}
    </if>
    <if test="acceptBranch !=null">
      AND ACCEPT_BRANCH = #{acceptBranch}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>


  </select>
  <select id="queryAdRegisterOne" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="tranType !=null">
      AND TRAN_TYPE = #{tranType}
    </if>
    <if test="billType !=null">
      AND BILL_TYPE = #{billType}
    </if>
    <if test="billNo !=null">
      AND BILL_NO = #{billNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="billMediumType !=null">
      AND BILL_MEDIUM_TYPE = #{billMediumType}
    </if>
    </where>
  </select>

  <select id="selectByLostNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    <where>
    <if test="lostNo !=null">
      AND LOST_NO = #{lostNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

<!--  <select id="getAdRegisterParam" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">-->
<!--    select <include refid="Ad_Base_Column"/>-->
<!--    from RB_AD_REGISTER MA,RB_AD_SETTLE MB-->
<!--    where MA.INTERNAL_KEY = MB.INTERNAL_KEY-->
<!--    AND MA.BILL_MATURITY_DATE <![CDATA[ <= ]]> #{baseParam.runDate}-->
<!--    AND MB.NEED_SETTLE_AMT > 0-->
<!--    <if test="baseParam.billNo !=null and baseParam.billNo.length() > 0">-->
<!--      AND MA.BILL_NO = #{baseParam.billNo}-->
<!--    </if>-->
<!--    <if test="baseParam.tranBranch !=null and baseParam.tranBranch.length() > 0">-->
<!--      AND MA.ACCEPT_BRANCH = #{baseParam.tranBranch}-->
<!--    </if>-->
<!--    <if test="baseParam.acceptStatus !=null and baseParam.acceptStatus.length() > 0">-->
<!--      AND MA.ACCEPT_STATUS = #{baseParam.acceptStatus}-->
<!--    </if>-->
<!--    <if test="baseParam.billType !=null and baseParam.billType.length() > 0">-->
<!--      AND MA.BILL_TYPE = #{baseParam.billType}-->
<!--    </if>-->
<!--    <if test="baseParam.acceptContractNo !=null and baseParam.acceptContractNo.length() > 0">-->
<!--      AND MA.ACCEPT_CONTRACT_NO = #{baseParam.acceptContractNo}-->
<!--    </if>-->
<!--    <if test="baseParam.payerAcctNo !=null and baseParam.payerAcctNo.length() > 0">&lt;!&ndash;修改ACCEPT_ACCT为PAYER_ACCT_NO&ndash;&gt;-->
<!--      AND MA.PAYER_ACCT_NO = #{baseParam.payerAcctNo}-->
<!--    </if>-->
<!--    <if test="baseParam.billMaturityDate !=null and baseParam.billMaturityDate.length() > 0">-->
<!--      <![CDATA[AND MA.BILL_MATURITY_DATE <= #{baseParam.billMaturityDate}]]>-->
<!--    </if>-->
<!--    order by MA.BILL_MATURITY_DATE asc,MA.BILL_NO asc-->
<!--  </select>-->

  <select id="queryAdRegisterForKeys" parameterType="java.util.Map" resultType="String">
    SELECT INTERNAL_KEY
    FROM RB_AD_REGISTER
    WHERE ACCEPT_STATUS ='3'
      AND  <![CDATA[AND BILL_MATURITY_DATE <= #{toDate}]]>
      AND INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

    ORDER BY INTERNAL_KEY
  </select>

  <select id="queryAdRegisiterForDetail" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    FROM RB_AD_REGISTER
    WHERE ACCEPT_STATUS ='3'
    AND INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

    ORDER BY INTERNAL_KEY
  </select>

  <select id="queryAdRegisterForSplit" parameterType="java.util.Map" resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      <![CDATA[
      SELECT
      MIN(internal_key) START_KEY,
      MAX(internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM
      (
      SELECT
      DISTINCT internal_key,
      @rownum :=@rownum + 1 AS rownum
      FROM
      RB_AD_REGISTER,
      (SELECT @rownum := -1) t
      WHERE ACCEPT_STATUS ='3'
      AND BILL_MATURITY_DATE <= #{runDate}
      ORDER BY internal_key
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
      ]]>
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT MIN (internal_key) START_KEY, MAX (internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM (SELECT DISTINCT internal_key
      FROM RB_AD_REGISTER
      WHERE ACCEPT_STATUS ='3'
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      <![CDATA[ AND BILL_MATURITY_DATE <= #{runDate}
      ORDER BY internal_key)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
      ]]>
    </if>
  </select>
  <select id="queryAdRegisterByTranDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    WHERE TRAN_DATE =#{tranDate}
    AND TRAN_BRANCH = #{tranBranch}
    <if test="userId !=null">
      and OPER_USER_ID =  #{userId}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

    AND ACCEPT_STATUS ='1'
  </select>
  <select id="qryRegListBychgDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    WHERE LAST_CHANGE_DATE =#{changeDate}
    AND BILL_NO IS NOT NULL
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>
  <select id="queryMbAdRegisterByDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    WHERE LAST_CHANGE_DATE =#{tranDate} and ACCEPT_STATUS > '2'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by LAST_CHANGE_DATE ASC
  </select>

<!--  <select id="selectAdRegisterListByPage" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">-->
<!--    select <include refid="Base_Column"/>-->
<!--    from RB_AD_REGISTER-->
<!--    where 1=1-->
<!--    <if test="baseParam.acceptBranch !=null and baseParam.acceptBranch!=''">-->
<!--      AND ACCEPT_BRANCH = #{baseParam.acceptBranch}-->
<!--    </if>-->
<!--    <if test="baseParam.acceptBranch ==null or baseParam.acceptBranch==''">-->
<!--      AND ACCEPT_BRANCH IN (select tranBranch from FM_BRANCH WHERE attached_to = #{baseParam.userBranch}-->
<!--      or tranBranch = #{baseParam.userBranch})-->
<!--    </if>-->
<!--    <if test="baseParam.billType !=null and baseParam.billType!=''">-->
<!--      AND BILL_TYPE = #{baseParam.billType}-->
<!--    </if>-->
<!--    <if test="baseParam.billNo !=null and baseParam.billNo!=''">-->
<!--      AND BILL_NO = #{baseParam.billNo}-->
<!--    </if>-->
<!--    <if test="baseParam.billPrefix !=null and baseParam.billPrefix!=''">-->
<!--      AND BILL_PREFIX = #{baseParam.billPrefix}-->
<!--    </if>-->
<!--    <if test="baseParam.payerAcctNo !=null and baseParam.payerAcctNo!=''">-->
<!--      AND PAYER_ACCT_NO = #{baseParam.payerAcctNo}-->
<!--    </if>-->
<!--    <if test="baseParam.acceptStatus !=null and baseParam.acceptStatus!=''">-->
<!--      AND ACCEPT_STATUS = #{baseParam.acceptStatus}-->
<!--    </if>-->
<!--    <if test="baseParam.billStatus !=null and baseParam.billStatus!=''">-->
<!--      AND BILL_STATUS = #{baseParam.billStatus}-->
<!--    </if>-->
<!--    <if test="baseParam.billMediumType !=null and baseParam.billMediumType!=''">-->
<!--      AND BILL_MEDIUM_TYPE = #{baseParam.billMediumType}-->
<!--    </if>-->
<!--    <if test="baseParam.clientNo !=null and baseParam.clientNo!=''">-->
<!--      AND CLIENT_NO = #{baseParam.clientNo}-->
<!--    </if>-->
<!--    <if test="baseParam.clientName !=null and baseParam.clientName!=''">-->
<!--      AND CLIENT_NAME = #{baseParam.clientName}-->
<!--    </if>-->
<!--    <if test="baseParam.payerName !=null and baseParam.payerName!=''">-->
<!--      AND PAYER_NAME = #{baseParam.payerName}-->
<!--    </if>-->
<!--    <if test="baseParam.startBillSignDate !=null and baseParam.startBillSignDate!=''">-->
<!--      AND BILL_SIGN_DATE <![CDATA[>=]]> #{baseParam.startBillSignDate}-->
<!--    </if>-->
<!--    <if test="baseParam.endBillSignDate !=null and baseParam.endBillSignDate!=''">-->
<!--      AND BILL_SIGN_DATE <![CDATA[<=]]> #{baseParam.endBillSignDate}-->
<!--    </if>-->
<!--    <if test="baseParam.startBillMaturityDate !=null and baseParam.startBillMaturityDate!=''">-->
<!--      AND BILL_MATURITY_DATE <![CDATA[>=]]> #{baseParam.startBillMaturityDate}-->
<!--    </if>-->
<!--    <if test="baseParam.endBillMaturityDate !=null and baseParam.endBillMaturityDate!=''">-->
<!--      AND BILL_MATURITY_DATE <![CDATA[<=]]> #{baseParam.endBillMaturityDate}-->
<!--    </if>-->
<!--    <if test="baseParam.startBillCashDate !=null and baseParam.startBillCashDate!=''">-->
<!--      AND PAYMENT_DATE <![CDATA[>=]]> #{baseParam.startBillCashDate}-->
<!--    </if>-->
<!--    <if test="baseParam.endBillCashDate !=null and baseParam.endBillCashDate!=''">-->
<!--      AND PAYMENT_DATE <![CDATA[<=]]> #{baseParam.endBillCashDate}-->
<!--    </if>-->
<!--    ORDER BY LAST_CHANGE_DATE ,BILL_NO ASC-->
<!--  </select>-->
<!--  <select id="getAdRegisterPage" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">-->
<!--    select <include refid="Ad_Base_Column"/>-->
<!--    from RB_AD_REGISTER MA,RB_AD_SETTLE MB-->
<!--    where MA.INTERNAL_KEY = MB.INTERNAL_KEY-->
<!--    AND MB.SETTLE_ACCT_TYPE IN ('BOND','SETTLE')-->
<!--    AND MB.NEED_SETTLE_AMT > 0-->
<!--    AND MA.BILL_MATURITY_DATE <![CDATA[ <= ]]> #{baseParam.runDate}-->
<!--    <if test="baseParam.billNo !=null and baseParam.billNo.length() > 0">-->
<!--      AND MA.BILL_NO = #{baseParam.billNo}-->
<!--    </if>-->
<!--    <if test="baseParam.tranBranch !=null and baseParam.tranBranch.length() > 0">-->
<!--      AND MA.ACCEPT_BRANCH = #{baseParam.tranBranch}-->
<!--    </if>-->
<!--    <if test="baseParam.acceptStatus !=null and baseParam.acceptStatus.length() > 0">-->
<!--      AND MA.ACCEPT_STATUS = #{baseParam.acceptStatus}-->
<!--    </if>-->
<!--    <if test="baseParam.billType !=null and baseParam.billType.length() > 0">-->
<!--      AND MA.BILL_TYPE = #{baseParam.billType}-->
<!--    </if>-->
<!--    <if test="baseParam.acceptContractNo !=null and baseParam.acceptContractNo.length() > 0">-->
<!--      AND MA.ACCEPT_CONTRACT_NO = #{baseParam.acceptContractNo}-->
<!--    </if>-->
<!--    <if test="baseParam.payerAcctNo !=null and baseParam.payerAcctNo.length() > 0">&lt;!&ndash;修改ACCEPT_ACCT为PAYER_ACCT_NO&ndash;&gt;-->
<!--      AND MA.PAYER_ACCT_NO = #{baseParam.payerAcctNo}-->
<!--    </if>-->
<!--    <if test="baseParam.billMaturityDate !=null and baseParam.billMaturityDate.length() > 0">-->
<!--      <![CDATA[AND MA.BILL_MATURITY_DATE <= #{baseParam.billMaturityDate}]]>-->
<!--    </if>-->
<!--    order by MA.BILL_MATURITY_DATE asc,MA.BILL_NO asc-->
<!--  </select>-->

  <select id="queryMbAdRegisterByDateSplit" parameterType="java.util.Map" resultType="java.util.Map">
    <if test="_databaseId == 'oracle'">
      SELECT MIN (internal_key) START_KEY, MAX (internal_key) END_KEY ,count(1) ROW_COUNT FROM (
      select
      internal_key
      from RB_AD_REGISTER
      where LAST_CHANGE_DATE = #{runDate}
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      order BY LAST_CHANGE_DATE ASC)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>

  <select id="queryMbAdRegisterByDateForKeys" parameterType="java.util.Map" resultType="String">
    SELECT  internal_key
    FROM RB_AD_REGISTER
    where LAST_CHANGE_DATE = #{runDate}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and internal_key BETWEEN #{startKey} and #{endKey}
    ORDER BY LAST_CHANGE_DATE ASC
  </select>


  <select id="queryMbAdRegisterByDateDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister">
    select <include refid="Base_Column"/>
    from RB_AD_REGISTER
    where LAST_CHANGE_DATE = #{runDate}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and internal_key BETWEEN #{startKey} and #{endKey}
    order BY LAST_CHANGE_DATE ASC
  </select>

  <!-- 机构撤并更新机构号 -->
  <update id="updateByBranch" parameterType="java.util.Map">
    UPDATE RB_AD_REGISTER a
    SET tranBranch = #{newBranch}
    WHERE EXISTS (SELECT 1 FROM RB_acct b WHERE a.internal_key = b.internal_key AND b.tranBranch = #{oldBranch}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    )
      AND tranBranch = #{oldBranch}
  </update>

</mapper>
