<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">

  <!-- Created by shaosonga on 2017/03/22 10:25:12. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    select <include refid="Base_Column"/>
    from RB_DEP_RECOMMEND
    where INTERNAL_KEY = #{internalKey}
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    delete from RB_DEP_RECOMMEND
    where INTERNAL_KEY = #{internalKey}
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <delete id="deleteByTwoPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    delete from RB_DEP_RECOMMEND
    where INTERNAL_KEY = #{internalKey}
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    update RB_DEP_RECOMMEND
    <set>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="percent != null">
        PERCENT = #{percent},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="userName != null">
        COMPANY = #{userName}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateByTwoPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    update RB_DEP_RECOMMEND
    <set>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="percent != null">
        PERCENT = #{percent},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="userName != null">
        COMPANY = #{userName}
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    insert into RB_DEP_RECOMMEND
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="percent != null">
        PERCENT,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="userName != null">
        userName,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="percent != null">
        #{percent},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="userName != null">
        #{userName},
      </if>
    </trim>
  </insert>

  <insert id="insertOne" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    insert into RB_DEP_RECOMMEND
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="percent != null">
        PERCENT,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="percent != null">
        #{percent},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="userName != null">
        #{userName},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
    </trim>
  </insert>

  <select id="getListMbDepRecommend" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    select <include refid="Base_Column"/>
    from RB_DEP_RECOMMEND
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="seqGetListMbAcctByClientNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    select <include refid="Base_Column"/>
    from RB_DEP_RECOMMEND
    where INTERNAL_KEY = #{internalKey}
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getALLMbDepRecommend" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    select <include refid="Base_Column"/>
    from RB_DEP_RECOMMEND
    where 1=1
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="seqGetMbAcctByClientNo" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    select <include refid="Base_Column"/>
    from RB_DEP_RECOMMEND
    where INTERNAL_KEY = #{internalKey}
    <if test="seqNo != null">
      AND SEQ_NO = #{seqNo}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMaxSeqNoByinternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepRecommend">
    select max(SEQ_NO) SEQ_NO
    from RB_DEP_RECOMMEND
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
