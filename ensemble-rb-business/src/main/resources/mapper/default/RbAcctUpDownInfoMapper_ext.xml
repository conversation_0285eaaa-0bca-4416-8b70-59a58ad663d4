<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo">
  <!-- Created by admin on 2017/08/31 14:33:03. -->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo">
    select *
    from RB_ACCT_UP_DOWN_INFO
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo">
    select *
    from RB_ACCT_UP_DOWN_INFO
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="selectDownByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo">
    select *
    from RB_ACCT_UP_DOWN_INFO
    where INTERNAL_KEY = #{internalKey}
    AND UP_DOWN_TYPE = 'DOWN'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>




  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo">
    delete from RB_ACCT_UP_DOWN_INFO
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo">
    update RB_ACCT_UP_DOWN_INFO
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        ACCT_CCY = #{acctCcy},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="oldAcctClass != null">
        OLD_ACCT_CLASS = #{oldAcctClass},
      </if>
      <if test="acctClass != null">
        ACCT_CLASS = #{acctClass},
      </if>
      <if test="upDownType != null">
        UP_DOWN_TYPE = #{upDownType},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="branch != null">
        TRAN_BRANCH = #{branch},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo">
    insert into RB_ACCT_UP_DOWN_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="oldAcctClass != null">
        OLD_ACCT_CLASS,
      </if>
      <if test="acctClass != null">
        ACCT_CLASS,
      </if>
      <if test="upDownType != null">
        UP_DOWN_TYPE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="branch != null">
        TRAN_BRANCH,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="oldAcctClass != null">
        #{oldAcctClass},
      </if>
      <if test="acctClass != null">
        #{acctClass},
      </if>
      <if test="upDownType != null">
        #{upDownType},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
</mapper>
