<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingDefine">

    <select id="getUnderlyingDefine" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingDefine">
        select <include refid="Base_Column"/>
        from RB_DC_UNDERLYING_DEFINE
        where UNDERLYING_ID = #{underlyingId}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingDefine">
        delete from RB_DC_UNDERLYING_DEFINE
        where UNDERLYING_ID = #{underlyingId} and UNDERLYING_TYPE = #{underlyingType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingDefine">
        update RB_DC_UNDERLYING_DEFINE
        <set>
            <if test="underlyingType != null">
                UNDERLYING_TYPE = #{underlyingType},
            </if>
            <if test="underlyingId != null">
                UNDERLYING_ID = #{underlyingId},
            </if>
            <if test="underlyingName != null">
                UNDERLYING_NAME = #{underlyingName},
            </if>
            <if test="underlyingDesc != null">
                UNDERLYING_DESC = #{underlyingDesc},
            </if>
            <if test="company != null">
                COMPANY = #{company}
            </if>
        </set>
        where UNDERLYING_ID = #{underlyingId}
        and UNDERLYING_TYPE = #{underlyingType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>


    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcUnderlyingDefine">
        insert into RB_DC_UNDERLYING_DEFINE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="underlyingType != null">
                UNDERLYING_TYPE,
            </if>
            <if test="underlyingId != null">
                UNDERLYING_ID,
            </if>
            <if test="underlyingName != null">
                UNDERLYING_NAME,
            </if>
            <if test="underlyingDesc != null">
                UNDERLYING_DESC,
            </if>
            <if test="company != null">
                COMPANY
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="underlyingType != null">
                #{underlyingType},
            </if>
            <if test="underlyingId != null">
                #{underlyingId},
            </if>
            <if test="underlyingName != null">
                #{underlyingName},
            </if>
            <if test="underlyingDesc != null">
                #{underlyingDesc},
            </if>
            <if test="company != null">
                #{company}
            </if>
        </trim>
    </insert>

</mapper>
