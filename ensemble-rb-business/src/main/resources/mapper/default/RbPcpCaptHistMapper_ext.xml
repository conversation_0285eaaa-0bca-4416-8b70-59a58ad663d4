<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCaptHist">

  <!-- Created by admin on 2018/08/01 13:55:34. -->
  <select id="getCaptHistForDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCaptHist">
    select
    <include refid="Base_Column"/>
    from RB_PCP_CAPT_HIST
    where INTERNAL_KEY = #{internalKey}
    <if test="startDate !=  null">
      AND TRAN_DATE between #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getCaptHist" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCaptHist">
    select
    <include refid="Base_Column"/>
    from RB_PCP_CAPT_HIST
    where INTERNAL_KEY = #{internalKey}
    and PCP_GROUP_ID = #{pcpGroupId}
    and CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectBySeqNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCaptHist">
    select
    <include refid="Base_Column"/>
    from RB_PCP_CAPT_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
