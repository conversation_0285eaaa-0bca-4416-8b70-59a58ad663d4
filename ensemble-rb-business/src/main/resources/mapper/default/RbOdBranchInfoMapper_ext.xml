<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOdBranchInfo">
        <update id="updateByBranch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOdBranchInfo">
        update RB_OD_BRANCH_INFO
        <set>
            USED_AMT = #{userdAmt}
        </set>
        <where>
            <if test="branch != null and branch.length() > 0">
                AND BRANCH = #{branch}
            </if>
        </where>
    </update>

</mapper>
