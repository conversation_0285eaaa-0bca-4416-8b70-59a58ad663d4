<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.bc.repository.BranchChange">
    <update id="updateRbAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and ACCT_BRANCH = #{oldBranch} and SOURCE_MODULE != 'GL'  and   ACCT_STATUS != 'C'
    </update>

    <update id="updateRbAcctApplyBranchByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT
        SET APPLY_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and APPLY_BRANCH = #{oldBranch} and SOURCE_MODULE != 'GL'  and   ACCT_STATUS != 'C'
    </update>

    <update id="updateRbBaseAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_BASE_ACCT
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and ACCT_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbBaseAcctApplyBranchByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_BASE_ACCT
        SET
        APPLY_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and APPLY_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbVoucherLostByTranBranch" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and TRAN_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbVoucherLostUnchainBranchByTranBranch" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET
        UNCHAIN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and UNCHAIN_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbAgreementByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT
        SET TRAN_BRANCH = #{newBranch}
        WHERE AGREEMENT_KEY between #{startKey} and #{endKey} and TRAN_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbAgreementBySignBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT
        SET SIGN_BRANCH = #{newBranch}
        WHERE AGREEMENT_KEY between #{startKey} and #{endKey} and SIGN_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbAdRegisterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and TRAN_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbAdRegisterOpenBranchByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET
        OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey} and OPEN_BRANCH = #{oldBranch}
    </update>

    <update id="updateRbRestraintsByTranBranch" parameterType="java.util.Map">
        UPDATE RB_RESTRAINTS
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY between #{startKey} and #{endKey}  and TRAN_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbAcctClientRelationByTranBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_CLIENT_RELATION
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and ACCT_BRANCH = #{oldBranch}
    </update>

    <update id="updateProdRbAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT
        SET ACCT_BRANCH = #{newBranch}
        WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbAcctApplyBranchByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT
        SET
        APPLY_BRANCH = #{newBranch}
        WHERE PROD_TYPE = #{prodType} AND APPLY_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="updateProdRbBaseAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_BASE_ACCT
        SET ACCT_BRANCH = #{newBranch}
        WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbBaseAcctApplyBranchByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_BASE_ACCT
        SET
        APPLY_BRANCH = #{newBranch}
        WHERE PROD_TYPE = #{prodType} AND APPLY_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="updateProdRbVoucherLostByTranBranch" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET TRAN_BRANCH = #{newBranch}
        WHERE LOST_KEY IN(
        SELECT INTERNAL_KEY FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
        )
    </update>
    <update id="updateProdRbVoucherLostUnchainBranchByTranBranch" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET
        UNCHAIN_BRANCH = #{newBranch}
        WHERE LOST_KEY IN(
        SELECT INTERNAL_KEY FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
        )
    </update>

    <update id="updateProdRbAgreementByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT
        SET TRAN_BRANCH = #{newBranch}
        WHERE AGREEMENT_KEY IN(
        SELECT INTERNAL_KEY FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND TRAN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
        )
    </update>

    <update id="updateProdRbAdRegisterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY IN(
        SELECT INTERNAL_KEY FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
        )
    </update>
    <update id="updateProdRbAdRegisterOpenBranchByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET
        OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY IN(
        SELECT INTERNAL_KEY FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
        )
    </update>
    <update id="updateProdRbRestraintsByTranBranch" parameterType="java.util.Map">
        UPDATE RB_RESTRAINTS
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY IN(
        SELECT INTERNAL_KEY FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
        )
    </update>

    <update id="updateProdRbAcctClientRelationByTranBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_CLIENT_RELATION
        SET ACCT_BRANCH = #{newBranch},
        WHERE INTERNAL_KEY IN(
        SELECT INTERNAL_KEY FROM RB_ACCT_CLIENT_RELATION WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH = #{oldBranch}
        )
    </update>

    <update id="singleupdateRbAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and ACCT_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo} and SOURCE_MODULE != 'GL'
    </update>

    <update id="singleupdateRbAcctApplyBranchByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT
        SET APPLY_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and APPLY_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}  and SOURCE_MODULE != 'GL'
    </update>

    <update id="singleupdateRbBaseAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_BASE_ACCT
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and ACCT_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbBaseAcctApplyBranchByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_BASE_ACCT
        SET
        APPLY_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and APPLY_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbVoucherLostByTranBranch" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET TRAN_BRANCH = #{newBranch}
        WHERE LOST_KEY = #{internalKey} and TRAN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbVoucherLostUnchainBranchByTranBranch" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET UNCHAIN_BRANCH = #{newBranch}
        WHERE LOST_KEY = #{internalKey} and UNCHAIN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbAgreementByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT
        SET TRAN_BRANCH = #{newBranch}
        WHERE AGREEMENT_KEY = #{internalKey} and TRAN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbAgreementBySignBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT
        SET SIGN_BRANCH = #{newBranch}
        WHERE AGREEMENT_KEY = #{internalKey} and SIGN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbAdRegisterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and TRAN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbAdRegisterOpenBranchByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET
        OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and OPEN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbRestraintsByTranBranch" parameterType="java.util.Map">
        UPDATE RB_RESTRAINTS
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and TRAN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbAcctClientRelationByTranBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_CLIENT_RELATION
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and ACCT_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbAcctEventRegisterByBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_EVENT_REGISTER
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and ACCT_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbAgreementImpoundByBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT_IMPOUND
        SET Tran_Branch = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>


    <update id="updateRbAgreementSupplementByBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT_SUPPLEMENT
        SET Tran_Branch = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbAgreementWdlByBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT_WDL
        SET Tran_Branch = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbAdRegisterByBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET Tran_Branch = #{newBranch},OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbAdSettleByBranch" parameterType="java.util.Map">
        UPDATE RB_AD_SETTLE
        SET Tran_Branch = #{newBranch},BAIL_OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbAdRegisterHistByBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER_HIST
        SET Tran_Branch = #{newBranch},ACCEPT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbBatchChangeByBranch" parameterType="java.util.Map">
        UPDATE RB_BATCH_CHARGE
        SET Tran_Branch = #{newBranch},ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbPrecontractByBranch" parameterType="java.util.Map">
        UPDATE RB_PRECONTRACT
        SET Tran_Branch = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbDcChangeInfoByBranch" parameterType="java.util.Map">
        UPDATE RB_DC_CHANGE_INFO
        SET ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and ACCT_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbDcTohonorRecByBranch" parameterType="java.util.Map">
        UPDATE RB_DC_TOHONOR_REC_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and Tran_Branch= #{oldBranch}
    </update>

    <update id="updateRbDcPrecontractByBranch" parameterType="java.util.Map">
        UPDATE RB_DC_PRECONTRACT
        SET PRECONTRACT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and PRECONTRACT_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbOpenCloseRegByOpenBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET OPEN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and OPEN_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbOpenCloseRegByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET ACCT_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and ACCT_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbOpenCloseRegByTranBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbPrecontractByTranBranch" parameterType="java.util.Map">
        UPDATE RB_PRECONTRACT
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbApplyAcctInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_APPLY_ACCT_INFO
        SET ORDER_BRANCH = #{newBranch}
        WHERE  ORDER_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbApprLetterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_APPR_LETTER
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbIntSplitByTranBranch" parameterType="java.util.Map">
        UPDATE RB_INT_SPLIT
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbSettleCardRealByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_REAL
        SET ACCT_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO
        IN(
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and  ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbSettleCardRealByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_REAL
        SET TRAN_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO
        IN(
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and  TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbSettleCardPresetAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_PRESET_ACCT
        SET ACCT_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO
        IN(
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and  ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbSettleCardPresetAcctByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_PRESET_ACCT
        SET TRAN_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO
        IN(
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and  TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbFinancialAcctRegisterByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_FINANCIAL_ACCT_REGISTER
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE PARENT_INTERNAL_KEY  between #{startKey} and #{endKey}  and  TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbGlWriteOffAccountRegisterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_GL_WRITE_OFF_ACCOUNT
        SET TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE BASE_ACCT_NO
        IN(
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and  TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbImpoundInfoRepositoryByTranBranch" parameterType="java.util.Map">
        UPDATE RB_IMPOUND_INFO
        SET TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbAcctDossRegRepositoryByWaitdossBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        WAITDOSS_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and WAITDOSS_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbAcctDossRegRepositoryByWithdrawalBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        WITHDRAWAL_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and WITHDRAWAL_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbAcctDossRegRepositoryByDossBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        DOSS_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and DOSS_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbOsdServChargeByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        ACCT_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbOsdServChargeByTranBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbNewStayAcctVerificationByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET TRAN_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and TRAN_BRANCH = #{oldBranch}
    </update>
    <update id="updateRbNewStayAcctVerificationHomeByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET HOME_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and HOME_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbNewStayAcctVerificationCardByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbNewStayAcctVerificationHomeCardByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET HOME_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and HOME_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbSettleCardCollectInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_COLLECT_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and TRAN_BRANCH = #{oldBranch}
    </update>
    <update id="updateRbSettleCardHolderInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_HOLDER_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}
        )  and TRAN_BRANCH = #{oldBranch}
    </update>
    <update id="updateRbAcctSettleHistBySettleBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_SETTLE_HIST
        SET SETTLE_BRANCH = #{newBranch}
        WHERE
        SETTLE_ACCT_INTERNAL_KEY  between #{startKey} and #{endKey}  and  SETTLE_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbAcctUpDownInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_UP_DOWN_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE
        INTERNAL_KEY  between #{startKey} and #{endKey}  and  TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbDepositCertRecByTranBranch" parameterType="java.util.Map">
        UPDATE RB_DEPOSIT_CERT_REC
        SET TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRbDepositCertRecHistByTranBranch" parameterType="java.util.Map">
        UPDATE RB_DEPOSIT_CERT_REC_INFO
        SET TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateRcAllListByTranBranch" parameterType="java.util.Map">
        UPDATE RC_ALL_LIST
        SET TRAN_BRANCH = #{newBranch}
        WHERE DATA_VALUE IN
        (SELECT  BASE_ACCT_NO FROM RB_ACCT_CLIENT_RELATION WHERE INTERNAL_KEY between #{startKey} and #{endKey} AND ACCT_BRANCH = #{oldBranch})
        AND TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateCdCardPrevByTranCardBranch" parameterType="java.util.Map">
        UPDATE CD_CARD_PREV
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN
        (SELECT CARD_NO FROM RB_ACCT_CLIENT_RELATION WHERE INTERNAL_KEY between #{startKey} and #{endKey} AND ACCT_BRANCH = #{oldBranch})
        AND TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbVoucherLostByTranBranch1" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET
        TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE LOST_KEY  =#{internalKey} AND PROD_TYPE = #{prodType} AND TRAN_BRANCH =#{oldBranch}   and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbApprLetterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_APPR_LETTER
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  =#{internalKey} AND TRAN_BRANCH =#{oldBranch}   and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbIntSplitByTranBranch" parameterType="java.util.Map">
        UPDATE RB_INT_SPLIT
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  =#{internalKey} AND PROD_TYPE = #{prodType} AND TRAN_BRANCH =#{oldBranch}   and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbSettleCardRealByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_REAL
        SET
        ACCT_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        AND ACCT_CCY = #{acctCcy}
        AND PROD_TYPE = #{prodType}
        AND ACCT_SEQ_NO = #{acctSeqNo}
        and CLIENT_NO = #{clientNo}
        and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbSettleCardRealByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_REAL
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        AND ACCT_CCY = #{acctCcy}
        AND PROD_TYPE = #{prodType}
        AND ACCT_SEQ_NO = #{acctSeqNo}
        and CLIENT_NO = #{clientNo}
        and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbSettleCardPresetAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_PRESET_ACCT
        SET
        ACCT_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        and CLIENT_NO = #{clientNo}
        and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbSettleCardPresetAcctByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_PRESET_ACCT
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        and CLIENT_NO = #{clientNo}
        and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbFinancialAcctRegisterByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_FINANCIAL_ACCT_REGISTER
        SET
        TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE PARENT_INTERNAL_KEY= #{internalKey} AND CLIENT_NO = #{clientNo}
        AND  TRAN_BRANCH =#{oldBranch}
    </update>
    <update id="updateProdRbOpenCloseRegByOpenBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET
        OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and PROD_TYPE = #{prodType}  AND  OPEN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbOpenCloseRegByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET
        ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and ACCT_BRANCH = #{oldBranch} AND PROD_TYPE = #{prodType} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbOpenCloseRegByTranBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and TRAN_BRANCH = #{oldBranch} AND PROD_TYPE = #{prodType} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbGlWriteOffAccountRegisterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_GL_WRITE_OFF_ACCOUNT
        SET TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE BASE_ACCT_NO= #{baseAcctNo} AND CCY= #{acctCcy} AND TRAN_BRANCH =#{oldBranch} AND CLIENT_NO=#{clientNo}
    </update>
    <update id="updateProdRbImpoundInfoRepositoryByTranBranch" parameterType="java.util.Map">
        UPDATE RB_IMPOUND_INFO
        SET TRAN_BRANCH = #{newBranch}
        <!--现场差异没有该字段LAST_CHANGE_DATE-->
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  AND  PROD_TYPE = #{prodType} AND TRAN_BRANCH =#{oldBranch}  AND CLIENT_NO=#{clientNo}
    </update>

    <update id="updateProdRbAcctDossRegRepositoryByWaitdossBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        WAITDOSS_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  AND  PROD_TYPE = #{prodType} and CLIENT_NO = #{clientNo}   and WAITDOSS_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbAcctDossRegRepositoryByWithdrawalBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        WITHDRAWAL_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  AND  PROD_TYPE = #{prodType} and CLIENT_NO = #{clientNo}   and WITHDRAWAL_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbAcctDossRegRepositoryByDossBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        DOSS_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  AND  PROD_TYPE = #{prodType} and CLIENT_NO = #{clientNo}  and DOSS_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbOsdServChargeByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        ACCT_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE INTERNAL_KEY =#{internalKey}  and CLIENT_NO = #{clientNo}   and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbOsdServChargeByTranBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE INTERNAL_KEY =#{internalKey}  and CLIENT_NO = #{clientNo}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdRbPrecontractByTranBranch" parameterType="java.util.Map">
        UPDATE RB_PRECONTRACT
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE INTERNAL_KEY = #{internalKey}  and TRAN_BRANCH= #{oldBranch}  AND PROD_TYPE = #{prodType} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbNewStayAcctVerificationByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET TRAN_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbNewStayAcctVerificationHomeByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET HOME_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch} and CLIENT_NO = #{clientNo}
        )  and HOME_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbNewStayAcctVerificationCardByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbNewStayAcctVerificationHomeCardByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET HOME_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch} and CLIENT_NO = #{clientNo}
        )  and HOME_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbSettleCardCollectInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_COLLECT_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbSettleCardHolderInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_HOLDER_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbAcctUpDownInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_UP_DOWN_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey}  AND TRAN_BRANCH= #{oldBranch}  AND PROD_TYPE = #{prodType} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbAgreementBySignBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT
        SET SIGN_BRANCH = #{newBranch}
        WHERE AGREEMENT_KEY =#{internalKey} AND SIGN_BRANCH =#{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbDepositCertRecByTranBranch" parameterType="java.util.Map">
        UPDATE RB_DEPOSIT_CERT_REC
        SET
        TRAN_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY = #{internalKey}  AND TRAN_BRANCH= #{oldBranch} AND CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbDepositCertRecHistByTranBranch" parameterType="java.util.Map">
        UPDATE RB_DEPOSIT_CERT_REC_INFO
        SET
        TRAN_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY = #{internalKey}  AND TRAN_BRANCH= #{oldBranch} AND CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRbAcctSettleHistBySettleBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_SETTLE_HIST
        SET
        SETTLE_BRANCH = #{newBranch}
        WHERE SETTLE_ACCT_INTERNAL_KEY = #{internalKey}  AND SETTLE_BRANCH= #{oldBranch} AND CLIENT_NO = #{clientNo}
    </update>
    <update id="updateProdRcAllListByTranBranch" parameterType="java.util.Map">
        UPDATE RC_ALL_LIST
        SET TRAN_BRANCH = #{newBranch}
        WHERE DATA_VALUE
        IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT_CLIENT_RELATION WHERE INTERNAL_KEY = #{internalKey} AND PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch}  and CLIENT_NO = #{clientNo}
        ) AND TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="updateProdCdCardPrevByTranBranch" parameterType="java.util.Map">
        UPDATE CD_CARD_PREV
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO
        IN(
        SELECT CARD_NO FROM RB_ACCT_CLIENT_RELATION WHERE INTERNAL_KEY = #{internalKey}  and CLIENT_NO = #{clientNo}
        ) AND TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbVoucherLostByTranBranch1" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET TRAN_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE LOST_KEY =#{cardNo}  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbApprLetterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_APPR_LETTER
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  =#{internalKey} AND TRAN_BRANCH =#{oldBranch}   and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbIntSplitByTranBranch" parameterType="java.util.Map">
        UPDATE RB_INT_SPLIT
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  =#{internalKey} AND TRAN_BRANCH =#{oldBranch}   and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbSettleCardRealByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_REAL
        SET
        ACCT_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        AND ACCT_CCY = #{acctCcy}
        AND PROD_TYPE = #{prodType}
        AND ACCT_SEQ_NO = #{acctSeqNo}
        and CLIENT_NO = #{clientNo}
        and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbSettleCardRealByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_REAL
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        AND ACCT_CCY = #{acctCcy}
        AND PROD_TYPE = #{prodType}
        AND ACCT_SEQ_NO = #{acctSeqNo}
        and CLIENT_NO = #{clientNo}
        and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbSettleCardPresetAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_PRESET_ACCT
        SET
        ACCT_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        and CLIENT_NO = #{clientNo}
        and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbSettleCardPresetAcctByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_PRESET_ACCT
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE
        BASE_ACCT_NO = #{baseAcctNo}
        and CLIENT_NO = #{clientNo}
        and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbFinancialAcctRegisterByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_FINANCIAL_ACCT_REGISTER
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE PARENT_INTERNAL_KEY  =#{internalKey}  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbGlWriteOffAccountRegisterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_GL_WRITE_OFF_ACCOUNT
        SET TRAN_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE BASE_ACCT_NO= #{baseAcctNo} AND CCY= #{acctCcy} AND TRAN_BRANCH =#{oldBranch} AND CLIENT_NO=#{clientNo}
    </update>
    <update id="singleupdateRbImpoundInfoRepositoryByTranBranch" parameterType="java.util.Map">
        UPDATE RB_IMPOUND_INFO
        SET TRAN_BRANCH = #{newBranch}
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>

    <update id="singleupdateRbAcctDossRegRepositoryByWaitdossBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        WAITDOSS_BRANCH = #{newBranch}
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  and CLIENT_NO = #{clientNo}   and WAITDOSS_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbAcctDossRegRepositoryByWithdrawalBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        WITHDRAWAL_BRANCH = #{newBranch}
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  AND  CLIENT_NO = #{clientNo}   and WITHDRAWAL_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbAcctDossRegRepositoryByDossBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_DOSS_REG
        SET
        DOSS_BRANCH = #{newBranch}
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY =#{internalKey}  AND  CLIENT_NO = #{clientNo}  and DOSS_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbOpenCloseRegByOpenBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET
        OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}  AND  OPEN_BRANCH = #{oldBranch}
    </update>
    <update id="singleupdateRbOpenCloseRegByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET
        ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}  AND  ACCT_BRANCH = #{oldBranch}
    </update>
    <update id="singleupdateRbOpenCloseRegByTranBranch" parameterType="java.util.Map">
        UPDATE RB_OPEN_CLOSE_REG
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}  AND  TRAN_BRANCH = #{oldBranch}
    </update>
    <update id="singleupdateRbOsdServChargeByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        ACCT_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE INTERNAL_KEY =#{internalKey}  and CLIENT_NO = #{clientNo}   and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbOsdServChargeByTranBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE INTERNAL_KEY =#{internalKey}  and CLIENT_NO = #{clientNo}   and TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="singlupdateRbPrecontractByTranBranch" parameterType="java.util.Map">
        UPDATE RB_PRECONTRACT
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>
        WHERE INTERNAL_KEY = #{internalKey}  and TRAN_BRANCH= #{oldBranch}  and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbNewStayAcctVerificationByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET TRAN_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH = #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbNewStayAcctVerificationHomeByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET HOME_BRANCH = #{newBranch}
        WHERE BASE_ACCT_NO IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}
        )  and HOME_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbNewStayAcctVerificationCardByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbNewStayAcctVerificationHomeCardByTranBranch" parameterType="java.util.Map">
        UPDATE RB_NEWXSTAY_ACCT_VERIFICATION
        SET HOME_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}
        )  and HOME_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbSettleCardCollectInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_COLLECT_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbSettleCardHolderInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_SETTLE_CARD_HOLDER_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO IN (
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY = #{internalKey} and CLIENT_NO = #{clientNo}
        )  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbAcctSettleHistByBranchId" parameterType="java.util.Map">
        UPDATE RB_ACCT_SETTLE_HIST
        <!--        SET BRANCH_ID = #{newBranch}-->
        <!--        WHERE INTERNAL_KEY  =#{internalKey}  and BRANCH_ID= #{oldBranch} and CLIENT_NO = #{clientNo}-->
        SET
        SETTLE_BRANCH = #{newBranch}
        WHERE SETTLE_ACCT_INTERNAL_KEY = #{internalKey}  AND SETTLE_BRANCH= #{oldBranch} AND CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbAcctUpDownInfoByTranBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_UP_DOWN_INFO
        SET TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  =#{internalKey}  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbDepositCertRecByTranBranch" parameterType="java.util.Map">
        UPDATE RB_DEPOSIT_CERT_REC
        SET TRAN_BRANCH = #{newBranch}
        <!--<if test = "lastChangeDate !=null">
            ,LAST_CHANGE_DATE = #{lastChangeDate}
        </if>-->
        WHERE INTERNAL_KEY  =#{internalKey}  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRbDepositCertRecHistByTranBranch" parameterType="java.util.Map">
        UPDATE RB_DEPOSIT_CERT_REC_INFO
        SET TRAN_BRANCH = #{newBranch}
        <!-- <if test = "lastChangeDate !=null">
             ,LAST_CHANGE_DATE = #{lastChangeDate}
         </if>-->
        WHERE INTERNAL_KEY  =#{internalKey}  and TRAN_BRANCH= #{oldBranch} and CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateRcAllListByTranBranch" parameterType="java.util.Map">
        UPDATE RC_ALL_LIST
        SET TRAN_BRANCH = #{newBranch}
        WHERE DATA_VALUE
        IN (
        SELECT BASE_ACCT_NO FROM RB_ACCT_CLIENT_RELATION WHERE INTERNAL_KEY = #{internalKey}  AND ACCT_BRANCH =#{oldBranch}  and CLIENT_NO = #{clientNo}
        ) AND TRAN_BRANCH= #{oldBranch}  AND CLIENT_NO = #{clientNo}
    </update>
    <update id="singleupdateCdCardPrevByTranBranch" parameterType="java.util.Map">
        UPDATE CD_CARD_PREV
        SET TRAN_BRANCH = #{newBranch}
        WHERE CARD_NO
        IN(
        SELECT CARD_NO FROM RB_ACCT_CLIENT_RELATION WHERE INTERNAL_KEY = #{internalKey}
        AND ACCT_BRANCH =#{oldBranch}  AND CLIENT_NO = #{clientNo}
        ) AND TRAN_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbGlHistByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        ACCT_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY =#{internalKey}  and CLIENT_NO = #{clientNo}   and ACCT_BRANCH= #{oldBranch}
    </update>
    <update id="singleupdateRbGlHistByTranBranch" parameterType="java.util.Map">
        UPDATE RB_OSD_SERV_CHARGE
        SET
        TRAN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY =#{internalKey}  and CLIENT_NO = #{clientNo}   and TRAN_BRANCH= #{oldBranch}
    </update>
</mapper>