<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal">


    <select id="sumOneAcctBalance" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal">
        <if test="_databaseId == 'mysql'">
            select
            a.ACCT_BRANCH as BRANCH,
            a.ACCT_CCY as CCY,
            a.PROD_TYPE,
            ifnull(ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            'BAL' as AMT_TYPE,
            ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
            SUM(b.TOTAL_AMOUNT_PREV) as BALANCE,
            'RB' as <PERSON>Y<PERSON><PERSON>_ID,
            #{yesterday} as LAST_CHANGE_DATE,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID
            from RB_ACCT a,RB_ACCT_BALANCE b
            where a.INTERNAL_KEY = b.INTERNAL_KEY
            AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
            AND concat(a.ACCT_BRANCH,a.PROD_TYPE,a.ACCT_CCY,ifnull(a.ACCOUNTING_STATUS,'ZHC')) = #{acctKey}
            AND a.SOURCE_MODULE != 'CL'
            AND ifnull(b.TOTAL_AMOUNT_PREV,0) <![CDATA[ <> ]]> 0
            AND a.ACCT_REAL_FLAG = 'Y'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND a.COMPANY = #{company}
            </if>
            group by a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER
        </if>
        <if test="_databaseId == 'oracle'">
            select
            a.ACCT_BRANCH as BRANCH,
            a.ACCT_CCY as CCY,
            a.PROD_TYPE,
            nvl(ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            'BAL' as AMT_TYPE,
            nvl(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
            SUM(b.TOTAL_AMOUNT_PREV) as BALANCE,
            'RB' as SYSTEM_ID,
            #{yesterday} as LAST_CHANGE_DATE,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID
            from RB_ACCT a,RB_ACCT_BALANCE b
            where a.INTERNAL_KEY = b.INTERNAL_KEY
            AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
            AND a.ACCT_BRANCH||a.PROD_TYPE||a.ACCT_CCY||nvl(a.ACCOUNTING_STATUS,'ZHC') = #{acctKey}
            AND a.SOURCE_MODULE != 'CL'
            AND nvl(b.TOTAL_AMOUNT_PREV,0) <![CDATA[ <> ]]> 0
            AND a.ACCT_REAL_FLAG = 'Y'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND a.COMPANY = #{company}
            </if>
            group by a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER
        </if>

    </select>

    <select id="sumOneAcctInt" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal">
        <if test="_databaseId == 'mysql'">
            select
            a.ACCT_BRANCH as BRANCH,
            a.ACCT_CCY as CCY,
            a.PROD_TYPE,
            ifnull(a.ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            'INT' as AMT_TYPE,
            ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
            SUM(ifnull(b.INT_ACCRUED_PREV, 0)+ifnull(b.INT_ADJ_PREV, 0)-ifnull(b.DISCNT_INT_PREV, 0)+ifnull(c.PAST_INTEREST, 0)) as
            BALANCE,
            'RB' as SYSTEM_ID,
            #{yesterday} as LAST_CHANGE_DATE,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID
            from
            RB_ACCT a
            inner join RB_ACCT_INT_DETAIL b on a.INTERNAL_KEY = b.INTERNAL_KEY
            left join RB_DELAY_PAY_INT c on a.internal_key = c.internal_key
            where concat(a.ACCT_BRANCH,a.PROD_TYPE,a.ACCT_CCY,ifnull(a.ACCOUNTING_STATUS,'ZHC')) = #{acctKey}
            AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
            AND ( b.INT_ACCRUED_PREV <![CDATA[ <> ]]> 0 or b.INT_ADJ_PREV <![CDATA[ <> ]]> 0
            or b.DISCNT_INT_PREV <![CDATA[ <> ]]> 0 or c.PAST_INTEREST<![CDATA[ <> ]]> 0 )
            AND a.ACCT_REAL_FLAG = 'Y'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND a.COMPANY = #{company}
            </if>
            group by
            a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER
        </if>
        <if test="_databaseId == 'oracle'">
            select
            a.ACCT_BRANCH as BRANCH,
            a.ACCT_CCY as CCY,
            a.PROD_TYPE,
            nvl(a.ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            'INT' as AMT_TYPE,
            nvl(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
            SUM(nvl(b.INT_ACCRUED_PREV, 0)+nvl(b.INT_ADJ_PREV, 0)-nvl(b.DISCNT_INT_PREV, 0)+nvl(c.PAST_INTEREST, 0)) as
            BALANCE,
            'RB' as SYSTEM_ID,
            #{yesterday} as LAST_CHANGE_DATE,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID
            from
            RB_ACCT a
            inner join RB_ACCT_INT_DETAIL b on a.INTERNAL_KEY = b.INTERNAL_KEY
            left join RB_DELAY_PAY_INT c on a.internal_key = c.internal_key
            where a.ACCT_BRANCH||a.PROD_TYPE||a.ACCT_CCY||nvl(a.ACCOUNTING_STATUS,'ZHC') = #{acctKey}
            AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
            AND ( b.INT_ACCRUED_PREV <![CDATA[ <> ]]> 0 or b.INT_ADJ_PREV <![CDATA[ <> ]]> 0
            or b.DISCNT_INT_PREV <![CDATA[ <> ]]> 0 or c.PAST_INTEREST<![CDATA[ <> ]]> 0 )
            AND a.ACCT_REAL_FLAG = 'Y'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND a.COMPANY = #{company}
            </if>
            group by
            a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER
        </if>
    </select>

    <update id="updateDealFlag2" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal">
        UPDATE RB_PROD_ACCT_BAL
        SET DEAL_FLAG = '2'
        WHERE BRANCH = #{branch}
        and CCY = #{ccy}
        and PROD_TYPE = #{prodType}
        and ACCOUNTING_STATUS = #{accountingStatus}
        and AMT_TYPE = #{amtType}
        and LAST_CHANGE_DATE = #{lastChangeDate}
    </update>


    <select id="selectSumProdAcctBalNoDeal" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal">
        <if test="_databaseId == 'mysql'">
            select a.BRANCH, a.CCY, a.PROD_TYPE, ifnull(a.ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            sum(a.balance) balance,a.AMT_TYPE,a.LAST_CHANGE_DATE,a.COMPANY
            from rb_prod_acct_bal a
            where a.LAST_CHANGE_DATE = #{lastChangeDate}
            AND ifnull(DEAL_FLAG,'1') = #{dealFlag}
            group by a.BRANCH, a.CCY, a.PROD_TYPE, a.ACCOUNTING_STATUS, a.AMT_TYPE,a.LAST_CHANGE_DATE,a.COMPANY
        </if>
        <if test="_databaseId == 'oracle'">
            select a.BRANCH, a.CCY, a.PROD_TYPE, nvl(a.ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            sum(a.balance) balance,a.AMT_TYPE,a.LAST_CHANGE_DATE,a.COMPANY
            from rb_prod_acct_bal a
            where a.LAST_CHANGE_DATE = #{lastChangeDate}
            AND nvl(DEAL_FLAG,'1') = #{dealFlag}
            group by a.BRANCH, a.CCY, a.PROD_TYPE, a.ACCOUNTING_STATUS, a.AMT_TYPE,a.LAST_CHANGE_DATE,a.COMPANY
        </if>
    </select>

    <select id="selectLastLastRunDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal"
            resultType="java.util.Date" >
        select MAX(LAST_CHANGE_DATE)
        from rb_prod_acct_bal
        where <![CDATA[ LAST_CHANGE_DATE < #{lastChangeDate,jdbcType=DATE} ]]>
    </select>
</mapper>
