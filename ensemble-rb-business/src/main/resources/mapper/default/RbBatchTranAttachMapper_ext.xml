<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTranAttach">
  <!-- Created by admin on 2018/05/18 23:40:36. -->

  <select id="selectByBatchNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTranAttach">
    select *
    from RB_BATCH_TRAN_ATTACH
    where  BATCH_NO = #{batchNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbBatchTranAttachForList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTranAttach">
    select *
    from RB_BATCH_TRAN_ATTACH
    <where>
      <trim suffixOverrides="AND">
        <if test="batchNo != null and batchNo != '' ">
          BATCH_NO = #{batchNo} AND
        </if>
        <if test="branchId != null and branchId != '' ">
          BATCH_NO in (SELECT DISTINCT BATCH_NO FROM RB_BATCH_TRAN_ATTACH WHERE ATTR_KEY = 'TRAN_BRANCH' AND ATTR_VALUE = #{branchId}) AND
        </if>
        <if test="tranDate != null and tranDate.length() > 0">
          BATCH_NO IN (SELECT DISTINCT BATCH_NO FROM RB_BATCH_TRAN_ATTACH WHERE ATTR_KEY = 'TRAN_DATE' AND ATTR_VALUE = #{tranDate}) AND
        </if>
        <if test="busiType != null and busiType.length() > 0">
          BATCH_NO IN (SELECT DISTINCT BATCH_NO FROM RB_BATCH_TRAN_ATTACH WHERE ATTR_KEY = 'BUSI_TYPE' AND ATTR_VALUE = #{busiType}) AND
        </if>
        <if test="totalNum != null and totalNum.length() > 0">
          BATCH_NO IN (SELECT DISTINCT BATCH_NO FROM RB_BATCH_TRAN_ATTACH WHERE ATTR_KEY = 'TOTAL_NUM' AND ATTR_VALUE = #{totalNum}) AND
        </if>
        <if test="totalAmt != null and totalAmt.length() > 0">
          BATCH_NO IN (SELECT DISTINCT BATCH_NO FROM RB_BATCH_TRAN_ATTACH WHERE ATTR_KEY = 'TOTAL_AMT' AND ATTR_VALUE = #{totalAmt}) AND
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
          BATCH_NO IN (SELECT DISTINCT BATCH_NO FROM RB_BATCH_TRAN_ATTACH WHERE ATTR_KEY = 'BASE_ACCT_NO' AND ATTR_VALUE = #{baseAcctNo}) AND
        </if>
        <if test="corpAcctNo != null and corpAcctNo.length() > 0">
          BATCH_NO IN (SELECT DISTINCT BATCH_NO FROM RB_BATCH_TRAN_ATTACH WHERE ATTR_KEY = 'CORP_ACCT_NO' AND ATTR_VALUE = #{corpAcctNo}) AND
        </if>
        <if test="company != null and company != '' ">
          COMPANY = #{company} AND
        </if>
      </trim>
    </where>
  </select>
</mapper>
