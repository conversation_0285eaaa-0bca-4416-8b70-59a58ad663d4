<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
  <sql id="S_Column_List">
    s.AMT_TYPE,
    s.AUTO_BLOCKING,
    s.BANK_IN_OUT,
    s.COMPANY,
    s.DAC_VALUE,
    s.EVENT_TYPE,
    s.FREEZE_TYPE,
    s.INTERNAL_KEY,
    s.SELF_SUPPORT_FLAG,
    s.PAYEE_BANK_CODE,
    s.PAYEE_BANK_NAME,
    s.PAY_REC_IND,
    s.PRIORITY,
    s.PROFIT_RATIO,
    s.REFERENCE,
    s.RES_SEQ_NO,
    s.SETTLE_ACCT_CCY,
    s.SETTLE_ACCT_CLASS,
    s.SETTLE_ACCT_INTERNAL_KEY,
    s.SETTLE_ACCT_NAME,
    s.SETTLE_ACCT_SEQ_NO,
    s.SETTLE_AMT,
    s.SETTLE_BANK_FLAG,
    s.SETTLE_BANK_NAME,
    s.SETTLE_BASE_ACCT_NO,
    s.SETTLE_BRANCH,
    s.SETTLE_CCY,
    s.SETTLE_CLIENT,
    s.SETTLE_METHOD,
    s.SETTLE_NO,
    s.SETTLE_PROD_TYPE,
    s.SETTLE_WEIGHT,
    s.SETTLE_XRATE,
    s.XRATE_ID,
    s.SETTLE_MOBILE_PHONE,
    s.TRAN_TIMESTAMP,
    s.TRAN_TYPE,
    s.TRUSTED_PAY_NO,
    s.USER_ID,
    s.LAST_CHANGE_USER_ID ,
    s.LAST_CHANGE_DATE
  </sql>
  <select id="selectBySettleNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Sep 10 18:46:06 CST 2015.
    -->
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where  INTERNAL_KEY = #{internalKey}
    and SETTLE_NO = #{settleNo}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getAcctAutoSettle" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Sep 10 18:46:06 CST 2015.
    -->
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    and SETTLE_ACCT_CLASS = 'AUS'
    and  SETTLE_METHOD = 'R'
    and PAY_REC_IND = 'REC'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getAcctAutoSettlePRI" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    WHERE SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo}
    and SETTLE_ACCT_CLASS = 'PRI'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getAcctAutoSettlePF" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where  INTERNAL_KEY = #{internalKey}
    and AMT_TYPE = 'PF'
    and  SETTLE_METHOD = 'R'
    and PAY_REC_IND = 'PAY'
    and EVENT_TYPE = 'TRA'
    and SETTLE_ACCT_CLASS = 'PF'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectBySettleAcctInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where SETTLE_ACCT_INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByMbSettleFourElement" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo}
    and SETTLE_PROD_TYPE = #{settleProdType}
    and SETTLE_ACCT_CCY = #{settleAcctCcy}
    <if test="settleAcctSeqNo != null">
      and SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectAcctSettleDefault" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    and (REFERENCE is null or REFERENCE = '')
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByReceiptNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
     and REFERENCE = #{reference}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Sep 10 18:46:06 CST 2015.
    -->
    insert into RB_ACCT_SETTLE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="settleNo != null">
        SETTLE_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="eventType != null">
        EVENT_TYPE,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="settleAcctClass != null">
        SETTLE_ACCT_CLASS,
      </if>
      <if test="settleMethod != null">
        SETTLE_METHOD,
      </if>
      <if test="payRecInd != null">
        PAY_REC_IND,
      </if>
      <if test="amtType != null">
        AMT_TYPE,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="settleClient != null">
        SETTLE_CLIENT,
      </if>
      <if test="settleBranch != null">
        SETTLE_BRANCH,
      </if>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY,
      </if>
      <if test="settleBaseAcctNo != null">
        SETTLE_BASE_ACCT_NO,
      </if>
      <if test="settleProdType != null">
        SETTLE_PROD_TYPE,
      </if>
      <if test="settleAcctCcy != null">
        SETTLE_ACCT_CCY,
      </if>
      <if test="settleAcctSeqNo != null">
        SETTLE_ACCT_SEQ_NO,
      </if>
      <if test="settleCcy != null">
        SETTLE_CCY,
      </if>
      <if test="settleAmt != null">
        SETTLE_AMT,
      </if>
      <if test="settleXrate != null">
        SETTLE_XRATE,
      </if>
      <if test="xrateId != null">
        XRATE_ID,
      </if>
      <if test="priority != null">
        PRIORITY,
      </if>
      <if test="settleWeight != null">
        SETTLE_WEIGHT,
      </if>
      <if test="autoBlocking != null">
        AUTO_BLOCKING,
      </if>
      <if test="trustedPayNo != null">
        TRUSTED_PAY_NO,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="dacValue != null">
        DAC_VALUE,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="settleBankFlag != null">
        SETTLE_BANK_FLAG,
      </if>
      <if test="settleBankName != null">
        SETTLE_BANK_NAME,
      </if>
      <if test="settleAcctName != null">
        SETTLE_ACCT_NAME,
      </if>
      <if test="profitRatio != null">
        PROFIT_RATIO,
      </if>
      <if test="selfSupportFlag != null">
        SELF_SUPPORT_FLAG,
      </if>
      <if test="payeeBankName != null">
        PAYEE_BANK_NAME,
      </if>
      <if test="payeeBankCode != null">
        PAYEE_BANK_CODE,
      </if>
      <if test="freezeType != null">
        FREEZE_TYPE,
      </if>
      <if test="bankInOut != null">
        BANK_IN_OUT,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="settleMobilePhone != null">
        SETTLE_MOBILE_PHONE,
      </if>
      <if test="createDate != null">
        CREATE_DATE
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="settleNo != null">
        #{settleNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="eventType != null">
        #{eventType},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="settleAcctClass != null">
        #{settleAcctClass},
      </if>
      <if test="settleMethod != null">
        #{settleMethod},
      </if>
      <if test="payRecInd != null">
        #{payRecInd},
      </if>
      <if test="amtType != null">
        #{amtType},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="settleClient != null">
        #{settleClient},
      </if>
      <if test="settleBranch != null">
        #{settleBranch},
      </if>
      <if test="settleAcctInternalKey != null">
        #{settleAcctInternalKey},
      </if>
      <if test="settleBaseAcctNo != null">
        #{settleBaseAcctNo},
      </if>
      <if test="settleProdType != null">
        #{settleProdType},
      </if>
      <if test="settleAcctCcy != null">
        #{settleAcctCcy},
      </if>
      <if test="settleAcctSeqNo != null">
        #{settleAcctSeqNo},
      </if>
      <if test="settleCcy != null">
        #{settleCcy},
      </if>
      <if test="settleAmt != null">
        #{settleAmt},
      </if>
      <if test="settleXrate != null">
        #{settleXrate},
      </if>
      <if test="xrateId != null">
        #{xrateId},
      </if>
      <if test="priority != null">
        #{priority},
      </if>
      <if test="settleWeight != null">
        #{settleWeight},
      </if>
      <if test="autoBlocking != null">
        #{autoBlocking},
      </if>
      <if test="trustedPayNo != null">
        #{trustedPayNo},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
      <if test="dacValue != null">
        #{dacValue},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="settleBankFlag != null">
        #{settleBankFlag},
      </if>
      <if test="settleBankName != null">
        #{settleBankName},
      </if>
      <if test="settleAcctName != null">
        #{settleAcctName},
      </if>
      <if test="profitRatio != null">
        #{profitRatio},
      </if>
      <if test="contributiveRatio != null">
        #{contributiveRatio},
      </if>
      <if test="isSelfSupportFlag != null">
        #{isSelfSupportFlag},
      </if>


      <if test="payeeBankName != null">
        #{payeeBankName},
      </if>
      <if test="payeeBankCode != null">
        #{payeeBankCode},
      </if>
      <if test="freezeType != null">
        #{freezeType},
      </if>
      <if test="bankInOut != null">
        #{bankInOut},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="lastChangeUserId != null">
        #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="settleMobilePhone != null">
        #{settleMobilePhone},
      </if>
      <if test="createDate != null">
        #{createDate}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Sep 10 18:46:06 CST 2015.
    -->
    update RB_ACCT_SETTLE
    <set>
      <if test="eventType != null">
        EVENT_TYPE = #{eventType},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="settleAcctClass != null">
        SETTLE_ACCT_CLASS = #{settleAcctClass},
      </if>
      <if test="settleMethod != null">
        SETTLE_METHOD = #{settleMethod},
      </if>
      <if test="payRecInd != null">
        PAY_REC_IND = #{payRecInd},
      </if>
      <if test="amtType != null">
        AMT_TYPE = #{amtType},
      </if>
      <if test="settleClient != null">
        SETTLE_CLIENT = #{settleClient},
      </if>
      <if test="settleBranch != null">
        SETTLE_BRANCH = #{settleBranch},
      </if>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey},
      </if>
      <if test="settleBaseAcctNo != null">
        SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo},
      </if>
      <if test="settleProdType != null">
        SETTLE_PROD_TYPE = #{settleProdType},
      </if>
      <if test="settleAcctCcy != null">
        SETTLE_ACCT_CCY = #{settleAcctCcy},
      </if>
      <if test="settleAcctSeqNo != null">
        SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo},
      </if>
      <if test="settleCcy != null">
        SETTLE_CCY = #{settleCcy},
      </if>
      <if test="settleAmt != null">
        SETTLE_AMT = #{settleAmt},
      </if>
      <if test="settleXrate != null">
        SETTLE_XRATE = #{settleXrate},
      </if>
      <if test="xrateId != null">
        XRATE_ID = #{xrateId},
      </if>
      <if test="priority != null">
        PRIORITY = #{priority},
      </if>

        SETTLE_WEIGHT = #{settleWeight},

      <if test="autoBlocking != null">
        AUTO_BLOCKING = #{autoBlocking},
      </if>
      <if test="trustedPayNo != null">
        TRUSTED_PAY_NO = #{trustedPayNo},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="dacValue != null">
        DAC_VALUE = #{dacValue},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="settleBankFlag != null">
        SETTLE_BANK_FLAG = #{settleBankFlag},
      </if>
      <if test="settleAcctName != null">
        SETTLE_ACCT_NAME = #{settleAcctName},
      </if>
      <if test="settleBankName != null">
        SETTLE_BANK_NAME = #{settleBankName},
      </if>


      <if test="profitRatio != null">
        PROFIT_RATIO = #{profitRatio},
      </if>
      <if test="selfSupportFlag != null">
        SELF_SUPPORT_FLAG = #{selfSupportFlag},
      </if>
      <if test="bankInOut != null">
        BANK_IN_OUT = #{bankInOut},
      </if>
      <if test="userId != null">
        USER_ID =  #{userId},
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE =  #{lastChangeDate},
      </if>
      <if test="settleMobilePhone != null">
        SETTLE_MOBILE_PHONE =  #{settleMobilePhone}
      </if>
    </set>
    where SETTLE_NO = #{settleNo}
      and INTERNAL_KEY = #{internalKey}
      <if test="clientNo != null and clientNo != ''">
       and CLIENT_NO = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateByRefNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    update RB_ACCT_SETTLE
    <set>
      <if test="eventType != null">
        EVENT_TYPE = #{eventType},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="settleAcctClass != null">
        SETTLE_ACCT_CLASS = #{settleAcctClass},
      </if>
      <if test="settleMethod != null">
        SETTLE_METHOD = #{settleMethod},
      </if>
      <if test="payRecInd != null">
        PAY_REC_IND = #{payRecInd},
      </if>
      <if test="amtType != null">
        AMT_TYPE = #{amtType},
      </if>
      <if test="settleClient != null">
        SETTLE_CLIENT = #{settleClient},
      </if>
      <if test="settleBranch != null">
        SETTLE_BRANCH = #{settleBranch},
      </if>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey},
      </if>
      <if test="settleBaseAcctNo != null">
        SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo},
      </if>
      <if test="settleProdType != null">
        SETTLE_PROD_TYPE = #{settleProdType},
      </if>
      <if test="settleAcctCcy != null">
        SETTLE_ACCT_CCY = #{settleAcctCcy},
      </if>
      <if test="settleAcctSeqNo != null">
        SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo},
      </if>
      <if test="settleCcy != null">
        SETTLE_CCY = #{settleCcy},
      </if>
      <if test="settleAmt != null">
        SETTLE_AMT = #{settleAmt},
      </if>
      <if test="settleXrate != null">
        SETTLE_XRATE = #{settleXrate},
      </if>
      <if test="xrateId != null">
        XRATE_ID = #{xrateId},
      </if>
      <if test="priority != null">
        PRIORITY = #{priority},
      </if>
      <if test="settleWeight != null">
        SETTLE_WEIGHT = #{settleWeight},
      </if>
      <if test="autoBlocking != null">
        AUTO_BLOCKING = #{autoBlocking},
      </if>
      <if test="trustedPayNo != null">
        TRUSTED_PAY_NO = #{trustedPayNo},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="dacValue != null">
        DAC_VALUE = #{dacValue},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>

      <if test="profitRatio != null">
        PROFIT_RATIO = #{profitRatio},
      </if>
      <if test="selfSupportFlag != null">
        SELF_SUPPORT_FLAG = #{selfSupportFlag},
      </if>
      <if test="userId != null">
        USER_ID =  #{userId},
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE =  #{lastChangeDate}
      </if>
      <if test="settleMobilePhone != null">
        SETTLE_MOBILE_PHONE =  #{settleMobilePhone}
      </if>
    </set>
    where  REFERENCE = #{reference}
    and INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <delete id="deleteByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    delete from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    <if test="settleNo != null">
      AND SETTLE_NO = #{settleNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <delete id="deleteByInternalKeyEventType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    delete from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
      AND EVENT_TYPE = #{eventType}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    delete from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
      AND SETTLE_NO = #{settleNo}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <select id="selectAcctSettleBySettleAcctType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    <if test="settleAcctClass != null">
      and SETTLE_ACCT_CLASS = #{settleAcctClass}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectAcctSettleByEventType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    and event_type = #{eventType}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectBySettleInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey}
     <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
     </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectBySettleInternalKey2" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey}
    <if test="settleClient != null and settleClient != ''">
      and SETTLE_CLIENT = #{settleClient}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByRefNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    delete from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    AND REFERENCE = #{reference}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>

  <select id="selectSettleBySettleAcctClasses" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and SETTLE_ACCT_CLASS  IN
    <foreach item="item" index="index" collection="settleAcctClasses" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getAcctSettleBySettleAcctType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where SETTLE_ACCT_INTERNAL_KEY = #{settleInternalKey}
    and SETTLE_ACCT_CLASS = #{settleAcctClass}
    <if test="settleClient != null and settleClient != ''">
      and SETTLE_CLIENT = #{settleClient}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getFixedAcctSettle" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select c.internal_key
    FROM (SELECT a.internal_key
      FROM RB_ACCT_SETTLE a LEFT JOIN RB_ACCT b ON a.internal_key = b.internal_key and a.client_no = b.client_no
      WHERE  a.SETTLE_ACCT_CLASS in ('AUS','TRA','INT') AND b.acct_status != 'C' and b.acct_type = 'T'
      <if test="settleAcctInternalKey != null and settleAcctInternalKey != ''">
        AND a.SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey}
      </if>
      limit 1
    ) c
  </select>

  <select id="getAcctSettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE  internal_key = #{internalKey}
    AND settle_acct_class = 'AUT'
    AND pay_rec_ind = 'REC'
    AND amt_type = 'ALL'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getIntSettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE  internal_key = #{internalKey}
    AND settle_acct_class = 'AUT'
    AND pay_rec_ind = 'REC'
    AND amt_type IN( 'ALL','INT')
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getRelSettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE  internal_key = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND settle_acct_class = 'REL'
    AND pay_rec_ind = 'ALL'
  </select>

  <select id="getPaySettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE  internal_key = #{internalKey}
    AND settle_acct_class = 'PAY'
    AND pay_rec_ind = 'PAY'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectSettleByWeight" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="S_Column_List"/>
    from RB_ACCT_SETTLE s, RB_acct a
    where a.BASE_ACCT_NO = #{baseAcctNo}
    and a.ACCT_SEQ_NO = #{acctSeqNo}
    and a.internal_key = s.internal_key
    and s.SETTLE_ACCT_CLASS  IN
    <foreach item="item" index="index" collection="settleAcctClasses" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND s.pay_rec_ind = 'REC'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND s.SETTLE_WEIGHT is not null
  </select>
  <select id="getSettleByWeightByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE internal_key = #{internalKey}
    AND settle_acct_class IN
    <foreach item="item" index="index" collection="settleAcctClasses" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND pay_rec_ind = 'REC'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND SETTLE_WEIGHT is not null
  </select>
  <update id="updateMbAcctSettleRes" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    update RB_ACCT_SETTLE
    <set>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="resSeqNo == null">
        RES_SEQ_NO = null,
      </if>

      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE =  #{lastChangeDate},
      </if>
    </set>
    where SETTLE_NO = #{settleNo}
    and INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateMbAcctSettleWeight" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    update RB_ACCT_SETTLE
    <set>
      <if test="settleWeight == null">
        SETTLE_WEIGHT = null,
      </if>

      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE =  #{lastChangeDate},
      </if>
    </set>
    where SETTLE_NO = #{settleNo}
    and INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="selectBySettleAcctInternalKey2" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey}
    AND SETTLE_ACCT_CLASS = #{settleAcctClass}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getAcctSettleBySettleClass" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE internal_key = #{internalKey}
    <if test="settleAmtType != null">
      AND amt_type = #{settleAmtType}
    </if>
    <if test="settleAcctClass != null">
      AND settle_acct_class = #{settleAcctClass}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDcAcctAutoSettle" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where  INTERNAL_KEY = #{internalKey}
    and  SETTLE_METHOD = 'R'
    and PAY_REC_IND = 'REC'
    and SETTLE_ACCT_CLASS = 'AUS'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByTrustedPayNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where TRUSTED_PAY_NO = #{trustedPayNo}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getcAcctAutoDepositSettle" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where  INTERNAL_KEY = #{internalKey}
    and AMT_TYPE = 'PRI'
    and  SETTLE_METHOD = 'R'
    and PAY_REC_IND = 'PAY'
    and EVENT_TYPE = 'ARO'
    and SETTLE_ACCT_CLASS = 'AUT'
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <update id="updateIdep" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    update RB_ACCT_SETTLE
    <set>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey},
      </if>
      <if test="settleBaseAcctNo != null">
        SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo},
      </if>
      <if test="settleClient != null">
        SETTLE_CLIENT = #{settleClient},
      </if>
      <if test="settleAcctSeqNo != null">
        SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo},
      </if>
      <if test="settleProdType != null">
        SETTLE_PROD_TYPE =  #{settleProdType},
      </if>
      <if test="settleAcctCcy != null">
        SETTLE_ACCT_CCY =  #{settleAcctCcy},
      </if>
      <if test="settleBranch != null">
        SETTLE_BRANCH =  #{settleBranch},
      </if>
      <if test="settleAcctName != null">
        SETTLE_ACCT_NAME =  #{settleAcctName},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
          AND SETTLE_ACCT_CLASS = 'INT'
          AND AMT_TYPE = 'ID'
          <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
          </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <update id="updateTpp" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    update RB_ACCT_SETTLE
    <set>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey},
      </if>
      <if test="settleBaseAcctNo != null">
        SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo},
      </if>
      <if test="settleClient != null">
        SETTLE_CLIENT = #{settleClient},
      </if>
      <if test="settleAcctSeqNo != null">
        SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo},
      </if>
      <if test="settleProdType != null">
        SETTLE_PROD_TYPE =  #{settleProdType},
      </if>
      <if test="settleAcctCcy != null">
        SETTLE_ACCT_CCY =  #{settleAcctCcy},
      </if>
      <if test="settleBranch != null">
        SETTLE_BRANCH =  #{settleBranch},
      </if>
      <if test="settleAcctName != null">
        SETTLE_ACCT_NAME =  #{settleAcctName},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    AND SETTLE_ACCT_CLASS = 'TPP'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="selectByInternalKeyAndAmttype" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE internal_key = #{internalKey}
    <if test="settleAmtType != null">
      AND amt_type = #{settleAmtType}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectBySettleBaseAcctOrCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SETTLE
    WHERE internal_key = #{internalKey}
    <if test="(settleBaseAcctNo != null and settleBaseAcctNo != '') and (settleCardNo != null and settleCardNo !='')">
       and (SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo} or SETTLE_BASE_ACCT_NO = #{settleCardNo})
    </if>
    <if test="(settleBaseAcctNo != null and settleBaseAcctNo != '') and (settleCardNo == null or settleCardNo =='')">
      and SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo}
    </if>
    <if test="(settleBaseAcctNo == null or settleBaseAcctNo == '') and (settleCardNo != null and settleCardNo !='')">
      and SETTLE_BASE_ACCT_NO = #{settleCardNo}
    </if>
    <if test="settleAcctSeqNo != null and settleAcctSeqNo !=''">
      AND SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo}
    </if>
    <if test="settleCcy != null and settleCcy !=''">
      AND SETTLE_ACCT_CCY = #{settleCcy}
    </if>
    <if test="settleAcctClass != null and settleAcctClass !=''">
      AND SETTLE_ACCT_CLASS = #{settleAcctClass}
    </if>
    <if test="settleAmtType != null">
      AND amt_type = #{settleAmtType}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getRelBindInfoByBaseAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where SETTLE_ACCT_CLASS = 'REL'
    <if test="baseAcctNo != null">
      AND (SETTLE_BASE_ACCT_NO = #{baseAcctNo} or SETTLE_BASE_ACCT_NO = #{cardNo})
    </if>
    <if test="baseAcctNo != null">
      AND SETTLE_ACCT_SEQ_NO = #{settleAcctSeqNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectAcctSettleBySettleAcctType1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle">
    select <include refid="Base_Column"/>
    from RB_ACCT_SETTLE
    where INTERNAL_KEY = #{internalKey}
    <if test="settleAcctClass != null">
      and SETTLE_ACCT_CLASS = #{settleAcctClass}
    </if>
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="settleBaseAcctNo != null and settleBaseAcctNo != ''">
      and SETTLE_BASE_ACCT_NO = #{settleBaseAcctNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
