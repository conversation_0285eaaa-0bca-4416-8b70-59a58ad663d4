<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
  <!--多法人改造 by LIYUANV-->
    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    select <include refid="Base_Column"/>
    from CD_POS_AUTH_REG
    where AUTH_SEQ_NO = #{authSeqNo}
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  	<select id="getCdPosAuthRegByAuthId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
		select <include refid="Base_Column"/> 
        from cd_pos_auth_reg  
        where auth_seq_no   in 
               (select max(auth_seq_no) auth_seq_no          
                from cd_pos_auth_reg
                where
       <if test="company != null and company != '' ">
         COMPANY = #{company}
       </if>
                group by auth_id)
		and AUTH_ID = #{authId}
	</select>
  <!--多法人改造 by LIYUANV-->
	<select id="getCdPosAuthRegBySeqNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
		select <include refid="Base_Column"/>
		from CD_POS_AUTH_REG
		where CLIENT_NO = #{clientNo}
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      <if test="authNo != null and authNo.length() > 0">
        AND AUTH_SEQ_NO = #{authNo}
      </if>
      <if test="origChannelSeqNo != null and origChannelSeqNo.length() > 0">
        AND ChANNEL_SEQ_NO = #{origChannelSeqNo}
      </if>
	</select>
  <!--多法人改造 by LIYUANV-->
	<select id="getCdPosAuthInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
        select <include refid="Base_Column"/> 
        from cd_pos_auth_reg  
        where auth_seq_no   in 
               (select max(auth_seq_no) auth_seq_no          
                from cd_pos_auth_reg    
                group by auth_id)
        AND card_no = #{cardNo}
		AND tran_date = #{authDate}
		<if test="tranAmt != null">
			AND tran_amt = #{tranAmt}
		</if>
		<if test="authId != null and authId.length() > 0">
			AND auth_id = #{authId}
		</if>
		<if test="ccy != null and ccy.length() > 0">
			AND ccy = #{ccy}
		</if>
		<if test="channelTranStatus != null and channelTranStatus.length() > 0">
			AND channel_tran_status = #{channelTranStatus}
		</if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
	</select>
  <!--多法人改造 by LIYUANV-->
	<select id="selectOrigAuthInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
      select <include refid="Base_Column"/>
      from cd_pos_auth_reg
      where  AUTH_ID = #{authId}
      and CHANNEL_TRAN_STATUS = #{channelTranStatus}
      and CARD_NO =#{cardNo}
      and CLIENT_NO =#{clientNo}
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
	</select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCdPosAuthInfoa" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    select <include refid="Base_Column"/>
    from cd_pos_auth_reg
    where AUTH_ID   in
    (select max(AUTH_ID) AUTH_ID
    from cd_pos_auth_reg
      where card_no = #{cardNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>)
    AND card_no = #{cardNo}
    AND client_no = #{clientNo}
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCdPosAuthInfob" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    select <include refid="Base_Column"/>
    from cd_pos_auth_reg
    where
    card_no = #{cardNo} AND
    auth_id = #{authId}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    delete from CD_POS_AUTH_REG
    where AUTH_SEQ_NO = #{authSeqNo}
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
  </delete>
  <!--多法人改造 by LIYUANV-->
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    update CD_POS_AUTH_REG
    <set>
      <if test="authId != null">
        AUTH_ID = #{authId},
      </if>
      <if test="cardNo != null">
        CARD_NO = #{cardNo},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="fullAmt != null">
        FULL_AMT = #{fullAmt},
      </if>
      <if test="authFromDate != null">
        AUTH_FROM_DATE = #{authFromDate},
      </if>
      <if test="authThruDate != null">
        AUTH_THRU_DATE = #{authThruDate},
      </if>
      <if test="channelTranStatus != null">
        CHANNEL_TRAN_STATUS = #{channelTranStatus},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="terminalId != null">
        TERMINAL_ID = #{terminalId},
      </if>
      <if test="merchantCode != null">
        MERCHANT_CODE = #{merchantCode},
      </if>
      <if test="cupDate != null">
        CUP_DATE = #{cupDate},
      </if>
      <if test="cupAreaCode != null">
        CUP_AREA_CODE = #{cupAreaCode},
      </if>
      <if test="cupSendCode != null">
        CUP_SEND_CODE = #{cupSendCode},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo}
      </if>
    </set>
    where CLIENT_NO = #{clientNo}
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      <if test="authId != null and authId.length() > 0">
        AND AUTH_ID = #{authId}
      </if>
      <if test="channelSeqNo != null and channelSeqNo.length() > 0">
        AND CHANNEL_SEQ_NO = #{channelSeqNo}
      </if>
      <if test="authSeqNo != null and authSeqNo.length() > 0">
        AND AUTH_SEQ_NO = #{authSeqNo}
      </if>
  </update>

    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    insert into CD_POS_AUTH_REG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authSeqNo != null">
        AUTH_SEQ_NO,
      </if>
      <if test="authId != null">
        AUTH_ID,
      </if>
      <if test="cardNo != null">
        CARD_NO,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="fullAmt != null">
        FULL_AMT,
      </if>
      <if test="authFromDate != null">
        AUTH_FROM_DATE,
      </if>
      <if test="authThruDate != null">
        AUTH_THRU_DATE,
      </if>
      <if test="channelTranStatus != null">
        CHANNEL_TRAN_STATUS,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="terminalId != null">
        TERMINAL_ID,
      </if>
      <if test="merchantCode != null">
        MERCHANT_CODE,
      </if>
      <if test="cupDate != null">
        CUP_DATE,
      </if>
      <if test="cupAreaCode != null">
        CUP_AREA_CODE,
      </if>
      <if test="cupSendCode != null">
        CUP_SEND_CODE,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="authSeqNo != null">
        #{authSeqNo},
      </if>
      <if test="authId != null">
        #{authId},
      </if>
      <if test="cardNo != null">
        #{cardNo},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="fullAmt != null">
        #{fullAmt},
      </if>
      <if test="authFromDate != null">
        #{authFromDate},
      </if>
      <if test="authThruDate != null">
        #{authThruDate},
      </if>
      <if test="channelTranStatus != null">
        #{channelTranStatus},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="terminalId != null">
        #{terminalId},
      </if>
      <if test="merchantCode != null">
        #{merchantCode},
      </if>
      <if test="cupDate != null">
        #{cupDate},
      </if>
      <if test="cupAreaCode != null">
        #{cupAreaCode},
      </if>
      <if test="cupSendCode != null">
        #{cupSendCode},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
    </trim>
  </insert>
  <!--多法人改造 by LIYUANV-->
  <select id="selectOnebyCardAuthAndTranStatus" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    select <include refid="Base_Column"/>
    from cd_pos_auth_reg
    where
    card_no = #{cardNo} AND
    channel_tran_status = #{channelTranStatus}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="channelSeqNo != null and channelSeqNo.length() > 0">
      AND CHANNEL_SEQ_NO = #{channelSeqNo}
    </if>
    <if test="authSeqNo != null and authSeqNo.length() > 0">
      AND AUTH_SEQ_NO = #{authSeqNo}
    </if>

  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCdPosAuthRegByResinfo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg">
    select <include refid="Base_Column"/>
    from cd_pos_auth_reg
    where
    channel_tran_status = #{channelTranStatus} AND
    client_no = #{clientNo} AND
    res_seq_no = #{resSeqNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

</mapper>
