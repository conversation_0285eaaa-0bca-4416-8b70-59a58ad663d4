<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">
    select <include refid="Base_Column"/>
    from RB_AD_LOST
    where AD_LOST_NO = #{adLostNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">
    delete from RB_AD_LOST
    where AD_LOST_NO = #{adLostNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">
    update RB_AD_LOST
    <set>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="feeAmt != null">
        FEE_AMT = #{feeAmt},
      </if>
      <if test="lostCommName1 != null">
        LOST_COMM_NAME_1 = #{lostCommName1},
      </if>
      <if test="lostCommClientNo1 != null">
        LOST_COMM_CLIENT_NO_1 = #{lostCommClientNo1},
      </if>
      <if test="lostDocumentType1 != null">
        LOST_DOCUMENT_TYPE_1 = #{lostDocumentType1},
      </if>
      <if test="adLostStatus != null">
        AD_LOST_STATUS = #{adLostStatus},
      </if>
      <if test="lostDocumentId1 != null">
        LOST_DOCUMENT_ID_1 = #{lostDocumentId1},
      </if>
      <if test="lostCommName2 != null">
        LOST_COMM_NAME_2 = #{lostCommName2},
      </if>
      <if test="lostCommClientNo2 != null">
        LOST_COMM_CLIENT_NO_2 = #{lostCommClientNo2},
      </if>
      <if test="lostDocumentType2 != null">
        LOST_DOCUMENT_TYPE_2 = #{lostDocumentType2},
      </if>
      <if test="lostDocumentId2 != null">
        LOST_DOCUMENT_ID_2 = #{lostDocumentId2},
      </if>
      <if test="unlostCommName1 != null">
        UNLOST_COMM_NAME_1 = #{unlostCommName1},
      </if>
      <if test="unlostCommClientNo1 != null">
        UNLOST_COMM_CLIENT_NO_1 = #{unlostCommClientNo1},
      </if>
      <if test="unlostDocumentType1 != null">
        UNLOST_DOCUMENT_TYPE_1 = #{unlostDocumentType1},
      </if>
      <if test="unlostDocumentId1 != null">
        UNLOST_DOCUMENT_ID_1 = #{unlostDocumentId1},
      </if>
      <if test="unlostCommName2 != null">
        UNLOST_COMM_NAME_2 = #{unlostCommName2},
      </if>
      <if test="unlostCommClientNo2 != null">
        UNLOST_COMM_CLIENT_NO_2 = #{unlostCommClientNo2},
      </if>
      <if test="unlostDocumentType2 != null">
        UNLOST_DOCUMENT_TYPE_2 = #{unlostDocumentType2},
      </if>
      <if test="unlostDocumentId2 != null">
        UNLOST_DOCUMENT_ID_2 = #{unlostDocumentId2},
      </if>
      <if test="billTransferFlag != null">
        BILL_TRANSFER_FLAG = #{billTransferFlag},
      </if>
      <if test="lostUserId != null">
        LOST_USER_ID = #{lostUserId},
      </if>
      <if test="lostTranDate != null">
        LOST_TRAN_DATE = #{lostTranDate},
      </if>
      <if test="unlostUserId != null">
        UNLOST_USER_ID = #{unlostUserId},
      </if>
      <if test="unlostDate != null">
        UNLOST_DATE = #{unlostDate},
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="lostReason != null">
        LOST_REASON = #{lostReason},
      </if>
      <if test="unlostReason != null">
        UNLOST_REASON = #{unlostReason},
      </if>
      <if test="printCnt != null">
        PRINT_CNT = #{printCnt},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="lostLossDate != null">
        LOST_LOSS_DATE = #{lostLossDate},
      </if>
      <if test="lostCompany != null">
        LOST_COMPANY = #{lostCompany},
      </if>
      <if test="unlostCompany != null">
        UNLOST_COMPANY = #{unlostCompany},
      </if>
      <if test="lostCompDocumentType != null">
        LOST_COMP_DOCUMENT_TYPE = #{lostCompDocumentType},
      </if>
      <if test="lostCompDocumentId != null">
        LOST_COMP_DOCUMENT_ID = #{lostCompDocumentId},
      </if>
      <if test="unlostCompDocumentType != null">
        UNLOST_COMP_DOCUMENT_TYPE = #{unlostCompDocumentType},
      </if>
      <if test="unlostCompDocumentId != null">
        UNLOST_COMP_DOCUMENT_ID = #{unlostCompDocumentId},
      </if>
      <if test="lostType != null">
        LOST_TYPE = #{lostType},
      </if>
      <if test="crtWorker1 != null">
        CRT_WORKER1 = #{crtWorker1},
      </if>
      <if test="crtWorker1DocumentId != null">
        CRT_WORKER1_DOCUMENT_ID = #{crtWorker1DocumentId},
      </if>
      <if test="crtWorker2 != null">
        CRT_WORKER2 = #{crtWorker2},
      </if>
      <if test="crtWorker2DocumentId != null">
        CRT_WORKER2_DOCUMENT_ID = #{crtWorker2DocumentId},
      </if>
      <if test="lostLegalDocument != null">
        LOST_LEGAL_DOCUMENT = #{lostLegalDocument},
      </if>
      <if test="crtWorker3 != null">
        CRT_WORKER3 = #{crtWorker3},
      </if>
      <if test="crtWorker3DocumentId != null">
        CRT_WORKER3_DOCUMENT_ID = #{crtWorker3DocumentId},
      </if>
      <if test="crtWorker4 != null">
        CRT_WORKER4 = #{crtWorker4},
      </if>
      <if test="crtWorker4DocumentId != null">
        CRT_WORKER4_DOCUMENT_ID = #{crtWorker4DocumentId},
      </if>
      <if test="unlostLegalDocument != null">
        UNLOST_LEGAL_DOCUMENT = #{unlostLegalDocument},
      </if>
      <if test="lostTelNo != null">
        LOST_TEL_NO = #{lostTelNo},
      </if>
      <if test="lostCompTel != null">
        LOST_COMP_TEL = #{lostCompTel},
      </if>
      <if test="lostUnit != null">
        LOST_UNIT = #{lostUnit},
      </if>
      <if test="lostCaseNo != null">
        LOST_CASE_NO = #{lostCaseNo},
      </if>
      <if test="unlostTel != null">
        UNLOST_TEL = #{unlostTel},
      </if>
      <if test="unlostCompTel != null">
        UNLOST_COMP_TEL = #{unlostCompTel},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="unchainCaseNo != null">
        UNCHAIN_CASE_NO = #{unchainCaseNo},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey}
      </if>
    </set>
    where AD_LOST_NO = #{adLostNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">
    insert into RB_AD_LOST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="feeAmt != null">
        FEE_AMT,
      </if>
      <if test="lostCommName1 != null">
        LOST_COMM_NAME_1,
      </if>
      <if test="lostCommClientNo1 != null">
        LOST_COMM_CLIENT_NO_1,
      </if>
      <if test="lostDocumentType1 != null">
        LOST_DOCUMENT_TYPE_1,
      </if>
      <if test="adLostStatus != null">
        AD_LOST_STATUS,
      </if>
      <if test="lostDocumentId1 != null">
        LOST_DOCUMENT_ID_1,
      </if>
      <if test="lostCommName2 != null">
        LOST_COMM_NAME_2,
      </if>
      <if test="lostCommClientNo2 != null">
        LOST_COMM_CLIENT_NO_2,
      </if>
      <if test="lostDocumentType2 != null">
        LOST_DOCUMENT_TYPE_2,
      </if>
      <if test="lostDocumentId2 != null">
        LOST_DOCUMENT_ID_2,
      </if>
      <if test="unlostCommName1 != null">
        UNLOST_COMM_NAME_1,
      </if>
      <if test="unlostCommClientNo1 != null">
        UNLOST_COMM_CLIENT_NO_1,
      </if>
      <if test="unlostDocumentType1 != null">
        UNLOST_DOCUMENT_TYPE_1,
      </if>
      <if test="unlostDocumentId1 != null">
        UNLOST_DOCUMENT_ID_1,
      </if>
      <if test="unlostCommName2 != null">
        UNLOST_COMM_NAME_2,
      </if>
      <if test="unlostCommClientNo2 != null">
        UNLOST_COMM_CLIENT_NO_2,
      </if>
      <if test="unlostDocumentType2 != null">
        UNLOST_DOCUMENT_TYPE_2,
      </if>
      <if test="unlostDocumentId2 != null">
        UNLOST_DOCUMENT_ID_2,
      </if>
      <if test="billTransferFlag != null">
        BILL_TRANSFER_FLAG,
      </if>
      <if test="lostUserId != null">
        LOST_USER_ID,
      </if>
      <if test="lostTranDate != null">
        LOST_TRAN_DATE,
      </if>
      <if test="unlostUserId != null">
        UNLOST_USER_ID,
      </if>
      <if test="unlostDate != null">
        UNLOST_DATE,
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="lostReason != null">
        LOST_REASON,
      </if>
      <if test="unlostReason != null">
        UNLOST_REASON,
      </if>
      <if test="printCnt != null">
        PRINT_CNT,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="lostLossDate != null">
        LOST_LOSS_DATE,
      </if>
      <if test="lostCompany != null">
        LOST_COMPANY,
      </if>
      <if test="unlostCompany != null">
        UNLOST_COMPANY,
      </if>
      <if test="lostCompDocumentType != null">
        LOST_COMP_DOCUMENT_TYPE,
      </if>
      <if test="lostCompDocumentId != null">
        LOST_COMP_DOCUMENT_ID,
      </if>
      <if test="unlostCompDocumentType != null">
        UNLOST_COMP_DOCUMENT_TYPE,
      </if>
      <if test="unlostCompDocumentId != null">
        UNLOST_COMP_DOCUMENT_ID,
      </if>
      <if test="lostType != null">
        LOST_TYPE,
      </if>
      <if test="crtWorker1 != null">
        CRT_WORKER1,
      </if>
      <if test="crtWorker1DocumentId != null">
        CRT_WORKER1_DOCUMENT_ID,
      </if>
      <if test="crtWorker2 != null">
        CRT_WORKER2,
      </if>
      <if test="crtWorker2DocumentId != null">
        CRT_WORKER2_DOCUMENT_ID,
      </if>
      <if test="lostLegalDocument != null">
        LOST_LEGAL_DOCUMENT,
      </if>
      <if test="crtWorker3 != null">
        CRT_WORKER3,
      </if>
      <if test="crtWorker3DocumentId != null">
        CRT_WORKER3_DOCUMENT_ID,
      </if>
      <if test="crtWorker4 != null">
        CRT_WORKER4,
      </if>
      <if test="crtWorker4DocumentId != null">
        CRT_WORKER4_DOCUMENT_ID,
      </if>
      <if test="unlostLegalDocument != null">
        UNLOST_LEGAL_DOCUMENT,
      </if>
      <if test="lostTelNo != null">
        LOST_TEL_NO,
      </if>
      <if test="lostCompTel != null">
        LOST_COMP_TEL,
      </if>
      <if test="lostUnit != null">
        LOST_UNIT,
      </if>
      <if test="lostCaseNo != null">
        LOST_CASE_NO,
      </if>
      <if test="unlostTel != null">
        UNLOST_TEL,
      </if>
      <if test="unlostCompTel != null">
        UNLOST_COMP_TEL,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="unchainCaseNo != null">
        UNCHAIN_CASE_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="adLostNo != null">
        AD_LOST_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reference != null">
        #{reference},
      </if>
      <if test="feeAmt != null">
        #{feeAmt},
      </if>
      <if test="lostCommName1 != null">
        #{lostCommName1},
      </if>
      <if test="lostCommClientNo1 != null">
        #{lostCommClientNo1},
      </if>
      <if test="lostDocumentType1 != null">
        #{lostDocumentType1},
      </if>
      <if test="adLostStatus != null">
        #{adLostStatus},
      </if>
      <if test="lostDocumentId1 != null">
        #{lostDocumentId1},
      </if>
      <if test="lostCommName2 != null">
        #{lostCommName2},
      </if>
      <if test="lostCommClientNo2 != null">
        #{lostCommClientNo2},
      </if>
      <if test="lostDocumentType2 != null">
        #{lostDocumentType2},
      </if>
      <if test="lostDocumentId2 != null">
        #{lostDocumentId2},
      </if>
      <if test="unlostCommName1 != null">
        #{unlostCommName1},
      </if>
      <if test="unlostCommClientNo1 != null">
        #{unlostCommClientNo1},
      </if>
      <if test="unlostDocumentType1 != null">
        #{unlostDocumentType1},
      </if>
      <if test="unlostDocumentId1 != null">
        #{unlostDocumentId1},
      </if>
      <if test="unlostCommName2 != null">
        #{unlostCommName2},
      </if>
      <if test="unlostCommClientNo2 != null">
        #{unlostCommClientNo2},
      </if>
      <if test="unlostDocumentType2 != null">
        #{unlostDocumentType2},
      </if>
      <if test="unlostDocumentId2 != null">
        #{unlostDocumentId2},
      </if>
      <if test="billTransferFlag != null">
        #{billTransferFlag},
      </if>
      <if test="lostUserId != null">
        #{lostUserId},
      </if>
      <if test="lostTranDate != null">
        #{lostTranDate},
      </if>
      <if test="unlostUserId != null">
        #{unlostUserId},
      </if>
      <if test="unlostDate != null">
        #{unlostDate},
      </if>
      <if test="lastChangeUserId != null">
        #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="lostReason != null">
        #{lostReason},
      </if>
      <if test="unlostReason != null">
        #{unlostReason},
      </if>
      <if test="printCnt != null">
        #{printCnt},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="lostLossDate != null">
        #{lostLossDate},
      </if>
      <if test="lostCompany != null">
        #{lostCompany},
      </if>
      <if test="unlostCompany != null">
        #{unlostCompany},
      </if>
      <if test="lostCompDocumentType != null">
        #{lostCompDocumentType},
      </if>
      <if test="lostCompDocumentId != null">
        #{lostCompDocumentId},
      </if>
      <if test="unlostCompDocumentType != null">
        #{unlostCompDocumentType},
      </if>
      <if test="unlostCompDocumentId != null">
        #{unlostCompDocumentId},
      </if>
      <if test="lostType != null">
        #{lostType},
      </if>
      <if test="crtWorker1 != null">
        #{crtWorker1},
      </if>
      <if test="crtWorker1DocumentId != null">
        #{crtWorker1DocumentId},
      </if>
      <if test="crtWorker2 != null">
        #{crtWorker2},
      </if>
      <if test="crtWorker2DocumentId != null">
        #{crtWorker2DocumentId},
      </if>
      <if test="lostLegalDocument != null">
        #{lostLegalDocument},
      </if>
      <if test="crtWorker3 != null">
        #{crtWorker3},
      </if>
      <if test="crtWorker3DocumentId != null">
        #{crtWorker3DocumentId},
      </if>
      <if test="crtWorker4 != null">
        #{crtWorker4},
      </if>
      <if test="crtWorker4DocumentId != null">
        #{crtWorker4DocumentId},
      </if>
      <if test="unlostLegalDocument != null">
        #{unlostLegalDocument},
      </if>
      <if test="lostTelNo != null">
        #{lostTelNo},
      </if>
      <if test="lostCompTel != null">
        #{lostCompTel},
      </if>
      <if test="lostUnit != null">
        #{lostUnit},
      </if>
      <if test="lostCaseNo != null">
        #{lostCaseNo},
      </if>
      <if test="unlostTel != null">
        #{unlostTel},
      </if>
      <if test="unlostCompTel != null">
        #{unlostCompTel},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="unchainCaseNo != null">
        #{unchainCaseNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="adLostNo != null">
        #{adLostNo},
      </if>
    </trim>
  </insert>
<!--  <select id="queryAdLostParam" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">-->
<!--    select <include refid="Base_Column"/>-->
<!--    from RB_AD_LOST-->
<!--    where 1=1-->
<!--    <if test="baseParam.startDate !=null and baseParam.endDate !=null">-->
<!--      AND (LOST_TRAN_DATE BETWEEN #{baseParam.startDate} AND #{baseParam.endDate}-->
<!--      OR UNLOST_DATE  BETWEEN #{baseParam.startDate} AND #{baseParam.endDate})-->
<!--    </if>-->
<!--    <if test="baseParam.lostStatus !=null">-->
<!--      AND AD_LOST_STATUS = #{baseParam.lostStatus}-->
<!--    </if>-->
<!--    <if test="baseParam.lostNo !=null">-->
<!--      AND AD_LOST_NO = #{baseParam.lostNo}-->
<!--    </if>-->
<!--    <if test="baseParam.internalKey !=null">-->
<!--      AND INTERNAL_KEY = #{baseParam.internalKey}-->
<!--    </if>-->
<!--  </select>-->
  <select id="queryByAdLostNoAndStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">
    select <include refid="Base_Column"/>
    from RB_AD_LOST
    <where>
    <if test="lostStatus !=null">
      AND AD_LOST_STATUS = #{lostStatus}
    </if>
    <if test="lostNo !=null">
      AND AD_LOST_NO = #{lostNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="queryByReferenceAndStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">
    select <include refid="Base_Column"/>
    from RB_AD_LOST
    <where>
    <if test="reference !=null">
      AND reference = #{reference}
    </if>
    <if test="adLostStatus !=null">
      AND adLostStatus = #{adLostStatus}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <update id="updateExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost">
    update RB_AD_LOST
    <set>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="feeAmt != null">
        FEE_AMT = #{feeAmt},
      </if>
      <if test="lostCommName1 != null">
        LOST_COMM_NAME_1 = #{lostCommName1},
      </if>
      <if test="lostCommClientNo1 != null">
        LOST_COMM_CLIENT_NO_1 = #{lostCommClientNo1},
      </if>
      <if test="lostDocumentType1 != null">
        LOST_DOCUMENT_TYPE_1 = #{lostDocumentType1},
      </if>
      <if test="adLostStatus != null">
        AD_LOST_STATUS = #{adLostStatus},
      </if>
      <if test="lostDocumentId1 != null">
        LOST_DOCUMENT_ID_1 = #{lostDocumentId1},
      </if>
      <if test="lostCommName2 != null">
        LOST_COMM_NAME_2 = #{lostCommName2},
      </if>
      <if test="lostCommClientNo2 != null">
        LOST_COMM_CLIENT_NO_2 = #{lostCommClientNo2},
      </if>
      <if test="lostDocumentType2 != null">
        LOST_DOCUMENT_TYPE_2 = #{lostDocumentType2},
      </if>
      <if test="lostDocumentId2 != null">
        LOST_DOCUMENT_ID_2 = #{lostDocumentId2},
      </if>
      <if test="unlostCommName1 != null">
        UNLOST_COMM_NAME_1 = #{unlostCommName1},
      </if>
      <if test="unlostCommClientNo1 != null">
        UNLOST_COMM_CLIENT_NO_1 = #{unlostCommClientNo1},
      </if>
      <if test="unlostDocumentType1 != null">
        UNLOST_DOCUMENT_TYPE_1 = #{unlostDocumentType1},
      </if>
      <if test="unlostDocumentId1 != null">
        UNLOST_DOCUMENT_ID_1 = #{unlostDocumentId1},
      </if>
      <if test="unlostCommName2 != null">
        UNLOST_COMM_NAME_2 = #{unlostCommName2},
      </if>
      <if test="unlostCommClientNo2 != null">
        UNLOST_COMM_CLIENT_NO_2 = #{unlostCommClientNo2},
      </if>
      <if test="unlostDocumentType2 != null">
        UNLOST_DOCUMENT_TYPE_2 = #{unlostDocumentType2},
      </if>
      <if test="unlostDocumentId2 != null">
        UNLOST_DOCUMENT_ID_2 = #{unlostDocumentId2},
      </if>
      <if test="billTransferFlag != null">
        BILL_TRANSFER_FLAG = #{billTransferFlag},
      </if>
      <if test="lostUserId != null">
        LOST_USER_ID = #{lostUserId},
      </if>
      <if test="lostTranDate != null">
        LOST_TRAN_DATE = #{lostTranDate},
      </if>
      <if test="unlostUserId != null">
        UNLOST_USER_ID = #{unlostUserId},
      </if>
      <if test="unlostDate != null">
        UNLOST_DATE = #{unlostDate},
      </if>
      <if test="lastChangeUserId != null">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="lostReason != null">
        LOST_REASON = #{lostReason},
      </if>
      <if test="unlostReason != null">
        UNLOST_REASON = #{unlostReason},
      </if>
      <if test="printCnt != null">
        PRINT_CNT = #{printCnt},
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="lostLossDate != null">
        LOST_LOSS_DATE = #{lostLossDate},
      </if>
      <if test="lostCompany != null">
        LOST_COMPANY = #{lostCompany},
      </if>
      <if test="unlostCompany != null">
        UNLOST_COMPANY = #{unlostCompany},
      </if>
      <if test="lostCompDocumentType != null">
        LOST_COMP_DOCUMENT_TYPE = #{lostCompDocumentType},
      </if>
      <if test="lostCompDocumentId != null">
        LOST_COMP_DOCUMENT_ID = #{lostCompDocumentId},
      </if>
      <if test="unlostCompDocumentType != null">
        UNLOST_COMP_DOCUMENT_TYPE = #{unlostCompDocumentType},
      </if>
      <if test="unlostCompDocumentId != null">
        UNLOST_COMP_DOCUMENT_ID = #{unlostCompDocumentId},
      </if>
      <if test="lostType != null">
        LOST_TYPE = #{lostType},
      </if>
      <if test="crtWorker1 != null">
        CRT_WORKER1 = #{crtWorker1},
      </if>
      <if test="crtWorker1DocumentId != null">
        CRT_WORKER1_DOCUMENT_ID = #{crtWorker1DocumentId},
      </if>
      <if test="crtWorker2 != null">
        CRT_WORKER2 = #{crtWorker2},
      </if>
      <if test="crtWorker2DocumentId != null">
        CRT_WORKER2_DOCUMENT_ID = #{crtWorker2DocumentId},
      </if>
      <if test="lostLegalDocument != null">
        LOST_LEGAL_DOCUMENT = #{lostLegalDocument},
      </if>
      <if test="crtWorker3 != null">
        CRT_WORKER3 = #{crtWorker3},
      </if>
      <if test="crtWorker3DocumentId != null">
        CRT_WORKER3_DOCUMENT_ID = #{crtWorker3DocumentId},
      </if>
      <if test="crtWorker4 != null">
        CRT_WORKER4 = #{crtWorker4},
      </if>
      <if test="crtWorker4DocumentId != null">
        CRT_WORKER4_DOCUMENT_ID = #{crtWorker4DocumentId},
      </if>
      <if test="unlostLegalDocument != null">
        UNLOST_LEGAL_DOCUMENT = #{unlostLegalDocument},
      </if>
      <if test="lostTelNo != null">
        LOST_TEL_NO = #{lostTelNo},
      </if>
      <if test="lostCompTel != null">
        LOST_COMP_TEL = #{lostCompTel},
      </if>
      <if test="lostUnit != null">
        LOST_UNIT = #{lostUnit},
      </if>
      <if test="lostCaseNo != null">
        LOST_CASE_NO = #{lostCaseNo},
      </if>
      <if test="unlostTel != null">
        UNLOST_TEL = #{unlostTel},
      </if>
      <if test="unlostCompTel != null">
        UNLOST_COMP_TEL = #{unlostCompTel},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="unchainCaseNo != null">
        UNCHAIN_CASE_NO = #{unchainCaseNo},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey}
      </if>
    </set>
    where AD_LOST_NO = #{adLostNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
