<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAdjustHist">
  <update id="restoreRbAcctAdjustHists">
    UPDATE
    RB_ACCT_ADJUST_HIST
    <set>
        INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},
        ADJ_SEQ_NO = #{adjSeqNo,jdbcType=VARCHAR},
        CAPT_DATE = #{captDate,jdbcType=DATE},
        ADJUST_DATE = #{adjustDate,jdbcType=DATE},
        INT_ADJ_TYPE = #{intAdjType,jdbcType=VARCHAR},
        REAL_RATE = #{realRate,jdbcType=DECIMAL},
        AGG = #{agg,jdbcType=DECIMAL},
        AGG_ADJ = #{aggAdj,jdbcType=DECIMAL},
        INT_ADJ_AMT = #{intAdjAmt,jdbcType=DECIMAL},
        FROM_INT_DATE = #{fromIntDate,jdbcType=DATE},
        TO_INT_DATE = #{toIntDate,jdbcType=DATE},
        DEAL_AMT = #{dealAmt,jdbcType=DECIMAL},
        ADJUST_FROM = #{adjustFrom,jdbcType=VARCHAR},
        ADJ_REASON = #{adjReason,jdbcType=VARCHAR},
        TRAN_STATUS = #{tranStatus,jdbcType=VARCHAR},
        PROCESS_FLAG = #{processFlag,jdbcType=VARCHAR},
        ORIG_CHANNEL_SEQ_NO = #{origChannelSeqNo,jdbcType=VARCHAR},
        SUB_CHANNEL_SEQ_NO = #{subChannelSeqNo,jdbcType=VARCHAR},
        COMPANY = #{company,jdbcType=VARCHAR},
        TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
    </set>
    <where>
      <if test="adjSeqNo != null and  adjSeqNo != '' ">
        AND ADJ_SEQ_NO = #{adjSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="clientNo != null and  clientNo != '' ">
        AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
      </if>
    </where>
  </update>
    <select id="selectCountByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAdjustHist" resultType="java.lang.Integer">
        select count(1)
        from rb_acct_adjust_hist
        where internal_key = #{internalKey}
        <if test="clientNo != null and clientNo != ''">
            and client_no = #{clientNo}
        </if>
        <if test="processFlag != null and processFlag != ''">
            and process_flag = #{processFlag}
        </if>
    </select>

    <select id="getAccrInfoMainListByStartEndDate" parameterType="java.util.HashMap"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAdjustHist">
        SELECT <include refid="Base_Column_List"/>
        FROM RB_ACCT_ADJUST_HIST
        WHERE INTERNAL_KEY = #{internalKey}
        <if test="intAdjType !=null and intAdjType != ''">
            AND INT_ADJ_TYPE = #{intAdjType}
        </if>
        <if test="startDate != null and endDate != null">
            AND ADJUST_DATE between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        </if>
    </select>

    <select id="getRbAcctAdjustHistsByNegativeAgg" parameterType="java.util.HashMap"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAdjustHist">
        SELECT <include refid="Base_Column_List"/>
        FROM RB_ACCT_ADJUST_HIST
        WHERE (AGG_ADJ <![CDATA[ < ]]> 0
        or INT_ADJ_AMT <![CDATA[ < ]]> 0)
        <if test="internalKey !=null and internalKey != ''">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo !=null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="processFlag != null and processFlag != ''">
            AND PROCESS_FLAG = #{processFlag}
        </if>
    </select>

    <sql id="Base_Column_List">
        INTERNAL_KEY,
        ADJ_SEQ_NO,
        CAPT_DATE,
        ADJUST_DATE,
        INT_ADJ_TYPE,
        REAL_RATE,
        AGG,
        AGG_ADJ,
        INT_ADJ_AMT,
        FROM_INT_DATE,
        TO_INT_DATE,
        DEAL_AMT,
        ADJUST_FROM,
        ADJ_REASON,
        TRAN_STATUS,
        PROCESS_FLAG,
        ORIG_CHANNEL_SEQ_NO,
        SUB_CHANNEL_SEQ_NO,
        COMPANY,
        TRAN_TIMESTAMP,
        CLIENT_NO,
        ADJUST_USER_ID,
        ADJUST_BRANCH
    </sql>

    <update id="updRbAcctAdjHistProcessFlag">
        UPDATE
        RB_ACCT_ADJUST_HIST
        set PROCESS_FLAG =  #{processFlag,jdbcType=VARCHAR},
        CAPT_DATE = #{captDate,jdbcType=DATE}
        WHERE  PROCESS_FLAG = 'N'
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="internalKey !=null and internalKey != ''">
                AND INTERNAL_KEY = #{internalKey}
            </if>
    </update>


</mapper>
