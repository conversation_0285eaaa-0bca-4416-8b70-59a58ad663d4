<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTranDetails">
	<select id="selectByContrastBatNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTranDetails">
		select *
		from RB_BATCH_TRAN_DETAILS
			where BATCH_NO = #{contrastBatNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectSumByContrastBatNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.BatchTranSum">
		select BATCH_NO,BATCH_STATUS,BASE_ACCT_NO,
		COUNT(1) as totalCount,sum(tran_amt) as totalAmt
		from RB_BATCH_TRAN_DETAILS
		where BATCH_NO = #{contrastBatNo}
		and BATCH_STATUS = #{tranStatus}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		group by BATCH_NO,BATCH_STATUS,BASE_ACCT_NO
	</select>
	<select id="selectBatchTranCountByBatchNo" parameterType="java.util.Map" resultType="int">
		SELECT COUNT(1) FROM RB_BATCH_TRAN_DETAILS WHERE BATCH_NO = #{batchNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectByPrimaryKeyForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTranDetails"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchTranDetails">
		select *
		from RB_BATCH_TRAN_DETAILS
		where BATCH_STATUS=#{batchStatus} AND BATCH_NO = #{batchNo} AND SEQ_NO= #{seqNo} FOR UPDATE
	</select>
</mapper>
