<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCrossSubAcct">

  <select id="selectActiveByBaseAcctNoAndBankNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCrossSubAcct">
    select <include refid="Base_Column"/>
    from RB_PCP_CROSS_SUB_ACCT
    where AGREEMENT_STATUS = 'A'
    <if test="baseAcctNo != null and baseAcctNo !='' ">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="bankNo != null and bankNo !='' ">
      AND BANK_NO = #{bankNo}
    </if>
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

  <select id="selectActiveByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCrossSubAcct">
    select <include refid="Base_Column"/>
    from RB_PCP_CROSS_SUB_ACCT
    where AGREEMENT_STATUS = 'A'
    <if test="internalKey != null and internalKey !='' ">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

  <select id="selectActiveByAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCrossSubAcct">
    select <include refid="Base_Column"/>
    from RB_PCP_CROSS_SUB_ACCT
    where AGREEMENT_STATUS = 'A' AND AGREEMENT_ID = #{agreementId}
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

  <update id="endByAgreementId" parameterType="java.util.Map">
    update RB_PCP_CROSS_SUB_ACCT set AGREEMENT_STATUS = 'E'
    where AGREEMENT_ID = #{agreementId}
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
  </update>

</mapper>
