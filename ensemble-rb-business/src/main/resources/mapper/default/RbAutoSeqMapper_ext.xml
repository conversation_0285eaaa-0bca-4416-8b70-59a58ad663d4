<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAutoSeq">
    <select id="selectByAttrKeyAndRest"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAutoSeq" useCache="false" flushCache="true">
        SELECT <include refid="Base_Column"/>
        from  RB_AUTO_SEQ
        where ATTR_KEY = #{attrKey}
        <if test="seqResetParam != null and seqResetParam.length() > 0">
            AND SEQ_RESET_PARAM = #{seqResetParam}
        </if>
    </select>
</mapper>