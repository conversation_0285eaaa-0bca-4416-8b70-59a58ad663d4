<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl">

  <select id="selectChannelControl" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl">
    select <include refid="Base_Column"/>
    from RB_CHANNEL_CONTROL
    <where>
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="controlSeqNo != null and controlSeqNo.length() > 0">
      AND CONTROL_SEQ_NO = #{controlSeqNo}
    </if>
    <if test="baseAcctNo != null and baseAcctNo.length() > 0">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="controlType != null and controlType.length() > 0">
      AND CONTROL_TYPE = #{controlType}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="controlStatus != null and controlStatus.length() > 0">
      AND CONTROL_STATUS = #{controlStatus}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="selectChannelControlEffecting" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl">
    select <include refid="Base_Column"/>
    from RB_CHANNEL_CONTROL
    where CONTROL_STATUS = 'A'
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="controlSeqNo != null">
      AND CONTROL_SEQ_NO = #{controlSeqNo}
    </if>
    <if test="baseAcctNo != null">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="controlType != null">
      AND CONTROL_TYPE = #{restraintType}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>


  <select id="selectChannelControlNotEnd" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl">
    select <include refid="Base_Column"/>
    from RB_CHANNEL_CONTROL
    where CONTROL_STATUS != 'E'
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo, jdbcType=VARCHAR}
    </if>
    <if test="controlType != null and controlType != ''">
      AND CONTROL_TYPE= #{controlType, jdbcType=VARCHAR}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>

</mapper>
