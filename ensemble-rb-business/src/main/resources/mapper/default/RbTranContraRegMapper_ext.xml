<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranContraReg">

  <select id="getTranContraInfoList" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranContraReg">
	  SELECT <include refid="Base_Column"/>
	  FROM RB_TRAN_CONTRA_REG
	  WHERE 1=1
		<if test="reference != null and reference !=''">
			AND REFERENCE = #{reference}
		</if>
		<if test="channelSeqNo != null and channelSeqNo !=''">
			AND CHANNEL_SEQ_NO = #{channelSeqNo}
		</if>
		<if test="subSeqNo != null and subSeqNo !=''">
			AND SUB_SEQ_NO = #{subSeqNo}
		</if>
	    <if test="seqNo != null and seqNo !=''">
			AND SEQ_NO = #{seqNo}
		</if>
		ORDER BY SEQ_NO DESC,REGISTER_SEQ_NO ASC
  </select>

	<select id="getTranContraInfoCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		select COUNT(*)
		from RB_TRAN_CONTRA_REG
		where 1=1
		<if test="seqNo != null and seqNo !=''">
			AND SEQ_NO = #{seqNo}
		</if>
		<if test="reference != null and reference !=''">
			AND REFERENCE = #{reference}
		</if>
	</select>
</mapper>
