<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">

    <select id="queryByIntRateFormNo"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_INT_RATE_FORM_MSG
        where 1=1
        <if test="intRateFormNo != null and intRateFormNo.length() > 0">
            and INT_RATE_FORM_NO = #{intRateFormNo}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
    </select>

    <select id="queryByIntRateFormNoForAll"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_INT_RATE_FORM_MSG
        where 1=1
        <if test="intRateFormNo != null and intRateFormNo.length() > 0">
            and INT_RATE_FORM_NO = #{intRateFormNo}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
    </select>

    <select id="queryByIntRateFormNoForAllByPage" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_INT_RATE_FORM_MSG
        where 1=1
        <if test="intRateFormNo != null and intRateFormNo.length() > 0">
            and INT_RATE_FORM_NO = #{intRateFormNo}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
    </select>

    <select id="queryByBaseAcctNoAndStatusNoIsN"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_INT_RATE_FORM_MSG
        where
        INT_AGREEMENT_STATUS !='N'
        <if test="baseAcctNo != null and baseAcctNo.length() > 0" >
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="intRateRbProdType != null and intRateRbProdType.length() > 0">
            and INT_RATE_RB_PROD_TYPE = #{intRateRbProdType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo.length() > 0">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="internalKey != null and internalKey !='' ">
            and internal_key = #{internalKey}
        </if>
    </select>

    <select id="queryByBaseAcctNoForAll"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_INT_RATE_FORM_MSG
        where 1=1
        <if test="baseAcctNo != null and baseAcctNo.length() > 0">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            and CLIENT_NO = #{clientNo}
        </if>
    </select>

    <select id="queryByBaseAcctNotClose"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_INT_RATE_FORM_MSG
        <where>
            <if test="intAgreementStatus != null and intAgreementStatus != '' ">
                INT_AGREEMENT_STATUS = #{intAgreementStatus}
            </if>
            <if test="baseAcctNo != null and baseAcctNo.length() > 0">
                and BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="acctSeqNo != null and acctSeqNo.length() > 0">
                and ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="clientNo != null and clientNo.length() > 0">
                and CLIENT_NO = #{clientNo}
            </if>
            <if test="ccy  != null and ccy.length() > 0">
                and CCY = #{ccy}
            </if>
        </where>
    </select>

    <update id="updateForAll" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        update RB_INT_RATE_FORM_MSG
        <set>
            <if test="intRateFormNo != null and intRateFormNo != '' ">
                INT_RATE_FORM_NO = #{intRateFormNo,jdbcType=VARCHAR},
            </if>
            <if test="addAgreementFlag != null and addAgreementFlag != '' ">
                ADD_AGREEMENT_FLAG = #{addAgreementFlag,jdbcType=VARCHAR},
            </if>
            <if test="preIntRateFormNo != null and preIntRateFormNo != '' ">
                PRE_INT_RATE_FORM_NO = #{preIntRateFormNo,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null and ccy != '' ">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="newAcctNoFlag != null and newAcctNoFlag != '' ">
                NEW_ACCT_NO_FLAG = #{newAcctNoFlag,jdbcType=VARCHAR},
            </if>
            <if test="clientName != null and clientName != '' ">
                CLIENT_NAME = #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="intRateRbProdType != null and intRateRbProdType != '' ">
                INT_RATE_RB_PROD_TYPE = #{intRateRbProdType,jdbcType=VARCHAR},
            </if>
            <if test="rbProdTerm != null and rbProdTerm != '' ">
                RB_PROD_TERM = #{rbProdTerm,jdbcType=VARCHAR},
            </if>
            <if test="intRateFormApplyType != null and intRateFormApplyType != '' ">
                INT_RATE_FORM_APPLY_TYPE = #{intRateFormApplyType,jdbcType=VARCHAR},
            </if>
            <if test="intRateTerm != null and intRateTerm != '' ">
                INT_RATE_TERM = #{intRateTerm,jdbcType=VARCHAR},
            </if>
            <if test="discBaseRate != null ">
                DISC_BASE_RATE = #{discBaseRate,jdbcType=DECIMAL},
            </if>
            <if test="floatPoint != null ">
                FLOAT_POINT = #{floatPoint,jdbcType=DECIMAL},
            </if>
            <if test="realRate != null ">
                REAL_RATE = #{realRate,jdbcType=DECIMAL},
            </if>
            <if test="priAmtLimit != null ">
                PRI_AMT_LIMIT = #{priAmtLimit,jdbcType=DECIMAL},
            </if>
            <if test="intAgreementStatus != null and intAgreementStatus != '' ">
                INT_AGREEMENT_STATUS = #{intAgreementStatus,jdbcType=VARCHAR},
            </if>
            <if test="authClientFlag != null and authClientFlag != '' ">
                AUTH_CLIENT_FLAG = #{authClientFlag,jdbcType=VARCHAR},
            </if>
            <if test="reason != null and reason != '' ">
                REASON = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="authClientPayment != null ">
                AUTH_CLIENT_PAYMENT = #{authClientPayment,jdbcType=DECIMAL},
            </if>
            <if test="validFromDate != null ">
                VALID_FROM_DATE = #{validFromDate,jdbcType=DATE},
            </if>
            <if test="validThruDate != null ">
                VALID_THRU_DATE = #{validThruDate,jdbcType=DATE},
            </if>
            <if test="intValidFromDate != null ">
                INT_VALID_FROM_DATE = #{intValidFromDate,jdbcType=DATE},
            </if>
            <if test="intValidThruDate != null ">
                INT_VALID_THRU_DATE = #{intValidThruDate,jdbcType=DATE},
            </if>
            <if test="lastChangeDate != null ">
                LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=DATE},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="intRateFormNo != null and intRateFormNo != '' ">
                AND INT_RATE_FORM_NO = #{intRateFormNo,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <update id="updateForByIntRateFormNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        update RB_INT_RATE_FORM_MSG
        <set>
            <if test="intAgreementStatus != null and intAgreementStatus != '' ">
                INT_AGREEMENT_STATUS = #{intAgreementStatus,jdbcType=VARCHAR},
            </if>
            <if test="acctSeqNo != null and acctSeqNo != '' ">
                ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="intRateFormApplyType != null and intRateFormApplyType != '' ">
                INT_RATE_FORM_APPLY_TYPE = #{intRateFormApplyType,jdbcType=VARCHAR},
            </if>
            <if test="reason != null and reason != '' ">
                REASON = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="internalKey != null and internalKey != '' ">
                INTERNAL_KEY = #{internalKey},
            </if>
        </set>
        <where>
            <if test="intRateFormNo != null and intRateFormNo != '' ">
                AND INT_RATE_FORM_NO = #{intRateFormNo,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>

    <update id="updateStatusByInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg">
        update RB_INT_RATE_FORM_MSG
        <set>
            <if test="intAgreementStatus != null and intAgreementStatus != '' ">
                INT_AGREEMENT_STATUS = #{intAgreementStatus,jdbcType=VARCHAR},
            </if>
            <if test="reason != null and reason != '' ">
                REASON = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null and clientNo != '' ">
                CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <if test="intRateFormNo != null and intRateFormNo != '' ">
                AND INT_RATE_FORM_NO = #{intRateFormNo,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and ccy != '' ">
                AND CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    
</mapper>