<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.PtPaymentTranHist">

	<select id="selectDataByRefAndSter" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column" />
		FROM pt_payment_tran_hist
		WHERE  channel_step_no = #{channelStep,jdbcType=VARCHAR}
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		<if test="channelFlag != null and channelFlag !=''">
			AND channel_flag = #{channelFlag,jdbcType=VARCHAR}
		</if>
		<if test="sourceType != null and sourceType !=''">
			AND source_type = #{sourceType,jdbcType=VARCHAR}
		</if>
		AND channel_ref_no =  #{channelRef,jdbcType=VARCHAR}
		<if test="startChannelDate != null and startChannelDate.length() > 0">
			AND  channel_date <![CDATA[>=]]> #{startChannelDate,jdbcType=VARCHAR}
		</if>
		<if test="endChannelDate != null and endChannelDate.length() > 0">
			AND  channel_date <![CDATA[<=]]> #{endChannelDate,jdbcType=VARCHAR}
		</if>
		<if test="companyList != null">
			AND COMPANY IN
			<foreach item="item"  index="index" collection="companyList"  open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
	</select>

	<select id="selectDataByChanAndDate" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column" />
		FROM pt_payment_tran_hist
		WHERE  settle_branch = #{settleBranch,jdbcType=VARCHAR}
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND channel_flag = #{channelFlag,jdbcType=VARCHAR}
		<if test="startDate != null and startDate.length() > 0">
			AND  collate_date <![CDATA[>=]]> #{startDate,jdbcType=VARCHAR}
		</if>
		<if test="endDate != null and endDate.length() > 0">
			AND  collate_date <![CDATA[<=]]> #{endDate,jdbcType=VARCHAR}
		</if>
		<if test="companyList != null">
			AND COMPANY IN
			<foreach item="item"  index="index" collection="companyList"  open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
	</select>

	<select id="selectByDateBranch" parameterType="java.util.Map"
			resultMap="Base_Result_Map"  fetchSize="100000">
		SELECT <include refid="Base_Column" />
		FROM pt_payment_tran_hist
		WHERE  settle_branch = #{settleBranch,jdbcType=VARCHAR}
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND channel_flag = #{channelFlag,jdbcType=VARCHAR}
		<if test="startDate != null">
			<![CDATA[  AND  collate_date >=#{startDate,jdbcType=VARCHAR}]]>
		</if>
		<if test="endDate != null">
			<![CDATA[   AND  collate_date <= #{endDate,jdbcType=VARCHAR}]]>
		</if>
		<if test="collateDate != null">
			<![CDATA[   AND  collate_date = #{collateDate,jdbcType=VARCHAR}]]>
		</if>
		<if test="companyList != null">
			AND COMPANY IN
			<foreach item="item"  index="index" collection="companyList"  open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
	</select>

	<select id="selectByChannelTypeAndRefNo" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column" />
		FROM pt_payment_tran_hist
		WHERE reference = #{refNo,jdbcType=VARCHAR}
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND channel = #{channelType,jdbcType=VARCHAR}
		<if test="settleStep != null">
			AND SETTLE_STEP = #{settleStep,jdbcType=VARCHAR}
		</if>
		<if test="settleNo != null">
			AND SETTLE_NO = #{settleNo,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="selectByRefNoAndSubSeqNo" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column" />
		FROM pt_payment_tran_hist
		WHERE reference = #{refNo,jdbcType=VARCHAR}
		<if test="channel != null and channel != ''">
			AND channel = #{channelType,jdbcType=VARCHAR}
		</if>
		<if test="channelSubSeqNo != null and channelSubSeqNo != ''">
			AND CHANNEL_SUB_SEQ_NO = #{channelSubSeqNo,jdbcType=VARCHAR}
		</if>
		<if test="settleStep != null">
			AND SETTLE_STEP = #{settleStep,jdbcType=VARCHAR}
		</if>
		<if test="settleNo != null">
			AND SETTLE_NO = #{settleNo,jdbcType=VARCHAR}
		</if>
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.PtPaymentTranHist">
		update pt_payment_tran_hist
		<set>
			<if test="collateDate != null">
				collate_date = #{collateDate,jdbcType=VARCHAR},
			</if>
			<if test="channelDate != null">
				channel_date = #{channelDate,jdbcType=VARCHAR},
			</if>
			<if test="eventType != null">
				event_type = #{eventType,jdbcType=VARCHAR},
			</if>
			<if test="origChannelRefNo != null">
				orig_channel_ref_no = #{origChannelRefNo,jdbcType=VARCHAR},
			</if>
			<if test="origChannelStepNo != null">
				orig_channel_step_no = #{origChannelStepNo,jdbcType=VARCHAR},
			</if>
			<if test="origChannelDate != null">
				orig_channel_date = #{origChannelDate,jdbcType=VARCHAR},
			</if>
			<if test="accountDesc != null">
				account_desc = #{accountDesc,jdbcType=VARCHAR},
			</if>
			<if test="trustedPayNo != null">
				trusted_pay_no = #{trustedPayNo,jdbcType=VARCHAR},
			</if>
			<if test="lastChangeDate != null">
				last_change_date = #{lastChangeDate,jdbcType=VARCHAR},
			</if>
			<if test="rbSeqNo != null">
				rb_seq_no = #{rbSeqNo,jdbcType=BIGINT},
			</if>
			<if test="authUserId != null">
				auth_user_id = #{authUserId,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR}
			</if>
		</set>
		where status = 'A'
		<if test="channelRefNo != null">
			AND channel_ref_no = #{channelRefNo,jdbcType=VARCHAR}
		</if>
		<if test="channelFlag != null">
			AND channel_flag = #{channelFlag,jdbcType=VARCHAR}
		</if>
		<if test="reference != null">
			AND reference = #{reference,jdbcType=VARCHAR}
		</if>
		<if test="channelStepNo != null">
			AND channel_step_no = #{channelStepNo,jdbcType=VARCHAR}
		</if>
		<if test="companyList != null">
			AND COMPANY IN
			<foreach item="item"  index="index" collection="companyList"  open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
	</update>

	<select id="selectByReference" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column" />
		FROM pt_payment_tran_hist
		WHERE  reference = #{reference,jdbcType=VARCHAR}
		<if test="companyList != null">
			AND COMPANY IN
			<foreach item="item"  index="index" collection="companyList"  open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
	</select>


	<update id="updateByRefNo" parameterType="java.util.Map">
		update pt_payment_tran_hist
		<set>
			<if test="bean.retCode != null and bean.retCode != '' ">
				RET_CODE = #{bean.retCode},
			</if>
		    <if test="bean.resSeqNo != null and bean.resSeqNo != '' ">
			RES_SEQ_NO = #{bean.resSeqNo},
		    </if>
			<if test="bean.hangStatus != null and  bean.hangStatus != '' ">
				HANG_STATUS = #{bean.hangStatus},
			</if>
			<if test="bean.retMsg != null and  bean.retMsg != '' ">
				RET_MSG = #{bean.retMsg},
			</if>
			<if test="bean.acctPaymentStatus != null and  bean.acctPaymentStatus != ''">
				ACCT_PAYMENT_STATUS = #{bean.acctPaymentStatus},
			</if>
			<if test="bean.reference != null and  bean.reference != ''">
				reference = #{bean.reference}
			</if>
		</set>
		where
		<if test="bean.clientNo != null ">
			CLIENT_NO = #{bean.clientNo}
		</if>
		<if test="bean.settleStep != null">
			AND settle_step = #{bean.settleStep}
		</if>
		<if test="acctPaymentStatus != null">
			AND ACCT_PAYMENT_STATUS = #{acctPaymentStatus}
		</if>
		<if test="bean.reference != null and  bean.reference != ''">
			AND reference = #{bean.reference}
		</if>
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>


	<select id="selectByRefNoAndStepNo" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column"/>
		FROM <include refid="Table_Name" />
		WHERE reference = #{channelRefNo,jdbcType=VARCHAR}
		<if test="channelStepNo != null and channelStepNo != '' ">
			AND  settle_step = #{channelStepNo,jdbcType=VARCHAR}
		</if>
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		order by acct_payment_status desc
	</select>

	<select id="selectByChannelRefNoAndStepNo" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column"/>
		FROM <include refid="Table_Name" />
		WHERE channel_seq_no = #{channelRefNo,jdbcType=VARCHAR}
		<if test="channelStepNo != null and channelStepNo != '' ">
			AND  settle_step = #{channelStepNo,jdbcType=VARCHAR}
		</if>
		<!--多法人改造 by LIYUANV-->
		<if test="companyList != null">
			AND COMPANY in
			<foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by acct_payment_status desc
	</select>

	<select id="selectByOption" parameterType="java.util.Map"
			resultMap="Base_Result_Map">
		SELECT <include refid="Base_Column" />
		FROM pt_payment_tran_hist
		WHERE PT_OPERATE_TYPE = #{ptOperateType,jdbcType=VARCHAR}
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>


	<select id="getPaymentTranHistCount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.PtPaymentTranHist">
		SELECT
		<include refid="Base_Column" />
		from
		pt_payment_tran_hist
		<where>
		<if test="startDate != null and endDate != null ">
			and COLLATE_DATE BETWEEN #{startDate} and #{endDate}
		</if>
		<if test="channelType != null and channelType != '' ">
			AND  CHANNEL = #{channelType,jdbcType=VARCHAR}
		</if>
		<if test="collateBatchNo != null and collateBatchNo !='' ">
			and COLLATE_BATCH_NO = #{collateBatchNo,jdbcType=VARCHAR}
		</if>
<!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>
</mapper>