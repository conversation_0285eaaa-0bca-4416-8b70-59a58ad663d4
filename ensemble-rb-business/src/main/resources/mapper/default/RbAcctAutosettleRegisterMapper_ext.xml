<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAutosettleRegister">

    <select id="getAutosettleRegister" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAutosettleRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <trim suffixOverrides="AND">
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo} AND
                </if>
                <if test="baseAcctNo != null and  baseAcctNo != '' ">
                    BASE_ACCT_NO = #{baseAcctNo} AND
                </if>
                <if test="prodType != null and  prodType != '' ">
                    PROD_TYPE = #{prodType} AND
                </if>
                <!--多法人改造 by LIYUANV-->
                <if test="company != null and company != '' ">
                    AND COMPANY = #{company}
                </if>
            </trim>
        </where>
    </select>

</mapper>
