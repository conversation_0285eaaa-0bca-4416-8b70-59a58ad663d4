<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchChange">

    <select id="getBranchChangeByTranDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchChange">
        select *
        from RB_BRANCH_CHANGE
        <where>
        <if test="fromDate != null and toDate!= null ">
            AND TRAN_DATE BETWEEN  #{fromDate} AND #{toDate}
        </if>
        </where>
    </select>
    <select id="getBranchChangeByTranDateAndCompany" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchChange">
        SELECT <include refid="Base_Column"/>
        from RB_BRANCH_CHANGE
        <where>
            <if test="fromDate != null and toDate!= null ">
                AND TRAN_DATE BETWEEN  #{fromDate} AND #{toDate}
            </if>
            <if test="companyList != null">
                AND COMPANY in
                <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <update id="updateCombo" parameterType="java.util.Map">
        UPDATE ${tableName}
        <set>
            ${set}
        </set>
        <where>
            <trim prefix="(" suffix=")" suffixOverrides="AND">
                <if test="clientNo != null" >
                    CLIENT_NO = #{clientNo} AND
                </if>
                ${where}
            </trim>
        </where>
    </update>

    <update id="updateRbAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT
        SET ACCT_BRANCH = #{newBranch},
        APPLY_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and ACCT_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbBaseAcctByAcctBranch" parameterType="java.util.Map">
        UPDATE RB_BASE_ACCT
        SET ACCT_BRANCH = #{newBranch},
        APPLY_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and ACCT_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbVoucherLostByTranBranch" parameterType="java.util.Map">
        UPDATE RB_VOUCHER_LOST
        SET TRAN_BRANCH = #{newBranch},
        UNCHAIN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and TRAN_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbAgreementByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AGREEMENT
        SET TRAN_BRANCH = #{newBranch}
        WHERE AGREEMENT_KEY  between #{startKey} and #{endKey}  and TRAN_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbAdRegisterByTranBranch" parameterType="java.util.Map">
        UPDATE RB_AD_REGISTER
        SET TRAN_BRANCH = #{newBranch},
        OPEN_BRANCH = #{newBranch}
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and TRAN_BRANCH= #{oldBranch}
    </update>

    <update id="updateRbAcctClientRelationByTranBranch" parameterType="java.util.Map">
        UPDATE RB_ACCT_CLIENT_RELATION
        SET ACCT_BRANCH = #{newBranch},
        WHERE INTERNAL_KEY  between #{startKey} and #{endKey}  and ACCT_BRANCH= #{oldBranch}
    </update>

</mapper>
