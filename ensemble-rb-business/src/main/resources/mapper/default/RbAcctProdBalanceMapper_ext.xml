<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance">

    <delete id="deleteAll" parameterType="java.util.Map">
        DELETE FROM RB_PROD_BALANCE
        <where>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </delete>

</mapper>
