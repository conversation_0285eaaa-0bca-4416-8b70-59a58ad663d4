<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchDossIndvl">
	<update id="updateWithdrawAmt">
		UPDATE
		<include refid="Table_Name"/>
		<set>
			per_amt_withdraw = per_amt_withdraw + #{porIntTot},
			per_amt_tot = per_amt_tot - #{porIntTot},
			last_change_date = SYSDATE
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="internalKey != null ">
					tranBranch = #{tranBranch}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>
</mapper>
