<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.cm.reg.EgFileDetailsResult">
    
    <insert id="insertEgFileDetailsResult" parameterType="com.dcits.ensemble.rb.business.model.cm.reg.EgFileDetailsResult">
        insert into
        ENS_EG.EG_FILE_DETAILS_RESULT (RUN_DATE,FILE_NAME ,FILE_PATH ,DB_TYPE ,DEAL_FLAG ,FILE_INFO_SUM)
        VALUES(#{egFileDetailsResult.runDate},#{egFileDetailsResult.fileName},#{egFileDetailsResult.filePath},#{egFileDetailsResult.dbType},#{egFileDetailsResult.dealFlag},#{egFileDetailsResult.fileInfoSum})
    </insert>
 

</mapper>
