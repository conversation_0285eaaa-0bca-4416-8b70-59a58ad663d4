<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementLimit">

  <select id="selectMbPcpLimitByLimitType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementLimit">
    select
    <include refid="Base_Column"/>
    from RB_PCP_AGREEMENT_LIMIT
    where LIMIT_TYPE = 'PM'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectMbPcpLimitByTypePW" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementLimit">
    select
    <include refid="Base_Column"/>
    from RB_PCP_AGREEMENT_LIMIT
    where LIMIT_TYPE = 'PW'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectMbPcpLimitByTypePDorPT" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementLimit">
    select
    <include refid="Base_Column"/>
    from RB_PCP_AGREEMENT_LIMIT
    where (LIMIT_TYPE = 'PD'
    OR LIMIT_TYPE = 'PT')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectMbPcpLimitByTypePc" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementLimit">
    select
    <include refid="Base_Column"/>
    from RB_PCP_AGREEMENT_LIMIT
    where LIMIT_TYPE = 'PC'
    AND END_DATE = #{runDate}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementLimit">
    SELECT <include refid="Base_Column"/>
    FROM RB_PCP_AGREEMENT_LIMIT
    WHERE AGREEMENT_ID =#{agreementId}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
