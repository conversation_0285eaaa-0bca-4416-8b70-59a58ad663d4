<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
  <!--多法人改造 by LIYUANV-->
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where APPLY_NO = #{applyNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--多法人改造 by LIYUANV-->
  <select id="getCardMakeRegsByApplyNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where APPLY_NO = #{applyNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCdMakeCardRegByProdType" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where PROD_TYPE = #{prodType}
    and TRAN_BRANCH in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="applyDate != null">
      AND APPLY_DATE = #{applyDate}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getCdMakeCardRegByUserId" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    <where>
      <if test="userId != null and userId != ''">
        USER_ID = #{userId}
      </if>
      <if test="applyStartDate != null and applyEndDate != null">
        AND APPLY_DATE BETWEEN #{applyStartDate} and #{applyEndDate}
      </if>
      <if test="applyNo != null and applyNo != ''">
        AND APPLY_NO = #{applyNo}
      </if>
      <if test="tranBranchs != null and tranBranchs != ''">
        AND TRAN_BRANCH IN
        <foreach item="item" index="index" collection="tranBranchs" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="prodType != null and prodType !=''">
        AND PROD_TYPE = #{prodType}
      </if>
    </where>
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCardMakeRegsByApplyDate" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where APPLY_DATE between #{startDate} and #{endDate}
    AND MAKE_CD_STATUS = '0'
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="applyBranch != null">
      AND TRAN_BRANCH = #{applyBranch}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getCardMakeRegsReserveByApplyDate" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where APPLY_DATE between #{startDate} and #{endDate}
    AND MAKE_CD_STATUS = '0'
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="applyBranch != null">
      AND TRAN_BRANCH = #{applyBranch}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCardMakeRegsByApplyDateStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where APPLY_DATE    between #{startDate} and #{endDate}
    AND MAKE_CD_STATUS = '3'
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="applyNo != null">
      AND APPLY_NO = #{applyNo}
    </if>
    <if test="applyBranch != null">
      AND TRAN_BRANCH = #{applyBranch}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCardMakeRegsInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where APPLY_DATE    between #{startDate} and #{endDate}
    AND MAKE_CD_STATUS = '0'
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="applyBranch != null">
      AND TRAN_BRANCH = #{applyBranch}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!--多法人改造 by LIYUANV-->
  <select id="getCardMakeRegs" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select TRAN_BRANCH,prod_type,sum(card_num) as card_num
    from CD_MAKE_CARD_REG
    where APPLY_DATE    between #{startDate} and #{endDate}
    AND MAKE_CD_STATUS = '0'
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="applyBranch != null">
      AND TRAN_BRANCH = #{applyBranch}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    group by TRAN_BRANCH,PROD_TYPE
  </select>

  <!--多法人改造 by LIYUANV-->
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    delete from CD_MAKE_CARD_REG
    where APPLY_NO = #{applyNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>

  <!--多法人改造 by LIYUANV-->
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    update CD_MAKE_CARD_REG
    <set>
      <if test="applyDate != null">
        APPLY_DATE = #{applyDate},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="cardApplyType != null">
        CARD_APPLY_TYPE = #{cardApplyType},
      </if>
      <if test="areaCode != null">
        AREA_CODE = #{areaCode},
      </if>
      <if test="cardNum != null">
        CARD_NUM = #{cardNum},
      </if>
      <if test="makeCdStatus != null">
        MAKE_CD_STATUS = #{makeCdStatus},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="batchJobNo != null">
        BATCH_JOB_NO = #{batchJobNo},
      </if>
      <if test="remark != null">
        REMARK = #{remark},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
      <if test="luckyCardFlag != null">
        LUCKY_CARD_FLAG = #{luckyCardFlag},
      </if>
      <if test="makeCardType != null">
        MAKE_CARD_TYPE = #{makeCardType},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType}
      </if>
    </set>
    where APPLY_NO = #{applyNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    insert into CD_MAKE_CARD_REG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        APPLY_NO,
      </if>
      <if test="applyDate != null">
        APPLY_DATE,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="cardApplyType != null">
        CARD_APPLY_TYPE,
      </if>
      <if test="areaCode != null">
        AREA_CODE,
      </if>
      <if test="cardNum != null">
        CARD_NUM,
      </if>
      <if test="makeCdStatus != null">
        MAKE_CD_STATUS,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="batchJobNo != null">
        BATCH_JOB_NO,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="luckyCardFlag != null">
        LUCKY_CARD_FLAG,
      </if>
      <if test="makeCardType != null">
        MAKE_CARD_TYPE,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        #{applyNo},
      </if>
      <if test="applyDate != null">
        #{applyDate},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="cardApplyType != null">
        #{cardApplyType},
      </if>
      <if test="areaCode != null">
        #{areaCode},
      </if>
      <if test="cardNum != null">
        #{cardNum},
      </if>
      <if test="makeCdStatus != null">
        #{makeCdStatus},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="batchJobNo != null">
        #{batchJobNo},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="luckyCardFlag != null">
        #{luckyCardFlag},
      </if>
      <if test="makeCardType != null">
        #{makeCardType},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
    </trim>
  </insert>

  <!--多法人改造 by LIYUANV-->
  <update id="updateStatusByApplyNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    update CD_MAKE_CARD_REG
    <set>
      <if test="makeCdStatus != null">
        MAKE_CD_STATUS = #{makeCdStatus},
      </if>
    </set>
    where APPLY_NO = #{applyNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getCdMkCdRegByApplyNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where APPLY_NO = #{applyNo}
    AND MAKE_CD_STATUS = '0'
  </select>

  <select id="getCdMkCdRegByProdType" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where MAKE_CD_STATUS = '0'
    AND MAKE_CARD_TYPE in ('S', 'A')
    <if test="prodType != null and prodType.length() > 0">
      AND PROD_TYPE = #{prodType}
    </if>
  </select>

  <select id="getCdMkCdRegByapplyNo2" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where MAKE_CARD_TYPE in ('S', 'A')
    AND MAKE_CD_STATUS IN
    <foreach item="item" index="index" collection="makeCdStatus" open="(" separator="," close=")">
        #{item}
    </foreach>
    <if test="applyNo != null and applyNo.length() > 0">
      AND APPLY_NO = #{applyNo}
    </if>
    <if test="applyBranch != null and applyBranch.length() > 0">
      AND TRAN_BRANCH = #{applyBranch}
    </if>
    <if test="makeCardDate != null">
      AND MAKE_CARD_DATE = #{makeCardDate}
    </if>
    <if test="startDate != null and endDate != null">
      AND APPLY_DATE BETWEEN #{startDate} and #{endDate}
    </if>
  </select>

  <select id="getCardMakeRegs1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    where MAKE_CD_STATUS IN
    <foreach item="item" index="index" collection="makeCdStatus" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND MAKE_CARD_TYPE in ('S', 'A')
    <if test="prodType != null and prodType.length() > 0">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="applyBranch != null and applyBranch.length() > 0">
      AND TRAN_BRANCH = #{applyBranch}
    </if>
    <if test="makeCardDate != null">
      AND MAKE_CARD_DATE = #{makeCardDate}
    </if>
    <if test="startDate != null and endDate != null">
      AND APPLY_DATE BETWEEN #{startDate} and #{endDate}
    </if>
    <if test="applyNos != null">
      AND APPLY_NO IN
      <foreach item="item" index="index" collection="applyNos" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by APPLY_NO DESC
  </select>

  <select id="queryCardRegByCondition" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select
    <include refid="Base_Column"/>
    from CD_MAKE_CARD_REG
    WHERE MAKE_CD_STATUS IN ('6', '7')
    <if test="applyNo != null and applyNo != ''" >
      AND APPLY_NO = #{applyNo, jdbcType = VARCHAR}
    </if>
    <if test="startDate != null">
      <![CDATA[AND APPLY_DATE >= #{startDate}]]>
    </if>
    <if test="endDate != null">
      <![CDATA[AND APPLY_DATE <= #{endDate}]]>
    </if>
    <if test="userId != null and userId != ''">
      AND USER_ID = #{userId, jdbcType = VARCHAR}
    </if>
    <if test="docType != null and docType != ''">
      AND DOC_TYPE = #{docType, jdbcType = VARCHAR}
    </if>
    <if test="tranBranch != null and tranBranch != ''">
      AND TRAN_BRANCH = #{tranBranch, jdbcType = VARCHAR}
    </if>
    ORDER BY APPLY_DATE DESC
  </select>

  <select id="queryCardRegCountByCondition" parameterType="java.util.Map" resultType="java.util.HashMap">
    select count(1) TOTAL_CNT
    from CD_MAKE_CARD_REG
    WHERE MAKE_CD_STATUS = 1
    <if test="applyNo != null and applyNo != ''" >
      AND APPLY_NO = #{applyNo, jdbcType = VARCHAR}
    </if>
    <if test="startDate != null">
      <![CDATA[AND APPLY_DATE >= #{startDate}]]>
    </if>
    <if test="endDate != null">
      <![CDATA[AND APPLY_DATE <= #{endDate}]]>
    </if>
    <if test="userId != null and userId != ''">
      AND USER_ID = #{userId, jdbcType = VARCHAR}
    </if>
    <if test="docType != null and docType != ''">
      AND DOC_TYPE = #{docType, jdbcType = VARCHAR}
    </if>
    <if test="tranBranch != null and tranBranch != ''">
      AND TRAN_BRANCH = #{tranBranch, jdbcType = VARCHAR}
    </if>
    ORDER BY APPLY_DATE DESC
  </select>

  <select id="queryCardRegByConditionPage" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdMakeCardReg">
    select <include refid="Base_Column"/> from (
      SELECT  rownum r,<include refid="Base_Column"/>
      from CD_MAKE_CARD_REG
      WHERE MAKE_CD_STATUS = 1
      <if test="applyNo != null and applyNo != ''" >
        AND APPLY_NO = #{applyNo, jdbcType = VARCHAR}
      </if>
      <if test="startDate != null">
        <![CDATA[AND APPLY_DATE >= #{startDate}]]>
      </if>
      <if test="endDate != null">
        <![CDATA[AND APPLY_DATE <= #{endDate}]]>
      </if>
      <if test="userId != null and userId != ''">
        AND USER_ID = #{userId, jdbcType = VARCHAR}
      </if>
      <if test="docType != null and docType != ''">
        AND DOC_TYPE = #{docType, jdbcType = VARCHAR}
      </if>
      <if test="tranBranch != null and tranBranch != ''">
        AND TRAN_BRANCH = #{tranBranch, jdbcType = VARCHAR}
      </if>
      ORDER BY APPLY_DATE DESC
    ) a
    <where>
      <if test="startRowNum != null and startRowNum != '' and endRowNum != null and endRowNum != ''">
        a.r between #{startRowNum} and #{endRowNum}
      </if>
    </where>
  </select>
</mapper>
