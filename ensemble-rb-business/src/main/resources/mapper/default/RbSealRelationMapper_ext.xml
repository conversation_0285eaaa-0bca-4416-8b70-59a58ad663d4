<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSealRelation">

  <select id="getMbSealRelationList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSealRelation">
    SELECT <include refid="Base_Column"/>
    FROM RB_SEAL_RELATION
    WHERE INTERNAL_KEY > 0
    <if test="internalKey != null and internalKey.length() > 0">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="docType != null and docType.length() > 0">
      AND DOC_TYPE = #{docType}
    </if>
    <if test="voucherNo != null and voucherNo.length() > 0">
      AND VOUCHER_NO = #{voucherNo}
    </if>
    <if test="prefix != null and prefix.length() > 0">
      AND PREFIX = #{prefix}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </select>
   <select id="getMbSealRelationList1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSealRelation">
    SELECT <include refid="Base_Column"/>
    FROM RB_SEAL_RELATION
    where DOC_TYPE = #{docType}
    AND VOUCHER_NO = #{voucherNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
     <if test="prefix != null and prefix.length() > 0">
       AND PREFIX = #{prefix}
     </if>
       <!-- 多法人改造 by luocwa -->
       <if test="company != null and company != '' ">
           AND COMPANY = #{company}
       </if>
  </select>
  <select id="selectByInternalKey1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSealRelation">
    select <include refid="Base_Column"/>
    from RB_SEAL_RELATION
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
          AND COMPANY = #{company}
      </if>
  </select>
</mapper>
