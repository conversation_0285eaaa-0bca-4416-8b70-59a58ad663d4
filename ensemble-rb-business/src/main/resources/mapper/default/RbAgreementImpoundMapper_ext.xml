<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementImpound">

  <select id="getMbAgreementImpoundList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementImpound">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_IMPOUND
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null and clientNo !=''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbAgreementImpoundByBenefitSide" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementImpound">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_IMPOUND
    where BENEFIT_BASE_ACCT_NO = #{benefitBaseAcctNo}
    <if test="benefitProdType != null and benefitProdType !=''">
      AND BENEFIT_PROD_TYPE = #{benefitProdType}
    </if>
    <if test="benefnitSeqNo != null and benefnitSeqNo !=''">
      AND BENENFIT_SEQ_NO = #{benefnitSeqNo}
    </if>
    <if test="benenfitCCY != null and benenfitCCY !=''">
      AND BENENFIT_CCY = #{benenfitCCY}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
<!--  <select id="selectPCDAgreement" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementImpound" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" >-->
<!--    SELECT mai.AGREEMENT_ID,mai.AUTH_USER_ID,mai.BENEFIT_BASE_ACCT_NO,mai.BENEFIT_PROD_TYPE,mai.BENENFIT_ACCT_NAME,-->
<!--   mai.BENENFIT_CCY,mai.BENENFIT_SEQ_NO,mai.CCY,mai.CLIENT_NO,mai.DEDUCTION_JUDICIARY_NAME,mai.IMPOUND_TYPE,mai.IMPOUND_END_FLAG,-->
<!--   mai.IMPOUND_AMT,mai.INTERNAL_KEY,mai.JUDICIARY_DOCUMENT_ID,mai.JUDICIARY_DOCUMENT_TYPE,mai.JUDICIARY_OFFICER_NAME,-->
<!--   mai.JUDICIARY_OTH_DOCUMENT_ID,mai.JUDICIARY_OTH_DOCUMENT_TYPE,mai.JUDICIARY_OTH_OFFICER_NAME,mai.LAW_NO,mai.NARRATIVE,-->
<!--   mai.NEXT_DEAL_DATE,mai.PERIOD_FREQ,mai.RES_SEQ_NO,mai.START_DATE,mai.TOTAL_AMT,mai.TOTAL_TIMES,-->
<!--   mai.IMPOUND_TOTAL_AMT,mai.TRANSFER_TIMES,mai.TRAN_BRANCH,mai.TRAN_TIMESTAMP,mai.USER_ID-->
<!--     FROM RB_agreement ma,RB_AGREEMENT_IMPOUND mai-->
<!--    WHERE ma.AGREEMENT_STATUS='A'-->
<!--    and ma.AGREEMENT_ID=mai.AGREEMENT_ID-->
<!--    and ma.AGREEMENT_TYPE = 'PCD' order by mai.AGREEMENT_ID-->
<!--  </select>-->
  <select id="selectPCDAgreementForKeys" parameterType="java.util.Map" resultType="String">
    SELECT mai.agreement_id
    FROM RB_agreement ma,RB_AGREEMENT_IMPOUND mai
    WHERE ma.AGREEMENT_STATUS='A'
    and ma.AGREEMENT_ID=mai.AGREEMENT_ID
    and ma.AGREEMENT_TYPE = 'PCD'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND mai.agreement_id BETWEEN #{startKey} and #{endKey}
    ORDER BY mai.agreement_id
  </select>
  <update id="updateMbAgreementImpoundByAgreementId" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementImpound">
    update RB_AGREEMENT_IMPOUND
    <set>
      <if test="transferTimes != null">
        transfer_times = #{transferTimes},
      </if>
      <if test="impoundTotalAmt != null">
        IMPOUND_TOTAL_AMT = #{impoundTotalAmt},
      </if>
      <if test="nextDealDate != null">
        NEXT_DEAL_DATE = #{nextDealDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="agreementType != null">
        AGREEMENT_TYPE = #{agreementType},
      </if>
      <if test="agreementStatus != null">
        AGREEMENT_STATUS = #{agreementStatus},
      </if>
      <if test="startDate != null">
        START_DATE = #{startDate},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate},
      </if>
    </set>
    where AGREEMENT_ID = #{agreementId}
    and INTERNAL_KEY = #{internalKey}
    and CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
