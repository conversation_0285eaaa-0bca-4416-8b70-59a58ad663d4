<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
	<sql id="Base_Column2">
		A.UNCOUNTER_RESTRAINT_TYPE AS BLACK_LIST_NO,
		A.UNCOUNTER_DESC AS BLACK_LIST_DESC,
		A.CLIENT_NO AS CLIENT_NO,
		A.BASE_ACCT_NO AS BASE_ACCT_NO,
		A.ACCT_STATUS AS ACCT_STATUS,
		A.CLIENT_NAME AS CLIENT_NAME,
		A.DOCUMENT_ID AS DOCUMENT_ID,
		A.DOCUMENT_TYPE AS DOCUMENT_TYPE,
		A.EFFECT_DATE AS START_EFFECT_DATE,
		A<PERSON>EXPIRE_DATE AS END_EFFECT_DATE,
		<PERSON><PERSON>CO<PERSON>_DESC AS UNCOUNTER_DESC,
		A<PERSON>LIST_SOURCE AS ACCT_SOURCE_TYPE,
		A.UNCOUNTER_RESTRAINT_STATUS AS EXAMINE_FLAG,
		A.UPDATE_DATE AS EXAMINE_TIME,
		A.UPDATE_USER AS EXAMINE_TELLER,
		A.UPDATE_BRANCH AS INPUT_BRANCH,
		A.UPDATE_USER AS INPUT_USER_ID
	</sql>
	<select id="selectRbCounterRestraintsInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE uncounter_restraint_type = #{uncounterRestraintType}
		AND base_acct_no = #{baseAcctNo}
		AND UNCOUNTER_RESTRAINT_STATUS &lt;&gt; '3'
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectRbCounterRestraintsInfo2" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE uncounter_restraint_type = #{uncounterRestraintType}
		AND base_acct_no = #{baseAcctNo}
		AND UNCOUNTER_RESTRAINT_STATUS in ('3')
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectUnCounterInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE BATCH_NO = #{batchNo}
		<if test="successFlag != null and  successFlag != '' ">
			AND SUCCESS_FLAG = #{successFlag}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="selectActiveRes"
			parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints" >
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		<where>
			AND BASE_ACCT_NO = #{baseAcctNo}
			AND UNCOUNTER_RESTRAINT_STATUS = '2'
			AND <![CDATA[EFFECT_DATE <= now()  AND EXPIRE_DATE >= now()]]>
			<if test="clientNo != null and clientNo != '' ">
				AND CLIENT_NO = #{clientNo}
			</if>
		</where>

	</select>

	<select id="selectByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE    (UNCOUNTER_RESTRAINT_STATUS = '1' or  UNCOUNTER_RESTRAINT_STATUS = '3')
		AND  BASE_ACCT_NO = #{baseAcctNo}
		AND  UNCOUNTER_RESTRAINT_TYPE = #{uncounterRestraintType}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="selectByBaseAcctNo2" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE      BASE_ACCT_NO = #{baseAcctNo}
		AND  UNCOUNTER_RESTRAINT_TYPE = #{uncounterRestraintType}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectByGlobal" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE  UNCOUNTER_RESTRAINT_STATUS = '1'
		AND  DOCUMENT_TYPE = #{documentType}
		AND  DOCUMENT_ID = #{docunmentId}
		AND  UNCOUNTER_RESTRAINT_TYPE = #{uncounterRestraintType}
		AND  BASE_ACCT_NO is NULL
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="selectByGlobal2" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE  UNCOUNTER_RESTRAINT_STATUS = '3'
		AND  DOCUMENT_TYPE = #{documentType}
		AND  DOCUMENT_ID = #{docunmentId}
		AND  UNCOUNTER_RESTRAINT_TYPE = #{uncounterRestraintType}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectByBaseActNoAndUncounterNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		<where>
		<if test="documentType != null and  documentType != '' ">
			AND DOCUMENT_TYPE = #{documentType}
		</if>
		<if test="docunmentId != null and  docunmentId != '' ">
			AND DOCUMENT_ID = #{docunmentId}
		</if>
		<if test="clientNo != null and  clientNo != '' ">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="baseAcctNo != null and  baseAcctNo != '' ">
			AND BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="uncounterRestraintType != null and  uncounterRestraintType != '' ">
			AND  UNCOUNTER_RESTRAINT_TYPE = #{uncounterRestraintType}
		</if>
		<if test="uncounterNo != null and  uncounterNo != '' ">
			AND  UNCOUNTER_NO = #{uncounterNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>


	<select id="selectClientCheck" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE  UNCOUNTER_RESTRAINT_STATUS in ('1','3','5')
		AND UNCOUNTER_RESTRAINT_TYPE in  ('H001','H002','H003','H004','HX13','HX23','HX33','HX43','0001','0002','0003')
		AND (
		<if test="clientNo != null and  clientNo != '' ">
			(CLIENT_NO = #{clientNo})
		</if>
		<if test='clientNo == null ||  clientNo == "" '>
			1=2
		</if>
		<if test="docunmentId != null and  docunmentId != '' and documentType != null and  documentType != ''  ">
			OR (DOCUMENT_ID = #{docunmentId} AND  DOCUMENT_TYPE = #{documentType})
		</if>
		<if test='docunmentId == null ||  docunmentId == "" || documentType == null || documentType == ""  '>
			OR 1=2
		</if>
		<if test=" baseAcctNo != null and  baseAcctNo != '' ">
			OR (BASE_ACCT_NO = #{baseAcctNo})
		</if>
		<if test=' baseAcctNo == null || baseAcctNo == "" '>
			OR 1=2
		</if>
		)
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<update id="updateByBaseAcctNoAndUncounterNo"  >
		UPDATE <include refid="Table_Name" />
		<set>
			<if test="uncounterTime != null ">
				UNCOUNTER_TIME = #{uncounterTime},
			</if>
			<if test="uncounterRestraintStatus != null and  uncounterRestraintStatus != '' ">
				UNCOUNTER_RESTRAINT_STATUS = #{uncounterRestraintStatus},
			</if>
			<if test="updateUser != null and  updateUser != '' ">
				UPDATE_USER = #{updateUser},
			</if>
			<if test="updateDate != null ">
				UPDATE_DATE = #{updateDate},
			</if>
			<if test="acctSourceType != null and  acctSourceType != '' ">
				ACCT_SOURCE_TYPE = #{acctSourceType},
			</if>

			<if test="updateBranch != null and  updateBranch != '' ">
				UPDATE_BRANCH = #{updateBranch},
			</if>

			<if test="endEffectDate != null ">
				END_EFFECT_DATE = #{endEffectDate},
			</if>

			<if test="startEffectDate != null ">
				START_EFFECT_DATE = #{startEffectDate},
			</if>
			<if test="uncounterDesc != null and  uncounterDesc != '' ">
				UNCOUNTER_DESC = #{uncounterDesc},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				<if test="documentType != null and  documentType != '' ">
					DOCUMENT_TYPE = #{documentType} AND
				</if>
				<if test="docunmentId != null and  docunmentId != '' ">
					DOCUMENT_ID = #{docunmentId} AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo} AND
				</if>
				<if test="baseAcctNo != null and  baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo} AND
				</if>
				<if test="uncounterRestraintType != null and  uncounterRestraintType != '' ">
					UNCOUNTER_RESTRAINT_TYPE = #{uncounterRestraintType} AND
				</if>
				<if test="uncounterNo != null and  uncounterNo != '' ">
					UNCOUNTER_NO = #{uncounterNo} AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</update>

	<select id="selectByDocumentTypeAndDocumentType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		WHERE
		<trim prefix="(" suffix=")" suffixOverrides="AND">
			<if test="clientNo != null and  clientNo != '' ">
				CLIENT_NO = #{clientNo} AND
			</if>
			<if test="documentType != null and  documentType != '' ">
				DOCUMENT_TYPE = #{documentType} AND
			</if>
			<if test="docunmentId != null and  docunmentId != '' ">
				DOCUMENT_ID = #{docunmentId} AND
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				COMPANY = #{company} AND
			</if>
		</trim>
	</select>

	<select id="selectByBaseAcctNoCheck" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUncounterRestraints">
		select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		<where>
		<if test="baseAcctNo != null and  baseAcctNo != '' ">
			AND (BASE_ACCT_NO = #{baseAcctNo}
			AND((UNCOUNTER_RESTRAINT_STATUS = '3')
			or (uncounter_restraint_status = '1' and uncounter_restraint_type not in ('H002'))
			))
		</if>
		<if test="documentType != null and  documentType != '' and docunmentId != null and  docunmentId != ''and baseAcctNo != null and  baseAcctNo != ''">
			or (DOCUMENT_TYPE = #{documentType}  AND DOCUMENT_ID = #{docunmentId}
			and uncounter_restraint_type  in ('H002','H004') and UNCOUNTER_RESTRAINT_STATUS = '3')
		</if>
		<if test="documentType != null and  documentType != '' and docunmentId != null and  docunmentId != ''and (baseAcctNo == null or  baseAcctNo == '') ">
			AND (DOCUMENT_TYPE = #{documentType}  AND DOCUMENT_ID = #{docunmentId}
			and uncounter_restraint_type  in ('H002','H004') and UNCOUNTER_RESTRAINT_STATUS = '3')
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>
	<select id="selectBlackAcctNotice" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTelecomblackList">
		select
		<include refid="Base_Column2" />
		from
		(select
		<include refid="Base_Column" />
		from rb_uncounter_restraints
		<where>
		<if test="uncounterRestraintType != null">
			AND UNCOUNTER_RESTRAINT_TYPE IN
			<foreach item="item" index="index" collection="uncounterRestraintType" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="tranDate != null">
			AND UPDATE_DATE = #{tranDate}
			AND (UNCOUNTER_TIME != #{tranDate} or UNCOUNTER_TIME is null )
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
		) A
		ORDER BY A.UNCOUNTER_NO DESC
	</select>
</mapper>
