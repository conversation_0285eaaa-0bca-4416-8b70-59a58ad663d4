<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.bc.unit.cm.backupClean.CleanService">
    <delete id="delete" parameterType="java.util.Map">
        DELETE FROM ${cometTableName}
        where 1=1
        <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}
        </if>
    </delete>

    <delete id="truncate" parameterType="java.util.Map">
        ALTER TABLE ${cometTableName} TRUNCATE PARTITION ${partitionName}
    </delete>

    <delete id="rbGlHistClean" parameterType="java.util.Map">
        DELETE FROM ${cometTableName}
        <if test="partitionName != null">PARTITION (${partitionName})</if>
        WHERE TRAN_DATE &lt;= #{retainDate,jdbcType=DATE}
    </delete>
    <delete id="rbTranHistClean" parameterType="java.util.Map">
        DELETE FROM ${cometTableName}
        <if test="partitionName != null">PARTITION (${partitionName})</if>
        WHERE TRAN_DATE &lt;= #{retainDate,jdbcType=DATE}
    </delete>
</mapper>
