<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.ClGlHist">

    <sql id="Table_Name">
        ENS_CL.CL_GL_HIST
    </sql>

    <sql id="Base_Column">
        <trim suffixOverrides=",">
            GL_SEQ_NO,
            INTERNAL_KEY,
            REFERENCE,
            TRAN_BRANCH,
            EFFECT_DATE,
            ACCT_CCY,
            SOURCE_MODULE,
            SOURCE_TYPE,
            BUSINESS_UNIT,
            AMT_TYPE,
            AMOUNT,
            PROD_TYPE,
            BUSI_PROD,
            LOAN_NO,
            BRANCH,
            ACCOUNTING_STATUS,
            PROFIT_CENTER,
            CCY,
            CLIENT_TYPE,
            CLIENT_NO,
            SYSTEM_ID,
            REVERSAL,
            NARRATIVE,
            PRI_AMT,
            INT_AMT,
            ODP_AMT,
            ODI_AMT,
            TRAN_PROFIT_CENTER,
            EVENT_TYPE,
            TRAN_TYPE,
            TRAN_DATE,
            BANK_SEQ_NO,
            GL_CODE,
            CHANNEL_DATE,
            TAX_AMT,
            CR_DR_MAINT_IND,
            GL_POSTED_FLAG,
            CHANNEL_SEQ_NO,
            REVERSAL_SEQ_NO,
            REVERSAL_DATE,
            SPREAD_PERCENT,
            IN_STATUS,
            MARKETING_PROD,
            MARKETING_PROD_DESC,
            TRAN_CATEGORY,
            RESERVE1,
            RESERVE2,
            CHANNEL_SUB_SEQ_NO,
            UN_REAL,
            SETTLE_MODE,
            COMPANY,
            TRAN_TIMESTAMP,
            BUS_SEQ_NO,
            REACCOUNT_CD,
        </trim>
    </sql>

    <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.model.entity.dbmodel.ClGlHist">
        <result property="glSeqNo" column="GL_SEQ_NO"/>
        <result property="internalKey" column="INTERNAL_KEY"/>
        <result property="reference" column="REFERENCE"/>
        <result property="tranBranch" column="TRAN_BRANCH"/>
        <result property="effectDate" column="EFFECT_DATE"/>
        <result property="acctCcy" column="ACCT_CCY"/>
        <result property="sourceModule" column="SOURCE_MODULE"/>
        <result property="sourceType" column="SOURCE_TYPE"/>
        <result property="businessUnit" column="BUSINESS_UNIT"/>
        <result property="amtType" column="AMT_TYPE"/>
        <result property="amount" column="AMOUNT"/>
        <result property="prodType" column="PROD_TYPE"/>
        <result property="busiProd" column="BUSI_PROD"/>
        <result property="loanNo" column="LOAN_NO"/>
        <result property="branch" column="BRANCH"/>
        <result property="accountingStatus" column="ACCOUNTING_STATUS"/>
        <result property="profitCenter" column="PROFIT_CENTER"/>
        <result property="ccy" column="CCY"/>
        <result property="clientType" column="CLIENT_TYPE"/>
        <result property="clientNo" column="CLIENT_NO"/>
        <result property="systemId" column="SYSTEM_ID"/>
        <result property="reversal" column="REVERSAL"/>
        <result property="narrative" column="NARRATIVE"/>
        <result property="priAmt" column="PRI_AMT"/>
        <result property="intAmt" column="INT_AMT"/>
        <result property="odpAmt" column="ODP_AMT"/>
        <result property="odiAmt" column="ODI_AMT"/>
        <result property="tranProfitCenter" column="TRAN_PROFIT_CENTER"/>
        <result property="eventType" column="EVENT_TYPE"/>
        <result property="tranType" column="TRAN_TYPE"/>
        <result property="tranDate" column="TRAN_DATE"/>
        <result property="bankSeqNo" column="BANK_SEQ_NO"/>
        <result property="glCode" column="GL_CODE"/>
        <result property="channelDate" column="CHANNEL_DATE"/>
        <result property="taxAmt" column="TAX_AMT"/>
        <result property="crDrMaintInd" column="CR_DR_MAINT_IND"/>
        <result property="glPostedFlag" column="GL_POSTED_FLAG"/>
        <result property="channelSeqNo" column="CHANNEL_SEQ_NO"/>
        <result property="reversalSeqNo" column="REVERSAL_SEQ_NO"/>
        <result property="reversalDate" column="REVERSAL_DATE"/>
        <result property="spreadPercent" column="SPREAD_PERCENT"/>
        <result property="inStatus" column="IN_STATUS"/>
        <result property="marketingProd" column="MARKETING_PROD"/>
        <result property="marketingProdDesc" column="MARKETING_PROD_DESC"/>
        <result property="tranCategory" column="TRAN_CATEGORY"/>
        <result property="reserve1" column="RESERVE1"/>
        <result property="reserve2" column="RESERVE2"/>
        <result property="channelSubSeqNo" column="CHANNEL_SUB_SEQ_NO"/>
        <result property="unReal" column="UN_REAL"/>
        <result property="settleMode" column="SETTLE_MODE"/>
        <result property="company" column="COMPANY"/>
        <result property="tranTimestamp" column="TRAN_TIMESTAMP"/>
        <result property="busSeqNo" column="BUS_SEQ_NO"/>
        <result property="reaccountCd" column="REACCOUNT_CD"/>
    </resultMap>

    <sql id="Base_Where">
        <where>
            <if test="glSeqNo != null and  glSeqNo != '' ">
                AND GL_SEQ_NO = #{glSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
            </if>
            <if test="reference != null and  reference != '' ">
                AND REFERENCE = #{reference,jdbcType=VARCHAR}
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="effectDate != null ">
                AND EFFECT_DATE = #{effectDate,jdbcType=DATE}
            </if>
            <if test="acctCcy != null and  acctCcy != '' ">
                AND ACCT_CCY = #{acctCcy,jdbcType=VARCHAR}
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                AND SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                AND SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
            </if>
            <if test="businessUnit != null and  businessUnit != '' ">
                AND BUSINESS_UNIT = #{businessUnit,jdbcType=VARCHAR}
            </if>
            <if test="amtType != null and  amtType != '' ">
                AND AMT_TYPE = #{amtType,jdbcType=VARCHAR}
            </if>
            <if test="amount != null ">
                AND AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="busiProd != null and  busiProd != '' ">
                AND BUSI_PROD = #{busiProd,jdbcType=VARCHAR}
            </if>
            <if test="loanNo != null and  loanNo != '' ">
                AND LOAN_NO = #{loanNo,jdbcType=VARCHAR}
            </if>
            <if test="branch != null and  branch != '' ">
                AND BRANCH = #{branch,jdbcType=VARCHAR}
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                AND ACCOUNTING_STATUS = #{accountingStatus,jdbcType=VARCHAR}
            </if>
            <if test="profitCenter != null and  profitCenter != '' ">
                AND PROFIT_CENTER = #{profitCenter,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="clientType != null and  clientType != '' ">
                AND CLIENT_TYPE = #{clientType,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="systemId != null and  systemId != '' ">
                AND SYSTEM_ID = #{systemId,jdbcType=VARCHAR}
            </if>
            <if test="reversal != null and  reversal != '' ">
                AND REVERSAL = #{reversal,jdbcType=VARCHAR}
            </if>
            <if test="narrative != null and  narrative != '' ">
                AND NARRATIVE = #{narrative,jdbcType=VARCHAR}
            </if>
            <if test="priAmt != null ">
                AND PRI_AMT = #{priAmt,jdbcType=DECIMAL}
            </if>
            <if test="intAmt != null ">
                AND INT_AMT = #{intAmt,jdbcType=DECIMAL}
            </if>
            <if test="odpAmt != null ">
                AND ODP_AMT = #{odpAmt,jdbcType=DECIMAL}
            </if>
            <if test="odiAmt != null ">
                AND ODI_AMT = #{odiAmt,jdbcType=DECIMAL}
            </if>
            <if test="tranProfitCenter != null and  tranProfitCenter != '' ">
                AND TRAN_PROFIT_CENTER = #{tranProfitCenter,jdbcType=VARCHAR}
            </if>
            <if test="eventType != null and  eventType != '' ">
                AND EVENT_TYPE = #{eventType,jdbcType=VARCHAR}
            </if>
            <if test="tranType != null and  tranType != '' ">
                AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
            </if>
            <if test="tranDate != null ">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE}
            </if>
            <if test="bankSeqNo != null and  bankSeqNo != '' ">
                AND BANK_SEQ_NO = #{bankSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="glCode != null and  glCode != '' ">
                AND GL_CODE = #{glCode,jdbcType=VARCHAR}
            </if>
            <if test="channelDate != null ">
                AND CHANNEL_DATE = #{channelDate,jdbcType=DATE}
            </if>
            <if test="taxAmt != null ">
                AND TAX_AMT = #{taxAmt,jdbcType=DECIMAL}
            </if>
            <if test="crDrMaintInd != null and  crDrMaintInd != '' ">
                AND CR_DR_MAINT_IND = #{crDrMaintInd,jdbcType=VARCHAR}
            </if>
            <if test="glPostedFlag != null and  glPostedFlag != '' ">
                AND GL_POSTED_FLAG = #{glPostedFlag,jdbcType=VARCHAR}
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                AND CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="reversalSeqNo != null and  reversalSeqNo != '' ">
                AND REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="reversalDate != null ">
                AND REVERSAL_DATE = #{reversalDate,jdbcType=DATE}
            </if>
            <if test="spreadPercent != null ">
                AND SPREAD_PERCENT = #{spreadPercent,jdbcType=DECIMAL}
            </if>
            <if test="inStatus != null and  inStatus != '' ">
                AND IN_STATUS = #{inStatus,jdbcType=VARCHAR}
            </if>
            <if test="marketingProd != null and  marketingProd != '' ">
                AND MARKETING_PROD = #{marketingProd,jdbcType=VARCHAR}
            </if>
            <if test="marketingProdDesc != null and  marketingProdDesc != '' ">
                AND MARKETING_PROD_DESC = #{marketingProdDesc,jdbcType=VARCHAR}
            </if>
            <if test="tranCategory != null and  tranCategory != '' ">
                AND TRAN_CATEGORY = #{tranCategory,jdbcType=VARCHAR}
            </if>
            <if test="reserve1 != null and  reserve1 != '' ">
                AND RESERVE1 = #{reserve1,jdbcType=VARCHAR}
            </if>
            <if test="reserve2 != null and  reserve2 != '' ">
                AND RESERVE2 = #{reserve2,jdbcType=VARCHAR}
            </if>
            <if test="channelSubSeqNo != null and  channelSubSeqNo != '' ">
                AND CHANNEL_SUB_SEQ_NO = #{channelSubSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="unReal != null and  unReal != '' ">
                AND UN_REAL = #{unReal,jdbcType=VARCHAR}
            </if>
            <if test="settleMode != null and  settleMode != '' ">
                AND SETTLE_MODE = #{settleMode,jdbcType=VARCHAR}
            </if>
            <if test="company != null and  company != '' ">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
            <if test="tranTimestamp != null and  tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="busSeqNo != null and  busSeqNo != '' ">
                AND BUS_SEQ_NO = #{busSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="reaccountCd != null and  reaccountCd != '' ">
                AND REACCOUNT_CD = #{reaccountCd,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="PrimaryKey_Where">
        <where>
            <if test="glSeqNo != null and  glSeqNo != '' ">
                AND GL_SEQ_NO = #{glSeqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="Base_Set">
        <set>
            <if test="glSeqNo != null and glSeqNo != '' ">
                GL_SEQ_NO = #{glSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="internalKey != null ">
                INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},
            </if>
            <if test="reference != null and reference != '' ">
                REFERENCE = #{reference,jdbcType=VARCHAR},
            </if>
            <if test="tranBranch != null and tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="effectDate != null ">
                EFFECT_DATE = #{effectDate,jdbcType=DATE},
            </if>
            <if test="acctCcy != null and acctCcy != '' ">
                ACCT_CCY = #{acctCcy,jdbcType=VARCHAR},
            </if>
            <if test="sourceModule != null and sourceModule != '' ">
                SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null and sourceType != '' ">
                SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="businessUnit != null and businessUnit != '' ">
                BUSINESS_UNIT = #{businessUnit,jdbcType=VARCHAR},
            </if>
            <if test="amtType != null and amtType != '' ">
                AMT_TYPE = #{amtType,jdbcType=VARCHAR},
            </if>
            <if test="amount != null ">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="prodType != null and prodType != '' ">
                PROD_TYPE = #{prodType,jdbcType=VARCHAR},
            </if>
            <if test="busiProd != null and busiProd != '' ">
                BUSI_PROD = #{busiProd,jdbcType=VARCHAR},
            </if>
            <if test="loanNo != null and loanNo != '' ">
                LOAN_NO = #{loanNo,jdbcType=VARCHAR},
            </if>
            <if test="branch != null and branch != '' ">
                BRANCH = #{branch,jdbcType=VARCHAR},
            </if>
            <if test="accountingStatus != null and accountingStatus != '' ">
                ACCOUNTING_STATUS = #{accountingStatus,jdbcType=VARCHAR},
            </if>
            <if test="profitCenter != null and profitCenter != '' ">
                PROFIT_CENTER = #{profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null and ccy != '' ">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="clientType != null and clientType != '' ">
                CLIENT_TYPE = #{clientType,jdbcType=VARCHAR},
            </if>
            <if test="systemId != null and systemId != '' ">
                SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="reversal != null and reversal != '' ">
                REVERSAL = #{reversal,jdbcType=VARCHAR},
            </if>
            <if test="narrative != null and narrative != '' ">
                NARRATIVE = #{narrative,jdbcType=VARCHAR},
            </if>
            <if test="priAmt != null ">
                PRI_AMT = #{priAmt,jdbcType=DECIMAL},
            </if>
            <if test="intAmt != null ">
                INT_AMT = #{intAmt,jdbcType=DECIMAL},
            </if>
            <if test="odpAmt != null ">
                ODP_AMT = #{odpAmt,jdbcType=DECIMAL},
            </if>
            <if test="odiAmt != null ">
                ODI_AMT = #{odiAmt,jdbcType=DECIMAL},
            </if>
            <if test="tranProfitCenter != null and tranProfitCenter != '' ">
                TRAN_PROFIT_CENTER = #{tranProfitCenter,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null and eventType != '' ">
                EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="tranType != null and tranType != '' ">
                TRAN_TYPE = #{tranType,jdbcType=VARCHAR},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="bankSeqNo != null and bankSeqNo != '' ">
                BANK_SEQ_NO = #{bankSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="glCode != null and glCode != '' ">
                GL_CODE = #{glCode,jdbcType=VARCHAR},
            </if>
            <if test="channelDate != null ">
                CHANNEL_DATE = #{channelDate,jdbcType=DATE},
            </if>
            <if test="taxAmt != null ">
                TAX_AMT = #{taxAmt,jdbcType=DECIMAL},
            </if>
            <if test="crDrMaintInd != null and crDrMaintInd != '' ">
                CR_DR_MAINT_IND = #{crDrMaintInd,jdbcType=VARCHAR},
            </if>
            <if test="glPostedFlag != null and glPostedFlag != '' ">
                GL_POSTED_FLAG = #{glPostedFlag,jdbcType=VARCHAR},
            </if>
            <if test="channelSeqNo != null and channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="reversalSeqNo != null and reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="reversalDate != null ">
                REVERSAL_DATE = #{reversalDate,jdbcType=DATE},
            </if>
            <if test="spreadPercent != null ">
                SPREAD_PERCENT = #{spreadPercent,jdbcType=DECIMAL},
            </if>
            <if test="inStatus != null and inStatus != '' ">
                IN_STATUS = #{inStatus,jdbcType=VARCHAR},
            </if>
            <if test="marketingProd != null and marketingProd != '' ">
                MARKETING_PROD = #{marketingProd,jdbcType=VARCHAR},
            </if>
            <if test="marketingProdDesc != null and marketingProdDesc != '' ">
                MARKETING_PROD_DESC = #{marketingProdDesc,jdbcType=VARCHAR},
            </if>
            <if test="tranCategory != null and tranCategory != '' ">
                TRAN_CATEGORY = #{tranCategory,jdbcType=VARCHAR},
            </if>
            <if test="reserve1 != null and reserve1 != '' ">
                RESERVE1 = #{reserve1,jdbcType=VARCHAR},
            </if>
            <if test="reserve2 != null and reserve2 != '' ">
                RESERVE2 = #{reserve2,jdbcType=VARCHAR},
            </if>
            <if test="channelSubSeqNo != null and channelSubSeqNo != '' ">
                CHANNEL_SUB_SEQ_NO = #{channelSubSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="unReal != null and unReal != '' ">
                UN_REAL = #{unReal,jdbcType=VARCHAR},
            </if>
            <if test="settleMode != null and settleMode != '' ">
                SETTLE_MODE = #{settleMode,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="busSeqNo != null and busSeqNo != '' ">
                BUS_SEQ_NO = #{busSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="reaccountCd != null and reaccountCd != '' ">
                REACCOUNT_CD = #{reaccountCd,jdbcType=VARCHAR},
            </if>
        </set>
    </sql>

    <sql id="Base_Select">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </sql>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'">${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'">*</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null ">
            and ${cometKeyField} between #{cometStart} and #{cometEnd}
        </if>
    </sql>

    <sql id="comet_row_num_step_column">
        <if test="cometSqlType == 'total'">count(*) AS TOTAL</if>
        <if test="cometSqlType == 'offset'">*</if>
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="glSeqNo != null ">GL_SEQ_NO,</if>
            <if test="internalKey != null ">INTERNAL_KEY,</if>
            <if test="reference != null ">REFERENCE,</if>
            <if test="tranBranch != null ">TRAN_BRANCH,</if>
            <if test="effectDate != null ">EFFECT_DATE,</if>
            <if test="acctCcy != null ">ACCT_CCY,</if>
            <if test="sourceModule != null ">SOURCE_MODULE,</if>
            <if test="sourceType != null ">SOURCE_TYPE,</if>
            <if test="businessUnit != null ">BUSINESS_UNIT,</if>
            <if test="amtType != null ">AMT_TYPE,</if>
            <if test="amount != null ">AMOUNT,</if>
            <if test="prodType != null ">PROD_TYPE,</if>
            <if test="busiProd != null ">BUSI_PROD,</if>
            <if test="loanNo != null ">LOAN_NO,</if>
            <if test="branch != null ">BRANCH,</if>
            <if test="accountingStatus != null ">ACCOUNTING_STATUS,</if>
            <if test="profitCenter != null ">PROFIT_CENTER,</if>
            <if test="ccy != null ">CCY,</if>
            <if test="clientType != null ">CLIENT_TYPE,</if>
            <if test="clientNo != null ">CLIENT_NO,</if>
            <if test="systemId != null ">SYSTEM_ID,</if>
            <if test="reversal != null ">REVERSAL,</if>
            <if test="narrative != null ">NARRATIVE,</if>
            <if test="priAmt != null ">PRI_AMT,</if>
            <if test="intAmt != null ">INT_AMT,</if>
            <if test="odpAmt != null ">ODP_AMT,</if>
            <if test="odiAmt != null ">ODI_AMT,</if>
            <if test="tranProfitCenter != null ">TRAN_PROFIT_CENTER,</if>
            <if test="eventType != null ">EVENT_TYPE,</if>
            <if test="tranType != null ">TRAN_TYPE,</if>
            <if test="tranDate != null ">TRAN_DATE,</if>
            <if test="bankSeqNo != null ">BANK_SEQ_NO,</if>
            <if test="glCode != null ">GL_CODE,</if>
            <if test="channelDate != null ">CHANNEL_DATE,</if>
            <if test="taxAmt != null ">TAX_AMT,</if>
            <if test="crDrMaintInd != null ">CR_DR_MAINT_IND,</if>
            <if test="glPostedFlag != null ">GL_POSTED_FLAG,</if>
            <if test="channelSeqNo != null ">CHANNEL_SEQ_NO,</if>
            <if test="reversalSeqNo != null ">REVERSAL_SEQ_NO,</if>
            <if test="reversalDate != null ">REVERSAL_DATE,</if>
            <if test="spreadPercent != null ">SPREAD_PERCENT,</if>
            <if test="inStatus != null ">IN_STATUS,</if>
            <if test="marketingProd != null ">MARKETING_PROD,</if>
            <if test="marketingProdDesc != null ">MARKETING_PROD_DESC,</if>
            <if test="tranCategory != null ">TRAN_CATEGORY,</if>
            <if test="reserve1 != null ">RESERVE1,</if>
            <if test="reserve2 != null ">RESERVE2,</if>
            <if test="channelSubSeqNo != null ">CHANNEL_SUB_SEQ_NO,</if>
            <if test="unReal != null ">UN_REAL,</if>
            <if test="settleMode != null ">SETTLE_MODE,</if>
            <if test="company != null ">COMPANY,</if>
            <if test="tranTimestamp != null ">TRAN_TIMESTAMP,</if>
            <if test="busSeqNo != null ">BUS_SEQ_NO,</if>
            <if test="reaccountCd != null ">REACCOUNT_CD,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="glSeqNo != null ">#{glSeqNo,jdbcType=VARCHAR},</if>
            <if test="internalKey != null ">#{internalKey,jdbcType=BIGINT},</if>
            <if test="reference != null ">#{reference,jdbcType=VARCHAR},</if>
            <if test="tranBranch != null ">#{tranBranch,jdbcType=VARCHAR},</if>
            <if test="effectDate != null ">#{effectDate,jdbcType=DATE},</if>
            <if test="acctCcy != null ">#{acctCcy,jdbcType=VARCHAR},</if>
            <if test="sourceModule != null ">#{sourceModule,jdbcType=VARCHAR},</if>
            <if test="sourceType != null ">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="businessUnit != null ">#{businessUnit,jdbcType=VARCHAR},</if>
            <if test="amtType != null ">#{amtType,jdbcType=VARCHAR},</if>
            <if test="amount != null ">#{amount,jdbcType=DECIMAL},</if>
            <if test="prodType != null ">#{prodType,jdbcType=VARCHAR},</if>
            <if test="busiProd != null ">#{busiProd,jdbcType=VARCHAR},</if>
            <if test="loanNo != null ">#{loanNo,jdbcType=VARCHAR},</if>
            <if test="branch != null ">#{branch,jdbcType=VARCHAR},</if>
            <if test="accountingStatus != null ">#{accountingStatus,jdbcType=VARCHAR},</if>
            <if test="profitCenter != null ">#{profitCenter,jdbcType=VARCHAR},</if>
            <if test="ccy != null ">#{ccy,jdbcType=VARCHAR},</if>
            <if test="clientType != null ">#{clientType,jdbcType=VARCHAR},</if>
            <if test="clientNo != null ">#{clientNo,jdbcType=VARCHAR},</if>
            <if test="systemId != null ">#{systemId,jdbcType=VARCHAR},</if>
            <if test="reversal != null ">#{reversal,jdbcType=VARCHAR},</if>
            <if test="narrative != null ">#{narrative,jdbcType=VARCHAR},</if>
            <if test="priAmt != null ">#{priAmt,jdbcType=DECIMAL},</if>
            <if test="intAmt != null ">#{intAmt,jdbcType=DECIMAL},</if>
            <if test="odpAmt != null ">#{odpAmt,jdbcType=DECIMAL},</if>
            <if test="odiAmt != null ">#{odiAmt,jdbcType=DECIMAL},</if>
            <if test="tranProfitCenter != null ">#{tranProfitCenter,jdbcType=VARCHAR},</if>
            <if test="eventType != null ">#{eventType,jdbcType=VARCHAR},</if>
            <if test="tranType != null ">#{tranType,jdbcType=VARCHAR},</if>
            <if test="tranDate != null ">#{tranDate,jdbcType=DATE},</if>
            <if test="bankSeqNo != null ">#{bankSeqNo,jdbcType=VARCHAR},</if>
            <if test="glCode != null ">#{glCode,jdbcType=VARCHAR},</if>
            <if test="channelDate != null ">#{channelDate,jdbcType=DATE},</if>
            <if test="taxAmt != null ">#{taxAmt,jdbcType=DECIMAL},</if>
            <if test="crDrMaintInd != null ">#{crDrMaintInd,jdbcType=VARCHAR},</if>
            <if test="glPostedFlag != null ">#{glPostedFlag,jdbcType=VARCHAR},</if>
            <if test="channelSeqNo != null ">#{channelSeqNo,jdbcType=VARCHAR},</if>
            <if test="reversalSeqNo != null ">#{reversalSeqNo,jdbcType=VARCHAR},</if>
            <if test="reversalDate != null ">#{reversalDate,jdbcType=DATE},</if>
            <if test="spreadPercent != null ">#{spreadPercent,jdbcType=DECIMAL},</if>
            <if test="inStatus != null ">#{inStatus,jdbcType=VARCHAR},</if>
            <if test="marketingProd != null ">#{marketingProd,jdbcType=VARCHAR},</if>
            <if test="marketingProdDesc != null ">#{marketingProdDesc,jdbcType=VARCHAR},</if>
            <if test="tranCategory != null ">#{tranCategory,jdbcType=VARCHAR},</if>
            <if test="reserve1 != null ">#{reserve1,jdbcType=VARCHAR},</if>
            <if test="reserve2 != null ">#{reserve2,jdbcType=VARCHAR},</if>
            <if test="channelSubSeqNo != null ">#{channelSubSeqNo,jdbcType=VARCHAR},</if>
            <if test="unReal != null ">#{unReal,jdbcType=VARCHAR},</if>
            <if test="settleMode != null ">#{settleMode,jdbcType=VARCHAR},</if>
            <if test="company != null ">#{company,jdbcType=VARCHAR},</if>
            <if test="tranTimestamp != null ">#{tranTimestamp,jdbcType=VARCHAR},</if>
            <if test="busSeqNo != null ">#{busSeqNo,jdbcType=VARCHAR},</if>
            <if test="reaccountCd != null ">#{reaccountCd,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>

    <update id="updateByPrimaryKey">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>


    <update id="updateByEntity">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="s.glSeqNo != null and s.glSeqNo != '' ">
                GL_SEQ_NO = #{s.glSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.internalKey != null ">
                INTERNAL_KEY = #{s.internalKey,jdbcType=BIGINT},
            </if>
            <if test="s.reference != null and s.reference != '' ">
                REFERENCE = #{s.reference,jdbcType=VARCHAR},
            </if>
            <if test="s.tranBranch != null and s.tranBranch != '' ">
                TRAN_BRANCH = #{s.tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.effectDate != null ">
                EFFECT_DATE = #{s.effectDate,jdbcType=DATE},
            </if>
            <if test="s.acctCcy != null and s.acctCcy != '' ">
                ACCT_CCY = #{s.acctCcy,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceModule != null and s.sourceModule != '' ">
                SOURCE_MODULE = #{s.sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceType != null and s.sourceType != '' ">
                SOURCE_TYPE = #{s.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="s.businessUnit != null and s.businessUnit != '' ">
                BUSINESS_UNIT = #{s.businessUnit,jdbcType=VARCHAR},
            </if>
            <if test="s.amtType != null and s.amtType != '' ">
                AMT_TYPE = #{s.amtType,jdbcType=VARCHAR},
            </if>
            <if test="s.amount != null ">
                AMOUNT = #{s.amount,jdbcType=DECIMAL},
            </if>
            <if test="s.prodType != null and s.prodType != '' ">
                PROD_TYPE = #{s.prodType,jdbcType=VARCHAR},
            </if>
            <if test="s.busiProd != null and s.busiProd != '' ">
                BUSI_PROD = #{s.busiProd,jdbcType=VARCHAR},
            </if>
            <if test="s.loanNo != null and s.loanNo != '' ">
                LOAN_NO = #{s.loanNo,jdbcType=VARCHAR},
            </if>
            <if test="s.branch != null and s.branch != '' ">
                BRANCH = #{s.branch,jdbcType=VARCHAR},
            </if>
            <if test="s.accountingStatus != null and s.accountingStatus != '' ">
                ACCOUNTING_STATUS = #{s.accountingStatus,jdbcType=VARCHAR},
            </if>
            <if test="s.profitCenter != null and s.profitCenter != '' ">
                PROFIT_CENTER = #{s.profitCenter,jdbcType=VARCHAR},
            </if>
            <if test="s.ccy != null and s.ccy != '' ">
                CCY = #{s.ccy,jdbcType=VARCHAR},
            </if>
            <if test="s.clientType != null and s.clientType != '' ">
                CLIENT_TYPE = #{s.clientType,jdbcType=VARCHAR},
            </if>
            <if test="s.systemId != null and s.systemId != '' ">
                SYSTEM_ID = #{s.systemId,jdbcType=VARCHAR},
            </if>
            <if test="s.reversal != null and s.reversal != '' ">
                REVERSAL = #{s.reversal,jdbcType=VARCHAR},
            </if>
            <if test="s.narrative != null and s.narrative != '' ">
                NARRATIVE = #{s.narrative,jdbcType=VARCHAR},
            </if>
            <if test="s.priAmt != null ">
                PRI_AMT = #{s.priAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.intAmt != null ">
                INT_AMT = #{s.intAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.odpAmt != null ">
                ODP_AMT = #{s.odpAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.odiAmt != null ">
                ODI_AMT = #{s.odiAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.tranProfitCenter != null and s.tranProfitCenter != '' ">
                TRAN_PROFIT_CENTER = #{s.tranProfitCenter,jdbcType=VARCHAR},
            </if>
            <if test="s.eventType != null and s.eventType != '' ">
                EVENT_TYPE = #{s.eventType,jdbcType=VARCHAR},
            </if>
            <if test="s.tranType != null and s.tranType != '' ">
                TRAN_TYPE = #{s.tranType,jdbcType=VARCHAR},
            </if>
            <if test="s.tranDate != null ">
                TRAN_DATE = #{s.tranDate,jdbcType=DATE},
            </if>
            <if test="s.bankSeqNo != null and s.bankSeqNo != '' ">
                BANK_SEQ_NO = #{s.bankSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.glCode != null and s.glCode != '' ">
                GL_CODE = #{s.glCode,jdbcType=VARCHAR},
            </if>
            <if test="s.channelDate != null ">
                CHANNEL_DATE = #{s.channelDate,jdbcType=DATE},
            </if>
            <if test="s.taxAmt != null ">
                TAX_AMT = #{s.taxAmt,jdbcType=DECIMAL},
            </if>
            <if test="s.crDrMaintInd != null and s.crDrMaintInd != '' ">
                CR_DR_MAINT_IND = #{s.crDrMaintInd,jdbcType=VARCHAR},
            </if>
            <if test="s.glPostedFlag != null and s.glPostedFlag != '' ">
                GL_POSTED_FLAG = #{s.glPostedFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.channelSeqNo != null and s.channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{s.channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.reversalSeqNo != null and s.reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{s.reversalSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.reversalDate != null ">
                REVERSAL_DATE = #{s.reversalDate,jdbcType=DATE},
            </if>
            <if test="s.spreadPercent != null ">
                SPREAD_PERCENT = #{s.spreadPercent,jdbcType=DECIMAL},
            </if>
            <if test="s.inStatus != null and s.inStatus != '' ">
                IN_STATUS = #{s.inStatus,jdbcType=VARCHAR},
            </if>
            <if test="s.marketingProd != null and s.marketingProd != '' ">
                MARKETING_PROD = #{s.marketingProd,jdbcType=VARCHAR},
            </if>
            <if test="s.marketingProdDesc != null and s.marketingProdDesc != '' ">
                MARKETING_PROD_DESC = #{s.marketingProdDesc,jdbcType=VARCHAR},
            </if>
            <if test="s.tranCategory != null and s.tranCategory != '' ">
                TRAN_CATEGORY = #{s.tranCategory,jdbcType=VARCHAR},
            </if>
            <if test="s.reserve1 != null and s.reserve1 != '' ">
                RESERVE1 = #{s.reserve1,jdbcType=VARCHAR},
            </if>
            <if test="s.reserve2 != null and s.reserve2 != '' ">
                RESERVE2 = #{s.reserve2,jdbcType=VARCHAR},
            </if>
            <if test="s.channelSubSeqNo != null and s.channelSubSeqNo != '' ">
                CHANNEL_SUB_SEQ_NO = #{s.channelSubSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.unReal != null and s.unReal != '' ">
                UN_REAL = #{s.unReal,jdbcType=VARCHAR},
            </if>
            <if test="s.settleMode != null and s.settleMode != '' ">
                SETTLE_MODE = #{s.settleMode,jdbcType=VARCHAR},
            </if>
            <if test="s.company != null and s.company != '' ">
                COMPANY = #{s.company,jdbcType=VARCHAR},
            </if>
            <if test="s.tranTimestamp != null and s.tranTimestamp != '' ">
                TRAN_TIMESTAMP = #{s.tranTimestamp,jdbcType=VARCHAR},
            </if>
            <if test="s.busSeqNo != null and s.busSeqNo != '' ">
                BUS_SEQ_NO = #{s.busSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.reaccountCd != null and s.reaccountCd != '' ">
                REACCOUNT_CD = #{s.reaccountCd,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="w.glSeqNo != null and w.glSeqNo != '' ">
                AND GL_SEQ_NO = #{w.glSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.internalKey != null ">
                AND INTERNAL_KEY = #{w.internalKey,jdbcType=BIGINT}
            </if>
            <if test="w.reference != null and w.reference != '' ">
                AND REFERENCE = #{w.reference,jdbcType=VARCHAR}
            </if>
            <if test="w.tranBranch != null and w.tranBranch != '' ">
                AND TRAN_BRANCH = #{w.tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.effectDate != null ">
                AND EFFECT_DATE = #{w.effectDate,jdbcType=DATE}
            </if>
            <if test="w.acctCcy != null and w.acctCcy != '' ">
                AND ACCT_CCY = #{w.acctCcy,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceModule != null and w.sourceModule != '' ">
                AND SOURCE_MODULE = #{w.sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceType != null and w.sourceType != '' ">
                AND SOURCE_TYPE = #{w.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="w.businessUnit != null and w.businessUnit != '' ">
                AND BUSINESS_UNIT = #{w.businessUnit,jdbcType=VARCHAR}
            </if>
            <if test="w.amtType != null and w.amtType != '' ">
                AND AMT_TYPE = #{w.amtType,jdbcType=VARCHAR}
            </if>
            <if test="w.amount != null ">
                AND AMOUNT = #{w.amount,jdbcType=DECIMAL}
            </if>
            <if test="w.prodType != null and w.prodType != '' ">
                AND PROD_TYPE = #{w.prodType,jdbcType=VARCHAR}
            </if>
            <if test="w.busiProd != null and w.busiProd != '' ">
                AND BUSI_PROD = #{w.busiProd,jdbcType=VARCHAR}
            </if>
            <if test="w.loanNo != null and w.loanNo != '' ">
                AND LOAN_NO = #{w.loanNo,jdbcType=VARCHAR}
            </if>
            <if test="w.branch != null and w.branch != '' ">
                AND BRANCH = #{w.branch,jdbcType=VARCHAR}
            </if>
            <if test="w.accountingStatus != null and w.accountingStatus != '' ">
                AND ACCOUNTING_STATUS = #{w.accountingStatus,jdbcType=VARCHAR}
            </if>
            <if test="w.profitCenter != null and w.profitCenter != '' ">
                AND PROFIT_CENTER = #{w.profitCenter,jdbcType=VARCHAR}
            </if>
            <if test="w.ccy != null and w.ccy != '' ">
                AND CCY = #{w.ccy,jdbcType=VARCHAR}
            </if>
            <if test="w.clientType != null and w.clientType != '' ">
                AND CLIENT_TYPE = #{w.clientType,jdbcType=VARCHAR}
            </if>
            <if test="w.clientNo != null and w.clientNo != '' ">
                AND CLIENT_NO = #{w.clientNo,jdbcType=VARCHAR}
            </if>
            <if test="w.systemId != null and w.systemId != '' ">
                AND SYSTEM_ID = #{w.systemId,jdbcType=VARCHAR}
            </if>
            <if test="w.reversal != null and w.reversal != '' ">
                AND REVERSAL = #{w.reversal,jdbcType=VARCHAR}
            </if>
            <if test="w.narrative != null and w.narrative != '' ">
                AND NARRATIVE = #{w.narrative,jdbcType=VARCHAR}
            </if>
            <if test="w.priAmt != null ">
                AND PRI_AMT = #{w.priAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.intAmt != null ">
                AND INT_AMT = #{w.intAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.odpAmt != null ">
                AND ODP_AMT = #{w.odpAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.odiAmt != null ">
                AND ODI_AMT = #{w.odiAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.tranProfitCenter != null and w.tranProfitCenter != '' ">
                AND TRAN_PROFIT_CENTER = #{w.tranProfitCenter,jdbcType=VARCHAR}
            </if>
            <if test="w.eventType != null and w.eventType != '' ">
                AND EVENT_TYPE = #{w.eventType,jdbcType=VARCHAR}
            </if>
            <if test="w.tranType != null and w.tranType != '' ">
                AND TRAN_TYPE = #{w.tranType,jdbcType=VARCHAR}
            </if>
            <if test="w.tranDate != null ">
                AND TRAN_DATE = #{w.tranDate,jdbcType=DATE}
            </if>
            <if test="w.bankSeqNo != null and w.bankSeqNo != '' ">
                AND BANK_SEQ_NO = #{w.bankSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.glCode != null and w.glCode != '' ">
                AND GL_CODE = #{w.glCode,jdbcType=VARCHAR}
            </if>
            <if test="w.channelDate != null ">
                AND CHANNEL_DATE = #{w.channelDate,jdbcType=DATE}
            </if>
            <if test="w.taxAmt != null ">
                AND TAX_AMT = #{w.taxAmt,jdbcType=DECIMAL}
            </if>
            <if test="w.crDrMaintInd != null and w.crDrMaintInd != '' ">
                AND CR_DR_MAINT_IND = #{w.crDrMaintInd,jdbcType=VARCHAR}
            </if>
            <if test="w.glPostedFlag != null and w.glPostedFlag != '' ">
                AND GL_POSTED_FLAG = #{w.glPostedFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.channelSeqNo != null and w.channelSeqNo != '' ">
                AND CHANNEL_SEQ_NO = #{w.channelSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.reversalSeqNo != null and w.reversalSeqNo != '' ">
                AND REVERSAL_SEQ_NO = #{w.reversalSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.reversalDate != null ">
                AND REVERSAL_DATE = #{w.reversalDate,jdbcType=DATE}
            </if>
            <if test="w.spreadPercent != null ">
                AND SPREAD_PERCENT = #{w.spreadPercent,jdbcType=DECIMAL}
            </if>
            <if test="w.inStatus != null and w.inStatus != '' ">
                AND IN_STATUS = #{w.inStatus,jdbcType=VARCHAR}
            </if>
            <if test="w.marketingProd != null and w.marketingProd != '' ">
                AND MARKETING_PROD = #{w.marketingProd,jdbcType=VARCHAR}
            </if>
            <if test="w.marketingProdDesc != null and w.marketingProdDesc != '' ">
                AND MARKETING_PROD_DESC = #{w.marketingProdDesc,jdbcType=VARCHAR}
            </if>
            <if test="w.tranCategory != null and w.tranCategory != '' ">
                AND TRAN_CATEGORY = #{w.tranCategory,jdbcType=VARCHAR}
            </if>
            <if test="w.reserve1 != null and w.reserve1 != '' ">
                AND RESERVE1 = #{w.reserve1,jdbcType=VARCHAR}
            </if>
            <if test="w.reserve2 != null and w.reserve2 != '' ">
                AND RESERVE2 = #{w.reserve2,jdbcType=VARCHAR}
            </if>
            <if test="w.channelSubSeqNo != null and w.channelSubSeqNo != '' ">
                AND CHANNEL_SUB_SEQ_NO = #{w.channelSubSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.unReal != null and w.unReal != '' ">
                AND UN_REAL = #{w.unReal,jdbcType=VARCHAR}
            </if>
            <if test="w.settleMode != null and w.settleMode != '' ">
                AND SETTLE_MODE = #{w.settleMode,jdbcType=VARCHAR}
            </if>
            <if test="w.company != null and w.company != '' ">
                AND COMPANY = #{w.company,jdbcType=VARCHAR}
            </if>
            <if test="w.tranTimestamp != null and w.tranTimestamp != '' ">
                AND TRAN_TIMESTAMP = #{w.tranTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="w.busSeqNo != null and w.busSeqNo != '' ">
                AND BUS_SEQ_NO = #{w.busSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.reaccountCd != null and w.reaccountCd != '' ">
                AND REACCOUNT_CD = #{w.reaccountCd,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <delete id="delete">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </delete>

    <select id="count" parameterType="java.util.Map" resultType="int">
        SELECT count(1) FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </select>

    <select id="selectOne" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectList" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
        <include refid="Base_Select"/>
        FOR UPDATE
    </select>

    <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </delete>

    <!--更新cl_gl_hist过账标记-->
    <update id="updateClGlHistBySeqNo" parameterType="java.util.Map">
        update
        <include refid="Table_Name"/>
        SET GL_POSTED_FLAG='Y'
        WHERE
        GL_SEQ_NO IN
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
