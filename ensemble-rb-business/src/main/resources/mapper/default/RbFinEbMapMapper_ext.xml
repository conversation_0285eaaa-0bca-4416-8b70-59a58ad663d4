<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">

    <select id="selectByOptionFrontInfoN" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        <include refid="Base_Column"/>
        from RB_FIN_EB_MAP
        <where>
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <if test="entityFlag != null and  entityFlag != '' ">
                    ENTITY_FLAG = #{entityFlag} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
    <select id="selectByOptionFrontInfoY" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        <include refid="Base_Column"/>
        from RB_FIN_EB_MAP
        <where>
            OPERATE_NATURE_DESC not like '%无实物%' and
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
    <select id="selectEntityFlagByOptionFrontInfoY" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        <include refid="Base_Column"/>
        from RB_FIN_EB_MAP
        <where>
            OPERATE_NATURE_DESC like '表外%' and
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <if test="tranType != null and  tranType != ''">
                    TRAN_TYPE = #{tranType} AND
                </if>
                <if test="billCode != null and  billCode != ''">
                    BILL_CODE = #{billCode}  AND
                </if>
                <if test="billMediumType != null and  billMediumType != ''">
                    BILL_MEDIUM_TYPE = #{billMediumType} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
    <select id="selectByOptionDistinctInfoN" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        BILL_CODE,BILL_MEDIUM_TYPE,PROD_TYPE
        from RB_FIN_EB_MAP
        <where>
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <if test="entityFlag != null and  entityFlag != '' ">
                    ENTITY_FLAG = #{entityFlag} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
        group by BILL_CODE,BILL_MEDIUM_TYPE,PROD_TYPE
    </select>
    <select id="selectByOptionDistinctInfoY" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        BILL_CODE,BILL_MEDIUM_TYPE,PROD_TYPE
        from RB_FIN_EB_MAP
        <where>
            OPERATE_NATURE_DESC not like '%无实物%' and
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
        group by BILL_CODE,BILL_MEDIUM_TYPE,PROD_TYPE
    </select>
    <select id="selectByOptionInfoN" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        <include refid="Base_Column"/>
        from RB_FIN_EB_MAP
        <where>
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <if test="entityFlag != null and  entityFlag != '' ">
                    ENTITY_FLAG = #{entityFlag} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
    <select id="selectByOptionInfoY" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        <include refid="Base_Column"/>
        from RB_FIN_EB_MAP
        <where>
            OPERATE_NATURE_DESC not like '%无实物%' and
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
    <select id="selectByAllInfoN" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        <include refid="Base_Column"/>
        from RB_FIN_EB_MAP
        <where>
            <trim suffixOverrides="AND">
                <if test="operateFlag != null and  operateFlag != '' ">
                    OPERATE_FLAG = #{operateFlag} AND
                </if>
                <if test="billMediumType != null and  billMediumType != '' ">
                    BILL_MEDIUM_TYPE = #{billMediumType} AND
                </if>
                <if test="billCode != null and  billCode != '' ">
                    BILL_CODE = #{billCode} AND
                </if>
                <if test="entityFlag != null and  entityFlag != ''  ">
                    ENTITY_FLAG = #{entityFlag} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
    <select id="selectByAllInfoY" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap">
        select
        <include refid="Base_Column"/>
        from RB_FIN_EB_MAP
        <where>
            OPERATE_NATURE_DESC not like '%无实物%' and
            <trim suffixOverrides="AND">
                <if test="OperateFlag != null and  OperateFlag != '' ">
                    OPERATE_FLAG = #{OperateFlag} AND
                </if>
                <if test="billMediumType != null and  billMediumType != '' ">
                    BILL_MEDIUM_TYPE = #{billMediumType} AND
                </if>
                <if test="billCode != null and  billCode != '' ">
                    BILL_CODE = #{billCode} AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </select>
</mapper>
