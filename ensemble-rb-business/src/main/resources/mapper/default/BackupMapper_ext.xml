<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.bc.unit.cm.backupClean.BackupService">
    <update id="insertSelect" parameterType="java.util.Map">
        INSERT INTO ${backupTableName}
        SELECT * FROM ${cometTableName}
        <if test="partitionName != null">PARTITION (${partitionName})</if>
        where 1=1
        <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}
        </if>
    </update>
    <update id="rbGlHistBackup" parameterType="java.util.Map">
        INSERT INTO ${backupTableName}
        (CHANNEL_SEQ_NO, GL_SEQ_NO, INTERNAL_KEY, BASE_ACCT_NO, ACCT_SEQ_NO, ACCT_CCY,
        PROD_TYPE, CLIENT_NO, CLIENT_TYPE, CHANNEL_DATE, EFFECT_DATE, TRAN_CATEGORY,
        TRAN_DATE, BRANCH, TRAN_BRANCH, SUB_SEQ_NO, CR_DR_IND, TRAN_TYPE, EVENT_TYPE,
        AMOUNT, SPREAD_PERCENT, INT_AMT, ODI_AMT, PRI_AMT, TAX_AMT, ODP_AMT, AMT_TYPE,
        GL_POSTED_FLAG, IN_STATUS, REFERENCE, REVERSAL_FLAG, SOURCE_TYPE, ACCOUNTING_STATUS,
        BANK_SEQ_NO, BUSINESS_UNIT, MARKETING_PROD, MARKETING_PROD_DESC, NARRATIVE,
        PROFIT_CENTER, SEND_SYSTEM, SOURCE_MODULE, GL_CODE, SYSTEM_ID, UN_REAL,
        TRAN_TIMESTAMP, COMPANY, USER_ID)
        select
        CHANNEL_SEQ_NO,
        GL_SEQ_NO,
        INTERNAL_KEY,
        BASE_ACCT_NO,
        ACCT_SEQ_NO,
        ACCT_CCY,
        PROD_TYPE,
        CLIENT_NO,
        CLIENT_TYPE,
        CHANNEL_DATE,
        EFFECT_DATE,
        TRAN_CATEGORY,
        TRAN_DATE,
        ACCT_BRANCH,
        TRAN_BRANCH,
        SUB_SEQ_NO,
        CR_DR_IND,
        TRAN_TYPE,
        EVENT_TYPE,
        AMOUNT,
        SPREAD_PERCENT,
        INT_AMT,
        ODI_AMT,
        PRI_AMT,
        TAX_AMT,
        ODP_AMT,
        AMT_TYPE,
        GL_POSTED_FLAG,
        IN_STATUS,
        REFERENCE,
        REVERSAL_FLAG,
        SOURCE_TYPE,
        ACCOUNTING_STATUS,
        BANK_SEQ_NO,
        BUSINESS_UNIT,
        MARKETING_PROD,
        MARKETING_PROD_DESC,
        NARRATIVE,
        PROFIT_CENTER,
        SEND_SYSTEM,
        SOURCE_MODULE,
        GL_CODE,
        SYSTEM_ID,
        UN_REAL,
        TRAN_TIMESTAMP,
        COMPANY,
        USER_ID
        FROM ${cometTableName}
        <if test="partitionName != null">PARTITION (${partitionName})</if>
        WHERE TRAN_DATE &lt;= #{retainDate,jdbcType=DATE}
    </update>
</mapper>
