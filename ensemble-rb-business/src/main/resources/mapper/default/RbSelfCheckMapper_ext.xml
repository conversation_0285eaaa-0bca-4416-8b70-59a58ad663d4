<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSelfCheck">
    <select id="selectLastLastRunDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSelfCheck"
            resultType="java.util.Date" >
        select MAX(CHECK_DATE)
        from RB_SELF_CHECK
        where <![CDATA[ CHECK_DATE < #{checkDate,jdbcType=DATE} ]]>
    </select>
</mapper>