<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_BALANCE_HIST
        <where>
            <if test="internalKey != null">
                AND INTERNAL_KEY = #{internalKey,jdbcType=DECIMAL}
            </if>
            <if test="tranDate != null">
                AND TRAN_DATE = #{tranDate,jdbcType=VARCHAR}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">
        delete from RB_ACCT_BALANCE_HIST
        <where>
            <if test="internalKey != null">
                INTERNAL_KEY = #{internalKey,jdbcType=DECIMAL}
            </if>
            <if test="tranDate != null">
                AND TRAN_DATE = #{tranDate,jdbcType=VARCHAR}
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">
        update RB_ACCT_BALANCE_HIST
        <set>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="dacValue != null">
                DAC_VALUE = #{dacValue,jdbcType=VARCHAR},
            </if>
            <if test="lastChangeUserId != null">
                LAST_CHANGE_USER_ID = #{lastChangeUserId,jdbcType=VARCHAR},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                COMPANY = #{company,jdbcType=VARCHAR},
            </if>


        </set>
        where INTERNAL_KEY = #{internalKey,jdbcType=DECIMAL}
        AND TRAN_DATE = #{tranDate,jdbcType=VARCHAR}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">
        insert into RB_ACCT_BALANCE_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="dacValue != null">
                DAC_VALUE,
            </if>
            <if test="lastChangeUserId != null">
                LAST_CHANGE_USER_ID,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="company != null">
                COMPANY,
            </if>


        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                #{internalKey,jdbcType=BIGINT},
            </if>
            <if test="tranDate != null">
                #{tranDate,jdbcType=VARCHAR},
            </if>
            <if test="amtType != null">
                #{amtType,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="caAmount != null">
                #{caAmount,jdbcType=DECIMAL},
            </if>
            <if test="ttAmount != null">
                #{ttAmount,jdbcType=DECIMAL},
            </if>
            <if test="dacValue != null">
                #{dacValue,jdbcType=VARCHAR},
            </if>
            <if test="lastChangeUserId != null">
                #{lastChangeUserId,jdbcType=VARCHAR},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                #{company,jdbcType=VARCHAR},
            </if>


        </trim>
    </insert>


    <select id="selectMinBalC" parameterType="java.util.Map" resultType="java.math.BigDecimal" flushCache="true"
            useCache="false">
        SELECT
        MIN(TOTAL_AMOUNT * (-1))
        FROM RB_ACCT_BALANCE_HIST
        WHERE  <![CDATA[internal_key =  #{internalKey}
              AND client_no = #{clientNo}
              AND  tran_date >= ]]>(select MAX(TRAN_DATE) from RB_ACCT_BALANCE_HIST
        where
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <![CDATA[ TRAN_DATE <= #{effectDate}
                                  and INTERNAL_KEY = #{internalKey}
                                  and CLIENT_NO = #{clientNo}  ]]>)
    </select>

    <select id="selectMinBalD" parameterType="java.util.Map" resultType="java.math.BigDecimal" flushCache="true"
            useCache="false">
        SELECT
        MIN (TOTAL_AMOUNT)
        FROM RB_ACCT_BALANCE_HIST
        WHERE <![CDATA[  internal_key =  #{internalKey}
              AND client_no = #{clientNo}
              AND  tran_date >=   ]]>(select MAX(TRAN_DATE) from RB_ACCT_BALANCE_HIST
        where
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <![CDATA[ TRAN_DATE <= #{effectDate}
                                  and INTERNAL_KEY = #{internalKey}
                                  and CLIENT_NO = #{clientNo} ]]>)
    </select>

    <select id="selectNearBalanceHist" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist" flushCache="true"
            useCache="false">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_BALANCE_HIST
        WHERE internal_key = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND tran_date >= #{nearDate}
        ORDER BY tran_date ASC

    </select>

    <select id="selectNearTranDate" parameterType="java.util.HashMap" resultType="java.lang.String" flushCache="true"
            useCache="false">
        select MAX(TRAN_DATE) tran_date
        from RB_ACCT_BALANCE_HIST
        where
        <![CDATA[ TRAN_DATE <= #{effectDate} ]]>
        and INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectBalanceHistForUpdate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist" flushCache="true"
            useCache="false">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_BALANCE_HIST
        WHERE internal_key = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CLIENT_NO = #{clientNo}
        AND  <![CDATA[ tran_date >= #{effectDate} ]]>
    </select>

    <select id="selectBalanceHist" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist" flushCache="true"
            useCache="false">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_ACCT_BALANCE_HIST
        WHERE internal_key = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CLIENT_NO = #{clientNo}
        <if test="tranDate != null">
            AND tran_date = #{tranDate}
        </if>
    </select>

    <select id="selectFixDayAmt" parameterType="java.util.Map" resultType="java.math.BigDecimal" flushCache="true" useCache="false">
        SELECT
        TOTAL_AMOUNT
        FROM RB_ACCT_BALANCE_HIST
        WHERE <![CDATA[  internal_key =  #{internalKey}
              AND client_no = #{clientNo}
              AND  tran_date <=   ]]>(select MAX(TRAN_DATE) from RB_ACCT_BALANCE_HIST
        where
        <!-- Multi-legal transformation by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <![CDATA[ TRAN_DATE < #{effectDate}
                                  and INTERNAL_KEY = #{internalKey}
                                  and CLIENT_NO = #{clientNo} ]]>)

        order by TRAN_DATE desc limit 1
    </select>

    <select id="selectMonthBalanceHistBal" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">
        SELECT
            CLIENT_NO,
            <!--DATE_FORMAT(TRAN_DATE, '%Y-%m') AS TRAN_DATE, -->
            MAX(CASE WHEN is_end = 1 THEN TOTAL_AMOUNT END) AS TOTAL_AMOUNT
        FROM (
             SELECT
                 INTERNAL_KEY,
                 TRAN_DATE,
                 TOTAL_AMOUNT,
                 CLIENT_NO,
                 TRAN_DATE = MAX(TRAN_DATE) OVER (PARTITION BY INTERNAL_KEY, DATE_FORMAT(TRAN_DATE, '%Y-%m')) AS is_end
             FROM
                 RB_ACCT_BALANCE_HIST rabh
             where
                 TRAN_DATE between  #{startDate} and  #{endDate}
         ) t
        WHERE
            is_end = 1 and client_no = #{clientNo}
        GROUP BY
            CLIENT_NO
        ORDER BY
            CLIENT_NO;


</select>
</mapper>
