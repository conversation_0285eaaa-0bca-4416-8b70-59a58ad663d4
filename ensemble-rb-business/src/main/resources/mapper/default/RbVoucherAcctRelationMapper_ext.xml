<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_ACCT_RELATION
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="docType != null">
            AND DOC_TYPE = #{docType}
        </if>
        <if test="voucherNo != null">
            AND VOUCHER_NO = #{voucherNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        delete from RB_VOUCHER_ACCT_RELATION
        where BASE_ACCT_NO = #{baseAcctNo}
        <if test="docType != null">
            AND DOC_TYPE = #{docType}
        </if>
        <if test="voucherNo != null">
            AND VOUCHER_NO = #{voucherNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        update RB_VOUCHER_ACCT_RELATION
        <set>
            <if test="prefix != null">
                PREFIX = #{prefix},
            </if>
            <if test="voucherStatus != null">
                VOUCHER_STATUS = #{voucherStatus},
            </if>
            <if test="oldStatus != null">
                OLD_STATUS = #{oldStatus},
            </if>
            <if test="cardNo != null">
                CARD_NO = #{cardNo},
            </if>
            <if test="prodType != null">
                PROD_TYPE = #{prodType},
            </if>
            <if test="acctCcy != null">
                ACCT_CCY = #{acctCcy},
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO = #{acctSeqNo},
            </if>
            <if test="collatInd != null">
                COLLAT_IND = #{collatInd},
            </if>
            <if test="collatNo != null">
                COLLAT_NO = #{collatNo},
            </if>
            <if test="remark != null">
                REMARK = #{remark},
            </if>
            <if test="company != null">
                COMPANY = #{company},
            </if>
            <if test="tranDate != null  ">
                TRAN_DATE = #{tranDate}  AND
            </if>
        </set>
        where BASE_ACCT_NO = #{baseAcctNo}
        AND DOC_TYPE = #{docType}
        AND VOUCHER_NO = #{voucherNo}
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        insert into RB_VOUCHER_ACCT_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="docType != null">
                DOC_TYPE,
            </if>
            <if test="prefix != null">
                PREFIX,
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO,
            </if>
            <if test="voucherStatus != null">
                VOUCHER_STATUS,
            </if>
            <if test="oldStatus != null">
                OLD_STATUS,
            </if>
            <if test="cardNo != null">
                CARD_NO,
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="acctCcy != null">
                ACCT_CCY,
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO,
            </if>
            <if test="collatInd != null">
                COLLAT_IND,
            </if>
            <if test="collatNo != null">
                COLLAT_NO,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="tranDate != null  ">
                TRAN_DATE,
            </if>
            <if test="canReasonCode != null  ">
                CAN_REASON_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="docType != null">
                #{docType},
            </if>
            <if test="prefix != null">
                #{prefix},
            </if>
            <if test="voucherNo != null">
                #{voucherNo},
            </if>
            <if test="voucherStatus != null">
                #{voucherStatus},
            </if>
            <if test="oldStatus != null">
                #{oldStatus},
            </if>
            <if test="cardNo != null">
                #{cardNo},
            </if>
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="acctCcy != null">
                #{acctCcy},
            </if>
            <if test="acctSeqNo != null">
                #{acctSeqNo},
            </if>
            <if test="collatInd != null">
                #{collatInd},
            </if>
            <if test="collatNo != null">
                #{collatNo},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="openBranch != null">
                #{openBranch},
            </if>
            <if test="tranDate != null  ">
                #{tranDate},
            </if>
            <if test="canReasonCode != null  ">
                #{canReasonCode},
            </if>
        </trim>
    </insert>
    <update id="changeVoucherStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        update RB_VOUCHER_ACCT_RELATION
        <set>
            <if test="prefix != null">
                PREFIX=#{prefix},
            </if>
            <if test="remark != null">
                REMARK=#{remark},
            </if>
            <if test="voucherStatus != null">
                VOUCHER_STATUS = #{voucherStatus},
            </if>
            <if test="oldStatus != null">
                OLD_STATUS = #{oldStatus},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="canReasonCode != null">
                CAN_REASON_CODE = #{canReasonCode},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != ''">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
        </set>
        where DOC_TYPE = #{docType}
        and VOUCHER_NO = #{voucherNo}
        and CLIENT_NO = #{clientNo}
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="upadteDocTypeAndStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        update RB_VOUCHER_ACCT_RELATION
        <set>
            <if test="voucherStatus != null">
                VOUCHER_STATUS = #{voucherStatus},
            </if>
            <if test="oldStatus != null">
                OLD_STATUS = #{oldStatus},
            </if>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate}
            </if>
            <if test="canReasonCode != null">
                CAN_REASON_CODE = #{canReasonCode},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != ''">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
        </set>
        where DOC_TYPE = #{docType}
        and VOUCHER_NO = #{voucherNo}
        and CLIENT_NO = #{clientNo}
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="changeVoucherStatusForNewOldAcctChange" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        update RB_VOUCHER_ACCT_RELATION
        <set>
                OLD_STATUS = VOUCHER_STATUS,
            <if test="prefix != null">
                PREFIX=#{prefix,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK=#{remark,jdbcType=VARCHAR},
            </if>
            <if test="voucherStatus != null">
                VOUCHER_STATUS = #{voucherStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where 1=1
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="docType != null and docType != ''">
            and DOC_TYPE = #{docType,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <update id="relationChange" parameterType="java.util.Map" >
        update RB_VOUCHER_ACCT_RELATION
        <set>
            <if test="newProdType != null">
                PROD_TYPE = #{newProdType},
            </if>
            <if test="voucherStatus != null">
                VOUCHER_STATUS = #{voucherStatus,jdbcType=VARCHAR},
            </if>
            <if test="newCcy != null">
                ACCT_CCY = #{newCcy},
            </if>
            <if test="newAcctSeqNo != null">
                ACCT_SEQ_NO = #{newAcctSeqNo},
            </if>
            <if test="newBaseAcctNo != null">
                BASE_ACCT_NO = #{newBaseAcctNo},
            </if>
            <if test="prefix != null">
                PREFIX=#{prefix},
            </if>
            <if test="remark != null">
                REMARK=#{remark}
            </if>
        </set>
        where DOC_TYPE = #{docType}
        and VOUCHER_NO = #{voucherNo}
        and BASE_ACCT_NO = #{oldBaseAcctNo}
        <if test="clientNo != null and clientNo != ''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>


    <select id="getMbVoucherAcctRelationByAcctNoVouch2" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE VOUCHER_STATUS = 'ACT'
        and DOC_CLASS in('PBK','CRD','DCT')
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="isCard =='true'">
            and CARD_NO IS NOT NULL
        </if>
        <if test="isCard =='false'">
            and CARD_NO IS NULL
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbVoucherAcctRelationInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE VOUCHER_STATUS != 'POB'
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="voucherStatus != null and voucherStatus !=''">
            and VOUCHER_STATUS = #{voucherStatus}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="isCard =='true'">
            and CARD_NO IS NOT NULL
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbVoucherInfoByAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE VOUCHER_STATUS NOT IN ('CAN', 'USE')
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="filterDocType != null and filterDocType !=''">
            and DOC_CLASS != #{filterDocType}
        </if>
        <if test="docTypeNeq != null and docTypeNeq !=''">
            and DOC_TYPE != #{docTypeNeq}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbVoucherInfoByAcctNoCla" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE VOUCHER_STATUS NOT IN ('CAN', 'USE')
        <if test="cardNo != null and cardNo !=''">
            and CARD_NO = #{cardNo}
        </if>
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="docClass != null and docClass !=''">
            and DOC_CLASS = #{docClass}
        </if>
        <if test="docTypeNeq != null and docTypeNeq !=''">
            and DOC_TYPE != #{docTypeNeq}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getRbVoucherAcctRelationListByPage" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        <where>
            <if test="baseAcctNo != null and baseAcctNo !=''">
                and BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="acctCcy != null and acctCcy !=''">
                and ACCT_CCY = #{acctCcy}
            </if>
            <if test="prodType != null and prodType !=''">
                and PROD_TYPE = #{prodType}
            </if>
            <if test="acctSeqNo != null and acctSeqNo !=''">
                and ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="docType != null and docType !=''">
                and DOC_TYPE = #{docType}
            </if>
            <if test="clientNo != null and clientNo !=''">
                and CLIENT_NO = #{clientNo}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
    <select id="getMbVoucherAcctRelationByCardNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE CARD_NO = #{cardNo}
          and CLIENT_NO =#{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbVoucherAcctRelationByVoucher" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <if test="voucherStatus != null and voucherStatus !=''">
            and VOUCHER_STATUS = #{voucherStatus}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getVouAcctMsgByVoucherOrDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">

        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and (BASE_ACCT_NO = #{baseAcctNo} OR CARD_NO = #{baseAcctNo})
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="startVoucherNo != null and startVoucherNo !=''">
            and VOUCHER_NO <![CDATA[ >=]]>  #{startVoucherNo}
        </if>
        <if test="endVoucherNo != null and endVoucherNo !=''">
         <![CDATA[
          and VOUCHER_NO <= #{endVoucherNo}
         ]]>
        </if>

        <if test="voucherStatus != null and voucherStatus !=''">
            <![CDATA[
            and VOUCHER_STATUS = #{voucherStatus}
            ]]>
        </if>
        <if test="tranDate != null">
            and TRAN_DATE = #{tranDate}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY PREFIX,DOC_TYPE,VOUCHER_NO,VOUCHER_STATUS

    </select>
    <select id="getMbVoucherAcctRelationByVoucher14000330" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and (BASE_ACCT_NO = #{baseAcctNo} OR CARD_NO = #{baseAcctNo})
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="startVoucherNo != null and startVoucherNo !=''">
            and VOUCHER_NO >= #{startVoucherNo}
        </if>
        <if test="endVoucherNo != null and endVoucherNo !=''">
            <![CDATA[
          and VOUCHER_NO <= #{endVoucherNo}
                ]]>
        </if>

        <if test="voucherStatus != null and voucherStatus !=''">
            <![CDATA[
            and VOUCHER_STATUS = #{voucherStatus}
            ]]>
        </if>
        <if test="tranDate != null">
            and TRAN_DATE = #{tranDate}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        ORDER BY PREFIX,DOC_TYPE,VOUCHER_NO,VOUCHER_STATUS

    </select>
    <select id="getMbVoucherAcctRelationByBaseInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="seqNo != null and seqNo !=''">
            and ACCT_SEQ_NO = #{seqNo}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <if test="docClass != null and docClass !=''">
            and DOC_CLASS =#{docClass}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and VOUCHER_STATUS not in ('USE','CAN')
        order by VOUCHER_NO
    </select>
    <!-- Query the voucher whose status is reported loss -->
    <select id="getMbVoucherAcctRelationForLost" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE 1=1
        and VOUCHER_STATUS in ('VER','LCC')
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="seqNo != null and seqNo !=''">
            and ACCT_SEQ_NO = #{seqNo}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getInfoByAcctVouchStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="seqNo != null and seqNo !=''">
            and ACCT_SEQ_NO = #{seqNo}
        </if>
        <if test="tranDate != null">
            and TRAN_DATE = #{tranDate}
        </if>
        <if test="voucherStatus != null and voucherStatus !=''">
            and VOUCHER_STATUS = #{voucherStatus}
        </if>
        <if test="oldStatus != null and oldStatus !=''">
            and OLD_STATUS = #{oldStatus}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getRbVoucherClientNoList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        CLIENT_NO,
        BASE_ACCT_NO,
        CARD_NO
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and (BASE_ACCT_NO = #{baseAcctNo} or CARD_NO = #{baseAcctNo})
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="prefix != null and prefix !=''">
            and PREFIX = #{prefix}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <!-- 14000009 Dedicated to prevent query results from overflowing and add conditions -->
    <select id="getByBaseInfoEffective" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE DOC_CLASS in('PBK','CRD','DCT')
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and (BASE_ACCT_NO = #{baseAcctNo} or CARD_NO = #{baseAcctNo})
        </if>
        <if test="ccy != null and ccy !=''">
            and ACCT_CCY = #{ccy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="seqNo != null and seqNo !=''">
            and ACCT_SEQ_NO = #{seqNo}
        </if>
        <if test="docClass != null and docClass !=''">
            and (DOC_CLASS != #{docClass} or DOC_CLASS is null)
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getRbVoucherBusiModelList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherBusiModel" >
        SELECT
        r.DOC_TYPE DOC_TYPE,
        r.PREFIX PREFIX,
        r.VOUCHER_NO VOUCHER_NO,
        j.VOUCHER_STATUS VOUCHER_STATUS,
        r.BASE_ACCT_NO BASE_ACCT_NO,
        j.VOUCHER_STATUS TRAN_EVENT,
        j.TRAN_DATE TRAN_DATE,
        j.BRANCH BRANCH,
        j.USER_ID USER_ID
        FROM RB_VOUCHER_ACCT_RELATION r, RB_VOUCHER_JOURNAL j
        WHERE 1=1
            and r.BASE_ACCT_NO = j.BASE_ACCT_NO
            and r.DOC_TYPE = j.DOC_TYPE
            and r.VOUCHER_NO = j.VOUCHER_NO
            and r.CLIENT_NO = j.CLIENT_NO
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and (r.BASE_ACCT_NO = #{baseAcctNo} or r.CARD_NO = #{baseAcctNo})
        </if>
        <if test="docType != null and docType !=''">
            and r.DOC_TYPE = #{docType}
        </if>
        <if test="prefix != null and prefix !=''">
            and r.PREFIX = #{prefix}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and r.VOUCHER_NO = #{voucherNo}
        </if>
        <if test="clientNo != null and clientNo !=''">
            and r.CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND r.COMPANY = #{company}
        </if>
        order by  j.TRAN_DATE desc
    </select>

    <select id="getRbVoucherStatusModelList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherStatusModel" >
        SELECT r.DOC_TYPE DOC_TYPE,
        r.OLD_STATUS OLD_STATUS,
        r.VOUCHER_STATUS VOUCHER_STATUS,
        r.TRAN_DATE TRAN_DATE,
        r.BASE_ACCT_NO BASE_ACCT_NO,
        j.USER_ID USER_ID
        FROM RB_VOUCHER_ACCT_RELATION r, RB_VOUCHER_JOURNAL j
        WHERE r.CLIENT_NO = j.CLIENT_NO
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and (r.BASE_ACCT_NO = #{baseAcctNo} or r.CARD_NO = #{baseAcctNo})
        </if>
        <if test="clientNo != null and clientNo !=''">
            and r.CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND r.COMPANY = #{company}
        </if>
    </select>
    <update id="updateByPrimaryKeyExtBak"  >
        UPDATE <include refid="Table_Name" />
        <set>
            <if test="acctSeqNo != null and  acctSeqNo != '' ">
                ACCT_SEQ_NO = #{acctSeqNo},
            </if>
            <if test="remark != null and  remark != '' ">
                REMARK = #{remark},
            </if>
            <if test="voucherStatus != null and  voucherStatus != '' ">
                VOUCHER_STATUS = #{voucherStatus},
            </if>
            <if test="oldStatus != null and  oldStatus != '' ">
                OLD_STATUS = #{oldStatus},
            </if>
            <if test="cardNo != null and  cardNo != '' ">
                CARD_NO = #{cardNo},
            </if>
            <if test="company != null and  company != '' ">
                COMPANY = #{company},
            </if>
            <if test="collatInd != null and  collatInd != '' ">
                COLLAT_IND = #{collatInd},
            </if>
            <if test="acctCcy != null and  acctCcy != '' ">
                ACCT_CCY = #{acctCcy},
            </if>
            <if test="collatNo != null and  collatNo != '' ">
                COLLAT_NO = #{collatNo},
            </if>
            <if test="prodType != null and  prodType != '' ">
                PROD_TYPE = #{prodType},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="prefix != null and  prefix != '' ">
                PREFIX = #{prefix},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND">
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo}  AND
                </if>
                <if test="baseAcctNo != null and  baseAcctNo != '' ">
                    BASE_ACCT_NO = #{baseAcctNo}  AND
                </if>
                <if test="voucherNo != null and  voucherNo != '' ">
                    VOUCHER_NO = #{voucherNo}  AND
                </if>
                <if test="docType != null and  docType != '' ">
                    DOC_TYPE = #{docType}  AND
                </if>
                <!-- 多法人改造 by luocwa -->
                <if test="company != null and company != '' ">
                    COMPANY = #{company} AND
                </if>
            </trim>
        </where>
    </update>

    <select id="getMbVoucherAcctRelationIsNotCan" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE VOUCHER_STATUS NOT IN ('CAN')
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="isCard =='true'">
            and CARD_NO IS NOT NULL
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <update id="updateMbVoucherRelationByBaseInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">

        update RB_VOUCHER_ACCT_RELATION
        <set>
            prod_type = #{newProdType}
        </set>
        <where>
            PROD_TYPE = #{prodType, jdbcType=VARCHAR}
            AND  BASE_ACCT_NO = #{baseAcctNo, jdbcType=VARCHAR}
            AND  ACCT_CCY = #{acctCcy, jdbcType=VARCHAR}
            AND  ACCT_SEQ_NO = #{acctSeqNo, jdbcType=VARCHAR}
            AND  CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </where>
    </update>

    <update id="updateMbVoucherByCardNoAndVoucherNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">

        update RB_VOUCHER_ACCT_RELATION
        <set>
            BASE_ACCT_NO = #{cardNo}
        </set>
        <where>
              VOUCHER_STATUS='ACT'
            <if test="cardNo != null and cardNo !=''">
                AND  CARD_NO=#{cardNo, jdbcType=VARCHAR}
            </if>
            <if test="acctSeqNo != null and acctSeqNo !=''">
                AND  ACCT_SEQ_NO = #{acctSeqNo, jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null and clientNo !=''">
                AND  CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
            </if>
            <if test="docType != null and docType !=''">
                and DOC_TYPE = #{docType, jdbcType=VARCHAR}
            </if>
        </where>
    </update>

    <select id="getMbVoucherAcctRelationByStartEndVoucherNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        <where>
            <if test="baseAcctNo != null and baseAcctNo !=''">
                and BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="docType != null and docType !=''">
                and DOC_TYPE = #{docType}
            </if>
            <if test="prefix != null and prefix != ''">
                and prefix = #{prefix}
            </if>
            <if test="voucherStartNo != null and voucherStartNo !=''">
                and VOUCHER_NO <![CDATA[>=]]> #{voucherStartNo}
            </if>
            <if test="voucherEndNo != null and voucherEndNo !=''">
                and VOUCHER_NO <![CDATA[<=]]> #{voucherEndNo}
            </if>
            <if test="voucherStatus != null and voucherStatus !=''">
                and VOUCHER_STATUS = #{voucherStatus}
            </if>
            <if test="clientNo != null and clientNo !=''">
                and CLIENT_NO = #{clientNo}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
    <select id="getListByVoucherNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        <where>
            <if test="docType != null and docType !=''">
                and DOC_TYPE = #{docType}
            </if>
            <if test="prefix != null and prefix != ''">
                and prefix = #{prefix}
            </if>
            <if test="voucherNo != null and voucherNo !=''">
                and VOUCHER_NO = #{voucherNo}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="companyList != null">
                AND COMPANY in
                <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getRbVoucherByVoucherStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE  VOUCHER_STATUS NOT IN ('USE','CAN')
        <if test="baseAcctNo != null and baseAcctNo !=''">
            AND BASE_ACCT_NO=#{baseAcctNo}
        </if>
        <if test="ccy != null and ccy !=''">
            AND ACCT_CCY=#{ccy}
        </if>
        <if test="prodType != null and prodType !=''">
            AND PROD_TYPE=#{prodType}
        </if>
        <if test="seqNo != null and seqNo !=''">
            AND ACCT_SEQ_NO=#{seqNo}
        </if>
        <if test="clientNo != null and clientNo !=''">
            AND CLIENT_NO=#{clientNo}
        </if>
        <if test="docType != null and docType !=''">
            AND DOC_TYPE=#{docType}
        </if>
    </select>
    <select id="getRbVoucherAcctRelation" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        <where>
            <if test="baseAcctNo != null and baseAcctNo !=''">
                BASE_ACCT_NO = #{baseAcctNo} or CARD_NO = #{baseAcctNo}
            </if>
            <if test="voucherStatus != null and voucherStatus !=''">
                and VOUCHER_STATUS = #{voucherStatus}
            </if>
            <if test="clientNo != null and clientNo !=''">
                and CLIENT_NO = #{clientNo}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </select>
</mapper>
