<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSignType">

	<select id="getRbSignType" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSignType">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_SIGN_TYPE
		WHERE SIGN_TYPE =#{signType}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND ( COMPANY = #{company} OR COMPANY = 'ALL' )
		</if>
	</select>
</mapper>
