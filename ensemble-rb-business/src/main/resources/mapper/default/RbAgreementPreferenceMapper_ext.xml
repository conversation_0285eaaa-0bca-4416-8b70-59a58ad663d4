<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPreference">

    <select id="getAgreementPreferenceByAgreementId" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPreference" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPreference">
        select <include refid="Base_Column"/>
        from RB_AGREEMENT_PREFERENCE
        where AGREEMENT_ID = #{agreementId}
        <if test="preferenceType != null">
            AND PREFERENCE_TYPE = #{preferenceType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
	</select>
    <select id="getMbAgrePreByagreId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPreference">
        SELECT <include refid="Base_Column"/> FROM RB_AGREEMENT_PREFERENCE
        WHERE AGREEMENT_ID = #{agreementId}
        <if test="preferenceType != null">
            AND PREFERENCE_TYPE = #{preferenceType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

        ORDER BY PREFERENCE_VALUE+0 ASC
    </select>
    <delete id="deleteByAgreementId" parameterType="java.util.Map">
        delete from RB_AGREEMENT_PREFERENCE
        where AGREEMENT_ID = #{agreementId}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
</mapper>
