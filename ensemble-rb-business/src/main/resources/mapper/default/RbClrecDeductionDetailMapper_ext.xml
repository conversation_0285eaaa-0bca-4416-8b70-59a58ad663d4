<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail">

    <!-- 自动回收明细前处理、贷款自动回收扣款明细处理 -->
    <select id="getListByBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail">
        <if test="_databaseId == 'mysql'">
            select
            <include refid="Base_Column"/>
            from RB_CLREC_DEDUCTION_DETAIL
            where BASE_ACCT_NO BETWEEN #{cometStart} and #{cometEnd}
            and TRAN_AMT = 0 and ifnull(RET_STATUS,'D') != 'S'
            <if test="amtCtl != null">
                AND AMT_CTL = #{amtCtl}
            </if>
            <if test="sceneId != null">
                AND SCENE_ID = #{sceneId}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            order by BATCH_SEQ_NO, BASE_ACCT_NO, ACCT_SEQ_NO+0, ORDER_NUM asc
        </if>
        <if test="_databaseId == 'oracle'">
            select
            <include refid="Base_Column"/>
            from RB_CLREC_DEDUCTION_DETAIL
            where BASE_ACCT_NO BETWEEN #{cometStart} and #{cometEnd}
            and TRAN_AMT = 0 and nvl(RET_STATUS,'D') != 'S'
            <if test="amtCtl != null">
                AND AMT_CTL = #{amtCtl}
            </if>
            <if test="sceneId != null">
                AND SCENE_ID = #{sceneId}
            </if>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            order by BATCH_SEQ_NO, BASE_ACCT_NO, to_number(ACCT_SEQ_NO), ORDER_NUM asc
        </if>
    </select>

    <!-- 华兴平安普惠、产业集群、新心金融(明细前处理、扣款明细处理) -->
    <select id="getListByBaseAcctNoAndSceneId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail">
        <if test="_databaseId == 'mysql'">
            select
            <include refid="Base_Column"/>
            from RB_CLREC_DEDUCTION_DETAIL
            where BASE_ACCT_NO BETWEEN #{cometStart} and #{cometEnd}
            and TRAN_AMT = 0 and ifnull(RET_STATUS,'D') != 'S'
            <if test="amtCtl != null">
                AND AMT_CTL = #{amtCtl}
            </if>
            <if test="sceneId != null">
                AND SCENE_ID = #{sceneId}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            order by BATCH_SEQ_NO, BASE_ACCT_NO, ACCT_SEQ_NO+0, ORDER_NUM asc
        </if>
        <if test="_databaseId == 'oracle'">
            select
            <include refid="Base_Column"/>
            from RB_CLREC_DEDUCTION_DETAIL
            where BASE_ACCT_NO BETWEEN #{cometStart} and #{cometEnd}
            and TRAN_AMT = 0 and nvl(RET_STATUS,'D') != 'S'
            <if test="amtCtl != null">
                AND AMT_CTL = #{amtCtl}
            </if>
            <if test="sceneId != null">
                AND SCENE_ID = #{sceneId}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            order by BATCH_SEQ_NO, BASE_ACCT_NO, to_number(ACCT_SEQ_NO), ORDER_NUM asc
        </if>
    </select>

    <select id="getListBySeqNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail">
        select
        <include refid="Base_Column"/>
        from RB_CLREC_DEDUCTION_DETAIL
        where SEQ_NO BETWEEN #{cometStart} and #{cometEnd}
        and TRAN_AMT = 0
        <if test="amtCtl != null">
            AND AMT_CTL = #{amtCtl}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by BATCH_SEQ_NO, BASE_ACCT_NO, ACCT_SEQ_NO+0, ORDER_NUM asc
    </select>

    <select id="getSettleSumByBatchNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(SETTLE_AMT) AS SUM_AMT
        FROM RB_CLREC_DEDUCTION_DETAIL
        WHERE BATCH_NO = #{batchNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getTranSumByBatchNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(TRAN_AMT) AS SUM_AMT
        FROM RB_CLREC_DEDUCTION_DETAIL
        WHERE BATCH_NO = #{batchNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getTranSum" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.cm.eod.RbClBatchSumCheckModel">
        SELECT count(*) AS SUM_NUM, sum(TRAN_AMT) AS SUM_AMT, BATCH_NO
        FROM RB_CLREC_DEDUCTION_DETAIL
        <where>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
        GROUP BY BATCH_NO
    </select>

    <select id="getDebtedDetailByBatch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail">
        select
        <include refid="Base_Column"/>
        from RB_CLREC_DEDUCTION_DETAIL
        WHERE BATCH_NO = #{batchNo}
        AND BATCH_SEQ_NO = #{batchSeqNo}
        AND TRAN_AMT > 0
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by ORDER_NUM asc
    </select>
    <select id="selectRbClrecDeductionDetailForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail">
        SELECT <include refid="Base_Column"/>
        FROM RB_CLREC_DEDUCTION_DETAIL
        WHERE SEQ_NO = #{seqNo}
        AND RET_STATUS = #{retStatus} FOR UPDATE
    </select>

    <update id="updateStatusByBaseAcctNo"  parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClrecDeductionDetail">
        update RB_CLREC_DEDUCTION_DETAIL
        <set>
            <if test="retStatus != null">
                RET_STATUS = #{retStatus},
            </if>
        </set>
        <where>
            <if test="baseAcctNo != null and baseAcctNo.length() > 0">
                AND BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="prodType != null and prodType.length() > 0">
                AND PROD_TYPE = #{prodType}
            </if>
            <if test="acctCcy != null and acctCcy.length() > 0">
                AND ACCT_CCY = #{acctCcy}
            </if>
            <if test="acctSeqNo != null and acctSeqNo.length() > 0">
                AND ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <if test="reference != null and reference.length() > 0">
                AND REFERENCE = #{reference}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </update>

    <select id="selectTotalTranAmtByBaseAcctNo" parameterType="java.util.Map"
            resultType="java.util.Map">
        select BASE_ACCT_NO,sum(TRAN_AMT) TOTAL_TRAN_AMT from RB_CLREC_DEDUCTION_DETAIL
        <where>
            <if test="baseAcctNo != null and baseAcctNo.length() > 0">
                AND BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="prodType != null and prodType.length() > 0">
                AND PROD_TYPE = #{prodType}
            </if>
            <if test="acctCcy != null and acctCcy.length() > 0">
                AND ACCT_CCY = #{acctCcy}
            </if>
            <if test="acctSeqNo != null and acctSeqNo.length() > 0">
                AND ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="reference != null and reference.length() > 0">
                AND REFERENCE = #{reference}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <if test="groupId != null and groupId.length() > 0">
                AND GROUP_ID = #{groupId}
            </if>
        </where>
        group by BASE_ACCT_NO
    </select>
</mapper>