<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionHist">

  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionHist">
    insert into RB_COMMISSION_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="commissionClientNo != null">
        COMMISSION_CLIENT_NO,
      </if>
      <if test="commissionClientName != null">
        COMMISSION_CLIENT_NAME,
      </if>
      <if test="commissionDocumentType != null">
        COMMISSION_DOCUMENT_TYPE,
      </if>
      <if test="commissionDocumentId != null">
        COMMISSION_DOCUMENT_ID,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="commissionClientTel != null">
        COMMISSION_CLIENT_TEL,
      </if>
      <if test="commissionExpireDate != null">
        COMMISSION_EXPIRE_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="commissionClientNo != null">
        #{commissionClientNo},
      </if>
      <if test="commissionClientName != null">
        #{commissionClientName},
      </if>
      <if test="commissionDocType != null">
        #{commissionDocType},
      </if>
      <if test="commissionDocId != null">
        #{commissionDocId},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="commissionPhone != null">
        #{commissionPhone},
      </if>
      <if test="commissionExpiryDate != null">
        #{commissionExpiryDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>

  <select id="getCommissionByInternalKey" parameterType="java.lang.String" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionHist">
    SELECT <include refid="Base_Column"/>
    FROM RB_COMMISSION_HIST
    WHERE internal_key =
    #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="deleteMbCommissionHist" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCommissionHist">
    select <include refid="Base_Column"/>
    from RB_COMMISSION_HIST
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
