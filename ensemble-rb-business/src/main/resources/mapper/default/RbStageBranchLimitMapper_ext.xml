<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchLimit">

	<select id="selectStageBranchLimitsByStageCode"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_BRANCH_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

	<select id="selectBranchLimitsByStageCodeAndAttachedTo"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageBranchLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_BRANCH_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
				<if test="attachedTo != null and  attachedTo != '' ">
					ATTACHED_TO = #{attachedTo}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>


</mapper>
