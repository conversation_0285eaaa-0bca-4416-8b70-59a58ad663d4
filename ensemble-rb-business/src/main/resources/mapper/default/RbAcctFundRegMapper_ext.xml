<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctFundReg">

	<select id="getFundRegInfoList" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctFundReg">
		SELECT <include refid="Base_Column"/>
		FROM RB_ACCT_FUND_REG
		WHERE FUND_STATUS = 'Y'
		<if test="baseAcctNo != null and baseAcctNo !=''">
			AND BASE_ACCT_NO = #{baseAcctNo}
		</if>
		<if test="fundAcctType != null and fundAcctType !=''">
			AND FUND_ACCT_TYPE = #{fundAcctType}
		</if>
		<if test="branchList != null and branchList.size() > 0">
			AND BRANCH IN
			<foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
				#{branch}
			</foreach>
		</if>
		ORDER BY TRAN_TIMESTAMP DESC
	</select>
	<select id="getFundRegInfoModel" parameterType="java.util.HashMap"
			resultType="com.dcits.ensemble.rb.business.model.inneracct.MbAcctFundRegModel">
		SELECT t1.ACCT_NAME,t2.TOTAL_AMOUNT,t3.CONTRA_BASE_ACCT_NO
		FROM RB_ACCT t1
		LEFT JOIN RB_ACCT_BALANCE t2 on t1.INTERNAL_KEY = t2.INTERNAL_KEY
		LEFT JOIN RB_ACCT_CONTRA_INFO t3 on t1.INTERNAL_KEY = t3.INTERNAL_KEY
		WHERE t1.INTERNAL_KEY = #{internalKey}
	</select>
	<select id="getFundRegInfoListByBranch" parameterType="java.util.HashMap"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctFundReg">
		SELECT <include refid="Base_Column"/>
		FROM RB_ACCT_FUND_REG
		WHERE FUND_STATUS = 'Y'
		<if test="branch != null and branch !=''">
			AND BRANCH = #{branch}
		</if>
		ORDER BY TRAN_TIMESTAMP DESC
	</select>

	<update id="updateByInfo" parameterType="java.util.Map">
		UPDATE RB_ACCT_FUND_REG SET FUND_STATUS = 'Y'
		<if test="othAcctSeqNo != null and othAcctSeqNo !=''">
			,OTH_ACCT_SEQ_NO = #{othAcctSeqNo}
		</if>
		<if test="othAcctSeqNo == null or othAcctSeqNo ==''">
			,OTH_ACCT_SEQ_NO = ''
		</if>
		<if test="othBaseAcctNo != null and othBaseAcctNo !=''">
			,OTH_BASE_ACCT_NO=#{othBaseAcctNo}
		</if>
		<if test="othBaseAcctNo == null or othBaseAcctNo ==''">
			,OTH_BASE_ACCT_NO=''
		</if>
		,FUND_ACCT_TYPE=#{fundAcctType}
		where BASE_ACCT_NO = #{baseAcctNo} AND ACCT_SEQ_NO = #{acctSeqNo}
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO = #{clientNo}
		</if>
	</update>
	<update id="updateStatusByAcctNo" parameterType="java.util.Map">
		UPDATE RB_ACCT_FUND_REG SET FUND_STATUS = 'N'
		where BASE_ACCT_NO = #{baseAcctNo} AND ACCT_SEQ_NO = #{acctSeqNo}
		<if test="clientNo != null and clientNo !=''">
			AND CLIENT_NO = #{clientNo}
		</if>
	</update>
</mapper>
