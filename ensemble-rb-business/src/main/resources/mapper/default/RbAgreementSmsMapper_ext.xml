<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSms">
    <select id="selectCosSmsSignInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSms">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SMS
        where
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        to_char(TRAN_TIMESTAMP,'yyyymmdd') = to_char(#{tranTimestamp},'yyyymmdd') and take_sign_flag in ('A','Y')
    </select>

    <select id="selectListByTakeSignFlag" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSms">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_SMS
        <where>
            <include refid="Base_Where_List" />
        </where>
    </select>

    <select id="updateRbAgreementName" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSms">
        update RB_AGREEMENT_SMS
        <set>
            <if test="chClientName != null and chClientName != '' ">
                CH_CLIENT_NAME = #{chClientName},
            </if>
            <if test="documentType != null and documentType != '' ">
                DOCUMENT_TYPE = #{documentType},
            </if>
            <if test="documentId != null and documentId != '' ">
                DOCUMENT_ID = #{documentId},
            </if>
            <if test="genderFlag != null and genderFlag != '' ">
                GENDER_FLAG = #{genderFlag},
            </if>
            <if test="documentExpiryDate != null and documentExpiryDate != '' ">
                DOCUMENT_EXPIRY_DATE = #{documentExpiryDate},
            </if>
        </set>
        where CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <sql id="Base_Where_List">
        <trim suffixOverrides="AND">
            <if test="internalKey != null ">
                INTERNAL_KEY = #{internalKey}  AND
            </if>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                 COMPANY = #{company} AND
            </if>
            <if test="agreementId != null and  agreementId != '' ">
                AGREEMENT_ID = #{agreementId}  AND
            </if>
            <if test="tranMinAmt != null ">
                TRAN_MIN_AMT = #{tranMinAmt}  AND
            </if>
            <if test="feeAmt != null ">
                FEE_AMT = #{feeAmt}  AND
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                CLIENT_NO = #{clientNo}  AND
            </if>
            <if test="documentType != null and  documentType != '' ">
                DOCUMENT_TYPE = #{documentType}  AND
            </if>
            <if test="position != null and  position != '' ">
                POSITION = #{position}  AND
            </if>
            <if test="chClientName != null and  chClientName != '' ">
                CH_CLIENT_NAME = #{chClientName}  AND
            </if>
            <if test="takeInSignCash != null ">
                TAKE_IN_SIGN_CASH = #{takeInSignCash}  AND
            </if>
            <if test="takeOutSign != null ">
                TAKE_OUT_SIGN = #{takeOutSign}  AND
            </if>
            <if test="documentExpiryDate != null and  documentExpiryDate != '' ">
                DOCUMENT_EXPIRY_DATE = #{documentExpiryDate}  AND
            </if>
            <if test="contactTel != null and  contactTel != '' ">
                CONTACT_TEL = #{contactTel}  AND
            </if>
            <if test="chargeDay != null and  chargeDay != '' ">
                CHARGE_DAY = #{chargeDay}  AND
            </if>
            <if test="feeType != null and  feeType != '' ">
                FEE_TYPE = #{feeType}  AND
            </if>
            <if test="agreementLevel != null and  agreementLevel != '' ">
                AGREEMENT_LEVEL = #{agreementLevel}  AND
            </if>
            <if test="nextChargeDate != null ">
                NEXT_CHARGE_DATE = #{nextChargeDate}  AND
            </if>
            <if test="takeSignFlag != null">
                TAKE_SIGN_FLAG IN
                <foreach item="item" index="index" collection="takeSignFlag" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND
            </if>
            <if test="takeOutSignCash != null ">
                TAKE_OUT_SIGN_CASH = #{takeOutSignCash}  AND
            </if>
            <if test="documentId != null and  documentId != '' ">
                DOCUMENT_ID = #{documentId}  AND
            </if>
            <if test="smsOpenFlag != null and  smsOpenFlag != '' ">
                SMS_OPEN_FLAG = #{smsOpenFlag}  AND
            </if>
            <if test="genderFlag != null and  genderFlag != '' ">
                GENDER_FLAG = #{genderFlag}  AND
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP = #{tranTimestamp}  AND
            </if>
            <if test="cashMinAmt != null ">
                CASH_MIN_AMT = #{cashMinAmt}  AND
            </if>
            <if test="chargePeriodFreq != null and  chargePeriodFreq != '' ">
                CHARGE_PERIOD_FREQ = #{chargePeriodFreq}  AND
            </if>
            <if test="chargeToBaseAcctNo != null and  chargeToBaseAcctNo != '' ">
                CHARGE_TO_BASE_ACCT_NO = #{chargeToBaseAcctNo}  AND
            </if>
        </trim>
    </sql>
</mapper>
