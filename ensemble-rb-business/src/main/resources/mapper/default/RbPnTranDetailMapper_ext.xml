<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail">

  <select id="selectMbPnTranDetailOne" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail">
    SELECT *
    FROM RB_PN_TRAN_DETAIL
    WHERE
    <if test="origSerialNo != null">
      ORIG_SERIAL_NO = #{origSerialNo}
    </if>
    <if test="operType != null">
      AND OPER_TYPE = #{operType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectMbPnTranDetailList" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail">
    SELECT *
    FROM RB_PN_TRAN_DETAIL
    <where>
      <if test="serialNo != null and serialNo !=''">
        ORIG_SERIAL_NO = #{serialNo}
      </if>
      <if test="operType != null and operType !=''">
        AND OPER_TYPE = #{operType}
      </if>
      <if test="branch != null and branch !=''">
        AND TRAN_BRANCH = #{branch}
      </if>
      <if test="billStatus != null and billStatus !=''">
        AND BILL_STATUS = #{billStatus}
      </if>
      <if test="startDate != null and startDate !=''">
        <![CDATA[AND TRAN_DATE >= #{startDate}]]>
      </if>
      <if test="endDate != null and endDate !=''">
        <![CDATA[AND TRAN_DATE <= #{endDate}]]>
      </if>
      <if test="billNo != null and billNo !=''">
        AND BILL_NO = #{billNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    ORDER BY TRAN_DATE DESC
  </select>
  <select id="selectMbPnTranDetailListBySerialNo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail">
    SELECT A.*,B.BILL_APPLY_NO
    FROM RB_PN_TRAN_DETAIL A
    LEFT JOIN MB_PN_REGISTER B ON A.ORIG_SERIAL_NO = B.SERIAL_NO
    <where>
      <if test="serialNo != null and serialNo !=''">
        A.SERIAL_NO = #{serialNo}
      </if>
      <if test="operType != null and operType !=''">
        AND A.OPER_TYPE = #{operType}
      </if>
      <if test="billStatus != null and billStatus !=''">
        AND A.BILL_STATUS = #{billStatus}
      </if>
      <if test="startDate != null and startDate !=''">
        <![CDATA[AND A.TRAN_DATE >= #{startDate}]]>
      </if>
      <if test="endDate != null and endDate !=''">
        <![CDATA[AND A.TRAN_DATE <= #{endDate}]]>
      </if>
      <if test="billNo != null and billNo !=''">
        AND A.BILL_NO = #{billNo}
      </if>
      <if test="origSerialNo != null and origSerialNo !=''">
        AND A.ORIG_SERIAL_NO = #{origSerialNo}
      </if>
      <if test="branchs != null and branchs !=''">
        AND A.TRAN_BRANCH IN
        <foreach item="item" index="index" collection="branchs" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND A.COMPANY = #{company}
      </if>
    </where>
    ORDER BY A.TRAN_DATE DESC, A.SERIAL_NO DESC
  </select>
</mapper>
