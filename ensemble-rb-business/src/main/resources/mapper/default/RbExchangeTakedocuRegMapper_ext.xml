<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTakedocuReg">
	<sql id="Base_Column_List">
				DOCUMENT_NO,
				NATIONAL,
				CCY,
				OLD_VOUCHER_NO,
				SEQ_NO,
				CLIENT_NAME,
				BRANCH,
				EFFECT_DATE,
				STATUS,
				DOCUMENT_TYPE,
				VOUCHER_NO,
				USER_ID,
				OPTIONS,
				OLD_EFFECT_DATE,
				BOURN,
				AMT,
				TRAN_DATE,
				DOC_TYPE,
				PREFIX,
				EFFECT_DATE,
				OLD_DOC_TYPE,
				OLD_PREFIX,
				VOUCHER_STATUS,
				OLD_STATUS,
				TAKE_DOC_ID
	</sql>

	<select id="getRbExchangeTakedocuReg" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTakedocuReg"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTakedocuReg">
		select <include refid="Base_Column_List"/>
		from RB_EXCHANGE_TAKEDOCU_REG
		<where>1=1
			<if test="documentNo != null and  documentNo != '' ">
				AND DOCUMENT_NO = #{documentNo}
			</if>
			<if test="ccy != null and  ccy != '' ">
				AND CCY = #{ccy}
			</if>
			<if test="voucherNo != null and  voucherNo != '' ">
				AND VOUCHER_NO = #{voucherNo}
			</if>
			<if test="national != null and  national != '' ">
				AND NATIONAL = #{national}
			</if>
			<if test="branch != null and  branch != '' ">
				AND BRANCH = #{branch}
			</if>
			<if test="effectDate != null ">
				AND EFFECT_DATE = #{effectDate}
			</if>
			<if test="docType != null and  docType != '' ">
				AND DOC_TYPE = #{docType}
			</if>
			<if test="prefix != null and  prefix != '' ">
				AND PREFIX = #{prefix}
			</if>
			<if test="documentType != null and  documentType != '' ">
				AND DOCUMENT_TYPE = #{documentType}
			</if>
			<if test="bourn != null and  bourn != '' ">
				AND BOURN = #{bourn}
			</if>
			<if test="takeDocId != null and  takeDocId != '' ">
				AND TAKE_DOC_ID = #{takeDocId}
			</if>
		</where>
	</select>

	<update id="updateRbExchangeTakedocuStatusReg" parameterType="java.util.Map">
		update RB_CREDIT_BSE_RESTRICT_SIGN
		<set>
			STATUS = '1'
		</set>
		<![CDATA[
			where EFFECT_DATE <= #{effectDate}
			AND STATUS IS NULL
		]]>)
	</update>
</mapper>