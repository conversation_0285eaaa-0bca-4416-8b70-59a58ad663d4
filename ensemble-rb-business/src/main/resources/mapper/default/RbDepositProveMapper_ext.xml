<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositProve">

  <select id="getRbDepositProve" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositProve">
    select
    <include refid="Base_Column"/>
    from RB_DEPOSIT_PROVE
    where 1=1
    <if test="internalKey != null and internalKey != ''">
      AND
      INTERNAL_KEY=#{internalKey}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND
      CLIENT_NO=#{clientNo}
    </if>
    <if test="baseAcctNo != null and  baseAcctNo != '' ">
      AND BASE_ACCT_NO  = #{baseAcctNo}
    </if>
    <if test="channel != null and  channel != '' ">
      AND CHANNEL  = #{channel}
    </if>
    <if test="certEndDate != null  ">
      AND CERT_END_DATE  = #{certEndDate}
    </if>
    <if test="tranDate != null  ">
      AND TRAN_DATE  = #{tranDate}
    </if>
    <if test="tranBranch != null and  tranBranch != '' ">
      AND TRAN_BRANCH  = #{tranBranch}
    </if>
    <if test="userId != null and  userId != '' ">
      AND USER_ID  = #{userId}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <insert id="insertRbDepositProve" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositProve">
    insert into RB_DEPOSIT_PROVE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctCcy != null">
        ACCT_CCY,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="bondCode != null">
        BOND_CODE,
      </if>
      <if test="bondBriefName != null">
        BOND_BRIEF_NAME,
      </if>
      <if test="riseAmt != null">
        RISE_AMT,
      </if>
      <if test="balance != null">
        BALANCE,
      </if>
      <if test="channel != null">
        CHANNEL,
      </if>
      <if test="certBal != null">
        CERT_BAL,
      </if>
      <if test="term != null">
        TERM,
      </if>
      <if test="termType != null">
        TERM_TYPE,
      </if>
      <if test="certEndDate != null">
        CERT_END_DATE,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="PREFIX != null">
        PREFIX,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="bond1No != null">
        BOND1_NO,
      </if>
      <if test="riseDate != null">
        RISE_DATE,
      </if>
      <if test="bondType != null">
        BOND_TYPE,
      </if>
      <if test="bonfEndDate != null">
        BONF_END_DATE,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="seqNo != null">
            SEQ_NO
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctCcy != null">
        #{acctCcy},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="bondCode != null">
        #{bondCode},
      </if>
      <if test="bondBriefName != null">
        #{bondBriefName},
      </if>
      <if test="riseAmt != null">
        #{riseAmt},
      </if>
      <if test="balance != null">
        #{balance},
      </if>
      <if test="channel != null">
        #{channel},
      </if>
      <if test="certBal != null">
        #{certBal},
      </if>
      <if test="term != null">
        #{term},
      </if>
      <if test="termType != null">
        #{termType},
      </if>
      <if test="certEndDate != null">
        #{certEndDate},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
      <if test="PREFIX != null">
        #{PREFIX},
      </if>
      <if test="voucherNo != null">
        #{voucherNo},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
      <if test="bond1No != null">
        #{bond1No},
      </if>
      <if test="riseDate != null">
        #{riseDate},
      </if>
      <if test="bondType != null">
        #{bondType},
      </if>
      <if test="bonfEndDate != null">
        #{bonfEndDate},
      </if>
      <if test="branch != null">
        #{branch},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="seqNo != null">
         #{seqNo}
      </if>

    </trim>
  </insert>

</mapper>
