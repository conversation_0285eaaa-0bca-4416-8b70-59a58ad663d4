<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchFinancialDetails">

  <select id="selectRbBatchFinancialDetailsForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchFinancialDetails" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchFinancialDetails">
    SELECT <include refid="Base_Column"/>
    FROM RB_BATCH_FINANCIAL_DETAILS
    WHERE SEQ_NO = #{seqNo}
    AND STATUS = #{status} FOR UPDATE
  </select>

  <select id="selectFinDetailsByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchFinancialDetails">
    SELECT <include refid="Base_Column"/>
    FROM RB_BATCH_FINANCIAL_DETAILS
    WHERE REFERENCE = #{reference}
    AND BUSI_TYPE = 'PAPH'
  </select>

</mapper>