<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge">

  <select id="selectByChargeInternalStatus" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge">
    select <include refid="Base_Column"/>
      from RB_OSD_SERV_CHARGE
     where INTERNAL_KEY= #{internalKey} AND FEE_AMT > 0 AND OSD_STATUS='C'
    <if test="clientNo != null and  clientNo != '' ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="feeType != null and feeType.length() > 0" >
      AND fee_type =#{feeType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByChargeStatus" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge">
    select <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE
    where INTERNAL_KEY= #{internalKey} AND FEE_AMT > 0 AND OSD_STATUS='C'
    <if test="clientNo != null and  clientNo != '' ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="nextChargeDate != null ">
      and next_charge_date <![CDATA[<= ]]> #{nextChargeDate,jdbcType=DATE}
    </if>
    <if test="feeTypes != null and feeTypes.length > 0 ">
      AND FEE_TYPE in
      <foreach item="item" index="index" collection="feeTypes" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="selectByBaseAcctNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="java.lang.String" >
    select
    <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE where base_acct_no = #{baseAcctNo} and fee_amt > 0 and
    (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')
    <if test="startDate != null">
      AND TRAN_DATE <![CDATA[ >= ]]> #{startDate}
    </if>
    <if test="endDate != null">
      AND TRAN_DATE <![CDATA[ <= ]]> #{endDate}
    </if>
    <if test="feeType != null and feeType.length() > 0" >
      AND fee_type =#{feeType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByInternalKeyAndFeeType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="java.lang.String" >
    select
    <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE where  fee_amt > 0 and
    (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')
    <if test="nextChargeDate != null">
      AND NEXT_CHARGE_DATE <![CDATA[ = ]]> #{nextChargeDate,jdbcType=DATE}
    </if>
    <if test="internalKey != null">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="feeType != null and feeType.length() > 0" >
      AND FEE_TYPE = #{feeType}
    </if>
    <if test="tranBranch != null and tranBranch.length() > 0" >
      AND TRAN_BRANCH = #{tranBranch}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


<!--  <select id="selectByClientNoByPage" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" >-->
<!--      select-->
<!--      <include refid="Base_Column"/>-->
<!--      from RB_OSD_SERV_CHARGE-->
<!--      where client_no = #{baseParam.clientNo}-->
<!--        and fee_amt > 0-->
<!--        and (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')-->
<!--    <if test="baseParam.endDate != null and baseParam.endDate.length() > 0 and baseParam.startDate != null and baseParam.startDate.length() > 0" >-->
<!--      AND tran_date between #{baseParam.startDate} and #{baseParam.endDate}-->
<!--    </if>-->
<!--    <if test="baseParam.feeType != null and baseParam.feeType.length() > 0" >-->
<!--      AND fee_type =#{baseParam.feeType}-->
<!--    </if>-->
<!--    order by EFFECT_DATE-->
<!--  </select>-->
  <select id="selectArrearByAgreementId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" >
      select
      <include refid="Base_Column"/>
      from RB_OSD_SERV_CHARGE where agreement_id = #{agreementId} and fee_amt > 0 and (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')
    <if test="osdSeqNo != null and osdSeqNo.length() > 0" >
      AND osd_seq_no != #{osdSeqNo}
    </if>
    <if test="feeType != null and feeType.length() > 0" >
      AND fee_type =#{feeType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" >
      select
      <include refid="Base_Column"/>
      from RB_OSD_SERV_CHARGE where reference = #{reference} and (REVERSAL_FLAG is null or REVERSAL_FLAG != 'Y')
      <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
  </select>

  <update id="updateByPrimaryKeyAndClientNo"  >
    UPDATE RB_OSD_SERV_CHARGE
    <set>
      <if test="clientNo != null and  clientNo != '' ">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientType != null and  clientType != '' ">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="internalKey != null ">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="feeType != null and  feeType != '' ">
        FEE_TYPE = #{feeType},
      </if>
      <if test="chargePeriodFreq != null and  chargePeriodFreq != '' ">
        CHARGE_PERIOD_FREQ = #{chargePeriodFreq},
      </if>
      <if test="chargeDay != null and  chargeDay != '' ">
        CHARGE_DAY = #{chargeDay},
      </if>
      <if test="lastChargeDate != null  ">
        LAST_CHARGE_DATE = #{lastChargeDate},
      </if>
      <if test="priority != null and  priority != '' ">
        PRIORITY = #{priority},
      </if>
      <if test="delayFlag != null and  delayFlag != '' ">
        DELAY_FLAG = #{delayFlag},
      </if>
      <if test="effectDate != null  ">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="feeCcy != null and  feeCcy != '' ">
        FEE_CCY = #{feeCcy},
      </if>
      <if test="origFeeAmt != null ">
        ORIG_FEE_AMT = #{origFeeAmt},
      </if>
      <if test="discFeeAmt != null ">
        DISC_FEE_AMT = #{discFeeAmt},
      </if>
      <if test="feeAmt != null ">
        FEE_AMT = #{feeAmt},
      </if>
      <if test="scDiscountType != null and  scDiscountType != '' ">
        SC_DISCOUNT_TYPE = #{scDiscountType},
      </if>
      <if test="scDiscountRate != null ">
        SC_DISCOUNT_RATE = #{scDiscountRate},
      </if>
      <if test="primaryTranSeqNo != null and  primaryTranSeqNo != '' ">
        PRIMARY_TRAN_SEQ_NO = #{primaryTranSeqNo},
      </if>
      <if test="boInd != null and  boInd != '' ">
        BO_IND = #{boInd},
      </if>
      <if test="tranBranch != null and  tranBranch != '' ">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="chargeWay != null and  chargeWay != '' ">
        CHARGE_WAY = #{chargeWay},
      </if>
      <if test="tranDate != null  ">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID = #{userId},
      </if>
      <if test="lastChangeUserId != null and  lastChangeUserId != '' ">
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="lastChangeDate != null  ">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="chargeToInternalKey != null ">
        CHARGE_TO_INTERNAL_KEY = #{chargeToInternalKey},
      </if>
      <if test="chargeToBaseAcctNo != null and  chargeToBaseAcctNo != '' ">
        CHARGE_TO_BASE_ACCT_NO = #{chargeToBaseAcctNo},
      </if>
      <if test="chargeToProdType != null and  chargeToProdType != '' ">
        CHARGE_TO_PROD_TYPE = #{chargeToProdType},
      </if>
      <if test="chargeToCcy != null and  chargeToCcy != '' ">
        CHARGE_TO_CCY = #{chargeToCcy},
      </if>
      <if test="chargeToAcctSeqNo != null and  chargeToAcctSeqNo != '' ">
        CHARGE_TO_ACCT_SEQ_NO = #{chargeToAcctSeqNo},
      </if>
      <if test="docType != null and  docType != '' ">
        DOC_TYPE = #{docType},
      </if>
      <if test="prefix != null and  prefix != '' ">
        PREFIX = #{prefix},
      </if>
      <if test="acctBranch != null and  acctBranch != '' ">
        ACCT_BRANCH = #{acctBranch},
      </if>
      <if test="voucherSum != null and  voucherSum != '' ">
        VOUCHER_SUM = #{voucherSum},
      </if>
      <if test="unitPrice != null ">
        UNIT_PRICE = #{unitPrice},
      </if>
      <if test="voucherStartNo != null and  voucherStartNo != '' ">
        VOUCHER_START_NO = #{voucherStartNo},
      </if>
      <if test="endNo != null and  endNo != '' ">
        END_NO = #{endNo},
      </if>
      <if test="company != null and  company != '' ">
        COMPANY = #{company},
      </if>
      <if test="taxType != null and  taxType != '' ">
        TAX_TYPE = #{taxType},
      </if>
      <if test="taxRate != null ">
        TAX_RATE = #{taxRate},
      </if>
      <if test="taxAmt != null ">
        TAX_AMT = #{taxAmt},
      </if>
      <if test="tranFeeAmt != null ">
        TRAN_FEE_AMT = #{tranFeeAmt},
      </if>
      <if test="reversalFlag != null and  reversalFlag != '' ">
        REVERSAL_FLAG = #{REVERSAL_FLAG},
      </if>
      <if test="reversalDate != null  ">
        REVERSAL_DATE = #{reversalDate},
      </if>
      <if test="reversalBranch != null and  reversalBranch != '' ">
        REVERSAL_BRANCH = #{reversalBranch},
      </if>
      <if test="reversalUserId != null and  reversalUserId != '' ">
        REVERSAL_USER_ID = #{reversalUserId},
      </if>
      <if test="reversalAuthUserId != null and  reversalAuthUserId != '' ">
        REVERSAL_AUTH_USER_ID = #{reversalAuthUserId},
      </if>
      <if test="agreementId != null and  agreementId != '' ">
        AGREEMENT_ID = #{agreementId},
      </if>
      <if test="othBusinessNo != null and  othBusinessNo != '' ">
        OTH_BUSINESS_NO = #{othBusinessNo},
      </if>
      <if test="seqNo != null and  seqNo != '' ">
        SEQ_NO = #{seqNo},
      </if>
      <if test="othName != null and  othName != '' ">
        OTH_NAME = #{othName},
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="openBranchPercent != null ">
        OPEN_BRANCH_PERCENT = #{openBranchPercent},
      </if>
      <if test="tranBranchPercent != null ">
        TRAN_BRANCH_PERCENT = #{tranBranchPercent},
      </if>
      <if test="openProfitAmt != null ">
        OPEN_PROFIT_AMT = #{openProfitAmt},
      </if>
      <if test="osdStatus != null and  osdStatus != '' ">
        OSD_STATUS = #{osdStatus},
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctName != null and  acctName != '' ">
        ACCT_NAME = #{acctName},
      </if>
      <if test="nextChargeDate != null ">
        NEXT_CHARGE_DATE = #{nextChargeDate},
      </if>
    </set>
    <where>
      <trim suffixOverrides="AND">
        <if test="osdSeqNo != null and  osdSeqNo != '' ">
          OSD_SEQ_NO = #{osdSeqNo}  AND
        </if>
        <if test="clientNo != null and clientNo != ''">
           CLIENT_NO = #{clientNo} AND
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          COMPANY = #{company} AND
        </if>
      </trim>
    </where>
  </update>

  <select id="selectByClientNoAndKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="java.util.Map" >
    select
    <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE
    where
    <if test="osdSeqNo != null and  osdSeqNo != '' ">
      OSD_SEQ_NO = #{osdSeqNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getOsdServChargeByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" parameterType="java.util.Map" >
    select
    <include refid="Base_Column"/>
    from RB_OSD_SERV_CHARGE
    where 1=1
    <if test="internalKey != null and  internalKey != '' ">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <if test="feeType != null and  feeType != '' ">
      and FEE_TYPE = #{feeType}
    </if>
    <if test="tranDate != null and  tranDate != '' ">
      and tran_date = #{tranDate}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!-- 根据暂不收费签约 情况删除处理未收费信息-->
  <delete id="deleteOsdServChargeByAgrt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" >
    delete
      from RB_OSD_SERV_CHARGE
     where CLIENT_NO = #{clientNo}
       AND CHARGE_TO_BASE_ACCT_NO =#{chargeToBaseAcctNo}
       AND AGREEMENT_ID = #{agreementId}
       <if test="feeType != null and feeType != '' and feeType!= 'ALL' ">
          AND FEE_TYPE = #{feeType}
       </if>
  </delete>

  <!-- 根据暂不收费签约 情况删除处理未收费信息-->
  <update id="updateOsdServChargeByAgrt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge" >
    update RB_OSD_SERV_CHARGE
    set CHARGE_WAY = #{chargeWay}
      <if test="nextChargeDate != null ">
        , NEXT_CHARGE_DATE = #{nextChargeDate}
      </if>
    where CLIENT_NO = #{clientNo}
    AND CHARGE_TO_BASE_ACCT_NO = #{chargeToBaseAcctNo}
    AND AGREEMENT_ID = #{agreementId}
    <if test="feeType != null and feeType != '' and feeType!= 'ALL' ">
      AND FEE_TYPE = #{feeType}
    </if>
  </update>

  <update id="updateProdByKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge">

    update RB_OSD_SERV_CHARGE
    <set>
      CHARGE_TO_PROD_TYPE = #{newProdType},
    </set>
    <where>
      CHARGE_TO_INTERNAL_KEY = #{internalKey}
      AND CLIENT_NO = #{clientNo}
    </where>
  </update>

</mapper>
