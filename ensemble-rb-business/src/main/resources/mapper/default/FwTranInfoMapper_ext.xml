<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.comet.flow.antirepeate.entity.FwTranInfoPo">

    <update id="updateFwTranInfoPostedFlagByReference" parameterType="java.util.Map">
        UPDATE
        FW_TRAN_INFO
        SET GL_POSTED_FLAG='Y'
        WHERE
        SEQ_NO in
        <foreach collection="seqNos" item="seqNo" index="index" open="(" close=")" separator=",">
            #{seqNo}
        </foreach>
        AND SUB_SEQ_NO in
        <foreach collection="subSeqNos" item="subSeqNo" index="index" open="(" close=")" separator=",">
            #{subSeqNo}
        </foreach>

    </update>

    <select id="selectListByCondition" resultType="com.dcits.comet.flow.antirepeate.entity.FwTranInfoPo"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column"/>
        from FW_TRAN_INFO
        <where>
            <if test="subSeqNo != null and subSeqNo != '' ">
                AND SUB_SEQ_NO = #{subSeqNo}
            </if>
            <if test="seqNo != null and seqNo != '' ">
                AND SEQ_NO = #{seqNo}
            </if>
        </where>

    </select>

    <select id="selectOneByReference" resultType="com.dcits.comet.flow.antirepeate.entity.FwTranInfoPo"
            parameterType="java.util.Map">
        select
        SUB_SEQ_NO,SEQ_NO,reference,BRANCH_ID
        from FW_TRAN_INFO
        <where>
            <if test="reference != null and reference != '' ">
                AND reference = #{reference}
            </if>
            <if test="serviceId != null and serviceId != '' ">
                AND SERVICE_ID != #{serviceId}
            </if>
        </where>

    </select>

</mapper>