<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbErpNatureCfg">

	<select id="getMbErpNatureCfgInfo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbErpNatureCfg">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_ERP_NATURE_CFG
		<where>
			<if test="acctOperateType != null and acctOperateType.length() > 0">
				ACCT_OPERATE_TYPE = #{acctOperateType}
			</if>
			<if test="acctNature != null and acctNature.length() > 0">
				AND ACCT_NATURE = #{acctNature}
			</if>
			<if test="corporation != null and corporation.length() > 0">
				AND CORPORATION = #{corporation}
			</if>
			<if test="inlandOffshore != null and inlandOffshore.length() > 0">
				AND INLAND_OFFSHORE = #{inlandOffshore}
			</if>
			<if test="reasonCode != null and reasonCode.length() > 0">
				AND REASON_CODE = #{reasonCode}
			</if>
			<if test="natureProperty != null and natureProperty.length() > 0">
				AND NATURE_PROPERTY = #{natureProperty}
			</if>
		</where>
	</select>



	<select id="getMbErpNatureCfgbyReasonCodeInfo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbErpNatureCfg">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_ERP_NATURE_CFG
		<where>
			<if test="acctNature != null and acctNature.length() > 0">
				AND ACCT_NATURE = #{acctNature}
			</if>
			AND  REASON_CODE = '1' or REASON_CODE = '2' ;
		</where>
	</select>

	<select id="selectForAddRes" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbErpNatureCfg">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_ERP_NATURE_CFG
		<where>
			<if test="acctOperateType != null and acctOperateType.length() > 0">
				ACCT_OPERATE_TYPE = #{acctOperateType}
			</if>
			<if test="acctNature != null and acctNature.length() > 0">
				AND ACCT_NATURE = #{acctNature}
			</if>
		</where>
	</select>




</mapper>