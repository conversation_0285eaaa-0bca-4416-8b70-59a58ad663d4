<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">

  <select id="getMbPcpSubInfoByClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where CLIENT_NO = #{clientNo}
      AND PCP_GROUP_ID    = #{pcpGroupId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbPcpSubInfos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where
      MIN_SUB_STATUS = 'A'
    <if test="baseAcctNo != null">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null">
      and PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      and ACCT_CCY
      = #{acctCcy}
    </if>
    <if test="acctSeqNo != null">
      and ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="subSeqNo != null">
      and SUB_SEQ_NO = #{subSeqNo}
    </if>
    <if test="internalKey != null">
      and INTERNAL_KEY = #{internalKey}
    </if>
    <if test="priority != null">
      and PRIORITY = #{priority}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbPcpSubInfosByGroupId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where
    MIN_SUB_STATUS = 'A'
    and PCP_GROUP_ID    = #{pcpGroupId}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByIdAndKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where PCP_GROUP_ID = #{pcpGroupId}
    AND INTERNAL_KEY = #{internalKey}
    AND MIN_SUB_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectDeleteByIdAndKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where PCP_GROUP_ID = #{pcpGroupId}
    AND INTERNAL_KEY = #{internalKey}
    AND MIN_SUB_STATUS != 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectEodPcpSubAcctInfos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where MIN_SUB_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY INTERNAL_KEY
  </select>
  <select id="selectByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where
    INTERNAL_KEY = #{internalKey}
    AND PCP_GROUP_ID   = #{pcpGroupId}
    and MIN_SUB_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByNotIdKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where
    INTERNAL_KEY = #{internalKey}
    AND PCP_GROUP_ID != #{pcpGroupId}
    and MIN_SUB_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getSubAcctByUpperInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where
    UPPER_INTERNAL_KEY = #{upperInternalKey}
    and MIN_SUB_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getSubAcctByseqNoAndGroupId" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where
        PCP_GROUP_ID  = #{pcpGroupId}
    and SUB_SEQ_NO = #{subSeqNo}
    and MIN_SUB_STATUS = 'A'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getSubAcctByStatus"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where MIN_SUB_STATUS = 'P'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbPcpSubInfoDetail" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo">
    select
    <include refid="Base_Column"/>
    from RB_PCP_SUB_ACCT_INFO
    where
    MIN_SUB_STATUS = 'A'
    <if test="baseAcctNo != null and baseAcctNo != ''">
      and BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null and prodType != ''">
      and PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null and acctCcy != ''">
      and ACCT_CCY = #{acctCcy}
    </if>
    <if test="acctSeqNo != null and acctSeqNo != ''">
      and ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>
</mapper>
