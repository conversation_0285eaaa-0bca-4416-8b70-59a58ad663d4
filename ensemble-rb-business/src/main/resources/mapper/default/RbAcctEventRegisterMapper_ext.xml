<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Thu Sep 10 09:37:50 CST 2015.
        -->
        select <include refid="Base_Column"/>
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and SEQ_NO = #{seqNo}
    </select>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        update RB_ACCT_EVENT_REGISTER
        <set>
            <if test="intClass != null">
                INT_CLASS = #{intClass},
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO = #{baseAcctNo},
            </if>
            <if test="prodType != null">
                PROD_TYPE = #{prodType},
            </if>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="movtStatus != null">
                MOVT_STATUS = #{movtStatus},
            </if>
            <if test="acctOpenDate != null">
                ACCT_OPEN_DATE = #{acctOpenDate},
            </if>
            <if test="maturityDate != null">
                MATURITY_DATE = #{maturityDate},
            </if>
            <if test="principalAmt != null">
                PRINCIPAL_AMT = #{principalAmt},
            </if>
            <if test="term != null">
                TERM = #{term},
            </if>
            <if test="termType != null">
                TERM_TYPE = #{termType},
            </if>
            <if test="lastCycleDate != null">
                LAST_CYCLE_DATE = #{lastCycleDate},
            </if>
            <if test="acctLevelIntRate != null">
                ACCT_LEVEL_INT_RATE = #{acctLevelIntRate},
            </if>
            <if test="spreadRate != null">
                SPREAD_RATE = #{spreadRate},
            </if>
            <if test="grossInterestAmt != null">
                GROSS_INTEREST_AMT = #{grossInterestAmt},
            </if>
            <if test="taxAmt != null">
                TAX_AMT = #{taxAmt},
            </if>
            <if test="intAdj != null">
                INT_ADJ = #{intAdj},
            </if>
            <if test="intAdjCtd != null">
                INT_ADJ_CTD = #{intAdjCtd},
            </if>
            <if test="netInterestAmt != null">
                NET_INTEREST_AMT = #{netInterestAmt},
            </if>
            <if test="tranAmt != null">
                TRAN_AMT = #{tranAmt},
            </if>
            <if test="seqRenewRolloverNo != null">
                SEQ_RENEW_ROLLOVER_NO = #{seqRenewRolloverNo},
            </if>
            <if test="userId != null">
                USER_ID = #{userId},
            </if>
            <if test="referenceNo != null">
                REFERENCE = #{referenceNo},
            </if>
            <if test="reversalDate != null">
                REVERSAL_DATE = #{reversalDate},
            </if>
            <if test="docType != null">
                DOC_TYPE = #{docType},
            </if>
            <if test="prefix != null">
                PREFIX = #{prefix},
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO = #{voucherNo},
            </if>
            <if test="company != null">
                COMPANY = #{company},
            </if>
            <if test="accountingStatus != null">
                ACCOUNTING_STATUS = #{accountingStatus},
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTER = #{profitCenter},
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE = #{sourceModule},
            </if>
            <if test="businessUnit != null">
                BUSINESS_UNIT = #{businessUnit},
            </if>
            <if test="clientType != null">
                CLIENT_TYPE = #{clientType},
            </if>
            <if test="amtType != null">
                AMT_TYPE = #{amtType},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
            <if test="acctBranch != null">
                ACCT_BRANCH = #{acctBranch},
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="intCapFlag != null">
                INT_CAP_FLAG = #{intCapFlag},
            </if>
            <if test="glPostedFlag != null">
                GL_POSTED_FLAG = #{glPostedFlag},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="taxType != null">
                TAX_TYPE = #{taxType},
            </if>
            <if test="taxRate != null">
                TAX_RATE = #{taxRate},
            </if>
            <if test="debtIntRate != null">
                DEBT_INT_RATE = #{debtIntRate},
            </if>
            <if test="printCnt != null">
                PRINT_CNT = #{printCnt},
            </if>
            <if test="tranStatus != null">
                TRAN_STATUS = #{tranStatus},
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND SEQ_NO = #{seqNo}
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        insert into RB_ACCT_EVENT_REGISTER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="seqNo != null">
                SEQ_NO,
            </if>
            <if test="intClass != null">
                INT_CLASS,
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="movtStatus != null">
                MOVT_STATUS,
            </if>
            <if test="acctOpenDate != null">
                ACCT_OPEN_DATE,
            </if>
            <if test="maturityDate != null">
                MATURITY_DATE,
            </if>
            <if test="principalAmt != null">
                PRINCIPAL_AMT,
            </if>
            <if test="term != null">
                TERM,
            </if>
            <if test="termType != null">
                TERM_TYPE,
            </if>
            <if test="lastCycleDate != null">
                LAST_CYCLE_DATE,
            </if>
            <if test="acctLevelIntRate != null">
                ACCT_LEVEL_INT_RATE,
            </if>
            <if test="spreadRate != null">
                SPREAD_RATE,
            </if>

            <if test="grossInterestAmt != null">
                GROSS_INTEREST_AMT,
            </if>
            <if test="taxAmt != null">
                TAX_AMT,
            </if>
            <if test="intAdj != null">
                INT_ADJ,
            </if>
            <if test="intAdjCtd != null">
                INT_ADJ_CTD,
            </if>
            <if test="netInterestAmt != null">
                NET_INTEREST_AMT,
            </if>
            <if test="tranAmt != null">
                TRAN_AMT,
            </if>
            <if test="seqRenewRolloverNo != null">
                SEQ_RENEW_ROLLOVER_NO,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="reversalDate != null">
                REVERSAL_DATE,
            </if>
            <if test="docType != null">
                DOC_TYPE,
            </if>
            <if test="prefix != null">
                PREFIX,
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="accountingStatus != null">
                ACCOUNTING_STATUS,
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTER,
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE,
            </if>
            <if test="businessUnit != null">
                BUSINESS_UNIT,
            </if>
            <if test="clientType != null">
                CLIENT_TYPE,
            </if>
            <if test="amtType != null">
                AMT_TYPE,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="acctBranch != null">
                ACCT_BRANCH,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="intCapFlag != null">
                INT_CAP_FLAG,
            </if>
            <if test="glPostedFlag != null">
                GL_POSTED_FLAG,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="taxType != null">
                TAX_TYPE,
            </if>
            <if test="taxRate != null">
                TAX_RATE,
            </if>
            <if test="debtIntRate != null">
                DEBT_INT_RATE,
            </if>
            <if test="printCnt != null">
                PRINT_CNT,
            </if>
            <if test="tranStatus != null">
                TRAN_STATUS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="seqNo != null">
                #{seqNo},
            </if>
            <if test="intClass != null">
                #{intClass},
            </if>
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="movtStatus != null">
                #{movtStatus},
            </if>
            <if test="acctOpenDate != null">
                #{acctOpenDate},
            </if>
            <if test="maturityDate != null">
                #{maturityDate},
            </if>
            <if test="principalAmt != null">
                #{principalAmt},
            </if>
            <if test="term != null">
                #{term},
            </if>
            <if test="termType != null">
                #{termType},
            </if>
            <if test="lastCycleDate != null">
                #{lastCycleDate},
            </if>
            <if test="acctLevelIntRate != null">
                #{acctLevelIntRate},
            </if>
            <if test="spreadRate != null">
                #{spreadRate},
            </if>

            <if test="grossInterestAmt != null">
                #{grossInterestAmt},
            </if>
            <if test="taxAmt != null">
                #{taxAmt},
            </if>
            <if test="intAdj != null">
                #{intAdj},
            </if>
            <if test="intAdjCtd != null">
                #{intAdjCtd},
            </if>
            <if test="netInterestAmt != null">
                #{netInterestAmt},
            </if>
            <if test="tranAmt != null">
                #{tranAmt},
            </if>
            <if test="seqRenewRolloverNo != null">
                #{seqRenewRolloverNo},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="reversalDate != null">
                #{reversalDate},
            </if>
            <if test="docType != null">
                #{docType},
            </if>
            <if test="prefix != null">
                #{prefix},
            </if>
            <if test="voucherNo != null">
                #{voucherNo},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="accountingStatus != null">
                #{accountingStatus},
            </if>
            <if test="profitCenter != null">
                #{profitCenter},
            </if>
            <if test="sourceModule != null">
                #{sourceModule},
            </if>
            <if test="businessUnit != null">
                #{businessUnit},
            </if>
            <if test="clientType != null">
                #{clientType},
            </if>
            <if test="amtType != null">
                #{amtType},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="acctBranch != null">
                #{acctBranch},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="intCapFlag != null">
                #{intCapFlag},
            </if>
            <if test="glPostedFlag != null">
                #{glPostedFlag},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="taxType != null">
                #{taxType},
            </if>
            <if test="taxRate != null">
                #{taxRate},
            </if>
            <if test="debtIntRate != null">
                #{debtIntRate},
            </if>
            <if test="printCnt != null">
                #{printCnt},
            </if>
            <if test="tranStatus != null">
                #{tranStatus},
            </if>
        </trim>
    </insert>

    <select id="selectMaxSeqNoByinternalKey" parameterType="java.util.HashMap" resultType="java.lang.String">
        select max(seq_no + 0) seq_no
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getPreDrawedNumByInternalKey" parameterType="java.util.HashMap" resultType="java.math.BigDecimal">
        select count(1) predraw_num
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        and REVERSAL_DATE is null
        and MOVT_STATUS in ('F','P')
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getPartDrawedNumByInternalKey" parameterType="java.util.HashMap" resultType="java.math.BigDecimal">
        select count(1) partdraw_num
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        and REVERSAL_DATE is null
        and MOVT_STATUS in ('P')
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getTranAmtByCycle" parameterType="java.util.HashMap" resultType="java.math.BigDecimal">
        select sum(TRAN_AMT)
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        and TRAN_DATE = #{tranAmt,jdbcType=DATE}
        and MOVT_STATUS in ('I')
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getRegisterByDates" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        select <include refid="Base_Column"/>
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        and TRAN_DATE = #{tranDate,jdbcType=DATE}
        and TRAN_STATUS NOT IN ('X','R')
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getRegisterByDate" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        select <include refid="Base_Column"/>
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        and TRAN_DATE = #{tranDate,jdbcType=DATE}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getEventByRefNoAndClientNo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        select <include refid="Base_Column"/>
        from RB_ACCT_EVENT_REGISTER
        where REFERENCE = #{reference}
        and CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getEventByRefNoAndClientNoInternalKey" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        select <include refid="Base_Column"/>
        from RB_ACCT_EVENT_REGISTER
        where REFERENCE = #{reference}
        and CLIENT_NO = #{clientNo}
        <if test="internalKey != null and  internalKey != '' ">
            and INTERNAL_KEY = #{internalKey}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getEventByRefNoGroupbyIntClass" parameterType="java.util.HashMap"
            resultType="java.util.HashMap">
        SELECT internal_key, int_class, SUM( net_interest_amt ) interest, SUM( tax_amt ) tax_amt
        FROM RB_ACCT_EVENT_REGISTER
        WHERE REFERENCE = #{reference}
        AND INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        GROUP BY internal_key, int_class
    </select>

    <select id="getEventByRefNoAndClientNoGroupbyIntClass" parameterType="java.util.HashMap"
            resultType="java.util.HashMap">
        SELECT internal_key, int_class, SUM( net_interest_amt ) interest, SUM( tax_amt ) tax_amt
        FROM RB_ACCT_EVENT_REGISTER
        WHERE REFERENCE = #{reference}
          AND INTERNAL_KEY = #{internalKey}
          AND CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        GROUP BY internal_key, int_class
    </select>

    <update id="updateByRefNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        update RB_ACCT_EVENT_REGISTER
        <set>
            <if test="reversalDate != null">
                reversal_date = #{reversalDate},
            </if>
        </set>
        where REFERENCE = #{reference}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CLIENT_NO = #{clientNo}
    </update>

    <select id="getAddAmtMaxDate" parameterType="java.util.HashMap" resultType="java.lang.String">
        select max(tran_date) tran_date
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        and REVERSAL_DATE is null
        and MOVT_STATUS in ('A')
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectByInterbalKey" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        select <include refid="Base_Column"/>
        from RB_ACCT_EVENT_REGISTER
        where INTERNAL_KEY = #{internalKey}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and TRAN_DATE = #{tranDate,jdbcType=DATE}
    </select>
<!--    <select id="queryAcctCaptForEod" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"-->
<!--            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">-->
<!--        SELECT <include refid="Base_Column"/>-->
<!--        FROM RB_ACCT_EVENT_REGISTER-->
<!--        WHERE tran_date=#{baseParam.lastRunDate}-->
<!--        AND tran_amt != 0-->
<!--        AND movt_status in ('P','F','C','I','Q','E','G')-->
<!--    </select>-->
    <select id="queryAcctCaptForEodSplit" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT COUNT(1) ROW_COUNT
        FROM RB_ACCT_EVENT_REGISTER
        WHERE tran_date=#{lastRunDate,jdbcType=DATE}
        AND tran_amt != 0
        AND movt_status in ('P','F','C','I','Q','E','G')
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister" >
        update RB_ACCT_EVENT_REGISTER set GL_POSTED_FLAG='Y'   where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="getListByAcct" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT <include refid="Base_Column"/>
        FROM RB_ACCT_EVENT_REGISTER
        WHERE GL_POSTED_FLAG = 'N'
            AND BASE_ACCT_NO = #{cardBaseAcctNo}
            AND PROD_TYPE = #{prodType}
            AND ACCT_CCY = #{acctCcy}
            <if test="acctSeqNo != null and  acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo}
            </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
            order by tran_date ASC
    </select>

    <select id="getByInternalKeyAndTranDate" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT
        <include refid="Base_Column" />
        FROM
        <include refid="Table_Name" />
        <where>
            <trim suffixOverrides="AND">
                <if test="spreadRate != null ">
                    SPREAD_RATE = #{spreadRate}  AND
                </if>
                <if test="intAdj != null ">
                    INT_ADJ = #{intAdj}  AND
                </if>
                <if test="netInterestAmt != null ">
                    NET_INTEREST_AMT = #{netInterestAmt}  AND
                </if>
                <if test="reversalDate != null ">
                    REVERSAL_DATE = #{reversalDate}  AND
                </if>
                <if test="clientType != null and  clientType != '' ">
                    CLIENT_TYPE = #{clientType}  AND
                </if>
                <if test="tranBranch != null and  tranBranch != '' ">
                    TRAN_BRANCH = #{tranBranch}  AND
                </if>
                <if test="movtStatus != null and  movtStatus != '' ">
                    MOVT_STATUS = #{movtStatus}  AND
                </if>
                <if test="taxAmt != null ">
                    TAX_AMT = #{taxAmt}  AND
                </if>
                <if test="acctOpenDate != null ">
                    ACCT_OPEN_DATE = #{acctOpenDate}  AND
                </if>
                <if test="tranAmt != null ">
                    TRAN_AMT = #{tranAmt}  AND
                </if>
                <if test="reference != null and  reference != '' ">
                    REFERENCE = #{reference}  AND
                </if>
                <if test="startDate != null  and endDate != null ">
                    TRAN_DATE between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE} AND
                </if>
                <if test="internalKey != null ">
                    INTERNAL_KEY = #{internalKey}  AND
                </if>
                <if test="clientNo != null and  clientNo != '' ">
                    CLIENT_NO = #{clientNo}  AND
                </if>
                <if test="calcIntAmt != null ">
                    CALC_INT_AMT = #{calcIntAmt}  AND
                </if>
                <if test="maturityDate != null ">
                    MATURITY_DATE = #{maturityDate}  AND
                </if>
                <if test="principalAmt != null ">
                    PRINCIPAL_AMT = #{principalAmt}  AND
                </if>
                <if test="seqRenewRolloverNo != null and  seqRenewRolloverNo != '' ">
                    SEQ_RENEW_ROLLOVER_NO = #{seqRenewRolloverNo}  AND
                </if>
                <if test="company != null and  company != '' ">
                    COMPANY = #{company}  AND
                </if>
                <if test="intCapFlag != null and  intCapFlag != '' ">
                    INT_CAP_FLAG = #{intCapFlag}  AND
                </if>
                <if test="debtIntRate != null ">
                    DEBT_INT_RATE = #{debtIntRate}  AND
                </if>
                <if test="taxRate != null ">
                    TAX_RATE = #{taxRate}  AND
                </if>
                <if test="calcDays != null and  calcDays != '' ">
                    CALC_DAYS = #{calcDays}  AND
                </if>
                <if test="acctLevelIntRate != null ">
                    ACCT_LEVEL_INT_RATE = #{acctLevelIntRate}  AND
                </if>
                <if test="sourceModule != null and  sourceModule != '' ">
                    SOURCE_MODULE = #{sourceModule}  AND
                </if>
                <if test="baseAcctNo != null and  baseAcctNo != '' ">
                    BASE_ACCT_NO = #{baseAcctNo}  AND
                </if>
                <if test="acctCcy != null and  acctCcy != '' ">
                    ACCT_CCY = #{acctCcy}  AND
                </if>
                <if test="floatRate != null ">
                    FLOAT_RATE = #{floatRate}  AND
                </if>
                <if test="term != null and  term != '' ">
                    TERM = #{term}  AND
                </if>
                <if test="userId != null and  userId != '' ">
                    USER_ID = #{userId}  AND
                </if>
                <if test="voucherNo != null and  voucherNo != '' ">
                    VOUCHER_NO = #{voucherNo}  AND
                </if>
                <if test="businessUnit != null and  businessUnit != '' ">
                    BUSINESS_UNIT = #{businessUnit}  AND
                </if>
                <if test="narrative != null and  narrative != '' ">
                    NARRATIVE = #{narrative}  AND
                </if>
                <if test="prodType != null and  prodType != '' ">
                    PROD_TYPE = #{prodType}  AND
                </if>
                <if test="actualRate != null ">
                    ACTUAL_RATE = #{actualRate}  AND
                </if>
                <if test="profitCenter != null and  profitCenter != '' ">
                    PROFIT_CENTER = #{profitCenter}  AND
                </if>
                <if test="acctBranch != null and  acctBranch != '' ">
                    ACCT_BRANCH = #{acctBranch}  AND
                </if>
                <if test="acctSeqNo != null and  acctSeqNo != '' ">
                    ACCT_SEQ_NO = #{acctSeqNo}  AND
                </if>
                <if test="intType != null and  intType != '' ">
                    INT_TYPE = #{intType}  AND
                </if>
                <if test="realRate != null ">
                    REAL_RATE = #{realRate}  AND
                </if>
                <if test="docType != null and  docType != '' ">
                    DOC_TYPE = #{docType}  AND
                </if>
                <if test="seqNo != null and  seqNo != '' ">
                    SEQ_NO = #{seqNo}  AND
                </if>
                <if test="intClass != null and  intClass != '' ">
                    INT_CLASS = #{intClass}  AND
                </if>
                <if test="taxType != null and  taxType != '' ">
                    TAX_TYPE = #{taxType}  AND
                </if>
                <if test="amtType != null and  amtType != '' ">
                    AMT_TYPE = #{amtType}  AND
                </if>
                <if test="termType != null and  termType != '' ">
                    TERM_TYPE = #{termType}  AND
                </if>
                <if test="lastCycleDate != null ">
                    LAST_CYCLE_DATE = #{lastCycleDate}  AND
                </if>

                <if test="grossInterestAmt != null ">
                    GROSS_INTEREST_AMT = #{grossInterestAmt}  AND
                </if>
                <if test="intAdjCtd != null ">
                    INT_ADJ_CTD = #{intAdjCtd}  AND
                </if>
                <if test="prefix != null and  prefix != '' ">
                    PREFIX = #{prefix}  AND
                </if>
                <if test="accountingStatus != null and  accountingStatus != '' ">
                    ACCOUNTING_STATUS = #{accountingStatus}  AND
                </if>
                <if test="tranTimestamp != null ">
                    TRAN_TIMESTAMP = #{tranTimestamp}  AND
                </if>
                <if test="glPostedFlag != null and  glPostedFlag != '' ">
                    GL_POSTED_FLAG = #{glPostedFlag}  AND
                </if>
                <if test="printCnt != null and  printCnt != '' ">
                    PRINT_CNT = #{printCnt}  AND
                </if>
                <if test="tranStatus != null and  tranStatus != '' ">
                    TRAN_STATUS = #{tranStatus}  AND
                </if>
            </trim>
        </where>
    </select>
    <select id="getByInternalKeyAndTranDateInt" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT
        ACCT_OPEN_DATE,
        sum(GROSS_INTEREST_AMT) as GROSS_INTEREST_AMT,
        REFERENCE,
        TRAN_DATE,
        INTERNAL_KEY,
        CLIENT_NO,
        BASE_ACCT_NO,
        ACCT_CCY,
        ACCT_SEQ_NO,
        LAST_CYCLE_DATE,
        PRINT_CNT,
        MOVT_STATUS
        FROM
        RB_ACCT_EVENT_REGISTER
        <where>
             <if test="startDate != null  and endDate != null ">
                AND   TRAN_DATE between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
             </if>
             <if test="internalKey != null ">
                AND  INTERNAL_KEY = #{internalKey}
             </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND  CLIENT_NO = #{clientNo}
             </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
        group by ACCT_OPEN_DATE,
        REFERENCE,
        TRAN_DATE,
        INTERNAL_KEY,
        CLIENT_NO,
        BASE_ACCT_NO,
        ACCT_CCY,
        ACCT_SEQ_NO,
        LAST_CYCLE_DATE,
        PRINT_CNT,
        MOVT_STATUS
        order by TRAN_DATE asc
    </select>
    <select id="getByInternalKeyAndTranDateIntN" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_ACCT_EVENT_REGISTER
        <where>
        <if test="startDate != null  and endDate != null ">
            AND   TRAN_DATE between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        </if>
        <if test="internalKey != null ">
            AND  INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo != null and  clientNo != '' ">
            AND  CLIENT_NO = #{clientNo}
        </if>
        <if test="reference != null and  reference != '' ">
            AND  REFERENCE = #{reference}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
        order by TRAN_DATE, INT_CLASS, TRAN_TIMESTAMP
    </select>
    <update id="updateByReference" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        update RB_ACCT_EVENT_REGISTER
        <set>
          print_cnt = #{printCnt}
        </set>
        where REFERENCE = #{reference}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CLIENT_NO = #{clientNo}
    </update>

    <select id="getRegisterByReference" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT
        *
        FROM
        RB_ACCT_EVENT_REGISTER
        <where>
        <if test="startDate != null  and endDate != null ">
            AND   TRAN_DATE between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        </if>
        <if test="reference != null ">
            AND  REFERENCE = #{reference}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>
    
    <select id="selectByInternalAndCalcTime" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT
        *
        FROM RB_ACCT_EVENT_REGISTER
        <where>
        <if test="beginStartDate != null  and endStartDate != null ">
            AND   ACCT_OPEN_DATE between #{beginStartDate} and #{endStartDate}
        </if>
        <if test="internalKey != null ">
            AND  INTERNAL_KEY = #{internalKey}
        </if>
        <!--多法人改造 by LIYUANV-->
        <if test="beginEndDate != null and finalEndDate != null">
            AND MATURITY_DATE between #{beginEndDate} and #{finalEndDate}
        </if>
        </where>

    </select>

    <select id="getRbAcctEventRegister" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT
        <include refid="Base_Column"/>
        FROM
        RB_ACCT_EVENT_REGISTER
        <where>
        <if test="startDate != null  and endDate != null ">
            AND   TRAN_DATE between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        </if>
        <if test="internalKey != null and internalKey !='' ">
            AND  INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo != null and  clientNo != '' ">
            AND  CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
        order by TRAN_DATE, INT_CLASS, TRAN_TIMESTAMP
    </select>
    <select id="getRegisterByInternalKey" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        SELECT
        *
        FROM
        RB_ACCT_EVENT_REGISTER
        <where>
        <if test="internalKey != null and internalKey != '' ">
            AND  INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo != null and clientNo != '' ">
            AND  CLIENT_NO = #{clientNo}
        </if>
        </where>
    </select>

    <update id="updateTranStatusByReference" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
        UPDATE RB_ACCT_EVENT_REGISTER
        SET TRAN_STATUS = #{tranStatus}
        <where>
        <if test="internalKey != null and internalKey != '' ">
            AND  INTERNAL_KEY = #{internalKey}
        </if>
        <if test="reference != null and reference != '' ">
            AND  reference = #{reference}
        </if>
        <if test="clientNo != null and clientNo != '' ">
            AND  client_no = #{clientNo}
        </if>
        </where>
    </update>
</mapper>
