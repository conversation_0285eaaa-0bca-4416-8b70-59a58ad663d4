<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    where SC_SEQ_NO = #{scSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    delete from RB_SERV_CHARGE_HIST
    where SC_SEQ_NO = #{scSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    insert into RB_SERV_CHARGE_HIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scSeqNo != null">
        SC_SEQ_NO,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientType != null">
        CLIENT_TYPE,
      </if>
      <if test="feeType != null">
        FEE_TYPE,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="feeCcy != null">
        FEE_CCY,
      </if>
      <if test="origFeeAmt != null">
        ORIG_FEE_AMT,
      </if>
      <if test="discFeeAmt != null">
        DISC_FEE_AMT,
      </if>
      <if test="feeAmt != null">
        FEE_AMT,
      </if>
      <if test="scDiscountType != null">
        SC_DISCOUNT_TYPE,
      </if>
      <if test="scDiscountRate != null">
        SC_DISCOUNT_RATE,
      </if>
      <if test="boInd != null">
        BO_IND,
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="chargeToInternalKey != null">
        CHARGE_TO_INTERNAL_KEY,
      </if>
      <if test="chargeToBaseAcctNo != null">
        CHARGE_TO_BASE_ACCT_NO,
      </if>
      <if test="chargeToProdType != null">
        CHARGE_TO_PROD_TYPE,
      </if>
      <if test="chargeToCcy != null">
        CHARGE_TO_CCY,
      </if>
      <if test="chargeToAcctSeqNo != null">
        CHARGE_TO_ACCT_SEQ_NO,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="prefix != null">
        PREFIX,
      </if>
      <if test="voucherSum != null">
        VOUCHER_SUM,
      </if>
      <if test="unitPrice != null">
        UNIT_PRICE,
      </if>
      <if test="voucherStartNo != null">
        VOUCHER_START_NO,
      </if>
      <if test="endNo != null">
        END_NO,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="taxType != null">
        TAX_TYPE,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="taxAmt != null">
        TAX_AMT,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="tranFeeAmt != null">
        TRAN_FEE_AMT,
      </if>
      <if test="osdSeqNo != null">
        OSD_SEQ_NO,
      </if>
      <if test="reversalFlag != null">
        REVERSAL_FLAG,
      </if>
      <if test="reversalBranch != null">
        REVERSAL_BRANCH,
      </if>
      <if test="reversalUserId != null">
        REVERSAL_USER_ID,
      </if>
      <if test="reversalAuthUserId != null">
        REVERSAL_AUTH_USER_ID,
      </if>
      <if test="reversalScSeqNo != null">
        REVERSAL_SC_SEQ_NO,
      </if>
      <if test="agreementId != null">
        AGREEMENT_ID,
      </if>
      <if test="othBusinessNo != null">
        OTH_BUSINESS_NO,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="othName != null">
        OTH_NAME,
      </if>
      <if test="openBranchPercent != null">
        OPEN_BRANCH_PERCENT,
      </if>
      <if test="tranBranchPercent != null">
        TRAN_BRANCH_PERCENT,
      </if>
      <if test="openProfitAmt != null">
        OPEN_PROFIT_AMT,
      </if>
      <if test="acctBranch != null">
        ACCT_BRANCH,
      </if>
      <if test="amortizePeriodType != null">
        AMORTIZE_PERIOD_TYPE,
      </if>
      <if test="amortizeTimeType != null">
        AMORTIZE_TIME_TYPE,
      </if>
      <if test="amortizeMonth != null">
        AMORTIZE_MONTH,
      </if>
      <if test="amortizeDay != null">
        AMORTIZE_DAY,
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scSeqNo != null">
        #{scSeqNo},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientType != null">
        #{clientType},
      </if>
      <if test="feeType != null">
        #{feeType},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="feeCcy != null">
        #{feeCcy},
      </if>
      <if test="origFeeAmt != null">
        #{origFeeAmt},
      </if>
      <if test="discFeeAmt != null">
        #{discFeeAmt},
      </if>
      <if test="feeAmt != null">
        #{feeAmt},
      </if>
      <if test="scDiscountType != null">
        #{scDiscountType},
      </if>
      <if test="scDiscountRate != null">
        #{scDiscountRate},
      </if>
      <if test="primaryTranSeqNo != null">
        #{primaryTranSeqNo},
      </if>
      <if test="boInd != null">
        #{boInd},
      </if>
      <if test="tranSeqNo != null">
        #{tranSeqNo},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="chargeWay != null">
        #{chargeWay},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="chargeToInternalKey != null">
        #{chargeToInternalKey},
      </if>
      <if test="chargeToBaseAcctNo != null">
        #{chargeToBaseAcctNo},
      </if>
      <if test="chargeToProdType != null">
        #{chargeToProdType},
      </if>
      <if test="chargeToCcy != null">
        #{chargeToCcy},
      </if>
      <if test="chargeToAcctSeqNo != null">
        #{chargeToAcctSeqNo},
      </if>
      <if test="docType != null">
        #{docType},
      </if>
      <if test="prefix != null">
        #{prefix},
      </if>
      <if test="voucherSum != null">
        #{voucherSum},
      </if>
      <if test="unitPrice != null">
        #{unitPrice},
      </if>
      <if test="voucherStartNo != null">
        #{voucherStartNo},
      </if>
      <if test="endNo != null">
        #{endNo},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="taxType != null">
        #{taxType},
      </if>
      <if test="taxRate != null">
        #{taxRate},
      </if>
      <if test="taxAmt != null">
        #{taxAmt},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="bankSeqNo != null">
        #{bankSeqNo},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="tranFeeAmt != null">
        #{tranFeeAmt},
      </if>
      <if test="osdSeqNo != null">
        #{osdSeqNo},
      </if>
      <if test="reversalFlag != null">
        #{reversalFlag},
      </if>
      <if test="reversalDate != null">
        #{reversalDate},
      </if>
      <if test="reversalBranch != null">
        #{reversalBranch},
      </if>
      <if test="reversalUserId != null">
        #{reversalUserId},
      </if>
      <if test="reversalAuthUserId != null">
        #{reversalAuthUserId},
      </if>
      <if test="reversalScSeqNo != null">
        #{reversalScSeqNo},
      </if>
      <if test="agreementId != null">
        #{agreementId},
      </if>
      <if test="othBusinessNo != null">
        #{othBusinessNo},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="othName != null">
        #{othName},
      </if>
      <if test="profitAllotFlag != null">
        #{profitAllotFlag},
      </if>
      <if test="openBranchPercent != null">
        #{openBranchPercent},
      </if>
      <if test="tranBranchPercent != null">
        #{tranBranchPercent},
      </if>
      <if test="openProfitAmt != null">
        #{openProfitAmt},
      </if>
      <if test="tranProfitAmt != null">
        #{tranProfitAmt},
      </if>
      <if test="acctBranch != null">
        #{acctBranch},
      </if>
      <if test="profitAmortizeFlag != null">
        #{profitAmortizeFlag},
      </if>
      <if test="amortizePeriodType != null">
        #{amortizePeriodType},
      </if>
      <if test="amortizeTimeType != null">
        #{amortizeTimeType},
      </if>
      <if test="amortizeMonth != null">
        #{amortizeMonth},
      </if>
      <if test="amortizeDay != null">
        #{amortizeDay},
      </if>
      <if test="glPostedFlag != null">
        #{glPostedFlag},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    update RB_SERV_CHARGE_HIST
    <set>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientType != null">
        CLIENT_TYPE = #{clientType},
      </if>
      <if test="feeType != null">
        FEE_TYPE = #{feeType},
      </if>
      <if test="effectDate != null">
        EFFECT_DATE = #{effectDate},
      </if>
      <if test="feeCcy != null">
        FEE_CCY = #{feeCcy},
      </if>
      <if test="origFeeAmt != null">
        ORIG_FEE_AMT = #{origFeeAmt},
      </if>
      <if test="discFeeAmt != null">
        DISC_FEE_AMT = #{discFeeAmt},
      </if>
      <if test="feeAmt != null">
        FEE_AMT = #{feeAmt},
      </if>
      <if test="scDiscountType != null">
        SC_DISCOUNT_TYPE = #{scDiscountType},
      </if>
      <if test="scDiscountRate != null">
        SC_DISCOUNT_RATE = #{scDiscountRate},
      </if>
      <if test="boInd != null">
        BO_IND = #{boInd},
      </if>
      <if test="tranSeqNo != null">
        TRAN_SEQ_NO = #{tranSeqNo},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY = #{chargeWay},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="chargeToInternalKey != null">
        CHARGE_TO_INTERNAL_KEY = #{chargeToInternalKey},
      </if>
      <if test="chargeToBaseAcctNo != null">
        CHARGE_TO_BASE_ACCT_NO = #{chargeToBaseAcctNo},
      </if>
      <if test="chargeToProdType != null">
        CHARGE_TO_PROD_TYPE = #{chargeToProdType},
      </if>
      <if test="chargeToCcy != null">
        CHARGE_TO_CCY = #{chargeToCcy},
      </if>
      <if test="chargeToAcctSeqNo != null">
        CHARGE_TO_ACCT_SEQ_NO = #{chargeToAcctSeqNo},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType},
      </if>
      <if test="prefix != null">
        PREFIX = #{prefix},
      </if>
      <if test="voucherSum != null">
        VOUCHER_SUM = #{voucherSum},
      </if>
      <if test="unitPrice != null">
        UNIT_PRICE = #{unitPrice},
      </if>
      <if test="voucherStartNo != null">
        VOUCHER_START_NO = #{voucherStartNo},
      </if>
      <if test="endNo != null">
        END_NO = #{endNo},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="taxType != null">
        TAX_TYPE = #{taxType},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate},
      </if>
      <if test="taxAmt != null">
        TAX_AMT = #{taxAmt},
      </if>
      <if test="tranType != null">
        TRAN_TYPE = #{tranType},
      </if>
      <if test="bankSeqNo != null">
        BANK_SEQ_NO = #{bankSeqNo},
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO = #{channelSeqNo},
      </if>
      <if test="tranFeeAmt != null">
        TRAN_FEE_AMT = #{tranFeeAmt},
      </if>
      <if test="osdSeqNo != null">
        OSD_SEQ_NO = #{osdSeqNo},
      </if>
      <if test="reversalFlag != null">
        REVERSAL_FLAG = #{reversalFlag},
      </if>
      <if test="reversalBranch != null">
        REVERSAL_BRANCH = #{reversalBranch},
      </if>
      <if test="reversalUserId != null">
        REVERSAL_USER_ID = #{reversalUserId},
      </if>
      <if test="reversalAuthUserId != null">
        REVERSAL_AUTH_USER_ID = #{reversalAuthUserId},
      </if>
      <if test="reversalScSeqNo != null">
        REVERSAL_SC_SEQ_NO = #{reversalScSeqNo},
      </if>
      <if test="agreementId != null">
        AGREEMENT_ID = #{agreementId},
      </if>
      <if test="othBusinessNo != null">
        OTH_BUSINESS_NO = #{othBusinessNo},
      </if>
      <if test="seqNo != null">
        SEQ_NO = #{seqNo},
      </if>
      <if test="othName != null">
        OTH_NAME = #{othName},
      </if>
      <if test="openBranchPercent != null">
        OPEN_BRANCH_PERCENT = #{openBranchPercent},
      </if>
      <if test="tranBranchPercent != null">
        TRAN_BRANCH_PERCENT = #{tranBranchPercent},
      </if>
      <if test="acctBranch != null">
        ACCT_BRANCH = #{acctBranch},
      </if>
      <if test="amortizePeriodType != null">
        AMORTIZE_PERIOD_TYPE = #{amortizePeriodType},
      </if>
      <if test="amortizeTimeType != null">
        AMORTIZE_TIME_TYPE = #{amortizeTimeType},
      </if>
      <if test="amortizeMonth != null">
        AMORTIZE_MONTH = #{amortizeMonth},
      </if>
      <if test="amortizeDay != null">
        AMORTIZE_DAY = #{amortizeDay},
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG = #{glPostedFlag},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
    </set>
    where SC_SEQ_NO = #{scSeqNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="selectMbServChargeHistSplitOB" parameterType="java.util.Map" resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      MIN(SC_SEQ_NO) START_KEY,
      MAX(SC_SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
      FROM
      (
      SELECT
      SC_SEQ_NO,
      @rownum :=@rownum + 1 AS rownum
      FROM (SELECT
      DISTINCT SC_SEQ_NO FROM RB_SERV_CHARGE_HIST,
      (SELECT @rownum := -1) t
      where tran_date =  #{runDate}
      AND FEE_AMT!=0
      AND (GL_POSTED_FLAG = 'N' or GL_POSTED_FLAG is null))t1
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      ORDER BY SC_SEQ_NO+0
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT MIN (SC_SEQ_NO) START_KEY, MAX (SC_SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
      FROM (SELECT DISTINCT SC_SEQ_NO
      from RB_SERV_CHARGE_HIST
      where tran_date =  #{runDate,jdbcType=DATE}
      AND FEE_AMT!=0
      AND (GL_POSTED_FLAG = 'N' or GL_POSTED_FLAG is null)
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      ORDER BY SC_SEQ_NO+0)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>
  <select id="selectByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST where reference = #{reference}
    and (REVERSAL_FLAG IS NULL OR REVERSAL_FLAG != 'Y')
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByReferenceExtract" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST where reference = #{reference}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByCondition" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    where (REVERSAL_FLAG IS NULL OR REVERSAL_FLAG != 'Y') and reversal_sc_seq_no is null
    <if test="zeroAmtFlag != null and zeroAmtFlag == 0 ">
      and FEE_AMT != 0
    </if>
    <if test="scSeqNo != null and scSeqNo !=''">
      and sc_seq_no = #{scSeqNo}
    </if>
    <if test="internalKey != null">
      and internal_key = #{internalKey}
    </if>
    <if test="clientNo != null and clientNo !=''">
      and client_no = #{clientNo}
    </if>
    <if test="clientType != null and clientType !=''">
      and client_type = #{clientType}
    </if>
    <if test="feeType != null and feeType !=''">
      and fee_type = #{feeType}
    </if>
    <if test="effectDate != null and effectDate !=''">
      and effect_date = #{effectDate}
    </if>
    <if test="feeCcy != null and feeCcy !=''">
      and fee_ccy = #{feeCcy}
    </if>
    <if test="origFeeAmt != null">
      and orig_fee_amt = #{origFeeAmt}
    </if>
    <if test="discFeeAmt != null">
      and disc_fee_amt = #{discFeeAmt}
    </if>
    <if test="feeAmt != null">
      and fee_amt = #{feeAmt}
    </if>
    <if test="scDiscountType != null and scDiscountType !=''">
      and sc_discount_type = #{scDiscountType}
    </if>
    <if test="scDiscountRate != null">
      and sc_discount_rate = #{scDiscountRate}
    </if>
    <if test="boInd != null and boInd !=''">
      and bo_ind = #{boInd}
    </if>
    <if test="tranSeqNo != null and tranSeqNo !=''">
      and tran_seq_no = #{tranSeqNo}
    </if>
    <if test="tranBranch != null and tranBranch !=''">
      and tran_branch = #{tranBranch}
    </if>
    <if test="reference != null and reference !=''">
      and reference = #{reference}
    </if>
    <if test="chargeWay != null and chargeWay !=''">
      and charge_way = #{chargeWay}
    </if>
    <if test="tranDate != null and tranDate !=''">
      and tran_date = #{tranDate,jdbcType=DATE}
    </if>
    <if test="userId != null and userId !=''">
      and user_id = #{userId}
    </if>
    <if test="chargeToInternalKey != null">
      and charge_to_internal_key = #{chargeToInternalKey}
    </if>
    <if test="chargeToBaseAcctNo != null and chargeToBaseAcctNo !=''">
      and charge_to_base_acct_no = #{chargeToBaseAcctNo}
    </if>
    <if test="chargeToProdType != null and chargeToProdType !=''">
      and charge_to_prod_type = #{chargeToProdType}
    </if>
    <if test="chargeToCcy != null and chargeToCcy !=''">
      and charge_to_ccy = #{chargeToCcy}
    </if>
    <if test="chargeToAcctSeqNo != null and chargeToAcctSeqNo !=''">
      and charge_to_acct_seq_no = #{chargeToAcctSeqNo}
    </if>
    <if test="docType != null and docType !=''">
      and doc_type = #{docType}
    </if>
    <if test="prefix != null and prefix !=''">
      and prefix = #{prefix}
    </if>
    <if test="voucherSum != null and voucherSum !=''">
      and voucher_sum = #{voucherSum}
    </if>
    <if test="unitPrice != null">
      and unit_price = #{unitPrice}
    </if>
    <if test="voucherStartNo != null and voucherStartNo !=''">
      and VOUCHER_START_NO = #{voucherStartNo}
    </if>
    <if test="endNo != null and endNo !=''">
      and end_no = #{endNo}
    </if>
    <if test="company != null and company !=''">
      and company = #{company}
    </if>
    <if test="taxType != null and taxType !=''">
      and tax_type = #{taxType}
    </if>
    <if test="taxRate != null">
      and tax_rate = #{taxRate}
    </if>
    <if test="taxAmt != null">
      and tax_amt = #{taxAmt}
    </if>
    <if test="tranType != null and tranType !=''">
      and tran_type = #{tranType}
    </if>
    <if test="bankSeqNo != null and bankSeqNo !=''">
      and bank_seq_no = #{bankSeqNo}
    </if>
    <if test="channelSeqNo != null and channelSeqNo !=''">
      and channel_seq_no = #{channelSeqNo}
    </if>
    <if test="tranFeeAmt != null and tranFeeAmt !=''">
      and tran_fee_amt = #{tranFeeAmt}
    </if>
    <if test="osdSeqNo != null and osdSeqNo !=''">
      and osd_seq_no = #{osdSeqNo}
    </if>
    <if test="reversalBranch != null and reversalBranch !=''">
      and reversal_branch = #{reversalBranch}
    </if>
    <if test="reversalUserId != null and reversalUserId !=''">
      and reversal_user_id = #{reversalUserId}
    </if>
    <if test="reversalAuthUserId != null and reversalAuthUserId !=''">
      and reversal_auth_user_id = #{reversalAuthUserId}
    </if>
    <if test="reversalScSeqNo != null and reversalScSeqNo !=''">
      and reversal_sc_seq_no = #{reversalScSeqNo}
    </if>
    <if test="agreementId != null and agreementId !=''">
      and agreement_id = #{agreementId}
    </if>
    <if test="othName != null and agreementId !=''">
      and othName = #{othName}
    </if>
    <if test="feeChargeMethod != null and  feeChargeMethod != '' ">
      AND FEE_CHARGE_METHOD = #{feeChargeMethod}
    </if>
    <if test="chargePayFlag != null and  chargePayFlag != '' ">
      AND CHARGE_PAY_FLAG = #{chargePayFlag}
    </if>
    <if test="startDate != null">
      and tran_date <![CDATA[>= ]]> #{startDate}
    </if>
    <if test="endDate != null">
      and tran_date <![CDATA[<= ]]> #{endDate}
    </if>
    <if test="inTranBranches != null">
      AND TRAN_BRANCH IN
      <foreach collection="inTranBranches" item="tranBranch" index="index" open="(" separator="," close=")">
        #{tranBranch}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by sc_seq_no desc
  </select>
  <select id="selectByConditionFree" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    where (REVERSAL_FLAG IS NULL OR REVERSAL_FLAG != 'Y') and reversal_sc_seq_no is null and ORIG_FEE_AMT = DISC_FEE_AMT
    <if test="scSeqNo != null and scSeqNo !=''">
      and sc_seq_no = #{scSeqNo}
    </if>
    <if test="internalKey != null">
      and internal_key = #{internalKey}
    </if>
    <if test="clientNo != null and clientNo !=''">
      and client_no = #{clientNo}
    </if>
    <if test="clientType != null and clientType !=''">
      and client_type = #{clientType}
    </if>
    <if test="feeType != null and feeType !=''">
      and fee_type = #{feeType}
    </if>
    <if test="effectDate != null and effectDate !=''">
      and effect_date = #{effectDate}
    </if>
    <if test="feeCcy != null and feeCcy !=''">
      and fee_ccy = #{feeCcy}
    </if>
    <if test="origFeeAmt != null">
      and orig_fee_amt = #{origFeeAmt}
    </if>
    <if test="discFeeAmt != null">
      and disc_fee_amt = #{discFeeAmt}
    </if>
    <if test="feeAmt != null">
      and fee_amt = #{feeAmt}
    </if>
    <if test="scDiscountType != null and scDiscountType !=''">
      and sc_discount_type = #{scDiscountType}
    </if>
    <if test="scDiscountRate != null">
      and sc_discount_rate = #{scDiscountRate}
    </if>
    <if test="boInd != null and boInd !=''">
      and bo_ind = #{boInd}
    </if>
    <if test="tranSeqNo != null and tranSeqNo !=''">
      and tran_seq_no = #{tranSeqNo}
    </if>
    <if test="tranBranch != null and tranBranch !=''">
      and tran_branch = #{tranBranch}
    </if>
    <if test="reference != null and reference !=''">
      and reference = #{reference}
    </if>
    <if test="chargeWay != null and chargeWay !=''">
      and charge_way = #{chargeWay}
    </if>
    <if test="tranDate != null and tranDate !=''">
      and tran_date = #{tranDate,jdbcType=DATE}
    </if>
    <if test="userId != null and userId !=''">
      and user_id = #{userId}
    </if>
    <if test="chargeToInternalKey != null">
      and charge_to_internal_key = #{chargeToInternalKey}
    </if>
    <if test="chargeToBaseAcctNo != null and chargeToBaseAcctNo !=''">
      and charge_to_base_acct_no = #{chargeToBaseAcctNo}
    </if>
    <if test="chargeToProdType != null and chargeToProdType !=''">
      and charge_to_prod_type = #{chargeToProdType}
    </if>
    <if test="chargeToCcy != null and chargeToCcy !=''">
      and charge_to_ccy = #{chargeToCcy}
    </if>
    <if test="chargeToAcctSeqNo != null and chargeToAcctSeqNo !=''">
      and charge_to_acct_seq_no = #{chargeToAcctSeqNo}
    </if>
    <if test="docType != null and docType !=''">
      and doc_type = #{docType}
    </if>
    <if test="prefix != null and prefix !=''">
      and prefix = #{prefix}
    </if>
    <if test="voucherSum != null and voucherSum !=''">
      and voucher_sum = #{voucherSum}
    </if>
    <if test="unitPrice != null">
      and unit_price = #{unitPrice}
    </if>
    <if test="voucherStartNo != null and voucherStartNo !=''">
      and VOUCHER_START_NO = #{voucherStartNo}
    </if>
    <if test="endNo != null and endNo !=''">
      and end_no = #{endNo}
    </if>
    <if test="company != null and company !=''">
      and company = #{company}
    </if>
    <if test="taxType != null and taxType !=''">
      and tax_type = #{taxType}
    </if>
    <if test="taxRate != null">
      and tax_rate = #{taxRate}
    </if>
    <if test="taxAmt != null">
      and tax_amt = #{taxAmt}
    </if>
    <if test="tranType != null and tranType !=''">
      and tran_type = #{tranType}
    </if>
    <if test="bankSeqNo != null and bankSeqNo !=''">
      and bank_seq_no = #{bankSeqNo}
    </if>
    <if test="channelSeqNo != null and channelSeqNo !=''">
      and channel_seq_no = #{channelSeqNo}
    </if>
    <if test="tranFeeAmt != null and tranFeeAmt !=''">
      and tran_fee_amt = #{tranFeeAmt}
    </if>
    <if test="osdSeqNo != null and osdSeqNo !=''">
      and osd_seq_no = #{osdSeqNo}
    </if>
    <if test="reversalBranch != null and reversalBranch !=''">
      and reversal_branch = #{reversalBranch}
    </if>
    <if test="reversalUserId != null and reversalUserId !=''">
      and reversal_user_id = #{reversalUserId}
    </if>
    <if test="reversalAuthUserId != null and reversalAuthUserId !=''">
      and reversal_auth_user_id = #{reversalAuthUserId}
    </if>
    <if test="reversalScSeqNo != null and reversalScSeqNo !=''">
      and reversal_sc_seq_no = #{reversalScSeqNo}
    </if>
    <if test="agreementId != null and agreementId !=''">
      and agreement_id = #{agreementId}
    </if>
    <if test="othName != null and agreementId !=''">
      and othName = #{othName}
    </if>
    <if test="feeChargeMethod != null and  feeChargeMethod != '' ">
      AND FEE_CHARGE_METHOD = #{feeChargeMethod}
    </if>
    <if test="chargePayFlag != null and  chargePayFlag != '' ">
      AND CHARGE_PAY_FLAG = #{chargePayFlag}
    </if>
    <if test="startDate != null">
      and tran_date <![CDATA[>= ]]> #{startDate}
    </if>
    <if test="endDate != null">
      and tran_date <![CDATA[<= ]]> #{endDate}
    </if>
    <if test="inTranBranches != null">
      AND TRAN_BRANCH IN
      <foreach collection="inTranBranches" item="tranBranch" index="index" open="(" separator="," close=")">
        #{tranBranch}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by sc_seq_no desc
  </select>
  <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    update RB_SERV_CHARGE_HIST set GL_POSTED_FLAG='Y' where SC_SEQ_NO = #{scSeqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="getRbServChargEHistReversalWipe" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    SELECT <include refid="Base_Column"/>
    FROM  RB_SERV_CHARGE_HIST
    WHERE  REVERSAL_FLAG = 'Y'
    <if test="chargeToBaseAcctNo != null and  chargeToBaseAcctNo != '' ">
      AND CHARGE_TO_BASE_ACCT_NO = #{chargeToBaseAcctNo}
    </if>
    <if test="userId != null and  userId != '' ">
      AND USER_ID = #{userId}
    </if>
    <if test="feeCcy != null and  feeCcy != '' ">
      AND FEE_CCY = #{feeCcy}
    </if>
    <if test="branchList != null and branchList.size() > 0 ">
      AND TRAN_BRANCH IN
      <foreach collection="branchList" item="tranBranch" open="(" separator="," close=")">
        #{tranBranch}
      </foreach>
    </if>
    <if test="startDate !=  null">
      AND  <![CDATA[ TRAN_DATE >= #{startDate}]]>
    </if>
    <if test="endDate !=  null">
      AND  <![CDATA[ TRAN_DATE <= #{endDate}]]>
    </if>

    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- It is used for special transaction inquiry, and you can also check those that have been corrected -->
  <select id="selectByReferenceSpecial" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST where reference = #{reference}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByClientTypeFeetype" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    WHERE  TRAN_DATE BETWEEN #{startDate} and #{endDate}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="baseAcctNo != null and baseAcctNo != ''">
      and CHARGE_TO_BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="clientType != null and clientType != '' and clientType == '100' ">
      and (FEE_TYPE  = 'C0025' OR FEE_TYPE = 'C0053')
    </if>
    <if test="clientType != null and clientType != '' and clientType == '200' ">
      and FEE_TYPE  = 'C0029'
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>

  <select id="selectRbServChargeHistByTranDate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    where   TRAN_DATE=#{tranDate}
    and  AMORTIZE_STATUS is not null
    and
    <![CDATA[(GL_POSTED_FLAG IS NULL OR GL_POSTED_FLAG='N')]]>
  </select>

  <select id="selectByAgreementId" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    where agreement_Id in
    <foreach collection="agreementIdList" item="agreementId" index="index"  open="(" close=")" separator=",">
      #{agreementId}
    </foreach>
  </select>

  <update id="updateByChange" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    update RB_SERV_CHARGE_HIST
    <set>
      <if test="amortizeStatus != null">
        AMORTIZE_STATUS = #{amortizeStatus},
      </if>
    </set>
    where AGREEMENT_ID = #{agreementId}
  </update>


  <select id="selectRbServChargeHistByScSeqNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    where SC_SEQ_NO = #{scSeqNo}
  </select>

  <select id="selectRbServChargeHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    <where>
      <if test="scSeqNo != null and scSeqNo != ''">
        and SC_SEQ_NO = #{scSeqNo}
      </if>
      <if test="reference != null and reference != ''">
        and REFERENCE = #{reference}
      </if>
    </where>
  </select>
  <select id="selectRbServChargeHistByChannelSeqNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    where channel_seq_no = #{orgChannelSeqNo}
  </select>

  <select id="selectRbServChargeHistByChannelSeqNoAndSubSeqNo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    <where>
      <if test="orgChannelSeqNo != null and orgChannelSeqNo != ''">
        and  channel_seq_no = #{orgChannelSeqNo}
      </if>
      <if test="origSubSeqNo != null and origSubSeqNo != ''">
        and SUB_SEQ_NO = #{origSubSeqNo}
      </if>
    </where>
  </select>

  <select id="selectByReferenceAndDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    WHERE  TRAN_DATE BETWEEN #{startDate} and #{endDate}
    <if test="reference != null and reference != ''">
      and reference = #{reference}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <!-- Received in real time -->
    and FEE_CHARGE_METHOD='0'
    <!-- cash collection -->
    and CHARGE_PAY_FLAG='C'
  </select>

  <select id="selectRbServChargeHistByReferenceAndTrantype" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    <where>
      <if test="scSeqNo != null and scSeqNo != ''">
        and SC_SEQ_NO = #{scSeqNo}
      </if>
      <if test="reference != null and reference != ''">
        and REFERENCE = #{reference}
      </if>
      <if test="trantype != null and trantype != ''">
        and TRAN_TYPE = #{trantype}
      </if>
    </where>

  </select>

  <select id="selectRbServChargeHistByReferenceAndTrantype1" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    select
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    <where>
      <if test="reference != null and reference != ''">
        and REFERENCE = #{reference}
      </if>
      <if test="trantype != null and trantype != ''">
        and TRAN_TYPE = #{trantype}
      </if>
    </where>

  </select>

  <update id="updateGlPostedY" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist" >
    <if test="_databaseId == 'mysql'">
      update RB_SERV_CHARGE_HIST set GL_POSTED_FLAG='Y' where SC_SEQ_NO = #{scSeqNo} and ifnull(GL_POSTED_FLAG,'N')='N'
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </if>
    <if test="_databaseId == 'oracle'">
      update RB_SERV_CHARGE_HIST set GL_POSTED_FLAG='Y' where SC_SEQ_NO = #{scSeqNo} and nvl(GL_POSTED_FLAG,'N')='N'
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </if>
  </update>

  <select id="getMbTranHistByCcyForReversal" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist">
    SELECT
    <include refid="Base_Column"/>
    from RB_SERV_CHARGE_HIST
    <where>
      <if test="sourceType != null and sourceType != ''">
        AND SOURCE_TYPE = #{sourceType}
      </if>
      <if test="reference != null and reference != ''">
        AND REFERENCE = #{reference}
      </if>
      <if test="startDate != null">
        AND <![CDATA[ TRAN_DATE >= #{startDate} ]]>
      </if>
      <if test="endDate != null">
        AND <![CDATA[ TRAN_DATE <= #{endDate} ]]>
      </if>
      <if test="startAmt != null">
        AND <![CDATA[ FEE_AMT >= #{startAmt} ]]>
      </if>
      <if test="endAmt != null">
        AND <![CDATA[ FEE_AMT <= #{endAmt} ]]>
      </if>
      <if test="userId != null and userId != ''">
        AND USER_ID = #{userId}
      </if>
      <if test="chargeToBaseAcctNo !=  null and chargeToBaseAcctNo != ''">
        AND CHARGE_TO_BASE_ACCT_NO = #{chargeToBaseAcctNo}
      </if>
      <if test="feeCcy != null and feeCcy != ''">
        AND FEE_CCY = #{feeCcy}
      </if>
      <if test="tranBranch != null and tranBranch != ''">
        AND TRAN_BRANCH = #{tranBranch}
      </if>
      <if test="clientNo !=  null and clientNo != ''">
        AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

</mapper>
