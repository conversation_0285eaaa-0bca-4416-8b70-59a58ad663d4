<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist">
        select
        <include refid="Base_Column"/>
        from RB_AC_HIST
        where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbAcHistByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist">
        select
        <include refid="Base_Column"/>
        from RB_AC_HIST
        where BASE_ACCT_NO = #{baseAcctNo}
        and TRAN_DATE = #{tranDate}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>


    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist">
    delete from RB_AC_HIST
    where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist">
        update RB_AC_HIST
        <set>
            <if test="internalKey != null">
                INTERNAL_KEY = #{internalKey},
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO = #{baseAcctNo},
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO = #{acctSeqNo},
            </if>
            <if test="acctCcy != null">
                ACCT_CCY = #{acctCcy},
            </if>
            <if test="prodType != null">
                PROD_TYPE = #{prodType},
            </if>
            <if test="acctDesc != null">
                ACCT_DESC = #{acctDesc},
            </if>
            <if test="clientNo != null">
                CLIENT_NO = #{clientNo},
            </if>
            <if test="clientType != null">
                CLIENT_TYPE = #{clientType},
            </if>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="tranType != null">
                TRAN_TYPE = #{tranType},
            </if>
            <if test="eventType != null">
                EVENT_TYPE = #{eventType},
            </if>
            <if test="ccy != null">
                CCY = #{ccy},
            </if>
            <if test="amtType != null">
                AMT_TYPE = #{amtType},
            </if>
            <if test="tranAmt != null">
                TRAN_AMT = #{tranAmt},
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE = #{sourceType},
            </if>
            <if test="userId != null">
                USER_ID = #{userId},
            </if>
            <if test="reference != null">
                REFERENCE = #{reference},
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO = #{bankSeqNo},
            </if>
            <if test="reversal != null">
                REVERSAL = #{reversal},
            </if>
            <if test="reversalTranType != null">
                REVERSAL_TRAN_TYPE = #{reversalTranType},
            </if>
            <if test="reversalDate != null">
                REVERSAL_DATE = #{reversalDate},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTER = #{profitCenter},
            </if>
            <if test="businessUnit != null">
                BUSINESS_UNIT = #{businessUnit},
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE = #{sourceModule},
            </if>
            <if test="company != null">
                COMPANY = #{company},
            </if>
            <if test="lender != null">
                LENDER = #{lender},
            </if>
            <if test="acctStatus != null">
                ACCT_STATUS = #{acctStatus},
            </if>
            <if test="accountingStatus != null">
                ACCOUNTING_STATUS = #{accountingStatus}
            </if>
        </set>
        where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist">
        insert into RB_AC_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                SEQ_NO,
            </if>
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO,
            </if>
            <if test="acctCcy != null">
                ACCT_CCY,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="acctDesc != null">
                ACCT_DESC,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="clientType != null">
                CLIENT_TYPE,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="eventType != null">
                EVENT_TYPE,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="amtType != null">
                AMT_TYPE,
            </if>
            <if test="tranAmt != null">
                TRAN_AMT,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO,
            </if>
            <if test="reversal != null">
                REVERSAL,
            </if>
            <if test="reversalTranType != null">
                REVERSAL_TRAN_TYPE,
            </if>
            <if test="reversalDate != null">
                REVERSAL_DATE,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTER,
            </if>
            <if test="businessUnit != null">
                BUSINESS_UNIT,
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="lender != null">
                LENDER,
            </if>
            <if test="acctStatus != null">
                ACCT_STATUS,
            </if>
            <if test="accountingStatus != null">
                ACCOUNTING_STATUS,
            </if>
            <if test="acctBranch != null">
                ACCT_TRAN_BRANCH,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                #{seqNo},
            </if>
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="acctSeqNo != null">
                #{acctSeqNo},
            </if>
            <if test="acctCcy != null">
                #{acctCcy},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="acctDesc != null">
                #{acctDesc},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="clientType != null">
                #{clientType},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="tranType != null">
                #{tranType},
            </if>
            <if test="eventType != null">
                #{eventType},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="amtType != null">
                #{amtType},
            </if>
            <if test="tranAmt != null">
                #{tranAmt},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="bankSeqNo != null">
                #{bankSeqNo},
            </if>
            <if test="reversal != null">
                #{reversal},
            </if>
            <if test="reversalTranType != null">
                #{reversalTranType},
            </if>
            <if test="reversalDate != null">
                #{reversalDate},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="profitCenter != null">
                #{profitCenter},
            </if>
            <if test="businessUnit != null">
                #{businessUnit},
            </if>
            <if test="sourceModule != null">
                #{sourceModule},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="lender != null">
                #{lender},
            </if>
            <if test="acctStatus != null">
                #{acctStatus},
            </if>
            <if test="accountingStatus != null">
                #{accountingStatus},
            </if>
            <if test="acctBranch != null">
                #{acctBranch},
            </if>
        </trim>
    </insert>

    <select id="selectMbAcHistSplitOB" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(seq_no) START_KEY,
            MAX(seq_no) END_KEY,COUNT(*) ROW_COUNT
            FROM
            (
            SELECT
            seq_no,
            @rownum :=@rownum + 1 AS rownum
            FROM (SELECT DISTINCT seq_no FROM RB_AC_HIST,
            (SELECT @rownum := -1) t
            where tran_date = #{runDate}
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            AND (GL_POSTED_FLAG = 'N' or GL_POSTED_FLAG is null) )t1
            ORDER BY seq_no
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (seq_no) START_KEY, MAX (seq_no) END_KEY,COUNT(*) ROW_COUNT
            FROM (SELECT DISTINCT seq_no
            from RB_AC_HIST
            where tran_date = #{runDate}
            AND (GL_POSTED_FLAG = 'N' or GL_POSTED_FLAG is null)
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            ORDER BY seq_no)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>
    <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcHist">
    update RB_AC_HIST set GL_POSTED_FLAG='Y'   where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
  </update>
</mapper>
