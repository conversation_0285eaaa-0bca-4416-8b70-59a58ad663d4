<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRecInfo">


  <update id="updateByDepositCertNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRecInfo">
    update RB_DEPOSIT_CERT_REC_INFO
    <set>
      <if test="depositCertOperateType != null">
        DEPOSIT_CERT_OPERATE_TYPE= #{depositCertOperateType},
      </if>
    </set>
    where DEPOSIT_CERT_NO = #{depositCertNo}
    <if test="clientNo != null">
      and CLIENT_NO= #{clientNo},
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="getDepositCertRecInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRecInfo">
    select <include refid="Base_Column"/>
    from RB_DEPOSIT_CERT_REC_INFO
    where DEPOSIT_CERT_NO = #{mbDepositCertNo}
    <if test="clientNo != null and clientNo != ''">
      and CLIENT_NO = #{clientNo}
    </if>
    <if test="depositCertOperateType != null and  depositCertOperateType != '' ">
      and DEPOSIT_CERT_OPERATE_TYPE = #{depositCertOperateType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
