<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.EgTranInfo">

    <sql id="Table_Name">
       ENS_EG.EG_TRAN_INFO
    </sql>

    <sql id="Base_Column">
        <trim suffixOverrides=",">
            SERVICE_ID,
            SERVICE_NO,
            TRAN_DATE,
            TRAN_TIME,
            RESPONSE_TYPE,
            END_TIME,
            SOURCE_TYPE,
            SEQ_NO,
            PROGRAM_ID,
            STATUS,
            REFERENCE,
            IP_ADDR,
            COMPENSATE_SERVICE_NO,
            WEEK_DAY,
            PLATFORM_ID,
            BRANCH_ID,
            USER_ID,
            CREATE_DATE,
            DEAL_FLAG,
        </trim>
    </sql>

    <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.model.entity.dbmodel.EgTranInfo">
        <result property="serviceId" column="SERVICE_ID"/>
        <result property="serviceNo" column="SERVICE_NO"/>
        <result property="tranDate" column="TRAN_DATE"/>
        <result property="tranTime" column="TRAN_TIME"/>
        <result property="responseType" column="RESPONSE_TYPE"/>
        <result property="endTime" column="END_TIME"/>
        <result property="sourceType" column="SOURCE_TYPE"/>
        <result property="seqNo" column="SEQ_NO"/>
        <result property="programId" column="PROGRAM_ID"/>
        <result property="status" column="STATUS"/>
        <result property="reference" column="REFERENCE"/>
        <result property="ipAddr" column="IP_ADDR"/>
        <result property="compensateServiceNo" column="COMPENSATE_SERVICE_NO"/>
        <result property="weekDay" column="WEEK_DAY"/>
        <result property="platformId" column="PLATFORM_ID"/>
        <result property="branchId" column="BRANCH_ID"/>
        <result property="userId" column="USER_ID"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="dealFlag" column="DEAL_FLAG"/>
    </resultMap>

    <sql id="Base_Where">
        <where>
            <if test="serviceId != null and  serviceId != '' ">
                AND SERVICE_ID = #{serviceId,jdbcType=VARCHAR}
            </if>
            <if test="serviceNo != null and  serviceNo != '' ">
                AND SERVICE_NO = #{serviceNo,jdbcType=VARCHAR}
            </if>
            <if test="tranDate != null ">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE}
            </if>
            <if test="tranTime != null and  tranTime != '' ">
                AND TRAN_TIME = #{tranTime,jdbcType=VARCHAR}
            </if>
            <if test="responseType != null and  responseType != '' ">
                AND RESPONSE_TYPE = #{responseType,jdbcType=VARCHAR}
            </if>
            <if test="endTime != null and  endTime != '' ">
                AND END_TIME = #{endTime,jdbcType=VARCHAR}
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                AND SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
            <if test="programId != null and  programId != '' ">
                AND PROGRAM_ID = #{programId,jdbcType=VARCHAR}
            </if>
            <if test="status != null and  status != '' ">
                AND STATUS = #{status,jdbcType=VARCHAR}
            </if>
            <if test="reference != null and  reference != '' ">
                AND REFERENCE = #{reference,jdbcType=VARCHAR}
            </if>
            <if test="ipAddr != null and  ipAddr != '' ">
                AND IP_ADDR = #{ipAddr,jdbcType=VARCHAR}
            </if>
            <if test="compensateServiceNo != null and  compensateServiceNo != '' ">
                AND COMPENSATE_SERVICE_NO = #{compensateServiceNo,jdbcType=VARCHAR}
            </if>
            <if test="weekDay != null ">
                AND WEEK_DAY = #{weekDay,jdbcType=DATE}
            </if>
            <if test="platformId != null and  platformId != '' ">
                AND PLATFORM_ID = #{platformId,jdbcType=VARCHAR}
            </if>
            <if test="branchId != null and  branchId != '' ">
                AND BRANCH_ID = #{branchId,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and  userId != '' ">
                AND USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="createDate != null ">
                AND CREATE_DATE = #{createDate,jdbcType=DATE}
            </if>
            <if test="dealFlag != null and  dealFlag != '' ">
                AND DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="PrimaryKey_Where">
        <where>
        </where>
    </sql>

    <sql id="Base_Set">
        <set>
            <if test="serviceId != null and serviceId != '' ">
                SERVICE_ID = #{serviceId,jdbcType=VARCHAR},
            </if>
            <if test="serviceNo != null and serviceNo != '' ">
                SERVICE_NO = #{serviceNo,jdbcType=VARCHAR},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="tranTime != null and tranTime != '' ">
                TRAN_TIME = #{tranTime,jdbcType=VARCHAR},
            </if>
            <if test="responseType != null and responseType != '' ">
                RESPONSE_TYPE = #{responseType,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null and endTime != '' ">
                END_TIME = #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null and sourceType != '' ">
                SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="seqNo != null and seqNo != '' ">
                SEQ_NO = #{seqNo,jdbcType=VARCHAR},
            </if>
            <if test="programId != null and programId != '' ">
                PROGRAM_ID = #{programId,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="reference != null and reference != '' ">
                REFERENCE = #{reference,jdbcType=VARCHAR},
            </if>
            <if test="ipAddr != null and ipAddr != '' ">
                IP_ADDR = #{ipAddr,jdbcType=VARCHAR},
            </if>
            <if test="compensateServiceNo != null and compensateServiceNo != '' ">
                COMPENSATE_SERVICE_NO = #{compensateServiceNo,jdbcType=VARCHAR},
            </if>
            <if test="weekDay != null ">
                WEEK_DAY = #{weekDay,jdbcType=DATE},
            </if>
            <if test="platformId != null and platformId != '' ">
                PLATFORM_ID = #{platformId,jdbcType=VARCHAR},
            </if>
            <if test="branchId != null and branchId != '' ">
                BRANCH_ID = #{branchId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null and userId != '' ">
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null ">
                CREATE_DATE = #{createDate,jdbcType=DATE},
            </if>
            <if test="dealFlag != null and dealFlag != '' ">
                DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR},
            </if>
        </set>
    </sql>

    <sql id="Base_Select">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </sql>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'"> ${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'"> *</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}</if>
    </sql>

    <sql id="comet_row_num_step_column">
        <if test="cometSqlType == 'total'"> count(*) AS TOTAL</if>
        <if test="cometSqlType == 'offset'"> *</if>
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceId != null ">SERVICE_ID,</if>
            <if test="serviceNo != null ">SERVICE_NO,</if>
            <if test="tranDate != null ">TRAN_DATE,</if>
            <if test="tranTime != null ">TRAN_TIME,</if>
            <if test="responseType != null ">RESPONSE_TYPE,</if>
            <if test="endTime != null ">END_TIME,</if>
            <if test="sourceType != null ">SOURCE_TYPE,</if>
            <if test="seqNo != null ">SEQ_NO,</if>
            <if test="programId != null ">PROGRAM_ID,</if>
            <if test="status != null ">STATUS,</if>
            <if test="reference != null ">REFERENCE,</if>
            <if test="ipAddr != null ">IP_ADDR,</if>
            <if test="compensateServiceNo != null ">COMPENSATE_SERVICE_NO,</if>
            <if test="weekDay != null ">WEEK_DAY,</if>
            <if test="platformId != null ">PLATFORM_ID,</if>
            <if test="branchId != null ">BRANCH_ID,</if>
            <if test="userId != null ">USER_ID,</if>
            <if test="createDate != null ">CREATE_DATE,</if>
            <if test="dealFlag != null ">DEAL_FLAG,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceId != null ">#{serviceId,jdbcType=VARCHAR},</if>
            <if test="serviceNo != null ">#{serviceNo,jdbcType=VARCHAR},</if>
            <if test="tranDate != null ">#{tranDate,jdbcType=DATE},</if>
            <if test="tranTime != null ">#{tranTime,jdbcType=VARCHAR},</if>
            <if test="responseType != null ">#{responseType,jdbcType=VARCHAR},</if>
            <if test="endTime != null ">#{endTime,jdbcType=VARCHAR},</if>
            <if test="sourceType != null ">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="seqNo != null ">#{seqNo,jdbcType=VARCHAR},</if>
            <if test="programId != null ">#{programId,jdbcType=VARCHAR},</if>
            <if test="status != null ">#{status,jdbcType=VARCHAR},</if>
            <if test="reference != null ">#{reference,jdbcType=VARCHAR},</if>
            <if test="ipAddr != null ">#{ipAddr,jdbcType=VARCHAR},</if>
            <if test="compensateServiceNo != null ">#{compensateServiceNo,jdbcType=VARCHAR},</if>
            <if test="weekDay != null ">#{weekDay,jdbcType=DATE},</if>
            <if test="platformId != null ">#{platformId,jdbcType=VARCHAR},</if>
            <if test="branchId != null ">#{branchId,jdbcType=VARCHAR},</if>
            <if test="userId != null ">#{userId,jdbcType=VARCHAR},</if>
            <if test="createDate != null ">#{createDate,jdbcType=DATE},</if>
            <if test="dealFlag != null ">#{dealFlag,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>

    <update id="updateByPrimaryKey">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>


    <update id="updateByEntity">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="s.serviceId != null and s.serviceId != '' ">
                SERVICE_ID = #{s.serviceId,jdbcType=VARCHAR},
            </if>
            <if test="s.serviceNo != null and s.serviceNo != '' ">
                SERVICE_NO = #{s.serviceNo,jdbcType=VARCHAR},
            </if>
            <if test="s.tranDate != null ">
                TRAN_DATE = #{s.tranDate,jdbcType=DATE},
            </if>
            <if test="s.tranTime != null and s.tranTime != '' ">
                TRAN_TIME = #{s.tranTime,jdbcType=VARCHAR},
            </if>
            <if test="s.responseType != null and s.responseType != '' ">
                RESPONSE_TYPE = #{s.responseType,jdbcType=VARCHAR},
            </if>
            <if test="s.endTime != null and s.endTime != '' ">
                END_TIME = #{s.endTime,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceType != null and s.sourceType != '' ">
                SOURCE_TYPE = #{s.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="s.seqNo != null and s.seqNo != '' ">
                SEQ_NO = #{s.seqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.programId != null and s.programId != '' ">
                PROGRAM_ID = #{s.programId,jdbcType=VARCHAR},
            </if>
            <if test="s.status != null and s.status != '' ">
                STATUS = #{s.status,jdbcType=VARCHAR},
            </if>
            <if test="s.reference != null and s.reference != '' ">
                REFERENCE = #{s.reference,jdbcType=VARCHAR},
            </if>
            <if test="s.ipAddr != null and s.ipAddr != '' ">
                IP_ADDR = #{s.ipAddr,jdbcType=VARCHAR},
            </if>
            <if test="s.compensateServiceNo != null and s.compensateServiceNo != '' ">
                COMPENSATE_SERVICE_NO = #{s.compensateServiceNo,jdbcType=VARCHAR},
            </if>
            <if test="s.weekDay != null ">
                WEEK_DAY = #{s.weekDay,jdbcType=DATE},
            </if>
            <if test="s.platformId != null and s.platformId != '' ">
                PLATFORM_ID = #{s.platformId,jdbcType=VARCHAR},
            </if>
            <if test="s.branchId != null and s.branchId != '' ">
                BRANCH_ID = #{s.branchId,jdbcType=VARCHAR},
            </if>
            <if test="s.userId != null and s.userId != '' ">
                USER_ID = #{s.userId,jdbcType=VARCHAR},
            </if>
            <if test="s.createDate != null ">
                CREATE_DATE = #{s.createDate,jdbcType=DATE},
            </if>
            <if test="s.dealFlag != null and s.dealFlag != '' ">
                DEAL_FLAG = #{s.dealFlag,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="w.serviceId != null and w.serviceId != '' ">
                AND SERVICE_ID = #{w.serviceId,jdbcType=VARCHAR}
            </if>
            <if test="w.serviceNo != null and w.serviceNo != '' ">
                AND SERVICE_NO = #{w.serviceNo,jdbcType=VARCHAR}
            </if>
            <if test="w.tranDate != null ">
                AND TRAN_DATE = #{w.tranDate,jdbcType=DATE}
            </if>
            <if test="w.tranTime != null and w.tranTime != '' ">
                AND TRAN_TIME = #{w.tranTime,jdbcType=VARCHAR}
            </if>
            <if test="w.responseType != null and w.responseType != '' ">
                AND RESPONSE_TYPE = #{w.responseType,jdbcType=VARCHAR}
            </if>
            <if test="w.endTime != null and w.endTime != '' ">
                AND END_TIME = #{w.endTime,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceType != null and w.sourceType != '' ">
                AND SOURCE_TYPE = #{w.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="w.seqNo != null and w.seqNo != '' ">
                AND SEQ_NO = #{w.seqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.programId != null and w.programId != '' ">
                AND PROGRAM_ID = #{w.programId,jdbcType=VARCHAR}
            </if>
            <if test="w.status != null and w.status != '' ">
                AND STATUS = #{w.status,jdbcType=VARCHAR}
            </if>
            <if test="w.reference != null and w.reference != '' ">
                AND REFERENCE = #{w.reference,jdbcType=VARCHAR}
            </if>
            <if test="w.ipAddr != null and w.ipAddr != '' ">
                AND IP_ADDR = #{w.ipAddr,jdbcType=VARCHAR}
            </if>
            <if test="w.compensateServiceNo != null and w.compensateServiceNo != '' ">
                AND COMPENSATE_SERVICE_NO = #{w.compensateServiceNo,jdbcType=VARCHAR}
            </if>
            <if test="w.weekDay != null ">
                AND WEEK_DAY = #{w.weekDay,jdbcType=DATE}
            </if>
            <if test="w.platformId != null and w.platformId != '' ">
                AND PLATFORM_ID = #{w.platformId,jdbcType=VARCHAR}
            </if>
            <if test="w.branchId != null and w.branchId != '' ">
                AND BRANCH_ID = #{w.branchId,jdbcType=VARCHAR}
            </if>
            <if test="w.userId != null and w.userId != '' ">
                AND USER_ID = #{w.userId,jdbcType=VARCHAR}
            </if>
            <if test="w.createDate != null ">
                AND CREATE_DATE = #{w.createDate,jdbcType=DATE}
            </if>
            <if test="w.dealFlag != null and w.dealFlag != '' ">
                AND DEAL_FLAG = #{w.dealFlag,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <delete id="delete">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </delete>

    <select id="count" parameterType="java.util.Map" resultType="int">
        SELECT count(1) FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </select>

    <select id="selectOne" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectList" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
        <include refid="Base_Select"/>
        FOR UPDATE
    </select>

    <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </select>

</mapper>
