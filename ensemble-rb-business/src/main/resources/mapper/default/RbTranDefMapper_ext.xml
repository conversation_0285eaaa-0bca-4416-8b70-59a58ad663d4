<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef">
        select
        <include refid="Base_Column"/>
        from RB_TRAN_DEF
        where TRAN_TYPE = #{tranType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef">
        delete from RB_TRAN_DEF
        where TRAN_TYPE = #{tranType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef">
        insert into RB_TRAN_DEF
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="tranTypeDesc != null">
                TRAN_TYPE_DESC,
            </if>
            <if test="tranClass != null">
                TRAN_CLASS,
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE,
            </if>
            <if test="crDrMaintInd != null">
                CR_DR_MAINT_IND,
            </if>
            <if test="reversalTranType != null">
                REVERSAL_TRAN_TYPE,
            </if>
            <if test="cashTranFlag != null">
                CASH_TRAN_FLAG,
            </if>
            <if test="multiRvsTranTypeFlag != null">
                MULTI_RVS_TRAN_TYPE_FLAG,
            </if>
            <if test="programIdGroup != null">
                PROGRAM_ID_GROUP,
            </if>
            <if test="reversal != null">
                REVERSAL,
            </if>
            <if test="balanceFlag != null">
                BALANCE_FLAG,
            </if>
            <if test="balTypePriority != null">
                BAL_TYPE_PRIORITY,
            </if>
            <if test="printTranDesc != null">
                PRINT_TRAN_DESC,
            </if>
            <if test="isCorrect != null">
                IS_CORRECT,
            </if>
            <if test="othTranType != null">
                OTH_TRAN_TYPE,
            </if>
            <if test="recalcAcctStopPayFlag != null">
                RECALC_ACCT_STOP_PAY_FLAG,
            </if>
            <if test="recalcResAmtFlag != null">
                RECALC_RES_AMT_FLAG,
            </if>
            <if test="resPriority != null">
                RES_PRIORITY,
            </if>
            <if test="availbalCalcType != null">
                AVAILBAL_CALC_TYPE,
            </if>
            <if test="updTrailboxFlag != null">
                UPD_TRAILBOX_FLAG,
            </if>

            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="isInitParam != null">
                IS_INIT_PARAM,
            </if>
            <if test="company != null">
               COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tranType != null">
                #{tranType},
            </if>
            <if test="tranTypeDesc != null">
                #{tranTypeDesc},
            </if>
            <if test="tranClass != null">
                #{tranClass},
            </if>
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="crDrMaintInd != null">
                #{crDrMaintInd},
            </if>
            <if test="reversalTranType != null">
                #{reversalTranType},
            </if>
            <if test="cashTranFlag != null">
                #{cashTranFlag},
            </if>
            <if test="multiRvsTranTypeFlag != null">
                #{multiRvsTranTypeFlag},
            </if>
            <if test="programIdGroup != null">
                #{programIdGroup},
            </if>
            <if test="reversal != null">
                #{reversal},
            </if>
            <if test="balanceFlag != null">
                #{balanceFlag},
            </if>
            <if test="balTypePriority != null">
                #{balTypePriority},
            </if>
            <if test="printTranDesc != null">
                #{printTranDesc},
            </if>
            <if test="isCorrect != null">
                #{isCorrect},
            </if>
            <if test="othTranType != null">
                #{othTranType},
            </if>
            <if test="recalcAcctStopPayFlag != null">
                #{recalcAcctStopPayFlag},
            </if>
            <if test="recalcResAmtFlag != null">
                #{recalcResAmtFlag},
            </if>
            <if test="resPriority != null">
                #{resPriority},
            </if>
            <if test="availbalCalcType != null">
                #{availbalCalcType},
            </if>
            <if test="updTrailboxFlag != null">
                #{updTrailboxFlag},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="isInitParam != null">
                #{isInitParam},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef">
        update RB_TRAN_DEF
        <set>
            <if test="tranTypeDesc != null">
                TRAN_TYPE_DESC = #{tranTypeDesc},
            </if>
            <if test="tranClass != null">
                TRAN_CLASS = #{tranClass},
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE = #{sourceType},
            </if>
            <if test="crDrMaintInd != null">
                CR_DR_MAINT_IND = #{crDrMaintInd},
            </if>
            <if test="reversalTranType != null">
                REVERSAL_TRAN_TYPE = #{reversalTranType},
            </if>
            <if test="cashTranFlag != null">
                CASH_TRAN_FLAG = #{cashTranFlag},
            </if>
            <if test="multiRvsTranTypeFlag != null">
                MULTI_RVS_TRAN_TYPE_FLAG = #{multiRvsTranTypeFlag},
            </if>
            <if test="programIdGroup != null">
                PROGRAM_ID_GROUP = #{programIdGroup},
            </if>
            <if test="reversal != null">
                REVERSAL = #{reversal},
            </if>
            <if test="balanceFlag != null">
                BALANCE_FLAG = #{balanceFlag},
            </if>
            <if test="balTypePriority != null">
                BAL_TYPE_PRIORITY = #{balTypePriority},
            </if>
            <if test="printTranDesc != null">
                PRINT_TRAN_DESC = #{printTranDesc},
            </if>
            <if test="isCorrect != null">
                IS_CORRECT = #{isCorrect},
            </if>
            <if test="othTranType != null">
                OTH_TRAN_TYPE = #{othTranType},
            </if>
            <if test="recalcAcctStopPayFlag != null">
                RECALC_ACCT_STOP_PAY_FLAG = #{recalcAcctStopPayFlag},
            </if>
            <if test="recalcResAmtFlag != null">
                RECALC_RES_AMT_FLAG = #{recalcResAmtFlag},
            </if>
            <if test="resPriority != null">
                RES_PRIORITY = #{resPriority},
            </if>
            <if test="availbalCalcType != null">
                AVAILBAL_CALC_TYPE = #{availbalCalcType},
            </if>
            <if test="updTrailboxFlag != null">
                UPD_TRAILBOX_FLAG = #{updTrailboxFlag},
            </if>

            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
            <if test="isInitParam != null">
                IS_INIT_PARAM = #{isInitParam}
            </if>
        </set>
        where TRAN_TYPE = #{tranType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="getReversalTranType" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT reversal_tran_type
        from RB_TRAN_DEF
        where TRAN_TYPE = #{tranType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getCrDrMaintInd" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT CR_DR_MAINT_IND
        from RB_TRAN_DEF
        where TRAN_TYPE = #{tranType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getReversalTranFlag" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT REVERSAL
        from RB_TRAN_DEF
        where TRAN_TYPE = #{tranType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getTranType" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef">
        SELECT
        <include refid="Base_Column"/>
        from RB_TRAN_DEF
        <where>
        <if test="availbalCalcType != null">
            AND AVAILBAL_CALC_TYPE = #{availbalCalcType}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        </where>
    </select>
    <select id="getOthTranType" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT oth_tran_type
        from RB_TRAN_DEF
        where TRAN_TYPE = #{tranType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
