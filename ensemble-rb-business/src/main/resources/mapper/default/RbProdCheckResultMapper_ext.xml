<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCheckResult">

    <select id="selectCheckResultByPage" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCheckResult">
        SELECT <include refid="Base_Column"/>
        FROM RB_PROD_CHECK_RESULT
        <where>
            <if test="startDate != null">
                AND check_date <![CDATA[ >= ]]> #{startDate,jdbcType=DATE}
            </if>
            <if test="endDate != null">
                AND check_date <![CDATA[ <= ]]> #{endDate,jdbcType=DATE}
            </if>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
            </if>
            <if test="branch != null and  branch != '' ">
                AND BRANCH = #{branch,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                AND ACCOUNTING_STATUS = #{accountingStatus,jdbcType=VARCHAR}
            </if>
            <if test="glCode != null and  glCode != '' ">
                AND GL_CODE = #{glCode,jdbcType=VARCHAR}
            </if>
            <if test="amtType != null and  amtType != '' ">
                AND AMT_TYPE = #{amtType,jdbcType=VARCHAR}
            </if>
            <if test="checkResult != null and  checkResult != '' ">
                AND CHECK_RESULT = #{checkResult,jdbcType=VARCHAR}
            </if>
            <if test="narrative != null and  narrative != '' ">
                AND NARRATIVE = #{narrative,jdbcType=VARCHAR}
            </if>
            <if test="company != null and  company != '' ">
                AND COMPANY = #{company,jdbcType=VARCHAR}
            </if>
        </where>
        order by check_date desc, branch
    </select>
    <select id="getProdResultByBranch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCheckResult">
        SELECT <include refid="Base_Column"/>
        FROM RB_PROD_CHECK_RESULT
        where branch between #{startKey} and #{endKey} and check_date = #{lastRunDate,jdbcType=DATE}
    </select>
</mapper>