<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">

  <select id="selectByPrimaryKeyExt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Tue Aug 11 13:56:23 CST 2015.
    -->
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Tue Aug 11 13:56:23 CST 2015.
    -->
    insert into RB_RESTRAINTS_HIST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="resPriority != null" >
        RES_PRIORITY,
      </if>
      <if test="seqNo != null" >
        SEQ_NO,
      </if>
      <if test="internalKey != null" >
        INTERNAL_KEY,
      </if>
      <if test="restraintType != null" >
        RESTRAINT_TYPE,
      </if>
      <if test="resSeqNo != null" >
        RES_SEQ_NO,
      </if>
      <if test="initSeqNo != null" >
        INIT_SEQ_NO,
      </if>
      <if test="startDate != null" >
        START_DATE,
      </if>
      <if test="endDate != null" >
        END_DATE,
      </if>
      <if test="pledgedAmt != null" >
        PLEDGED_AMT,
      </if>
      <if test="lastChangeDate != null" >
        LAST_CHANGE_DATE,
      </if>
      <if test="lastChangeUserId != null" >
        LAST_CHANGE_USER_ID,
      </if>
      <if test="channelSeqNo != null" >
        CHANNEL_SEQ_NO,
      </if>
      <if test="sourceType != null" >
        SOURCE_TYPE,
      </if>
      <if test="baseAcctNo != null" >
        BASE_ACCT_NO,
      </if>
      <if test="systemPhase != null" >
        SYSTEM_PHASE,
      </if>
      <if test="mainFlag != null" >
        MAIN_FLAG,
      </if>
      <if test="restraintsStatus != null" >
        RESTRAINTS_STATUS,
      </if>
      <if test="lastStatus != null" >
        LAST_STATUS,
      </if>
      <if test="narrative != null" >
        NARRATIVE,
      </if>
      <if test="subRestraintClass != null">
        SUB_RESTRAINT_CLASS,
      </if>
      <if test="restraintJudiciaryName != null" >
        RESTRAINT_JUDICIARY_NAME,
      </if>
      <if test="resLawNo != null" >
        RES_LAW_NO,
      </if>
      <if test="releaseJudiciaryName != null" >
        RELEASE_JUDICIARY_NAME,
      </if>
      <if test="releaseLawNo != null" >
        RELEASE_LAW_NO,
      </if>
      <if test="deductionJudiciaryName != null" >
        DEDUCTION_JUDICIARY_NAME,
      </if>
      <if test="judiciaryOfficerName != null" >
        JUDICIARY_OFFICER_NAME,
      </if>
      <if test="judiciaryDocumentType != null" >
        JUDICIARY_DOCUMENT_TYPE,
      </if>
      <if test="judiciaryDocumentId != null" >
        JUDICIARY_DOCUMENT_ID,
      </if>
      <if test="judiciaryOthOfficerName != null" >
        JUDICIARY_OTH_OFFICER_NAME,
      </if>
      <if test="judiciaryOthDocumentType != null" >
        JUDICIARY_OTH_DOCUMENT_TYPE,
      </if>
      <if test="judiciaryOthDocumentId != null" >
        JUDICIARY_OTH_DOCUMENT_ID,
      </if>
      <if test="authUserId != null" >
        AUTH_USER_ID,
      </if>
      <if test="company != null" >
        COMPANY,
      </if>
      <if test="reference != null" >
        REFERENCE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="TRAN_BRANCH != null">
        TRAN_BRANCH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="resPriority != null" >
        #{resPriority},
      </if>
      <if test="seqNo != null" >
        #{seqNo},
      </if>
      <if test="internalKey != null" >
        #{internalKey},
      </if>
      <if test="restraintType != null" >
        #{restraintType},
      </if>
      <if test="resSeqNo != null" >
        #{resSeqNo},
      </if>
      <if test="initSeqNo != null" >
        #{initSeqNo},
      </if>
      <if test="startDate != null" >
        #{startDate},
      </if>
      <if test="endDate != null" >
        #{endDate},
      </if>
      <if test="pledgedAmt != null" >
        #{pledgedAmt},
      </if>
      <if test="resAcctRange != null">
        #{resAcctRange},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="lastChangeUserId != null" >
        #{lastChangeUserId},
      </if>
      <if test="channelSeqNo != null" >
        #{channelSeqNo},
      </if>
      <if test="sourceType != null" >
        #{sourceType},
      </if>
      <if test="baseAcctNo != null" >
        #{baseAcctNo},
      </if>
      <if test="systemPhase != null" >
        #{systemPhase},
      </if>
      <if test="mainFlag != null" >
        #{mainFlag},
      </if>
      <if test="restraintsStatus != null" >
        #{restraintsStatus},
      </if>
      <if test="lastStatus != null" >
        #{lastStatus},
      </if>
      <if test="narrative != null" >
        #{narrative},
      </if>
      <if test="subRestraintClass != null">
        #{subRestraintClass},
      </if>
      <if test="restraintJudiciaryName != null" >
        #{restraintJudiciaryName},
      </if>
      <if test="resLawNo != null" >
        #{resLawNo},
      </if>
      <if test="releaseJudiciaryName != null" >
        #{releaseJudiciaryName},
      </if>
      <if test="releaseLawNo != null" >
        #{releaseLawNo},
      </if>
      <if test="deductionJudiciaryName != null" >
        #{deductionJudiciaryName},
      </if>
      <if test="judiciaryOfficerName != null" >
        #{judiciaryOfficerName},
      </if>
      <if test="judiciaryDocumentType != null" >
        #{judiciaryDocumentType},
      </if>
      <if test="judiciaryDocumentId != null" >
        #{judiciaryDocumentId},
      </if>
      <if test="judiciaryOthOfficerName != null" >
        #{judiciaryOthOfficerName},
      </if>
      <if test="judiciaryOthDocumentType != null" >
        #{judiciaryOthDocumentType},
      </if>
      <if test="judiciaryOthDocumentId != null" >
        #{judiciaryOthDocumentId},
      </if>
      <if test="authUserId != null" >
        #{authUserId},
      </if>
      <if test="company != null" >
        #{company},
      </if>
      <if test="reference != null" >
        #{reference},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="TRAN_BRANCH != null">
        #{TRAN_BRANCH},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Tue Aug 11 13:56:23 CST 2015.
    -->
    update RB_RESTRAINTS_HIST
    <set >
      <if test="resPriority != null" >
        RES_PRIORITY = #{resPriority},
      </if>
      <if test="internalKey != null" >
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="restraintType != null" >
        RESTRAINT_TYPE = #{restraintType},
      </if>
      <if test="subRestraintClass != null">
        SUB_RESTRAINT_CLASS = #{subRestraintClass},
      </if>
      <if test="resSeqNo != null" >
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="initSeqNo != null" >
        INIT_SEQ_NO = #{initSeqNo},
      </if>
      <if test="startDate != null" >
        START_DATE = #{startDate},
      </if>
      <if test="endDate != null" >
        END_DATE = #{endDate},
      </if>
      <if test="pledgedAmt != null" >
        PLEDGED_AMT = #{pledgedAmt},
      </if>
      <if test="lastChangeDate != null" >
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="lastChangeUserId != null" >
        LAST_CHANGE_USER_ID = #{lastChangeUserId},
      </if>
      <if test="channelSeqNo != null" >
        CHANNEL_SEQ_NO = #{channelSeqNo},
      </if>
      <if test="sourceType != null" >
        SOURCE_TYPE = #{sourceType},
      </if>
      <if test="baseAcctNo != null" >
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="systemPhase != null" >
        SYSTEM_PHASE = #{systemPhase},
      </if>
      <if test="mainFlag != null" >
        MAIN_FLAG = #{mainFlag},
      </if>
      <if test="restraintsStatus != null" >
        RESTRAINTS_STATUS = #{restraintsStatus},
      </if>
      <if test="lastStatus != null" >
        LAST_STATUS = #{lastStatus},
      </if>
      <if test="narrative != null" >
        NARRATIVE = #{narrative},
      </if>
      <if test="restraintJudiciaryName != null" >
        RESTRAINT_JUDICIARY_NAME = #{restraintJudiciaryName},
      </if>
      <if test="resLawNo != null" >
        RES_LAW_NO = #{resLawNo},
      </if>
      <if test="releaseJudiciaryName != null" >
        RELEASE_JUDICIARY_NAME = #{releaseJudiciaryName},
      </if>
      <if test="releaseLawNo != null" >
        RELEASE_LAW_NO = #{releaseLawNo},
      </if>
      <if test="deductionJudiciaryName != null" >
        DEDUCTION_JUDICIARY_NAME = #{deductionJudiciaryName},
      </if>
      <if test="judiciaryOfficerName != null" >
        JUDICIARY_OFFICER_NAME = #{judiciaryOfficerName},
      </if>
      <if test="judiciaryDocumentType != null" >
        JUDICIARY_DOCUMENT_TYPE = #{judiciaryDocumentType},
      </if>
      <if test="judiciaryDocumentId != null" >
        JUDICIARY_DOCUMENT_ID = #{judiciaryDocumentId},
      </if>
      <if test="judiciaryOthOfficerName != null" >
        JUDICIARY_OTH_OFFICER_NAME = #{judiciaryOthOfficerName},
      </if>
      <if test="judiciaryOthDocumentType != null" >
        JUDICIARY_OTH_DOCUMENT_TYPE = #{judiciaryOthDocumentType},
      </if>
      <if test="judiciaryOthDocumentId != null" >
        JUDICIARY_OTH_DOCUMENT_ID = #{judiciaryOthDocumentId},
      </if>
      <if test="authUserId != null" >
        AUTH_USER_ID = #{authUserId},
      </if>
      <if test="company != null" >
        COMPANY = #{company},
      </if>
      <if test="reference != null" >
        REFERENCE = #{reference},
      </if>
		<if test="tranTimestamp != null">
			TRAN_TIMESTAMP = #{tranTimestamp}
		</if>
      <if test="TRAN_BRANCH != null">
        TRAN_BRANCH = #{TRAN_BRANCH}
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="selectByReference" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where REFERENCE = #{reference}
    <if test="clientNo != null" >
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByReferenceForReversal" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where REFERENCE = #{reference}
    <if test="clientNo != null" >
      AND CLIENT_NO = #{clientNo}
    </if>
    AND (RESERVE IS NULL OR RESERVE != 'Y')
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="selectByResType" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where
    RESTRAINTS_STATUS = 'A'
    <if test="baseAcctNo != null" >
        AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="restraintType != null" >
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByResSeqNo" parameterType="map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where
    RES_SEQ_NO = #{resSeqNo} order by TRAN_TIMESTAMP desc
  </select>

  <select id="selectByChannelSeqNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where CHANNEL_SEQ_NO = #{channelSeqNo}
    <if test="clientNo != null" >
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP desc
  </select>

  <select id="selectByResSeqNoAndClientNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where RES_SEQ_NO = #{resSeqNo}
    <if test="clientNo != null" >
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP desc
  </select>

  <select id="selectByResSeqAndChannelSeqNo" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist">
    select <include refid="Base_Column"/>
    from RB_RESTRAINTS_HIST
    where RES_SEQ_NO = #{resSeqNo}
    AND CHANNEL_SEQ_NO = #{channelSeqNo}
    <if test="clientNo != null" >
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <update id="updateBySeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Tue Aug 11 13:56:23 CST 2015.
    -->
    update RB_RESTRAINTS_HIST
    <set >
      <if test="reserve != null" >
        reserve = #{reserve},
      </if>
    </set>
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="clientNo != null" >
      AND CLIENT_NO = #{clientNo}
    </if>
  </update>
</mapper>
