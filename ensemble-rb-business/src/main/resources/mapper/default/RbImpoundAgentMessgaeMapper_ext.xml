<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundAgentMessgae">

	<select id="getRbImpoundAgentMessgaeList" parameterType="java.util.HashMap"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundAgentMessgae">
		select *
		from RB_IMPOUND_AGENT_MESSGAE
		where 1=1
		<if test="reference != null and  reference != '' ">
		and REFERENCE = #{reference}
		</if>
		<if test="clientNo != null and  clientNo != '' ">
		and CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

</mapper>
