<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementIdep">

    <select id="fuzzyQueryByAgreementId" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementIdep">
        SELECT <include refid="Base_Column"/>
        FROM RB_AGREEMENT_IDEP
        WHERE
        <if test="agreementIdList != null">
            AGREEMENT_ID IN
            <foreach item="item" index="index" collection="agreementIdList" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>


    <select id="selectMbAgreementByfive" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementIdep">
        select
        <include refid="Base_Column"/>
        from RB_AGREEMENT_IDEP
        where BASE_ACCT_NO = #{baseAcctNo}
        and PROD_TYPE = #{prodType}
        and ACCT_SEQ_NO = #{acctSeqNo}
        and ACCT_CCY = #{acctCcy}
        and AGREEMENT_TYPE = #{agreementType}
        and CLIENT_NO = #{clientNo}
        and AGREEMENT_STATUS='A'
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
