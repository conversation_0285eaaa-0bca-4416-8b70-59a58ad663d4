<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTaeAccountCheckDetail">

    <select id="selectBatchCheckDetailBySegment" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTaeAccountCheckDetail">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_TAE_ACCOUNT_CHECK_DETAIL
        WHERE SEQ_NO BETWEEN #{segmentStart} and #{segmentEnd}
    </select>

    <delete id="deleteBatchCheckDetailBySegment" parameterType="java.util.Map">
        DELETE FROM RB_TAE_ACCOUNT_CHECK_DETAIL
        WHERE SEQ_NO BETWEEN #{segmentStart} and #{segmentEnd}
    </delete>

</mapper>