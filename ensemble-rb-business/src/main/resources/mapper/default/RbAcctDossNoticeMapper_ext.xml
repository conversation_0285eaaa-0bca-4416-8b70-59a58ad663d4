<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossNotice">

    <select id="selectDossAcctNoticeList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossNotice">
        <if test="_databaseId == 'mysql'">
            SELECT
            <include refid="Base_Column"/>
            FROM
            <include refid="Table_Name"/>
            <where>
                <if test="baseAcctNo != null and baseAcctNo != '' ">
                    AND BASE_ACCT_NO = #{baseAcctNo}
                </if>
                <if test="startDate != null  ">
                    AND DORMANT_DATE <![CDATA[ >= ]]> #{startDate}
                </if>
                <if test="endDate != null   ">
                    AND DORMANT_DATE <![CDATA[ <= ]]> #{endDate}
                </if>
                <if test="noticeStatus != null and noticeStatus != '' ">
                    AND NOTICE_STATUS = #{noticeStatus}
                </if>
                <if test="branch != null and branch != '' ">
                    AND BRANCH = #{branch}
                </if>
                <if test="companyList != null">
                    AND COMPANY in
                    <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            limit 1000
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT
            <include refid="Base_Column"/>
            FROM
            <include refid="Table_Name"/>
            <where>
                <if test="baseAcctNo != null and baseAcctNo != '' ">
                    AND BASE_ACCT_NO = #{baseAcctNo}
                </if>
                <if test="startDate != null  ">
                    AND DORMANT_DATE <![CDATA[ >= ]]> #{startDate}
                </if>
                <if test="endDate != null   ">
                    AND DORMANT_DATE <![CDATA[ <= ]]> #{endDate}
                </if>
                <if test="noticeStatus != null and noticeStatus != '' ">
                    AND NOTICE_STATUS = #{noticeStatus}
                </if>
                <if test="branch != null and branch != '' ">
                    AND BRANCH = #{branch}
                </if>
                <if test="companyList != null">
                    AND COMPANY in
                    <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            FETCH FIRST 1000 ROWS ONLY
        </if>
    </select>

</mapper>