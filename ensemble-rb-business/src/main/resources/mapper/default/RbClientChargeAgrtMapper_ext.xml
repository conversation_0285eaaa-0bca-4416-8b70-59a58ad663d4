<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">

<!--	<select id="selectNextChargeDateMature" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">-->
<!--		     select <include refid="Base_Column"/>-->
<!--        <![CDATA[-->
<!--		      from RB_CLIENT_CHARGE_AGRT-->
<!--		      where NEXT_CHARGE_DATE <= #{baseParam.runDate} and STATUS='A'-->
<!--		      order by AGREEMENT_ID-->
<!--    	]]>-->
<!--	</select>-->

    <select id="getChargeAgrt" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">
        select a.agreement_id,a.fee_type,a.charge_way,a.period_freq,a.CHARGE_DAY,a.next_charge_date,a.status,a.oth_business_no,a.seq_no,a.oth_name,a.tran_timestamp,a.client_no,a.company
        from RB_CLIENT_CHARGE_AGRT a,RB_agreement b
        where a.agreement_id = b.agreement_id 
        and (a.fee_type = #{feeType} or a.fee_type = 'ALL')
        and b.agreement_key = #{internalKey}
        and a.status='A'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND a.COMPANY = #{company}
        </if>
    </select>

    <select id="getChargeAgrtByKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">
        select *
        from RB_CLIENT_CHARGE_AGRT
        where  AGREEMENT_ID=#{agreementId}
          and  (fee_type = #{feeType} or fee_type = 'ALL')
          and  status='A'
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>

    <select id="getChargeAgrtByKeyMod" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">
        select *
        from RB_CLIENT_CHARGE_AGRT
        where   (fee_type = #{feeType} or fee_type = 'ALL')
        and  status='A'
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>
    <select id="getChargeAgrtByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">
        select a.agreement_id,a.fee_type,a.charge_way,a.period_freq,a.CHARGE_DAY,a.next_charge_date,a.status,a.oth_business_no,a.seq_no,a.oth_name,a.tran_timestamp,a.client_no,a.company
        from RB_CLIENT_CHARGE_AGRT a,RB_agreement b
        where
        a.client_no = b.client_no
        and a.client_no = #{clientNo}
        and a.agreement_id = b.agreement_id
        and b.agreement_key = #{internalKey}
        and a.status='A'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbClientChargeAgrtByAgreementIdAndFeeType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">
        SELECT
         AGREEMENT_ID,
         FEE_TYPE,
         SEQ_NO,
         OTH_BUSINESS_NO,
         OTH_NAME
        FROM
         RB_CLIENT_CHARGE_AGRT
        WHERE
         AGREEMENT_ID=#{agreementId}
        AND
         FEE_TYPE=#{feeType}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <update id="updateRbClientChargeAgrt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt">
        update RB_CLIENT_CHARGE_AGRT
        <set>
            <if test="seqNo != null">
                SEQ_NO = #{seqNo},
            </if>
            <if test="agreementId != null">
                AGREEMENT_ID = #{agreementId},
            </if>
            <if test="feeType != null">
                FEE_TYPE = #{feeType},
            </if>
            <if test="chargeWay != null">
                CHARGE_WAY = #{chargeWay},
            </if>
            PERIOD_FREQ = #{periodFreq},
            CHARGE_DAY = #{chargeDay},
            NEXT_CHARGE_DATE = #{nextChargeDate},
            <if test="othName != null">
                OTH_NAME = #{othName},
            </if>
            <if test="othBusinessNo != null">
                OTH_BUSINESS_NO = #{othBusinessNo},
            </if>
            <if test="status != null">
                STATUS = #{status},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
        </set>
        where AGREEMENT_ID = #{agreementId}
        AND  FEE_TYPE = #{feeType}
        AND  CLIENT_NO = #{clientNo}
    </update>
</mapper>
