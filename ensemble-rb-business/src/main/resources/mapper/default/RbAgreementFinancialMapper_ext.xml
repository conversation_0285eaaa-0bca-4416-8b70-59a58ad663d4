<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial">

  <select id="selectAgreementFinancialByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.agr.MbAgreementFinancialModel">
    select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY, ma.BASE_ACCT_NO, ma.PROD_TYPE,
    ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.<PERSON>ANCH, ma.AGREEMENT_OPEN_DATE, ma.USER_ID, ma.LAST_CHANGE_USER_ID,
    ma.LAST_CHANGE_DATE, ma.COMPANY, maf.FIN_PROD_TYPE, maf.FIN_PROD_DESC, maf.INTERNAL_KEY, maf.BASE_ACCT_NO, maf.PROD_TYPE, maf.ACCT_CCY, maf.ACCT_SEQ_NO,
    maf.REMAIN_AMT
    from MB_AGREEMENT ma, RB_AGREEMENT_FINANCIAL maf
    where ma.AGREEMENT_KEY_TYPE = 'IK'
    and ma.AGREEMENT_KEY = #{internalKey}
    and maf.AGREEMENT_ID = ma.AGREEMENT_ID
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND maf.COMPANY = #{company}
    </if>
  </select>

  <select id="selectActiveFinAgrement" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.agr.MbAgreementFinancialModel">
    select ma.AGREEMENT_ID, ma.AGREEMENT_TYPE, ma.START_DATE, ma.END_DATE, ma.AGREEMENT_KEY as INTERNAL_KEY, ma.BASE_ACCT_NO, ma.PROD_TYPE,
    ma.ACCT_CCY, ma.ACCT_SEQ_NO, ma.CLIENT_NO, ma.CLIENT_SHORT, ma.AGREEMENT_STATUS, ma.BRANCH, ma.AGREEMENT_OPEN_DATE, ma.USER_ID, ma.LAST_CHANGE_USER_ID,
    ma.LAST_CHANGE_DATE, ma.COMPANY, maf.FIN_PROD_TYPE, maf.FIN_PROD_DESC,
    maf.REMAIN_AMT
    from MB_AGREEMENT ma, RB_AGREEMENT_FINANCIAL maf
    where ma.AGREEMENT_TYPE = 'FIN'
    and maf.AGREEMENT_ID = ma.AGREEMENT_ID
    and ma.AGREEMENT_STATUS = 'A'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND maf.COMPANY = #{company}
    </if>
  </select>
  <select id="selectActiveFinAgrementSplit" resultType="java.util.Map" parameterType="java.util.Map" >
    select COUNT(1) ROW_COUNT
    from MB_AGREEMENT ma, RB_AGREEMENT_FINANCIAL maf
    where ma.AGREEMENT_TYPE = 'FIN'
    and maf.AGREEMENT_ID = ma.AGREEMENT_ID
    and ma.AGREEMENT_STATUS = 'A'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND maf.COMPANY = #{company}
    </if>
  </select>

  <select id="getMbAgreementFinancial" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    where AGREEMENT_ID = #{agreementId} and AGREEMENT_STATUS in ('A', 'N', 'S')
    <if test="clientNo != null and clientNo.length() > 0">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbFinAcctByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial" >
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectAgreementFinancialByAgreementId" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from  RB_AGREEMENT_FINANCIAL
    where  AGREEMENT_ID = #{agreementId}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbAgreementFin" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial">
    select
    <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    where AGREEMENT_STATUS in('A','N','S')
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="baseAcctNo != null and baseAcctNo.length() > 0">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null and prodType.length() > 0">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctSeqNo != null and acctSeqNo.length() > 0">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="acctCcy != null and acctCcy.length() > 0">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <if test="agreementType != null and agreementType.length() > 0">
      AND AGREEMENT_TYPE = #{agreementType}
    </if>
    <if test="clientNo != null and clientNo.length() > 0">
      AND CLIENT_NO = #{clientNo}
    </if>
  </select>

  <select id="getMbAgreementFinAllStatus" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial">
    select
    <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    <where>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      <if test="agreementId != null and agreementId.length() > 0">
        AND AGREEMENT_ID = #{agreementId}
      </if>
      <if test="baseAcctNo != null and baseAcctNo.length() > 0">
        AND BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test="prodType != null and prodType.length() > 0">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="acctSeqNo != null and acctSeqNo.length() > 0">
        AND ACCT_SEQ_NO = #{acctSeqNo}
      </if>
      <if test="acctCcy != null and acctCcy.length() > 0">
        AND ACCT_CCY = #{acctCcy}
      </if>
      <if test="agreementType != null and agreementType.length() > 0">
        AND AGREEMENT_TYPE = #{agreementType}
      </if>
      <if test="clientNo != null and clientNo.length() > 0">
        AND CLIENT_NO = #{clientNo}
      </if>
    </where>
  </select>


  <select id="getZxqyAvaByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    where INTERNAL_KEY = #{internalKey}
    <if test="clientNo != null ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="agreementType != null">
      AND AGREEMENT_TYPE = #{agreementType}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    and AGREEMENT_STATUS in ('A','N','S')
  </select>
  <select id="getRbAgreementFinancialByPage" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    <where>
      <if test="internalKey != null ">
        INTERNAL_KEY = #{internalKey}
      </if>
      <if test="baseAcctNo != null and baseAcctNo.length() > 0">
        AND  BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test="prodType != null and prodType.length() > 0">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="acctSeqNo != null and acctSeqNo.length() > 0">
        AND ACCT_SEQ_NO = #{acctSeqNo}
      </if>
      <if test="agreementStatus != null and agreementStatus.length() > 0">
        AND  AGREEMENT_STATUS = #{agreementStatus}
      </if>
      <if test="clientNo != null  and clientNo.length() > 0">
        AND  CLIENT_NO = #{clientNo}
      </if>
      <if test="agreementType != null and agreementType.length() > 0">
        AND  AGREEMENT_TYPE = #{agreementType}
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
    ORDER BY TRANSFER_START_DATE ASC
  </select>

  <select id="getRbAgreementFinancialNoPage" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    <where>
      <if test="internalKey != null ">
        INTERNAL_KEY = #{internalKey}
      </if>
      <if test="baseAcctNo != null and baseAcctNo.length() > 0">
        AND  BASE_ACCT_NO = #{baseAcctNo}
      </if>
      <if test="prodType != null and prodType.length() > 0">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="acctSeqNo != null and acctSeqNo.length() > 0">
        AND ACCT_SEQ_NO = #{acctSeqNo}
      </if>
      <if test="acctCcy != null and acctCcy.length() > 0">
        AND ACCT_CCY = #{acctCcy}
      </if>
      <if test="agreementStatus != null and agreementStatus.length() > 0">
        AND  AGREEMENT_STATUS = #{agreementStatus}
      </if>
      <if test="clientNo != null  and clientNo.length() > 0">
        AND  CLIENT_NO = #{clientNo}
      </if>
      <if test="agreementType != null and agreementType.length() > 0">
        AND  AGREEMENT_TYPE = #{agreementType}
      </if>
      <if test="agreementId != null and agreementId.length() > 0">
        AND  AGREEMENT_ID = #{agreementId}
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>




  <select id="selectFinAgreementInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_FINANCIAL
    where AGREEMENT_STATUS != 'E'
    <if test="acctCcy != null and acctCcy !=''">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <if test="baseAcctNo != null and baseAcctNo != ''">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="acctSeqNo != null and acctSeqNo != ''">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="prodType != null and  prodType !=''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="agreementId != null and agreementId !=''">
      AND AGREEMENT_ID = #{agreementId}
    </if>
    <if test="agreementType != null and agreementType !=''">
      AND AGREEMENT_TYPE = #{agreementType}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
