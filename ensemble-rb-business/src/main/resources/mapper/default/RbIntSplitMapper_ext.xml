<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
	<sql id="Base_Column_List_mat">
		mat.SYSTEM_ID,mat.INTERNAL_KEY,mat.START_DATE,mat.END_DATE,mat.INT_CLASS,mat.BASE_ACCT_NO,mat.PROD_TYPE,mat.EVENT_TYPE,mat.TRAN_BRANCH,mat.SOURCE_TYPE,mat.CCY,mat.INT_CALC_BAL,mat.CLIENT_NO,mat.CR_RATING,mat.IRL_GENE_STR,mat.INT_AMT,mat.CUR_INT_ACCRUED,mat.CUR_INT_ADJ,mat.CUR_TAX_ACCRUED,mat.TAX_TYPE,mat.TAX_RATE,mat.PRE_INT_ACCRUED,mat.AFTER_INT_ACCRUED,mat.PRE_INT_ADJ,mat.AFTER_INT_ADJ,mat.PRE_TAX_ACCRUED,mat.AFTER_TAX_ACCRUED,mat.COMPANY,mat.MONTH_BASIS,mat.YEAR_BASIS,mat.TRAN_TIMESTAMP,mat.mat.mat.INT_TYPE,mat.ACTUAL_RATE,mat.FLOAT_RATE,mat.REAL_RATE,mat.ACCT_SPREAD_RATE,mat.ACCT_PERCENT_RATE,mat.ACCT_FIXED_RATE,mat.AGREE_CHANGE_TYPE,mat.AGREE_FIXED_RATE,mat.AGREE_PERCENT_RATE,mat.AGREE_SPREAD_RATE,mat.AGREE_REDUCE_AMT,mat.SPLIT_RATE_FLAG
	</sql>
	<select id="get" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE
			INTERNAL_KEY = #{internalKey}
		  and INT_CLASS = #{intClass}
		  and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		  and END_DATE IS NULL
		ORDER BY START_DATE ASC
	</select>

	<select id="getSplitListByEndDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE
			INTERNAL_KEY = #{internalKey}
		  and INT_CLASS = #{intClass}
		  and SYSTEM_ID =#{systemId}
		  and END_DATE Between #{startDate} and #{endDate}
		  and START_DATE Between #{startDat,jdbcType=DATE} and #{endDate,jdbcType=DATE}
		<if test="clientNo!=null">
			and CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getSplitInDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">

		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG  FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}

			<if test="company != null and company != '' ">
		  		AND COMPANY = #{company}
			</if>
		  AND INT_CLASS = #{intClass}
		<![CDATA[
		  AND (( START_DATE >= #{startDate,jdbcType=DATE} AND START_DATE <= #{endDate,jdbcType=DATE} )
			OR (END_DATE >= #{startDate} AND END_DATE <= #{endDate} )
			OR ( START_DATE >= #{startDate,jdbcType=DATE} AND END_DATE <= #{endDate} AND #{startDate} < END_DATE)
			OR ( START_DATE <= #{startDate,jdbcType=DATE} AND END_DATE >= #{endDate} AND #{endDate} > START_DATE,jdbcType=DATE))
		ORDER BY START_DATE DESC
		]]>
    </select>

	<select id="findByCondition" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT
		SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF
		FROM RB_INT_SPLIT
		<where>
			<if test="internalKey!=null">
				INTERNAL_KEY = #{internalKey}
			</if>
			<if test="intClass!=null">
				AND INT_CLASS = #{intClass}
			</if>
			and SYSTEM_ID =#{systemId}
			<if test="clientNo!=null">
				AND CLIENT_NO = #{clientNo}
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			and END_DATE IS NULL
		</where>
		ORDER BY START_DATE DESC
	</select>


	<select id="findByInternalKeyIntClass" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT
		SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF
		FROM RB_INT_SPLIT where 1=1
		<if test="systemId!=null">
			and   SYSTEM_ID =#{systemId}
		</if>
		<if test="internalKey!=null">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="intClass!=null">
			AND INT_CLASS = #{intClass}
		</if>
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY START_DATE asc
	</select>

	<select id="findByConditionHistSplit" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT
		SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF
		FROM RB_INT_SPLIT
		<where>
			<if test="internalKey!=null">
				INTERNAL_KEY = #{internalKey}
			</if>
			<if test="intClass!=null">
				AND INT_CLASS = #{intClass}
			</if>
			and SYSTEM_ID =#{systemId}
			<if test="clientNo!=null">
				AND CLIENT_NO = #{clientNo}
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			and END_DATE IS not NULL
		</where>
		ORDER BY START_DATE DESC
	</select>

	<select id="findSplitDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		SELECT
		SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF
		FROM RB_INT_SPLIT
		<where>
			<if test="internalKey!=null">
				INTERNAL_KEY = #{internalKey}
			</if>
			<if test="intClass!=null">
				AND INT_CLASS = #{intClass}
			</if>
			<if test="clientNo!=null">
				AND CLIENT_NO = #{clientNo}
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			AND END_DATE IS NULL
		</where>
	</select>

	<update id="updateByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		UPDATE RB_INT_SPLIT
		<set>
			<if test="intAmt!=null">
				INT_AMT = #{intAmt},
			</if>
			<if test="preIntAccrued!=null">
				PRE_INT_ACCRUED = #{preIntAccrued},
			</if>
			<if test="preIntAccruedDiff!=null">
				PRE_INT_ACCRUED_DIFF = #{preIntAccruedDiff},
			</if>
			<if test="startDate!=null">
				START_DATE = #{startDate},
			</if>
			<if test="endDate!=null">
				END_DATE = #{endDate},
			</if>
			<if test="curIntAccrued!=null">
				CUR_INT_ACCRUED = #{curIntAccrued},
			</if>
			<if test="curIntAdj!=null">
				CUR_INT_ADJ = #{curIntAdj},
			</if>
			<if test="curTaxAccrued!=null">
				CUR_TAX_ACCRUED = #{curTaxAccrued},
			</if>
			<if test="afterIntAccrued!=null">
				AFTER_INT_ACCRUED = #{afterIntAccrued},
			</if>
			<if test="afterIntAccruedDiff!=null">
				AFTER_INT_ACCRUED_DIFF = #{afterIntAccruedDiff},
			</if>
			<if test="afterIntAdj!=null">
				AFTER_INT_ADJ = #{afterIntAdj},
			</if>
			<if test="afterTaxAccrued!=null">
				AFTER_TAX_ACCRUED = #{afterTaxAccrued},
			</if>
			<if test="intType != null">
				INT_TYPE=#{intType},
			</if>
			<if test="actualRate != null">
				ACTUAL_RATE=#{actualRate},
			</if>
			<if test="floatRate != null">
				FLOAT_RATE=#{floatRate},
			</if>
			<if test="realRate != null">
				REAL_RATE=#{realRate},
			</if>
			<if test="acctSpreadRate != null">
				ACCT_SPREAD_RATE=#{acctSpreadRate},
			</if>
			<if test="acctPercentRate != null">
				ACCT_PERCENT_RATE=#{acctPercentRate},
			</if>
			<if test="acctFixedRate != null">
				ACCT_FIXED_RATE=#{acctFixedRate},
			</if>
			<if test="agreeChangeType != null">
				AGREE_CHANGE_TYPE=#{agreeChangeType},
			</if>
			<if test="agreeFixedRate != null">
				AGREE_FIXED_RATE=#{agreeFixedRate},
			</if>
			<if test="agreePercentRate != null">
				AGREE_PERCENT_RATE=#{agreePercentRate},
			</if>
			<if test="agreeSpreadRate != null">
				AGREE_SPREAD_RATE=#{agreeSpreadRate},
			</if>
			<if test="splitRateFlag != null">
				SPLIT_RATE_FLAG=#{splitRateFlag},
			</if>
			<if test="agreeReduceAmt != null">
				AGREE_REDUCE_AMT=#{agreeReduceAmt},
			</if>
		</set>
		WHERE INTERNAL_KEY = #{internalKey}
		AND START_DATE = #{startDate,jdbcType=DATE}
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<update id="updateByIntAccrued" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		UPDATE RB_INT_SPLIT
		<set>
			<if test="curIntAccrued!=null">
				CUR_INT_ACCRUED = CUR_INT_ACCRUED + #{curIntAccrued},
			</if>
			<if test="curIntAdj!=null">
				CUR_INT_ADJ =CUR_INT_ADJ + #{curIntAdj},
			</if>
			<if test="curTaxAccrued!=null">
				CUR_TAX_ACCRUED =CUR_INT_ADJ + #{curTaxAccrued},
			</if>
			<if test="tdIntNumDays!=null">
				TD_INT_NUM_DAYS =#{tdIntNumDays},
			</if>
			<if test="tdAccrIntDay!=null">
				TD_ACCR_INT_DAY =#{tdAccrIntDay},
			</if>
			<if test="calcBeginDate!=null">
				CALC_BEGIN_DATE =#{calcBeginDate},
			</if>
			<if test="tdEndAccrDate!=null">
				TD_END_ACCR_DATE =#{tdEndAccrDate},
			</if>
			<if test="tdLastAccrDate!=null">
				TD_LAST_ACCR_DATE =#{tdLastAccrDate},
			</if>
			<if test="actualRate != null">
				ACTUAL_RATE=#{actualRate},
			</if>
			<if test="floatRate != null">
				FLOAT_RATE=#{floatRate},
			</if>
			<if test="realRate != null">
				REAL_RATE=#{realRate},
			</if>
			<if test="acctSpreadRate != null">
				ACCT_SPREAD_RATE=#{acctSpreadRate},
			</if>
			<if test="acctPercentRate != null">
				ACCT_PERCENT_RATE=#{acctPercentRate},
			</if>
		</set>
		WHERE INTERNAL_KEY = #{internalKey}
		AND START_DATE = #{startDate,jdbcType=DATE}
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<update id="updateNew" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		UPDATE RB_INT_SPLIT
		<set>
			<if test="endDate!=null">
				END_DATE = #{endDate},
			</if>
			<if test="curIntAccrued!=null">
				CUR_INT_ACCRUED = #{curIntAccrued},
			</if>
			<if test="curIntAdj!=null">
				CUR_INT_ADJ = #{curIntAdj},
			</if>
			<if test="curTaxAccrued!=null">
				CUR_TAX_ACCRUED = #{curTaxAccrued},
			</if>
			<if test="afterIntAccrued!=null">
				AFTER_INT_ACCRUED = #{afterIntAccrued},
			</if>
			<if test="afterIntAdj!=null">
				AFTER_INT_ADJ = #{afterIntAdj},
			</if>
			<if test="afterTaxAccrued!=null">
				AFTER_TAX_ACCRUED = #{afterTaxAccrued},
			</if>
			<if test="intAmt != null">
				INT_AMT=#{intAmt},
			</if>
			<if test="tdAccrIntDay != null">
				TD_ACCR_INT_DAY=#{tdAccrIntDay},
			</if>
			<if test="tdEndAccrDate != null">
				TD_END_ACCR_DATE=#{tdEndAccrDate},
			</if>
			<if test="tdIntNumDays != null">
				TD_INT_NUM_DAYS=#{tdIntNumDays},
			</if>
			<if test="tdLastAccrDate != null">
				TD_LAST_ACCR_DATE=#{tdLastAccrDate},
			</if>
			<if test="calcBeginDate != null">
				CALC_BEGIN_DATE=#{calcBeginDate},
			</if>
			<if test="realRate != null">
				REAL_RATE=#{realRate},
			</if>
		</set>
		WHERE INTERNAL_KEY = #{internalKey}
		AND START_DATE = #{startDate,jdbcType=DATE}
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>
	<delete id="deleteStartDate" parameterType="java.util.Map">
		DELETE FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{INTERNAL_KEY}
		  and INT_CLASS = #{INT_CLASS}
		<![CDATA[
		  and START_DATE > #{START_DATE,jdbcType=DATE}
		 ]]>
		  <if test="clientNo!=null">
		  AND CLIENT_NO = #{clientNo}
		  </if>
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
    </delete>
	<select id="selectBatchDate" parameterType="java.util.Map"
			resultMap="RbIntSplitMap">
		select
		<include refid="Base_Column_List_mat"/>
		from
		RB_INT_SPLIT mat, RB_acct ma
		where ma.ACCT_STATUS != 'C'
		and ma.main_bal_flag = 'Y'
		and ma.main_int_flag = 'Y'
		and ma.int_ind = 'Y'
		and mat.end_date is null
		and (ma.source_module != 'CL' OR (ma.source_module = 'CL' and ma.lead_acct_flag !='Y'))
		and mat.internal_key = ma.internal_key
		AND mat.internal_key BETWEEN #{START_KEY} and #{END_KEY}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND mat.COMPANY = #{company}
		</if>
		ORDER BY INTERNAL_KEY
	</select>
	<select id="getSplitList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		  AND INT_CLASS = #{intClass}
			<if test="clientNo!=null">
		  AND CLIENT_NO = #{clientNo}
			</if>
			<if test="company != null and company != '' ">
		  AND COMPANY = #{company}
			</if>
		<![CDATA[
		  AND START_DATE >= #{startDate,jdbcType=DATE}
		  AND (END_DATE <= #{endDate} or END_DATE is null)
		ORDER BY START_DATE DESC
		]]>
    </select>
	<select id="getSplitListBefore" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		  AND INT_CLASS = #{intClass}
		<if test="clientNo!=null">
		  AND CLIENT_NO = #{clientNo}
		</if>
		<![CDATA[
		  AND START_DATE < #{startDate,jdbcType=DATE}
		]]>
		<if test="company != null and company != '' ">
		  AND COMPANY = #{company}
		</if>
		ORDER BY START_DATE DESC
    </select>
	<select id="getHistList" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		select a.* from(
		<if test="endDate!=null">
			SELECT
			SYSTEM_ID,
			INTERNAL_KEY,
			START_DATE,
			#{endDate}  END_DATE,
			INT_CLASS,
			BASE_ACCT_NO,
			PROD_TYPE,
			EVENT_TYPE,
			TRAN_BRANCH,
			SOURCE_TYPE,
			CCY,
			INT_CALC_BAL,
			CLIENT_NO,
			CR_RATING,
			IRL_GENE_STR,
			INT_AMT,
			CUR_INT_ACCRUED,
			CUR_INT_ADJ,
			CUR_TAX_ACCRUED,
			TAX_TYPE,
			TAX_RATE,
			PRE_INT_ACCRUED,
			AFTER_INT_ACCRUED,
			PRE_INT_ADJ,
			AFTER_INT_ADJ,
			PRE_TAX_ACCRUED,
			AFTER_TAX_ACCRUED,
			COMPANY,
			MONTH_BASIS,
			YEAR_BASIS,
			TRAN_TIMESTAMP,


			INT_TYPE,
			ACTUAL_RATE,
			FLOAT_RATE,
			REAL_RATE,
			ACCT_SPREAD_RATE,
			ACCT_PERCENT_RATE,
			ACCT_FIXED_RATE,
			AGREE_CHANGE_TYPE,
			AGREE_FIXED_RATE,
			AGREE_PERCENT_RATE,
			AGREE_SPREAD_RATE,
			AGREE_REDUCE_AMT,
			SPLIT_RATE_FLAG
			FROM
			RB_INT_SPLIT
			WHERE
			INTERNAL_KEY = #{internalKey}
			AND INT_CLASS = #{intClass}
			<if test="clientNo!=null">
				AND CLIENT_NO = #{clientNo}
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			AND #{endDate,jdbcType=DATE}>= START_DATE
			AND END_DATE is null
			UNION
		</if>
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		AND INT_CLASS = #{intClass}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND START_DATE >= #{startDate,jdbcType=DATE}
		AND  #{endDate} >= END_DATE
		) a
		ORDER BY START_DATE ASC
	</select>
<!--	<select id="getHistListByPage" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper"-->
<!--			resultMap="RbIntSplitMap">-->
<!--        <![CDATA[-->
<!--		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT-->
<!--		WHERE INTERNAL_KEY = #{baseParam.internalKey}-->
<!--		  AND INT_CLASS = #{baseParam.intClass}-->
<!--		  AND START_DATE >= #{baseParam.startDate}-->
<!--		  AND END_DATE <= #{baseParam.endDate}-->
<!--			<if test="clientNo!=null">-->
<!--		  AND CLIENT_NO = #{clientNo}-->
<!--			</if>-->
<!--		ORDER BY START_DATE ASC-->
<!--		]]>-->
<!--    </select>-->
	<select id="getSplitListEnd" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		  AND INT_CLASS = #{intClass}
		  <![CDATA[
		  AND END_DATE > #{startDate}
		  AND END_DATE < #{endDate}
		  ]]>
		  <if test="clientNo!=null">
		  AND CLIENT_NO = #{clientNo}
			</if>
			<if test="company != null and company != '' ">
		  AND COMPANY = #{company}
			</if>
		ORDER BY START_DATE ASC
    </select>
	<delete id="deleteAll" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		DELETE FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		  AND INT_CLASS = #{intClass}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</delete>
	<select id="selectSplitAndHist" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT *
		FROM (SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF,TD_LAST_ACCR_DATE,TD_ACCR_INT_DAY,TD_INT_NUM_DAYS,TD_END_ACCR_DATE
			  FROM RB_INT_SPLIT
			  WHERE INTERNAL_KEY = #{internalKey}
			    <![CDATA[
				AND START_DATE > = #{startDate,jdbcType=DATE}
				AND (END_DATE IS NULL OR END_DATE < =  #{endDate})
				AND (INT_AMT > 0 OR CUR_INT_ADJ > 0)
				]]>
				<if test="clientNo!=null">
				AND CLIENT_NO = #{clientNo}
				</if>

				<if test="company != null and company != '' ">
				AND COMPANY = #{company}
				</if>
			   )
		ORDER BY START_DATE DESC
    </select>
	<select id="existSplitInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit" resultType="Integer">
		select 1 from RB_INT_SPLIT where INTERNAL_KEY = #{internalKey}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectSplitInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit" resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE
			INTERNAL_KEY = #{internalKey}
		  and INT_CLASS = #{intClass}
		  and END_DATE is null
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectLastSplitInfo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit" resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE
			INTERNAL_KEY = #{internalKey}
		  and INT_CLASS = #{intClass}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		order by start_date desc
	</select>
	<select id="selectSplitLists" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit"
			resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE
			INTERNAL_KEY = #{internalKey}
		  and INT_CLASS = #{intClass}
		  and SYSTEM_ID =#{systemId}
		  <![CDATA[
		  and START_DATE<=#{startDate,jdbcType=DATE}
		  ]]>
			<if test="clientNo!=null">
		  AND CLIENT_NO = #{clientNo}
			</if>

			<if test="company != null and company != '' ">
		  AND COMPANY = #{company}
			</if>

    </select>

	<select id="selectSplitByDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit" resultMap="RbIntSplitMap">
		SELECT SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		  AND START_DATE = #{startDate,jdbcType=DATE}
		  AND INT_CLASS = #{intClass}
		  and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<update id="updateIntAccrued" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		UPDATE RB_INT_SPLIT
		<set>
			<if test="curIntAccrued!=null">
				CUR_INT_ACCRUED =#{curIntAccrued},
			</if>
			<if test="curIntAdj!=null">
				CUR_INT_ADJ =#{curIntAdj},
			</if>
			<if test="curTaxAccrued!=null">
				CUR_TAX_ACCRUED =#{curTaxAccrued},
			</if>
			<if test="tdIntNumDays!=null">
				TD_INT_NUM_DAYS =#{tdIntNumDays},
			</if>
			<if test="tdAccrIntDay!=null">
				TD_ACCR_INT_DAY =#{tdAccrIntDay},
			</if>
			<if test="calcBeginDate!=null">
				CALC_BEGIN_DATE =#{calcBeginDate},
			</if>
			<if test="tdEndAccrDate!=null">
				TD_END_ACCR_DATE =#{tdEndAccrDate},
			</if>
			<if test="tdLastAccrDate!=null">
				TD_LAST_ACCR_DATE =#{tdLastAccrDate},
			</if>
		</set>
		WHERE INTERNAL_KEY = #{internalKey}
		AND START_DATE = #{startDate,jdbcType=DATE}
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<select id="getIrlIntSplitByEffectDate" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbIntSplitBak"
			resultMap="RbIntSplitMap">
		SELECT
			SYSTEM_ID,
			INTERNAL_KEY,
			START_DATE,
			END_DATE,
			INT_CLASS,
			INT_AMT,
			MONTH_BASIS,
			YEAR_BASIS,
			INT_TYPE,
			ACTUAL_RATE,
			FLOAT_RATE,
			REAL_RATE,
			SPLIT_RATE_FLAG,
			COMPANY,
			TRAN_TIMESTAMP,
		FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		  AND (END_DATE > #{endDate} or END_DATE is null)
		  AND INT_CLASS = #{intClass}
		  AND SYSTEM_ID = #{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY START_DATE
	</select>

	<update id="updateForReverse" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		UPDATE RB_INT_SPLIT
		<set>
			END_DATE = #{endDate},
			<if test="curIntAdj!=null">
				CUR_INT_ADJ = #{curIntAdj},
			</if>
			<if test="afterIntAccrued!=null">
				AFTER_INT_ACCRUED = #{afterIntAccrued},
			</if>
			<if test="afterIntAdj!=null">
				AFTER_INT_ADJ = #{afterIntAdj},
			</if>
		</set>
		WHERE INTERNAL_KEY = #{internalKey}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		AND START_DATE =
		(SELECT
		MAX(START_DATE)
		FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		)
		AND END_DATE = #{queryEndDate}
	</update>


	<update id="updateForAmt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		UPDATE RB_INT_SPLIT
		<set>
			<if test="intAmt!=null">
				INT_AMT = #{intAmt},
			</if>
		</set>
		WHERE INTERNAL_KEY = #{internalKey}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		AND START_DATE =
		(SELECT
		MAX(START_DATE)
		FROM RB_INT_SPLIT
		WHERE INTERNAL_KEY = #{internalKey}
		AND INT_CLASS = #{intClass}
		and SYSTEM_ID =#{systemId}
		<if test="clientNo!=null">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		)
		AND END_DATE IS NULL
	</update>

	<select id="selectRbhqBatchDate" parameterType="java.util.Map"
			resultMap="RbIntSplitMap">
		select
		<include refid="Base_Column_List_mat"/>
		from
		RB_INT_SPLIT mat, RB_acct ma
		where ma.ACCT_STATUS != 'C'
		and ma.main_bal_flag = 'Y'
		and ma.main_int_flag = 'Y'
		and ma.int_ind = 'Y'
		and mat.end_date is null
		AND ma.ACCT_TYPE IN('C','S')
		and ma.source_module != 'CL'
		and mat.internal_key = ma.internal_key
		AND mat.internal_key BETWEEN #{START_KEY} and #{END_KEY}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND mat.COMPANY = #{company}
		</if>
		ORDER BY INTERNAL_KEY
	</select>

	<select id="selectRbdqBatchDate" parameterType="java.util.Map"
			resultMap="RbIntSplitMap">
		select
		<include refid="Base_Column_List_mat"/>
		from
		RB_INT_SPLIT mat, RB_acct ma
		where ma.ACCT_STATUS != 'C'
		and ma.main_bal_flag = 'Y'
		and ma.main_int_flag = 'Y'
		and ma.int_ind = 'Y'
		and mat.end_date is null
		AND ma.ACCT_TYPE = 'T'
		and ma.source_module != 'CL'
		and mat.internal_key = ma.internal_key
		AND mat.internal_key BETWEEN #{START_KEY} and #{END_KEY}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND mat.COMPANY = #{company}
		</if>
		ORDER BY INTERNAL_KEY
	</select>

	<select id="selectClBatchDate" parameterType="java.util.Map"
			resultMap="RbIntSplitMap">
		select
		<include refid="Base_Column_List_mat"/>
		from
		RB_INT_SPLIT mat, RB_acct ma
		where ma.ACCT_STATUS != 'C'
		and ma.main_bal_flag = 'Y'
		and ma.main_int_flag = 'Y'
		and ma.int_ind = 'Y'
		and mat.end_date is null
		and ma.source_module = 'CL' and ma.lead_acct_flag !='Y'
		and mat.internal_key = ma.internal_key
		AND mat.internal_key BETWEEN #{START_KEY} and #{END_KEY}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND mat.COMPANY = #{company}
		</if>
		ORDER BY INTERNAL_KEY
	</select>


	<select id="getMaxStartDateInt" parameterType="java.util.HashMap" resultMap="RbIntSplitMap">
		select
		SYSTEM_ID,INTERNAL_KEY,START_DATE,END_DATE,INT_CLASS,BASE_ACCT_NO,PROD_TYPE,EVENT_TYPE,TRAN_BRANCH,SOURCE_TYPE,CCY,INT_CALC_BAL,CLIENT_NO,CR_RATING,IRL_GENE_STR,INT_AMT,     CUR_INT_ACCRUED,CUR_INT_ADJ,CUR_TAX_ACCRUED,TAX_TYPE,TAX_RATE,PRE_INT_ACCRUED,AFTER_INT_ACCRUED,PRE_INT_ADJ,AFTER_INT_ADJ,PRE_TAX_ACCRUED,AFTER_TAX_ACCRUED,COMPANY,MONTH_BASIS,YEAR_BASIS,TRAN_TIMESTAMP,INT_TYPE,ACTUAL_RATE,FLOAT_RATE,REAL_RATE,ACCT_SPREAD_RATE,ACCT_PERCENT_RATE,ACCT_FIXED_RATE,AGREE_CHANGE_TYPE,AGREE_FIXED_RATE,AGREE_PERCENT_RATE,AGREE_SPREAD_RATE,AGREE_REDUCE_AMT,SPLIT_RATE_FLAG,PRE_INT_ACCRUED_DIFF,AFTER_INT_ACCRUED_DIFF
		FROM
		RB_INT_SPLIT
		where 1=1
		<if test="internalKey != null and internalKey != '' ">
			and INTERNAL_KEY = #{internalKey}
		</if>
		<if test="intClass != null and intClass != '' ">
			and INT_CLASS = #{intClass}
		</if>
		<if test="clientNo != null and clientNo != '' ">
			and CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		order by START_DATE DESC
	</select>
	<resultMap id="RbIntSplitMap" type="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		<result column="START_DATE" property="startDate" jdbcType="VARCHAR" javaType="String" />
	</resultMap>

	<sql id="Base_Where_By_Date">
		<trim suffixOverrides="AND">
			<if test="systemId != null and  systemId != '' ">
				SYSTEM_ID = #{systemId}  AND
			</if>
			<if test="internalKey != null ">
				INTERNAL_KEY = #{internalKey}  AND
			</if>
			<if test="startDate != null ">
				START_DATE <![CDATA[ >= ]]> #{startDate,jdbcType=DATE} AND
			</if>
			<if test="endDate != null ">
				START_DATE <![CDATA[ < ]]> #{endDate,jdbcType=DATE} AND
			</if>
			<if test="intClass != null and  intClass != '' ">
				INT_CLASS = #{intClass}  AND
			</if>
			<if test="baseAcctNo != null and  baseAcctNo != '' ">
				BASE_ACCT_NO = #{baseAcctNo}  AND
			</if>
			<if test="prodType != null and  prodType != '' ">
				PROD_TYPE = #{prodType}  AND
			</if>
			<if test="eventType != null and  eventType != '' ">
				EVENT_TYPE = #{eventType}  AND
			</if>
			<if test="tranBranch != null and  tranBranch != '' ">
				TRAN_BRANCH = #{tranBranch}  AND
			</if>
			<if test="sourceType != null and  sourceType != '' ">
				SOURCE_TYPE = #{sourceType}  AND
			</if>
			<if test="ccy != null and  ccy != '' ">
				CCY = #{ccy}  AND
			</if>
			<if test="intCalcBal != null and  intCalcBal != '' ">
				INT_CALC_BAL = #{intCalcBal}  AND
			</if>
			<if test="clientNo != null and  clientNo != '' ">
				CLIENT_NO = #{clientNo}  AND
			</if>
			<if test="crRating != null and  crRating != '' ">
				CR_RATING = #{crRating}  AND
			</if>
			<if test="irlGeneStr != null and  irlGeneStr != '' ">
				IRL_GENE_STR = #{irlGeneStr}  AND
			</if>
			<if test="intAmt != null ">
				INT_AMT = #{intAmt}  AND
			</if>
			<if test="curIntAccrued != null ">
				CUR_INT_ACCRUED = #{curIntAccrued}  AND
			</if>
			<if test="curIntAdj != null ">
				CUR_INT_ADJ = #{curIntAdj}  AND
			</if>
			<if test="curTaxAccrued != null ">
				CUR_TAX_ACCRUED = #{curTaxAccrued}  AND
			</if>
			<if test="taxType != null and  taxType != '' ">
				TAX_TYPE = #{taxType}  AND
			</if>
			<if test="taxRate != null ">
				TAX_RATE = #{taxRate}  AND
			</if>
			<if test="preIntAccrued != null ">
				PRE_INT_ACCRUED = #{preIntAccrued}  AND
			</if>
			<if test="afterIntAccrued != null ">
				AFTER_INT_ACCRUED = #{afterIntAccrued}  AND
			</if>
			<if test="preIntAdj != null ">
				PRE_INT_ADJ = #{preIntAdj}  AND
			</if>
			<if test="afterIntAdj != null ">
				AFTER_INT_ADJ = #{afterIntAdj}  AND
			</if>
			<if test="preTaxAccrued != null ">
				PRE_TAX_ACCRUED = #{preTaxAccrued}  AND
			</if>
			<if test="afterTaxAccrued != null ">
				AFTER_TAX_ACCRUED = #{afterTaxAccrued}  AND
			</if>
			<if test="company != null and  company != '' ">
				COMPANY = #{company}  AND
			</if>
			<if test="monthBasis != null and  monthBasis != '' ">
				MONTH_BASIS = #{monthBasis}  AND
			</if>
			<if test="yearBasis != null and  yearBasis != '' ">
				YEAR_BASIS = #{yearBasis}  AND
			</if>
			<if test="intType != null and  intType != '' ">
				INT_TYPE = #{intType}  AND
			</if>
			<if test="actualRate != null ">
				ACTUAL_RATE = #{actualRate}  AND
			</if>
			<if test="floatRate != null ">
				FLOAT_RATE = #{floatRate}  AND
			</if>
			<if test="realRate != null ">
				REAL_RATE = #{realRate}  AND
			</if>
			<if test="acctSpreadRate != null ">
				ACCT_SPREAD_RATE = #{acctSpreadRate}  AND
			</if>
			<if test="acctPercentRate != null ">
				ACCT_PERCENT_RATE = #{acctPercentRate}  AND
			</if>
			<if test="acctFixedRate != null ">
				ACCT_FIXED_RATE = #{acctFixedRate}  AND
			</if>
			<if test="agreeChangeType != null and  agreeChangeType != '' ">
				AGREE_CHANGE_TYPE = #{agreeChangeType}  AND
			</if>
			<if test="agreeFixedRate != null ">
				AGREE_FIXED_RATE = #{agreeFixedRate}  AND
			</if>
			<if test="agreePercentRate != null ">
				AGREE_PERCENT_RATE = #{agreePercentRate}  AND
			</if>
			<if test="agreeSpreadRate != null ">
				AGREE_SPREAD_RATE = #{agreeSpreadRate}  AND
			</if>
			<if test="splitRateFlag != null and  splitRateFlag != '' ">
				SPLIT_RATE_FLAG = #{splitRateFlag}  AND
			</if>
			<if test="afterIntAccruedDiff != null ">
				AFTER_INT_ACCRUED_DIFF = #{afterIntAccruedDiff}  AND
			</if>
			<if test="preIntAccruedDiff != null ">
				PRE_INT_ACCRUED_DIFF = #{preIntAccruedDiff}  AND
			</if>
			<if test="agreeReduceAmt != null ">
				AGREE_REDUCE_AMT = #{agreeReduceAmt}  AND
			</if>
			<if test="tdLastAccrDate != null  ">
				TD_LAST_ACCR_DATE = #{tdLastAccrDate}  AND
			</if>
			<if test="tdAccrIntDay != null and  tdAccrIntDay != '' ">
				TD_ACCR_INT_DAY = #{tdAccrIntDay}  AND
			</if>
			<if test="tdIntNumDays != null and  tdIntNumDays != '' ">
				TD_INT_NUM_DAYS = #{tdIntNumDays}  AND
			</if>
			<if test="calcBeginDate != null  ">
				CALC_BEGIN_DATE = #{calcBeginDate}  AND
			</if>
			<if test="tdEndAccrDate != null  ">
				TD_END_ACCR_DATE = #{tdEndAccrDate}  AND
			</if>
			<if test="tranTimestamp != null ">
				TRAN_TIMESTAMP = #{tranTimestamp} AND
			</if>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
				COMPANY = #{company} AND
			</if>
			1=1 ORDER BY START_DATE ASC
		</trim>
	</sql>

	<sql id="getAllIntSplitList">
		SELECT
		<include refid="Base_Column" />
		FROM
		<include refid="Table_Name" />

		<include refid="Base_Where_by_data" />
	</sql>


	<sql id="Base_Where_by_data">
		<where>
			<if test="intClass != null and  intClass != '' ">
				AND INT_CLASS = #{intClass,jdbcType=VARCHAR}
			</if>
			<if test="internalKey != null ">
				AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
			</if>
			<if test="startDate != null ">
				AND START_DATE = #{startDate,jdbcType=DATE}
			</if>
			<if test="systemId != null and  systemId != '' ">
				AND SYSTEM_ID = #{systemId,jdbcType=VARCHAR}
			</if>
			<if test="clientNo != null and  clientNo != '' ">
				AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
			</if>
			<if test="prodType != null and  prodType != '' ">
				AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
			</if>
			<if test="baseAcctNo != null and  baseAcctNo != '' ">
				AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
			</if>
			<if test="acctFixedRate != null ">
				AND ACCT_FIXED_RATE = #{acctFixedRate,jdbcType=DECIMAL}
			</if>
			<if test="acctPercentRate != null ">
				AND ACCT_PERCENT_RATE = #{acctPercentRate,jdbcType=DECIMAL}
			</if>
			<if test="acctSpreadRate != null ">
				AND ACCT_SPREAD_RATE = #{acctSpreadRate,jdbcType=DECIMAL}
			</if>
			<if test="calcBeginDate != null ">
				AND CALC_BEGIN_DATE = #{calcBeginDate,jdbcType=DATE}
			</if>
			<if test="endDate != null ">
				AND END_DATE = #{endDate,jdbcType=DATE}
			</if>
			<if test="tdEndAccrDate != null ">
				AND TD_END_ACCR_DATE = #{tdEndAccrDate,jdbcType=DATE}
			</if>
			<if test="tdLastAccrDate != null ">
				AND TD_LAST_ACCR_DATE = #{tdLastAccrDate,jdbcType=DATE}
			</if>
			<if test="tranBranch != null and  tranBranch != '' ">
				AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
			</if>
			<if test="agreeReduceAmt != null ">
				AND AGREE_REDUCE_AMT = #{agreeReduceAmt,jdbcType=DECIMAL}
			</if>
			<if test="ccy != null and  ccy != '' ">
				AND CCY = #{ccy,jdbcType=VARCHAR}
			</if>
			<if test="intAmt != null ">
				AND INT_AMT = #{intAmt,jdbcType=DECIMAL}
			</if>
			<if test="intCalcBal != null and  intCalcBal != '' ">
				AND INT_CALC_BAL = #{intCalcBal,jdbcType=VARCHAR}
			</if>
			<if test="intType != null and  intType != '' ">
				AND INT_TYPE = #{intType,jdbcType=VARCHAR}
			</if>
			<if test="actualRate != null ">
				AND ACTUAL_RATE = #{actualRate,jdbcType=DECIMAL}
			</if>
			<if test="afterIntAccrued != null ">
				AND AFTER_INT_ACCRUED = #{afterIntAccrued,jdbcType=DECIMAL}
			</if>
			<if test="afterIntAccruedDiff != null ">
				AND AFTER_INT_ACCRUED_DIFF = #{afterIntAccruedDiff,jdbcType=DECIMAL}
			</if>
			<if test="afterIntAdj != null ">
				AND AFTER_INT_ADJ = #{afterIntAdj,jdbcType=DECIMAL}
			</if>
			<if test="agreeFixedRate != null ">
				AND AGREE_FIXED_RATE = #{agreeFixedRate,jdbcType=DECIMAL}
			</if>
			<if test="agreeSpreadRate != null ">
				AND AGREE_SPREAD_RATE = #{agreeSpreadRate,jdbcType=DECIMAL}
			</if>
			<if test="curIntAccrued != null ">
				AND CUR_INT_ACCRUED = #{curIntAccrued,jdbcType=DECIMAL}
			</if>
			<if test="curIntAdj != null ">
				AND CUR_INT_ADJ = #{curIntAdj,jdbcType=DECIMAL}
			</if>
			<if test="floatRate != null ">
				AND FLOAT_RATE = #{floatRate,jdbcType=DECIMAL}
			</if>
			<if test="preIntAccrued != null ">
				AND PRE_INT_ACCRUED = #{preIntAccrued,jdbcType=DECIMAL}
			</if>
			<if test="preIntAccruedDiff != null ">
				AND PRE_INT_ACCRUED_DIFF = #{preIntAccruedDiff,jdbcType=DECIMAL}
			</if>
			<if test="preIntAdj != null ">
				AND PRE_INT_ADJ = #{preIntAdj,jdbcType=DECIMAL}
			</if>
			<if test="realRate != null ">
				AND REAL_RATE = #{realRate,jdbcType=DECIMAL}
			</if>
			<if test="tdAccrIntDay != null and  tdAccrIntDay != '' ">
				AND TD_ACCR_INT_DAY = #{tdAccrIntDay,jdbcType=VARCHAR}
			</if>
			<if test="tdIntNumDays != null ">
				AND TD_INT_NUM_DAYS = #{tdIntNumDays,jdbcType=INTEGER}
			</if>
			<if test="afterTaxAccrued != null ">
				AND AFTER_TAX_ACCRUED = #{afterTaxAccrued,jdbcType=DECIMAL}
			</if>
			<if test="curTaxAccrued != null ">
				AND CUR_TAX_ACCRUED = #{curTaxAccrued,jdbcType=DECIMAL}
			</if>
			<if test="preTaxAccrued != null ">
				AND PRE_TAX_ACCRUED = #{preTaxAccrued,jdbcType=DECIMAL}
			</if>
			<if test="taxRate != null ">
				AND TAX_RATE = #{taxRate,jdbcType=DECIMAL}
			</if>
			<if test="taxType != null and  taxType != '' ">
				AND TAX_TYPE = #{taxType,jdbcType=VARCHAR}
			</if>
			<if test="agreeChangeType != null and  agreeChangeType != '' ">
				AND AGREE_CHANGE_TYPE = #{agreeChangeType,jdbcType=VARCHAR}
			</if>
			<if test="eventType != null and  eventType != '' ">
				AND EVENT_TYPE = #{eventType,jdbcType=VARCHAR}
			</if>
			<if test="sourceType != null and  sourceType != '' ">
				AND SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
			</if>
			<if test="splitRateFlag != null and  splitRateFlag != '' ">
				AND SPLIT_RATE_FLAG = #{splitRateFlag,jdbcType=VARCHAR}
			</if>
			<if test="agreePercentRate != null ">
				AND AGREE_PERCENT_RATE = #{agreePercentRate,jdbcType=DECIMAL}
			</if>
			<if test="crRating != null and  crRating != '' ">
				AND CR_RATING = #{crRating,jdbcType=VARCHAR}
			</if>
			<if test="irlGeneStr != null and  irlGeneStr != '' ">
				AND IRL_GENE_STR = #{irlGeneStr,jdbcType=VARCHAR}
			</if>
			<if test="monthBasis != null and  monthBasis != '' ">
				AND MONTH_BASIS = #{monthBasis,jdbcType=VARCHAR}
			</if>
			<if test="yearBasis != null and  yearBasis != '' ">
				AND YEAR_BASIS = #{yearBasis,jdbcType=VARCHAR}
			</if>
			<if test="tranTimestamp != null and  tranTimestamp != '' ">
				AND TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR}
			</if>
			<if test="company != null and  company != '' ">
				AND COMPANY = #{company,jdbcType=VARCHAR}
			</if>
			AND 1=1 ORDER BY START_DATE DESC
		</where>
	</sql>


	<select id="getIntSplitList" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		<include refid="getAllIntSplitList" />
	</select>



	<sql id="Base_Set1">
		<set>
			<if test="intClass != null and intClass != '' ">
				INT_CLASS = #{intClass,jdbcType=VARCHAR},
			</if>
			<if test="internalKey != null ">
				INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},
			</if>
			<if test="startDate != null ">
				START_DATE = #{startDate,jdbcType=DATE},
			</if>
			<if test="systemId != null and systemId != '' ">
				SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
			</if>
			<if test="prodType != null and prodType != '' ">
				PROD_TYPE = #{prodType,jdbcType=VARCHAR},
			</if>
			<if test="baseAcctNo != null and baseAcctNo != '' ">
				BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
			</if>

				ACCT_FIXED_RATE = #{acctFixedRate,jdbcType=DECIMAL},

				ACCT_PERCENT_RATE = #{acctPercentRate,jdbcType=DECIMAL},

				ACCT_SPREAD_RATE = #{acctSpreadRate,jdbcType=DECIMAL},

			<if test="calcBeginDate != null ">
				CALC_BEGIN_DATE = #{calcBeginDate,jdbcType=DATE},
			</if>
			<if test="endDate != null ">
				END_DATE = #{endDate,jdbcType=DATE},
			</if>
			<if test="tdEndAccrDate != null ">
				TD_END_ACCR_DATE = #{tdEndAccrDate,jdbcType=DATE},
			</if>
			<if test="tdLastAccrDate != null ">
				TD_LAST_ACCR_DATE = #{tdLastAccrDate,jdbcType=DATE},
			</if>
			<if test="tranBranch != null and tranBranch != '' ">
				TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
			</if>
			<if test="agreeReduceAmt != null ">
				AGREE_REDUCE_AMT = #{agreeReduceAmt,jdbcType=DECIMAL},
			</if>
			<if test="ccy != null and ccy != '' ">
				CCY = #{ccy,jdbcType=VARCHAR},
			</if>
			<if test="intAmt != null ">
				INT_AMT = #{intAmt,jdbcType=DECIMAL},
			</if>
			<if test="intCalcBal != null and intCalcBal != '' ">
				INT_CALC_BAL = #{intCalcBal,jdbcType=VARCHAR},
			</if>
			<if test="intType != null and intType != '' ">
				INT_TYPE = #{intType,jdbcType=VARCHAR},
			</if>
			<if test="actualRate != null ">
				ACTUAL_RATE = #{actualRate,jdbcType=DECIMAL},
			</if>
			<if test="afterIntAccrued != null ">
				AFTER_INT_ACCRUED = #{afterIntAccrued,jdbcType=DECIMAL},
			</if>
			<if test="afterIntAccruedDiff != null ">
				AFTER_INT_ACCRUED_DIFF = #{afterIntAccruedDiff,jdbcType=DECIMAL},
			</if>
			<if test="afterIntAdj != null ">
				AFTER_INT_ADJ = #{afterIntAdj,jdbcType=DECIMAL},
			</if>
			<if test="agreeFixedRate != null ">
				AGREE_FIXED_RATE = #{agreeFixedRate,jdbcType=DECIMAL},
			</if>
			<if test="agreeSpreadRate != null ">
				AGREE_SPREAD_RATE = #{agreeSpreadRate,jdbcType=DECIMAL},
			</if>
			<if test="curIntAccrued != null ">
				CUR_INT_ACCRUED = #{curIntAccrued,jdbcType=DECIMAL},
			</if>
			<if test="curIntAdj != null ">
				CUR_INT_ADJ = #{curIntAdj,jdbcType=DECIMAL},
			</if>
			<if test="floatRate != null ">
				FLOAT_RATE = #{floatRate,jdbcType=DECIMAL},
			</if>
			<if test="preIntAccrued != null ">
				PRE_INT_ACCRUED = #{preIntAccrued,jdbcType=DECIMAL},
			</if>
			<if test="preIntAccruedDiff != null ">
				PRE_INT_ACCRUED_DIFF = #{preIntAccruedDiff,jdbcType=DECIMAL},
			</if>
			<if test="preIntAdj != null ">
				PRE_INT_ADJ = #{preIntAdj,jdbcType=DECIMAL},
			</if>
			<if test="realRate != null ">
				REAL_RATE = #{realRate,jdbcType=DECIMAL},
			</if>
			<if test="tdAccrIntDay != null and tdAccrIntDay != '' ">
				TD_ACCR_INT_DAY = #{tdAccrIntDay,jdbcType=VARCHAR},
			</if>
			<if test="tdIntNumDays != null ">
				TD_INT_NUM_DAYS = #{tdIntNumDays,jdbcType=INTEGER},
			</if>
			<if test="afterTaxAccrued != null ">
				AFTER_TAX_ACCRUED = #{afterTaxAccrued,jdbcType=DECIMAL},
			</if>
			<if test="curTaxAccrued != null ">
				CUR_TAX_ACCRUED = #{curTaxAccrued,jdbcType=DECIMAL},
			</if>
			<if test="preTaxAccrued != null ">
				PRE_TAX_ACCRUED = #{preTaxAccrued,jdbcType=DECIMAL},
			</if>
			<if test="taxRate != null ">
				TAX_RATE = #{taxRate,jdbcType=DECIMAL},
			</if>
			<if test="taxType != null and taxType != '' ">
				TAX_TYPE = #{taxType,jdbcType=VARCHAR},
			</if>
			<if test="agreeChangeType != null and agreeChangeType != '' ">
				AGREE_CHANGE_TYPE = #{agreeChangeType,jdbcType=VARCHAR},
			</if>
			<if test="eventType != null and eventType != '' ">
				EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
			</if>
			<if test="sourceType != null and sourceType != '' ">
				SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="splitRateFlag != null and splitRateFlag != '' ">
				SPLIT_RATE_FLAG = #{splitRateFlag,jdbcType=VARCHAR},
			</if>
			<if test="agreePercentRate != null ">
				AGREE_PERCENT_RATE = #{agreePercentRate,jdbcType=DECIMAL},
			</if>
			<if test="crRating != null and crRating != '' ">
				CR_RATING = #{crRating,jdbcType=VARCHAR},
			</if>
			<if test="irlGeneStr != null and irlGeneStr != '' ">
				IRL_GENE_STR = #{irlGeneStr,jdbcType=VARCHAR},
			</if>
			<if test="monthBasis != null and monthBasis != '' ">
				MONTH_BASIS = #{monthBasis,jdbcType=VARCHAR},
			</if>
			<if test="yearBasis != null and yearBasis != '' ">
				YEAR_BASIS = #{yearBasis,jdbcType=VARCHAR},
			</if>
			<if test="tranTimestamp != null and tranTimestamp != '' ">
				TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
			</if>
			<if test="company != null and company != '' ">
				COMPANY = #{company,jdbcType=VARCHAR},
			</if>
		</set>
	</sql>

	<update id="updateIntSplitByInternalKey">
		UPDATE
		<include refid="Table_Name"/>
		<include refid="Base_Set1"/>
		<include refid="PrimaryKey_Where"/>
	</update>

	<select id="getSplitByInternalKey" parameterType="java.util.HashMap"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		SELECT	<include refid="Base_Column" />
		FROM RB_INT_SPLIT where 1=1
		<if test="internalKey!=null and internalKey!=''">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		AND 1=1 ORDER BY START_DATE DESC
	</select>

	<select id="getSplitByStarDate" parameterType="java.util.HashMap"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit">
		SELECT	<include refid="Base_Column" />
		FROM RB_INT_SPLIT where 1=1
		<if test="clientNo !=null and clientNo!=''">
			and   CLIENT_NO =#{clientNo,jdbcType=VARCHAR}
		</if>
		<if test="internalKey!=null and internalKey!=''">
			AND INTERNAL_KEY = #{internalKey,jdbcType=VARCHAR}
		</if>
		<if test="cycleRollDate !=null and cycleRollDate!=''">
			AND START_DATE <![CDATA[ <= ]]> #{cycleRollDate,jdbcType=DATE}
		</if>
		AND 1=1 ORDER BY START_DATE DESC
	</select>
</mapper>
