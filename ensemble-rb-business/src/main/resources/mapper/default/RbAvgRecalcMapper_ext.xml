<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAvgRecalc">

    <select id="getRbAvgRecalcNewest" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAvgRecalc" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAvgRecalc" >
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <where>
            <if test="agreementId != null and  agreementId != '' ">
                AND AGREEMENT_ID = #{agreementId,jdbcType=VARCHAR}
            </if>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY TRAN_TIMESTAMP DESC
    </select>

    <update id="cleanNextDealDate">
        UPDATE
        <include refid="Table_Name"/>
        SET NEXT_DEAL_DATE = null
        <include refid="Base_Where"/>
    </update>

</mapper>