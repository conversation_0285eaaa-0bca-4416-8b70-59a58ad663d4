<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_INFO
    where STAGE_CODE = #{stageCode}
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
<!--  <select id="getDcStageInfo" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">-->
<!--    select <include refid="Base_Column"/>-->
<!--    from RB_DC_STAGE_INFO-->
<!--    where-->
<!--    <if test="issueYear != null">-->
<!--      ISSUE_YEAR = #{issueYear}-->
<!--    </if>-->
<!--    <if test="prodType != null">-->
<!--      AND PROD_TYPE = #{prodType}-->
<!--    </if>-->
<!--    <if test="acctCcy != null">-->
<!--      AND CCY = #{acctCcy}-->
<!--    </if>-->
<!--  </select>-->
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">
    delete from RB_DC_STAGE_INFO
    where STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">
    update RB_DC_STAGE_INFO
    <set>
      <if test="issueYear != null">
        ISSUE_YEAR = #{issueYear},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="totalLimit != null">
        TOTAL_LIMIT = #{totalLimit},
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT = #{distributeLimit},
      </if>
      <if test="holdingLimit != null">
        HOLDING_LIMIT = #{holdingLimit},
      </if>
      <if test="prevHoldingLimit != null">
        PREV_HOLDING_LIMIT = #{prevHoldingLimit},
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT = #{leaveLimit},
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE = #{lastChangeDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS = #{stageProdClass}
      </if>
    </set>
    where STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">
    insert into RB_DC_STAGE_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        ISSUE_YEAR,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="totalLimit != null">
        TOTAL_LIMIT,
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT,
      </if>
      <if test="holdingLimit != null">
        HOLDING_LIMIT,
      </if>
      <if test="prevHoldingLimit != null">
        PREV_HOLDING_LIMIT,
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT,
      </if>
      <if test="lastChangeDate != null">
        LAST_CHANGE_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        #{issueYear},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="totalLimit != null">
        #{totalLimit},
      </if>
      <if test="distributeLimit != null">
        #{distributeLimit},
      </if>
      <if test="holdingLimit != null">
        #{holdingLimit},
      </if>
      <if test="prevHoldingLimit != null">
        #{prevHoldingLimit},
      </if>
      <if test="leaveLimit != null">
        #{leaveLimit},
      </if>
      <if test="lastChangeDate != null">
        #{lastChangeDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>


<!--  <select id="getDcStageInfos" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">-->
<!--    select <include refid="Base_Column"/>-->
<!--    from RB_DC_STAGE_INFO-->
<!--    where STAGE_CODE = #{stageCode}-->
<!--    <if test="issueYear != null">-->
<!--      AND ISSUE_YEAR = #{issueYear}-->
<!--    </if>-->
<!--    <if test="prodType != null">-->
<!--      AND PROD_TYPE = #{prodType}-->
<!--    </if>-->
<!--    <if test="acctCcy != null">-->
<!--      AND CCY = #{acctCcy}-->
<!--    </if>-->
<!--  </select>-->

<!--  <select id="geSumByLeaveLimt" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageInfo">-->
<!--    select-->
<!--    sum(HOLDING_LIMIT) as HOLDING_LIMIT,-->
<!--    sum(LEAVE_LIMIT) as LEAVE_LIMIT-->
<!--    from RB_DC_STAGE_INFO-->
<!--    where STAGE_CODE = #{stageCode}-->
<!--    <if test="issueYear != null">-->
<!--      AND ISSUE_YEAR = #{issueYear}-->
<!--    </if>-->
<!--  </select>-->
</mapper>
