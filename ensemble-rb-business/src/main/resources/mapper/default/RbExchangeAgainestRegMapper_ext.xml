<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeAgainestReg">

	<sql id="Base_Column_List">
				BUSI_NO,
				TEAM_BANK_RATE_OLD,
				FORE_CCY,
				FORE_AMT_NEW,
				TRAN_DATE,
				REG_TRAN_DATE,
				TEAM_BANK_RATE_NEW,
				STATUS,
				SEQ_NO,
				OFFER_LETTER_OLD,
				USER_ID,
				<PERSON><PERSON>CH,
				BASE_ACCT_NO,
				CLIENT_NO,
				OPTIONS,
				CLIENT_NAME_CHN,
				EX_TYPE,
				FORE_AMT_OLD,
				CNY_AMT_NEW,
				LOSS_INCOME_FLAG,
				CLIENT_NAME_EN,
				CNY_AMT_OLD,
				OFFER_LETTER_NEW,
				LOSS_INCOME_AMT,
				REFERENCE
	</sql>

	<select id="getBySeqNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeAgainestReg">
		select <include refid="Base_Column_List"/>
		from RB_EXCHANGE_AGAINEST_REG
		<where>
			STATUS = '1' AND
			<trim suffixOverrides="AND">
				<if test="seqNo != null and  seqNo != '' ">
					SEQ_NO = #{seqNo}  AND
				</if>
			</trim>
		</where>
	</select>

	<select id="getRbExchangeAgainestReg" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeAgainestReg">
		select <include refid="Base_Column_List"/>
		from RB_EXCHANGE_AGAINEST_REG
		<where>
			STATUS = '1' AND
			<trim suffixOverrides="AND">
				<if test="regTranDate != null ">
					REG_TRAN_DATE = #{regTranDate}  AND
				</if>
				<if test="clientNo != null  and clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="baseAcctNo != null and baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo}  AND
				</if>
				<if test="clientNameChn != null and clientNameChn != '' ">
					CLIENT_NAME_CHN = #{clientNameChn}  AND
				</if>
				<if test="clientNameEn != null and clientNameEn != '' ">
					CLIENT_NAME_EN = #{clientNameEn}  AND
				</if>
				<if test="exchangeFlag != null and exchangeFlag != '' ">
					EXCHANGE_FLAG = #{exchangeFlag}  AND
				</if>
				<if test="busiNo != null and busiNo != '' ">
					BUSI_NO = #{busiNo}  AND
				</if>
			</trim>
		</where>
	</select>

	<select id="getExChangeAgainestByBusiNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeAgainestReg">
		select <include refid="Base_Column_List"/>
		from RB_EXCHANGE_AGAINEST_REG
		<where>
			<trim suffixOverrides="AND">
				STATUS = '1' AND
				<if test="regTranDate != null ">
					REG_TRAN_DATE = #{regTranDate}  AND
				</if>
				<if test="clientNo != null  and clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="baseAcctNo != null and baseAcctNo != '' ">
					BASE_ACCT_NO = #{baseAcctNo}  AND
				</if>
				<if test="chClientName != null and chClientName != '' ">
					CH_CLIENT_NAME = #{chClientName}  AND
				</if>
				<if test="enClientName != null and enClientName != '' ">
					EN_CLIENT_NAME = #{enClientName}  AND
				</if>
				<if test="exType != null and exType != '' ">
					EX_TYPE = #{exType}  AND
				</if>
				<if test="busiNo != null and busiNo != '' ">
					BUSI_NO = #{busiNo}  AND
				</if>
			</trim>
		</where>
	</select>

	<update id="updateExChangeAgainestBySeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeAgainestReg">
		UPDATE RB_EXCHANGE_AGAINEST_REG
		SET STATUS = '2'
		<where>
			<trim suffixOverrides="AND">
				<if test="seqNo != null and  seqNo != '' ">
					SEQ_NO = #{seqNo}  AND
				</if>
				<if test="clientNo != null and  clientNo != '' ">
					CLIENT_NO = #{clientNo}  AND
				</if>
				<if test="busiNo != null and  busiNo != '' ">
					BUSI_NO = #{busiNo}  AND
				</if>
			</trim>
		</where>
	</update>

	<select id="selectExChangeAgainestReg" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeAgainestReg">
		select <include refid="Base_Column_List"/>
		from RB_EXCHANGE_AGAINEST_REG
		<where>
			<trim suffixOverrides="AND">
				   STATUS = '1'
				<if test="exType != null and exType != '' ">
					AND EX_TYPE = #{exType}
				</if>
				<if test="busiNo != null and busiNo != '' ">
					AND BUSI_NO = #{busiNo}
				</if>
			</trim>
		</where>
	</select>

</mapper>