<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBenefitOwnerInfo">
    <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBenefitOwnerInfo">
        update RB_BENEFIT_OWNER_INFO
        <set>
            <if test="beneficiaryClientNo != null and beneficiaryClientNo != ''">
                BENEFICIARY_CLIENT_NO = #{beneficiaryClientNo},
            </if>
            <if test="userId != null and userId != ''">
                USER_ID = #{userId},
            </if>
            <if test="tranTimestamp != null and tranTimestamp != ''">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        <where>
            SEQ_NO = #{seqNo}
            <if test="clientNo != null and clientNo != ''">
                AND CLIENT_NO = #{clientNo}
            </if>
            <if test="internalKey != null and internalKey != ''">
                AND INTERNAL_KEY = #{internalKey}
            </if>
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </update>
</mapper>
