<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">

    <select id="selectByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where LOST_KEY = #{lostKey,jdbcType=VARCHAR}
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getLostByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where VOUCHER_LOST_STATUS = 'USE'
        and LOST_KEY = #{lostKey,jdbcType=VARCHAR}
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectMbVoucherLost" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where LOST_KEY = #{lostKey,jdbcType=VARCHAR}
        and VOUCHER_LOST_STATUS = 'USE'
        and LOST_TYPE in('VER','FOR')
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectMbVoucherLostByVoucherNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where LOST_KEY = #{lostKey,jdbcType=VARCHAR}
        and VOUCHER_LOST_STATUS = 'USE'
        and LOST_TYPE in('VER','FOR')
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <if test="docType != null">
            and DOC_TYPE= #{docType}
        </if>
        <if test="voucherNo != null">
            and VOUCHER_NO= #{voucherNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbVoucherLostByLostNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where LOST_NO = #{lostNo}
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbVoucherLostByLostNoAndStatus" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where LOST_NO = #{lostNo}
        and VOUCHER_LOST_STATUS='USE'
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getLccInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where LOST_NO = #{lostNo}
        and VOUCHER_LOST_STATUS='USE'
        and LOST_TYPE ='FOR'
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getVerInfo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where LOST_NO = #{lostNo}
        and VOUCHER_LOST_STATUS='USE'
        and LOST_TYPE ='VER'
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getVerInfoByCardNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where VOUCHER_LOST_STATUS='USE'
        <if test="lostKey != null and  lostKey != '' ">
            AND LOST_KEY = #{lostKey,jdbcType=VARCHAR}
        </if>
        <if test="baseAcctNo != null and  baseAcctNo != '' ">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctSeqNo != null and  acctSeqNo != '' ">
            AND ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="clientNo != null and  clientNo != '' ">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getMbVoucherLostByMap" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where 1=1
        and LOST_TYPE in ('VER','FOR')
        <if test="lostStatus != null">
            and VOUCHER_LOST_STATUS= #{lostStatus}
        </if>
        <if test="startDate != null and endDate != null">
            and TRAN_DATE BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="lostKey != null">
            and LOST_KEY = #{lostKey,jdbcType=VARCHAR}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <if test="relieveLossType != null">
            and RELIEVE_LOSS_TYPE= #{relieveLossType}
        </if>
        <if test="tranBranch != null">
            and TRAN_BRANCH= #{tranBranch}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by TRAN_DATE desc
    </select>
    <select id="getMbVoucherLostNoLostKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        SELECT
        mvl.*
        FROM
        RB_VOUCHER_LOST mvl
        where mvl.LOST_TYPE in ('VER','FOR')
        <if test="lostStatus != null">
            and mvl.VOUCHER_LOST_STATUS= #{lostStatus}
        </if>
        <if test="clientNo != null">
            and mvl.CLIENT_NO= #{clientNo}
        </if>
        <if test="relieveLossType != null">
            and mvl.RELIEVE_LOSS_TYPE= #{relieveLossType}
        </if>
        <if test="startDate != null and endDate != null">
            and mvl.TRAN_DATE BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="branch != null">
            and mvl.TRAN_BRANCH= #{branch}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND mvl.DOC_TYPE = '748'
        order by mvl.TRAN_DATE DESC
    </select>

    <select id="getSortedRbVoucherLosts"  parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherLostExt"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        <where>
           <if test="voucherLostStatus != null and voucherLostStatus!='' ">
                and VOUCHER_LOST_STATUS= #{voucherLostStatus}
           </if>
            <if test="lostKey != null and lostKey!='' ">
                and LOST_KEY = #{lostKey}
            </if>
            <if test="lostNo != null and lostNo!='' ">
                and LOST_NO = #{lostNo}
            </if>
            <if test="clientNo != null and clientNo!='' ">
                and CLIENT_NO= #{clientNo}
            </if>
            <if test="baseAcctNo != null and baseAcctNo!='' ">
                and BASE_ACCT_NO= #{baseAcctNo}
            </if>
            <if test="docType != null and docType !='' ">
                and DOC_TYPE= #{docType}
            </if>
            <if test="voucherNo != null and voucherNo != '' ">
                and VOUCHER_NO= #{voucherNo}
            </if>
            <if test="startDate != null">
                AND  TRAN_DATE<![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND  TRAN_DATE<![CDATA[<=]]> #{endDate}
            </if>
        </where>
        order by TRAN_TIMESTAMP desc
    </select>

    <update id="updateAutoUnblockDate" parameterType="java.util.Map">
        update RB_VOUCHER_LOST set AUTO_UNBLOCK_DATE = #{autoUnblockDate}
        where LOST_NO = #{lostNo}
        <if test="clientNo != null">
            and CLIENT_NO= #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="getSortedRbVoucherLostsByTranBranch"  parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherLostExt"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        <where>
            <if test="lostKey != null and lostKey!='' ">
                and LOST_KEY = #{lostKey}
            </if>
            <if test="lostNo != null and lostNo!='' ">
                and LOST_NO = #{lostNo}
            </if>
            <if test="clientNo != null and clientNo!='' ">
                and CLIENT_NO= #{clientNo}
            </if>
            <if test="baseAcctNo != null and baseAcctNo!='' ">
                and (BASE_ACCT_NO= #{baseAcctNo} or LOST_KEY= #{baseAcctNo})
            </if>
            <if test="docType != null and docType !='' ">
                and DOC_TYPE= #{docType}
            </if>
            <if test="voucherNo != null and voucherNo != '' ">
                and VOUCHER_NO= #{voucherNo}
            </if>
            <if test="startDate != null ">
                AND  TRAN_DATE<![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null ">
                AND  TRAN_DATE<![CDATA[<=]]> #{endDate}
            </if>
            <if test="tranBranch != null ">
                AND  TRAN_BRANCH= #{tranBranch}
            </if>
        </where>
        order by TRAN_TIMESTAMP desc
    </select>

    <select id="getRbVoucherLostByBaseAcctNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost">
        select
        <include refid="Base_Column"/>
        from RB_VOUCHER_LOST
        where VOUCHER_LOST_STATUS='USE'
        <if test="voucherNo != null ">
            AND VOUCHER_NO = #{voucherNo}
        </if>
        <if test="baseAcctNo != null ">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
