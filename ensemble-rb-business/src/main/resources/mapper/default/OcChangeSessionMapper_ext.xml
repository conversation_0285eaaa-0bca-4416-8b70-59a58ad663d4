<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeSession">
  <!-- Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/05/10 14:26:05. -->
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeSession">
    insert into OC_CHANGE_SESSION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="changeBranchType != null">
        CHANGE_BRANCH_TYPE,
      </if>
      <if test="changeRegion != null">
        CHANGE_REGION,
      </if>
      <if test="changeSession != null">
        CHANGE_SESSION,
      </if>
      <if test="changeDateFlag != null">
        CHANGE_DATE_FLAG,
      </if>
      <if test="effectFlag != null">
        EFFECT_FLAG,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="changeBranchType != null">
        #{changeBranchType},
      </if>
      <if test="changeRegion != null">
        #{changeRegion},
      </if>
      <if test="changeSession != null">
        #{changeSession},
      </if>
      <if test="changeDateFlag != null">
        #{changeDateFlag},
      </if>
      <if test="effectFlag != null">
        #{effectFlag},
      </if>
    </trim>
  </insert>

  <!--<select id="selectChangeSession" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeSession">-->
    <!--select *-->
      <!--from OC_CHANGE_SESSION-->
     <!--where CHANGE_BRANCH_TYPE = #{changeBranchType}-->
       <!--and CHANGE_SESSION = #{changeSession}-->
  <!--</select>-->
  <!--<select id="getChangeSession" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeSession">-->
    <!--select *-->
    <!--from OC_CHANGE_SESSION-->
    <!--where CHANGE_BRANCH_TYPE = #{changeBranchType}-->
    <!--and CHANGE_REGION = #{changeRegion}-->
    <!--and EFFECT_FLAG = #{effectFlag}-->
  <!--</select>-->
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeSession">
    update OC_CHANGE_SESSION
    <set>
      <if test="changeDateFlag != null">
        CHANGE_DATE_FLAG = #{changeDateFlag},
      </if>
      <if test="effectFlag != null">
        EFFECT_FLAG = #{effectFlag}
      </if>
    </set>
    where CHANGE_REGION = #{changeRegion}
    AND CHANGE_SESSION = #{changeSession}
    AND CHANGE_BRANCH_TYPE = #{changeBranchType}
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.OcChangeSession">
    delete from OC_CHANGE_SESSION
    where CHANGE_REGION = #{changeRegion}
    <if test="changeSession != null">
      AND CHANGE_SESSION = #{changeSession}
    </if>
    <if test="changeBranchType != null">
      AND CHANGE_BRANCH_TYPE = #{changeBranchType}
    </if>
    <!--多法人改造 by LIYUANV-->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
</mapper>
