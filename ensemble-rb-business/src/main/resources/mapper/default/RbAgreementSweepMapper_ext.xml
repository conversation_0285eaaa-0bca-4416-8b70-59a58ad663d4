<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">

  <select id="selectByInternalKey" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_SWEEP mas
    where INTERNAL_KEY = #{internalKey}
    and mas.AGREEMENT_STATUS = 'A'
    and mas.client_no = #{clientNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mas.COMPANY = #{company}
    </if>
  </select>

  <select id="selectByOthInternalKey" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">
    select CON_TRANSFER_COUNT,ACCT_FIXED_RATE,ACCT_PERCENT_RATE,ACCT_SPREAD_RATE,ma.AGREEMENT_ID,INTERNAL_KEY,RENEW_MIN_AMT,OTH_ACCT_CCY,OTH_ACCT_DESC,OTH_ACCT_SEQ_NO,OTH_ACCT_SORT,OTH_BANK_CODE,OTH_BASE_ACCT_NO,OTH_INTERNAL_KEY,OTH_PROD_TYPE,PRIORITY,REASON_CODE,RENEW_METHOD,SEQ_NO,RENEW_TYPE,SCHED_NO,SIGN_DATE,AGREEMENT_STATUS,TERM,TERM_TYPE,TRAN_AMT,ma.TRAN_TIMESTAMP,ma.USER_ID,BAL_RATIO,TRAN_BASE_AMT,RENEW_MULTIPLE
    select CON_TRANSFER_COUNT,ACCT_FIXED_RATE,ACCT_PERCENT_RATE,ACCT_SPREAD_RATE,ma.AGREEMENT_ID,INTERNAL_KEY,RENEW_MIN_AMT,OTH_ACCT_CCY,OTH_ACCT_DESC,OTH_ACCT_SEQ_NO,OTH_ACCT_SORT,OTH_BANK_CODE,OTH_BASE_ACCT_NO,OTH_INTERNAL_KEY,OTH_PROD_TYPE,PRIORITY,REASON_CODE,RENEW_METHOD,SEQ_NO,RENEW_TYPE,SCHED_NO,SIGN_DATE,AGREEMENT_STATUS,TERM,TERM_TYPE,TRAN_AMT,ma.TRAN_TIMESTAMP,ma.USER_ID,BAL_RATIO,TRAN_BASE_AMT,RENEW_MULTIPLE,mas.CLIENT_NO
    from RB_AGREEMENT_SWEEP mas,RB_AGREEMENT ma
    where INTERNAL_KEY = #{internalKey}
    and ma.AGREEMENT_ID = mas.AGREEMENT_ID
    and mas.AGREEMENT_STATUS = 'A'
    and mas.AGREEMENT_STATUS = ma.AGREEMENT_STATUS
    and mas.client_no = #{clientNo}
    and mas.client_no = ma.client_no
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mas.COMPANY = #{company}
    </if>
  </select>

  <update id="updateBySchedNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">
    update RB_AGREEMENT_SWEEP
    <set>
      <if test="agreementStatus != null">
        AGREEMENT_STATUS = #{agreementStatus},
      </if>
    </set>
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mas.COMPANY = #{company}
    </if>
    AND SCHED_NO = #{schedNo}
  </update>

  <select id="selectByRenewRegSeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_SWEEP
    where SEQ_NO = #{seqNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mas.COMPANY = #{company}
    </if>
    and AGREEMENT_STATUS = 'A'
  </select>
  <select id="selectByAgreementId" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_SWEEP
    where AGREEMENT_ID = #{agreementId}
    and AGREEMENT_STATUS = 'A'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mas.COMPANY = #{company}
    </if>
    and CLIENT_NO = #{clientNo}
  </select>
  <select id="selectByInternalKeyAndSchedNo" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_SWEEP
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mas.COMPANY = #{company}
    </if>
    and SCHED_NO = #{schedNo}
  </select>

  <select id="getByOthInternalKeyRtt" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep">
    select <include refid="Base_Column"/>
    from RB_AGREEMENT_SWEEP
    where OTH_INTERNAL_KEY = #{othInternalkey}
    AND AGREEMENT_STATUS='A'
    AND RENEW_TYPE='RAL'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND mas.COMPANY = #{company}
    </if>
    ORDER BY PRIORITY DESC,AGREEMENT_ID ASC
  </select>
  <update id="updateFinFixedAmtByPrimaryKey">
    UPDATE
    RB_AGREEMENT_SWEEP
    SET FIN_FIXED_AMT = NULL
    WHERE AGREEMENT_ID = #{agreementId}
    AND CLIENT_NO = #{clientNo}
  </update>
</mapper>
