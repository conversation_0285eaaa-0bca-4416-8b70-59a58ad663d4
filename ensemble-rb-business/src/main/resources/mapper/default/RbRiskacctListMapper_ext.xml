<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbRiskacctList">

	<update id="updateByBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbRiskacctList" >
		UPDATE RB_RISKACCT_LIST
		<set>
			<if test="blackNo != null and  blackNo != '' ">
				BLACK_NO = #{blackNo},
			</if>
			<if test="clientName != null and  clientName != '' ">
				CLIENT_NAME = #{clientName},
			</if>
			<if test="asynDate != null and  asynDate != '' ">
				ASYN_DATE = #{asynDate},
			</if>
			<if test="remark1 != null and  remark1 != '' ">
				REMARK1 = #{remark1},
			</if>
			<if test="field3 != null and  field3 != '' ">
				FIELD3 = #{field3},
			</if>
			<if test="batchNo != null and  batchNo != '' ">
				BATCH_NO = #{batchNo},
			</if>
			<if test="blackDesc != null and  blackDesc != '' ">
				BLACK_DESC = #{blackDesc},
			</if>
			<if test="asynId != null and  asynId != '' ">
				ASYN_ID = #{asynId},
			</if>
			<if test="retMsg != null and  retMsg != '' ">
				RET_MSG = #{retMsg},
			</if>
			<if test="batchStatus != null and  batchStatus != '' ">
				BATCH_STATUS = #{batchStatus},
			</if>
			<if test="expireDate != null and  expireDate != '' ">
				EXPIRE_DATE = #{expireDate},
			</if>
			<if test="isOurBankFlag != null and  isOurBankFlag != '' ">
				IS_OUR_BANK_FLAG = #{isOurBankFlag},
			</if>
			<if test="examineTeller != null and  examineTeller != '' ">
				EXAMINE_TELLER = #{examineTeller},
			</if>
			<if test="remark2 != null and  remark2 != '' ">
				REMARK2 = #{remark2},
			</if>
			<if test="errorDesc != null and  errorDesc != '' ">
				ERROR_DESC = #{errorDesc},
			</if>
			<if test="tranTimestamp != null ">
				TRAN_TIMESTAMP = #{tranTimestamp},
			</if>
			<if test="listSource != null and  listSource != '' ">
				LIST_SOURCE = #{listSource},
			</if>
			<if test="checkFlag != null and  checkFlag != '' ">
				CHECK_FLAG = #{checkFlag},
			</if>
			<if test="field1 != null and  field1 != '' ">
				FIELD1 = #{field1},
			</if>
			<if test="baseAcctNo != null and  baseAcctNo != '' ">
				BASE_ACCT_NO = #{baseAcctNo},
			</if>
			<if test="acctStatus != null and  acctStatus != '' ">
				ACCT_STATUS = #{acctStatus},
			</if>
			<if test="documentType != null and  documentType != '' ">
				DOCUMENT_TYPE = #{documentType},
			</if>
			<if test="effectDate != null and  effectDate != '' ">
				EFFECT_DATE = #{effectDate},
			</if>
			<if test="jobRunId != null and  jobRunId != '' ">
				JOB_RUN_ID = #{jobRunId},
			</if>
			<if test="inputBranch != null and  inputBranch != '' ">
				INPUT_BRANCH = #{inputBranch},
			</if>
			<if test="examineFlag != null and  examineFlag != '' ">
				EXAMINE_FLAG = #{examineFlag},
			</if>
			<if test="operUserId != null and  operUserId != '' ">
				OPER_USER_ID = #{operUserId},
			</if>
			<if test="field2 != null and  field2 != '' ">
				FIELD2 = #{field2},
			</if>
			<if test="errorCode != null and  errorCode != '' ">
				ERROR_CODE = #{errorCode},
			</if>
			<if test="acctBranch != null and  acctBranch != '' ">
				ACCT_BRANCH = #{acctBranch},
			</if>
			<if test="clientNo != null and  clientNo != '' ">
				CLIENT_NO = #{clientNo},
			</if>
			<if test="documentId != null and  documentId != '' ">
				DOCUMENT_ID = #{documentId},
			</if>
			<if test="uncounterDesc != null and  uncounterDesc != '' ">
				UNCOUNTER_DESC = #{uncounterDesc},
			</if>
			<if test="blackCheckTime != null and  blackCheckTime != '' ">
				BLACK_CHECK_TIME = #{blackCheckTime},
			</if>
			<if test="listOperateType != null and  listOperateType != '' ">
				LIST_OPERATE_TYPE = #{listOperateType},
			</if>
			<if test="pushFlag != null and  pushFlag != '' ">
				PUSH_FLAG = #{pushFlag},
			</if>
			<if test="remark3 != null and  remark3 != '' ">
				REMARK3 = #{remark3},
			</if>
		</set>
		where BATCH_NO = #{batchNo}
		<if test="baseAcctNo != null">
			AND BASE_ACCT_NO = #{baseAcctNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>


</mapper>
