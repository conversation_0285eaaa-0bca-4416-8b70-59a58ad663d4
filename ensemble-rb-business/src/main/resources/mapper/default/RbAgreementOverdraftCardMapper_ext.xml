<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraftCard">


    <select id="selectByCondition" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraftCard">
        select
        <include refid="Base_Column"/>
        from rb_agreement_overdraft_card
        <where>
            <!-- 多法人改造 by LIYUANV -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            <if test="cardNo != null and cardNo != '' ">
                AND CARD_NO = #{cardNo}
            </if>
            <if test="agreementId != null">
                and AGREEMENT_ID = #{agreementId}
            </if>
            <if test="agreementType != null">
                and AGREEMENT_TYPE = #{agreementType}
            </if>
            <if test="startDate != null">
                and START_DATE = #{startDate}
            </if>
            <if test="endDate != null">
                and END_DATE = #{endDate}
            </if>
            <if test="agreementKeyType != null">
                and AGREEMENT_KEY_TYPE = #{agreementKeyType}
            </if>
            <if test="agreementKey != null">
                and AGREEMENT_KEY = #{agreementKey}
            </if>
            <if test="baseAcctNo != null">
                and BASE_ACCT_NO = #{baseAcctNo}
            </if>
            <if test="prodType != null">
                and PROD_TYPE = #{prodType}
            </if>
            <if test="acctCcy != null">
                and ACCT_CCY = #{acctCcy}
            </if>
            <if test="acctSeqNo != null">
                and ACCT_SEQ_NO = #{acctSeqNo}
            </if>
            <if test="clientNo != null">
                and CLIENT_NO = #{clientNo}
            </if>
            <if test="clientShort != null">
                and CLIENT_SHORT = #{clientShort}
            </if>
            <if test="agreementStatus != null  and agreementStatus!=''">
                and AGREEMENT_STATUS = #{agreementStatus}
            </if>
            <if test="branch != null">
                and BRANCH = #{branch}
            </if>
            <if test="agreementOpenDate != null">
                and AGREEMENT_OPEN_DATE = #{agreementOpenDate}
            </if>
            <if test="userId != null">
                and USER_ID = #{userId}
            </if>
            <if test="lastChangeUserId != null">
                and LAST_CHANGE_USER_ID = #{lastChangeUserId}
            </if>
            <if test="lastChangeDate != null">
                and LAST_CHANGE_DATE = #{lastChangeDate}
            </if>
            <if test="company != null">
                and COMPANY = #{company}
            </if>
            <if test="loanProdType != null">
                and LOAN_PROD_TYPE = #{loanProdType}
            </if>
            <if test="loanInternalKey != null">
                and LOAN_INTERNAL_KEY = #{loanInternalKey}
            </if>
            <if test="loanBaseAcctNo != null">
                and LOAN_BASE_ACCT_NO = #{loanBaseAcctNo}
            </if>
            <if test="loanAcctCcy != null">
                and LOAN_ACCT_CCY = #{loanAcctCcy}
            </if>
            <if test="loanSeqNo != null">
                and LOAN_SEQ_NO = #{loanSeqNo}
            </if>
            <if test="odMethod != null">
                and OD_METHOD = #{odMethod}
            </if>
            <if test="odCcy != null">
                and OD_CCY = #{odCcy}
            </if>
            <if test="odAmt != null">
                and OD_AMT = #{odAmt}
            </if>
            <if test="odTerm != null">
                and OD_TERM = #{odTerm}
            </if>
            <if test="odTermType != null">
                and OD_TERM_TYPE = #{odTermType}
            </if>
            <if test="odGracePeriod != null">
                and OD_GRACE_PERIOD = #{odGracePeriod}
            </if>
        </where>
    </select>

</mapper>
