<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">

    <select id="getMbImpoundInfoList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        select
        <include refid="Base_Column"/>
        from RB_IMPOUND_INFO
        where INTERNAL_KEY = #{internalKey}
        and SCHED_NO >0
<!--        and STATUS != 'Y'-->
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbImpoundInfoBySchedNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        select
        <include refid="Base_Column"/>
        from RB_IMPOUND_INFO
        where SCHED_NO = #{schedNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <update id="updateMbImpoundInfoBySchedNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        update RB_IMPOUND_INFO
        <set>
            <if test="transferTimes != null">
                transfer_times = #{transferTimes},
            </if>
            <if test="impoundTotalAmt != null">
                IMPOUND_TOTAL_AMT = #{impoundTotalAmt},
            </if>
        </set>
        where SCHED_NO = #{schedNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
<!--    <update id="updateMbImpoundStatus" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        update RB_IMPOUND_INFO
        <set>
            <if test="status != null">
                STATUS = #{status},
            </if>
        </set>
        where SCHED_NO = #{schedNo}
    </update>-->
    <select id="getMbImpoundInfoByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        select
        <include refid="Base_Column"/>
        from RB_IMPOUND_INFO
        where (REFERENCE = #{reference} or OTH_REFERENCE  = #{reference})
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getMbImpoundInfoByBaseAcctNoOrDate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        select
        <include refid="Base_Column"/>
        from  RB_IMPOUND_INFO
        where 1=1
        <if test="internal_key != null">
            AND INTERNAL_KEY = #{internal_key}
        </if>
        <if test="transferFlag != null and  transferFlag != ''">
            AND TRANSFER_FLAG = #{transferFlag}
        </if>
        <if test="startDate != null">
            AND
            <![CDATA[  #{startDate}<= TRAN_DATE ]]>
        </if>
        <if test="endDate != null ">
            AND
            <![CDATA[ TRAN_DATE <= #{endDate}]]>
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>

    </select>
    <select id="getMbImpoundInfoByBranch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        select
        <include refid="Base_Column"/>
        from RB_IMPOUND_INFO
        where 1=1 AND INDIVIDUAL_FLAG='Y'
        <if test="startDate != null">
            AND
            <![CDATA[  #{startDate}<= TRAN_DATE ]]>
        </if>
        <if test="endDate != null ">
            AND
            <![CDATA[ TRAN_DATE <= #{endDate}]]>
        </if>
        <if test="branch != null ">
            AND
             TRAN_BRANCH = #{branch}
        </if>
        <if test="transferFlag != null and  transferFlag != ''">
            AND TRANSFER_FLAG = #{transferFlag}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="selectFmBranchAttached" parameterType="java.util.Map" resultType="String">
        SELECT BRANCH
        FROM FM_BRANCH
        <if test="branch !=  null and  branch != ''">
            START WITH BRANCH = #{branch}
        </if>
        CONNECT BY PRIOR BRANCH = ATTACHED_TO

    </select>
    <select id="getMbImpoundInfoByClientAndInternalkey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo">
        select
        <include refid="Base_Column"/>
        from  RB_IMPOUND_INFO
        where 1=1
        <if test="clientNo != null and clientNo != ''">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="internal_key != null">
            AND INTERNAL_KEY = #{internal_key}
        </if>
    </select>
</mapper>
