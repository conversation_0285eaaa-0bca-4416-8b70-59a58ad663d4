<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbListChangeHist">

    <select id="selectRbListChangeHist" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbListChangeHist">
        select
        *
        from RB_LIST_CHANGE_HIST
        where 1=1
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <if test="dataType != null">
            AND DATA_TYPE = #{dataType}
        </if>
        <if test="dataValue != null">
            AND DATA_VALUE = #{dataValue}
        </if>
        <if test="baseAcctNo != null">
            AND BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        <if test="listType != null">
            AND LIST_TYPE = #{listType}
        </if>
        <if test="startDate != null ">
            AND  TRAN_DATE<![CDATA[>=]]> #{startDate}
        </if>
        <if test="endDate != null ">
            AND  TRAN_DATE<![CDATA[<=]]> #{endDate}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <delete id="deleteByDataTypeAndValue" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbListChangeHist">
        delete from RB_LIST_CHANGE_HIST
        where DATA_VALUE = #{dataValue}
        <if test="dataType != null">
            AND DATA_TYPE = #{dataType}
        </if>
        <if test="listType != null">
            AND LIST_TYPE = #{listType}
        </if>
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
</mapper>
