<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCashCtlSign">

    <update id="updateSubBaseAcctNo" parameterType="java.util.Map">
        update RB_CASH_CTL_SIGN
        <set>
            <if test="status != null">
                STATUS = #{status},
            </if>
            <if test="subBaseAcctNo != null">
                SUB_BASE_ACCT_NO = #{subBaseAcctNo},
            </if>
        </set>
        where BASE_ACCT_NO = #{internalKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
</mapper>
