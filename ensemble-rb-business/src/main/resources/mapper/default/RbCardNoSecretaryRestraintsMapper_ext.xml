<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardNoSecretaryRestraints">

    <update id="changeRbCardNoSecretaryRestraints"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardNoSecretaryRestraints">

        update RB_CARD_NO_SECRETARY_RESTRAINTS
        <set>
            <if test="internalKey != null">
                INTERNAL_KEY = #{internalKey},
            </if>
            <if test="cardNo != null">
                CARD_NO = #{cardNo},
            </if>
            <if test="singleLimit != null">
                SINGLE_LIMIT = #{singleLimit},
            </if>
            <if test="dayLimitAvail != null">
                DAY_LIMIT_AVAIL = #{dayLimitAvail},
            </if>
            <if test="totalDayAmt != null">
                TOTAL_DAY_AMT = #{totalDayAmt},
            </if>
            <if test="limitNum != null">
                LIMIT_NUM = #{limitNum},
            </if>
            <if test="noPasswordStatus != null">
                NO_PASSWORD_STATUS = #{noPasswordStatus},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate}
            </if>
        </set>
        where LIMIT_ID = #{limitId}
        and CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="queryRbCardNoSecretaryRestraints"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardNoSecretaryRestraints"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCardNoSecretaryRestraints">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_CARD_NO_SECRETARY_RESTRAINTS
        where
        <if test="internalKey != null">
            INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <if  test="cardNo != null">
            and CARD_NO = #{cardNo}
        </if>

    </select>

</mapper>
