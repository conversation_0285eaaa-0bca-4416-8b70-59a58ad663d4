<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist">
        select
        <include refid="Base_Column"/>
        from RB_INT_TAX_HIST
        where TAX_SEQ_NO = #{taxSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist">
        delete from RB_INT_TAX_HIST
        where TAX_SEQ_NO = #{taxSeqNo}
        <if test="internalKey != null">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist">
        insert into RB_INT_TAX_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taxSeqNo != null">
                TAX_SEQ_NO,
            </if>
            <if test="tranSeqNo != null">
                TRAN_SEQ_NO,
            </if>
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="baseAcctNo != null">
                BASE_ACCT_NO,
            </if>
            <if test="prodType != null">
                PROD_TYPE,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="acctSeqNo != null">
                ACCT_SEQ_NO,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="clientType != null">
                CLIENT_TYPE,
            </if>
            <if test="taxAmt != null">
                TAX_AMT,
            </if>
            <if test="taxCcy != null">
                TAX_CCY,
            </if>
            <if test="taxType != null">
                TAX_TYPE,
            </if>
            <if test="taxRate != null">
                TAX_RATE,
            </if>
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="eventType != null">
                EVENT_TYPE,
            </if>
            <if test="crDrInd != null">
                CR_DR_IND,
            </if>
            <if test="boInd != null">
                BO_IND,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="bankSeqNo != null">
                BANK_SEQ_NO,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="sourceModule != null">
                SOURCE_MODULE,
            </if>
            <if test="profitCenter != null">
                PROFIT_CENTRE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="channelSeqNo != null">
                CHANNEL_SEQ_NO,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE,
            </if>
            <if test="reversalFlag != null">
                REVERSAL_FLAG,
            </if>
            <if test="reversalDate != null">
                REVERSAL_DATE,
            </if>
            <if test="reversalBranch != null">
                REVERSAL_BRANCH,
            </if>
            <if test="reversalUserId != null">
                REVERSAL_USER_ID,
            </if>
            <if test="reversalAuthUserId != null">
                REVERSAL_AUTH_USER_ID,
            </if>
            <if test="reversalTaxSeqNo != null">
                REVERSAL_TAX_SEQ_NO,
            </if>
            <if test="payInterest != null">
                PAY_INTEREST,
            </if>
            <if test="glPostedFlag != null">
                GL_POSTED_FLAG,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taxSeqNo != null">
                #{taxSeqNo},
            </if>
            <if test="tranSeqNo != null">
                #{tranSeqNo},
            </if>
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="baseAcctNo != null">
                #{baseAcctNo},
            </if>
            <if test="prodType != null">
                #{prodType},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="acctSeqNo != null">
                #{acctSeqNo},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="clientType != null">
                #{clientType},
            </if>
            <if test="taxAmt != null">
                #{taxAmt},
            </if>
            <if test="taxCcy != null">
                #{taxCcy},
            </if>
            <if test="taxType != null">
                #{taxType},
            </if>
            <if test="taxRate != null">
                #{taxRate},
            </if>
            <if test="tranType != null">
                #{tranType},
            </if>
            <if test="eventType != null">
                #{eventType},
            </if>
            <if test="crDrInd != null">
                #{crDrInd},
            </if>
            <if test="boInd != null">
                #{boInd},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="bankSeqNo != null">
                #{bankSeqNo},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="sourceModule != null">
                #{sourceModule},
            </if>
            <if test="profitCenter != null">
                #{profitCenter},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="channelSeqNo != null">
                #{channelSeqNo},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="reversalFlag != null">
                #{reversalFlag},
            </if>
            <if test="reversalDate != null">
                #{reversalDate},
            </if>
            <if test="reversalBranch != null">
                #{reversalBranch},
            </if>
            <if test="reversalUserId != null">
                #{reversalUserId},
            </if>
            <if test="reversalAuthUserId != null">
                #{reversalAuthUserId},
            </if>
            <if test="reversalTaxSeqNo != null">
                #{reversalTaxSeqNo},
            </if>
            <if test="payInterest != null">
                #{payInterest},
            </if>
            <if test="glPostedFlag != null">
                #{glPostedFlag},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
    <select id="selectMbIntTaxHistSplitOB" parameterType="java.util.Map" resultType="java.util.Map">
        <if test="_databaseId == 'mysql'">
            SELECT
            MIN(TAX_SEQ_NO) START_KEY,
            MAX(TAX_SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
            FROM
            (
            SELECT
            TAX_SEQ_NO,
            @rownum :=@rownum + 1 AS rownum
            FROM (SELECT
            DISTINCT TAX_SEQ_NO FROM RB_INT_TAX_HIST,
            (SELECT @rownum := -1) t
            where tran_date =#{runDate}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            AND TAX_AMT!=0
            AND (gl_posted = 'N' or gl_posted is null)) t1
            ORDER BY TAX_SEQ_NO+0
            ) tt
            GROUP BY
            FLOOR(
            tt.rownum /#{maxPerCount} )
        </if>
        <if test="_databaseId == 'oracle'">
            SELECT MIN (TAX_SEQ_NO) START_KEY, MAX (TAX_SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
            FROM (SELECT DISTINCT TAX_SEQ_NO
            from RB_INT_TAX_HIST
            where tran_date = #{runDate}
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
            AND TAX_AMT!=0
            AND (gl_posted = 'N' or gl_posted is null)
            ORDER BY TAX_SEQ_NO+0)
            GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
        </if>
    </select>

    <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist">
        update RB_INT_TAX_HIST set gl_posted='Y'
        where TAX_SEQ_NO = #{taxSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
</mapper>
