<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardCollectInfo">
	<select id="getByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardCollectInfo" >
		select
		<include refid="Base_Column"/>
		from RB_SETTLE_CARD_COLLECT_INFO where reference = #{reference}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
