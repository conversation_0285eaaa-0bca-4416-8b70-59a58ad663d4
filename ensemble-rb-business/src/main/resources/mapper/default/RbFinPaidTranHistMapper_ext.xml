<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinPaidTranHist">

  <!-- Created by admin on 2019/01/10 11:12:03. -->
  <select id="getRbFinPaidTranHistByMap" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinPaidTranHist">
    select <include refid="Base_Column"/>
    from RB_FIN_PAID_TRAN_HIST
    where PAID_TYPE = #{paidType}
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by TRAN_TIMESTAMP desc
  </select>
</mapper>
