<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIndepStandardSplit">

  <select id="getSplitForToday" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIndepStandardSplit" parameterType="java.util.List" >
    select <include refid="Base_Column"/>
    from RB_INDEP_STANDARD_SPLIT
    where AGREEMENT_ID = #{agreementId}
    AND CLIENT_NO = #{clientNo}
    AND TRAN_DATE = #{tranDate}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
