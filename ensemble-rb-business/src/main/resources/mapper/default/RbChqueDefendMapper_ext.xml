<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDefend">
    <update id="updChequeDefend" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDefend">
        update RB_CHEQUE_DEFEND
        <set>
            <if test="status != null">
                STATUS = #{status},
            </if>
            <if test="cancelDate != null">
                CANCEL_DATE = #{cancelDate},
            </if>
            <if test="cancelUserId != null">
                CANCEL_USER_ID = #{cancelUserId},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
        </set>
        where SEQ_NO = #{seqNo}
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>
    </update>
    <select id="getChequeDefends" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDefend">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_CHEQUE_DEFEND
        WHERE STATUS='A'
        <if test="promptAcctNo!=null">
            and	PROMPT_ACCT_NO = #{promptAcctNo}
        </if>
        <if test="promptSeqNo!=null">
            and	PROMPT_SEQ_NO = #{promptSeqNo}
        </if>
        <if test="issueBranchFlag!=null">
            and	ISSUE_BRANCH_FLAG = #{issueBranchFlag}
        </if>
        <if test="docType!=null">
            and	DOC_TYPE = #{docType}
        </if>
        <if test="voucherPrefix!=null and voucherPrefix != '' ">
            and	VOUCHER_PREFIX = #{voucherPrefix}
        </if>
        <if test="voucherNo!=null">
            and	VOUCHER_NO = #{voucherNo}
        </if>
        <if test="clientNo!=null">
            AND	CLIENT_NO = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

</mapper>
