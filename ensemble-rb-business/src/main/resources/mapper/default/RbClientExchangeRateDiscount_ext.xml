<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientExchangeRateDiscount">

	<update id="updateDiscountRate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientExchangeRateDiscount">
		update RB_CLIENT_EXCHANGE_RATE_DISCOUNT
		<set>
			<if test="applyBranch != null and applyBranch != '' ">
				APPLY_BRANCH = #{applyBranch,jdbcType=VARCHAR},
			</if>
			<if test="intValidFromDate != null ">
				INT_VALID_FROM_DATE = #{intValidFromDate,jdbcType=DATE},
			</if>
			<if test="intValidThruDate != null ">
				INT_VALID_THRU_DATE = #{intValidThruDate,jdbcType=DATE},
			</if>
			<if test="ccy != null and ccy != '' ">
				CCY = #{ccy,jdbcType=VARCHAR},
			</if>
			<if test="sellBuyInd != null and sellBuyInd != '' ">
				SELL_BUY_IND = #{sellBuyInd,jdbcType=VARCHAR},
			</if>
			<if test="couponRateType != null and couponRateType != '' ">
				COUPON_RATE_TYPE = #{couponRateType,jdbcType=VARCHAR},
			</if>
			<if test="discountTerm != null ">
				DISCOUNT_TERM = #{discountTerm,jdbcType=INTEGER},
			</if>
			<if test="exchangeDiscountType != null and exchangeDiscountType != '' ">
				EXCHANGE_DISCOUNT_TYPE = #{exchangeDiscountType,jdbcType=VARCHAR},
			</if>
			<if test="discountValue != null ">
				DISCOUNT_VALUE = #{discountValue,jdbcType=INTEGER},
			</if>
			<if test="uncDiscountValue != null ">
				UNC_DISCOUNT_VALUE = #{uncDiscountValue,jdbcType=INTEGER},
			</if>
			<if test="discountStatus != null and discountStatus != '' ">
				DISCOUNT_STATUS = #{discountStatus,jdbcType=VARCHAR},
			</if>
			<if test="lastOperateStatus != null and lastOperateStatus != '' ">
				LAST_OPERATE_STATUS = #{lastOperateStatus,jdbcType=VARCHAR},
			</if>
			<if test="tranDate != null ">
				TRAN_DATE = #{tranDate,jdbcType=DATE},
			</if>
			<if test="userId != null and userId != '' ">
				USER_ID = #{userId,jdbcType=VARCHAR},
			</if>
			<if test="branch != null and branch != '' ">
				BRANCH = #{branch,jdbcType=VARCHAR},
			</if>
			<if test="intRateFormNo != null and intRateFormNo != '' ">
				INT_RATE_FORM_NO = #{intRateFormNo,jdbcType=VARCHAR},
			</if>
			<if test="tranTimestamp != null">
				TRAN_TIMESTAMP = #{tranTimestamp}
			</if>

		</set>
		<where>
			<if test="clientNo != null and clientNo.length() > 0">
				AND client_no = #{clientNo}
			</if>
			<if test="couponRateType != null and couponRateType.length() > 0">
				AND COUPON_RATE_TYPE = #{couponRateType}
			</if>
			<if test="applyBranch != null and applyBranch != ''">
				AND APPLY_BRANCH = #{applyBranch}
			</if>
			<if test="sellBuyInd != null and sellBuyInd.length() > 0">
				AND SELL_BUY_IND = #{sellBuyInd}
			</if>
			<if test="ccy != null and ccy.length() > 0">
				AND CCY = #{ccy}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</update>
	<update id="deleteDiscountRate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientExchangeRateDiscount">
		update RB_CLIENT_EXCHANGE_RATE_DISCOUNT
		<set>
			<if test="discountStatus != null and discountStatus != '' ">
				DISCOUNT_STATUS = #{discountStatus,jdbcType=VARCHAR},
			</if>
		</set>
		<where>
			<if test="clientNo != null and clientNo.length() > 0">
				AND client_no = #{clientNo}
			</if>
			<if test="sellBuyInd != null and sellBuyInd.length() > 0">
				AND SELL_BUY_IND = #{sellBuyInd}
			</if>
			<if test="couponRateType != null and couponRateType.length() > 0">
				AND COUPON_RATE_TYPE = #{couponRateType}
			</if>
			<if test="ccy != null and ccy.length() > 0">
				AND CCY = #{ccy}
			</if>
			<if test="applyBranch != null and applyBranch != ''">
				AND APPLY_BRANCH = #{applyBranch}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</update>
	<select id="selectDiscountRate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientExchangeRateDiscount"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientExchangeRateDiscount">
		SELECT <include refid="Base_Column"/>
		FROM RB_CLIENT_EXCHANGE_RATE_DISCOUNT
		<where>
			<if test="discountStatus != null and discountStatus.length() > 0">
				DISCOUNT_STATUS = #{discountStatus}
			</if>
			<if test="clientNo != null and clientNo.length() > 0">
				AND client_no = #{clientNo}
			</if>
			<if test="intRateFormNo != null and intRateFormNo.length() > 0">
				AND INT_RATE_FORM_NO = #{intRateFormNo}
			</if>
			<if test="sellBuyInd != null and sellBuyInd.length() > 0">
				AND SELL_BUY_IND = #{sellBuyInd}
			</if>
			<if test="couponRateType != null and couponRateType.length() > 0">
				AND COUPON_RATE_TYPE = #{couponRateType}
			</if>
			<if test="applyBranch != null and applyBranch.length() > 0">
				AND apply_branch = #{applyBranch}
			</if>

			<if test="ccy != null and ccy.length() > 0">
				AND CCY = #{ccy}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</select>
	<select id="selectDiscountRateList" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbClientExchangeRateDiscount">
		SELECT <include refid="Base_Column"/>
		FROM RB_CLIENT_EXCHANGE_RATE_DISCOUNT
		<where>
			<if test="discountStatus != null and discountStatus.length() > 0">
				DISCOUNT_STATUS = #{discountStatus}
			</if>
			<if test="clientNo != null and clientNo.length() > 0">
				AND client_no = #{clientNo}
			</if>
			<if test="intRateFormNo != null and intRateFormNo.length() > 0">
				AND INT_RATE_FORM_NO = #{intRateFormNo}
			</if>
			<if test="couponRateType != null and couponRateType.length() > 0">
				AND COUPON_RATE_TYPE = #{couponRateType}
			</if>
			<if test="branchList != null and branchList.size() > 0">
				AND apply_branch IN
				<foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
					#{branch}
				</foreach>
			</if>
			<if test="ccy != null and ccy.length() > 0">
				AND CCY = #{ccy}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
		</where>
	</select>
</mapper>
