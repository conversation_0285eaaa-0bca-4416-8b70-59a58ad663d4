<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo">

	<select id="get" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo" resultMap="RbCaptInfoBean">
		SELECT * FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		  AND CAPT_DATE = #{captDate}
		  AND INT_CLASS = #{intClass}
		  AND IRL_SEQ_NO = #{irlSeqNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY IRL_SEQ_NO+0
	</select>

	<select id="findByInternalKey" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo"
			resultMap="RbCaptInfoBean">
		SELECT * FROM RB_CAPT_INFO WHERE INTERNAL_KEY = #{internalKey}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getCaptInfoMaxNum" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo"
			resultMap="RbCaptInfoBean">
		SELECT * FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		  AND CAPT_DATE = #{captDate}
		  AND INT_CLASS = #{intClass}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY IRL_SEQ_NO+0 DESC
	</select>

	<select id="getMaxNum" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo"
			resultType="java.math.BigDecimal">
		SELECT MAX(SERIAL_NUM)
		FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		  AND CAPT_DATE = #{captDate}
		  AND INT_CLASS = #{intClass}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getAfterCapt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo" resultMap="RbCaptInfoBean">


		SELECT * FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		  <![CDATA[
		  AND CAPT_DATE > #{captDate}
		  AND INT_CLASS = #{intClass}
		  AND TRAN_SOURCE = #{tranSource}
		  ]]>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
		  AND COMPANY = #{company}
			</if>
		ORDER BY  CAPT_DATE


    </select>
	<select id="getBetweenCapt" parameterType="java.util.HashMap" resultMap="RbCaptInfoBean">


		SELECT * FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		  <![CDATA[
		  AND CAPT_DATE > #{beginDate}
		  AND CAPT_DATE <= #{endDate}
		  AND INT_CLASS = #{intClass}
		  AND TRAN_SOURCE = #{tranSource}
		  ]]>
<!-- 多法人改造 by luocwa -->
			<if test="company != null and company != '' ">
		  AND COMPANY = #{company}
			</if>
		ORDER BY  CAPT_DATE


    </select>
	<select id="getAlreadyCapt" parameterType="java.util.HashMap" resultMap="RbCaptInfoBean">


		SELECT * FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		<![CDATA[
		  AND CAPT_DATE >= #{startDate}
		  AND CAPT_DATE <= #{endDate}
		  AND INT_CLASS = #{intClass}
		]]>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY  CAPT_DATE DESC


    </select>
	<select id="selectCaptInfo" parameterType="java.util.Map" resultMap="RbCaptInfoBean">
		SELECT IRL_SEQ_NO,
			   B.REFERENCE AS REFERENCE,
			   B.BRANCH AS TRAN_BRANCH,CAPT_DATE AS EFFECT_DATE,
			   A.CCY AS TRAN_CCY,B.SOURCE_MODULE,A.SOURCE_TYPE,B.BUSINESS_UNIT,
			   B.INT_CLASS AS AMT_TYPE, B.INT_POSTED_CTD AS AMOUNT,
			   B.PROD_TYPE, B.ACCT_NO AS BASE_ACCT_NO,B.BRANCH,B.ACCOUNTING_STATUS,B.PROFIT_CENTRE,
			   A.CCY,B.CLIENT_TYPE,A.CLIENT_NO,'LI_MARKET' AS SYSTEM_ID,
			   '结息' AS NARRATIVE,B.PROFIT_CENTRE AS TRAN_PROFIT_CENTRE,
			   'DUE' AS EVENT_TYPE,'DUE' AS TRAN_TYPE,CAPT_DATE AS TRAN_DATE,B.INTERNAL_KEY
		FROM RB_ACCT A,RB_CAPT_INFO B
		WHERE A.INTERNAL_KEY = B.INTERNAL_KEY
		  AND B.INT_POSTED_CTD !=  0
		  AND B.CAPT_DATE BETWEEN #{FROM_DATE} AND #{TO_DATE}
		  AND (B.GL_POSTED = 'N' OR B.GL_POSTED IS NULL)
		  AND B.TRAN_SOURCE = #{TRAN_SOURCE}
		  AND B.REVERSAL_FLAG = #{REVERSAL}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND B.COMPANY = #{company}
		</if>
	</select>

	<update id="updateCaptInfo" parameterType="java.util.Map">
		UPDATE RB_CAPT_INFO
		<set>
			GL_POSTED = 'Y',
			TRAN_TIMESTAMP = #{tranTimestamp},
			TRAN_TIME = #{tranTime},
			ROUTER_KEY = #{routerKey}
		</set>
		WHERE IRL_SEQ_NO = #{IRL_SEQ_NO}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

	<select id="findCaptInfoByReference" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo"
			resultMap="RbCaptInfoBean">
		SELECT * FROM RB_CAPT_INFO WHERE REFERENCE = #{reference}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getAlreadyCaptByIntPostedCtd" parameterType="java.util.HashMap" resultMap="RbCaptInfoBean">


		SELECT * FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		    <![CDATA[
		  AND CAPT_DATE >= #{startDate}
		  AND CAPT_DATE <= #{endDate}
		  AND INT_CLASS = #{intClass}
		  AND INT_POSTED_CTD !='0'
		  	]]>
<!-- 多法人改造 by luocwa -->
    	<if test="company != null and company != '' ">
     		 AND COMPANY = #{company}
		</if>
		ORDER BY  CAPT_DATE DESC

    </select>


	<update id="updateTranStatusByReference" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo">
		UPDATE RB_CAPT_INFO
		<set>
			<if test="tranStatus!=null">
				TRAN_STATUS = #{tranStatus},
			</if>
		</set>
		WHERE REFERENCE = #{reference}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>


	<select id="getCaptInfoByTranStatus" parameterType="java.util.HashMap" resultMap="RbCaptInfoBean">


		SELECT * FROM RB_CAPT_INFO
		WHERE INTERNAL_KEY = #{internalKey}
		  <![CDATA[
		  AND CAPT_DATE >= #{startDate}
		  AND CAPT_DATE <= #{endDate}
		  AND INT_CLASS = #{intClass}
		  AND CLIENT_NO = #{clientNo}
		  AND( TRAN_STATUS !='X'
			or TRAN_STATUS is NULL)
		]]>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY  CAPT_DATE ASC


    </select>

	<resultMap id="RbCaptInfoBean" type="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbCaptInfo">
		<result column="CAPT_DATE" property="captDate" jdbcType="DATE" javaType="String"/>
	</resultMap>
</mapper>
