<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbNoteCycleHist">

	<select id="getCycleHistByAcctAndDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbNoteCycleHist">
		SELECT
		<include refid="Base_Column" />
		FROM RB_NOTE_CYCLE_HIST
		WHERE BASE_ACCT_NO = #{baseAcctNo}
		<if test="tranStartDate != null and tranEndDate != null">
			AND ACCT_OPEN_DATE between #{tranStartDate} AND #{tranEndDate}
		</if>
		<if test="internalKey != null and internalKey != '' ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
