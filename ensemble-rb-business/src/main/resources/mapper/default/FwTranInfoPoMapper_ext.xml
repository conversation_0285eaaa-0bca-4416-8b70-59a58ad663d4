<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.dcits.comet.flow.antirepeate.entity.FwTranInfoPo">

    <update id="updateFwTranInfo" parameterType="com.dcits.comet.flow.antirepeate.entity.FwTranInfoPo">
        UPDATE FW_TRAN_INFO
        SET STATUS = #{status,jdbcType=VARCHAR}
        WHERE service_no = #{serviceNo,jdbcType=VARCHAR}
        and create_date = #{createDate,jdbcType=DATE}
    </update>

    <select id="getFwTranInfoByPrimary" parameterType="java.util.Map" resultType="com.dcits.comet.flow.antirepeate.entity.FwTranInfoPo">
        select service_no,create_date
        from FW_TRAN_INFO
        where service_no = #{serviceNo,jdbcType=VARCHAR}
        and create_date between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
    </select>
</mapper>