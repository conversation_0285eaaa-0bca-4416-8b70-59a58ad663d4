<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">

  <select id="queryMbAdSettle" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    select <include refid="Base_Column"/>
    from RB_AD_SETTLE
    <where>
    <if test="acceptContractNo != null ">
      AND ACCEPT_CONTRACT_NO = #{acceptContractNo}
    </if>
    <if test="reference != null ">
      AND REFERENCE = #{reference}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    order by REFERENCE,CASE SETTLE_ACCT_TYPE WHEN 'BOND' THEN 1 WHEN 'SETTLE' THEN 3 ELSE 5 END
  </select>
  <select id="getMbAdSettle" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    select <include refid="Base_Column"/>
    from RB_AD_SETTLE
    <where>
    <if test="reference != null ">
      AND REFERENCE = #{reference}
    </if>
    <if test="settleAcctType != null ">
      AND SETTLE_ACCT_TYPE = #{settleAcctType}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    order by REFERENCE,SERIAL_NO
  </select>
  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    select <include refid="Base_Column"/>
    from RB_AD_SETTLE
    where SERIAL_NO = #{serialNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- id="selectMbAdSettle" add by wangcea 2017-10-27-->
  <select id="selectMbAdSettle" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    select <include refid="Base_Column"/>
    from RB_AD_SETTLE
    where BASE_ACCT_NO = #{baseAcctNo}
      AND ACCEPT_CONTRACT_NO = #{acceptContractNo}
      AND SETTLE_ACCT_TYPE = 'BOND'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    delete from RB_AD_SETTLE
    where SERIAL_NO = #{serialNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <delete id="deleteByReference" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    delete from RB_AD_SETTLE
    where REFERENCE = #{reference}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <!-- id="deleteByAcct" add by wangcea 2017-10-27-->
  <delete id="deleteByAcct" parameterType="java.util.Map">
    delete from RB_AD_SETTLE
    where BASE_ACCT_NO = #{baseAcctNo}
        <if test="prodType != null">
          AND PROD_TYPE = #{prodType}
        </if>
        AND CCY = #{ccy}
        AND SETTLE_ACCT_TYPE = 'BOND'
        AND SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey}
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    update RB_AD_SETTLE
    <set>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="reference != null">
        REFERENCE = #{reference},
      </if>
      <if test="direction != null">
        DIRECTION = #{direction},
      </if>
      <if test="settleAcctType != null">
        SETTLE_ACCT_TYPE = #{settleAcctType},
      </if>
      <if test="status != null">
        STATUS = #{status},
      </if>
      <if test="needSettleAmt != null">
        NEED_SETTLE_AMT = #{needSettleAmt},
      </if>
      <if test="settltAmt != null">
        SETTLE_AMT = #{settltAmt},
      </if>
      <if test="settleDate != null">
        SETTLE_DATE = #{settleDate},
      </if>
      <if test="totalAmt != null">
        TOTAL_AMT = #{totalAmt},
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO = #{resSeqNo},
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO = #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO = #{acctSeqNo},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY = #{internalKey},
      </if>
      <if test="balType != null">
        BAL_TYPE = #{balType},
      </if>
      <if test="bailCertificateNo != null">
        BAIL_CERTIFICATE_NO = #{bailCertificateNo},
      </if>
      <if test="tranAmt != null">
        TRAN_AMT = #{tranAmt},
      </if>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo},
      </if>
      <if test="clientName != null">
        CLIENT_NAME = #{clientName},
      </if>
      <if test="nosVosNo != null">
        NOS_VOS_NO = #{nosVosNo},
      </if>
      <if test="errorMsg != null">
        ERROR_MSG = #{errorMsg},
      </if>
      <if test="lastProcStamp != null">
        LAST_PROC_STAMP = #{lastProcStamp},
      </if>
      <if test="acceptContractNo != null">
        ACCEPT_CONTRACT_NO = #{acceptContractNo},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="bailOpenBranch != null">
        BAIL_OPEN_BRANCH = #{bailOpenBranch},
      </if>
    </set>
    where SERIAL_NO = #{serialNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    insert into RB_AD_SETTLE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        SERIAL_NO,
      </if>
      <if test="settleAcctInternalKey != null">
        SETTLE_ACCT_INTERNAL_KEY,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="direction != null">
        DIRECTION,
      </if>
      <if test="settleAcctType != null">
        SETTLE_ACCT_TYPE,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="needSettleAmt != null">
        NEED_SETTLE_AMT,
      </if>
      <if test="settltAmt != null">
        SETTLE_AMT,
      </if>
      <if test="settleDate != null">
        SETTLE_DATE,
      </if>
      <if test="totalAmt != null">
        TOTAL_AMT,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="baseAcctNo != null">
        BASE_ACCT_NO,
      </if>
      <if test="acctSeqNo != null">
        ACCT_SEQ_NO,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="balType != null">
        BAL_TYPE,
      </if>
      <if test="bailCertificateNo != null">
        BAIL_CERTIFICATE_NO,
      </if>
      <if test="tranAmt != null">
        TRAN_AMT,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="nosVosNo != null">
        NOS_VOS_NO,
      </if>
      <if test="errorMsg != null">
        ERROR_MSG,
      </if>
      <if test="lastProcStamp != null">
        LAST_PROC_STAMP,
      </if>
      <if test="acceptContractNo != null">
        ACCEPT_CONTRACT_NO,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="bailOpenBranch != null">
        BAIL_OPEN_BRANCH,
      </if>
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <!--多法人改造 by LIYUANV-->
      <if test="company != null">
        #{company},
      </if>
      <if test="serialNo != null">
        #{serialNo},
      </if>
      <if test="settleAcctInternalKey != null">
        #{settleAcctInternalKey},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="direction != null">
        #{direction},
      </if>
      <if test="settleAcctType != null">
        #{settleAcctType},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="needSettleAmt != null">
        #{needSettleAmt},
      </if>
      <if test="settltAmt != null">
        #{settltAmt},
      </if>
      <if test="settleDate != null">
        #{settleDate},
      </if>
      <if test="totalAmt != null">
        #{totalAmt},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
      <if test="baseAcctNo != null">
        #{baseAcctNo},
      </if>
      <if test="acctSeqNo != null">
        #{acctSeqNo},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="balType != null">
        #{balType},
      </if>
      <if test="bailCertificateNo != null">
        #{bailCertificateNo},
      </if>
      <if test="tranAmt != null">
        #{tranAmt},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="nosVosNo != null">
        #{nosVosNo},
      </if>
      <if test="errorMsg != null">
        #{errorMsg},
      </if>
      <if test="lastProcStamp != null">
        #{lastProcStamp},
      </if>
      <if test="acceptContractNo != null">
        #{acceptContractNo},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="bailOpenBranch != null">
        #{bailOpenBranch},
      </if>
      <if test="lastChangeUser != null">
        #{lastChangeUser},
      </if>
    </trim>
  </insert>

  <!-- added by liulff at 20171113 for 根据机构号查询银承结算信息, 机构撤并使用 -->
  <select id="selectMbAdSettleByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    select <include refid="Base_Column"/>
    from RB_AD_SETTLE
    where TRAN_BRANCH = #{tranBranch}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectMbAdSettleBySettleAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    select <include refid="Base_Column"/>
    from RB_AD_SETTLE
    where SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey}
    <if test="baseAcctNo != null ">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="settleAcctType != null ">
      AND SETTLE_ACCT_TYPE = #{settleAcctType}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectMbAdSettleByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle">
    select <include refid="Base_Column"/>
    from RB_AD_SETTLE
    where SETTLE_ACCT_INTERNAL_KEY = #{settleAcctInternalKey}
    <if test="settleAcctType != null ">
      AND SETTLE_ACCT_TYPE = #{settleAcctType}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getMbAdSettleByBackReference" parameterType="java.util.Map" resultType="java.util.Map">
    select DISTINCT A.CLIENT_NAME BILL_ACCEPT_NAME,A.BILL_NO BILL_NO
    from RB_AD_REGISTER A,RB_AD_SETTLE B
    where
      A.SETTLE_ACCT_INTERNAL_KEY = B.SETTLE_ACCT_INTERNAL_KEY
      AND B.BASE_ACCT_NO = #{baseAcctNo}
      AND B.BAIL_CERTIFICATE_NO = #{bailCertificateNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!-- 机构撤并更新机构号 -->
  <update id="updateByBranch" parameterType="java.util.Map">
    UPDATE RB_AD_SETTLE SET tranBranch = #{newBranch} WHERE tranBranch = #{oldBranch}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

</mapper>
