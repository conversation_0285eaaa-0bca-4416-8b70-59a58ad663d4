<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchDossAcct">
	<select id="selectByContrastBatNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchDossAcct">
    select *
    from RB_BATCH_DOSS_ACCT
    where BATCH_NO = #{batchNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
  </select>
	<select id="getSumByDossAcct" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchDossAcct" resultType="java.math.BigDecimal">
		select SUM (1)
		from RB_BATCH_DOSS_ACCT
		<where>
		<if test="batchNo != null and  batchNo != '' ">
			AND BATCH_NO = #{batchNo}
		</if>
		<if test="batchStatus != null and  batchStatus != '' ">
			AND BATCH_STATUS = #{batchStatus}
		</if>
		<if test="isIndividualFlag != null and  isIndividualFlag != '' ">
			AND IS_INDIVIDUAL_FLAG = #{isIndividualFlag}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>
	<select id="getSumPorIntTotByBatchNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchDossAcct" resultType="java.math.BigDecimal">
		select SUM (POR_INT_TOT)
		from RB_BATCH_DOSS_ACCT
		<where>
		<if test="batchNo != null and  batchNo != '' ">
			AND BATCH_NO = #{batchNo}
		</if>
		<if test="batchStatus != null and  batchStatus != '' ">
			AND BATCH_STATUS = #{batchStatus}
		</if>
		<if test="isIndividualFlag != null and  isIndividualFlag != '' ">
			AND IS_INDIVIDUAL_FLAG = #{isIndividualFlag}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>

</mapper>
