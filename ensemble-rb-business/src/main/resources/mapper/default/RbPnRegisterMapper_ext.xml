<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister">

  <select id="selectByBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister">
    select <include refid="Base_Column"/>
    from RB_PN_REGISTER
    where DOC_TYPE = #{docType}
    AND BILL_NO = #{billNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByBillType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister">
    select <include refid="Base_Column"/>
    from RB_PN_REGISTER
    where BILL_NO = #{billNo}
    <if test="billType != null and billType != ''">
      and BILL_TYPE = #{billType}
    </if>
    <if test="docType != null and docType != ''">
      and DOC_TYPE = #{docType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>



  <select id="selectMbPdRegisterList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister">
    SELECT <include refid="Base_Column"/>
    FROM RB_PN_REGISTER
    <where>
    <if test="branch != null and branch !=''">
      AND BILL_SIGN_BRANCH = #{branch}
    </if>
    <if test="billStatus != null and billStatus !=''">
      AND BILL_STATUS = #{billStatus}
    </if>
    <if test="startDate != null and startDate !=''">
      <![CDATA[AND LAST_TRAN_DATE >= #{startDate}]]>
    </if>
    <if test="endDate != null and endDate !=''">
      <![CDATA[AND LAST_TRAN_DATE <= #{endDate}]]>
    </if>
    <if test="billNo != null and billNo !=''">
      AND BILL_NO = #{billNo}
    </if>
    <if test="serialNo != null and serialNo !=''">
      AND SERIAL_NO = #{serialNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>


  <select id="selectRegisterByBillNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister">
    select <include refid="Base_Column"/>
    from RB_PN_REGISTER
    <where>
    <if test="billNo != null and billNo !=''">
      AND   BILL_NO = #{billNo}
    </if>
    <if test="billStatus != null and billStatus !=''">
      AND BILL_STATUS = #{billStatus}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="getPromissoryNote" parameterType="java.util.Map" resultType="java.util.Map">
    select mptd.REFERENCE
    from RB_PN_REGISTER mpr,MB_PN_TRAN_DETAIL mptd
    where mpr.BILL_STATUS = #{billStatus}
    AND mpr.BILL_APPLY_DATE=#{billApplyDate}
    AND mpr.BILL_SIGN_BRANCH=#{billSignBranch}
    AND mpr.SERIAL_NO = mptd.ORIG_SERIAL_NO
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND mpr.COMPANY = #{company}
    </if>
  </select>
</mapper>
