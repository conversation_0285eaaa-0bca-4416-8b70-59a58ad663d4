<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageClassLimit">

<!-- Query based on period code -->
	<select id="selectStageClassLimitListByStageCode" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageClassLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_CLASS_LIMIT
		<where>
			<trim suffixOverrides="AND">
				<if test="stageCode != null and  stageCode != '' ">
					STAGE_CODE = #{stageCode}  AND
				</if>
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					COMPANY = #{company} AND
				</if>
			</trim>
		</where>
	</select>

<!-- Query the list of period codes based on quota category -->
	<select id="selectStageListByClassLimit" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbStageClassLimit">
		SELECT
		<include refid="Base_Column" />
		FROM
		RB_STAGE_CLASS_LIMIT
		<where>
		<if test="limitClass!= null">
			AND LIMIT_CLASS = #{limitClass}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>

</mapper>
