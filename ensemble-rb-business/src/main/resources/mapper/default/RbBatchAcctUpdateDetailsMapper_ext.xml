<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchAcctUpdateDetails">

    <select id="selectRbBatchAcctUpdateDetailsForUpdate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchAcctUpdateDetails"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchAcctUpdateDetails">
        SELECT <include refid="Base_Column"/>
        FROM RB_BATCH_ACCT_UPDATE_DETAILS
        WHERE BATCH_NO = #{batchNo}
        AND SEQ_NO = #{seqNo}
        AND BATCH_STATUS = 'P' FOR UPDATE
    </select>

    <update id="updateStatusNoTransaction"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchAcctUpdateDetails">
        UPDATE RB_BATCH_ACCT_UPDATE_DETAILS
        <set>
            BATCH_STATUS = #{batchStatus}
        </set>
        WHERE BATCH_NO = #{batchNo, jdbcType=VARCHAR}
        AND SEQ_NO = #{seqNo, jdbcType=VARCHAR}
        AND BATCH_STATUS = 'P'
    </update>

    <update id="updRbBatchAcctUpdateDetails" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchAcctUpdateDetails">
        update RB_BATCH_ACCT_UPDATE_DETAILS
        <set>
            <if test="retMsg != null and  retMsg != '' ">
                Ret_Msg = #{retMsg, jdbcType=VARCHAR},
            </if>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null and  errorCode != '' ">
                Error_Code = #{errorCode, jdbcType=VARCHAR},
            </if>
            <if test="errorDesc != null and  errorDesc != '' ">
                Error_Desc = #{errorDesc, jdbcType=VARCHAR},
            </if>
        </set>
        where
            BATCH_NO = #{batchNo, jdbcType=VARCHAR}
            AND SEQ_NO = #{seqNo, jdbcType=VARCHAR}
    </update>

</mapper>
