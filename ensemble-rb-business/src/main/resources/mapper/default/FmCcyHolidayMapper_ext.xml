<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.fm.model.FmCcyHoliday">

    <select id="selectOneFmCcyHoliday" parameterType="java.util.Map"  resultType="com.dcits.ensemble.fm.model.FmCcyHoliday" databaseId="oracle" useCache="false" flushCache="true">
        select CCY,HOLIDAY_TYPE,HOLIDAY_DESC,APPLY_IND,HOLIDAY_DATE,COMPANY,TRAN_TIMESTAMP,HUB_BATCH_FLAG
        from FM_CCY_HOLIDAY where CCY = #{ccy} and HOLIDAY_DATE = #{holidayDate}
    </select>


</mapper>