<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenInnerParam">
    <!-- Created by admin on 2017/03/28 11:09:52. -->
    <select id="selectOneByTranTypeAndBranch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenInnerParam">
        select *
        from RB_OPEN_INNER_PARAM
        where 1=1
        <if test="tranType != null">
            AND TRAN_TYPE = #{tranType}
        </if>
        <if test="branch != null">
            AND BRANCH = #{branch}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectOneForUpdate" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenInnerParam">
        select *
        from RB_OPEN_INNER_PARAM
        where 1=1
        <if test="tranType != null">
            AND TRAN_TYPE = #{tranType}
        </if>
        <if test="branch != null">
            AND BRANCH = #{branch}
        </if>
        <if test="prodType != null">
            AND PROD_TYPE = #{prodType}
        </if>
        <if test="openBasis != null">
            AND OPEN_BASIS = #{openBasis}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        for update
    </select>

    <select id="selectOneByOpenBasis" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenInnerParam">
        select *
        from RB_OPEN_INNER_PARAM
        where 1=1
        <if test="openBasis != null">
            AND OPEN_BASIS = #{openBasis}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="selectOneByTranTypeAndBranchAndOpenBasis" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenInnerParam">
        select *
        from RB_OPEN_INNER_PARAM
        where 1=1
        <if test="tranType != null">
            AND TRAN_TYPE = #{tranType}
        </if>
        <if test="branch != null">
            AND BRANCH = #{branch}
        </if>
        <if test="openBasis != null">
            AND OPEN_BASIS = #{openBasis}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <update id="updateForOpenBasis" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenInnerParam">
        UPDATE
        RB_OPEN_INNER_PARAM
        <set>
            <if test="openBasis != null">
                OPEN_BASIS = #{openBasis}
            </if>
        </set>
        WHERE TRAN_TYPE = #{tranType}
        <if test="branch != null">
            AND BRANCH = #{branch}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

</mapper>
