<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.EgTranHistSource">

    <sql id="Table_Name">
        ENS_EG.EG_TRAN_HIST_SOURCE
    </sql>

    <sql id="Base_Column">
        <trim suffixOverrides=",">
            SYSTEM_ID,
            TRAN_DATE,
            CHANNEL_SEQ_NO,
            REFERENCE,
            TRAN_BRANCH,
            <PERSON><PERSON>CH,
            TRAN_TYPE,
            PROD_TYPE,
            EVENT_TYPE,
            AMT_TYPE,
            CCY,
            AMOUNT,
            CLIENT_NO,
            BASE_ACCT_NO,
            SOURCE_TYPE,
            OTH_BRANCH,
            <PERSON><PERSON><PERSON><PERSON><PERSON>,
            <PERSON><PERSON><PERSON><PERSON><PERSON>_SEQ_NO,
            REVE<PERSON><PERSON>_DATE,
            SEQ_NO,
            SOURCE_MODULE,
            DATA_FLAG,
            DEAL_FLAG,
            CR_DR_IND,
            GL_CODE,
            INTERNAL_KEY,
            INP_EOD_FLAG,
            ACCOUNTING_STATUS,
            AMOUNT_NATURE,
            INT_TAX_LEVY,
            INT_IND_FLAG,
            ACCT_SEQ_NO,
            ACCT_BRANCH,
            TIMESTAMP,
            EG_TIMESTAMP,
            FEE_CHARGE_METHOD,
        </trim>
    </sql>

    <resultMap id="Base_Result_Map" type="com.dcits.ensemble.rb.business.model.entity.dbmodel.EgTranHistSource">
        <result property="systemId" column="SYSTEM_ID"/>
        <result property="tranDate" column="TRAN_DATE"/>
        <result property="channelSeqNo" column="CHANNEL_SEQ_NO"/>
        <result property="reference" column="REFERENCE"/>
        <result property="tranBranch" column="TRAN_BRANCH"/>
        <result property="branch" column="BRANCH"/>
        <result property="tranType" column="TRAN_TYPE"/>
        <result property="prodType" column="PROD_TYPE"/>
        <result property="eventType" column="EVENT_TYPE"/>
        <result property="amtType" column="AMT_TYPE"/>
        <result property="ccy" column="CCY"/>
        <result property="amount" column="AMOUNT"/>
        <result property="clientNo" column="CLIENT_NO"/>
        <result property="baseAcctNo" column="BASE_ACCT_NO"/>
        <result property="sourceType" column="SOURCE_TYPE"/>
        <result property="othBranch" column="OTH_BRANCH"/>
        <result property="reversal" column="REVERSAL"/>
        <result property="reversalSeqNo" column="REVERSAL_SEQ_NO"/>
        <result property="reversalDate" column="REVERSAL_DATE"/>
        <result property="seqNo" column="SEQ_NO"/>
        <result property="sourceModule" column="SOURCE_MODULE"/>
        <result property="dataFlag" column="DATA_FLAG"/>
        <result property="dealFlag" column="DEAL_FLAG"/>
        <result property="crDrInd" column="CR_DR_IND"/>
        <result property="glCode" column="GL_CODE"/>
        <result property="internalKey" column="INTERNAL_KEY"/>
        <result property="inpEodFlag" column="INP_EOD_FLAG"/>
        <result property="accountingStatus" column="ACCOUNTING_STATUS"/>
        <result property="amountNature" column="AMOUNT_NATURE"/>
        <result property="intTaxLevy" column="INT_TAX_LEVY"/>
        <result property="intIndFlag" column="INT_IND_FLAG"/>
        <result property="acctSeqNo" column="ACCT_SEQ_NO"/>
        <result property="acctBranch" column="ACCT_BRANCH"/>
        <result property="timestamp" column="TIMESTAMP"/>
        <result property="egTimestamp" column="EG_TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Where">
        <where>
            <if test="systemId != null and  systemId != '' ">
                AND SYSTEM_ID = #{systemId,jdbcType=VARCHAR}
            </if>
            <if test="tranDate != null ">
                AND TRAN_DATE = #{tranDate,jdbcType=DATE}
            </if>
            <if test="channelSeqNo != null and  channelSeqNo != '' ">
                AND CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="reference != null and  reference != '' ">
                AND REFERENCE = #{reference,jdbcType=VARCHAR}
            </if>
            <if test="tranBranch != null and  tranBranch != '' ">
                AND TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="branch != null and  branch != '' ">
                AND BRANCH = #{branch,jdbcType=VARCHAR}
            </if>
            <if test="tranType != null and  tranType != '' ">
                AND TRAN_TYPE = #{tranType,jdbcType=VARCHAR}
            </if>
            <if test="prodType != null and  prodType != '' ">
                AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
            </if>
            <if test="eventType != null and  eventType != '' ">
                AND EVENT_TYPE = #{eventType,jdbcType=VARCHAR}
            </if>
            <if test="amtType != null and  amtType != '' ">
                AND AMT_TYPE = #{amtType,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null and  ccy != '' ">
                AND CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="amount != null ">
                AND AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="clientNo != null and  clientNo != '' ">
                AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
            </if>
            <if test="baseAcctNo != null and  baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="sourceType != null and  sourceType != '' ">
                AND SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
            </if>
            <if test="othBranch != null and  othBranch != '' ">
                AND OTH_BRANCH = #{othBranch,jdbcType=VARCHAR}
            </if>
            <if test="reversal != null and  reversal != '' ">
                AND REVERSAL = #{reversal,jdbcType=VARCHAR}
            </if>
            <if test="reversalSeqNo != null and  reversalSeqNo != '' ">
                AND REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="reversalDate != null ">
                AND REVERSAL_DATE = #{reversalDate,jdbcType=DATE}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
            <if test="sourceModule != null and  sourceModule != '' ">
                AND SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="dataFlag != null and  dataFlag != '' ">
                AND DATA_FLAG = #{dataFlag,jdbcType=VARCHAR}
            </if>
            <if test="dealFlag != null and  dealFlag != '' ">
                AND DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="crDrInd != null and  crDrInd != '' ">
                AND CR_DR_IND = #{crDrInd,jdbcType=VARCHAR}
            </if>
            <if test="glCode != null and  glCode != '' ">
                AND GL_CODE = #{glCode,jdbcType=VARCHAR}
            </if>
            <if test="internalKey != null ">
                AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
            </if>
            <if test="inpEodFlag != null and  inpEodFlag != '' ">
                AND INP_EOD_FLAG = #{inpEodFlag,jdbcType=VARCHAR}
            </if>
            <if test="accountingStatus != null and  accountingStatus != '' ">
                AND ACCOUNTING_STATUS = #{accountingStatus,jdbcType=VARCHAR}
            </if>
            <if test="amountNature != null and  amountNature != '' ">
                AND AMOUNT_NATURE = #{amountNature,jdbcType=VARCHAR}
            </if>
            <if test="intTaxLevy != null and  intTaxLevy != '' ">
                AND INT_TAX_LEVY = #{intTaxLevy,jdbcType=VARCHAR}
            </if>
            <if test="intIndFlag != null and  intIndFlag != '' ">
                AND INT_IND_FLAG = #{intIndFlag,jdbcType=VARCHAR}
            </if>
            <if test="acctSeqNo != null and  acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="acctBranch != null and  acctBranch != '' ">
                AND ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR}
            </if>
            <if test="timestamp != null and  timestamp != '' ">
                AND TIMESTAMP = #{timestamp,jdbcType=VARCHAR}
            </if>
            <if test="egTimestamp != null and  egTimestamp != '' ">
                AND EG_TIMESTAMP = #{egTimestamp,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="PrimaryKey_Where">
        <where>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <sql id="Base_Set">
        <set>
            <if test="systemId != null and systemId != '' ">
                SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="tranDate != null ">
                TRAN_DATE = #{tranDate,jdbcType=DATE},
            </if>
            <if test="channelSeqNo != null and channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="reference != null and reference != '' ">
                REFERENCE = #{reference,jdbcType=VARCHAR},
            </if>
            <if test="tranBranch != null and tranBranch != '' ">
                TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="branch != null and branch != '' ">
                BRANCH = #{branch,jdbcType=VARCHAR},
            </if>
            <if test="tranType != null and tranType != '' ">
                TRAN_TYPE = #{tranType,jdbcType=VARCHAR},
            </if>
            <if test="prodType != null and prodType != '' ">
                PROD_TYPE = #{prodType,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null and eventType != '' ">
                EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="amtType != null and amtType != '' ">
                AMT_TYPE = #{amtType,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null and ccy != '' ">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="amount != null ">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="baseAcctNo != null and baseAcctNo != '' ">
                BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null and sourceType != '' ">
                SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="othBranch != null and othBranch != '' ">
                OTH_BRANCH = #{othBranch,jdbcType=VARCHAR},
            </if>
            <if test="reversal != null and reversal != '' ">
                REVERSAL = #{reversal,jdbcType=VARCHAR},
            </if>
            <if test="reversalSeqNo != null and reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{reversalSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="reversalDate != null ">
                REVERSAL_DATE = #{reversalDate,jdbcType=DATE},
            </if>
            <if test="seqNo != null and seqNo != '' ">
                SEQ_NO = #{seqNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceModule != null and sourceModule != '' ">
                SOURCE_MODULE = #{sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="dataFlag != null and dataFlag != '' ">
                DATA_FLAG = #{dataFlag,jdbcType=VARCHAR},
            </if>
            <if test="dealFlag != null and dealFlag != '' ">
                DEAL_FLAG = #{dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="crDrInd != null and crDrInd != '' ">
                CR_DR_IND = #{crDrInd,jdbcType=VARCHAR},
            </if>
            <if test="glCode != null and glCode != '' ">
                GL_CODE = #{glCode,jdbcType=VARCHAR},
            </if>
            <if test="internalKey != null ">
                INTERNAL_KEY = #{internalKey,jdbcType=BIGINT},
            </if>
            <if test="inpEodFlag != null and inpEodFlag != '' ">
                INP_EOD_FLAG = #{inpEodFlag,jdbcType=VARCHAR},
            </if>
            <if test="accountingStatus != null and accountingStatus != '' ">
                ACCOUNTING_STATUS = #{accountingStatus,jdbcType=VARCHAR},
            </if>
            <if test="amountNature != null and amountNature != '' ">
                AMOUNT_NATURE = #{amountNature,jdbcType=VARCHAR},
            </if>
            <if test="intTaxLevy != null and intTaxLevy != '' ">
                INT_TAX_LEVY = #{intTaxLevy,jdbcType=VARCHAR},
            </if>
            <if test="intIndFlag != null and intIndFlag != '' ">
                INT_IND_FLAG = #{intIndFlag,jdbcType=VARCHAR},
            </if>
            <if test="acctSeqNo != null and acctSeqNo != '' ">
                ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="acctBranch != null and acctBranch != '' ">
                ACCT_BRANCH = #{acctBranch,jdbcType=VARCHAR},
            </if>
            <if test="timestamp != null and timestamp != '' ">
                TIMESTAMP = #{timestamp,jdbcType=VARCHAR},
            </if>
            <if test="egTimestamp != null and egTimestamp != '' ">
                EG_TIMESTAMP = #{egTimestamp,jdbcType=VARCHAR},
            </if>
        </set>
    </sql>

    <sql id="Base_Select">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </sql>

    <sql id="comet_step_column">
        <if test="cometSqlType == 'segment'">${cometKeyField} as KEY_FIELD</if>
        <if test="cometSqlType == 'page'">*</if>
    </sql>

    <sql id="comet_step_where">
        <if test="cometStart != null and cometEnd != null ">and ${cometKeyField} between #{cometStart} and #{cometEnd}
        </if>
    </sql>

    <sql id="comet_row_num_step_column">
        <if test="cometSqlType == 'total'">count(*) AS TOTAL</if>
        <if test="cometSqlType == 'offset'">*</if>
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemId != null ">SYSTEM_ID,</if>
            <if test="tranDate != null ">TRAN_DATE,</if>
            <if test="channelSeqNo != null ">CHANNEL_SEQ_NO,</if>
            <if test="reference != null ">REFERENCE,</if>
            <if test="tranBranch != null ">TRAN_BRANCH,</if>
            <if test="branch != null ">BRANCH,</if>
            <if test="tranType != null ">TRAN_TYPE,</if>
            <if test="prodType != null ">PROD_TYPE,</if>
            <if test="eventType != null ">EVENT_TYPE,</if>
            <if test="amtType != null ">AMT_TYPE,</if>
            <if test="ccy != null ">CCY,</if>
            <if test="amount != null ">AMOUNT,</if>
            <if test="clientNo != null ">CLIENT_NO,</if>
            <if test="baseAcctNo != null ">BASE_ACCT_NO,</if>
            <if test="sourceType != null ">SOURCE_TYPE,</if>
            <if test="othBranch != null ">OTH_BRANCH,</if>
            <if test="reversal != null ">REVERSAL,</if>
            <if test="reversalSeqNo != null ">REVERSAL_SEQ_NO,</if>
            <if test="reversalDate != null ">REVERSAL_DATE,</if>
            <if test="seqNo != null ">SEQ_NO,</if>
            <if test="sourceModule != null ">SOURCE_MODULE,</if>
            <if test="dataFlag != null ">DATA_FLAG,</if>
            <if test="dealFlag != null ">DEAL_FLAG,</if>
            <if test="crDrInd != null ">CR_DR_IND,</if>
            <if test="glCode != null ">GL_CODE,</if>
            <if test="internalKey != null ">INTERNAL_KEY,</if>
            <if test="inpEodFlag != null ">INP_EOD_FLAG,</if>
            <if test="accountingStatus != null ">ACCOUNTING_STATUS,</if>
            <if test="amountNature != null ">AMOUNT_NATURE,</if>
            <if test="intTaxLevy != null ">INT_TAX_LEVY,</if>
            <if test="intIndFlag != null ">INT_IND_FLAG,</if>
            <if test="acctSeqNo != null ">ACCT_SEQ_NO,</if>
            <if test="acctBranch != null ">ACCT_BRANCH,</if>
            <if test="timestamp != null ">TIMESTAMP,</if>
            <if test="egTimestamp != null ">EG_TIMESTAMP,</if>
            <if test="feeChargeMethod != null" >FEE_CHARGE_METHOD,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemId != null ">#{systemId,jdbcType=VARCHAR},</if>
            <if test="tranDate != null ">#{tranDate,jdbcType=DATE},</if>
            <if test="channelSeqNo != null ">#{channelSeqNo,jdbcType=VARCHAR},</if>
            <if test="reference != null ">#{reference,jdbcType=VARCHAR},</if>
            <if test="tranBranch != null ">#{tranBranch,jdbcType=VARCHAR},</if>
            <if test="branch != null ">#{branch,jdbcType=VARCHAR},</if>
            <if test="tranType != null ">#{tranType,jdbcType=VARCHAR},</if>
            <if test="prodType != null ">#{prodType,jdbcType=VARCHAR},</if>
            <if test="eventType != null ">#{eventType,jdbcType=VARCHAR},</if>
            <if test="amtType != null ">#{amtType,jdbcType=VARCHAR},</if>
            <if test="ccy != null ">#{ccy,jdbcType=VARCHAR},</if>
            <if test="amount != null ">#{amount,jdbcType=DECIMAL},</if>
            <if test="clientNo != null ">#{clientNo,jdbcType=VARCHAR},</if>
            <if test="baseAcctNo != null ">#{baseAcctNo,jdbcType=VARCHAR},</if>
            <if test="sourceType != null ">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="othBranch != null ">#{othBranch,jdbcType=VARCHAR},</if>
            <if test="reversal != null ">#{reversal,jdbcType=VARCHAR},</if>
            <if test="reversalSeqNo != null ">#{reversalSeqNo,jdbcType=VARCHAR},</if>
            <if test="reversalDate != null ">#{reversalDate,jdbcType=DATE},</if>
            <if test="seqNo != null ">#{seqNo,jdbcType=VARCHAR},</if>
            <if test="sourceModule != null ">#{sourceModule,jdbcType=VARCHAR},</if>
            <if test="dataFlag != null ">#{dataFlag,jdbcType=VARCHAR},</if>
            <if test="dealFlag != null ">#{dealFlag,jdbcType=VARCHAR},</if>
            <if test="crDrInd != null ">#{crDrInd,jdbcType=VARCHAR},</if>
            <if test="glCode != null ">#{glCode,jdbcType=VARCHAR},</if>
            <if test="internalKey != null ">#{internalKey,jdbcType=BIGINT},</if>
            <if test="inpEodFlag != null ">#{inpEodFlag,jdbcType=VARCHAR},</if>
            <if test="accountingStatus != null ">#{accountingStatus,jdbcType=VARCHAR},</if>
            <if test="amountNature != null ">#{amountNature,jdbcType=VARCHAR},</if>
            <if test="intTaxLevy != null ">#{intTaxLevy,jdbcType=VARCHAR},</if>
            <if test="intIndFlag != null ">#{intIndFlag,jdbcType=VARCHAR},</if>
            <if test="acctSeqNo != null ">#{acctSeqNo,jdbcType=VARCHAR},</if>
            <if test="acctBranch != null ">#{acctBranch,jdbcType=VARCHAR},</if>
            <if test="timestamp != null ">#{timestamp,jdbcType=VARCHAR},</if>
            <if test="egTimestamp != null ">#{egTimestamp,jdbcType=VARCHAR},</if>
            <if test="feeChargeMethod != null" >#{feeChargeMethod,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>

    <update id="updateByPrimaryKey">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        <include refid="PrimaryKey_Where"/>
    </update>


    <update id="updateByEntity">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            <if test="s.systemId != null and s.systemId != '' ">
                SYSTEM_ID = #{s.systemId,jdbcType=VARCHAR},
            </if>
            <if test="s.tranDate != null ">
                TRAN_DATE = #{s.tranDate,jdbcType=DATE},
            </if>
            <if test="s.channelSeqNo != null and s.channelSeqNo != '' ">
                CHANNEL_SEQ_NO = #{s.channelSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.reference != null and s.reference != '' ">
                REFERENCE = #{s.reference,jdbcType=VARCHAR},
            </if>
            <if test="s.tranBranch != null and s.tranBranch != '' ">
                TRAN_BRANCH = #{s.tranBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.branch != null and s.branch != '' ">
                BRANCH = #{s.branch,jdbcType=VARCHAR},
            </if>
            <if test="s.tranType != null and s.tranType != '' ">
                TRAN_TYPE = #{s.tranType,jdbcType=VARCHAR},
            </if>
            <if test="s.prodType != null and s.prodType != '' ">
                PROD_TYPE = #{s.prodType,jdbcType=VARCHAR},
            </if>
            <if test="s.eventType != null and s.eventType != '' ">
                EVENT_TYPE = #{s.eventType,jdbcType=VARCHAR},
            </if>
            <if test="s.amtType != null and s.amtType != '' ">
                AMT_TYPE = #{s.amtType,jdbcType=VARCHAR},
            </if>
            <if test="s.ccy != null and s.ccy != '' ">
                CCY = #{s.ccy,jdbcType=VARCHAR},
            </if>
            <if test="s.amount != null ">
                AMOUNT = #{s.amount,jdbcType=DECIMAL},
            </if>
            <if test="s.baseAcctNo != null and s.baseAcctNo != '' ">
                BASE_ACCT_NO = #{s.baseAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceType != null and s.sourceType != '' ">
                SOURCE_TYPE = #{s.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="s.othBranch != null and s.othBranch != '' ">
                OTH_BRANCH = #{s.othBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.reversal != null and s.reversal != '' ">
                REVERSAL = #{s.reversal,jdbcType=VARCHAR},
            </if>
            <if test="s.reversalSeqNo != null and s.reversalSeqNo != '' ">
                REVERSAL_SEQ_NO = #{s.reversalSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.reversalDate != null ">
                REVERSAL_DATE = #{s.reversalDate,jdbcType=DATE},
            </if>
            <if test="s.seqNo != null and s.seqNo != '' ">
                SEQ_NO = #{s.seqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.sourceModule != null and s.sourceModule != '' ">
                SOURCE_MODULE = #{s.sourceModule,jdbcType=VARCHAR},
            </if>
            <if test="s.dataFlag != null and s.dataFlag != '' ">
                DATA_FLAG = #{s.dataFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.dealFlag != null and s.dealFlag != '' ">
                DEAL_FLAG = #{s.dealFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.crDrInd != null and s.crDrInd != '' ">
                CR_DR_IND = #{s.crDrInd,jdbcType=VARCHAR},
            </if>
            <if test="s.glCode != null and s.glCode != '' ">
                GL_CODE = #{s.glCode,jdbcType=VARCHAR},
            </if>
            <if test="s.internalKey != null ">
                INTERNAL_KEY = #{s.internalKey,jdbcType=BIGINT},
            </if>
            <if test="s.inpEodFlag != null and s.inpEodFlag != '' ">
                INP_EOD_FLAG = #{s.inpEodFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.accountingStatus != null and s.accountingStatus != '' ">
                ACCOUNTING_STATUS = #{s.accountingStatus,jdbcType=VARCHAR},
            </if>
            <if test="s.amountNature != null and s.amountNature != '' ">
                AMOUNT_NATURE = #{s.amountNature,jdbcType=VARCHAR},
            </if>
            <if test="s.intTaxLevy != null and s.intTaxLevy != '' ">
                INT_TAX_LEVY = #{s.intTaxLevy,jdbcType=VARCHAR},
            </if>
            <if test="s.intIndFlag != null and s.intIndFlag != '' ">
                INT_IND_FLAG = #{s.intIndFlag,jdbcType=VARCHAR},
            </if>
            <if test="s.acctSeqNo != null and s.acctSeqNo != '' ">
                ACCT_SEQ_NO = #{s.acctSeqNo,jdbcType=VARCHAR},
            </if>
            <if test="s.acctBranch != null and s.acctBranch != '' ">
                ACCT_BRANCH = #{s.acctBranch,jdbcType=VARCHAR},
            </if>
            <if test="s.timestamp != null and s.timestamp != '' ">
                TIMESTAMP = #{s.timestamp,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="w.systemId != null and w.systemId != '' ">
                AND SYSTEM_ID = #{w.systemId,jdbcType=VARCHAR}
            </if>
            <if test="w.tranDate != null ">
                AND TRAN_DATE = #{w.tranDate,jdbcType=DATE}
            </if>
            <if test="w.channelSeqNo != null and w.channelSeqNo != '' ">
                AND CHANNEL_SEQ_NO = #{w.channelSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.reference != null and w.reference != '' ">
                AND REFERENCE = #{w.reference,jdbcType=VARCHAR}
            </if>
            <if test="w.tranBranch != null and w.tranBranch != '' ">
                AND TRAN_BRANCH = #{w.tranBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.branch != null and w.branch != '' ">
                AND BRANCH = #{w.branch,jdbcType=VARCHAR}
            </if>
            <if test="w.tranType != null and w.tranType != '' ">
                AND TRAN_TYPE = #{w.tranType,jdbcType=VARCHAR}
            </if>
            <if test="w.prodType != null and w.prodType != '' ">
                AND PROD_TYPE = #{w.prodType,jdbcType=VARCHAR}
            </if>
            <if test="w.eventType != null and w.eventType != '' ">
                AND EVENT_TYPE = #{w.eventType,jdbcType=VARCHAR}
            </if>
            <if test="w.amtType != null and w.amtType != '' ">
                AND AMT_TYPE = #{w.amtType,jdbcType=VARCHAR}
            </if>
            <if test="w.ccy != null and w.ccy != '' ">
                AND CCY = #{w.ccy,jdbcType=VARCHAR}
            </if>
            <if test="w.amount != null ">
                AND AMOUNT = #{w.amount,jdbcType=DECIMAL}
            </if>
            <if test="w.clientNo != null and w.clientNo != '' ">
                AND CLIENT_NO = #{w.clientNo,jdbcType=VARCHAR}
            </if>
            <if test="w.baseAcctNo != null and w.baseAcctNo != '' ">
                AND BASE_ACCT_NO = #{w.baseAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceType != null and w.sourceType != '' ">
                AND SOURCE_TYPE = #{w.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="w.othBranch != null and w.othBranch != '' ">
                AND OTH_BRANCH = #{w.othBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.reversal != null and w.reversal != '' ">
                AND REVERSAL = #{w.reversal,jdbcType=VARCHAR}
            </if>
            <if test="w.reversalSeqNo != null and w.reversalSeqNo != '' ">
                AND REVERSAL_SEQ_NO = #{w.reversalSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.reversalDate != null ">
                AND REVERSAL_DATE = #{w.reversalDate,jdbcType=DATE}
            </if>
            <if test="w.seqNo != null and w.seqNo != '' ">
                AND SEQ_NO = #{w.seqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.sourceModule != null and w.sourceModule != '' ">
                AND SOURCE_MODULE = #{w.sourceModule,jdbcType=VARCHAR}
            </if>
            <if test="w.dataFlag != null and w.dataFlag != '' ">
                AND DATA_FLAG = #{w.dataFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.dealFlag != null and w.dealFlag != '' ">
                AND DEAL_FLAG = #{w.dealFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.crDrInd != null and w.crDrInd != '' ">
                AND CR_DR_IND = #{w.crDrInd,jdbcType=VARCHAR}
            </if>
            <if test="w.glCode != null and w.glCode != '' ">
                AND GL_CODE = #{w.glCode,jdbcType=VARCHAR}
            </if>
            <if test="w.internalKey != null ">
                AND INTERNAL_KEY = #{w.internalKey,jdbcType=BIGINT}
            </if>
            <if test="w.inpEodFlag != null and w.inpEodFlag != '' ">
                AND INP_EOD_FLAG = #{w.inpEodFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.accountingStatus != null and w.accountingStatus != '' ">
                AND ACCOUNTING_STATUS = #{w.accountingStatus,jdbcType=VARCHAR}
            </if>
            <if test="w.amountNature != null and w.amountNature != '' ">
                AND AMOUNT_NATURE = #{w.amountNature,jdbcType=VARCHAR}
            </if>
            <if test="w.intTaxLevy != null and w.intTaxLevy != '' ">
                AND INT_TAX_LEVY = #{w.intTaxLevy,jdbcType=VARCHAR}
            </if>
            <if test="w.intIndFlag != null and w.intIndFlag != '' ">
                AND INT_IND_FLAG = #{w.intIndFlag,jdbcType=VARCHAR}
            </if>
            <if test="w.acctSeqNo != null and w.acctSeqNo != '' ">
                AND ACCT_SEQ_NO = #{w.acctSeqNo,jdbcType=VARCHAR}
            </if>
            <if test="w.acctBranch != null and w.acctBranch != '' ">
                AND ACCT_BRANCH = #{w.acctBranch,jdbcType=VARCHAR}
            </if>
            <if test="w.timestamp != null and w.timestamp != '' ">
                AND TIMESTAMP = #{w.timestamp,jdbcType=VARCHAR}
            </if>
        </where>
    </update>


    <delete id="delete">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </delete>

    <select id="count" parameterType="java.util.Map" resultType="int">
        SELECT count(1) FROM
        <include refid="Table_Name"/>
        <include refid="Base_Where"/>
    </select>

    <select id="selectOne" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectList" resultMap="Base_Result_Map">
        <include refid="Base_Select"/>
    </select>

    <select id="selectForUpdate" resultMap="Base_Result_Map" useCache="false">
        <include refid="Base_Select"/>
        FOR UPDATE
    </select>

    <select id="selectByPrimaryKey" resultMap="Base_Result_Map">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM
        <include refid="Table_Name"/>
        <include refid="PrimaryKey_Where"/>
    </delete>
</mapper>
