<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints">

  <select id="selectByPrimaryKeyExt" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Tue Aug 11 13:56:23 CST 2015.
    -->
    
    select <include refid="Base_Column"/>
    from RB_ACCT_NATURE_RESTRAINTS
    where ACCT_NATURE = #{acctNature}
    <if test="restraintType != null and restraintType != ''">
      AND RESTRAINT_TYPE = #{restraintType}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Tue Aug 11 13:56:23 CST 2015.
    -->

    insert into RB_ACCT_NATURE_RESTRAINTS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="acctNature != null" >
        ACCT_NATURE,
      </if>
      <if test="restraintType != null" >
        RESTRAINT_TYPE,
      </if>
      <if test="company != null" >
        COMPANY,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="acctNature != null" >
        #{acctNature},
      </if>
      <if test="restraintType != null" >
        #{restraintType},
      </if>
      <if test="company != null" >
        #{company},
      </if>

    </trim>
  </insert>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Tue Aug 11 13:56:23 CST 2015.
    -->
    update RB_ACCT_NATURE_RESTRAINTS
    <set >
      <if test="company != null" >
        COMPANY = #{company},
      </if>

    </set>
    where ACCT_NATURE = #{acctNature}
      and RESTRAINT_TYPE = #{restraintType}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <select id="getMbAcctNatResByAcctNa" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_NATURE_RESTRAINTS
    WHERE ACCT_NATURE = #{acctNature}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints">
    delete from RB_ACCT_NATURE_RESTRAINTS
    where ACCT_NATURE = #{acctNature}
    AND RESTRAINT_TYPE= #{restraintType}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
</mapper>
