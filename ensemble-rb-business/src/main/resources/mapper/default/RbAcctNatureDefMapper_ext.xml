<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.bc.unit.acct.repository.MbAcctNatureDefDao">
  <sql id="Base_Column_List">
    ACCT_NATURE,
    NATURE_CLASS,
    ACCT_NATURE_DESC,
    COMPANY,
    TRAN_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKeyExt" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureDef">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Aug 13 11:11:46 CST 2015.
    -->
    select
    <include refid="Base_Column_List"/>
    from RB_ACCT_NATURE_DEF
    where ACCT_NATURE = #{acctNature}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureDef">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Aug 13 11:11:46 CST 2015.
    -->
    insert into RB_ACCT_NATURE_DEF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="acctNature != null">
        ACCT_NATURE,
      </if>
      <if test="acctNatureDesc != null">
        ACCT_NATURE_DESC,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="natureClass != null">
        NATURE_CLASS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="acctNature != null">
        #{acctNature},
      </if>
      <if test="acctNatureDesc != null">
        #{acctNatureDesc},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="natureClass != null">
        #{natureClass},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureDef">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by Galaxy Tools Generator, do not modify.
      This element was generated on Thu Aug 13 11:11:46 CST 2015.
    -->
    update RB_ACCT_NATURE_DEF
    <set>
      <if test="acctNatureDesc != null">
        ACCT_NATURE_DESC = #{acctNatureDesc},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="natureClass != null">
        NATURE_CLASS = #{tranTimestamp}
      </if>
    </set>
    where ACCT_NATURE = #{acctNature}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureDef">
    delete from RB_ACCT_NATURE_DEF
    where ACCT_NATURE = #{acctNature}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
</mapper>
