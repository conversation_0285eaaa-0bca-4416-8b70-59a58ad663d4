<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctLimit">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctLimit"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctLimit">
        select <include refid="Base_Column"/>
        from RB_ACCT_LIMIT
        where INTERNAL_KEY = #{internalKey}
        <if test="sourceType != null">
            AND SOURCE_TYPE = #{sourceType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by RELEASE_PRIORITY asc
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctLimit">
        delete from RB_ACCT_LIMIT
        where INTERNAL_KEY = #{internalKey}
        <if test="sourceType != null">
            AND SOURCE_TYPE = #{sourceType}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctLimit">
        update RB_ACCT_LIMIT
        <set>
            <if test="totalLimit != null">
                TOTAL_LIMIT = #{totalLimit},
            </if>
            <if test="totalTimes != null">
                TOTAL_TIMES = #{totalTimes},
            </if>
            <if test="holdingLimit != null">
                HOLDING_LIMIT = #{holdingLimit},
            </if>
            <if test="holdingTimes != null">
                HOLDING_TIMES = #{holdingTimes},
            </if>
            <if test="releasePriority != null">
                RELEASE_PRIORITY = #{releasePriority},
            </if>
            <if test="lastChangeUserId != null">
                LAST_CHANGE_USER_ID = #{lastChangeUserId},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="dacValue != null">
                DAC_VALUE = #{dacValue},
            </if>
            <if test="company != null">
                COMPANY = #{company}
            </if>
            <if test="holdingLoopLimit != null">
                HOLDING_LOOP_LIMIT = #{holdingLoopLimit},
            </if>
            <if test="totalLoopLimit != null">
                TOTAL_LOOP_LIMIT = #{totalLoopLimit},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where INTERNAL_KEY = #{internalKey}
        AND SOURCE_TYPE = #{sourceType}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctLimit">
        insert into RB_ACCT_LIMIT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="sourceType != null">
                SOURCE_TYPE,
            </if>
            <if test="totalLimit != null">
                TOTAL_LIMIT,
            </if>
            <if test="totalTimes != null">
                TOTAL_TIMES,
            </if>
            <if test="holdingLimit != null">
                HOLDING_LIMIT,
            </if>
            <if test="holdingTimes != null">
                HOLDING_TIMES,
            </if>
            <if test="releasePriority != null">
                RELEASE_PRIORITY,
            </if>
            <if test="lastChangeUserId != null">
                LAST_CHANGE_USER_ID,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="dacValue != null">
                DAC_VALUE,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="totalLimit != null">
                #{totalLimit},
            </if>
            <if test="totalTimes != null">
                #{totalTimes},
            </if>
            <if test="holdingLimit != null">
                #{holdingLimit},
            </if>
            <if test="holdingTimes != null">
                #{holdingTimes},
            </if>
            <if test="releasePriority != null">
                #{releasePriority},
            </if>
            <if test="lastChangeUserId != null">
                #{lastChangeUserId},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate},
            </if>
            <if test="dacValue != null">
                #{dacValue},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
        </trim>
    </insert>
</mapper>
