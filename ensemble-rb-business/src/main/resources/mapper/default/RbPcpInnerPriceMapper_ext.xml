<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPrice">

  <select id="selectEodPcpInnerPrice" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPrice">
    select *
    from RB_PCP_INNER_PRICE
    <where>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    ORDER BY  INTERNAL_KEY
  </select>
  <select id="selectEodPcpInnerPriceAll" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPrice">
    select *
    from RB_PCP_INNER_PRICE
    <where>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    ORDER BY  INTERNAL_KEY
  </select>

  <select id="getMbPcpInnerPriceByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPrice">
    select  <include refid="Base_Column"/>
    from RB_PCP_INNER_PRICE
    where INTERNAL_KEY = #{internalKey}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectDoubleAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPrice">
    select  <include refid="Base_Column"/>
    from RB_PCP_INNER_PRICE
    where PCP_GROUP_ID = #{pcpGroupId}
    AND CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>
</mapper>
