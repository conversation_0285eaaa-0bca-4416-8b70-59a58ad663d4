<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule">

  <select id="selectScheduleByInternalKey" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="java.lang.Long" >
    select <include refid="Base_Column"/>
    from RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey}
      <if test="clientNo != null" >
          and client_no = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by start_date
  </select>

  <select id="selectScheduleByDealDate" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey}
      <if test="clientNo != null" >
          and client_no = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectScheduleByDealDateAfter" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey}
    and NEXT_DEAL_DATE >= #{nextDealDate}
      <if test="clientNo != null" >
          and client_no = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectScheduleByEndDateDecs" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey}
      <if test="clientNo != null" >
          and client_no = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by END_DATE DESC
  </select>

  <select id="selectScheduleBySchedNoDecs" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey}
      <if test="clientNo != null" >
          and client_no = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by SCHED_NO DESC
  </select>
  
  <select id="selectScheduleByEventType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey} and EVENT_TYPE = #{eventType}
    <if test="clientNo != null" >
      and client_no = #{clientNo}
    </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by start_date
  </select>

  <select id="selectSchedulesBySchedMode" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="java.util.Map" >
    select <include refid="Base_Column"/>
    from RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey}
    <if test="schedMode != null" >
      and SCHED_MODE = #{schedMode}
    </if>
    <if test="clientNo != null" >
      and client_no = #{clientNo}
    </if>
    order by start_date
  </select>

  <delete id="deleteScheduleByDealDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" >
    delete
    from  RB_ACCT_SCHEDULE
    where SCHED_NO = #{schedNo}
    and INTERNAL_KEY = #{internalKey}
    and SCHED_NO = #{schedNo}
    and NEXT_DEAL_DATE = #{nextDealDate}
      <if test="clientNo != null" >
          and client_no = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>

  <delete id="deleteScheduleByDealDateAfter" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" >
    delete
    from  RB_ACCT_SCHEDULE
    where  INTERNAL_KEY = #{internalKey}
    and NEXT_DEAL_DATE >= #{nextDealDate}
      <if test="clientNo != null" >
          and client_no = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>

<!--  <select id="selectScheduleForGenInvoice" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" >-->
<!--    select <include refid="Base_Column"/>-->
<!--    from RB_ACCT_SCHEDULE-->
<!--    where NEXT_DEAL_DATE = #{baseParam.runDate}-->
<!--    order by internal_key-->
<!--  </select>-->
  <!-- dao 中没找到 -->
  <select id="selectScheduleForGenInvoiceSplit" resultType="java.util.Map" parameterType="java.util.Map" >
    select COUNT(1) ROW_COUNT
    from RB_ACCT_SCHEDULE
    where NEXT_DEAL_DATE = #{runDate}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!-- 优化sql  wanglpg-->
  <select id="selectScheduleForConvTransferForKeys" parameterType="java.util.Map" resultType="String">
    SELECT internal_key
    FROM RB_ACCT_SCHEDULE
       WHERE EVENT_TYPE = 'REN'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND internal_key BETWEEN #{startKey} and #{endKey}
    ORDER BY internal_key
  </select>
  <select id="selectScheduleForConvTransferForDetail" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule">
    SELECT <include refid="Base_Column"/>
    FROM RB_ACCT_SCHEDULE
    WHERE EVENT_TYPE = 'REN'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND internal_key BETWEEN #{startKey} and #{endKey}
    ORDER BY internal_key
  </select>
<!--  <select id="selectDEDSchedule" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" >-->
<!--    select <include refid="Base_Column"/>-->
<!--    from  (select <include refid="Base_Column"/>-->
<!--    <![CDATA[from RB_ACCT_SCHEDULE ms-->
<!--    where  ms.EVENT_TYPE = 'DED'-->
<!--    and ms.LAST_DEAL_DATE = #{baseParam.runDate}-->
<!--    UNION-->
<!--    select ms.*-->
<!--    from RB_ACCT_SCHEDULE ms,mb_impound_info mii-->
<!--    where ms.sched_no = mii.sched_no-->
<!--    and ms.EVENT_TYPE = 'DED'-->
<!--    and ms.LAST_DEAL_DATE <= #{baseParam.runDate}-->
<!--    and ms.period_freq is null-->
<!--    and mii.impound_amt != mii.transfer_amt ) ms-->
<!--    order by internal_key-->
<!--     ]]>-->
<!--  </select>-->
<!--  <select id="selectQFINTSchedule2" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" >-->

<!--    select <include refid="Base_Column"/>-->
<!--    <![CDATA[from RB_ACCT_SCHEDULE-->
<!--    where  EVENT_TYPE = 'QFINT'-->
<!--    and NEXT_DEAL_DATE = #{baseParam.runDate}-->
<!--     ]]>-->
<!--  </select>-->
  
  <select id="selectQFINTScheduleSplit" resultType="java.util.Map" parameterType="java.util.Map" >
    <if test="_databaseId == 'mysql'">
      SELECT
      MIN(internal_key) START_KEY,
      MAX(internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM
      (
      SELECT
      DISTINCT internal_key,
      @rownum :=@rownum + 1 AS rownum
      FROM
      RB_ACCT_SCHEDULE,
      (SELECT @rownum := -1) t
      where  EVENT_TYPE = 'QFINT'
      and NEXT_DEAL_DATE = #{runDate}
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      ORDER BY internal_key
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT MIN (internal_key) START_KEY, MAX (internal_key) END_KEY,COUNT(1) ROW_COUNT
      FROM (SELECT DISTINCT internal_key
      FROM RB_ACCT_SCHEDULE
      where  EVENT_TYPE = 'QFINT'
      and NEXT_DEAL_DATE = #{runDate}
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      ORDER BY internal_key)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>
  
  <!-- dao 中没找到？ -->
  <select id="selectDEDScheduleSplit" resultType="java.util.Map" parameterType="java.util.Map" >

    select  COUNT(1) ROW_COUNT from  (select 'Y'
    from RB_ACCT_SCHEDULE ms
    where  ms.EVENT_TYPE = 'DED'
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND  ms.COMPANY = #{company}
    </if>
    and ms.LAST_DEAL_DATE = #{runDate}
    UNION
    select 'Y'
    from RB_ACCT_SCHEDULE ms,mb_impound_info mii
        where ms.sched_no = mii.sched_no
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
          AND ms.COMPANY = #{company}
        </if>
        and ms.EVENT_TYPE = 'DED'
        and ms.LAST_DEAL_DATE<![CDATA[ <= #  ]]>{runDate}
        and ms.period_freq is null
        and mii.impound_amt<![CDATA[ !=  ]]> mii.transfer_amt ) ms
        </select>
</mapper>
