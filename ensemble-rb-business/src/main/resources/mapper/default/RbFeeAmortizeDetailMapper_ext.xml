<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeDetail">

  <select id="selectByCondition" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeDetail">
    select <include refid="Base_Column"/>
    from RB_FEE_AMORTIZE_DETAIL
    where 1=1
    <if test="agreementId != null">
      AND AGREEMENT_ID = #{agreementId}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="tranType != null">
      AND TRAN_TYPE = #{tranType}
    </if>
    <if test="feeType != null">
      AND FEE_TYPE = #{feeType}
    </if>
    <if test="amortizePeriodFreq != null">
      AND AMORTIZE_PERIOD_FREQ = #{amortizePeriodFreq}
    </if>
    <if test="amortizePeriodType != null">
      AND AMORTIZE_PERIOD_TYPE = #{amortizePeriodType}
    </if>
    <if test="amortizeMonth != null">
      AND AMORTIZE_MONTH = #{amortizeMonth}
    </if>
    <if test="amortizeDay != null">
      AND AMORTIZE_DAY = #{amortizeDay}
    </if>
    <if test="amortizeCcy != null">
      AND AMORTIZE_CCY = #{amortizeCcy}
    </if>
    <if test="amortizeAmt != null">
      AND AMORTIZE_AMT = #{amortizeAmt}
    </if>
    <if test="amortizeDate != null">
      AND AMORTIZE_DATE = #{amortizeDate}
    </if>
    <if test="effectDate != null">
      AND EFFECT_DATE = #{effectDate}
    </if>
    <if test="reference != null">
      AND REFERENCE = #{reference}
    </if>
    <if test="channelSeqNo != null">
      AND CHANNEL_SEQ_NO = #{channelSeqNo}
    </if>
    <if test="endDate != null">
      AND END_DATE = #{endDate}
    </if>
    <if test="tranTimestamp != null">
      AND TRAN_TIMESTAMP = #{tranTimestamp}
    </if>
    <if test="glPostedFlag != null">
      AND GL_POSTED_FLAG = #{glPostedFlag}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectMbFeeAmortizeDetailSplitOB" parameterType="java.util.Map" resultType="java.util.Map">
    <if test="_databaseId == 'mysql'">
      SELECT
      MIN(AMORTIZE_SEQ_NO) START_KEY,
      MAX(AMORTIZE_SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
      FROM
      (
      SELECT
      AMORTIZE_SEQ_NO,
      @rownum :=@rownum + 1 AS rownum
      FROM (SELECT
      DISTINCT AMORTIZE_SEQ_NO FROM RB_FEE_AMORTIZE_DETAIL,
      (SELECT @rownum := -1) t
      where AMORTIZE_DATE =  #{runDate}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      AND AMORTIZE_AMT!=0
      AND (GL_POSTED_FLAG = 'N' or GL_POSTED_FLAG is null))t1
      ORDER BY AMORTIZE_SEQ_NO
      ) tt
      GROUP BY
      FLOOR(
      tt.rownum /#{maxPerCount} )
    </if>
    <if test="_databaseId == 'oracle'">
      SELECT MIN (AMORTIZE_SEQ_NO) START_KEY, MAX (AMORTIZE_SEQ_NO) END_KEY,COUNT(*) ROW_COUNT
      FROM (SELECT DISTINCT AMORTIZE_SEQ_NO
      from RB_FEE_AMORTIZE_DETAIL
      where AMORTIZE_DATE =  #{runDate}
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
      AND AMORTIZE_AMT!=0
      AND (GL_POSTED_FLAG = 'N' or GL_POSTED_FLAG is null)
      ORDER BY AMORTIZE_SEQ_NO)
      GROUP BY TRUNC ((ROWNUM-1) / #{maxPerCount}) order by START_KEY
    </if>
  </select>
  <update id="updateBatch" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeDetail" >
       update RB_FEE_AMORTIZE_DETAIL set GL_POSTED_FLAG='Y'
       where AMORTIZE_SEQ_NO = #{amortizeSeqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeDetail">
    insert into RB_FEE_AMORTIZE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="amortizeSeqNo != null">
        AMORTIZE_SEQ_NO,
      </if>
      <if test="agreementId != null">
        AGREEMENT_ID,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="tranType != null">
        TRAN_TYPE,
      </if>
      <if test="feeType != null">
        FEE_TYPE,
      </if>
      <if test="amortizePeriodFreq != null">
        AMORTIZE_PERIOD_FREQ,
      </if>
      <if test="amortizePeriodType != null">
        AMORTIZE_PERIOD_TYPE,
      </if>
      <if test="amortizeMonth != null">
        AMORTIZE_MONTH,
      </if>
      <if test="amortizeDay != null">
        AMORTIZE_DAY,
      </if>
      <if test="amortizeCcy != null">
        AMORTIZE_CCY,
      </if>
      <if test="amortizeAmt != null">
        AMORTIZE_AMT,
      </if>
      <if test="amortizeDate != null">
        AMORTIZE_DATE,
      </if>
      <if test="effectDate != null">
        EFFECT_DATE,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="channelSeqNo != null">
        CHANNEL_SEQ_NO,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="glPostedFlag != null">
        GL_POSTED_FLAG,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="amortizeSeqNo != null">
        #{amortizeSeqNo},
      </if>
      <if test="agreementId != null">
        #{agreementId},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="tranType != null">
        #{tranType},
      </if>
      <if test="feeType != null">
        #{feeType},
      </if>
      <if test="amortizePeriodFreq != null">
        #{amortizePeriodFreq},
      </if>
      <if test="amortizePeriodType != null">
        #{amortizePeriodType},
      </if>
      <if test="amortizeMonth != null">
        #{amortizeMonth},
      </if>
      <if test="amortizeDay != null">
        #{amortizeDay},
      </if>
      <if test="amortizeCcy != null">
        #{amortizeCcy},
      </if>
      <if test="amortizeAmt != null">
        #{amortizeAmt},
      </if>
      <if test="amortizeDate != null">
        #{amortizeDate},
      </if>
      <if test="effectDate != null">
        #{effectDate},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="channelSeqNo != null">
        #{channelSeqNo},
      </if>
      <if test="endDate != null">
        #{endDate},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="glPostedFlag != null">
        #{glPostedFlag},
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
  </insert>
</mapper>
