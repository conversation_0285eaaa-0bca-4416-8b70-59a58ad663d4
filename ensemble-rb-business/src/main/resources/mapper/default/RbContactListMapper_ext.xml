<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbContactList">

    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbContactList" >
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        insert into RB_CONTACT_LIST
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="internalKey != null" >
                INTERNAL_KEY,
            </if>
            <if test="linkmanType != null" >
                LINKMAN_TYPE,
            </if>
            <if test="linkmanName != null" >
                LINKMAN_NAME,
            </if>
            <if test="documentType != null" >
                DOCUMENT_TYPE,
            </if>
            <if test="documentId != null" >
                DOCUMENT_ID,
            </if>
            <if test="phoneNo1 != null" >
                PHONE_NO1,
            </if>
            <if test="phoneNo2 != null" >
                PHONE_NO2,
            </if>
            <if test="lastChangeDate != null" >
                LAST_CHANGE_DATE,
            </if>
            <if test="lastChangeUserId != null" >
                LAST_CHANGE_USER_ID,
            </if>
            <if test="contactStatus != null" >
                CONTACT_STATUS,
            </if>
            <if test="company != null" >
                COMPANY,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="internalKey != null" >
                #{internalKey},
            </if>
            <if test="linkmanType != null" >
                #{linkmanType},
            </if>
            <if test="linkmanName != null" >
                #{linkmanName},
            </if>
            <if test="documentType != null" >
                #{documentType},
            </if>
            <if test="documentId != null" >
                #{documentId},
            </if>
            <if test="phoneNo1 != null" >
                #{phoneNo1},
            </if>
            <if test="phoneNo2 != null" >
                #{phoneNo2},
            </if>
            <if test="lastChangeDate != null" >
                #{lastChangeDate},
            </if>
            <if test="lastChangeUserId != null" >
                #{lastChangeUserId},
            </if>
            <if test="contactStatus != null" >
                #{contactStatus},
            </if>
            <if test="company != null" >
                #{company},
            </if>


        </trim>
    </insert>

    <select id="getContactByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbContactList">
        select <include refid="Base_Column"/>
        FROM RB_CONTACT_LIST
        WHERE internal_key =
        #{internalKey}
        <if test="clientNo != null and  clientNo != '' ">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <delete id="deleteByInternalKey"  parameterType="java.util.Map" >
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Jun 02 11:23:06 CST 2015.
        -->
        delete
        FROM RB_CONTACT_LIST
        WHERE internal_key =
        #{internalKey}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <delete id="deleteByInternalKeyAndClientNo"  parameterType="java.util.Map" >
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Jun 02 11:23:06 CST 2015.
        -->
        delete
        FROM RB_CONTACT_LIST
        WHERE internal_key =
        #{internalKey}
        AND client_no =  #{clientNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
</mapper>
