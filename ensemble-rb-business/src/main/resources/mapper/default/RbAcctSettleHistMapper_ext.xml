<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettleHist">

    <select id="selectByPrimaryKeyAndRel" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettleHist"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettleHist">
        select
        <include refid="Base_Column"/>
        from RB_ACCT_SETTLE_HIST
        where SETTLE_ACCT_CLASS='REL'
        <if test="internalKey != null and internalKey != ''">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        ORDER BY SEQ_NO desc
    </select>

</mapper>
