<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
  <!-- Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/01/02 10:20:59. -->

  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
    update RB_SETTLE_CARD_REAL
    <set>
      <if test="cardProdType != null">
        CARD_PROD_TYPE = #{cardProdType},
      </if>
      <if test="defaultFlag != null">
        DEFAULT_FLAG = #{defaultFlag},
      </if>
      <if test="cardTranFlag != null">
        CARD_TRAN_FLAG = #{cardTranFlag},
      </if>
      <if test="isCashTransFlag != null">
        IS_CASH_TRANS_FLAG = #{isCashTransFlag},
      </if>
      <if test="isDebtTransFlag != null">
        IS_DEBT_TRANS_FLAG = #{isDebtTransFlag},
      </if>
      <if test="isCretTransFlag != null">
        IS_CRET_TRANS_FLAG = #{isCretTransFlag},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp}
      </if>
      <if test="mainCardFlag != null">
        MAIN_CARD_FLAG = #{mainCardFlag},
      </if>
      <if test="mainCardNo != null">
        MAIN_CARD_NO = #{mainCardNo},
      </if>
      <if test="autoCollectFlag != null">
        AUTO_COLLECT_FLAG = #{autoCollectFlag},
      </if>
      <if test="collectOrder != null">
        COLLECT_ORDER = #{collectOrder},
      </if>
      <if test="collectNo != null">
        COLLECT_NO = #{collectNo},
      </if>
      <if test="allDraInd != null">
        ALL_DRA_IND = #{allDraInd},
      </if>
      <if test="cardTranformFlag != null">
        CARD_TRANFORM_FLAG = #{cardTranformFlag},
      </if>
    </set>
    where ACCT_SEQ_NO = #{acctSeqNo}
        AND BASE_ACCT_NO = #{baseAcctNo}
        AND CARD_NO = #{cardNo}
        AND ACCT_CCY = #{acctCcy}
        AND PROD_TYPE = #{prodType}
          <if test="clientNo != null and clientNo != ''">
          AND CLIENT_NO= #{clientNo}
        </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="mbSettleCardRenewByCardNo" parameterType="java.util.Map">
    update RB_SETTLE_CARD_REAL
    <set>
      <if test="newCardNo != null">
        CARD_NO=#{newCardNo},
      </if>
    </set>
    where  CARD_NO = #{cardNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updateMainCardNo" parameterType="java.util.Map">
    update RB_SETTLE_CARD_REAL
    <set>
      <if test="newCardNo != null">
        MAIN_CARD_NO=#{newCardNo},
      </if>
    </set>
    where  MAIN_CARD_NO = #{cardNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getDefaultMbSettleCardRealByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal" flushCache="true" useCache="false">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_REAL
    where CARD_NO = #{cardNo}
    AND DEFAULT_FLAG = 'Y'
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getSubCardByMainCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
    SELECT <include refid="Base_Column"/>
    from RB_SETTLE_CARD_REAL
    WHERE MAIN_CARD_NO = #{mainCardNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <if test="mainCardFlag != null and mainCardFlag != ''">
      AND MAIN_CARD_FLAG = #{mainCardFlag}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getByClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
    select DISTINCT card_no
    from RB_SETTLE_CARD_REAL
    where CLIENT_NO = #{clientNo}
    <if test="mainCardFlag != null and mainCardFlag != ''">
      AND MAIN_CARD_FLAG= #{mainCardFlag}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="getMbSettleCardRealsByMap" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_REAL
    <where>
    <if test="cardNo != null and cardNo != ''">
      AND CARD_NO= #{cardNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <if test="baseAcctNo != null and baseAcctNo != ''">
      AND BASE_ACCT_NO= #{baseAcctNo}
    </if>
    <if test="defaultFlag != null and defaultFlag != ''">
      AND DEFAULT_FLAG= #{defaultFlag}
    </if>
    <if test="mainCardFlag != null and mainCardFlag != ''">
      AND MAIN_CARD_FLAG = #{mainCardFlag}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    <if test="branchList != null and branchList.size() > 0">
      and tran_branch in
      <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
        #{branch}
      </foreach>
    </if>
    </where>
  </select>
  <select id="getMbSettleCardRealByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal" flushCache="true" useCache="false">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_REAL
    where CARD_NO = #{cardNo}
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by DEFAULT_FLAG desc
  </select>
  <select id="getMbSettleCardRealByMbSettleCardReal" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal" flushCache="true" useCache="false">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_REAL
    <where>
    <if test="cardNo != null">
      AND CARD_NO = #{cardNo}
    </if>
    <if test="baseAcctNo != null">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <if test="acctSeqNo != null">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="cardProdType != null">
      AND CARD_PROD_TYPE = #{cardProdType}
    </if>
    <if test="defaultFlag != null">
      AND DEFAULT_FLAG = #{defaultFlag}
    </if>
    <if test="cardTranFlag != null">
      AND CARD_TRAN_FLAG = #{cardTranFlag}
    </if>
    <if test="isCashTransFlag != null">
      AND IS_CASH_TRANS_FLAG = #{isCashTransFlag}
    </if>
    <if test="isDebtTransFlag != null">
      AND IS_DEBT_TRANS_FLAG = #{isDebtTransFlag}
    </if>
    <if test="isCretTransFlag != null">
      AND IS_CRET_TRANS_FLAG = #{isCretTransFlag}
    </if>
    <if test="userId != null">
      AND USER_ID = #{userId}
    </if>
    <if test="tranDate != null">
      AND TRAN_DATE = #{tranDate}
    </if>
    <if test="tranBranch != null">
      AND TRAN_BRANCH = #{tranBranch}
    </if>
    <if test="company != null">
      AND COMPANY = #{company}
    </if>
    <if test="tranTimestamp != null">
      AND TRAN_TIMESTAMP = #{tranTimestamp}
    </if>
    <if test="mainCardFlag != null">
      AND MAIN_CARD_FLAG = #{mainCardFlag}
    </if>
    <if test="mainCardNo != null">
      AND MAIN_CARD_NO = #{mainCardNo}
    </if>
    <if test="autoCollectFlag != null">
      AND AUTO_COLLECT_FLAG = #{autoCollectFlag}
    </if>
    <if test="collectOrder != null">
      AND COLLECT_ORDER = #{collectOrder}
    </if>
    <if test="collectNo != null">
      AND COLLECT_NO = #{collectNo}
    </if>
    <if test="allDraInd != null">
      AND ALL_DRA_IND = #{allDraInd}
    </if>
    <if test="cardTranformFlag != null">
      AND CARD_TRANFORM_FLAG = #{cardTranformFlag}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <!-- Use of company settlement card cancellation -->
  <delete id="deleteByCardNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
    delete from RB_SETTLE_CARD_REAL
    where CARD_NO = #{cardNo}
    <if test="clientNo != null">
      AND CLIENT_NO = #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <select id="selectByCardNoOrderByCorrectNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_REAL
    where CARD_NO = #{cardNo}
    AND AUTO_COLLECT_FLAG = 'Y'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by COLLECT_NO asc
  </select>
  <select id="selectByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal">
    select <include refid="Base_Column"/>
    from RB_SETTLE_CARD_REAL
    where BASE_ACCT_NO = #{baseAcctNo}
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <if test="acctSeqNo != null">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <!-- Primary and secondary card replacement sql -->
  <update id="updateByMainCardNoAndMainCardFlag" parameterType="java.util.Map">
    update RB_SETTLE_CARD_REAL
    <set>
      MAIN_CARD_NO=#{mainCardNo},
      MAIN_CARD_FLAG=#{mainCardFlag}
    </set>
    where  CARD_NO = #{cardNo}
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <update id="updateIncExpFlagByAcctNo" parameterType="java.util.Map">
    update RB_SETTLE_CARD_REAL
    <set>
      INC_EXP_FLAG = #{incExpFlag}
    </set>
    where  BASE_ACCT_NO = #{baseAcctNo}
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <if test="acctSeqNo != null">
      AND ACCT_SEQ_NO = #{acctSeqNo}
    </if>
    <if test="clientNo != null and clientNo != ''">
      AND CLIENT_NO= #{clientNo}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <!-- Company settlement card related account maintenance transaction update form RB_SETTLE_CARD_REAL -->
  <update id="updateRbSettleCardRealByPrimaryKey" parameterType="java.util.Map">
    update RB_SETTLE_CARD_REAL
    <set>
      COLLECT_NO = #{collectNo,jdbcType=VARCHAR},
      <if test="cardNo != null and cardNo != '' ">
        CARD_NO = #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="cardProdType != null and cardProdType != '' ">
        CARD_PROD_TYPE = #{cardProdType,jdbcType=VARCHAR},
      </if>
      <if test="acctSeqNo != null and acctSeqNo != '' ">
        ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="baseAcctNo != null and baseAcctNo != '' ">
        BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="prodType != null and prodType != '' ">
        PROD_TYPE = #{prodType,jdbcType=VARCHAR},
      </if>
      <if test="acctCcy != null and acctCcy != '' ">
        ACCT_CCY = #{acctCcy,jdbcType=VARCHAR},
      </if>
      <if test="defaultFlag != null and defaultFlag != '' ">
        DEFAULT_FLAG = #{defaultFlag,jdbcType=VARCHAR},
      </if>
      <if test="mainCardFlag != null and mainCardFlag != '' ">
        MAIN_CARD_FLAG = #{mainCardFlag,jdbcType=VARCHAR},
      </if>
      <if test="mainCardNo != null and mainCardNo != '' ">
        MAIN_CARD_NO = #{mainCardNo,jdbcType=VARCHAR},
      </if>
      <if test="allDraInd != null and allDraInd != '' ">
        ALL_DRA_IND = #{allDraInd,jdbcType=VARCHAR},
      </if>

      <if test="autoCollectFlag != null and autoCollectFlag != '' ">
        AUTO_COLLECT_FLAG = #{autoCollectFlag,jdbcType=VARCHAR},
      </if>
      <if test="cardTranFlag != null and cardTranFlag != '' ">
        CARD_TRAN_FLAG = #{cardTranFlag,jdbcType=VARCHAR},
      </if>
      <if test="cardTranformFlag != null and cardTranformFlag != '' ">
        CARD_TRANFORM_FLAG = #{cardTranformFlag,jdbcType=VARCHAR},
      </if>
      <if test="cretTransFlag != null and cretTransFlag != '' ">
        CRET_TRANS_FLAG = #{cretTransFlag,jdbcType=VARCHAR},
      </if>
      <if test="debtTransFlag != null and debtTransFlag != '' ">
        DEBT_TRANS_FLAG = #{debtTransFlag,jdbcType=VARCHAR},
      </if>
      <if test="isCashTrans != null and isCashTrans != '' ">
        IS_CASH_TRANS = #{isCashTrans,jdbcType=VARCHAR},
      </if>
      <if test="incExpFlag != null and incExpFlag != '' ">
        INC_EXP_FLAG = #{incExpFlag,jdbcType=VARCHAR},
      </if>
      <if test="tranDate != null ">
        TRAN_DATE = #{tranDate,jdbcType=DATE},
      </if>
      <if test="tranBranch != null and tranBranch != '' ">
        TRAN_BRANCH = #{tranBranch,jdbcType=VARCHAR},
      </if>
      <if test="tranTimestamp != null and tranTimestamp != '' ">
        TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="userId != null and userId != '' ">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="company != null and company != '' ">
        COMPANY = #{company,jdbcType=VARCHAR},
      </if>
      COLLECT_ORDER = #{collectOrder,jdbcType=VARCHAR}
    </set>
    <where>
      <if test="cardNo != null and  cardNo != '' ">
        AND CARD_NO = #{cardNo,jdbcType=VARCHAR}
      </if>
      <if test="acctSeqNo != null and  acctSeqNo != '' ">
        AND ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="baseAcctNo != null and  baseAcctNo != '' ">
        AND BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR}
      </if>
      <if test="prodType != null and  prodType != '' ">
        AND PROD_TYPE = #{prodType,jdbcType=VARCHAR}
      </if>
      <if test="clientNo != null and  clientNo != '' ">
        AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
      </if>
    </where>
  </update>
</mapper>
