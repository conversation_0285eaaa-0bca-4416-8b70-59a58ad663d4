<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbCfckInfo">

	<select id="getRbCfckInfos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCfckInfo">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_CFCK_INFO
		WHERE REMAIN_AMT_STATUS='A'
		<if test="impAcctNo!=null">
		and	IMP_ACCT_NO = #{impAcctNo}
		</if>
		<if test="clientNo!=null">
		AND	CLIENT_NO = #{clientNo}
		</if>

<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getRbCfckInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbCfckInfo">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_CFCK_INFO
		WHERE REMAIN_AMT_STATUS='A'
		<if test="impAcctNo!=null">
			and	IMP_SEQ_NO = #{impSeqNo}
		</if>
		<if test="clientNo!=null">
			AND	CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>

	</select>
</mapper>
