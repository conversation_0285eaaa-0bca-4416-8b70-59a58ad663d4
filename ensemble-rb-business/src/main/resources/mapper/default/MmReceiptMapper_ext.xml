<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReceipt">

<sql id="Base_Column_List">
        RECEIPT_NO,
        INTERNAL_KEY,
        RECEIPT_DATE,
        RECEIPT_TYPE,
        CLIENT_NO,
        CCY,
        LOCAL_XRATE,
        LOCAL_XRATE_ID,
        RECEIPT_AMT,
        TRAN_DATE,
        REFERENCE,
        TRAN_BRANCH,
        RECEIPT_PRI_AMT,
        RECEIPT_INT_AMT,
        NARRATIVE,
        RECEIPT_RATE,
        APPROVAL_STATUS,
        APPR_USER_ID,
        APPROVAL_DATE,
        USER_ID,
        LAST_CHANGE_DATE,
        COMPANY,
        REVERS<PERSON>,
        REVERSAL_REASON,
        TRAN_TIMESTAMP
    </sql>
    <select id="selectByPrimaryKeyExt" parameterType="java.lang.String"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReceipt">
        select <include refid="Base_Column_List"/>
        from MM_RECEIPT
        where RECEIPT_NO = #{receiptNo}
    </select>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReceipt">
        insert into MM_RECEIPT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="receiptNo != null">
                RECEIPT_NO,
            </if>
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="receiptDate != null">
                RECEIPT_DATE,
            </if>
            <if test="receiptType != null">
                RECEIPT_TYPE,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="localXrate != null">
                LOCAL_XRATE,
            </if>
            <if test="localXrateId != null">
                LOCAL_XRATE_ID,
            </if>
            <if test="receiptAmt != null">
                RECEIPT_AMT,
            </if>
            <if test="receiptPriAmt != null">
                RECEIPT_PRI_AMT,
            </if>
            <if test="receiptIntAmt != null">
                RECEIPT_INT_AMT,
            </if>
            <if test="receiptRate != null">
                RECEIPT_RATE,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="reference != null">
                REFERENCE,
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH,
            </if>
            <if test="narrative != null">
                NARRATIVE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="approvalStatus != null">
                APPROVAL_STATUS,
            </if>
            <if test="apprUserId != null">
                APPR_USER_ID,
            </if>
            <if test="approvalDate != null">
                APPROVAL_DATE,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
            <if test="reversal != null">
                REVERSAL,
            </if>
            <if test="reversalReason != null">
                REVERSAL_REASON,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="receiptNo != null">
                #{receiptNo},
            </if>
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="receiptDate != null">
                #{receiptDate},
            </if>
            <if test="receiptType != null">
                #{receiptType},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="ccy != null">
                #{ccy},
            </if>
            <if test="localXrate != null">
                #{localXrate},
            </if>
            <if test="localXrateId != null">
                #{localXrateId},
            </if>
            <if test="receiptAmt != null">
                #{receiptAmt},
            </if>
            <if test="receiptPriAmt != null">
                #{receiptPriAmt},
            </if>
            <if test="receiptIntAmt != null">
                #{receiptIntAmt},
            </if>
            <if test="receiptRate != null">
                #{receiptRate},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="reference != null">
                #{reference},
            </if>
            <if test="tranBranch != null">
                #{tranBranch},
            </if>
            <if test="narrative != null">
                #{narrative},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="approvalStatus != null">
                #{approvalStatus},
            </if>
            <if test="apprUserId != null">
                #{apprUserId},
            </if>
            <if test="approvalDate != null">
                #{approvalDate},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="reversal != null">
                #{reversal},
            </if>
            <if test="reversalReason != null">
                #{reversalReason},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReceipt">
        update MM_RECEIPT
        <set>
            <if test="internalKey != null">
                INTERNAL_KEY = #{internalKey},
            </if>
            <if test="receiptDate != null">
                RECEIPT_DATE = #{receiptDate},
            </if>
            <if test="receiptType != null">
                RECEIPT_TYPE = #{receiptType},
            </if>
            <if test="clientNo != null">
                CLIENT_NO = #{clientNo},
            </if>
            <if test="ccy != null">
                CCY = #{ccy},
            </if>
            <if test="localXrate != null">
                LOCAL_XRATE = #{localXrate},
            </if>
            <if test="localXrateId != null">
                LOCAL_XRATE_ID = #{localXrateId},
            </if>
            <if test="receiptAmt != null">
                RECEIPT_AMT = #{receiptAmt},
            </if>
            <if test="receiptPriAmt != null">
                RECEIPT_PRI_AMT = #{receiptPriAmt},
            </if>
            <if test="receiptIntAmt != null">
                RECEIPT_INT_AMT = #{receiptIntAmt},
            </if>
            <if test="receiptRate != null">
                RECEIPT_RATE = #{receiptRate},
            </if>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="reference != null">
                REFERENCE = #{reference},
            </if>
            <if test="tranBranch != null">
                TRAN_BRANCH = #{tranBranch},
            </if>
            <if test="narrative != null">
                NARRATIVE = #{narrative},
            </if>
            <if test="userId != null">
                USER_ID = #{userId},
            </if>
            <if test="approvalStatus != null">
                APPROVAL_STATUS = #{approvalStatus},
            </if>
            <if test="apprUserId != null">
                APPR_USER_ID = #{apprUserId},
            </if>
            <if test="approvalDate != null">
                APPROVAL_DATE = #{approvalDate},
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="company != null">
                COMPANY = #{company},
            </if>
            <if test="reversal != null">
                REVERSAL = #{reversal},
            </if>
            <if test="reversalReason != null">
                REVERSAL_REASON = #{reversalReason},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where RECEIPT_NO = #{receiptNo}
    </update>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReceipt">
        delete from MM_RECEIPT
        where RECEIPT_NO = #{receiptNo}
    </delete>
    <select id="getMmReceipt" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReceipt">
        select <include refid="Base_Column_List"/>
        from  MM_RECEIPT
        <where>
        <if test="receiptNo != null and receiptNo.length()>0">
            and RECEIPT_NO=#{receiptNo}
        </if>
        <if test="receiptDate != null and receiptDate.length()>0">
            and RECEIPT_DATE=#{receiptDate}
        </if>
        <if test="tranBranch != null and tranBranch.length()>0">
            and TRAN_BRANCH=#{tranBranch}
        </if>
        </where>
    </select>
    <select id="getMmReceiptList" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.MmReceipt">
        select <include refid="Base_Column_List"/>
        from  MM_RECEIPT
        <where>
        <if test="internalKey != null">
            and INTERNAL_KEY=#{internalKey}
        </if>
        <if test="receiptDate != null and receiptDate.length()>0">
            and RECEIPT_DATE=#{receiptDate}
        </if>
        <if test="tranBranch != null and tranBranch.length()>0">
            and TRAN_BRANCH=#{tranBranch}
        </if>
        </where>
    </select>
</mapper>
