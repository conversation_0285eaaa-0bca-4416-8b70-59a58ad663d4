<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchAgreementRelationResult">

	<update id="updateRbBatchAgreementRelationResult" parameterType="java.util.Map" >
		update RB_BATCH_AGREEMENT_RELATION_RESULT set BATCH_STATUS = #{batchStatus} WHERE BATCH_NO = #{batchNo} and SEQ_NO=#{seqNo}
	</update>


</mapper>
