<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">

      <select id="selectNextChargeDateMature" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">
            select <include refid="Base_Column"/>

             from RB_BATCH_CHARGE
             where <![CDATA[NEXT_CHARGE_DATE <= #{runDate}]]>
              <!-- 多法人改造 by luocwa -->
              <if test="company != null and company != '' ">
                AND COMPANY = #{company}
              </if>
             order by BATCH_SEQ_NO

      </select>
  <select id="selectFeeAmtLessThanZero" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">
	    select <include refid="Base_Column"/>

	     from RB_BATCH_CHARGE
	     where <![CDATA[FEE_AMT = 0 and NEXT_CHARGE_DATE <= #{baseParam.runDate} ]]>
<!-- 多法人改造 by luocwa -->
              <if test="company != null and company != '' ">
                AND COMPANY = #{company}
              </if>
	     order by BATCH_SEQ_NO

  </select>
  <select id="selectFeeAmtLargeThanZero" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">
	    select <include refid="Base_Column"/>

	     from RB_BATCH_CHARGE
	     where <![CDATA[FEE_AMT > 0 and NEXT_CHARGE_DATE <= #{baseParam.runDate}]]>
          <!-- 多法人改造 by luocwa -->
          <if test="company != null and company != '' ">
            AND COMPANY = #{company}
          </if>
	     order by BATCH_SEQ_NO

  </select>
  <select id="getBaseAcctInfoByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">
    select <include refid="Base_Column"/>
	     from RB_BATCH_CHARGE
	     where INTERNAL_KEY = #{internalKey}
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
  </select>


  <select id="getMbBatchChargeListByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">
    select <include refid="Base_Column"/>
	     from RB_BATCH_CHARGE
	     where BASE_ACCT_NO = #{baseAcctNo}
        <if test="company != null and company != '' ">
          AND COMPANY = #{company}
        </if>
  </select>


    <select id="getMbBatchChargeListByCardNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">
        select <include refid="Base_Column"/>
        from RB_BATCH_CHARGE
        where CARD_NO = #{cardNo}
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <update id="updateProdByKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge">

        update RB_BATCH_CHARGE
        <set>
            prod_type = #{newProdType},
        </set>
        <where>
            INTERNAL_KEY = #{internalKey}
            AND CLIENT_NO = #{clientNo}
        </where>
    </update>
</mapper>
