<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseRegHist">
  <select id="getNotDealCount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    SELECT COUNT(1) FROM RB_OPEN_CLOSE_REG_HIST
    WHERE DEAL_FLAG = #{dealFlag}
  </select>
</mapper>
