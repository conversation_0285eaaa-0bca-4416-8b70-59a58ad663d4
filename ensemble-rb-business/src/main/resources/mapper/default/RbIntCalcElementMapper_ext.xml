<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement">

    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement">
        insert into RB_INT_CALC_ELEMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="intCalcClass != null">
                INT_CALC_CLASS,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="intCalcAmtType != null">
                INT_CALC_AMT_TYPE,
            </if>
            <if test="intCalcAmt != null">
                INT_CALC_AMT,
            </if>

            <if test="intNearAmtType != null">
                INT_NEAR_AMT_TYPE,
            </if>
            <if test="nearAmt != null">
                NEAR_AMT,
            </if>

            <if test="intCalcDaysType != null">
                INT_CALC_DAYS_TYPE,
            </if>
            <if test="intCalcDays != null">
                INT_CALC_DAYS,
            </if>

            <if test="intNearDaysType != null">
                INT_NEAR_DAYS_TYPE,
            </if>
            <if test="intNearDays != null">
                INT_NEAR_DAYS,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="intCalcClass != null">
                #{intCalcClass},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate},
            </if>
            <if test="intCalcAmtType != null">
                #{intCalcAmtType},
            </if>
            <if test="intCalcAmt != null">
                #{intCalcAmt},
            </if>
            <if test="intNearAmtType != null">
                #{intNearAmtType},
            </if>
            <if test="nearAmt != null">
                #{nearAmt},
            </if>

            <if test="intCalcDaysType != null">
                #{intCalcDaysType},
            </if>
            <if test="intCalcDays != null">
                #{intCalcDays},
            </if>

            <if test="intNearDaysType != null">
                #{intNearDaysType},
            </if>
            <if test="intNearDays != null">
                #{intNearDays},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>
    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement">
        SELECT <include refid="Base_Column"/>
        FROM RB_INT_CALC_ELEMENT
        WHERE INTERNAL_KEY = #{internalKey}
        AND INT_CALC_CLASS = #{intCalcClass}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement">
        delete from RB_INT_CALC_ELEMENT
        WHERE INTERNAL_KEY = #{internalKey}
        AND INT_CALC_CLASS = #{intCalcClass}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement">
        update RB_INT_CALC_ELEMENT
        <set>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE = #{lastChangeDate},
            </if>
            <if test="intCalcAmtType != null">
                INT_CALC_AMT_TYPE =  #{intCalcAmtType},
            </if>
            <if test="intCalcAmt != null">
                INT_CALC_AMT = #{intCalcAmt},
            </if>
            <if test="intNearAmtType != null">
                INT_NEAR_AMT_TYPE = #{intNearAmtType},
            </if>
            <if test="nearAmt != null">
                NEAR_AMT = #{nearAmt},
            </if>

            <if test="intCalcDaysType != null">
                INT_CALC_DAYS_TYPE =#{intCalcDaysType},
            </if>
            <if test="intCalcDays != null">
                INT_CALC_DAYS =#{intCalcDays},
            </if>

            <if test="intNearDaysType != null">
                INT_NEAR_DAYS_TYPE = #{intNearDaysType},
            </if>
            <if test="intNearDays != null">
                INT_NEAR_DAYS = #{intNearDays},
            </if>
            <if test="remark != null">
                REMARK = #{remark},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp},
            </if>
            <if test="company != null">
                COMPANY = #{company}
            </if>
        </set>
        WHERE INTERNAL_KEY = #{internalKey}
        AND INT_CALC_CLASS = #{intCalcClass}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <insert id="backInsert" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by Galaxy Tools Generator, do not modify.
          This element was generated on Tue Aug 11 13:56:23 CST 2015.
        -->
        insert into RB_INT_CALC_ELEMENT_HIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="intCalcClass != null">
                INT_CALC_CLASS,
            </if>
            <if test="lastChangeDate != null">
                LAST_CHANGE_DATE,
            </if>
            <if test="intCalcAmtType != null">
                INT_CALC_AMT_TYPE,
            </if>
            <if test="intCalcAmt != null">
                INT_CALC_AMT,
            </if>

            <if test="intNearAmtType != null">
                INT_NEAR_AMT_TYPE,
            </if>
            <if test="nearAmt != null">
                NEAR_AMT,
            </if>
            <if test="clientNo != null">
                CLIENT_NO,
            </if>
            <if test="intCalcDaysType != null">
                INT_CALC_DAYS_TYPE,
            </if>
            <if test="intCalcDays != null">
                INT_CALC_DAYS,
            </if>

            <if test="intNearDaysType != null">
                INT_NEAR_DAYS_TYPE,
            </if>
            <if test="intNearDays != null">
                INT_NEAR_DAYS,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="intCalcClass != null">
                #{intCalcClass},
            </if>
            <if test="lastChangeDate != null">
                #{lastChangeDate},
            </if>
            <if test="intCalcAmtType != null">
                #{intCalcAmtType},
            </if>
            <if test="intCalcAmt != null">
                #{intCalcAmt},
            </if>
            <if test="intNearAmtType != null">
                #{intNearAmtType},
            </if>
            <if test="nearAmt != null">
                #{nearAmt},
            </if>
            <if test="clientNo != null">
                #{clientNo},
            </if>
            <if test="intCalcDaysType != null">
                #{intCalcDaysType},
            </if>
            <if test="intCalcDays != null">
                #{intCalcDays},
            </if>

            <if test="intNearDaysType != null">
                #{intNearDaysType},
            </if>
            <if test="intNearDays != null">
                #{intNearDays},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="company != null">
                #{company},
            </if>
        </trim>
    </insert>

    <select id="selectElementHistByCalcClass" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement">
        SELECT <include refid="Base_Column"/>
        FROM RB_INT_CALC_ELEMENT_HIST
        WHERE INTERNAL_KEY = #{internalKey}
        AND INT_CALC_CLASS = #{intCalcClass}
        AND LAST_CHANGE_DATE = #{lastChangeDate}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
