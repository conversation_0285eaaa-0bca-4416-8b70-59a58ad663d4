<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialTranHist">

	<select id="getFinTranHistByInternalKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialTranHist">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_TRAN_HIST
		WHERE INTERNAL_KEY = #{internalKey}
		AND TRAN_DATE between #{startDate} AND #{endDate}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY TRAN_DATE
	</select>
	<select id="getFinTranHistByInternalKeyClientNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialTranHist">
		SELECT
		<include refid="Base_Column" />
		FROM RB_FINANCIAL_TRAN_HIST
		WHERE INTERNAL_KEY = #{internalKey}
		AND TRAN_DATE between #{startDate,jdbcType=DATE}} AND #{endDate,jdbcType=DATE}}
		AND CLIENT_NO = #{clientNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
