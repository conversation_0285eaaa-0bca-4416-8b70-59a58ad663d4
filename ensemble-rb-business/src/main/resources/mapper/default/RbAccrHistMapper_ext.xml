<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
	<select id="getSectionByDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
		select
		<include refid="Base_Column"/>
		from
		<include refid="Table_Name"/>
		where INTERNAL_KEY=#{internalKey}
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		<if test="lastCycleDate != null ">
			AND <![CDATA[ ACCR_DATE>=#{lastCycleDate,jdbcType=DATE}]]>
		</if>
		<![CDATA[
		  AND ACCR_DATE<#{captDate,jdbcType=DATE}
		  AND CLIENT_NO =#{clientNo}
		]]>
	</select>
	<select id="getAccrAmt" parameterType="java.util.Map" resultType="java.math.BigDecimal">
		select sum(INT_ACCRUED_CTD)
		from
		<include refid="Table_Name"/>
		where
		<![CDATA[
		   ACCR_DATE<#{captDate,jdbcType=DATE}
		]]>
		<if test="internalKey != null">
			and internal_key = #{internalKey}
		</if>
		<if test="baseAcctNo != null and acctSeqNo != null">
			and base_acct_no = #{baseAcctNo}
			and acct_seq_no = #{acctSeqNo}
		</if>
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		<if test="lastCycleDate != null ">
			AND <![CDATA[ ACCR_DATE>=#{lastCycleDate,jdbcType=DATE}]]>
		</if>
		<if test="clientNo != null">
			AND CLIENT_NO = #{clientNo}
		</if>

		group by internal_key
	</select>

	<select id="getAccrAmtByDate" parameterType="java.util.Map" resultType="java.math.BigDecimal">
		select sum(INT_ACCRUED_CTD)
		from
		<include refid="Table_Name"/>
		<where>
			<if test="internalKey != null">
				and internal_key = #{internalKey}
			</if>
			<if test="baseAcctNo != null and acctSeqNo != null">
				and base_acct_no = #{baseAcctNo}
				and acct_seq_no = #{acctSeqNo}
			</if>
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			AND <![CDATA[ ACCR_DATE BETWEEN #{startDate,jdbcType=DATE} and #{endDate}]]>
			<if test="clientNo != null">
				AND CLIENT_NO = #{clientNo}
			</if>
		</where>
		group by internal_key
	</select>
	<select id="getRbAccrHistByDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
		select
		<include refid="Base_Column" />
		from
		<include refid="Table_Name" />
		where INTERNAL_KEY=#{internalKey}
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND  <![CDATA[ ACCR_DATE>=#{lastCycleDate,jdbcType=DATE} ]]>
		AND CLIENT_NO =#{clientNo}
		ORDER BY ACCR_DATE ASC
	</select>

	<select id="getRbAccrHistMsg" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
		select
		<include refid="Base_Column" />
		from
		<include refid="Table_Name" />
		<where>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		<if test="internalKey != null ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="accrDate != null ">
			AND ACCR_DATE = #{accrDate,jdbcType=DATE}
		</if>
		<if test="clientNo != null ">
			AND CLIENT_NO = #{clientNo}
		</if>
		</where>
		ORDER BY ACCR_DATE ASC
	</select>


	<select id="getRbAccrHistListByAccrDateDesc" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
		select
		<include refid="Base_Column" />
		from
		<include refid="Table_Name" />
		<where>
		<if test="internalKey != null ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null ">
			AND CLIENT_NO = #{clientNo}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
		ORDER BY ACCR_DATE DESC
	</select>
	<select id="getRbAccrHistByEndDate" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
		select
		<include refid="Base_Column" />
		from
		<include refid="Table_Name" />
		<where>
		<if test="internalKey != null ">
			AND INTERNAL_KEY = #{internalKey}
		</if>
		<if test="clientNo != null ">
			AND CLIENT_NO = #{clientNo}
		</if>
		<if test="accrDate!= null ">
			AND ACCR_DATE=#{accrDate,jdbcType=DATE}
		</if>
		<if test="intClass != null ">
			AND INT_CLASS=#{intClass}
		</if>
<!-- 多法人改造 by LIYUANV -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>

	<select id="getAccrHistListByMergeType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
		select <include refid="Base_Column" />
		from
		RB_ACCR_HIST a
		where
		a.GL_MERGE_TYPE_FLAG = 'Y'
		AND
		a.ACCR_DATE BETWEEN #{lastRunDate,jdbcType=DATE} AND #{yesterday,jdbcType=DATE}
		AND
		(a.INT_ACCRUED_CTD <![CDATA[<>]]> 0 OR a.TAX_ACCRUED_CTD <![CDATA[<>]]> 0)
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="getAccrHistByDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
		select
		<include refid="Base_Column" />
		from RB_ACCR_HIST
		<where>
			<if test="internalKey != null and internalKey != ''">
				AND INTERNAL_KEY = #{internalKey}
			</if>
			<if test="intClass != null  and intClass != ''">
				AND INT_CLASS=#{intClass}
			</if>
			<if test="clientNo != null  and clientNo != ''">
				AND CLIENT_NO = #{clientNo}
			</if>
			<if test="startDate != null">
				AND ACCR_DATE <![CDATA[>=]]> #{startDate}
			</if>
			<if test="endDate != null">
				AND ACCR_DATE <![CDATA[<]]> #{endDate}
			</if>
		</where>
	</select>
</mapper>