<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveDetail">
  <!-- Created by ch<PERSON><PERSON>d on 2017/05/20 14:23:09. -->
  <select id="getReserveDetailByExtRefNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveDetail">
    select *
    from Rb_Batch_Reserve_Detail
    where EXT_REF_NO = #{extRefNo}
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    order by seq_no
  </select>


 <select id="getReserveDetailByExtRefNoAndSeqNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveDetail">
    select *
    from Rb_Batch_Reserve_Detail
    <where>
    <if test="extRefNo != null and extRefNo.length() > 0">
     and  EXT_REF_NO = #{extRefNo}
    </if>
   <if test="seqNo != null and seqNo.length() > 0">
     and  SEQ_NO = #{seqNo}
    </if>
   <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    order by seq_no
  </select>

</mapper>
