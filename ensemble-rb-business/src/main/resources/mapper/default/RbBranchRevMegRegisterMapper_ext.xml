<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchRevMegRegister">

	<select id="selectListByEffectDateAndStatus" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBranchRevMegRegister">
		SELECT
		<include refid="Base_Column"/>
		FROM RB_BRANCH_REV_MEG_REGISTER
		WHERE EFFECT_DATE = #{effectDate}
		  AND MERGE_APPLY_STATUS in  ('A','F')
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>

	</select>

</mapper>
