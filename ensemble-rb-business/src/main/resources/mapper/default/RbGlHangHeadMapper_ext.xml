<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangHead">
    <select id="queryByAcctHangSeqNo" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangHead">
        SELECT *
        FROM RB_GL_HANG_HEAD ma
        WHERE 1=1
        <if test="baseAcctNo != null and baseAcctNo != ''">
           and   ma.base_acct_no = #{baseAcctNo}
        </if>
        <if test="hangStatus != null and hangStatus != ''">
           and ma.HANG_STATUS =#{hangStatus}
        </if>
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
    </select>
    <select id="queryByCondition" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangHead">
        SELECT *
        FROM RB_GL_HANG_HEAD ma
        WHERE
        hang_bal !=0
        and HANG_STATUS!='R'
        <if test="baseAcctNo != null and baseAcctNo != ''">
            AND ma.base_acct_no = #{baseAcctNo}
        </if>
        <if test="hangSeqNo != null and hangSeqNo != ''">
            AND ma.hang_seq_no = #{hangSeqNo}
        </if>
        <if test="clientNo != null and clientNo != ''">
            AND ma.client_no = #{clientNo}
        </if>
        <if test="company != null and company != '' ">
            AND ma.COMPANY = #{company}
        </if>
        order by TRAN_TIMESTAMP DESC
    </select>

    <update id="updateHangHead" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangHead">
        update RB_GL_HANG_HEAD
        <set>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="hangTotalAmt != null">
                HANG_TOTAL_AMT = #{hangTotalAmt},
            </if>
            <if test="hangBal != null">
                HANG_BAL = #{hangBal},
            </if>
            <if test="hangStatus != null">
                HANG_STATUS = #{hangStatus},
            </if>
            <if test="hangEndDate != null">
                HANG_END_DATE = #{hangEndDate},
            </if>
        </set>
        where HANG_SEQ_NO = #{hangSeqNo}
        <if test="baseAcctNo != null">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="clientNo != null">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="queryAllAccountByInfo" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHangHead">
        SELECT
        <include refid="Base_Column" />
        FROM RB_GL_HANG_HEAD
        WHERE 1=1
        <if test="hangSeqNoList != null and hangSeqNoList.size() > 0">
            AND HANG_SEQ_NO IN
            <foreach collection="hangSeqNoList" open="(" close=")" separator="," index="index" item="hangSeqNo">
                #{hangSeqNo}
            </foreach>
        </if>
        <if test="hangStatusList != null and hangStatusList.size() > 0">
            AND HANG_STATUS IN
            <foreach collection="hangStatusList" open="(" close=")" separator="," index="index" item="hangStatus">
                #{hangStatus}
            </foreach>
        </if>
        <if test="branchList != null and branchList.size() > 0">
            AND CLIENT_NO IN
            <foreach collection="branchList" open="(" close=")" separator="," index="index" item="branch">
                #{branch}
            </foreach>
        </if>
        <if test="ccy != null and ccy !=''">
            AND CCY = #{ccy}
        </if>
        ORDER BY HANG_SEQ_NO
    </select>
</mapper>
