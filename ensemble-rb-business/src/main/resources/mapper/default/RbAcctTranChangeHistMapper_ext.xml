<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTranChangeHist">

  <sql id="Base_Column_List">
    <trim suffixOverrides=",">
            INTERNAL_KEY,
            TRAN_DATE,
            REMAIN_BALANCE,
            CLIENT_NO,
            COMPANY,
            TRAN_TIMESTAMP,
    </trim>
  </sql>

  <select id="getRbAcctTranChangeHistOrderByTranDate" resultMap="Base_Result_Map">
      SELECT
          <include refid="Base_Column_List"/>
      FROM RB_ACCT_TRAN_CHANGE_HIST
      where
          REMAIN_BALANCE &gt; 0
          AND INTERNAL_KEY = #{internalKey,jdbcType=BIGINT}
      ORDER BY TRAN_DATE DESC
  </select>

</mapper>
