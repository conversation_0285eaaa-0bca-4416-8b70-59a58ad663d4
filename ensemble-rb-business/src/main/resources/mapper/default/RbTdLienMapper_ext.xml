<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdLien">

	<select id="getRbTdLienByAcctInternalKey" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdLien">
		select <include refid="Base_Column"/>
		FROM RB_TD_LIEN
		WHERE  INTERNAL_KEY = #{internalKey,jdbcType=VARCHAR}
		<if test="clientNo != null">
			and CLIENT_NO= #{clientNo,jdbcType=VARCHAR}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>


	<delete id="deleteByInternalKey" parameterType="java.util.Map" >
		delete
		FROM RB_TD_LIEN
		WHERE  INTERNAL_KEY = #{internalKey,jdbcType=VARCHAR}
		<if test="clientNo != null">
			and CLIENT_NO= #{clientNo,jdbcType=VARCHAR}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</delete>



	<select id="getRbTdLienByAcctInternalKeyAndReference" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdLien">
		select <include refid="Base_Column"/>
		FROM RB_TD_LIEN
		WHERE  INTERNAL_KEY = #{internalKey,jdbcType=VARCHAR} and
		reference = #{reference,jdbcType=VARCHAR}
		<if test="clientNo != null">
			and CLIENT_NO= #{clientNo,jdbcType=VARCHAR}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>

	<select id="selectByDate" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdLien">
		select <include refid="Base_Column"/>
		FROM RB_TD_LIEN
		WHERE  INTERNAL_KEY = #{internalKey,jdbcType=VARCHAR}
		<if test="clientNo != null">
			and CLIENT_NO= #{clientNo,jdbcType=VARCHAR}
		</if>
		<if test="startDate != null">
			and START_DATE= #{startDate}
		</if>
		<if test="endDate != null">
			and END_DATE= #{endDate}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
	<select id="selectByReference" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbTdLien">
		select <include refid="Base_Column" />
		from RB_TD_LIEN
		where reference = #{reference,jdbcType=VARCHAR}
		<if test="clientNo != null">
			and CLIENT_NO= #{clientNo,jdbcType=VARCHAR}
		</if>
	</select>

</mapper>
