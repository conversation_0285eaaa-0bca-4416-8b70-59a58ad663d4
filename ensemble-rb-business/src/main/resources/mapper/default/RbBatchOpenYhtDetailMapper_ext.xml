<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenYhtDetail">

  <select id="getByPrimaryKey" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenYhtDetail">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_YHT_DETAIL
    where SEQ_NO = #{seqNo} AND BATCH_NO = #{batchNo}
  </select>

  <select id="getDetails" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenYhtDetail" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenYhtDetail">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_YHT_DETAIL
    where BATCH_NO = #{batchNo} order by SEQ_NO
  </select>

  <select id="queryDetails" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenYhtDetail">
    select <include refid="Base_Column"/>
    from RB_BATCH_OPEN_YHT_DETAIL
    where BATCH_NO = #{batchNo} and BATCH_STATUS=#{batchStatus} order by SEQ_NO
  </select>
  <select id="getSumByBatchStatus" parameterType="java.util.Map" resultType="java.math.BigDecimal">
    select SUM (1)
    from RB_BATCH_OPEN_YHT_DETAIL
    where BATCH_NO = #{batchNo}
    AND BATCH_STATUS = 'S'
  </select>

    <update id="updateExt">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set_Ext"/>
        <include refid="PrimaryKey_Where_Ext"/>
    </update>
    <sql id="PrimaryKey_Where_Ext">
        <where>
            <if test="batchNo != null and  batchNo != '' ">
                AND BATCH_NO = #{batchNo,jdbcType=VARCHAR}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                AND SEQ_NO = #{seqNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <sql id="Base_Set_Ext">
        <set>
            <if test="jobRunId != null ">JOB_RUN_ID = #{jobRunId,jdbcType=VARCHAR},</if>
            <if test="batchNo != null ">BATCH_NO = #{batchNo,jdbcType=VARCHAR},</if>
            <if test="seqNo != null ">SEQ_NO = #{seqNo,jdbcType=VARCHAR},</if>
            <if test="mainRbAcctNo != null ">MAIN_RB_ACCT_NO = #{mainRbAcctNo,jdbcType=VARCHAR},</if>
            <if test="mainProdType != null ">MAIN_PROD_TYPE = #{mainProdType,jdbcType=VARCHAR},</if>
            <if test="mainAcctSeqNo != null ">MAIN_ACCT_SEQ_NO = #{mainAcctSeqNo,jdbcType=VARCHAR},</if>
            <if test="mainCcy != null ">MAIN_CCY = #{mainCcy,jdbcType=VARCHAR},</if>
            <if test="clientNo != null ">CLIENT_NO = #{clientNo,jdbcType=VARCHAR},</if>
            <if test="baseAcctNo != null ">BASE_ACCT_NO = #{baseAcctNo,jdbcType=VARCHAR},</if>
            <if test="acctSeqNo != null ">ACCT_SEQ_NO = #{acctSeqNo,jdbcType=VARCHAR},</if>
            <if test="acctCcy != null ">ACCT_CCY = #{acctCcy,jdbcType=VARCHAR},</if>
            <if test="prodType != null ">PROD_TYPE = #{prodType,jdbcType=VARCHAR},</if>
            <if test="isEndLevel != null ">IS_END_LEVEL = #{isEndLevel,jdbcType=VARCHAR},</if>
            <if test="batchStatus != null ">BATCH_STATUS = #{batchStatus,jdbcType=VARCHAR},</if>
            <if test="retMsg != null ">RET_MSG = #{retMsg,jdbcType=VARCHAR},</if>
            <if test="errorCode != null ">ERROR_CODE = #{errorCode,jdbcType=VARCHAR},</if>
            <if test="tranTimestamp != null ">TRAN_TIMESTAMP = #{tranTimestamp,jdbcType=DATE},</if>
            <if test="company != null ">COMPANY = #{company,jdbcType=VARCHAR},</if>
        </set>
    </sql>
    <select id="getByBaseAcctNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenYhtDetail">
        select <include refid="Base_Column"/>
        from RB_BATCH_OPEN_YHT_DETAIL
        where 1=1
        <if test="baseAcctNo != null and baseAcctNo != ''">
            and MAIN_RB_ACCT_NO = #{baseAcctNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

</mapper>
