<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist">
    <!-- Created by furongb on 2016/01/11 14:33:35. -->
    <resultMap id="MbIntTaxHistBean" type="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntTaxHist">
        <result column="TRAN_DATE" property="tranDate" jdbcType="DATE" javaType="DATE" />
    </resultMap>
    <select id="getMbIntTaxHistByReference" parameterType="java.util.Map"
            resultMap="MbIntTaxHistBean">
        SELECT <include refid="Base_Column"/>
        from RB_INT_TAX_HIST
        where REFERENCE = #{reference}
        order by TRAN_TIMESTAMP desc
    </select>
</mapper>
