<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT_INFO
    where PRECONTRACT_NO = #{precontractNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo">
    delete from RB_DC_PRECONTRACT_INFO
    where PRECONTRACT_NO = #{precontractNo}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo">
    insert into RB_DC_PRECONTRACT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="precontractNo != null">
        PRECONTRACT_NO,
      </if>
      <if test="clientNo != null">
        CLIENT_NO,
      </if>
      <if test="chClientName != null">
        CH_CLIENT_NAME,
      </if>
      <if test="clientName != null">
        CLIENT_NAME,
      </if>
      <if test="documentType != null">
        DOCUMENT_TYPE,
      </if>
      <if test="documentId != null">
        DOCUMENT_ID,
      </if>
      <if test="issCountry != null">
        ISS_COUNTRY,
      </if>
      <if test="precontractStype != null">
        PRECONTRACT_STYPE,
      </if>
      <if test="precontractDate != null">
        PRECONTRACT_DATE,
      </if>
      <if test="precontractOpenDate != null">
        PRECONTRACT_OPEN_DATE,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="issueStartDate != null">
        ISSUE_START_DATE,
      </if>
      <if test="issueEndDate != null">
        ISSUE_END_DATE,
      </if>
      <if test="precontractAmtBranch != null">
        PRECONTRACT_AMT_BRANCH,
      </if>
      <if test="issueAmt != null">
        ISSUE_AMT,
      </if>
      <if test="precontractCcy != null">
        PRECONTRACT_CCY,
      </if>
      <if test="precontractAmt != null">
        PRECONTRACT_AMT,
      </if>
      <if test="internalKey != null">
        INTERNAL_KEY,
      </if>
      <if test="resSeqNo != null">
        RES_SEQ_NO,
      </if>
      <if test="precontractBranch != null">
        PRECONTRACT_BRANCH,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="precontractStatus != null">
        PRECONTRACT_STATUS,
      </if>
      <if test="delUserId != null">
        DEL_USER_ID,
      </if>
      <if test="delAuthUserId != null">
        DEL_AUTH_USER_ID,
      </if>
      <if test="deleteDate != null">
        DELETE_DATE,
      </if>
      <if test="delReason != null">
        DEL_REASON,
      </if>
      <if test="openDate != null">
        OPEN_DATE,
      </if>
      <if test="failureReason != null">
        FAILURE_REASON,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="authUserId != null">
        AUTH_USER_ID,
      </if>
      <if test="reference != null">
        REFERENCE,
      </if>
      <if test="seqNo != null">
        SEQ_NO,
      </if>
      <if test="intCalcType != null">
        INT_CALC_TYPE,
      </if>
      <if test="intType != null">
        INT_TYPE,
      </if>
      <if test="actualRate != null">
        ACTUAL_RATE,
      </if>
      <if test="floatRate != null">
        FLOAT_RATE,
      </if>
      <if test="realRate != null">
        REAL_RATE,
      </if>
      <if test="narrative != null">
        NARRATIVE,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="printUserId != null">
        PRINT_USER_ID,
      </if>
      <if test="printDate != null">
        PRINT_DATE,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="precontractNo != null">
        #{precontractNo},
      </if>
      <if test="clientNo != null">
        #{clientNo},
      </if>
      <if test="chClientName != null">
        #{chClientName},
      </if>
      <if test="clientName != null">
        #{clientName},
      </if>
      <if test="documentType != null">
        #{documentType},
      </if>
      <if test="documentId != null">
        #{documentId},
      </if>
      <if test="issCountry != null">
        #{issCountry},
      </if>
      <if test="precontractStype != null">
        #{precontractStype},
      </if>
      <if test="precontractDate != null">
        #{precontractDate},
      </if>
      <if test="precontractOpenDate != null">
        #{precontractOpenDate},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="issueStartDate != null">
        #{issueStartDate},
      </if>
      <if test="issueEndDate != null">
        #{issueEndDate},
      </if>
      <if test="precontractAmtBranch != null">
        #{precontractAmtBranch},
      </if>
      <if test="issueAmt != null">
        #{issueAmt},
      </if>
      <if test="precontractCcy != null">
        #{precontractCcy},
      </if>
      <if test="precontractAmt != null">
        #{precontractAmt},
      </if>
      <if test="internalKey != null">
        #{internalKey},
      </if>
      <if test="resSeqNo != null">
        #{resSeqNo},
      </if>
      <if test="precontractBranch != null">
        #{precontractBranch},
      </if>
      <if test="sourceType != null">
        #{sourceType},
      </if>
      <if test="precontractStatus != null">
        #{precontractStatus},
      </if>
      <if test="delUserId != null">
        #{delUserId},
      </if>
      <if test="delAuthUserId != null">
        #{delAuthUserId},
      </if>
      <if test="deleteDate != null">
        #{deleteDate},
      </if>
      <if test="delReason != null">
        #{delReason},
      </if>
      <if test="openDate != null">
        #{openDate},
      </if>
      <if test="failureReason != null">
        #{failureReason},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="authUserId != null">
        #{authUserId},
      </if>
      <if test="reference != null">
        #{reference},
      </if>
      <if test="seqNo != null">
        #{seqNo},
      </if>
      <if test="intCalcType != null">
        #{intCalcType},
      </if>
      <if test="intType != null">
        #{intType},
      </if>
      <if test="actualRate != null">
        #{actualRate},
      </if>
      <if test="floatRate != null">
        #{floatRate},
      </if>
      <if test="realRate != null">
        #{realRate},
      </if>
      <if test="narrative != null">
        #{narrative},
      </if>
      <if test="company != null">
        #{company},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="printUserId != null">
        #{printUserId},
      </if>
      <if test="printDate != null">
        #{printDate},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
    </trim>
  </insert>
  <select id="selectByInternalKey"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT_INFO
    WHERE INTERNAL_KEY = #{internalKey}
    AND PRECONTRACT_STATUS != 'R'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <select id="selectByDate"  parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo">
    SELECT <include refid="Base_Column"/>
    FROM RB_DC_PRECONTRACT_INFO
    WHERE PRECONTRACT_STATUS != 'R'
    AND  PRECONTRACT_DATE between {issueStartDate} and {issueEndDate}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>
  <select id="getByPrecontractNo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontractInfo">
    select <include refid="Base_Column"/>
    from RB_DC_PRECONTRACT_INFO
    where PRECONTRACT_NO = #{precontractNo}
    AND PRECONTRACT_STATUS != 'R'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
