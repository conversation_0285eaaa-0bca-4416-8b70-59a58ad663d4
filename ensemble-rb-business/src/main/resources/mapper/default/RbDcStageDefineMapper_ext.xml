<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">

  <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--  <select id="getDcStageDefine" parameterType="com.dcits.galaxy.dal.mybatis.proactor.page.ParameterWrapper" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">-->
  <!--    select <include refid="Base_Column"/>-->
  <!--    from RB_DC_STAGE_DEFINE-->
  <!--    where 1=1-->
  <!--    <if test="issueYear != null">-->
  <!--      AND ISSUE_YEAR = #{issueYear}-->
  <!--    </if>-->
  <!--    <if test="prodType != null">-->
  <!--      AND PROD_TYPE = #{prodType}-->
  <!--    </if>-->
  <!--    <if test="acctCcy != null">-->
  <!--      AND CCY = #{acctCcy}-->
  <!--    </if>-->
  <!--  </select>-->

  <select id="getDcStageDefineByStageCode" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      AND CCY = #{acctCcy}
    </if>
    <if test="stageCodeDesc != null and stageCodeDesc != ''">
      AND STAGE_CODE_DESC like '%${stageCodeDesc}%'
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="getDcStageDefineForNotDC" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_PROD_CLASS != 'DC'
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null">
      AND CCY = #{acctCcy}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    delete from RB_DC_STAGE_DEFINE
    where STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    update RB_DC_STAGE_DEFINE
    <set>
      <if test="issueYear != null">
        ISSUE_YEAR = #{issueYear},
      </if>
      <if test="prodType != null">
        PROD_TYPE = #{prodType},
      </if>
      <if test="ccy != null">
        CCY = #{ccy},
      </if>
      <if test="stageCodeDesc != null">
        STAGE_CODE_DESC = #{stageCodeDesc},
      </if>
      <if test="tranDate != null">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="totalLimit != null">
        TOTAL_LIMIT = #{totalLimit},
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT = #{distributeLimit},
      </if>
      <if test="holdingLimit != null">
        HOLDING_LIMIT = #{holdingLimit},
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT = #{leaveLimit},
      </if>
      <if test="saleType != null">
        SALE_TYPE = #{saleType},
      </if>
      <if test="operateMethod != null">
        OPERATE_METHOD = #{operateMethod},
      </if>
      <if test="issueStartDate != null">
        ISSUE_START_DATE = #{issueStartDate},
      </if>
      <if test="issueEndDate != null">
        ISSUE_END_DATE = #{issueEndDate},
      </if>
      <if test="intCalcType != null">
        INT_CALC_TYPE = #{intCalcType},
      </if>
      <if test="resetIntFreq != null">
        RESET_INT_FREQ = #{resetIntFreq},
      </if>
      <if test="payIntType != null">
        PAY_INT_TYPE = #{payIntType},
      </if>
      <if test="getIntFreq != null">
        GET_INT_FREQ = #{getIntFreq},
      </if>
      <if test="transferFlag != null">
        TRANSFER_FLAG = #{transferFlag},
      </if>
      <if test="preWithdrawFlag != null">
        PRE_WITHDRAW_FLAG = #{preWithdrawFlag},
      </if>
      <if test="partWithdrawNum != null">
        PART_WITHDRAW_NUM = #{partWithdrawNum},
      </if>
      <if test="term != null">
        TERM = #{term},
      </if>
      <if test="termType != null">
        TERM_TYPE = #{termType},
      </if>
      <if test="userId != null">
        USER_ID = #{userId},
      </if>
      <if test="backStatus != null">
        BACK_STATUS = #{backStatus},
      </if>
      <if test="errorDesc != null">
        ERROR_DESC = #{errorDesc},
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS = #{stageProdClass},
      </if>
      <if test="stageRemark != null">
        STAGE_REMARK = #{stageRemark},
      </if>
      <if test="stageStatus != null">
        STAGE_STATUS = #{stageStatus}
      </if>
    </set>
    where STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    insert into RB_DC_STAGE_DEFINE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        ISSUE_YEAR,
      </if>
      <if test="prodType != null">
        PROD_TYPE,
      </if>
      <if test="ccy != null">
        CCY,
      </if>
      <if test="stageCode != null">
        STAGE_CODE,
      </if>
      <if test="stageCodeDesc != null">
        STAGE_CODE_DESC,
      </if>
      <if test="tranDate != null">
        TRAN_DATE,
      </if>
      <if test="tranBranch != null">
        TRAN_BRANCH,
      </if>
      <if test="totalLimit != null">
        TOTAL_LIMIT,
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT,
      </if>
      <if test="holdingLimit != null">
        HOLDING_LIMIT,
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT,
      </if>
      <if test="saleType != null">
        SALE_TYPE,
      </if>
      <if test="operateMethod != null">
        OPERATE_METHOD,
      </if>
      <if test="issueStartDate != null">
        ISSUE_START_DATE,
      </if>
      <if test="issueEndDate != null">
        ISSUE_END_DATE,
      </if>
      <if test="intCalcType != null">
        INT_CALC_TYPE,
      </if>
      <if test="resetIntFreq != null">
        RESET_INT_FREQ,
      </if>
      <if test="payIntType != null">
        PAY_INT_TYPE,
      </if>
      <if test="getIntFreq != null">
        GET_INT_FREQ,
      </if>
      <if test="transferFlag != null">
        TRANSFER_FLAG,
      </if>
      <if test="preWithdrawFlag != null">
        PRE_WITHDRAW_FLAG,
      </if>
      <if test="partWithdrawNum != null">
        PART_WITHDRAW_NUM,
      </if>
      <if test="term != null">
        TERM,
      </if>
      <if test="termType != null">
        TERM_TYPE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="tranTimestamp != null">
        TRAN_TIMESTAMP,
      </if>
      <if test="stageProdClass != null">
        STAGE_PROD_CLASS,
      </if>
      <if test="stageRemark != null">
        STAGE_REMARK,
      </if>
      <if test="stageStatus != null">
        STAGE_STATUS,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="issueYear != null">
        #{issueYear},
      </if>
      <if test="prodType != null">
        #{prodType},
      </if>
      <if test="ccy != null">
        #{ccy},
      </if>
      <if test="stageCode != null">
        #{stageCode},
      </if>
      <if test="stageCodeDesc != null">
        #{stageCodeDesc},
      </if>
      <if test="tranDate != null">
        #{tranDate},
      </if>
      <if test="tranBranch != null">
        #{tranBranch},
      </if>
      <if test="totalLimit != null">
        #{totalLimit},
      </if>
      <if test="distributeLimit != null">
        #{distributeLimit},
      </if>
      <if test="holdingLimit != null">
        #{holdingLimit},
      </if>
      <if test="leaveLimit != null">
        #{leaveLimit},
      </if>
      <if test="saleType != null">
        #{saleType},
      </if>
      <if test="optionMethod != null">
        #{optionMethod},
      </if>
      <if test="issueStartDate != null">
        #{issueStartDate},
      </if>
      <if test="issueEndDate != null">
        #{issueEndDate},
      </if>
      <if test="intCalcType != null">
        #{intCalcType},
      </if>
      <if test="resetIntFreq != null">
        #{resetIntFreq},
      </if>
      <if test="payIntType != null">
        #{payIntType},
      </if>
      <if test="getIntFreq != null">
        #{getIntFreq},
      </if>
      <if test="transferFlag != null">
        #{transferFlag},
      </if>
      <if test="preWithdrawFlag != null">
        #{preWithdrawFlag},
      </if>
      <if test="partWithdrawNum != null">
        #{partWithdrawNum},
      </if>
      <if test="term != null">
        #{term},
      </if>
      <if test="termType != null">
        #{termType},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="tranTimestamp != null">
        #{tranTimestamp},
      </if>
      <if test="stageProdClass != null">
        #{stageProdClass},
      </if>
      <if test="stageRemark != null">
        #{stageRemark},
      </if>
      <if test="stageStatus != null">
        #{stageStatus},
      </if>
      <if test="company != null">
        #{company},
      </if>
    </trim>
  </insert>

  <select id="geSumByLeaveLimt" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select
    sum(HOLDING_LIMIT) as HOLDING_LIMIT,
    sum(LEAVE_LIMIT) as LEAVE_LIMIT
    from RB_DC_STAGE_DEFINE
    where STAGE_CODE = #{stageCode}
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDcStageDefineOne" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="issueYear != null and issueYear != ''">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and ccy != ''">
      AND CCY = #{ccy}
    </if>
    <if test="runDate != null">
      <![CDATA[
      AND ISSUE_START_DATE <= #{runDate}
     ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="getDcStageDefineByOne" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null">
      AND CCY = #{ccy}
    </if>
    <if test="runDate != null">
      <![CDATA[
      AND ISSUE_START_DATE <= #{runDate}
      AND ISSUE_END_DATE >= #{runDate}
      ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="getDcStageDefineForEodSplit" parameterType="java.util.Map" resultType="java.util.Map">
    SELECT count(*) ROW_COUNT
    from RB_DC_STAGE_DEFINE
    where ISSUE_END_DATE = #{runDate}
      <!-- Multi-legal entity transformation -->
        <if test="company != null and company != '' ">
      AND COMPANY = #{company}
        </if>
  </select>

  <select id="getDcStageDefineListForEodSplit" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>

    from RB_DC_STAGE_DEFINE
    where ( BACK_STATUS = 'N' OR BACK_STATUS IS NULL)
      <![CDATA[
      and ( ISSUE_END_DATE <= #{runDate} )
        ]]>
    AND STAGE_PROD_CLASS = 'DC'
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>


  <select id="getProdStageDefineListForEodSplit" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>

    from RB_DC_STAGE_DEFINE
    where ( BACK_STATUS = 'N' OR BACK_STATUS IS NULL)
    <![CDATA[
    and ( ISSUE_END_DATE <= #{runDate} )
    ]]>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    AND STAGE_PROD_CLASS IN ('DC','YBWL','CLD')
    AND STAGE_STATUS != 'D'
  </select>

  <select id="getProdStageDefineListForChangeStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_PROD_CLASS IN('DC','YBWL')
    AND STAGE_STATUS != 'D'
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>




  <select id="getDcStageDefineList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select  <include refid="Base_Column"/>
    FROM RB_DC_STAGE_DEFINE
    WHERE
    STAGE_STATUS NOT IN ('D','S')
    <if test="prodType != null and prodType!=''">
      AND PROD_TYPE = #{prodType, jdbcType=VARCHAR}
    </if>
    <if test="stageCode != null and stageCode!=''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="issueYear != null and issueYear!=''">
      AND ISSUE_YEAR = #{issueYear, jdbcType=VARCHAR}
    </if>
    <if test="timeStart != null">
      AND
      <![CDATA[
      ISSUE_END_DATE >= #{timeStart}
      ]]>
    </if>
    <if test="timeEnd != null ">
      AND
      <![CDATA[
      ISSUE_END_DATE <= #{timeEnd}
      ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    ORDER BY ISSUE_YEAR,ISSUE_START_DATE DESC
  </select>
  <select id="getDcStageDefineExistList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="issueYear != null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="stageCode != null">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null">
      AND CCY = #{ccy}
    </if>
    <if test="runDate != null">
      <![CDATA[
      AND #{runDate} >= ISSUE_START_DATE
       ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>
  <select id="getDcStageDefineUnexpiredList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
      <if test="issueYear != null and issueYear != ''">
        AND ISSUE_YEAR = #{issueYear}
      </if>
      <if test="stageCode != null and stageCode != ''">
        AND STAGE_CODE = #{stageCode}
      </if>
      <if test="prodType != null and prodType != ''">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="ccy != null and ccy != ''">
        AND CCY = #{ccy}
      </if>
      <if test="stageStatus != null and stageStatus != ''">
        AND STAGE_STATUS != #{stageStatus}
      </if>
      <if test="runDate != null and runDate != ''">
        <![CDATA[
        AND (#{runDate} >= ISSUE_START_DATE
        and #{runDate} <= ISSUE_END_DATE)
            ]]>
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>

  <select id="getDcStageDefineUnexpiredListByPage" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="issueYear != null and issueYear != ''">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and ccy != ''">
      AND CCY = #{ccy}
    </if>
    <if test="allowBuyWayCd != null and allowBuyWayCd != ''">
      AND ALLOW_BUY_WAY_CD = #{allowBuyWayCd}
    </if>
    <if test="stageCodeDesc != null and stageCodeDesc != ''">
      AND STAGE_CODE_DESC like '%${stageCodeDesc}%'
    </if>
    <if test="runDate != null and runDate != ''">
      <![CDATA[
      AND (#{runDate} >= ISSUE_START_DATE
      and #{runDate} <= ISSUE_END_DATE)
          ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
    order by STAGE_CODE
  </select>

  <select id="getDcStageDefineForDC" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_PROD_CLASS = 'DC'
    <if test="issueYear != null and issueYear != ''">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="ccy != null and ccy != ''">
      AND CCY = #{ccy}
    </if>
    <if test="runDate != null and runDate != ''">
      <![CDATA[
      AND (#{runDate} >= ISSUE_START_DATE
      and #{runDate} <= ISSUE_END_DATE)
          ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="getDcStageDefineListWithAttach" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
      STAGE_CODE in (
      select STAGE_CODE
      from RB_DC_STAGE_DEFINE_ATTACH
      <where>
        <if test="branch != null and branch != ''">
          AND (SELL_BRANCH = #{branch} or SELL_BRANCH is null)
        </if>
        <if test="channel != null and channel != ''">
          AND (ON_SALE_CHANNEL like '%${channel}%' or ON_SALE_CHANNEL like '%ALL%')
        </if>
        <if test="clientType != null and clientType != ''">
          AND CLIENT_TYPE like '%${clientType}%'
        </if>
        <if test="inlandOffshore != null and inlandOffshore != ''">
          AND (INLAND_OFFSHORE like '%${inlandOffshore}%' or INLAND_OFFSHORE IS NULL)
        </if>
      </where>
      )
      <if test="prodType != null and prodType != ''">
        AND PROD_TYPE = #{prodType}
      </if>
      <if test="runDate != null ">
        AND #{runDate} <![CDATA[ >= ]]> ISSUE_START_DATE
        AND #{runDate} <![CDATA[ <= ]]> ISSUE_END_DATE
      </if>
      <if test="issueYear != null and issueYear != ''">
        AND ISSUE_YEAR = #{issueYear}
      </if>
      <if test="ccy != null and ccy != ''">
        AND CCY = #{ccy}
      </if>
      <if test="customerBase != null and customerBase != ''">
        AND STAGE_CODE in (select STAGE_CODE from RB_DC_CUSTOMER_BASE_CLASS WHERE CUSTOMER_SON = #{customerBase})
      </if>
      <if test="stageCode != null and stageCode != ''">
        AND STAGE_CODE = #{stageCode}
      </if>
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
        and STAGE_CODE in (	SELECT b.STAGE_CODE from RB_DC_STAGE_QUOTA b where  b.leave_Quota > 0 )
    </where>
  </select>

  <select id="getDcStageDefineIssueDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_PROD_CLASS = 'DC'
    <if test="issueYear!= null">
      AND ISSUE_YEAR = #{issueYear}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>

  <update id="updateStageStatus" parameterType="java.util.Map">
    update RB_DC_STAGE_DEFINE
    set STAGE_STATUS=#{stageStatus}
    where STAGE_CODE = #{stageCode}
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>

  <select id="getDcStageDefineDesListByAcct" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="stageProdClass!= null">
      AND STAGE_PROD_CLASS = #{stageProdClass}
    </if>
    <if test="stageStatus!= null">
      AND STAGE_STATUS = #{stageStatus}
    </if>
    <if test="stageCodeList != null">
      STAGE_CODE IN
      <foreach item="item" index="index" collection="stageCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>


  <select id="selectByProdAndTranDateS" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="tranDate!= null">
      <![CDATA[ AND  ISSUE_START_DATE > #{tranDate} ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
<!--    <if test="preDate!= null">-->
<!--      <![CDATA[ AND  #{preDate} between PRECONTRACT_START_TIME and PRECONTRACT_END_TIME ]]>-->
<!--    </if>-->
  </select>




  <select id="selectSTDcByProdAndTranDateP" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="stageProdClass!= null">
      AND STAGE_PROD_CLASS = #{stageProdClass}
    </if>
    <if test="tranDate!= null">
      <![CDATA[ AND  #{tranDate} between PRECONTRACT_START_TIME and PRECONTRACT_END_TIME ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>


  <select id="selectSTDcByProdAndTranDateS" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_STATUS = 'R'
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="stageProdClass!= null">
      AND STAGE_PROD_CLASS = #{stageProdClass}
    </if>
    <if test="tranDate!= null">
     AND <![CDATA[   #{tranDate} < PRECONTRACT_START_TIME  ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>

  <select id="selectSTDcByProdAndTranDateG" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_STATUS = 'R'
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="stageProdClass!= null">
      AND STAGE_PROD_CLASS = #{stageProdClass}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>


  <select id="selectSTDcByProdAndTranDateAll" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="stageProdClass!= null">
      AND STAGE_PROD_CLASS = #{stageProdClass}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="selectByProdAndTranDateP" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="tranDate!= null">
      <![CDATA[ AND  ISSUE_START_DATE > #{tranDate} ]]>
    </if>
    <if test="preDate!= null">
      <![CDATA[ AND  #{preDate} < PRECONTRACT_END_TIME ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>


  <select id="selectByProdAndTranDateE" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_STATUS NOT IN ('D','E')
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>

  </select>



  <select id="selectByProdAndTranDateG" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_STATUS NOT IN ('D','S')
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="tranDate!= null">
      <![CDATA[
      AND  ISSUE_START_DATE <= #{tranDate}
      AND  ISSUE_END_DATE >= #{tranDate}
      ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectByProdAndTranDateA" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_STATUS NOT IN ('D','S')
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="tranDate!= null">
      <![CDATA[
      AND  ISSUE_START_DATE <= #{tranDate}
      AND  ISSUE_END_DATE >= #{tranDate}
      ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>


  <select id="s1111" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select a.STAGE_CODE
    from rb_dc_stage_rule_define a,rb_dc_stage_define b
    where a.STAGE_CODE = b.STAGE_CODE
      and b.STAGE_STATUS IN ('D','G')
      and (a.MATURITY_DATE <![CDATA[ >= ]]> #{runDate})
      and a.TOUCH_FLAG = 'N'
        <if test="company != null and company != '' ">
      AND COMPANY = #{company}
        </if>
            )
  </select>




  <select id="selectByRationType1" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="rationType!= null">
      AND RATION_TYPE = #{rationType}
    </if>
    <if test="tranDate!= null">
      <![CDATA[ AND  ISSUE_START_DATE > #{tranDate} ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="selectByRationType0" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="prodType!= null">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="rationType!= null">
      AND RATION_TYPE = #{rationType}
    </if>
    <if test="tranDate!= null">
      <![CDATA[ AND  ISSUE_START_DATE <= #{tranDate} ]]>
      <![CDATA[ AND  ISSUE_END_DATE >= #{tranDate} ]]>
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
    </where>
  </select>

  <select id="getStageListByProdType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where STAGE_STATUS != 'D'
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
     AND SALE_TYPE is not null
  </select>

  <select id="getStageByIssueDateForUpdate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
    <if test="stageCode != null and stageCode != ''">
      AND STAGE_CODE = #{stageCode}
    </if>
    <if test="runDate != null ">
      AND #{runDate} <![CDATA[ >= ]]> ISSUE_START_DATE
      AND #{runDate} <![CDATA[ <= ]]> ISSUE_END_DATE
    </if>
    </where>
    FOR UPDATE
  </select>

  <update id="updateByPre"  >
    UPDATE <include refid="Table_Name" />
    <set>
      <if test="stageMaxAmt != null ">
        STAGE_MAX_AMT = #{stageMaxAmt},
      </if>
      PRECONTRACT_START_TIME = #{precontractStartTime},
      <if test="tranDate != null ">
        TRAN_DATE = #{tranDate},
      </if>
      <if test="distributeLimit != null ">
        DISTRIBUTE_LIMIT = #{distributeLimit},
      </if>
      <if test="leaveLimit != null ">
        LEAVE_LIMIT = #{leaveLimit},
      </if>
      <if test="operateMethod != null and  operateMethod != '' ">
        OPERATE_METHOD = #{operateMethod},
      </if>
      <if test="userId != null and  userId != '' ">
        USER_ID = #{userId},
      </if>
      <if test="rationType != null and  rationType != '' ">
        RATION_TYPE = #{rationType},
      </if>
      <if test="tranBranch != null and  tranBranch != '' ">
        TRAN_BRANCH = #{tranBranch},
      </if>
      <if test="totalLimit != null ">
        TOTAL_LIMIT = #{totalLimit},
      </if>
      <if test="payIntType != null and  payIntType != '' ">
        PAY_INT_TYPE = #{payIntType},
      </if>
      <if test="getIntFreq != null and  getIntFreq != '' ">
        GET_INT_FREQ = #{getIntFreq},
      </if>
      <if test="transferFlag != null and  transferFlag != '' ">
        TRANSFER_FLAG = #{transferFlag},
      </if>
      <if test="company != null and  company != '' ">
        COMPANY = #{company},
      </if>
      <if test="saleStartTime != null ">
        SALE_START_TIME = #{saleStartTime},
      </if>
      PRECONTRACT_END_TIME = #{precontractEndTime},
      <if test="ccy != null and  ccy != '' ">
        CCY = #{ccy},
      </if>
      <if test="intCalcType != null and  intCalcType != '' ">
        INT_CALC_TYPE = #{intCalcType},
      </if>
      <if test="resetIntFreq != null and  resetIntFreq != '' ">
        RESET_INT_FREQ = #{resetIntFreq},
      </if>
      <if test="errorDesc != null and  errorDesc != '' ">
        ERROR_DESC = #{errorDesc},
      </if>
      <if test="tranTimestamp != null ">
        TRAN_TIMESTAMP = #{tranTimestamp},
      </if>
      <if test="stageStatus != null and  stageStatus != '' ">
        STAGE_STATUS = #{stageStatus},
      </if>
      <if test="stageMinAmt != null ">
        STAGE_MIN_AMT = #{stageMinAmt},
      </if>
      <if test="issueYear != null and  issueYear != '' ">
        ISSUE_YEAR = #{issueYear},
      </if>
      <if test="prodType != null and  prodType != '' ">
        PROD_TYPE = #{prodType},
      </if>
      <if test="issueStartDate != null ">
        ISSUE_START_DATE = #{issueStartDate},
      </if>
      <if test="term != null and  term != '' ">
        TERM = #{term},
      </if>
      <if test="stageProdClass != null and  stageProdClass != '' ">
        STAGE_PROD_CLASS = #{stageProdClass},
      </if>
      <if test="stageCodeDesc != null and  stageCodeDesc != '' ">
        STAGE_CODE_DESC = #{stageCodeDesc},
      </if>
      <if test="saleType != null and  saleType != '' ">
        SALE_TYPE = #{saleType},
      </if>
      <if test="preWithdrawFlag != null and  preWithdrawFlag != '' ">
        PRE_WITHDRAW_FLAG = #{preWithdrawFlag},
      </if>
      <if test="redemptionFlag != null and  redemptionFlag != '' ">
        REDEMPTION_FLAG = #{redemptionFlag},
      </if>
      <if test="backStatus != null and  backStatus != '' ">
        BACK_STATUS = #{backStatus},
      </if>
      <if test="stageRemark != null and  stageRemark != '' ">
        STAGE_REMARK = #{stageRemark},
      </if>
      <if test="saleEndTime != null ">
        SALE_END_TIME = #{saleEndTime},
      </if>
      <if test="holdingLimit != null ">
        HOLDING_LIMIT = #{holdingLimit},
      </if>
      <if test="issueEndDate != null ">
        ISSUE_END_DATE = #{issueEndDate},
      </if>
      <if test="partWithdrawNum != null ">
        PART_WITHDRAW_NUM = #{partWithdrawNum},
      </if>
      <if test="termType != null and  termType != '' ">
        TERM_TYPE = #{termType},
      </if>
      <if test="stageLimitClass != null and  stageLimitClass != '' ">
        STAGE_LIMIT_CLASS = #{stageLimitClass},
      </if>
    </set>
    <where>
      <trim suffixOverrides="AND">
        <if test="stageCode != null and  stageCode != '' ">
          STAGE_CODE = #{stageCode}  AND
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
          COMPANY = #{company} AND
        </if>
      </trim>
    </where>
  </update>

  <select id="selectStageProdLimit" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    where ((STAGE_PROD_CLASS = 'DC' AND STAGE_STATUS != 'E') OR (STAGE_PROD_CLASS = 'ST' AND (STAGE_STATUS != 'S' OR STAGE_STATUS != 'RS' OR STAGE_STATUS != 'PS')))
    <if test="prodType != null and prodType != ''">
      AND PROD_TYPE = #{prodType}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>

  <select id="selectDcStageDefineDetailList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.td.certtd.DcStageDefineDetailModel">
    select t1.STAGE_CODE,t1.STAGE_CODE_DESC,t1.PAY_INT_TYPE,t1.PRE_WITHDRAW_FLAG,t1.STAGE_PROD_CLASS,
    t2.REAL_RATE,t2.INT_START_DATE,t2.MATURITY_DATE
    from RB_DC_STAGE_DEFINE t1,RB_DC_STAGE_DEFINE_ATTACH t2
    WHERE t1.STAGE_CODE = t2.STAGE_CODE
    <if test="stageCodeList != null and stageCodeList != '' ">
      AND t1.STAGE_CODE IN
      <foreach item="item" index="index" collection="stageCodeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="prodType != null and prodType != '' ">
      AND t1.PROD_TYPE = #{prodType}
    </if>
  </select>

  <select id="selStageByIssueYearAndStatus" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    select <include refid="Base_Column"/>
    from RB_DC_STAGE_DEFINE
    <where>
      <if test="stageStatus != null and stageStatus != ''">
        AND STAGE_STATUS = #{stageStatus}
      </if>
      <if test="issueYear != null and issueYear != ''">
        AND ISSUE_YEAR = #{issueYear}
      </if>
    </where>
  </select>
  <update id="updateRbDcStageDefine" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbDcStageDefine">
    UPDATE RB_DC_STAGE_DEFINE
    <set>
      <if test="stageCodeDesc != null">
        STAGE_CODE_DESC=#{stageCodeDesc},
      </if>
      <if test="issueStartDate != null">
        ISSUE_START_DATE=#{issueStartDate},
      </if>
      <if test="issueEndDate != null">
        ISSUE_END_DATE=#{issueEndDate},
      </if>
      <if test="precontractStartTime != null">
        PRECONTRACT_START_TIME=#{precontractStartTime},
      </if>
      <if test="precontractEndTime != null ">
        PRECONTRACT_END_TIME=#{precontractEndTime},
      </if>
      <if test="saleStartTime != null">
        SALE_START_TIME=#{saleStartTime},
      </if>
      <if test="saleEndTime != null">
        SALE_END_TIME=#{saleEndTime},
      </if>
      <if test="stageProdClass != null and stageProdClass != ''">
        STAGE_PROD_CLASS=#{stageProdClass},
      </if>
      <if test="stageLimitClass != null and stageLimitClass != ''">
        STAGE_LIMIT_CLASS=#{stageLimitClass},
      </if>
      <if test="stageRemark != null and stageRemark != ''">
        STAGE_REMARK=#{stageRemark},
      </if>
      <if test="saleType != null and saleType != ''">
        SALE_TYPE=#{saleType},
      </if>
      <if test="operateMethod != null and operateMethod != ''">
        OPERATE_METHOD=#{operateMethod},
      </if>
      <if test="totalLimit != null">
        TOTAL_LIMIT=#{totalLimit},
      </if>
      <if test="leaveLimit != null">
        LEAVE_LIMIT=#{leaveLimit},
      </if>
      <if test="distributeLimit != null">
        DISTRIBUTE_LIMIT=#{distributeLimit},
      </if>
      <if test="holdingLimit != null">
        HOLDING_LIMIT=#{holdingLimit},
      </if>
      <if test="backStatus != null and backStatus != ''">
        BACK_STATUS=#{backStatus},
      </if>
      <if test="rationType != null and rationType != ''">
        RATION_TYPE=#{rationType},
      </if>
      <if test="transferFlag != null and transferFlag != ''">
        TRANSFER_FLAG=#{transferFlag},
      </if>
      <if test="ccy != null and ccy != ''">
        CCY=#{ccy},
      </if>
      <if test="term != null and term != ''">
        TERM=#{term},
      </if>
      <if test="termType != null and termType != ''">
        TERM_TYPE=#{termType},
      </if>
      <if test="resetIntFreq != null and resetIntFreq != ''">
        RESET_INT_FREQ=#{resetIntFreq},
      </if>
      <if test="stageMaxAmt != null">
        STAGE_MAX_AMT=#{stageMaxAmt},
      </if>
      <if test="stageMinAmt != null">
        STAGE_MIN_AMT=#{stageMinAmt},
      </if>
      <if test="intCalcType != null and intCalcType != ''">
        INT_CALC_TYPE=#{intCalcType},
      </if>
      <if test="preWithdrawFlag != null and preWithdrawFlag != ''">
        PRE_WITHDRAW_FLAG=#{preWithdrawFlag},
      </if>
      <if test="redemptionFlag != null and redemptionFlag != ''">
        REDEMPTION_FLAG=#{redemptionFlag},
      </if>
      <if test="tranDate != null">
        TRAN_DATE=#{tranDate},
      </if>
      <if test="tranBranch != null and tranBranch != ''">
        TRAN_BRANCH=#{tranBranch},
      </if>
      <if test="errorDesc != null and errorDesc != ''">
        ERROR_DESC=#{errorDesc},
      </if>
      <if test="userId != null and userId != ''">
        USER_ID=#{userId},
      </if>
      <if test="company != null and company != ''">
        COMPANY=#{company},
      </if>
      <if test="autoSettleFlag != null and autoSettleFlag != ''">
        AUTO_SETTLE_FLAG=#{autoSettleFlag},
      </if>
      <if test="whiteSellFlag != null and whiteSellFlag != ''">
        WHITE_SELL_FLAG=#{whiteSellFlag},
      </if>
      <if test="customerBase != null and customerBase != ''">
        CUSTOMER_BASE=#{customerBase},
      </if>
      <if test="allowBranch != null and allowBranch != ''">
        ALLOW_BRANCH=#{allowBranch},
      </if>
      <if test="allowAcctClass != null and allowAcctClass != ''">
        ALLOW_ACCT_CLASS=#{allowAcctClass},
      </if>
      <if test="newAndOldFundFlag != null and newAndOldFundFlag != ''">
        NEW_AND_OLD_FUND_FLAG=#{newAndOldFundFlag},
      </if>
      <if test="stageCodeDescSecond != null and stageCodeDescSecond != ''">
        STAGE_CODE_DESC_SECOND=#{stageCodeDescSecond},
      </if>
      <if test="stageRemarkSecond != null and stageRemarkSecond != ''">
        STAGE_REMARK_SECOND=#{stageRemarkSecond},
      </if>
      <if test="stageCodeDescEn != null">
        STAGE_CODE_DESC_EN=#{stageCodeDescEn},
      </if>
      FIXED_RATE=#{fixedRate},
      PART_WITHDRAW_NUM=#{partWithdrawNum},
      GET_INT_FREQ=#{getIntFreq},
      TRF_ORDER_THRU_DATE=#{trfOrderThruDate},
      TRF_THRU_DATE=#{trfThruDate},
      STAGE_STATUS=#{stageStatus},
      TRAN_TIMESTAMP=#{tranTimestamp}
    </set>
    WHERE STAGE_CODE=#{stageCode};
  </update>

  <select id="selectStageCountByYear" parameterType="java.util.Map" resultType="java.lang.Integer">
    SELECT COUNT(*) from  RB_DC_STAGE_DEFINE where ISSUE_YEAR=#{issueYear}
  </select>
</mapper>
