<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchOpenSinCard">

	<update id="updateByBatchNoAndSeq"  >
		UPDATE <include refid="Table_Name" />
		<set>
			<if test="birthDay != null and  birthDay != '' ">
				BIRTHDAY = #{birthDay},
			</if>
			<if test="contactTel != null and  contactTel != '' ">
				CONTACT_TEL = #{contactTel},
			</if>
			<if test="postalCode != null and  postalCode != '' ">
				POSTAL_CODE = #{postalCode},
			</if>
			<if test="countryLoc != null and  countryLoc != '' ">
				COUNTRY_LOC = #{countryLoc},
			</if>
			<if test="documentExpiryDate != null and  documentExpiryDate != '' ">
				DOCUMENT_EXPIRY_DATE = #{documentExpiryDate},
			</if>
			<if test="birthPlace != null and  birthPlace != '' ">
				BIRTH_PLACE = #{birthPlace},
			</if>
			<if test="personnelStatus != null and  personnelStatus != '' ">
				PERSONNEL_STATUS = #{personnelStatus},
			</if>
			<if test="accountNature != null and  accountNature != '' ">
				ACCOUNT_NATURE = #{accountNature},
			</if>
			<if test="email != null and  email != '' ">
				EMAIL = #{email},
			</if>
			<if test="guardianDocumentType != null and  guardianDocumentType != '' ">
				GUARDIAN_DOCUMENT_TYPE = #{guardianDocumentType},
			</if>
			<if test="errorCode != null and  errorCode != '' ">
				ERROR_CODE = #{errorCode},
			</if>
			<if test="socSecNo != null and  socSecNo != '' ">
				SOC_SEC_NO = #{socSecNo},
			</if>
			<if test="coprNo != null and  coprNo != '' ">
				COPR_NO = #{coprNo},
			</if>
			<if test="coprName != null and  coprName != '' ">
				COPR_NAME = #{coprName},
			</if>
			<if test="documentId != null and  documentId != '' ">
				DOCUMENT_ID = #{documentId},
			</if>
			<if test="applyType != null and  applyType != '' ">
				APPLY_TYPE = #{applyType},
			</if>
			<if test="batchStatus != null and  batchStatus != '' ">
				BATCH_STATUS = #{batchStatus},
			</if>
			<if test="acctName != null and  acctName != '' ">
				ACCT_NAME = #{acctName},
			</if>
			<if test="guardian != null and  guardian != '' ">
				GUARDIAN = #{guardian},
			</if>
			<if test="cardNo != null and  cardNo != '' ">
				CARD_NO = #{cardNo},
			</if>
			<if test="seqNo != null and  seqNo != '' ">
				SEQ_NO = #{seqNo},
			</if>
			<if test="docType != null and  docType != '' ">
				DOC_TYPE = #{docType},
			</if>
			<if test="sex != null and  sex != '' ">
				SEX = #{sex},
			</if>
			<if test="guardianDocumentId != null and  guardianDocumentId != '' ">
				GUARDIAN_DOCUMENT_ID = #{guardianDocumentId},
			</if>
			<if test="retMsg != null and  retMsg != '' ">
				RET_MSG = #{retMsg},
			</if>
			<if test="batchNo != null and  batchNo != '' ">
				BATCH_NO = #{batchNo},
			</if>
			<if test="jobRunId != null and  jobRunId != '' ">
				JOB_RUN_ID = #{jobRunId},
			</if>
			<if test="personalNo != null and  personalNo != '' ">
				PERSONAL_NO = #{personalNo},
			</if>
			<if test="nation != null and  nation != '' ">
				NATION = #{nation},
			</if>
			<if test="mobileNo != null and  mobileNo != '' ">
				MOBILE_NO = #{mobileNo},
			</if>
			<if test="address != null and  address != '' ">
				ADDRESS = #{address},
			</if>
			<if test="agency != null and  agency != '' ">
				AGENCY = #{agency},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND">
				BATCH_NO = #{batchNo} AND SEQ_NO = #{seqNo}
<!-- 多法人改造 by luocwa -->
				<if test="company != null and company != '' ">
					AND COMPANY = #{company}
				</if>
			</trim>
		</where>
	</update>
</mapper>
