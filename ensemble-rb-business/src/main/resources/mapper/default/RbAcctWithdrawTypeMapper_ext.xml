<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType">

  <select id="selectByPartPrimaryKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType">
    select <include refid="Base_Column"/>
    from RB_ACCT_WITHDRAW_TYPE
    <where>
      <if test="clientNo != null">
        CLIENT_NO = #{clientNo}
      </if>
      <if test="withdrawKey != null">
        AND WITHDRAW_KEY = #{withdrawKey}
      </if>
      <if test="withdrawalType != null">
        AND WITHDRAWAL_TYPE = #{withdrawalType}
      </if>
      <!-- 多法人改造 by LIYUANV -->
      <if test="company != null and company != '' ">
        AND COMPANY = #{company}
      </if>
    </where>
  </select>
  <delete id="deleteByInternalKey" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType">
    delete from RB_ACCT_WITHDRAW_TYPE
    where WITHDRAW_KEY = #{withdrawKey}
      <if test="withdrawalType != null">
        AND WITHDRAWAL_TYPE = #{withdrawalType}
      </if>
      <if test="clientNo != null">
        AND CLIENT_NO = #{clientNo}
      </if>
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </delete>
  <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType">
    update RB_ACCT_WITHDRAW_TYPE
    <set>
      <if test="channelMuster != null">
        CHANNEL = #{channelMuster},
      </if>
      <if test="dacValue != null">
        DAC_VALUE = #{dacValue},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>

    </set>
    where WITHDRAW_KEY = #{withdrawKey}
        AND WITHDRAWAL_TYPE = #{withdrawalType}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
  <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType">
    insert into RB_ACCT_WITHDRAW_TYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="withdrawKey != null">
        WITHDRAW_KEY,
      </if>
      <if test="withdrawalType != null">
        WITHDRAWAL_TYPE,
      </if>
      <if test="channelMuster != null">
        CHANNEL,
      </if>
      <if test="dacValue != null">
        DAC_VALUE,
      </if>
      <if test="company != null">
        COMPANY,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="withdrawKey != null">
        #{withdrawKey},
      </if>
      <if test="withdrawalType != null">
        #{withdrawalType},
      </if>
      <if test="channelMuster != null">
        #{channelMuster},
      </if>
      <if test="dacValue != null">
        #{dacValue},
      </if>
      <if test="company != null">
        #{company},
      </if>

    </trim>
  </insert>
  <update id="updateByRbAcctWithdrawType" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType">
    update RB_ACCT_WITHDRAW_TYPE
    <set>
      <if test="channelMuster != null">
        CHANNEL = #{channelMuster},
      </if>
      <if test="dacValue != null">
        DAC_VALUE = #{dacValue},
      </if>
      <if test="company != null">
        COMPANY = #{company},
      </if>
      <if test="withdrawalType != null">
        WITHDRAWAL_TYPE = #{withdrawalType},
      </if>
    </set>
    where WITHDRAW_KEY = #{withdrawKey}
    AND CLIENT_NO = #{clientNo}
    <!-- 多法人改造 by LIYUANV -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
