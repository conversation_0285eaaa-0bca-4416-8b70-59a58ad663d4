<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbNoteAccrHist">
	<select id="getNoteAccrByAccrDate" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbNoteAccrHist">
		select <include refid="Base_Column"/>
		from RB_NOTE_ACCR_HIST
		where
		ACCR_DATE BETWEEN #{lastRunDate} AND #{yesterday}
		AND INT_ACCRUED_CTD <![CDATA[<>]]> 0
		<if test="internalKey != null and internalKey != ''">
			AND INTERNAL_KEY = #{internalKey}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
