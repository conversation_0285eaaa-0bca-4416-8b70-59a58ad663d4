<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchInternalCloseDetail">


    <update id="updateByBatchNoAndSeqNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchInternalCloseDetail">
        UPDATE
        <include refid="Table_Name"/>
        <include refid="Base_Set"/>
        WHERE BATCH_NO = #{batchNo} AND SEQ_NO = #{seqNo}
    </update>

    <update id="updateBatchStatus"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchInternalCloseDetail">
        UPDATE
        <include refid="Table_Name"/>
        <set>
            BATCH_STATUS = #{batchStatus},
        </set>
        WHERE BASE_ACCT_NO = #{baseAcctNo}
        AND ACCT_SEQ_NO = #{acctSeqNo}
        AND ACCT_CCY = #{acctCcy}
        AND PROD_TYPE = #{prodType}
        AND ACCT_OPERATE_TYPE = #{acctOperateType}
    </update>

    <update id="updRbBatchInternalCloseDetail" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchInternalCloseDetail">
        update Rb_Batch_Internal_Close_Detail
        <set>
            <if test="retCode != null and  retCode != '' ">
                Ret_Code = #{retCode, jdbcType=VARCHAR},
            </if>
            <if test="batchStatus != null and  batchStatus != '' ">
                Batch_Status = #{batchStatus, jdbcType=VARCHAR},
            </if>
            <if test="errorCode != null and  errorCode != '' ">
                Error_Code = #{errorCode, jdbcType=VARCHAR},
            </if>
            <if test="errorDesc != null and  errorDesc != '' ">
                Error_Desc = #{errorDesc, jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="batchNo != null and  batchNo != '' ">
                and Batch_No = #{batchNo, jdbcType=VARCHAR}
            </if>
            <if test="seqNo != null and  seqNo != '' ">
                and Seq_No = #{seqNo, jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <select id="getDetailsByBranch" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchInternalCloseDetail">
        select a.*
        from
            RB_BATCH_INTERNAL_CLOSE_DETAIL a inner join RB_ACCT b
        on
            a.base_acct_no = b.base_acct_no and a.acct_seq_no = b.acct_seq_no
        where
            a.approval_no = #{approvalNo, jdbcType=VARCHAR}
        and
            a.acct_operate_type = #{acctOperateType, jdbcType=VARCHAR}
        <if test="batchStatus != null and batchStatus != ''">
            and a.batch_status = #{batchStatus, jdbcType=VARCHAR}
        </if>
        <if test="branchList != null and branchList.size > 0">
            and b.acct_branch in
            <foreach collection="branchList" index="index" open="(" close=")" separator="," item="branch">
                #{branch}
            </foreach>
        </if>

    </select>


    <select id="selectDetailInfoForUpdate"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchInternalCloseDetail"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchInternalCloseDetail">
        SELECT <include refid="Base_Column"/>
        FROM RB_BATCH_INTERNAL_CLOSE_DETAIL
        WHERE BATCH_NO = #{batchNo} and SEQ_NO = #{seqNo} and BATCH_STATUS = 'P' FOR UPDATE
    </select>
</mapper>