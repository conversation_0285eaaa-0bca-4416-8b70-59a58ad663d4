<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFreeze">


	<select id="selectByInternalKey" parameterType="map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFreeze">
		select <include refid="Base_Column"/>
		from RB_FREEZE
		where INTERNAL_KEY = #{internalKey}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		ORDER BY RES_SEQ_NO+0 DESC
	</select>

	<select id="getMbFreeze" parameterType="map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFreeze">
		select <include refid="Base_Column"/>
		from RB_FREEZE
		where RES_SEQ_NO = #{resSeqNo}
		  AND CLIENT_NO = #{clientNo}
		and freezeStatus != 'E'
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>
</mapper>
