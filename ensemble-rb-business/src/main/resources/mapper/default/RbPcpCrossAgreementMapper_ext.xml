<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCrossAgreement">

  <select id="selectOneActive" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCrossAgreement" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCrossAgreement">
    select <include refid="Base_Column"/>
    from RB_PCP_CROSS_AGREEMENT
    where AGREEMENT_STATUS = 'A'
    <if test="agreementId != null and agreementId !='' ">
      AND AGREEMENT_ID = #{agreementId}
    </if>
    <if test="internalKey != null and internalKey !='' ">
      AND INTERNAL_KEY = #{internalKey}
    </if>
    <if test="baseAcctNo != null and baseAcctNo !='' ">
      AND BASE_ACCT_NO = #{baseAcctNo}
    </if>
    <if test="prodType != null and prodType !='' ">
      AND PROD_TYPE = #{prodType}
    </if>
    <if test="acctCcy != null and acctCcy !='' ">
      AND ACCT_CCY = #{acctCcy}
    </if>
    <if test="clientNo != null and clientNo !='' ">
      AND CLIENT_NO = #{clientNo}
    </if>
    <if test="pcpProdType != null and pcpProdType !='' ">
      AND PCP_PROD_TYPE = #{pcpProdType}
    </if>
    <if test="signId != null and signId !='' ">
      AND SIGN_ID = #{signId}
    </if>
    <if test="recordNoticeNo != null and recordNoticeNo !='' ">
      AND RECORD_NOTICE_NO = #{recordNoticeNo}
    </if>
  </select>

</mapper>
