<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAccrInfoMain">
	<sql id="Base_Column_List">
		IRL_SEQ_NO,
		SYSTEM_ID,
		INTERNAL_KEY,
		INT_CLASS,
		ACCR_DATE,
		BASE_ACCT_NO,
		PROD_TYPE,
		BRANCH,
		SOURCE_TYPE,
		CCY,
		INT_ACCRUED,
		INT_ACCRUED_CTD,
		INT_ACCRUED_CALC_CTD,
		INT_ACCRUED_DIFF,
		TAX_ACCRUED,
		TAX_ACCRUED_CTD,
		TAX_ACCRUED_CALC_CTD,
		TAX_ACCRUED_DIFF,
		TAX_TYPE,
		TAX_RATE,
		CLIENT_NO,
		CLIENT_TYPE,
		TRAN_SOURCE,
		ACCOUNTING_STATUS,
		PROFIT_CENTRE,
		SOURCE_MODULE,
		BUSINESS_UNIT,
		AGG,
		INT_AMT,
		MONTH_BASIS,
		YEAR_BASIS,
		REFERENCE,
		REMARK,
		REVERSAL,
		ACCT_SEQ_NO,
		INT_CALC_BAL,
		TRAN_TIMESTAMP,
		TRAN_TIME,
		ROUTER_KEY,
		GL_POSTED,
		COMPANY,
		OTH_REFERENCE,
		INT_TYPE,
		ACTUAL_RATE,
		FLOAT_RATE,
		REAL_RATE,
		ACCT_SPREAD_RATE,
		ACCT_PERCENT_RATE,
		ACCT_FIXED_RATE,
		AGREE_CHANGE_TYPE,
		AGREE_FIXED_RATE,
		AGREE_PERCENT_RATE,
		AGREE_SPREAD_RATE,
		TD_INT_NUM_DAYS,
		TD_LAST_ACCR_DATE,
		SPLIT_RATE_FLAG
	</sql>
	<select id="get" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAccrInfoMain"
			resultMap="RbAccrInfoMainBean">
		SELECT <include refid="Base_Column_List"/>
		FROM RB_ACCR_INFO_MAIN
		WHERE INTERNAL_KEY = #{internalKey}
		AND SYSTEM_ID = #{systemId}
		AND ACCR_DATE = #{accrDate}
		AND INT_CLASS = #{intClass}
	</select>

	<select id="selectAccrInfoByDate" parameterType="java.util.Map"
			resultMap="RbAccrInfoMainBean">
		SELECT <include refid="Base_Column_List"/>
		FROM RB_ACCR_INFO_MAIN
		<![CDATA[
			WHERE INTERNAL_KEY = #{internalKey}
			AND SYSTEM_ID = #{systemId}
			AND ACCR_DATE >= #{startDate}
			AND ACCR_DATE < #{endDate}
		]]>
	</select>

	<update id="updateAccrInfo" parameterType="java.util.Map">
		UPDATE RB_ACCR_INFO_MAIN
		<set>
			GL_POSTED = 'Y',
			TRAN_TIMESTAMP = #{tranTimestamp},
			TRAN_TIME = #{tranTime},
			ROUTER_KEY = #{routerKey}
		</set>
		WHERE IRL_SEQ_NO = #{IRL_SEQ_NO}
	</update>

	<select id="getAccrInfoMainList" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAccrInfoMain" resultMap="RbAccrInfoMainBean">
		SELECT <include refid="Base_Column_List"/>
		FROM RB_ACCR_INFO_MAIN
		WHERE INTERNAL_KEY = #{internalKey}
		AND SYSTEM_ID = #{systemId}
		AND ACCR_DATE = #{accrDate}
		AND INT_CLASS = #{intClass}
		AND TRAN_SOURCE = #{tranSource}
        AND CLIENT_NO = #{clientNo}
	</select>


	<select id="getSumAccrInfoMainByDate" parameterType="map" resultMap="RbAccrInfoMainMap">
		<![CDATA[
		SELECT t.INT_CLASS INT_CLASS,SUM(t.INT_ACCRUED_CALC_CTD) INT_ACCRUED_CALC_CTD,
			   MIN(ACCR_DATE) START_DATE,MAX(accr_date) END_DATE
		FROM RB_ACCR_INFO_MAIN t
		WHERE INTERNAL_KEY = #{internalKey}
		  AND SYSTEM_ID = #{systemId}
		  AND ACCR_DATE >= #{startDate}
		  AND ACCR_DATE < #{endDate}
		  AND CLIENT_NO = #{clientNo}
		GROUP BY t.int_class
		]]>
	</select>
	<select id="getAccrInfoMainLists" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAccrInfoMain"
			resultMap="RbAccrInfoMainBean">
		SELECT <include refid="Base_Column_List"/>
		FROM RB_ACCR_INFO_MAIN
		<![CDATA[
			WHERE INTERNAL_KEY = #{internalKey}
			  AND SYSTEM_ID = #{systemId}
			  AND ACCR_DATE >= #{startDate}
			  AND ACCR_DATE <= #{endDate}
			  AND INT_CLASS = #{intClass}
			]]>

	</select>

	<resultMap id="RbAccrInfoMainBean" type="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAccrInfoMain">
		<result column="ACCR_DATE" property="accrDate" jdbcType="DATE" javaType="String" />
	</resultMap>
	<resultMap id="RbAccrInfoMainMap" type="java.util.Map">
		<result column="ACCR_DATE" property="accrDate" jdbcType="DATE" javaType="String" />
	</resultMap>
</mapper>