<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbNocheckAccount">

	<update id="updateByBatch" parameterType="com.dcits.ensemble.rb.business.model.entity.dbmodel.RbNocheckAccount" >

		UPDATE RB_NOCHECK_ACCOUNT
		<set>
			<if test="clientName != null and  clientName != '' ">
				CLIENT_NAME = #{clientName},
			</if>
			<if test="remark1 != null and  remark1 != '' ">
				REMARK1 = #{remark1},
			</if>
			<if test="batchNo != null and  batchNo != '' ">
				BATCH_NO = #{batchNo},
			</if>
			<if test="retMsg != null and  retMsg != '' ">
				RET_MSG = #{retMsg},
			</if>
			<if test="batchStatus != null and  batchStatus != '' ">
				BATCH_STATUS = #{batchStatus},
			</if>
			<if test="endEffectDate != null and  endEffectDate != '' ">
				END_EFFECT_DATE = #{endEffectDate},
			</if>
			<if test="remark2 != null and  remark2 != '' ">
				REMARK2 = #{remark2},
			</if>
			<if test="errorDesc != null and  errorDesc != '' ">
				ERROR_DESC = #{errorDesc},
			</if>
			<if test="tranTimestamp != null ">
				TRAN_TIMESTAMP = #{tranTimestamp},
			</if>
			<if test="baseAcctNo != null and  baseAcctNo != '' ">
				BASE_ACCT_NO = #{baseAcctNo},
			</if>
			<if test="globalIdType != null and  globalIdType != '' ">
				GLOBAL_ID_TYPE = #{globalIdType},
			</if>
			<if test="startEffectDate != null and  startEffectDate != '' ">
				START_EFFECT_DATE = #{startEffectDate},
			</if>
			<if test="jobRunId != null and  jobRunId != '' ">
				JOB_RUN_ID = #{jobRunId},
			</if>
			<if test="resFlag != null and  resFlag != '' ">
				RES_FLAG = #{resFlag},
			</if>
			<if test="errorCode != null and  errorCode != '' ">
				ERROR_CODE = #{errorCode},
			</if>
			<if test="clientNo != null and  clientNo != '' ">
				CLIENT_NO = #{clientNo},
			</if>
			<if test="globalId != null and  globalId != '' ">
				GLOBAL_ID = #{globalId},
			</if>
			<if test="remark3 != null and  remark3 != '' ">
				REMARK3 = #{remark3},
			</if>
		</set>
		where BATCH_NO = #{batchNo}
		<if test="baseAcctNo != null">
			AND BASE_ACCT_NO = #{baseAcctNo}
		</if>
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>

</mapper>
