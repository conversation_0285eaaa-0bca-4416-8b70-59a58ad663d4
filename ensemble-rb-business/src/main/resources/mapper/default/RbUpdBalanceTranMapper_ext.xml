<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">

    <select id="selectByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        select
        <include refid="Base_Column"/>
        from RB_UPD_BALANCE_TRAN
        where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getNoCalcBalTran" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        select
        <include refid="Base_Column"/>
        from RB_UPD_BALANCE_TRAN
        where BAL_CALC_FLAG = 'N'
        <if test="fromDate != null">
            AND TRAN_DATE BETWEEN #{fromDate} AND
            #{toDate}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <delete id="deleteByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        delete from RB_UPD_BALANCE_TRAN
        where SEQ_NO = #{seqNo}
        <if test="clientNo != null and  clientNo != '' ">
            AND CLIENT_NO = #{clientNo,jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>
    <update id="updateByPrimaryKeyExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        update RB_UPD_BALANCE_TRAN
        <set>
            <if test="internalKey != null">
                INTERNAL_KEY = #{internalKey},
            </if>
            <if test="tranDate != null">
                TRAN_DATE = #{tranDate},
            </if>
            <if test="tranType != null">
                TRAN_TYPE = #{tranType},
            </if>
            <if test="eventType != null">
                EVENT_TYPE = #{eventType},
            </if>
            <if test="crDrInd != null">
                CR_DR_IND = #{crDrInd},
            </if>
            <if test="tranAmt != null">
                TRAN_AMT = #{tranAmt},
            </if>
            <if test="balCalcFlag != null">
                BAL_CALC_FLAG = #{balCalcFlag},
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP = #{tranTimestamp}
            </if>
        </set>
        where SEQ_NO = #{seqNo}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>
    <select id="getNoCalcBalTranByInternalKey" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        SELECT
        SUM(
        CASE CR_DR_IND
        WHEN 'D'
        THEN tran_amt
        ELSE 0 - tran_amt END)
        TRAN_AMT,
        INTERNAL_KEY,
        TRAN_DATE
        FROM RB_UPD_BALANCE_TRAN
        WHERE BAL_CALC_FLAG IN ('P','N')
        <if test="internalKey != null">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo != null and clientNo.length() > 0">
            AND CLIENT_NO = #{clientNo}
        </if>

        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        GROUP BY INTERNAL_KEY, TRAN_DATE
    </select>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        insert into RB_UPD_BALANCE_TRAN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                SEQ_NO,
            </if>
            <if test="internalKey != null">
                INTERNAL_KEY,
            </if>
            <if test="tranDate != null">
                TRAN_DATE,
            </if>
            <if test="tranType != null">
                TRAN_TYPE,
            </if>
            <if test="eventType != null">
                EVENT_TYPE,
            </if>
            <if test="crDrInd != null">
                CR_DR_IND,
            </if>
            <if test="tranAmt != null">
                TRAN_AMT,
            </if>
            <if test="balCalcFlag != null">
                BAL_CALC_FLAG,
            </if>
            <if test="tranTimestamp != null">
                TRAN_TIMESTAMP,
            </if>
            <if test="company != null">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seqNo != null">
                #{seqNo},
            </if>
            <if test="internalKey != null">
                #{internalKey},
            </if>
            <if test="tranDate != null">
                #{tranDate},
            </if>
            <if test="tranType != null">
                #{tranType},
            </if>
            <if test="eventType != null">
                #{eventType},
            </if>
            <if test="crDrInd != null">
                #{crDrInd},
            </if>
            <if test="tranAmt != null">
                #{tranAmt},
            </if>
            <if test="balCalcFlag != null">
                #{balCalcFlag},
            </if>
            <if test="tranTimestamp != null">
                #{tranTimestamp},
            </if>
            <if test="company != null">
                #{company}
            </if>
        </trim>
    </insert>

    <select id="calcTotalTranAmt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        SELECT SUM (
        CASE cr_dr_ind
        WHEN 'D' THEN tran_amt
        WHEN 'C' THEN tran_amt * -1
        END) tran_amt
        FROM rb_upd_balance_tran
        WHERE     bal_calc_flag = 'N'
        AND internal_key = #{internalKey, jdbcType=BIGINT}
        AND client_no = #{clientNo, jdbcType=VARCHAR}
        <if test="seqNo != null and  seqNo != '' ">
            AND seq_no != #{seqNo, jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <select id="getNoCalcBalTranByCount" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        select
        <include refid="Base_Column"/>
        from RB_UPD_BALANCE_TRAN
        where BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>


    <select id="getNoCalcBalTranMinSeqNo" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        SELECT MIN(TIMES_SEQ_NO) AS TIMES_SEQ_NO
        FROM RB_UPD_BALANCE_TRAN
        WHERE BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        AND client_no =#{clientNo}
        <if test="cometStart != null">
            AND internal_key BETWEEN #{cometStart} and #{cometEnd}
        </if>
        AND tran_date =#{runDate,jdbcType=DATE}
        <if test="tranTimeStamp != null">
            AND #{tranTimeStamp} > TRAN_TIMESTAMP
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>

    <delete id="batchDeleteRbUpdBalanceTran" parameterType="java.util.HashMap">
        delete from RB_UPD_BALANCE_TRAN
        where client_no = #{clientNo, jdbcType=VARCHAR}
        and seq_no IN
        <foreach item="item" index="index" collection="seqNoList" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <delete id="batchDeleteRbUpdBalanceTranOne" parameterType="java.util.HashMap">
        delete from RB_UPD_BALANCE_TRAN
        where client_no = #{clientNo, jdbcType=VARCHAR}
        and seq_no =#{seqNo, jdbcType=VARCHAR}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </delete>

    <update id="batchUpdateRbUpdBalanceTranOne" parameterType="java.util.HashMap">
        update RB_UPD_BALANCE_TRAN
        set BAL_CALC_FLAG = #{balCalcFlag, jdbcType=VARCHAR}
        where client_no = #{clientNo, jdbcType=VARCHAR}
        and seq_no = #{seqNo, jdbcType=VARCHAR}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="queryRbUpdBalanceTran" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        select
        <include refid="Base_Column"/>
        from RB_UPD_BALANCE_TRAN
        where seq_no = #{tranSeqNo, jdbcType=VARCHAR}
        <if test="clientNo != null and  clientNo != '' ">
        AND client_no = #{clientNo, jdbcType=VARCHAR}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getNoCalcBalTranByFlag" parameterType="java.util.HashMap"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbUpdBalanceTran">
        select
        <include refid="Base_Column"/>
        from RB_UPD_BALANCE_TRAN
        where BAL_CALC_FLAG =#{balCalcFlag}
        <if test="baseAcctNo != null and  baseAcctNo != '' ">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
