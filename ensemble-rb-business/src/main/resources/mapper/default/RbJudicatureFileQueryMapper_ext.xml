<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbJudicatureFileQuery">

	<update id="updateRbJudicatureFileByBatchNo" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbJudicatureFileQuery">
		update RB_JUDICATURE_FILE_QUERY
		<set>
			<if test="fileName != fileName and  fileName != ''">
				FILE_NAME = #{fileName},
			</if>
			<if test="tranFileResult != null and  tranFileResult != ''">
				TRAN_FILE_RESULT = #{tranFileResult},
			</if>
			<if test="fileErrorMsg != null and  fileErrorMsg != ''">
				FILE_ERROR_MSG = #{fileErrorMsg},
			</if>
			<if test="channelSeqNo != null and  channelSeqNo != ''">
				CHANNEL_SEQ_NO = #{channelSeqNo},
			</if>
			<if test="subSeqNo != null and  subSeqNo != ''">
				SUB_SEQ_NO = #{subSeqNo},
			</if>
		</set>
		where BATCH_NO = #{batchNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</update>


	<select id="selectRbJudicatureFileQueryByBatchNo" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbJudicatureFileQuery">
		select
		<include refid="Base_Column"/>
		from RB_JUDICATURE_FILE_QUERY
		where BATCH_NO = #{batchNo}
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
	</select>



</mapper>
