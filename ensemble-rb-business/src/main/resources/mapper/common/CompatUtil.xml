<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="CompatUtil">
 <!--本文件为兼容工具，用于实现oracle和mysql兼容，oracle与mysql语法差异在这里抽出公共方法-->


    <!--日期时间格式转换-->
    <sql id="toDate">
        <if test="_databaseId == 'oracle'">
            to_date(${date},coalesce(#{pattern},'yyyy-mm-dd'))
        </if>
        <if test="_databaseId == 'mysql'">
            str_to_date(${date},coalesce(#{pattern},'%Y%m%d%'))
        </if>
    </sql>

    <sql id="toTimestamp">
        <if test="_databaseId == 'oracle'">
            to_date(${date},coalesce(#{pattern},'yyyy-mm-dd hh:mi:dd'))
        </if>
        <if test="_databaseId == 'mysql'">
            str_to_date(${date},coalesce(#{pattern},'%Y%m%d% %h:%i:%is'))
        </if>
    </sql>

</mapper>
