# 多数据库Mapper开发规范

## **一、规范要求**

- 新建sql时，若为通用标准sql，在default下存放，无需设定databaseId标签；
- 若为方言sql（非标准sql），无需在default目录中提交，但必须在现有每一个数据库文件夹下，均进行提交或修改，保持每个数据库逻辑一致。

## 二、细则说明

### 1.通用SQL

- 多数据库通用SQL放入mapper/default目录

### 2.方言SQL

- 依据数据库类型，在mapper目录下数据库目录，存放该数据库的方案SQL。如：mapper/mysql存放MySQL数据库的方言SQL

### 3.mapper的命名空间（namespace）

- 通用SQL与方言SQL公用同一个命名空间。如：clBatchInfo的通用SQL、MySQL方言、Oracle方言都在同一个命名空间下。

### 4.mapper文件命名

- 方言SQL的mapper文件采用xxxx.databaseid.xml命名，比如clBatchInfo的命名：
  - default文件夹下：clBatchInfo_ext.xml
  - mysql文件夹下：clBatchInfo_ext.mysql.xml
  - oracle文件夹下：clBatchInfo_ext.oracle.xml