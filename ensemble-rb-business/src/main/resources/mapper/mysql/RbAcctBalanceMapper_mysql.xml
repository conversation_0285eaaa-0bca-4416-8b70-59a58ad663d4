<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance">
    <select id="selectRbBalanceSummary" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance" useCache="false" databaseId="mysql">
        SELECT
        SUM(coalesce(total_amount_prev,0)) total_amount,
        STR_TO_DATE('${lastRunDate}','%Y-%m-%d') LAST_BAL_UPD_DATE
        FROM RB_ACCT_BALANCE
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        AND CLIENT_NO = #{clientNo}
    </select>

    <update id="updateFinRegAmount" parameterType="java.util.Map" databaseId="mysql">
        update RB_ACCT_BALANCE set FINREG_AMOUNT =  ifnull(FINREG_AMOUNT,0) + #{finRegAmount}, LAST_CHANGE_DATE  = #{runDate}
        where internal_key = #{internalKey} and CLIENT_NO = #{clientNo}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </update>

    <select id="selectRbBalanceSummary1"  parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.acct.RbAcctBalanceTotalModel"  useCache="false" databaseId="mysql">
        select SUM(case b.ACCT_STOP_PAY WHEN 'Y' THEN 0  ELSE coalesce((-1)*total_amount,0)+coalesce(-1*finreg_amount,0)-coalesce(pld_amount,0)+coalesce(-1*dos_amount,0) END) as  total_amount,
        prod_type as prod_type ,
        acct_ccy as acct_ccy,
        base_acct_no as base_acct_no
        from rb_acct_balance a,  rb_acct b
        where a.internal_key =b.internal_key
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        and b.base_acct_no =#{baseAcctNo}
        and b.client_no = #{clientNo}
        and b.acct_real_flag='Y'
    </select>
</mapper>
