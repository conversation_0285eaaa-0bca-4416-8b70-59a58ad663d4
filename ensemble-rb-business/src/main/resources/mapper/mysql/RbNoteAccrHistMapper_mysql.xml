<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbNoteAccrHist">
	<select id="sumNoteAcctIntMerge" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbNoteAccrHist" databaseId="mysql">
		select
		a.TRAN_BRANCH,
		a.CCY,
		a.PROD_TYPE,
		ifnull(a.ACCOUNTING_STATUS,'ZHC') as ACCOUNTING_STATUS,
		a.INT_CLASS,
		a.PROFIT_CENTER,
		SUM(ifnull(a.INT_ACCRUED_CTD, 0)) as INT_ACCRUED_CTD,
		a.ACCR_DATE,
		ifnull(a.SOURCE_MODULE, 'RB') as SOURCE_MODULE,
		'NI' as SOURCE_TYPE,
		MAX(a.CLIENT_NO) as CLIENT_NO,
		CONCAT(b.TERM,b.TERM_TYPE) AS TERM
		from
		RB_NOTE_ACCR_HIST a,rb_financial_acct_register b
		where
		a.SEQ_NO BETWEEN #{startKey,jdbcType=DATE} and #{endKey,jdbcType=DATE}
		AND
		a.ACCR_DATE BETWEEN #{lastRunDate} AND #{yesterday}
		AND a.internal_key =b.internal_key
		and  a.client_no =b.client_no
		AND
		a.INT_ACCRUED_CTD <![CDATA[<>]]> 0
<!-- 多法人改造 by luocwa -->
		<if test="company != null and company != '' ">
			AND a.COMPANY = #{company}
		</if>
		group by
		a.TRAN_BRANCH,a.CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER,a.INT_CLASS,a.SOURCE_MODULE,a.ACCR_DATE,CONCAT(b.TERM,b.TERM_TYPE)
	</select>

</mapper>
