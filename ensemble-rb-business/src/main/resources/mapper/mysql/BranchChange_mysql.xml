<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.bc.repository.BranchChange">

    <update id="updateCdCardArchByTranCardBranch" parameterType="java.util.Map" databaseId="mysql">
        UPDATE CD_CARD_ARCH
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_TIME = date_format(#{lastChangeDate},'%Y-%m-%d')
        </if>
        WHERE CARD_NO IN (
        SELECT  CARD_NO FROM RB_BASE_ACCT WHERE INTERNAL_KEY  between #{startKey} and #{endKey}   AND ACCT_BRANCH =#{oldBranch}
        )
    </update>

    <update id="updateProdCdCardArchByTranBranch" parameterType="java.util.Map" databaseId="mysql">
        UPDATE CD_CARD_ARCH
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_TIME = date_format(#{lastChangeDate},'%Y-%m-%d')
        </if>
        WHERE CARD_NO
        IN(
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY =#{internalKey} AND PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch}  and CLIENT_NO = #{clientNo}
        )
    </update>

    <update id="updateProdCdCardArchByTranBranch1" parameterType="java.util.Map" databaseId="mysql">
        UPDATE CD_CARD_ARCH
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_TIME = date_format(#{lastChangeDate},'%Y-%m-%d')
        </if>
        WHERE CARD_NO
        IN(
        SELECT CARD_NO FROM RB_BASE_ACCT WHERE INTERNAL_KEY =#{internalKey} AND PROD_TYPE = #{prodType} AND ACCT_BRANCH =#{oldBranch}  and CLIENT_NO = #{clientNo}
        )
    </update>

    <update id="singleupdateCdCardArchByTranBranch" parameterType="java.util.Map" databaseId="mysql">
        UPDATE CD_CARD_ARCH
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_TIME = date_format(#{lastChangeDate},'%Y-%m-%d')
        </if>
        WHERE CARD_NO
        IN(
        SELECT CARD_NO FROM RB_BASE_ACCT WHERE INTERNAL_KEY =#{internalKey}
        AND ACCT_BRANCH =#{oldBranch}  and CLIENT_NO = #{clientNo}
        )
    </update>


    <update id="singleupdateCdCardArchByTranBranch1" parameterType="java.util.Map" databaseId="mysql">
        UPDATE CD_CARD_ARCH
        SET TRAN_BRANCH = #{newBranch}
        <if test = "lastChangeDate !=null">
            ,LAST_CHANGE_TIME = date_format(#{lastChangeDate},'%Y-%m-%d')
        </if>
        WHERE CARD_NO
        IN(
        SELECT CARD_NO FROM RB_ACCT WHERE INTERNAL_KEY =#{internalKey}
        AND ACCT_BRANCH =#{oldBranch}  and CLIENT_NO = #{clientNo}
        )
    </update>
</mapper>