<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal">
    <delete id="deleteAll" parameterType="java.util.HashMap" databaseId="mysql">
        delete from RB_PROD_ACCT_BAL
        <where>
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND COMPANY = #{company}
            </if>
        </where>
    </delete>
    <insert id="insertExt" parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
        insert into
        RB_PROD_ACCT_BAL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="branch != null ">
                BRANCH,
            </if>
            <if test="ccy != null ">
                CCY,
            </if>
            <if test="prodType != null ">
                PROD_TYPE,
            </if>
            <if test="accountingStatus != null ">
                ACCOUNTING_STATUS,
            </if>
            <if test="amtType != null ">
                AMT_TYPE,
            </if>
            <if test="balance != null ">
                BALANCE,
            </if>
            <if test="lastChangeDate != null ">
                LAST_CHANGE_DATE,
            </if>
            <if test="profitCenter != null ">
                PROFIT_CENTER,
            </if>
            <if test="tranTimestamp != null ">
                TRAN_TIMESTAMP,
            </if>
            <if test="glCode != null ">
                GL_CODE,
            </if>
            <if test="company != null ">
                COMPANY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="branch != null ">
                #{branch},
            </if>
            <if test="ccy != null ">
                #{ccy},
            </if>
            <if test="prodType != null ">
                #{prodType},
            </if>
            <if test="accountingStatus != null ">
                #{accountingStatus},
            </if>
            <if test="amtType != null ">
                #{amtType},
            </if>
            <if test="balance != null ">
                #{balance, jdbcType=DECIMAL},
            </if>
            <if test="lastChangeDate != null ">
                #{lastChangeDate},
            </if>
            <if test="profitCenter != null ">
                #{profitCenter},
            </if>
            <if test="tranTimestamp != null ">
                #{tranTimestamp, jdbcType=DATE},
            </if>
            <if test="glCode != null ">
                #{glCode},
            </if>
            <if test="company != null ">
                #{company},
            </if>
        </trim>
    </insert>

    <select id="sumProdAcctBal" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
        select
        branch,ccy,prod_type,ifnull(ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,amt_type,sum(balance) balance,
        max(last_change_date) last_change_date,max(PROFIT_CENTER) PROFIT_CENTER,max(tran_timestamp)
        tran_timestamp,max(gl_code) gl_code,max(company) company,'RB' as system_id
        from
        RB_PROD_ACCT_BAL
        where
        last_change_date = #{lastRunDate}
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        group by
        branch,ccy,prod_type,ACCOUNTING_STATUS,amt_type,PROFIT_CENTER
    </select>
    <select id="sumAcctBalance" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
        select
        a.ACCT_BRANCH as BRANCH,
        a.ACCT_CCY as CCY,
        a.PROD_TYPE,
        ifnull(ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
        'BAL' as AMT_TYPE,
        ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
        SUM(b.TOTAL_AMOUNT_PREV) as BALANCE,
        'RB' as SYSTEM_ID,
        #{yesterday} as LAST_CHANGE_DATE,
        #{company} as COMPANY,
        #{nodeId} as NODE_ID
        from RB_ACCT a,RB_ACCT_BALANCE b
        where a.INTERNAL_KEY = b.INTERNAL_KEY
        AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
        AND concat(a.ACCT_BRANCH,a.PROD_TYPE,a.ACCT_CCY,ifnull(a.ACCOUNTING_STATUS,'ZHC')) BETWEEN #{startKey} and #{endKey}
        AND a.SOURCE_MODULE != 'CL'
        AND ifnull(b.TOTAL_AMOUNT_PREV,0) <![CDATA[ <> ]]> 0
        AND a.ACCT_REAL_FLAG = 'Y'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND a.COMPANY = #{company}
        </if>
        group by a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER
    </select>
    <select id="sumAcctInt" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
        select
        a.ACCT_BRANCH as BRANCH,
        a.ACCT_CCY as CCY,
        a.PROD_TYPE,
        ifnull(a.ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
        'INT' as AMT_TYPE,
        ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
        SUM(ifnull(b.INT_ACCRUED_PREV, 0)+ifnull(b.INT_ADJ_PREV, 0)-ifnull(b.DISCNT_INT_PREV, 0)+ifnull(c.PAST_INTEREST, 0)) as
        BALANCE,
        'RB' as SYSTEM_ID,
        #{yesterday} as LAST_CHANGE_DATE,
        #{company} as COMPANY,
        #{nodeId} as NODE_ID
        from
        RB_ACCT a
        inner join RB_ACCT_INT_DETAIL b on a.INTERNAL_KEY = b.INTERNAL_KEY
        left join RB_DELAY_PAY_INT c on a.internal_key = c.internal_key
        where concat(a.ACCT_BRANCH,a.PROD_TYPE,a.ACCT_CCY,ifnull(a.ACCOUNTING_STATUS,'ZHC')) BETWEEN #{startKey} and #{endKey}
        AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
        AND ( b.INT_ACCRUED_PREV <![CDATA[ <> ]]> 0 or b.INT_ADJ_PREV <![CDATA[ <> ]]> 0
        or b.DISCNT_INT_PREV <![CDATA[ <> ]]> 0 or c.PAST_INTEREST<![CDATA[ <> ]]> 0 )
        AND a.ACCT_REAL_FLAG = 'Y'
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND a.COMPANY = #{company}
        </if>
        group by
        a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER
    </select>
    <select id="sumAcctBalanceFin" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
        select
        TRAN_BRANCH as BRANCH,
        ACCT_CCY as CCY,
        PROD_TYPE,
        'ZHC' as ACCOUNTING_STATUS,
        'BAL' as AMT_TYPE,
        ifnull(PROFIT_CENTER, '99') as PROFIT_CENTER,
        SUM(TOTAL_AMOUNT_PREV) as BALANCE,
        'RB' as SYSTEM_ID,
        #{yesterday} as LAST_CHANGE_DATE,
        #{company} as COMPANY,
        #{nodeId} as NODE_ID
        from
        RB_FINANCIAL_ACCT_REGISTER
        where
        INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
        AND
        (ACCT_STATUS != 'C' OR (ACCT_STATUS = 'C' AND ACCT_CLOSE_DATE = #{runDate}))
        AND
        ifnull(TOTAL_AMOUNT_PREV, 0) <![CDATA[ <> ]]> 0
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        group by
        TRAN_BRANCH,ACCT_CCY,PROD_TYPE,PROFIT_CENTER
    </select>
    <select id="sumAcctIntFin" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
        select
        TRAN_BRANCH AS BRANCH,
        ACCT_CCY as CCY,
        PROD_TYPE,
        'ZHC' as ACCOUNTING_STATUS,
        INT_CLASS as AMT_TYPE,
        ifnull(PROFIT_CENTER, '99') as PROFIT_CENTER,
        SUM(ifnull(INT_ACCRUED_PREV, 0)+ifnull(INT_ADJ_PREV, 0)) as BALANCE,
        'RB' as SYSTEM_ID,
        #{yesterday} as LAST_CHANGE_DATE,
        #{company} as COMPANY,
        #{nodeId} as NODE_ID
        from
        RB_FINANCIAL_ACCT_REGISTER
        where
        INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
        AND
        (ACCT_STATUS != 'C' OR (ACCT_STATUS = 'C' AND ACCT_CLOSE_DATE = #{runDate}))
        AND
        ifnull(INT_ACCRUED_PREV, 0)+ifnull(INT_ADJ_PREV, 0) <![CDATA[ <> ]]> 0
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        group by
        TRAN_BRANCH,ACCT_CCY,PROD_TYPE,PROFIT_CENTER,INT_CLASS
    </select>
    <update id="updateProdAcctBalDealFlag" parameterType="java.util.Map" databaseId="mysql">
        UPDATE RB_PROD_ACCT_BAL
        SET DEAL_FLAG = '1'
        WHERE last_change_date = #{lastRunDate,jdbcType=DATE}
        AND DEAL_FLAG = '2'
    </update>
    <select id="selectOneProdAcctBalSum" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
        select a.BRANCH, a.CCY, a.PROD_TYPE, ifnull(a.ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
        sum(a.balance) balance,a.AMT_TYPE,a.LAST_CHANGE_DATE
        from rb_prod_acct_bal a
        where a.BRANCH = #{branch}
        and a.CCY = #{ccy}
        and a.PROD_TYPE = #{prodType}
        and a.ACCOUNTING_STATUS = #{accountingStatus}
        and a.AMT_TYPE = #{amtType}
        and a.LAST_CHANGE_DATE = #{lastChangeDate}
        group by a.BRANCH, a.CCY, a.PROD_TYPE, a.ACCOUNTING_STATUS, a.AMT_TYPE,a.LAST_CHANGE_DATE
    </select>

    <select id="sumAcctBalanceNew" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
            select
            SUM(v.TOTAL_AMOUNT_PREV)as BALANCE ,
            'BAL' as AMT_TYPE,
            'RB' as SYSTEM_ID,
            v.BRANCH,
            v.CCY,
            v.prod_type,
            v.ACCOUNTING_STATUS,
            v.PROFIT_CENTER,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID,
            #{yesterday} as LAST_CHANGE_DATE
            from (
            select
            a.ACCT_BRANCH as BRANCH,
            a.ACCT_CCY as CCY,
            <!-- Product changes for the day require the use of old products for balance summary -->
            if(a.amend_date = #{runDate},a.old_prod_type,a.prod_type) PROD_TYPE,
            ifnull(ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            'BAL' as AMT_TYPE,
            ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
            b.TOTAL_AMOUNT_PREV,
            'RB' as SYSTEM_ID,
            #{yesterday} as LAST_CHANGE_DATE,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID
            from RB_ACCT a,RB_ACCT_BALANCE b
            where a.INTERNAL_KEY = b.INTERNAL_KEY
            AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
            AND concat(a.ACCT_BRANCH,if(a.amend_date = #{runDate},a.old_prod_type,a.prod_type),a.ACCT_CCY,ifnull(a.ACCOUNTING_STATUS,'ZHC')) BETWEEN #{startKey} and #{endKey}
            AND a.SOURCE_MODULE != 'CL'
            AND ifnull(b.TOTAL_AMOUNT_PREV,0) <![CDATA[ <> ]]> 0
            AND a.ACCT_REAL_FLAG = 'Y'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND a.COMPANY = #{company}
            </if>
            ) v
            group by v.BRANCH,v.CCY,v.prod_type,v.ACCOUNTING_STATUS,v.PROFIT_CENTER
    </select>

    <select id="sumAcctIntNew" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
            select
            SUM(ifnull(v.INT_ACCRUED_PREV, 0)+ifnull(v.INT_ADJ_PREV, 0)-ifnull(v.DISCNT_INT_PREV, 0)+ifnull(v.PAST_INTEREST, 0)) as BALANCE,
            'INT' as AMT_TYPE,
            'RB' as SYSTEM_ID,
            v.BRANCH,
            v.CCY,
            v.prod_type,
            v.ACCOUNTING_STATUS,
            v.PROFIT_CENTER,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID,
            #{yesterday} as LAST_CHANGE_DATE
            from (
            select
            a.ACCT_BRANCH as BRANCH,
            a.ACCT_CCY as CCY,
            <!-- Product changes on the day and old products are used to summarize -->
            if(a.amend_date = #{runDate},a.old_prod_type,a.prod_type) PROD_TYPE,
            ifnull(a.ACCOUNTING_STATUS, 'ZHC') as ACCOUNTING_STATUS,
            ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
            b.INT_ACCRUED_PREV,
            b.INT_ADJ_PREV,
            ifnull(b.DISCNT_INT_PREV, 0) as DISCNT_INT_PREV,
            ifnull(c.PAST_INTEREST, 0) as PAST_INTEREST,
            'RB' as SYSTEM_ID,
            #{yesterday} as LAST_CHANGE_DATE,
            #{company} as COMPANY,
            #{nodeId} as NODE_ID
            from
            RB_ACCT a
            inner join RB_ACCT_INT_DETAIL b on a.INTERNAL_KEY = b.INTERNAL_KEY
            left join RB_DELAY_PAY_INT c on a.internal_key = c.internal_key
            where concat(a.ACCT_BRANCH,if(a.amend_date = #{runDate},a.old_prod_type,a.prod_type),a.ACCT_CCY,ifnull(a.ACCOUNTING_STATUS,'ZHC')) BETWEEN #{startKey} and #{endKey}
            AND (a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
            AND ( b.INT_ACCRUED_PREV <![CDATA[ <> ]]> 0 or b.INT_ADJ_PREV <![CDATA[ <> ]]> 0
            or b.DISCNT_INT_PREV <![CDATA[ <> ]]> 0 or c.PAST_INTEREST<![CDATA[ <> ]]> 0 )
            AND a.ACCT_REAL_FLAG = 'Y'
            <!-- 多法人改造 by luocwa -->
            <if test="company != null and company != '' ">
                AND a.COMPANY = #{company}
            </if>
            ) v
            group by
            v.BRANCH,v.CCY,v.prod_type,v.ACCOUNTING_STATUS,v.PROFIT_CENTER
    </select>

</mapper>
