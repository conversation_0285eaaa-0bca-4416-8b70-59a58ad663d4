<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal">
	<select id="sumAcctBalance" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
			select
			a.ACCT_BRANCH as BRANCH,
			a.ACCT_CCY as CCY,
			a.PROD_TYPE,
			a.ACCOUNTING_STATUS,
			'BAL' as AMT_TYPE,
			ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
			SUM(b.TOTAL_AMOUNT_PREV) as BALANC<PERSON>,
			'RB' as SYSTEM_ID,
			#{yesterday} as LAST_CHANGE_DATE,
			#{company} as COMPANY,
			#{nodeId} as NODE_ID
			from
			RB_ACCT a,RB_ACCT_BALANCE b
			where
			a.INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
			AND
			(a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
			AND a.INTERNAL_KEY = b.INTERNAL_KEY
			AND a.SOURCE_MODULE != 'CL'
			AND ifnull(b.TOTAL_AMOUNT_PREV,0) <![CDATA[ <> ]]> 0
			AND a.ACCT_REAL_FLAG = 'Y'
			<!--多法人改造 by luocwa  -->
			<if test="company != null and company != '' ">
				AND a.COMPANY = #{company}
			</if>
			group by
			a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER
	</select>

	<select id="sumAcctInt" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
			select
			a.ACCT_BRANCH as BRANCH,
			a.ACCT_CCY as CCY,
			a.PROD_TYPE,
			a.ACCOUNTING_STATUS,
			b.INT_CLASS as AMT_TYPE,
			ifnull(a.PROFIT_CENTER, '99') as PROFIT_CENTER,
			SUM(ifnull(b.INT_ACCRUED_PREV, 0)+ifnull(b.INT_ADJ_PREV, 0)-ifnull(b.DISCNT_INT_PREV, 0)+ifnull(c.PAST_INTEREST, 0)) as BALANCE,
			'RB' as SYSTEM_ID,
			#{yesterday} as LAST_CHANGE_DATE,
			#{company} as COMPANY,
			#{nodeId} as NODE_ID
			from
			RB_ACCT a
			inner join RB_ACCT_INT_DETAIL b on a.INTERNAL_KEY = b.INTERNAL_KEY
			left join RB_DELAY_PAY_INT c on a.internal_key = c.internal_key
			where
			a.INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
			AND
			(a.ACCT_STATUS != 'C' OR (a.ACCT_STATUS = 'C' AND a.ACCT_CLOSE_DATE = #{runDate}))
			AND
			ifnull(b.INT_ACCRUED_PREV, 0)+ifnull(b.INT_ADJ_PREV, 0)-ifnull(b.DISCNT_INT_PREV, 0)+ifnull(c.PAST_INTEREST,0) <![CDATA[ <> ]]> 0
			AND
			a.ACCT_REAL_FLAG = 'Y'
			<!--多法人改造 by luocwa  -->
			<if test="company != null and company != '' ">
				AND a.COMPANY = #{company}
			</if>
			group by
			a.ACCT_BRANCH,a.ACCT_CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER,b.INT_CLASS
	</select>

	<select id="sumAcctBalanceFin" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
			select
			TRAN_BRANCH as BRANCH,
			ACCT_CCY as CCY,
			PROD_TYPE,
			'ZHC' as ACCOUNTING_STATUS,
			'BAL' as AMT_TYPE,
			ifnull(PROFIT_CENTER, '99') as PROFIT_CENTER,
			SUM(TOTAL_AMOUNT_PREV) as BALANCE,
			'RB' as SYSTEM_ID,
			#{yesterday} as LAST_CHANGE_DATE,
			#{company} as COMPANY,
			#{nodeId} as NODE_ID
			from
			RB_FINANCIAL_ACCT_REGISTER
			where
			INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
			AND
			(ACCT_STATUS != 'C' OR (ACCT_STATUS = 'C' AND ACCT_CLOSE_DATE = #{runDate}))
			AND
			ifnull(TOTAL_AMOUNT_PREV, 0) <![CDATA[ <> ]]> 0
			<!--多法人改造 by luocwa  -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			group by
			TRAN_BRANCH,ACCT_CCY,PROD_TYPE,PROFIT_CENTER
	</select>

	<select id="sumAcctIntFin" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal" databaseId="mysql">
			select
			TRAN_BRANCH AS BRANCH,
			ACCT_CCY as CCY,
			PROD_TYPE,
			'ZHC' as ACCOUNTING_STATUS,
			INT_CLASS as AMT_TYPE,
			ifnull(PROFIT_CENTER, '99') as PROFIT_CENTER,
			SUM(ifnull(INT_ACCRUED_PREV, 0)+ifnull(INT_ADJ_PREV, 0)) as BALANCE,
			'RB' as SYSTEM_ID,
			#{yesterday} as LAST_CHANGE_DATE,
			#{company} as COMPANY,
			#{nodeId} as NODE_ID
			from
			RB_FINANCIAL_ACCT_REGISTER
			where
			INTERNAL_KEY BETWEEN #{startKey} and #{endKey}
			AND
			(ACCT_STATUS != 'C' OR (ACCT_STATUS = 'C' AND ACCT_CLOSE_DATE = #{runDate}))
			AND
			ifnull(INT_ACCRUED_PREV, 0)+ifnull(INT_ADJ_PREV, 0) <![CDATA[ <> ]]> 0
			<!--多法人改造 by luocwa  -->
			<if test="company != null and company != '' ">
				AND COMPANY = #{company}
			</if>
			group by
			TRAN_BRANCH,ACCT_CCY,PROD_TYPE,PROFIT_CENTER,INT_CLASS
	</select>
</mapper>
