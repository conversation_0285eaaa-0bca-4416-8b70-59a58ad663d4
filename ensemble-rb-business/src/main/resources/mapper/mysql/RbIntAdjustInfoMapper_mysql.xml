<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntAdjustInfo">

    <select id="getNewAdjustInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbIntAdjustInfo" databaseId="mysql">
        select
        a.*
        from (
        select
        <include refid="Base_Column" />
        from
        <include refid="Table_Name" />
        where 1=1
        <if test="internalKey != null ">
            AND INTERNAL_KEY = #{internalKey}
        </if>
        <if test="clientNo != null ">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        order by TRAN_TIMESTAMP desc
        ) a limit 1
    </select>
</mapper>
