<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist">

    <!--获取rbGlHist流水-->
    <select id="getRbGLHistByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist" databaseId="mysql">
        SELECT
        GL_SEQ_NO,
        CHANNEL_SEQ_NO,
        INTERNAL_KEY,
        PROD_TYPE,
        BASE_ACCT_NO,
        ACCT_CCY,
        ACCT_SEQ_NO,
        ACCT_BRANCH,
        CLIENT_NO,
        CLIENT_TYPE,
        CHANNEL_DATE,
        EFFECT_DATE,
        TRAN_DATE,
        TRAN_BRANCH,
        TRAN_TYPE,
        CR_DR_IND,
        if(REVERSAL_FLAG = 'Y',(CASE EVENT_TYPE WHEN 'CRET' THEN 'DEBT' WHEN 'DEBT' THEN 'CRET' ELSE EVENT_TYPE end),EVENT_TYPE) EVENT_TYPE,
        CCY,
        GL_CODE,
        MARKETING_PROD,
        MARKETING_PROD_DESC,
        FLAT_RATE,
        SUB_SEQ_NO,
        REFERENCE,
        (case EVENT_TYPE
        when 'ACR' then
        if(REVERSAL_FLAG =  'Y', AMOUNT * (-1), AMOUNT)
        else
        AMOUNT
        end) AMOUNT ,
        AMT_TYPE,
        INT_AMT,
        ODI_AMT,
        PRI_AMT,
        TAX_AMT,
        CONTRA_EQUIV_AMT,
        ODP_AMT,
        CROSS_RATE,
        SPREAD_PERCENT,
        GL_POSTED_FLAG,
        IN_STATUS,
        REVERSAL_FLAG,
        ACCOUNTING_STATUS,
        BANK_SEQ_NO,
        BUSINESS_UNIT,
        TRAN_CATEGORY,
        TRAN_PROFIT_CENTER,
        PROFIT_CENTER,
        SEND_SYSTEM,
        SOURCE_TYPE,
        SOURCE_MODULE,
        SYSTEM_ID,
        UN_REAL,
        RESERVE1,
        NARRATIVE,
        TRAN_TIMESTAMP,
        COMPANY,
        USER_ID,
        BUS_SEQ_NO,
        INT_IND_FLAG,
        AMOUNT_NATURE,
        LOAN_PROD_TYPE,
        OLD_BRANCH
        FROM
        RB_GL_HIST
        where
        (GL_POSTED_FLAG IS NULL OR GL_POSTED_FLAG='N')
        AND CHANNEL_SEQ_NO IN
        <foreach collection="seqNos" item="seqNo" index="index" open="(" close=")" separator=",">
            #{seqNo}
        </foreach>
        AND substring_index(SUB_SEQ_NO,'_',1) IN
        <foreach collection="subSeqNos" item="subSeqNo" index="index" open="(" close=")" separator=",">
            #{subSeqNo}
        </foreach>
    </select>

    <select id="selectAmountGroupBy" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist" databaseId="mysql">
            select
            <if test="cometSqlType == 'segment'">DISTINCT ${cometKeyField} as KEY_FIELD</if>
            <if test="cometSqlType != 'segment'">
                #{yesterday} tran_date,acct_branch,prod_type,ccy,company,internal_key,accounting_status,amt_type,
                sum(if(balance_change_type = '-',-1*ifnull(amount,0),ifnull(amount,0))) amount
            </if>
            from rb_gl_hist
            where ifnull(deal_flag,'1') = '1'
            and tran_date between #{lastRunDate} and #{yesterday}
            and gl_code is null and prod_type is not null and amount <![CDATA[ <> ]]> 0
            and internal_key in
            <foreach collection="internalKeys" index="index" item="internalKey" open="(" close=")" separator=",">
                #{internalKey}
            </foreach>
            group by #{yesterday},acct_branch,prod_type,ccy,company,internal_key,accounting_status,amt_type
    </select>

    <select id="selectAmountGroupByRbGlHist" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist" databaseId="mysql">
            select #{yesterday} tran_date,acct_branch,prod_type,ccy,ifnull(accounting_status,'ZHC'),amt_type,
            sum(if(balance_change_type = '-',-1*ifnull(amount,0),ifnull(amount,0))) amount
            from (
            select acct_branch,prod_type,ccy,accounting_status, if(amt_type = 'TAX','BAL',amt_type) amt_type,balance_change_type,amount
            from rb_gl_hist
            where acct_branch = #{acctBranch}
            and prod_type = #{prodType}
            and ccy = #{ccy}
            and tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
            and tran_date <![CDATA[ <= ]]> #{yesterday,jdbcType=DATE}
            and ifnull(deal_flag,'1') = '1'
            and gl_code is null
            and amount <![CDATA[ <> ]]> 0
            <!--机构变更只有一条流水，需要补旧机构流水-->
            union all
            <!--机构变更新机构余额方向是+，旧机构余额方向是-，要取反-->
            select old_branch acct_branch,prod_type,ccy,accounting_status, amt_type,
            (case balance_change_type
            when '-' then '+'
            when '+' then '-'
            END
            )
            balance_change_type,
            amount
            from rb_gl_hist
            where old_branch = #{acctBranch}
            and prod_type = #{prodType}
            and ccy = #{ccy}
            and TRAN_TYPE = 'TRFB'
            and tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
            and tran_date <![CDATA[ <= ]]> #{yesterday,jdbcType=DATE}
            <!--        and ifnull(deal_flag,'1') = '1'-->
            and gl_code is null
            and amount <![CDATA[ <> ]]> 0
            <!--需要并上结息只有单条BAL类型的流水，同时不能与第一段sql有重复，并特殊处理冲正的状况-->
            union all
            select acct_branch,prod_type,ccy,accounting_status,'INT' amt_type,if(REVERSAL_FLAG = 'N','-','+') balance_change_type,amount
            from rb_gl_hist t
            where exists
            <!--剔除与第一段重复的数据-->
            (select * from rb_gl_hist b
            where t.gl_seq_no = b.gl_seq_no
            and b.event_type = 'CYCLE' AND  amt_type<![CDATA[ <> ]]>'INT'
            and b.tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
            and b.tran_date <![CDATA[ <= ]]> #{yesterday,jdbcType=DATE}
            )
            and acct_branch = #{acctBranch}
            and prod_type = #{prodType}
            and ccy = #{ccy}
            and tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
            and tran_date <![CDATA[ <= ]]> #{yesterday,jdbcType=DATE}
            and gl_code is null
            and amount <![CDATA[ <> ]]> 0
            <!--需要并上结息只有单条INT类型的流水，同时不能与第一段sql有重复，并特殊处理冲正的状况-->
            union all
            select acct_branch,prod_type,ccy,accounting_status,'BAL' amt_type,if(REVERSAL_FLAG = 'N','+','-') balance_change_type,amount
            from rb_gl_hist t
            where exists
            (select * from rb_gl_hist b
            where t.gl_seq_no = b.gl_seq_no
            and b.event_type = 'CYCLE' AND  amt_type<![CDATA[ <> ]]>'BAL'
            and b.tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
            and b.tran_date <![CDATA[ <= ]]> #{yesterday,jdbcType=DATE}
            )
            and acct_branch = #{acctBranch}
            and prod_type = #{prodType}
            and ccy = #{ccy}
            and tran_date <![CDATA[ >= ]]> #{lastRunDate,jdbcType=DATE}
            and tran_date <![CDATA[ <= ]]> #{yesterday,jdbcType=DATE}
            and gl_code is null
            and amount <![CDATA[ <> ]]> 0
            )
            where
            amt_type = #{amtType}
            group by #{yesterday},acct_branch,prod_type,ccy,ifnull(accounting_status,'ZHC'),amt_type

    </select>

    <select id="selectSumAmountByInternalKey" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist" databaseId="mysql">
            select sum(if(balance_change_type = '-',-1*ifnull(amount,0),ifnull(amount,0))) amount,amt_type ,internal_key
            from (
            select balance_change_type,amt_type,amount,internal_key from rb_gl_hist
            where tran_date <![CDATA[ >= ]]> #{lastRunDate}
            and tran_date <![CDATA[ <= ]]>#{yesterday}
            and  internal_key in
            <foreach item="item" index="index" collection="internalKeys" open="(" separator="," close=")">
                #{item}
            </foreach>
            and gl_code is null
            and abs(amount) > 0
            union all
            select '-' balance_change_type,'INT' amt_type,amount,internal_key from rb_gl_hist
            where tran_date <![CDATA[ >= ]]> #{lastRunDate}
            and tran_date <![CDATA[ <= ]]>#{yesterday}
            and internal_key in
            <foreach item="item" index="index" collection="internalKeys" open="(" separator="," close=")">
                #{item}
            </foreach>
            and event_type='CYCLE'
            and gl_code is null
            and abs(amount) > 0
            )
            group by amt_type ,internal_key
    </select>

    <update id="updateDealFlagByGlHist" parameterType="java.util.Map" databaseId="mysql">
            update RB_GL_HIST
            SET deal_flag = #{dealFlag}
            WHERE tran_date between #{lastRunDate} and #{yesterday}
            and ifnull(deal_flag,'0') <![CDATA[ <> ]]> #{dealFlag}
            and ifnull(deal_flag,'0')  <![CDATA[ <> ]]> '3'
            <if test="acctBranch != null and acctBranch != ''">
                AND acct_branch = #{acctBranch}
            </if>
            <if test="prodType != null and prodType != ''">
                AND prod_type = #{prodType}
            </if>
            <if test="ccy != null and ccy.length() > 0">
                AND ccy = #{ccy}
            </if>
            <if test="accountingStatus != null and accountingStatus != ''">
                AND ifnull(accounting_status,'ZHC') = #{accountingStatus}
            </if>
            <if test="amtType != null and amtType != ''">
                AND if(amt_type = 'TAX','BAL',amt_type) = #{amtType}
            </if>
            <if test="company != null and company != ''">
                AND COMPANY = #{company}
            </if>
            <if test="clientNo != null and clientNo != '' ">
                AND CLIENT_NO = #{clientNo}
            </if>
    </update>

</mapper>