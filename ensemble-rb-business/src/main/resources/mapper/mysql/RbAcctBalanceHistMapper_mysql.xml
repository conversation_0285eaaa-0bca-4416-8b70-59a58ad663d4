<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist">

    <select id="selectBalanceHistByTranDate"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist" flushCache="true"
            useCache="false" databaseId="mysql">
        select
        <include refid="Base_Column"/>
        from
        <include refid="Table_Name"/>
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <if test="tranDate != null ">
            AND date_format(TRAN_DATE,'%Y-%m-%d') = #{tranDate}
        </if>
    </select>

    <select id="getRbAcctBalanceHist" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist"
            parameterType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist" flushCache="true"
            useCache="false" databaseId="mysql">
        select
        <include refid="Base_Column"/>
        from
        <include refid="Table_Name"/>
        where INTERNAL_KEY = #{internalKey}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="clientNo != null">
            AND CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <if test="startDate != null  and endDate != null ">
            <![CDATA[
			AND #{startDate} <= date_format(TRAN_DATE,'%Y-%m-%d') AND date_format(TRAN_DATE,'%Y-%m-%d') <= #{endDate}
                ]]>
        </if>
        order by tran_date ASC
    </select>

</mapper>
