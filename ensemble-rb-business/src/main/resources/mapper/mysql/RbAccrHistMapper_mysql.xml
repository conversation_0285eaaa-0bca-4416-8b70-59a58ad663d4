<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist">
	<select id="sumAcctIntMerge" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrHist" databaseId="mysql">
		select
		a.TRAN_BRANCH,
		a.CCY,
		a.PROD_TYPE,
		a.ACCOUNTING_STATUS,
		a.INT_CLASS,
		a.PROFIT_CENTER,
		CONCAT(b.TERM,b.TERM_TYPE) AS TERM,
		SUM(ifnull(a.INT_ACCRUED_CTD, 0)) as INT_ACCRUED_CTD,
		a.ACCR_DATE,
		a.SOURCE_MODULE,
		'NI' as SOURCE_TYPE,
		'RB' as SYSTEM_ID,
		MAX(a.CLIENT_NO) as CLIENT_NO,
		a.OTH_REFERENCE
		from
		RB_ACCR_HIST a,RB_ACCT b
		where
		a.IRL_SEQ_NO BETWEEN #{startKey} and #{endKey}
		AND
		a.GL_MERGE_TYPE_FLAG = 'Y'
        <!--多法人改造 by LIYUANV-->
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		AND
		a.INTERNAL_KEY = b.INTERNAL_KEY
		AND
		b.ACCT_REAL_FLAG = 'Y'
		AND
		a.ACCR_DATE BETWEEN #{lastRunDate,jdbcType=DATE} AND #{yesterday,jdbcType=DATE}
		AND
		(a.INT_ACCRUED_CTD <![CDATA[<>]]> 0 OR a.TAX_ACCRUED_CTD <![CDATA[<>]]> 0)
		group by
		CONCAT(b.TERM,b.TERM_TYPE),a.TRAN_BRANCH,a.CCY,a.PROD_TYPE,a.ACCOUNTING_STATUS,a.PROFIT_CENTER,a.INT_CLASS,a.OTH_REFERENCE,a.SOURCE_MODULE,a.ACCR_DATE
	</select>
</mapper>
