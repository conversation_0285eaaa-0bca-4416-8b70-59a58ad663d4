<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister">
    <select id="getLastEventRegister" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister" databaseId="mysql">
        SELECT
        *
        FROM
        (
        SELECT * FROM
        RB_ACCT_EVENT_REGISTER
        <where>
            <if test="internalKey != null and internalKey != '' ">
                AND  INTERNAL_KEY = #{internalKey}
            </if>
            <if test="intClass != null and intClass != ''">
                AND INT_CLASS = #{intClass}
            </if>
        </where>
        order by TRAN_TIMESTAMP desc
        ) AS t
        limit 1
    </select>

    <select id="getRbAcctCycleHist" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister" databaseId="mysql">
        SELECT <include refid="Base_Column"/>
        FROM RB_ACCT_EVENT_REGISTER
        WHERE INTERNAL_KEY = #{internalKey}
        <if test="clientNo != null and  clientNo != '' ">
            AND CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        <if test="userId != null and userId.length() > 0">
            AND USER_ID = #{userId}
        </if>
        <if test="captDate != null ">
            AND date_format(TRAN_DATE,'%Y-%m-%d') = #{captDate}
        </if>
        AND (TRAN_STATUS IS NULL OR TRAN_STATUS != 'R')
        order by tran_date desc
    </select>

    <select id="getListRefence" parameterType="java.util.HashMap" resultType="java.lang.String" databaseId="mysql">
        select REFERENCE
        from RB_ACCT_EVENT_REGISTER
        where CLIENT_NO = #{clientNo}
        <!--多法人改造 by LIYUANV-->
        <if test="companyList != null">
            AND COMPANY in
            <foreach collection="companyList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null and startDate != '' ">
            AND date_format(TRAN_DATE,'%Y-%m-%d') >= date_format(#{startDate},'%Y-%m-%d')
        </if>
        <if test="endDate != null and endDate != '' ">
            AND date_format(TRAN_DATE,'%Y-%m-%d') <![CDATA[ <= date_format(#{endDate},'%Y-%m-%d')]]>
        </if>
<!--        and date_format(TRAN_DATE,'%Y-%m-%d') between #{startDate} and  #{endDate}-->
    </select>

    <select id="getZxqylist" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister" databaseId="mysql">
        SELECT <include refid="Base_Column"/>
        FROM RB_ACCT_EVENT_REGISTER
        WHERE BASE_ACCT_NO = #{actualBaseAcctNo}
        and PROD_TYPE in ('41020','12014','12015')
        and  date_format(TRAN_DATE,'%Y-%m-%d') between #{startDate} and  #{endDate}
        <!-- 多法人改造 by LIYUANV -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
