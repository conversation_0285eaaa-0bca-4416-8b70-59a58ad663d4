<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHistPbk">

    <select id="getPrintTranHistInfoOnly999" parameterType="java.util.Map" resultMap="MbTranHistPbkMap" databaseId="mysql">
        select  t.*
        from (
        select
        *
        from RB_tran_hist_pbk
        <where>
            <trim suffixOverrides="AND">
                <if test="acctType != null and acctType == 'C'.toString()">
                    INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo}
                    <if test="closeFlag == null or closeFlag == ''">
                        and acct_status != 'C'
                    </if>
                    ) AND
                </if>
                <if test="acctType != null and acctType == 'T'.toString()">
                    INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo})
                    AND EVENT_TYPE != 'CYCLE' AND
                </if>
                <if test="pbkUdpFlag != null and pbkUdpFlag != ''">
                    PBK_UPD_FLAG = #{pbkUdpFlag} AND
                </if>
                <if test="clientNo != null and clientNo != ''">
                    CLIENT_NO=#{clientNo} AND
                </if>
                <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
                    TRAN_STATUS != 'W' AND
                </if>
            </trim>
        </where>
        order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO asc) t
        limit  1000
    </select>

    <select id="getPrintTranHistInfoOnly999ForOth" parameterType="java.util.Map" resultMap="MbTranHistPbkMap" databaseId="mysql">
        select
        <include refid="Base_Column"/>
        from RB_tran_hist_pbk b
        where b.seq_no not in
        (select seq_no
        from (
        select  t.seq_no
        from (
        select
        *
        from RB_tran_hist_pbk
        <where>
            <trim suffixOverrides="AND">
                <if test="acctType != null and acctType == 'C'.toString()">
                    INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo}
                    <if test="closeFlag == null or closeFlag == ''">
                        and acct_status != 'C'
                    </if>
                    ) AND
                </if>
                <if test="acctType != null and acctType == 'T'.toString()">
                    INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo})
                    AND EVENT_TYPE != 'CYCLE' AND
                </if>
                <if test="pbkUdpFlag != null and pbkUdpFlag != ''">
                    PBK_UPD_FLAG = #{pbkUdpFlag} AND
                </if>
                <if test="clientNo != null and clientNo != ''">
                    CLIENT_NO=#{clientNo} AND
                </if>
                <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
                    TRAN_STATUS != 'W' AND
                </if>
            </trim>
        </where>
        order by TRAN_DATE asc,TRAN_TIMESTAMP asc,SEQ_NO asc) t
        where
           <trim suffixOverrides="AND">
                <if test="acctType != null and acctType == 'C'.toString()">
                    and  INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo}
                    <if test="closeFlag == null or closeFlag == ''">
                        and acct_status != 'C'
                    </if>
                    ) AND
                </if>
                <if test="acctType != null and acctType == 'T'.toString()">
                    INTERNAL_KEY IN (SELECT INTERNAL_KEY FROM RB_ACCT WHERE base_acct_no =#{baseAcctNo})
                    AND EVENT_TYPE != 'CYCLE' AND
                </if>
                <if test="pbkUdpFlag != null and pbkUdpFlag != ''">
                    PBK_UPD_FLAG = #{pbkUdpFlag} AND
                </if>
                <if test="clientNo != null and clientNo != ''">
                    CLIENT_NO=#{clientNo} AND
                </if>
                <if test="eraseAccountPrintFlag != null and eraseAccountPrintFlag == 'N'.toString()">
                    TRAN_STATUS != 'W' AND
                </if>
            </trim>
          limit 300
    </select>
</mapper>
