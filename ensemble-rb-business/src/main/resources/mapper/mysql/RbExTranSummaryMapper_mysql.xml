<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary">
  <!--汇总查询未平盘的结售汇记录，进行汇总模式下的系统内平盘-->
  <select id="getIBUncEntriesForSumUnc" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary" databaseId="mysql">
    SELECT
    ifnull(REVERSAL_DATE, TRAN_DATE) AS TRAN_DATE,
    TRAN_BRANCH AS BRANCH,
    SELL_BUY_IND,
    if(REVERSAL_DATE, 'Y', 'N') AS REVERSAL,
    SELL_CCY,
    BUY_CCY,
    if(SELL_BUY_IND='E', TRAN_TYPE, '') AS TRAN_TYPE,
    ifnull(UNC_STATUS, 'N') AS STATUS,
    SUM(BUY_AMOUNT) AS AMT_BUY,
    SUM(SELL_AMOUNT) AS AMT_SELL ,
    COUNT(1) AS CTR_TRAN,
    #{nodeId} AS NODE_ID,
    sum(ifnull(FCY_CTRL_IBUNC_AMT, 0)) AS UNC_LCY_AMT
    FROM
    RB_EXCHANGE_TRAN_HIST
    WHERE (unc_status in ('N', 'D') OR unc_status IS NULL)
    AND ((REVERSAL_DATE IS NULL AND TRAN_DATE BETWEEN #{lastRunDate,jdbcType=DATE} AND #{yesterday,jdbcType=DATE})
    OR (REVERSAL_DATE IS NOT NULL AND REVERSAL_DATE != TRAN_DATE AND REVERSAL_DATE BETWEEN #{lastRunDate,jdbcType=DATE} AND #{yesterday,jdbcType=DATE}))
    AND seq_no BETWEEN #{startKey} AND #{endKey}
    GROUP BY ifnull(REVERSAL_DATE, TRAN_DATE),
    TRAN_BRANCH,
    SELL_BUY_IND,
    if(REVERSAL_DATE, 'Y', 'N'),
    SELL_CCY,
    BUY_CCY,
    if(SELL_BUY_IND='E', TRAN_TYPE, ''),
    ifnull(UNC_STATUS, 'N')
  </select>

  <!--查询垂直库汇总表未处理的记录进行二次汇总 for 市场平盘-->
  <select id="getBranchSumEntryForSumObunc" parameterType="java.util.Map"  resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExTranSummary" databaseId="mysql">
    SELECT TRAN_DATE,
    BRANCH,
    if(SELL_BUY_IND= 'B', BUY_CCY, SELL_CCY) AS BUY_CCY,
    SUM(if(SELL_BUY_IND= 'B', if(REVERSAL= 'N', AMT_BUY, -1*AMT_BUY), if(REVERSAL= 'N', -1*AMT_SELL, AMT_SELL))) AS AMT_BUY,
    SUM(if(SELL_BUY_IND= 'B', if(REVERSAL= 'N', -1*UNC_LCY_AMT, UNC_LCY_AMT), if(REVERSAL= 'N', UNC_LCY_AMT, -1*UNC_LCY_AMT))) AS AMT_SELL
    FROM RB_EX_TRAN_SUMMARY
    WHERE STATUS IN ('D', 'S')
    AND SELL_BUY_IND != 'E'
    AND TRAN_DATE between #{lastRunDate,jdbcType=DATE} and #{yesterday,jdbcType=DATE}
    <if test="company != null and company != '' ">
      AND a.COMPANY = #{company}
    </if>
    GROUP BY TRAN_DATE, BRANCH,  if(SELL_BUY_IND= 'B', BUY_CCY, SELL_CCY)
  </select>
</mapper>
