<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.TbGlHist">

    <!--获取obGLHist-->
    <select id="getObGLHistByReference" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.TbGlHist" databaseId="mysql">
        SELECT
        <include refid="Base_Column"/>
        FROM
        <include refid="Table_Name"/>
        where
        (GL_POSTED_FLAG IS NULL OR GL_POSTED_FLAG='N')
        AND CHANNEL_SEQ_NO IN
        <foreach collection="seqNos" item="seqNo" index="index" open="(" close=")" separator=",">
            #{seqNo}
        </foreach>
        AND substring_index(SUB_SEQ_NO,'_',1) IN
        <foreach collection="subSeqNos" item="subSeqNo" index="index" open="(" close=")" separator=",">
            #{subSeqNo}
        </foreach>
    </select>
</mapper>