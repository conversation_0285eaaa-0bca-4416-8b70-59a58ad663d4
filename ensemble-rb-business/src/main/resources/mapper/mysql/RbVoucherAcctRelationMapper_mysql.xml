<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation">

    <select id="getMbVoucherAcctRelationByAcctNoVouch" parameterType="java.util.Map"
            resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation" databaseId="mysql">
        SELECT
        <include refid="Base_Column"/>
        FROM RB_VOUCHER_ACCT_RELATION
        WHERE VOUCHER_STATUS NOT IN ('CAN', 'USE','POB')
        <if test="baseAcctNo != null and baseAcctNo !=''">
            and BASE_ACCT_NO = #{baseAcctNo}
        </if>
        <if test="acctCcy != null and acctCcy !=''">
            and ACCT_CCY = #{acctCcy}
        </if>
        <if test="prodType != null and prodType !=''">
            and PROD_TYPE = #{prodType}
        </if>
        <if test="acctSeqNo != null and acctSeqNo !=''">
            and ACCT_SEQ_NO = #{acctSeqNo}
        </if>
        <if test="docType != null and docType !=''">
            and DOC_TYPE = #{docType}
        </if>
        <if test="voucherNo != null and voucherNo !=''">
            and VOUCHER_NO = #{voucherNo}
        </if>
        <if test="prefix != null and prefix != ''">
            and prefix = #{prefix}
        </if>
        <if test="isCard =='true'">
            and CARD_NO IS NOT NULL and CARD_NO != ''
        </if>
        <if test="clientNo != null and clientNo !=''">
            and CLIENT_NO = #{clientNo}
        </if>
        <!-- 多法人改造 by luocwa -->
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
</mapper>
