<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance">
	<select id="getTranSumAmtInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalanceTemp" databaseId="mysql">
		select b.ACCT_BRANCH    as ACCT_BRANCH,
			   b.ACCT_CCY       as ACCT_CCY,
			   b.PROD_TYPE,
			   b.ACCOUNTING_STATUS,
			   b.amt_type       as AMT_TYPE,
			   sum(
					   case
						   when b.amt_type = 'BAL'
							   then (case when b.BALANCE_CHANGE_TYPE = '+' then -1 * amount else amount end)
						   when b.amt_type != 'BAL' then (case when b.BALANCE_CHANGE_TYPE = '-' then -1 * amount else amount end)
						   end) as balance,
			   #{nodeId}        as NODE_ID,
			   #{lastRunDate}   as TRAN_DATE,
			   b.company        as company
		from RB_GL_HIST b
		where (TRAN_DATE = #{lastRunDate,jdbcType=DATE} or TRAN_DATE = #{yesterday,jdbcType=DATE})
		  and gl_seq_no BETWEEN #{startKey} and #{endKey}
		  and tran_type not in ('CRGL', 'DEGL', 'FEE2', 'CCGL', 'FEE', 'AMORTIZE')
		  and b.amount is not null
		  and balance_change_type is not null
		group by b.ACCT_BRANCH, b.ACCT_CCY, b.PROD_TYPE, b.ACCOUNTING_STATUS, b.amt_type, b.company
	</select>
	<select id="sumAcctCycleAmount" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalanceTemp" databaseId="mysql">
		select
			b.ACCT_BRANCH as ACCT_BRANCH,
			b.ACCT_CCY as ACCT_CCY,
			b.PROD_TYPE,
			b.ACCOUNTING_STATUS,
			'INT' as AMT_TYPE,
			sum(-1 * b.amount) as balance,
			#{nodeId} as NODE_ID,
			#{lastRunDate} as TRAN_DATE,
			b.company as company
		from RB_GL_HIST b
		where TRAN_DATE = #{lastRunDate,jdbcType=DATE} and gl_seq_no  BETWEEN #{startKey} and #{endKey}
		  and event_type = 'CYCLE' and b.amount is not null
		  and balance_change_type is not null
		group by   b.ACCT_BRANCH , b.ACCT_CCY, b.PROD_TYPE,b.ACCOUNTING_STATUS, b.amt_type,b.company
	</select>

	<select id="getOneProdBalancegroupBy" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance" databaseId="mysql">
		select
		BRANCH,
		CCY,
		PROD_TYPE,
		ACCOUNTING_STATUS,
		AMT_TYPE,
		COMPANY,
		SUM(balance) as balance,
		gl_code
		from RB_ACCT_PROD_BALANCE
		where TRAN_DATE = #{tranDate,jdbcType=DATE}
		and branch = #{branch}
		and ccy = #{ccy}
		and PROD_TYPE = #{prodType}
		and ACCOUNTING_STATUS = #{accountingStatus}
		and AMT_TYPE = #{amtType}
		and company = #{company}
		group by BRANCH , CCY, PROD_TYPE,ACCOUNTING_STATUS, amt_type,COMPANY,gl_code
	</select>


	<select id="getTmpSumAmt" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance" databaseId="mysql">
		select
			b.ACCT_BRANCH as BRANCH,
			b.ACCT_CCY as CCY,
			b.PROD_TYPE as prod_type ,
			b.ACCOUNTING_STATUS as accounting_status,
			b.amt_type as AMT_TYPE,
			SUM(b.balance) as balance,
			#{lastRunDate} as TRAN_DATE,
			#{company} as company,
			b.gl_code as gl_code
		from RB_ACCT_PROD_BALANCE_TEMP b
		where TRAN_DATE = #{lastRunDate,jdbcType=DATE} and acct_branch between #{startKey} and #{endKey}
		group by b.ACCT_BRANCH , b.ACCT_CCY, b.PROD_TYPE,b.ACCOUNTING_STATUS, b.amt_type,b.gl_code
	</select>
	<select id="getBranchAcctBalance" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance" databaseId="mysql">
		select	<include refid="Base_Column"/>
		from RB_ACCT_PROD_BALANCE b
		where branch between #{startKey} and #{endKey} and tran_date = #{LAST_LAST_RUN_DATE,jdbcType=DATE}
	</select>
	<select id="sumGlHistBalByInternalKey" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist" databaseId="mysql">
		select sum(case when a.BALANCE_CHANGE_TYPE = '+' then -1 * a.amount else a.amount end) as amount
		from rb_gl_hist a
		where a.internal_key = #{internalKey}
		  and a.tran_date = #{tranDate}
		  and a.client_no = #{clientNo}
		  and a.amt_type = 'BAL'
		  and (a.tran_type != 'FEE2' or a.event_type not in ('FEE','AMORTIZE'))
	</select>
	<select id="sumGlHistIntByInternalKey" parameterType="java.util.Map"
			resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist" databaseId="mysql">
		select sum(CASE WHEN a.EVENT_TYPE = 'CYCLE' THEN (-1 * a.amount)
						WHEN a.EVENT_TYPE != 'CYClE' THEN (CASE WHEN a.BALANCE_CHANGE_TYPE = '-' THEN -1 * a.amount ELSE a.amount end)
			END) AS amount
		from rb_gl_hist a
		where a.internal_key = #{internalKey}
		  and a.tran_date = #{tranDate}
		  and a.client_no = #{clientNo}
		  and (a.amt_type in ('INT','PDUE') OR a.EVENT_TYPE = 'CYCLE')
	</select>
	<select id="selectAllBalanceTempInfo" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance" databaseId="mysql">
		select
			b.ACCT_BRANCH as BRANCH,
			b.ACCT_CCY as CCY,
			b.PROD_TYPE as prod_type ,
			b.ACCOUNTING_STATUS as accounting_status,
			b.amt_type as AMT_TYPE,
			b.balance as balance,
			#{lastRunDate} as TRAN_DATE,
			#{company} as company,
		    b.gl_code as gl_code
		from RB_ACCT_PROD_BALANCE_TEMP b
		where TRAN_DATE = #{lastRunDate,jdbcType=DATE}
	</select>
	<delete id="deleteByDate" parameterType="java.util.Map" databaseId="mysql">
		delete from rb_acct_prod_balance where tran_date = #{tranDate,jdbcType=DATE} and branch between #{start} and #{end}
	</delete>
</mapper>
