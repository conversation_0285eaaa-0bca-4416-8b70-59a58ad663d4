<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalance">
  <update id="updatePrevBalForEod" parameterType="java.util.Map" databaseId="mysql">
    update RB_PCP_ACCT_BALANCE
    set
    LAST_PCP_BALANCE = PCP_BALANCE,
    LAST_TOTAL_UP_AMT = TOTAL_UP_AMT,
    LAST_TOTAL_DOWN_AMT = TOTAL_DOWN_AMT,
    LAST_CHANGE_DATE = #{runDate}
    where <![CDATA[
              last_change_date < #{runDate} AND
                (ifnull(LAST_PCP_BALANCE,0)<> ifnull(PCP_BALANCE,0) or
                ifnull(LAST_TOTAL_UP_AMT,0)<>ifnull(TOTAL_UP_AMT,0) or
                ifnull(LAST_TOTAL_DOWN_AMT,0)<>ifnull(TOTAL_DOWN_AMT,0))
              ]]>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </update>
</mapper>
