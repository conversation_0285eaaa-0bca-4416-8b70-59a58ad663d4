<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.model.entity.dbmodel.BatchOnlineCheck">

	<select id="getBatchTransResult" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.BatchOnlineCheck" databaseId="mysql">
		select BOC.BATCH_NO,
		BOC.TRAN_DATE,
		BOC.TOTAL_NUMBER,
		BOC.SUCCESS_NUMBER,
		BOC.FAILURE_NUMBER,
		BOC.TRAN_STATUS,
		BOR.FILE_PATH
		from BATCH_ONLINE_CHECK BOC,rb_batch_online_result BOR
		where BOC.BATCH_NO = BOR.BATCH_NO
		<if test="batchNo != null and batchNo != ''">
			and BOC.BATCH_NO = #{batchNo,jdbcType=VARCHAR}
		</if>
		<if test="beginDate != null and endDate != null  ">
			and date_format(BOC.TRAN_DATE,'%Y-%m-%d') between #{beginDate} and #{endDate}
		</if>
		<if test="batchClass != null and batchClass.length() > 0">
			and BOC.BATCH_CLASS = #{batchClass,jdbcType=VARCHAR}
		</if>
		<if test="stepType != null and stepType != ''">
			and BOC.STEP_TYPE = #{stepType,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getBatchTransResultForMt" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.BatchOnlineCheck" databaseId="mysql">
		select
		<include refid="Base_Column_List" />
		from BATCH_ONLINE_CHECK
		<where>
			<if test="batchNo != null and batchNo != ''">
				and BATCH_NO = #{batchNo,jdbcType=VARCHAR}
			</if>
			<if test="beginDate != null  ">
				and date_format(TRAN_DATE,'%Y-%m-%d') between #{beginDate} and #{endDate}
			</if>
			<if test="batchClass != null and batchClass.length() > 0">
				and BATCH_CLASS = #{batchClass,jdbcType=VARCHAR}
			</if>
			<if test="stepType != null and stepType != ''">
				and STEP_TYPE = #{stepType,jdbcType=VARCHAR}
			</if>
			<if test="branchId != null and branchId != ''">
				and BRANCH_ID = #{branchId,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<select id="getBatchTransResultCB" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.BatchOnlineCheck" databaseId="mysql">
		select <include refid="Base_Column_List" />
		from BATCH_ONLINE_CHECK BOC
		where BOC.BATCH_NO like 'CB%'
		<if test="batchNo != null and batchNo != ''">
			and BOC.BATCH_NO = #{batchNo,jdbcType=VARCHAR}
		</if>
		<if test="beginDate != null and endDate != null  ">
			and date_format(BOC.TRAN_DATE,'%Y-%m-%d') between #{beginDate} and #{endDate}
		</if>
		<if test="batchClass != null and batchClass.length() > 0">
			and BOC.BATCH_CLASS = #{batchClass,jdbcType=VARCHAR}
		</if>
		<if test="stepType != null and stepType != ''">
			and BOC.STEP_TYPE = #{stepType,jdbcType=VARCHAR}
		</if>
		<if test="branchId != null and branchId != ''">
			and BOC.BRANCH_ID = #{branchId,jdbcType=VARCHAR}
		</if>
		<if test="sourceType != null and sourceType != ''">
			and BOC.SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
		</if>
		<if test="tranStatus != null and tranStatus != ''">
			and BOC.TRAN_STATUS = #{tranStatus,jdbcType=VARCHAR}
		</if>

	</select>

	<select id="getBatchTrans" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.BatchOnlineCheck" databaseId="mysql">
		select <include refid="Base_Column_List"/>
		from BATCH_ONLINE_CHECK
		<where>
			<if test="beginDate != null">
				AND <![CDATA[ TRAN_DATE >=  #{beginDate} ]]>
			</if>
			<if test="endDate != null ">
				AND <![CDATA[ TRAN_DATE <=  #{endDate} ]]>
			</if>
			<if test="batchClass != null and batchClass.length() > 0">
				and BATCH_CLASS = #{batchClass,jdbcType=VARCHAR}
			</if>
			<if test="stepType != null and stepType != ''">
				and STEP_TYPE = #{stepType,jdbcType=VARCHAR}
			</if>
			<if test="branchId != null and branchId.length() > 0">
				and branch_Id = #{branchId,jdbcType=VARCHAR}
			</if>
			<if test="tranStatus != null and tranStatus.length() > 0">
				and tran_status = #{tranStatus,jdbcType=VARCHAR}
			</if>
		</where>
		ORDER BY TRAN_DATE
	</select>

	<select id="getBatchResultByType" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.model.entity.dbmodel.BatchOnlineCheck" databaseId="mysql">
		select <include refid="Base_Column_List" />
		from BATCH_ONLINE_CHECK BOC
		<where>
			<if test="beginDate != null and endDate != null  ">
				and date_format(BOC.TRAN_DATE,'%Y-%m-%d') between #{beginDate} and #{endDate}
			</if>
			<if test="branchId != null and branchId != ''">
				and BOC.BRANCH_ID = #{branchId,jdbcType=VARCHAR}
			</if>
			<if test="tranStatus != null and tranStatus != ''">
				and BOC.TRAN_STATUS = #{tranStatus,jdbcType=VARCHAR}
			</if>
			<if test="stepTypeList != null ">
				AND BOC.step_Type IN
				<foreach collection="stepTypeList" item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>
</mapper>