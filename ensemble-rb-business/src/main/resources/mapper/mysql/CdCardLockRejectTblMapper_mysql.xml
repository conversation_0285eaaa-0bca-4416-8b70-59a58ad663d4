<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardLockRejectTbl">
	<!--多法人改造 by LIYUANV-->
	<select id="getCdCardLockRejectInfos" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.CdCardLockRejectTbl" databaseId="mysql">
		select
		<include refid="Base_Column"/>
		from
		<include refid="Table_Name" />
		<where>
		<if test="beginDate != null ">
			and date_format(TRAN_DATE,'%Y-%m-%d') &gt;= #{beginDate}
		</if>
		<if test="endDate != null ">
			and date_format(TRAN_DATE,'%Y-%m-%d') &lt;= #{endDate}
		</if>
		<if test="cardNo != null and  cardNo != '' ">
			and CARD_NO = #{cardNo}
		</if>
		<if test="company != null and company != '' ">
			AND COMPANY = #{company}
		</if>
		</where>
	</select>

</mapper>