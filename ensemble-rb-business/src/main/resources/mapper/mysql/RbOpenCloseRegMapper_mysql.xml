<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg">
  <select id="getSeqNo" resultType="java.util.Map" databaseId="mysql">
      select MAX(CONVERT(t.SEQ_NO ,DECIMAL)) SEQ_NO FROM RB_OPEN_CLOSE_REG t
      where 1=1
      <!-- 多法人改造 by luocwa -->
      <if test="company != null and company != '' ">
        AND t.COMPANY = #{company}
      </if>
  </select>
  <select id="getCloseAcctRegList" parameterType="java.util.Map" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg" databaseId="mysql">
    select ACCT_SEQ_NO,
    ACCT_TYPE,
    ACCT_CCY,
    TRAN_DATE,
    REFERENCE,
    ACTIVE_DATE,
    TRAN_BRANCH,
    COMPANY,
    INTERNAL_KEY,
    INFORM_BANK_FLAG,
    CARD_NO,
    ifnull(CARD_NO,BASE_ACCT_NO) as BASE_ACCT_NO,
    PROD_TYPE,
    USER_ID,
    NARRATIVE,
    ACCT_STATUS,
    ACCT_NATURE,
    SEQ_NO,
    REG_TYPE,
    OP_METHOD,
    CLIENT_NO,
    TRAN_TIMESTAMP,
    SUC_FLAG,
    DOCUMENT_ID
    from RB_OPEN_CLOSE_REG
    where  ACCT_STATUS='C'
    <if test="tranDate != null">
      AND  TRAN_DATE= #{tranDate,jdbcType=DATE}
    </if>
    <!-- 多法人改造 by luocwa -->
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
</mapper>
