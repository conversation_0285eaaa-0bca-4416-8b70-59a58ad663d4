<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeIntWriteDetail">
    <select id="selectLastReference" parameterType="java.util.HashMap" resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeIntWriteDetail"  databaseId="mysql" >
        SELECT
        *
        FROM
        (
        SELECT * FROM
        RB_FEE_INT_WRITE_DETAIL
        <where>
            <if test="feeIntNo != null and feeIntNo != '' ">
                FEE_INT_NO = #{feeIntNo}
            </if>
            <if test="clientNo != null and clientNo != '' ">
                CLIENT_NO = #{clientNo}
            </if>
        </where>
        order by TRAN_TIMESTAMP desc
        )
        limit  1
    </select>
</mapper>
