<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist">
  <!--查詢給定日期未平盘的流水-->
  <select id="getExTranHistListbyUncStatusAndDate" parameterType="java.util.Map"
          resultType="com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist" databaseId="mysql">
    select <include refid="Base_Column"/>
    from RB_EXCHANGE_TRAN_HIST
    where
    TRAN_DATE = #{tranDate}
    <if test="uncStatus != null and uncStatus.length() > 0">
      and ifnull(UNC_STATUS,'N') = #{uncStatus}
    </if>
    <if test="company != null and company != '' ">
      AND COMPANY = #{company}
    </if>
  </select>
  <!--汇总查询已经系统内平盘需要市场平盘的数据-->
  <select id="getObuncEntriesByBrnFcy" parameterType="java.util.Map"
          resultType="java.util.Map" databaseId="mysql">
    SELECT TRAN_BRANCH,
    SELL_BUY_IND,
    FCY_CCY,
    SUM(ifnull(if(AA.IS_REVERSAL='Y',
    -1 * AA.FCY_CTRL_IBUNC_AMT,
    AA.FCY_CTRL_IBUNC_AMT),
    0)) FCY_BRN_OBUNCAMT
    FROM (SELECT A.TRAN_DATE,
    A.TRAN_BRANCH,
    A.SELL_BUY_IND,
    if(A.SELL_BUY_IND='B', A.BUY_CCY, A.SELL_CCY) FCY_CCY,
    if(A.REVERSAL_TRAN_TYPE, 'Y', 'N') IS_REVERSAL,
    A.UNC_STATUS,
    A.IBUNC_REFERENCE,
    A.OBUNC_REFERENCE,
    A.FCY_CTRL_IBUNC_AMT
    FROM RB_EXCHANGE_TRAN_HIST A
    WHERE A.TRAN_DATE between  #{lastRunDate,jdbcType=DATE} and #{yesterday,jdbcType=DATE}
    AND A.UNC_STATUS = 'P'
    ) AA
    GROUP BY AA.TRAN_BRANCH, AA.SELL_BUY_IND, AA.FCY_CCY
  </select>
  <!--查询结售汇汇总记录，进行汇总模式下的系统内平盘-->
  <select id="getIBUncEntriesOnSum" parameterType="java.util.Map"
          resultType="java.util.Map" databaseId="mysql">
    select A.TRAN_BRANCH,
    A.SELL_BUY_IND,
    a.rate_type,
    if(a.reversal_tran_type, 'Y', 'N') IS_REVERSAL,
    if(a.sell_buy_ind='B', A.BUY_CCY, A.SELL_CCY) FCY_CCY,
    SUM(a.buy_amount) AMT_BUY,
    sum(a.sell_amount) AMT_SELL
    from rb_exchange_tran_hist a
    WHERE (a.unc_status = 'R')
    AND A.TRAN_DATE=#{tranDate}
    group by A.TRAN_BRANCH,
    A.SELL_BUY_IND,
    a.rate_type,
    if(a.reversal_tran_type, 'Y', 'N'),
    if(a.sell_buy_ind= 'B', A.BUY_CCY, A.SELL_CCY)
  </select>
</mapper>
