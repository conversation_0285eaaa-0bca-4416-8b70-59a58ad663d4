<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHist">
    <select id="getTranHIstBySeqNoMax" resultMap="MbTranHistBean"
            parameterType="java.util.Map" databaseId="mysql">
        SELECT MAX(seq_no+0) AS seq_no
        FROM RB_tran_hist
        WHERE  internal_key = #{internalKey}
        <if test="clientNo != null">
            AND client_no = #{clientNo}
        </if>
        AND tran_date =#{runDate,jdbcType=DATE}
        AND (seq_no+0) <![CDATA[  < ]]> #{seqNo}
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
    </select>
    <select id="getKindTranAmt2" parameterType="java.util.Map"
            resultMap="MbTranHistBean" databaseId="mysql">
        SELECT *
        FROM RB_tran_hist
        WHERE CLIENT_NO = #{clientNo}
        AND INTERNAL_KEY = #{internalKey}
        AND TRAN_DATE = #{tranDate}
        <if test="company != null and company != '' ">
            AND COMPANY = #{company}
        </if>
        limit 1
    </select>
</mapper>
