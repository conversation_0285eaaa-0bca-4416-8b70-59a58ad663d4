package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpParameter;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpParameterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpParameterRepository.class);

   public RbPcpParameter getParamterValue(String paraKey) {
      Map<String, Object> param = new HashMap();
      param.put("paraKey", paraKey);
      return (RbPcpParameter)this.daoSupport.selectOne(RbPcpParameter.class.getName() + ".getParamterValue", param);
   }
}
