package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementLimit;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpAgreementLimitRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpAgreementLimitRepository.class);

   public void inertPcpAgreementLimit(RbPcpAgreementLimit mbPcpAgreementLimit) {
      super.insert(mbPcpAgreementLimit);
   }

   public void creatPcpAgreementLimit(List<RbPcpAgreementLimit> mbPcpAgreementLimitList) {
      Iterator var2 = mbPcpAgreementLimitList.iterator();

      while(var2.hasNext()) {
         RbPcpAgreementLimit mbPcpAgreementLimit = (RbPcpAgreementLimit)var2.next();
         super.insert(mbPcpAgreementLimit);
      }

   }

   public List<RbPcpAgreementLimit> getMbPcpLimitByAgreementId(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbPcpAgreementLimit.class.getName() + ".selectByAgreementId", param);
   }

   public void updateMbPcpAgreementLimit(List<RbPcpAgreementLimit> mbPcpAgreementLimitList) {
      String agreementId = ((RbPcpAgreementLimit)mbPcpAgreementLimitList.get(0)).getAgreementId();
      String clientNo = ((RbPcpAgreementLimit)mbPcpAgreementLimitList.get(0)).getClientNo();
      RbPcpAgreementLimit mbPcpAgreementLimit = new RbPcpAgreementLimit();
      mbPcpAgreementLimit.setAgreementId(agreementId);
      mbPcpAgreementLimit.setClientNo(clientNo);
      this.daoSupport.delete(mbPcpAgreementLimit);
      Iterator var5 = mbPcpAgreementLimitList.iterator();

      while(var5.hasNext()) {
         RbPcpAgreementLimit pcpAgreementLimit1 = (RbPcpAgreementLimit)var5.next();
         super.insert(pcpAgreementLimit1);
      }

   }

   public List<RbPcpAgreementLimit> selectMbPcpLimitByLimitType() {
      return this.daoSupport.selectList(RbPcpAgreementLimit.class.getName() + ".selectMbPcpLimitByLimitType", new RbPcpAgreementLimit());
   }

   public List<RbPcpAgreementLimit> selectMbPcpLimitByTypePW() {
      return this.daoSupport.selectList(RbPcpAgreementLimit.class.getName() + ".selectMbPcpLimitByTypePW", new RbPcpAgreementLimit());
   }

   public List<RbPcpAgreementLimit> selectMbPcpLimitByTypePDorPT() {
      return this.daoSupport.selectList(RbPcpAgreementLimit.class.getName() + ".selectMbPcpLimitByTypePDorPT", new RbPcpAgreementLimit());
   }

   public List<RbPcpAgreementLimit> selectMbPcpLimitByTypePc() {
      Map<String, Object> param = new HashMap();
      param.put("runDate", Context.getInstance().getLastRunDate());
      return this.daoSupport.selectList(RbPcpAgreementLimit.class.getName() + ".selectMbPcpLimitByTypePc", param);
   }
}
