package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbGlAcctManualBatchDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbGlAcctManualBatchDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbGlAcctManualBatchDetailRepository.class);

   public void createRbGlAcctManualBatchDetail(List<RbGlAcctManualBatchDetail> rbGlAcctManualBatchDetail) {
      if (BusiUtil.isNotNull(rbGlAcctManualBatchDetail)) {
         for(int i = 0; i < rbGlAcctManualBatchDetail.size(); ++i) {
            super.insert((EnsBaseDbBean)rbGlAcctManualBatchDetail.get(i));
         }
      }

   }
}
