package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle;
import com.dcits.ensemble.rb.business.model.acct.settle.SettleClassEnum;
import com.dcits.ensemble.rb.business.repository.acct.RbAcctSettleRepsitory;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbTdLienRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbTdLienRepository.class);
   @Resource
   private RbAcctSettleRepsitory rbAcctSettleRepsitory;

   public RbAcctSettle getRbAcctSettle(Long internalKey, String clientNo) {
      RbAcctSettle rbAcctSettle = this.rbAcctSettleRepsitory.selectAcctSettleByIntSettleAcctClass(internalKey, SettleClassEnum.INT.getCode(), clientNo, (String)null);
      if (BusiUtil.isNull(rbAcctSettle)) {
         rbAcctSettle = this.rbAcctSettleRepsitory.selectAcctSettleByIntSettleAcctClass(internalKey, SettleClassEnum.CON.getCode(), clientNo, (String)null);
         if (BusiUtil.isNull(rbAcctSettle)) {
            throw BusiUtil.createBusinessException("RB3116");
         }
      }

      return rbAcctSettle;
   }

   public RbAcctSettle getRbAcctSettleDc(Long internalKey, String clientNo) {
      RbAcctSettle rbAcctSettle = this.rbAcctSettleRepsitory.selectAcctSettleByIntSettleAcctClass(internalKey, SettleClassEnum.INT.getCode(), clientNo, (String)null);
      if (BusiUtil.isNull(rbAcctSettle)) {
         rbAcctSettle = this.rbAcctSettleRepsitory.selectAcctSettleByIntSettleAcctClass(internalKey, SettleClassEnum.AUS.getCode(), clientNo, (String)null);
         if (BusiUtil.isNull(rbAcctSettle)) {
            throw BusiUtil.createBusinessException("RB3116");
         }
      }

      return rbAcctSettle;
   }
}
