package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveReg;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbBatchReserveRegRepository extends BusinessRepository {
   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateBatch(List<RbBatchReserveReg> list) {
      this.daoSupport.updateAddBatch(list);
   }
}
