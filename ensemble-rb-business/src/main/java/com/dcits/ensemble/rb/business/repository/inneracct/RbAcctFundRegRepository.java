package com.dcits.ensemble.rb.business.repository.inneracct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctFundReg;
import com.dcits.ensemble.rb.business.model.inneracct.MbAcctFundRegModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctFundRegRepository extends BusinessRepository {
   public RbAcctFundReg getFundRegInfo(String baseAcctNo, String acctSeqNo) {
      RbAcctFundReg fundReg = new RbAcctFundReg();
      fundReg.setBaseAcctNo(baseAcctNo);
      fundReg.setAcctSeqNo(acctSeqNo);
      return (RbAcctFundReg)this.daoSupport.selectOne(fundReg);
   }

   public void updateByInfo(String baseAcctNo, String acctSeqNo, String clientNo, String fundAcctType, String othBaseAcctNo, String othAcctSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("fundAcctType", fundAcctType);
      param.put("othBaseAcctNo", othBaseAcctNo);
      param.put("othAcctSeqNo", othAcctSeqNo);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAcctFundReg.class.getName() + ".updateByInfo", param);
   }

   public void updateStatusByAcctNo(String baseAcctNo, String acctSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAcctFundReg.class.getName() + ".updateStatusByAcctNo", param);
   }

   public List<RbAcctFundReg> getFundRegInfoList(String baseAcctNo, List<String> branchList) {
      Map<String, Object> param = new HashMap(4);
      param.put("baseAcctNo", baseAcctNo);
      param.put("branchList", branchList);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcctFundReg.class.getName() + ".getFundRegInfoList";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctFundReg> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctFundReg> getFundRegInfoByType(String fundAcctType) {
      Map<String, Object> param = new HashMap(4);
      param.put("fundAcctType", fundAcctType);
      return this.daoSupport.selectList(RbAcctFundReg.class.getName() + ".getFundRegInfoList", param);
   }

   public MbAcctFundRegModel getFundRegInfoModel(Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      List<MbAcctFundRegModel> modelList = this.daoSupport.selectList(RbAcctFundReg.class.getName() + ".getFundRegInfoModel", param);
      return BusiUtil.isNull(modelList) ? null : (MbAcctFundRegModel)modelList.get(0);
   }

   public List<RbAcctFundReg> selectFundRegInfoList(String branch) {
      Map<String, Object> param = new HashMap(4);
      param.put("branch", branch);
      return this.daoSupport.selectList(RbAcctFundReg.class.getName() + ".getFundRegInfoListByBranch", param);
   }
}
