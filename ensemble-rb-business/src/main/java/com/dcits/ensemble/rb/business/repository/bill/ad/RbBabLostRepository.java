package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabLost;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbBabLostRepository extends BusinessRepository {
   public int creatLost(RbBabLost rbBabLost) {
      return super.insert(rbBabLost);
   }

   public int updateLost(RbBabLost rbBabLost) {
      return super.update(rbBabLost);
   }

   public List<RbBabLost> queryBabLostParam(long internalKey, Date startDate, Date endDate, String lostStatus, String lostNo) {
      Map<String, Object> param = new HashMap();
      if (internalKey != 0L) {
         param.put("internalKey", internalKey);
      }

      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("lostStatus", lostStatus);
      param.put("lostNo", lostNo);
      return this.daoSupport.selectList(RbBabLost.class.getName() + ".queryBabLostParam", param);
   }

   public List<RbBabLost> queryByBabLostNoAndStatus(String lostStatus, long lostNo) {
      Map<String, Object> param = new HashMap();
      param.put("lostStatus", lostStatus);
      param.put("lostNo", lostNo);
      List<RbBabLost> rbBabLosts = this.daoSupport.selectList(RbBabLost.class.getName() + ".queryByBabLostNoAndStatus", param);
      if (BusiUtil.isNull(rbBabLosts)) {
         throw BusiUtil.createBusinessException("AD4002");
      } else {
         return rbBabLosts;
      }
   }

   public List<RbBabLost> queryByReferenceAndStatus(String reference, String status) {
      if (!BusiUtil.isNotNull(reference) && !BusiUtil.isNotNull(status)) {
         throw BusiUtil.createBusinessException("RB9847");
      } else {
         Map<String, Object> param = new HashMap();
         param.put("reference", reference);
         param.put("status", status);
         List<RbBabLost> rbBabLosts = this.daoSupport.selectList(RbBabLost.class.getName() + ".queryByReferenceAndStatus", param);
         return rbBabLosts;
      }
   }

   public List<RbBabLost> queryByBabLostsNoAndStatus(String lostStatus, long lostNo) {
      Map<String, Object> param = new HashMap();
      param.put("lostStatus", lostStatus);
      param.put("lostNo", lostNo);
      List<RbBabLost> rbBabLosts = this.daoSupport.selectList(RbBabLost.class.getName() + ".queryByBabLostsNoAndStatus", param);
      if (BusiUtil.isNull(rbBabLosts)) {
         throw BusiUtil.createBusinessException("AD4002");
      } else {
         return rbBabLosts;
      }
   }
}
