package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAccrMergeHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAccrMergeHistRepository extends BusinessRepository {
   public List<RbAccrMergeHist> sumAccrMergeInfo(Map map) {
      map.put("company", Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbAccrMergeHist.class.getName() + ".sumAccrMergeInfo", map);
   }

   public List<RbAccrMergeHist> extAccrMergeInfoStepSegment() {
      String company = Context.getInstance().getCompany();
      RbAccrMergeHist rbAccrMergeHist = new RbAccrMergeHist();
      rbAccrMergeHist.setCompany(company);
      return this.daoSupport.selectList(RbAccrMergeHist.class.getName() + ".extAccrMergeInfoStepSegment", rbAccrMergeHist);
   }

   public List<RbAccrMergeHist> extAccrMergeInfoStepSegmentInFo() {
      String company = Context.getInstance().getCompany();
      RbAccrMergeHist rbAccrMergeHist = new RbAccrMergeHist();
      rbAccrMergeHist.setCompany(company);
      return this.daoSupport.selectList(RbAccrMergeHist.class.getName() + ".extAccrMergeInfoStepSegmentInFo", rbAccrMergeHist);
   }
}
