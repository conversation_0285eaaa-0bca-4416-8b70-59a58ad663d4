package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPrice;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbPcpInnerPriceRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpInnerPriceRepository.class);

   public RbPcpInnerPrice getMbPcpInnerPriceByInternalKey(Long internalKey) {
      if (log.isInfoEnabled()) {
         log.info("internalKey : [" + internalKey + "]");
      }

      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      RbPcpInnerPrice mbPcpInnerPrice = (RbPcpInnerPrice)this.daoSupport.selectOne(RbPcpInnerPrice.class.getName() + ".getMbPcpInnerPriceByInternalKey", map);
      return mbPcpInnerPrice;
   }

   public List<RbPcpInnerPrice> selectDoubleAcct(String clientNo, String groupId) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("pcpGroupId", groupId);
      return this.daoSupport.selectList(RbPcpInnerPrice.class.getName() + ".selectByInternalkay", param);
   }

   public List<RbPcpInnerPrice> selectEodPcpInnerPrice(Long startKay, Long endKay) {
      Map<String, Object> param = new HashMap();
      param.put("startKay", startKay);
      param.put("endKay", endKay);
      return this.daoSupport.selectList(RbPcpInnerPrice.class.getName() + ".selectEodPcpInnerPrice", param);
   }

   public int createPcpInnerPrice(RbPcpInnerPrice mbPcpInnerPrice) {
      return super.insert(mbPcpInnerPrice);
   }

   public void createPcpInnerPrice(List<RbPcpInnerPrice> mbPcpInnerPriceList) {
      Iterator var2 = mbPcpInnerPriceList.iterator();

      while(var2.hasNext()) {
         RbPcpInnerPrice mbPcpInnerPrice = (RbPcpInnerPrice)var2.next();
         super.insert(mbPcpInnerPrice);
      }

   }

   public void updatePcpInnerPrice(List<RbPcpInnerPrice> mbPcpInnerPriceList) {
      Iterator var2 = mbPcpInnerPriceList.iterator();

      while(var2.hasNext()) {
         RbPcpInnerPrice mbPcpInnerPrice = (RbPcpInnerPrice)var2.next();
         super.update(mbPcpInnerPrice);
      }

   }

   public void updatePcpInnerPriceA(RbPcpInnerPrice mbPcpInnerPrice) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", mbPcpInnerPrice.getInternalKey());
      RbPcpInnerPrice mbPcpInnerPriceOld = (RbPcpInnerPrice)this.daoSupport.selectOne(RbPcpInnerPrice.class.getName() + ".getMbPcpInnerPriceByInternalKey", map);
      if (BusiUtil.isNotNull(mbPcpInnerPriceOld)) {
         super.update(mbPcpInnerPrice);
      } else {
         super.insert(mbPcpInnerPrice);
      }

   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updatePcpInnerPriceB(RbPcpInnerPrice mbPcpInnerPrice) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", mbPcpInnerPrice.getInternalKey());
      RbPcpInnerPrice mbPcpInnerPriceOld = (RbPcpInnerPrice)this.daoSupport.selectOne(RbPcpInnerPrice.class.getName() + ".getMbPcpInnerPriceByInternalKey", map);
      if (BusiUtil.isNotNull(mbPcpInnerPriceOld)) {
         super.update(mbPcpInnerPrice);
      } else {
         super.insert(mbPcpInnerPrice);
      }

   }
}
