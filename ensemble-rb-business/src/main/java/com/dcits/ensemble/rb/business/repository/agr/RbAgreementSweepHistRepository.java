package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweepHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementSweepHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementSweepHistRepository.class);

   public void createMbAgreementSweepHistDb(RbAgreementSweepHist rbAgreementSweepHist) {
      rbAgreementSweepHist.setRenewRegSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbAgreementSweepHistSeqNo));
      super.insert(rbAgreementSweepHist);
   }
}
