package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.bc.component.fee.sequence.MbBatchChargeSeqNo;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchCharge;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbBatchChargeRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbBatchChargeRepository.class);

   public List<RbBatchCharge> selectNextChargeDateMature(Date runDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("runDate", runDate);
      return this.daoSupport.selectList(RbBatchCharge.class.getName() + ".selectNextChargeDateMature", param);
   }

   public List<RbBatchCharge> selectFeeAmtLessThanZero(String runDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("runDate", runDate);
      return this.daoSupport.selectList(RbBatchCharge.class.getName() + ".selectFeeAmtLessThanZero", param);
   }

   public List<RbBatchCharge> selectFeeAmtLargeThanZero(String runDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("runDate", runDate);
      return this.daoSupport.selectList(RbBatchCharge.class.getName() + ".selectFeeAmtLargeThanZero", param);
   }

   public int insertBatch(List<RbBatchCharge> rbBatchCharges) {
      return super.insertAddBatch(rbBatchCharges);
   }

   public RbBatchCharge createNewMbBatchCharge(String batchSeqNo) {
      RbBatchCharge rbBatchCharge = new RbBatchCharge();
      if (BusiUtil.isNotNull(batchSeqNo)) {
         rbBatchCharge.setBatchSeqNo(batchSeqNo);
      } else {
         MbBatchChargeSeqNo mbBatchChargeSeqNo = new MbBatchChargeSeqNo();
         rbBatchCharge.setBatchSeqNo(mbBatchChargeSeqNo.getNextVal());
      }

      return rbBatchCharge;
   }

   public List<RbBatchCharge> getBaseAcctNoByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbBatchCharge.class.getName() + ".getBaseAcctInfoByInternalKey", param);
   }

   public List<RbBatchCharge> getMbBatchChargeListByBaseAcctNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbBatchCharge.class.getName() + ".getMbBatchChargeListByBaseAcctNo", param);
   }

   public RbBatchCharge getMbBatchChargeListByReference(String reference) {
      RbBatchCharge rbBatchCharge = new RbBatchCharge();
      rbBatchCharge.setReference(reference);
      return (RbBatchCharge)this.daoSupport.selectOne(rbBatchCharge);
   }

   public List<RbBatchCharge> getMbBatchChargeListByCardNo(String cardNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("cardNo", cardNo);
      return this.daoSupport.selectList(RbBatchCharge.class.getName() + ".getMbBatchChargeListByCardNo", param);
   }

   public void updateProdByKey(String newProdType, long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("newProdType", newProdType);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbBatchCharge.class.getName() + ".updateProdByKey", param);
   }
}
