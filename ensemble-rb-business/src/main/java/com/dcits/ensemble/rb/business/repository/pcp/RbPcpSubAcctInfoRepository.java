package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpSubAcctInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpSubAcctInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpSubAcctInfoRepository.class);

   public List<RbPcpSubAcctInfo> getMbPcpSubInfos(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String subSeqNo, Long internalKey, String priority) {
      Map<String, Object> map = new HashMap(16);
      map.put("baseAcctNo", baseAcctNo);
      map.put("prodType", prodType);
      map.put("acctCcy", acctCcy);
      map.put("acctSeqNo", acctSeqNo);
      map.put("subSeqNo", subSeqNo);
      map.put("internalKey", internalKey);
      map.put("priority", priority);
      return this.daoSupport.selectList(RbPcpSubAcctInfo.class.getName() + ".getMbPcpSubInfos", map);
   }

   public RbPcpSubAcctInfo selectByIdAndKey(String pcpGroupId, Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", pcpGroupId);
      param.put("internalKey", internalKey);
      return (RbPcpSubAcctInfo)this.daoSupport.selectOne(RbPcpSubAcctInfo.class.getName() + ".selectByIdAndKey", param);
   }

   public RbPcpSubAcctInfo selectDeleteByIdAndKey(String pcpGroupId, Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", pcpGroupId);
      param.put("internalKey", internalKey);
      return (RbPcpSubAcctInfo)this.daoSupport.selectOne(RbPcpSubAcctInfo.class.getName() + ".selectDeleteByIdAndKey", param);
   }

   public List<RbPcpSubAcctInfo> getMbPcpSubInfoByClientNo(String clientNo, String pcpGroupId) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("pcpGroupId", pcpGroupId);
      return this.daoSupport.selectList(RbPcpSubAcctInfo.class.getName() + ".getMbPcpSubInfoByClientNo", param);
   }

   public List<RbPcpSubAcctInfo> getMbPcpSubInfosByGroupId(String pcpGroupId) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", pcpGroupId);
      return this.daoSupport.selectList(RbPcpSubAcctInfo.class.getName() + ".getMbPcpSubInfosByGroupId", param);
   }

   public List<RbPcpSubAcctInfo> selectEodPcpSubAcctInfos(Long startKay, Long endKay) {
      Map<String, Object> param = new HashMap(16);
      param.put("START_KEY", startKay);
      param.put("END_KEY", endKay);
      return this.daoSupport.selectList(RbPcpSubAcctInfo.class.getName() + ".selectEodPcpSubAcctInfos", param);
   }

   public RbPcpSubAcctInfo selectByInternalKey(String pcpGroupId, Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", pcpGroupId);
      param.put("internalKey", internalKey);
      return (RbPcpSubAcctInfo)this.daoSupport.selectOne(RbPcpSubAcctInfo.class.getName() + ".selectByInternalKey", param);
   }

   public RbPcpSubAcctInfo selectByNotIdKey(String pcpGroupId, Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", pcpGroupId);
      param.put("internalKey", internalKey);
      return (RbPcpSubAcctInfo)this.daoSupport.selectOne(RbPcpSubAcctInfo.class.getName() + ".selectByNotIdKey", param);
   }

   public List<RbPcpSubAcctInfo> getSubAcctByUpperInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("upperInternalKey", internalKey);
      return this.daoSupport.selectList(RbPcpSubAcctInfo.class.getName() + ".getSubAcctByUpperInternalKey", param);
   }

   public List<RbPcpSubAcctInfo> getSubAcctByseqNoAndGroupId(String subSeqNo, String pcpGroupId) {
      Map<String, Object> param = new HashMap(16);
      param.put("subSeqNo", subSeqNo);
      param.put("pcpGroupId", pcpGroupId);
      return this.daoSupport.selectList(RbPcpSubAcctInfo.class.getName() + ".getSubAcctByseqNoAndGroupId", param);
   }

   public List<RbPcpSubAcctInfo> getSubAcctByStatus() {
      return this.daoSupport.selectList("getSubAcctByStatus", new RbPcpSubAcctInfo());
   }

   public RbPcpSubAcctInfo getMbPcpSubInfoDetail(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String clientNo) {
      Map<String, Object> map = new HashMap(16);
      map.put("baseAcctNo", baseAcctNo);
      map.put("prodType", prodType);
      map.put("acctCcy", acctCcy);
      map.put("acctSeqNo", acctSeqNo);
      map.put("clientNo", clientNo);
      List<RbPcpSubAcctInfo> list = this.daoSupport.selectList(RbPcpSubAcctInfo.class.getName() + ".getMbPcpSubInfoDetail", map);
      return BusiUtil.isNotNull(list) ? (RbPcpSubAcctInfo)list.get(0) : null;
   }
}
