package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.model.FmCashInoutCategory;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class FmCashInoutCategoryRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(FmCashInoutCategoryRepository.class);

   public List<FmCashInoutCategory> selectListByMap(String clientType, String categoryType, String prodType, String clinetBranchType) {
      Map<String, Object> param = new HashMap();
      param.put("categoryType", categoryType);
      param.put("clientType", clientType);
      param.put("prodType", prodType);
      param.put("clientBranchType", clinetBranchType);
      List list = this.daoSupport.selectList(FmCashInoutCategory.class.getName() + ".selectList", param);
      return list;
   }
}
