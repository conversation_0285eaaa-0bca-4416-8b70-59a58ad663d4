package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.bc.unit.voucher.base.business.VoucherStatusEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbVoucherAcctRelationRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherAcctRelationRepository.class);

   public RbVoucherAcctRelation rbVoucherAcctRelation(String voucherNo, String docType, String baseAcctNo) {
      RbVoucherAcctRelation rbVoucherAcctRelation = new RbVoucherAcctRelation();
      rbVoucherAcctRelation.setVoucherNo(voucherNo);
      rbVoucherAcctRelation.setDocType(docType);
      rbVoucherAcctRelation.setBaseAcctNo(baseAcctNo);
      return (RbVoucherAcctRelation)this.daoSupport.selectOne(rbVoucherAcctRelation);
   }

   public List<RbVoucherAcctRelation> getVouncherAcctRelation(String baseAcctNo, String Ccy, String acctSeqNo, String prodType) {
      RbVoucherAcctRelation rbVoucherAcctRelation = new RbVoucherAcctRelation();
      rbVoucherAcctRelation.setBaseAcctNo(baseAcctNo);
      rbVoucherAcctRelation.setAcctSeqNo(acctSeqNo);
      rbVoucherAcctRelation.setAcctCcy(Ccy);
      rbVoucherAcctRelation.setProdType(prodType);
      return this.daoSupport.selectList(rbVoucherAcctRelation);
   }

   public List<RbVoucherAcctRelation> getRbVoucherAcctRelation(String baseAcctNo, String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      map.put("voucherStatus", VoucherStatusEnum.ACT.getCode());
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getRbVoucherAcctRelation", map);
   }
}
