package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYht;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementYhtRepository extends BusinessRepository {
   public void createMbAgreemt(RbAgreementYht rbAgreementYht) {
      super.insert(rbAgreementYht);
   }

   public void updateByPrimaryKey(RbAgreementYht rbAgreementYht) {
      super.update(rbAgreementYht);
   }

   public void updateMaxSeqNoByInternalKey(Long internalKey, String nextMaxSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("nextMaxSeqNo", nextMaxSeqNo);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAgreementYht.class.getName() + ".updateMaxSeqNoByInternalKey", param);
   }

   public RbAgreementYht getByAgreementId(String agreementId) {
      RbAgreementYht rbAgreementYht = new RbAgreementYht();
      rbAgreementYht.setAgreementId(agreementId);
      return (RbAgreementYht)this.daoSupport.selectOne(rbAgreementYht);
   }

   public RbAgreementYht getMbAgreementYht(String baseAcctNo, String prodType, String ccy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getMbAgreementYht", param);
   }

   public RbAgreementYht getMainMbAgreementYht(RbAgreementYht rbAgreementYht) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", rbAgreementYht.getBaseAcctNo());
      param.put("prodType", rbAgreementYht.getProdType());
      param.put("acctCcy", rbAgreementYht.getAcctCcy());
      param.put("acctSeqNo", rbAgreementYht.getAcctSeqNo());
      RbAgreementYht rbAgreementYht1 = (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getMbAgreementYht", param);
      if (BusiUtil.isNull(rbAgreementYht1)) {
         throw BusiUtil.createBusinessException("MB4036", new String[]{rbAgreementYht.getBaseAcctNo()});
      } else {
         String agreementId = BusiUtil.isNotNull(rbAgreementYht1.getMainAgreementId()) ? rbAgreementYht1.getMainAgreementId() : "";
         if (!"".equals(agreementId)) {
            Map<String, Object> param1 = new HashMap(16);
            param1.put("agreementId", agreementId);
            RbAgreementYht mainRbAgreementYht = (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getMbYhtByAgreement1", param1);
            if (BusiUtil.isNotNull(mainRbAgreementYht)) {
               return mainRbAgreementYht;
            } else {
               throw BusiUtil.createBusinessException("");
            }
         } else {
            throw BusiUtil.createBusinessException("");
         }
      }
   }

   public RbAgreementYht getByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(1);
      param.put("internalKey", internalKey);
      return (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getByInternalKey", param);
   }

   public RbAgreementYht getByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getByInternalKey", param);
   }

   public List<RbAgreementYht> getByBaseAcctNoLike(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getByBaseAcctNoLike", param);
   }

   public RbAgreementYht getMainAcct(String agreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      param.put("yhtAcctFalg", "A");
      return (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getMainByInternalKey", param);
   }

   public List<RbAgreementYht> getByMainAgreementId(String mainAgreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("mainAgreementId", mainAgreementId);
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getByMainAgreementId", param);
   }

   public List<RbAgreementYht> getByMainAgreementIdInd(String mainAgreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("mainAgreementId", mainAgreementId);
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getByMainAgreementIdInd", param);
   }

   public List<RbAgreementYht> getByParentInternalKey(Long parentInternalKey) {
      Map<String, Object> param = new HashMap(1);
      param.put("parentInternalKey", parentInternalKey.toString());
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getByParentInternalKey", param);
   }

   public List<RbAgreementYht> getByParentInternalKey(Long parentInternalKey, String ClientNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("parentInternalKey", parentInternalKey.toString());
      param.put("clientNo", ClientNo);
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getByParentInternalKey", param);
   }

   public List<RbAgreementYht> getSelfAcctAndMyself(Long internalKey, String baseAcctNo, String acctPostfix) {
      Map<String, Object> param = new HashMap(1);
      param.put("internalKey", internalKey.toString());
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctPostFix", BusiUtil.nvl(acctPostfix, "0000"));
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getSelfAcctAndMyself", param);
   }

   public List<RbAgreementYht> getSelfAcctAndMyself(Long internalKey, String baseAcctNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("internalKey", internalKey.toString());
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getSelfAcctAndMyself", param);
   }

   public List<RbAgreementYht> getInvalidByParent(Long parentInternalKey, String baseAcctNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("parentInternalKey", parentInternalKey.toString());
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getInvalidByParent", param);
   }

   public List<RbAgreementYht> getOldBaseAcctNoInfo(String baseAcctNo, String prodType, String ccy, String seqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", seqNo);
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getOldBaseAcctNoInfo", param);
   }

   public RbAgreementYht getSelfAgreementYht(String mainInternalKey, String clientNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("parentInternalKey", mainInternalKey);
      param.put("clientNo", clientNo);
      return (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getSelfAgreementYht", param);
   }

   public List<RbAgreementYht> getSonListByInternalKey(String internalKey, String clientNo, String lawImpRule) {
      Map<String, Object> param = new HashMap(1);
      new ArrayList();
      param.put("parentInternalKey", internalKey.toString());
      param.put("clientNo", clientNo);
      param.put("lawImpRule", lawImpRule);
      List<RbAgreementYht> list = this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getSonListByInternalKey", param);
      return list;
   }

   public RbAgreementYht getOpenMbAgreementYht(String baseAcctNo, String prodType, String ccy, String seqNo, String clientNo, String acctOrgSchema) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      param.put("acctOrgSchema", acctOrgSchema);
      return (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getOpenMbAgreementYht", param);
   }

   public List<RbAgreementYht> getBranchMbAgreementYht(Map<String, Object> param) {
      return this.daoSupport.selectList(RbAgreementYht.class.getName() + ".getBranchMbAgreementYht", param);
   }

   public RbAgreementYht getMbAgreementYhtE(String baseAcctNo, String prodType, String ccy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", "E");
      return (RbAgreementYht)this.daoSupport.selectOne(RbAgreementYht.class.getName() + ".getMbAgreementYhtE", param);
   }
}
