package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabAcctRestraint;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBill;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabInternalAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabLost;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabSettle;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbBabRepository extends BusinessRepository {
   public RbBabRegister selectRegisterByAcceptContractNo(String acceptContractNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      return (RbBabRegister)this.daoSupport.selectOne(RbBabRegister.class.getName() + ".selectByAcceptContractNo", para);
   }

   public void updatePaymentFlag(String acceptContractNo, String clientNo, String paymentFlag) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("clientNo", clientNo);
      para.put("paymentFlag", paymentFlag);
      para.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabRegister.class.getName() + ".updatePaymentFlag", para);
   }

   public List<RbBabSettle> selectListByAcceptContractNo(String acceptContractNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      return this.daoSupport.selectList(RbBabSettle.class.getName() + ".selectListByAcceptContractNo", para);
   }

   public List<RbBabAcctRestraint> selectRbBabAcct(Long internalKey) {
      Map<String, Object> para = new HashMap(16);
      para.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbBabAcctRestraint.class.getName() + ".selectList", para);
   }

   public RbBabSettle selectRbBabSettleBySettleClass(String acceptContractNo, String settleClass) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("settleClass", settleClass);
      return (RbBabSettle)this.daoSupport.selectOne(RbBabSettle.class.getName() + ".selectRbBabSettleBySettleClass", para);
   }

   public List<RbBabSettle> selectSettleListBySettleClass(String acceptContractNo, String settleClass) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("settleClass", settleClass);
      return this.daoSupport.selectList(RbBabSettle.class.getName() + ".selectSettleListBySettleClass", para);
   }

   public void deleteByacceptContractNo(String acceptContractNo, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("clientNo", clientNo);
      this.daoSupport.delete(RbBabSettle.class.getName() + ".deleteByacceptContractNo", para);
   }

   public RbBabInternalAcct selectByClientNo(String openBranch, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("openBranch", openBranch);
      para.put("clientNo", clientNo);
      return (RbBabInternalAcct)this.daoSupport.selectOne(RbBabInternalAcct.class.getName() + ".selectByClientNo", para);
   }

   public List<RbBabBill> selectRbBabBillList(String acceptContractNo, String billDocType, String billPrefix, String billType, String billVoucherNo, String clientNo, String payBranch) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      para.put("clientNo", clientNo);
      para.put("payBranch", payBranch);
      return this.daoSupport.selectList(RbBabBill.class.getName() + ".selectList", para);
   }

   public RbBabBill selectRbBabBillOne(String acceptContractNo, String billDocType, String billPrefix, String billType, String billVoucherNo, String clientNo, String payBranch) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      para.put("clientNo", clientNo);
      para.put("payBranch", payBranch);
      return (RbBabBill)this.daoSupport.selectOne(RbBabBill.class.getName() + ".selectOne", para);
   }

   public RbBabBill selectRbBabBillOne(String acceptContractNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      return (RbBabBill)this.daoSupport.selectOne(RbBabBill.class.getName() + ".selectOne", para);
   }

   public void updateRbBabBillByPrimary(String oldBillDocType, String oldBillType, String oldBillVoucherNo, String newBillDocType, String newBillType, String newBillVoucherNo, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("oldBillDocType", oldBillDocType);
      para.put("oldBillType", oldBillType);
      para.put("oldBillVoucherNo", oldBillVoucherNo);
      para.put("newBillDocType", newBillDocType);
      para.put("newBillType", newBillType);
      para.put("newBillVoucherNo", newBillVoucherNo);
      para.put("clientNo", clientNo);
      para.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateByBillInfo", para);
   }

   public void updateStatusByBillNo(String billDocType, String billVoucherNo, String billStatus, String acceptStatus, String acceptContractNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("billDocType", billDocType);
      para.put("billVoucherNo", billVoucherNo);
      para.put("billStatus", billStatus);
      para.put("acceptStatus", acceptStatus);
      para.put("acceptContractNo", acceptContractNo);
      para.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateStatusByBillNo", para);
   }

   public List<RbBabBill> selectRbBabBillForCancel(String acceptContractNo, String billDocType, String billPrefix, String billType, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      return this.daoSupport.selectList(RbBabBill.class.getName() + ".selectRbBabBillForCancel", para);
   }

   public RbBabBill selectRbBabBillByVoucher(String acceptContractNo, String billDocType, String billPrefix, String billType, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      return (RbBabBill)this.daoSupport.selectOne(RbBabBill.class.getName() + ".selectRbBabBillByVoucher", para);
   }

   public void updateStatusByVoucherNo(RbBabBill rbBabBill) {
      rbBabBill.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateStatusByVoucherNo", rbBabBill);
   }

   public void updateAcceptStatusByVoucherNo(RbBabBill rbBabBill) {
      rbBabBill.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateAcceptStatusByVoucherNo", rbBabBill);
   }

   public void updateByVoucherNo(RbBabBill rbBabBill) {
      rbBabBill.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateByVoucherNo", rbBabBill);
   }

   public void updateResNoToNull(String billType, String billDocType, String billVoucherNo, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("billType", billType);
      para.put("billDocType", billDocType);
      para.put("billVoucherNo", billVoucherNo);
      para.put("clientNo", clientNo);
      para.put("tranTimestamp", clientNo);
      this.daoSupport.update(RbBabBill.class.getName() + ".updateResNoToNull", para);
   }

   public List<RbBabBill> selectRbBabBill(String acceptContractNo, String billDocType, String billPrefix, String billType, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      return this.daoSupport.selectList(RbBabBill.class.getName() + ".selectRbBabBill", para);
   }

   public RbBabAcctRestraint selectByResSeqNo(String resSeqNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("resSeqNo", resSeqNo);
      return (RbBabAcctRestraint)this.daoSupport.selectOne(RbBabAcctRestraint.class.getName() + ".selectByResSeqNo", para);
   }

   public RbBabAcctRestraint selectByResVoucherNo(String billDocType, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("billDocType", billDocType);
      para.put("billVoucherNo", billVoucherNo);
      return (RbBabAcctRestraint)this.daoSupport.selectOne(RbBabAcctRestraint.class.getName() + ".selectByResVoucherNo", para);
   }

   public RbBabAcctRestraint selectByPrimaryKeyAndClientNo(String acceptContractNo, Long internalKey, String resSeqNo, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("resSeqNo", resSeqNo);
      para.put("clientNo", clientNo);
      para.put("internalKey", internalKey);
      para.put("acceptContractNo", acceptContractNo);
      return (RbBabAcctRestraint)this.daoSupport.selectOne(RbBabAcctRestraint.class.getName() + ".selectByPrimaryKeyAndClientNo", para);
   }

   public void deleteByResSeqNoAndClientNo(String resSeqNo, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("resSeqNo", resSeqNo);
      para.put("clientNo", clientNo);
      this.daoSupport.delete(RbBabAcctRestraint.class.getName() + ".deleteByResSeqNoAndClientNo", para);
   }

   public void deleteByBaseAcctNoAndClientNo(String acceptContractNo, String baseAcctNo, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("baseAcctNo", baseAcctNo);
      para.put("clientNo", clientNo);
      this.daoSupport.delete(RbBabAcctRestraint.class.getName() + ".deleteByBaseAcctNoAndClientNo", para);
   }

   public void deleteByVoucherBaseAcctNoAndClientNo(String acceptContractNo, String baseAcctNo, String clientNo, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("baseAcctNo", baseAcctNo);
      para.put("clientNo", clientNo);
      para.put("billVoucherNo", billVoucherNo);
      this.daoSupport.delete(RbBabAcctRestraint.class.getName() + ".deleteByVoucherBaseAcctNoAndClientNo", para);
   }

   public List<RbBabAcctRestraint> selectRestraintByAcceptContractNo(String acceptContractNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      return this.daoSupport.selectList(RbBabAcctRestraint.class.getName() + ".selectByAcceptContractNo", para);
   }

   public List<RbBabAcctRestraint> selectRestraintByAcct(String baseAcctNo, String prodType, String acctSeqNo, String ccy) {
      Map<String, Object> para = new HashMap(16);
      para.put("baseAcctNo", baseAcctNo);
      para.put("prodType", prodType);
      para.put("acctSeqNo", acctSeqNo);
      para.put("ccy", ccy);
      return this.daoSupport.selectList(RbBabAcctRestraint.class.getName() + ".selectRestraintByAcct", para);
   }

   public void updateByBillLostNo(String billStatus, String lostNo) {
      Map<String, Object> para = new HashMap();
      para.put("billStatus", billStatus);
      para.put("lostNo", lostNo);
      para.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateByBillLostNo", para);
   }

   public RbBabBill queryRbBabBillByLostNo(String lostNo) {
      Map<String, Object> para = new HashMap();
      para.put("lostNo", lostNo);
      List<RbBabBill> rbBabBills = this.daoSupport.selectList(RbBabBill.class.getName() + ".selectByLostNo", para);
      if (BusiUtil.isNull(rbBabBills)) {
         throw BusiUtil.createBusinessException("AD4147", new String[]{lostNo});
      } else {
         RbBabBill rbBabBill = null;
         rbBabBill = (RbBabBill)rbBabBills.get(0);
         return rbBabBill;
      }
   }

   public void updateBillStatus(RbBabBill rbBabBill) {
      rbBabBill.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateBillStatus", rbBabBill);
   }

   public void updateBillLostNoToNull(RbBabBill rbBabBill) {
      rbBabBill.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateBillLostNoToNull", rbBabBill);
   }

   public void updateRbBabLostStatus(RbBabLost rbBabLost) {
      rbBabLost.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabLost.class.getName() + ".updateRbBabLostStatus", rbBabLost);
   }

   public RbBabBill selectRbBabBillByVoucherNo(String billDocType, String billPrefix, String billType, String billVoucherNo, String billStatus, String acceptStatus) {
      Map<String, Object> para = new HashMap(16);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      para.put("billStatus", billStatus);
      para.put("acceptStatus", acceptStatus);
      return (RbBabBill)this.daoSupport.selectOne(RbBabBill.class.getName() + ".selectRbBabBillByVoucherNo", para);
   }

   public RbBabLost selectRbBabLostByVoucherNo(String billDocType, String billPrefix, String billType, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      return (RbBabLost)this.daoSupport.selectOne(RbBabLost.class.getName() + ".selectRbBabLostByVoucherNo", para);
   }

   public RbBabLost selectRbBabLostByLostNo(String lostNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("lostNo", lostNo);
      return (RbBabLost)this.daoSupport.selectOne(RbBabLost.class.getName() + ".selectByLostNo", para);
   }

   public List<RbBabBill> selectRbBabBillForCancelByChannelSeqNo(String channelSeqNo, String subSeqNo, String channelDate) {
      Map<String, Object> para = new HashMap(16);
      para.put("channelSeqNo", channelSeqNo);
      para.put("subSeqNo", subSeqNo);
      para.put("channelDate", DateUtil.parseDate(channelDate));
      return this.daoSupport.selectList(RbBabBill.class.getName() + ".selectRbBabBillForCancelByChannelSeqNo", para);
   }

   public List<RbBabBill> selectRbBabBillNormal(String acceptContractNo, String billDocType, String billPrefix, String billType, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      return this.daoSupport.selectList(RbBabBill.class.getName() + ".selectRbBabBillNormal", para);
   }

   public void updateStatusByAcceptContractNo(String newBillStatus, String newAcceptStatus, String oldBillStatus, String oldAcceptStatus, String acceptContractNo, String clientNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("newBillStatus", newBillStatus);
      para.put("newAcceptStatus", newAcceptStatus);
      para.put("oldBillStatus", oldBillStatus);
      para.put("oldAcceptStatus", oldAcceptStatus);
      para.put("acceptContractNo", acceptContractNo);
      para.put("clientNo", clientNo);
      para.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBill.class.getName() + ".updateStatusByAcceptContractNo", para);
   }

   public List<RbBabBill> selectRbBabBillListByVoucherNo(String billDocType, String billPrefix, String billType, String billVoucherNo, String billStatus, String acceptStatus) {
      Map<String, Object> para = new HashMap(16);
      para.put("billDocType", billDocType);
      para.put("billPrefix", billPrefix);
      para.put("billType", billType);
      para.put("billVoucherNo", billVoucherNo);
      para.put("billStatus", billStatus);
      para.put("acceptStatus", acceptStatus);
      return this.daoSupport.selectList(RbBabBill.class.getName() + ".selectRbBabBillByVoucherNo", para);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertRbBabRegister(RbBabInternalAcct rbBabInternalAcct) {
      super.insert(rbBabInternalAcct);
   }
}
