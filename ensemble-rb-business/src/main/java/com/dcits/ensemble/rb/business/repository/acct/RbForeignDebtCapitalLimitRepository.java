package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbForeignDebtCapitalLimit;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbForeignDebtCapitalLimitRepository extends BusinessRepository {
   public RbForeignDebtCapitalLimit getByTraceNo(String traceNo) {
      RbForeignDebtCapitalLimit rbForeignDebtCapitalLimit = new RbForeignDebtCapitalLimit();
      rbForeignDebtCapitalLimit.setTradeNo(traceNo);
      return (RbForeignDebtCapitalLimit)this.daoSupport.selectOne(rbForeignDebtCapitalLimit);
   }

   public List<RbForeignDebtCapitalLimit> getRbForeignDebtCapitalLimit(String tradeNo, String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("tradeNo", tradeNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("status", "A");
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbForeignDebtCapitalLimit.class.getName() + ".getRbForeignDebtCapitalLimit";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbForeignDebtCapitalLimit> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }
}
