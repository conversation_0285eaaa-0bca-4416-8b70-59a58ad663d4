package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLostDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbVoucherLostDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherLostDetailRepository.class);

   public List<RbVoucherLostDetail> selectListByPrimaryKey(Long internalKey, String lostNo, String seqNo) {
      RbVoucherLostDetail rbVoucherLostDetail = new RbVoucherLostDetail();
      rbVoucherLostDetail.setLostKey(String.valueOf(internalKey));
      rbVoucherLostDetail.setLostNo(lostNo);
      rbVoucherLostDetail.setSeqNo(seqNo);
      return this.daoSupport.selectList(rbVoucherLostDetail);
   }

   public RbVoucherLostDetail selectByPrimaryKey(Long lostKey, String lostNo, String seqNo, String clientNo) {
      RbVoucherLostDetail rbVoucherLostDetail = new RbVoucherLostDetail();
      rbVoucherLostDetail.setLostKey(String.valueOf(lostKey));
      rbVoucherLostDetail.setLostNo(lostNo);
      rbVoucherLostDetail.setSeqNo(seqNo);
      rbVoucherLostDetail.setClientNo(clientNo);
      return (RbVoucherLostDetail)this.daoSupport.selectOne(rbVoucherLostDetail);
   }

   public RbVoucherLostDetail selectByDocType(String lostKey, String docType, String prefix, String voucherNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", lostKey);
      param.put("docType", docType);
      if (BusiUtil.isNotNull(prefix)) {
         param.put("prefix", prefix);
      }

      param.put("voucherStartNo", voucherNo);
      param.put("clientNo", clientNo);
      return (RbVoucherLostDetail)this.daoSupport.selectOne(RbVoucherLostDetail.class.getName() + ".selectByDocType", param);
   }

   public List<RbVoucherLostDetail> selectDetailList(Long internalKey, String lostNo, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", internalKey);
      param.put("lostNo", lostNo);
      param.put("seqNo", seqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherLostDetail.class.getName() + ".selectDetailList", param);
   }

   public RbVoucherLostDetail getLostDetailByLostNo(String lostNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostNo", lostNo);
      param.put("clientNo", clientNo);
      return (RbVoucherLostDetail)this.daoSupport.selectOne(RbVoucherLostDetail.class.getName() + ".getLostDetailByLostNo", param);
   }

   public RbVoucherLostDetail getLostDetailByDocType(Long internalKey, String docType, String seqNo, String voucherStatus, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", internalKey);
      param.put("docType", docType);
      param.put("seqNo", seqNo);
      param.put("lostStatus", voucherStatus);
      param.put("clientNo", clientNo);
      return (RbVoucherLostDetail)this.daoSupport.selectOne(RbVoucherLostDetail.class.getName() + ".getLostDetailByDocType", param);
   }

   public void updateByInternal(Long internalKey, String status) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", internalKey);
      param.put("status", status);
      this.daoSupport.update(RbVoucherLostDetail.class.getName() + ".updateByInternal", param);
   }

   public void createMbVoucherLostDetailDb(RbVoucherLostDetail mbVoucherLostDetail) {
      if (BusiUtil.isNotNull(mbVoucherLostDetail)) {
         super.insert(mbVoucherLostDetail);
      }

   }

   public void updateMbVoucherLostDetailDb(RbVoucherLostDetail mbVoucherLostDetail) {
      if (BusiUtil.isNotNull(mbVoucherLostDetail)) {
         super.update(mbVoucherLostDetail);
      }

   }

   public void updateByLostNo(String lostNo, String status, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostNo", lostNo);
      param.put("status", status);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbVoucherLostDetail.class.getName() + ".updateByLostNo", param);
   }
}
