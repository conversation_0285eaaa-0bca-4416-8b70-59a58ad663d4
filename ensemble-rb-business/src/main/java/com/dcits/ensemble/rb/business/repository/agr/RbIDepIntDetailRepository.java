package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIntDetail;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIntDetailHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbIDepIntDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbIDepIntDetailRepository.class);

   public void backInsert(RbIdepIntDetail mbIDepIntDetail) {
      RbIdepIntDetailHist rbIDepIntDetailHist = new RbIdepIntDetailHist();
      BeanUtil.copy(mbIDepIntDetail, rbIDepIntDetailHist);
      super.insert(rbIDepIntDetailHist);
   }

   public List<RbIdepIntDetailHist> getIntDetailHistByDayNum(Map<String, Object> map) {
      return this.daoSupport.selectList(RbIdepIntDetailHist.class.getName() + ".getIntDetailHistByDayNum", map);
   }

   public RbIdepIntDetail getMbIDepIntDetail(long internalKey, String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("agreementId", agreementId);
      return (RbIdepIntDetail)this.daoSupport.selectOne(RbIdepIntDetail.class.getName() + ".selectByPrimaryKey", param);
   }

   public void createMbIDepIntDetail(RbIdepIntDetail mbIDepIntDetail) {
      super.insert(mbIDepIntDetail);
   }
}
