package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDefend;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbChequeDefendRepository extends BusinessRepository<RbChequeDefend> {
   private static final Logger log = LoggerFactory.getLogger(RbChequeDefendRepository.class);

   public RbChequeDefend quiry(String docType, String voucherPrefix, String voucherNo) {
      RbChequeDefend rbChequeDefend = new RbChequeDefend();
      rbChequeDefend.setDocType(docType);
      rbChequeDefend.setVoucherPrefix(voucherPrefix);
      rbChequeDefend.setVoucherNo(voucherNo);
      rbChequeDefend.setIssueBranchFlag("0");
      return (RbChequeDefend)this.daoSupport.selectOne(rbChequeDefend);
   }

   public void addChequeDefend(RbChequeDefend rbChequeDefend) {
      rbChequeDefend.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.insert(rbChequeDefend);
   }

   public void updChequeDefend(RbChequeDefend rbChequeDefend) {
      rbChequeDefend.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbChequeDefend.class.getName() + ".updChequeDefend", rbChequeDefend);
   }

   public List<RbChequeDefend> getChequeDefends(String promptAcctNo, String promptSeqNo, String issueBranchFlag, String docType, String voucherPrefix, String voucherNo, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("promptAcctNo", promptAcctNo);
      param.put("promptSeqNo", promptSeqNo);
      param.put("issueBranchFlag", issueBranchFlag);
      param.put("docType", docType);
      param.put("voucherPrefix", voucherPrefix);
      param.put("voucherNo", voucherNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbChequeDefend.class.getName() + ".getChequeDefends", param);
   }

   public List<RbChequeDefend> quiryDefendList(String docType, String voucherPrefix, String voucherNo) {
      RbChequeDefend rbChequeDefend = new RbChequeDefend();
      rbChequeDefend.setDocType(docType);
      rbChequeDefend.setVoucherPrefix(voucherPrefix);
      rbChequeDefend.setVoucherNo(voucherNo);
      return this.daoSupport.selectList(rbChequeDefend);
   }
}
