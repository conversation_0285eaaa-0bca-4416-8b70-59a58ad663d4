package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPreference;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAgreementPreferenceRepository extends BusinessRepository {
   public List<RbAgreementPreference> getAgreementPreferenceByAgreementId(String agreementId) {
      RbAgreementPreference map = new RbAgreementPreference();
      map.setAgreementId(agreementId);
      return this.daoSupport.selectList(RbAgreementPreference.class.getName() + ".getAgreementPreferenceByAgreementId", map);
   }

   public List<RbAgreementPreference> getMbAgrePreByagreId(String agreementId, String preferenceType) {
      Map<String, Object> map = new HashMap();
      map.put("agreementId", agreementId);
      map.put("preferenceType", preferenceType);
      return this.daoSupport.selectList(RbAgreementPreference.class.getName() + ".getMbAgrePreByagreId", map);
   }

   public void deleteByAgreementId(String agreementId) {
      Map<String, Object> map = new HashMap();
      map.put("agreementId", agreementId);
      this.daoSupport.delete(RbAgreementPreference.class.getName() + ".deleteByAgreementId", map);
   }
}
