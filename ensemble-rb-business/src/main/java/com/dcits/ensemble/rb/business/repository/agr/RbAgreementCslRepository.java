package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementCsl;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementCslRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementCslRepository.class);

   public void createMbAgreementCslDb(RbAgreementCsl agreementCsl) {
      if (BusiUtil.isNotNull(agreementCsl)) {
         super.insert(agreementCsl);
      }

   }

   public void updateAgreementCsl(RbAgreementCsl rbAgreementCsl) {
      super.update(rbAgreementCsl);
   }

   public RbAgreementCsl selectByCondition(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String status) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementStatus", status);
      return (RbAgreementCsl)this.daoSupport.selectOne(RbAgreementCsl.class.getName() + ".selectByCondition", param);
   }

   public RbAgreementCsl selectByCondition(String baseAcctNo, String prodType, String acctCcy, String status) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("agreementStatus", status);
      return (RbAgreementCsl)this.daoSupport.selectOne(RbAgreementCsl.class.getName() + ".selectByCondition", param);
   }
}
