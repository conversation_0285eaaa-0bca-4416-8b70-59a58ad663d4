package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRecInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbDepositCertRecInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbDepositCertRecInfoRepository.class);

   public void creatembdepositcertinfodb(List<RbDepositCertRecInfo> rbDepositCertRecInfosList) {
      if (BusiUtil.isNotNull(rbDepositCertRecInfosList)) {
         Iterator var2 = rbDepositCertRecInfosList.iterator();

         while(var2.hasNext()) {
            RbDepositCertRecInfo rbDepositCertRecInfo = (RbDepositCertRecInfo)var2.next();
            super.insert(rbDepositCertRecInfo);
         }
      }

   }

   public void updatembdepositcertinfodb(List<RbDepositCertRecInfo> rbDepositCertRecInfosList) {
      if (BusiUtil.isNotNull(rbDepositCertRecInfosList)) {
         Iterator var2 = rbDepositCertRecInfosList.iterator();

         while(var2.hasNext()) {
            RbDepositCertRecInfo rbDepositCertRecInfo = (RbDepositCertRecInfo)var2.next();
            this.daoSupport.update(RbDepositCertRecInfo.class.getName() + ".updateByDepositCertNo", rbDepositCertRecInfo);
         }
      }

   }

   public RbDepositCertRecInfo createSeq() {
      RbDepositCertRecInfo mbDepositCertVoucherDtl = new RbDepositCertRecInfo();
      mbDepositCertVoucherDtl.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbDepositCertVoucherDtlSeqNo));
      return mbDepositCertVoucherDtl;
   }

   public RbDepositCertRecInfo getDepositCertInfo(String mbDepositCertNo, String clientNo, String depositCertOperateType) {
      Map<String, Object> param = new HashMap();
      param.put("mbDepositCertNo", mbDepositCertNo);
      param.put("clientNo", clientNo);
      param.put("depositCertOperateType", depositCertOperateType);
      return (RbDepositCertRecInfo)this.daoSupport.selectOne(RbDepositCertRecInfo.class.getName() + ".getDepositCertRecInfo", param);
   }
}
