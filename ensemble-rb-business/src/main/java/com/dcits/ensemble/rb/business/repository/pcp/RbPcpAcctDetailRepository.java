package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbPcpAcctDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpAcctDetailRepository.class);

   public void creatMbPcpAcctDetail(List<RbPcpAcctDetail> mbPcpAcctDetails) {
      Iterator var2 = mbPcpAcctDetails.iterator();

      while(var2.hasNext()) {
         RbPcpAcctDetail mbPcpAcctDetail = (RbPcpAcctDetail)var2.next();
         mbPcpAcctDetail.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbPcpAcctDetailseqNo));
         super.insert(mbPcpAcctDetail);
      }

   }

   public void updateMbPcpAcctDetail(List<RbPcpAcctDetail> mbPcpAcctDetails) {
      Iterator var2 = mbPcpAcctDetails.iterator();

      while(var2.hasNext()) {
         RbPcpAcctDetail mbPcpAcctDetail = (RbPcpAcctDetail)var2.next();
         super.update(mbPcpAcctDetail);
      }

   }

   public void updateMbPcpAcctDetailA(RbPcpAcctDetail mbPcpAcctDetail) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", mbPcpAcctDetail.getInternalKey());
      RbPcpAcctDetail mbPcpAcctDetail1Old = (RbPcpAcctDetail)this.daoSupport.selectOne(RbPcpAcctDetail.class.getName() + ".selectByInternalKey", param);
      if (BusiUtil.isNotNull(mbPcpAcctDetail1Old)) {
         mbPcpAcctDetail.setSeqNo(mbPcpAcctDetail1Old.getSeqNo());
         super.update(mbPcpAcctDetail);
      } else {
         mbPcpAcctDetail.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbPcpAcctDetailseqNo));
         super.insert(mbPcpAcctDetail);
      }

   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updatePcpAcctDetail(RbPcpAcctDetail mbPcpAcctDetail) {
      this.daoSupport.update(mbPcpAcctDetail);
   }

   public void updateMbPcpAcctDetailE(Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      RbPcpAcctDetail mbPcpAcctDetail = (RbPcpAcctDetail)this.daoSupport.selectOne(RbPcpAcctDetail.class.getName() + ".selectByInternalKey", param);
      if (BusiUtil.isNotNull(mbPcpAcctDetail)) {
         mbPcpAcctDetail.setPcpAcctStatus("E");
         super.update(mbPcpAcctDetail);
      }

   }

   public void deleteMbPcpAcctDetail(List<RbPcpAcctDetail> mbPcpAcctDetails) {
      Iterator var2 = mbPcpAcctDetails.iterator();

      while(var2.hasNext()) {
         RbPcpAcctDetail mbPcpAcctDetail = (RbPcpAcctDetail)var2.next();
         this.daoSupport.delete(mbPcpAcctDetail);
      }

   }

   public RbPcpAcctDetail selectByAgreementIdAndAcct(String agreementId, String baseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setBaseAcctNo(baseAcctNo);
      mbPcpAcctDetail.setProdType(prodType);
      mbPcpAcctDetail.setAcctCcy(ccy);
      mbPcpAcctDetail.setAcctSeqNo(acctSeqNo);
      mbPcpAcctDetail.setAgreementId(agreementId);
      mbPcpAcctDetail.setClientNo(clientNo);
      mbPcpAcctDetail.setPcpAcctStatus("A");
      return (RbPcpAcctDetail)this.daoSupport.selectOne(mbPcpAcctDetail);
   }

   public List<RbPcpAcctDetail> selectByAgreementId(String agreenemtId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreenemtId);
      return this.daoSupport.selectList(RbPcpAcctDetail.class.getName() + ".selectByAgreementId", param);
   }

   public List<RbPcpAcctDetail> selectByAgreementIdAndStatus(String agreenemtId, String status) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreenemtId);
      param.put("status", status);
      return this.daoSupport.selectList(RbPcpAcctDetail.class.getName() + ".selectByAgreementIdAndStatus", param);
   }

   public List<RbPcpAcctDetail> selectByAgreementId(String agreementId, String agreementFlag) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("pcpAgreementFlag", agreementFlag);
      return this.daoSupport.selectList(RbPcpAcctDetail.class.getName() + ".selectByAgreementIdAndFlag", param);
   }

   public RbPcpAcctDetail selectByPrimaryKey(String seqNo) {
      RbPcpAcctDetail rbPcpAcctDetail = new RbPcpAcctDetail();
      rbPcpAcctDetail.setSeqNo(seqNo);
      return (RbPcpAcctDetail)this.daoSupport.selectOne(rbPcpAcctDetail);
   }

   public RbPcpAcctDetail getMbPcpAcctDetalbyIKeyAndAgId(String agreementId, Long internalKey, String clientNo) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setAgreementId(agreementId);
      mbPcpAcctDetail.setInternalKey(internalKey);
      mbPcpAcctDetail.setClientNo(clientNo);
      mbPcpAcctDetail.setPcpAcctStatus("A");
      return (RbPcpAcctDetail)this.daoSupport.selectOne(mbPcpAcctDetail);
   }

   public RbPcpAcctDetail getMbPcpAcctDetalbyIKeyAndAgId1(String agreementId, Long internalKey, String clientNo) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setAgreementId(agreementId);
      mbPcpAcctDetail.setInternalKey(internalKey);
      mbPcpAcctDetail.setClientNo(clientNo);
      return (RbPcpAcctDetail)this.daoSupport.selectOne(mbPcpAcctDetail);
   }

   public RbPcpAcctDetail selectByAcct(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setBaseAcctNo(baseAcctNo);
      mbPcpAcctDetail.setProdType(prodType);
      mbPcpAcctDetail.setAcctCcy(ccy);
      mbPcpAcctDetail.setAcctSeqNo(acctSeqNo);
      mbPcpAcctDetail.setClientNo(clientNo);
      mbPcpAcctDetail.setPcpAcctStatus("A");
      return (RbPcpAcctDetail)this.daoSupport.selectOne(mbPcpAcctDetail);
   }

   public RbPcpAcctDetail getPcpDetailByInfo(String baseAcctNo, String acctSeqNo, String prodType, String acctCcy) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      return (RbPcpAcctDetail)this.daoSupport.selectOne(RbPcpAcctDetail.class.getName() + ".getPcpDetailByInfo", param);
   }

   public RbPcpAcctDetail selectByAcctAndStatus(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo, String status) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setBaseAcctNo(baseAcctNo);
      mbPcpAcctDetail.setProdType(prodType);
      mbPcpAcctDetail.setAcctCcy(ccy);
      mbPcpAcctDetail.setAcctSeqNo(acctSeqNo);
      mbPcpAcctDetail.setClientNo(clientNo);
      mbPcpAcctDetail.setPcpAcctStatus(status);
      return (RbPcpAcctDetail)this.daoSupport.selectOne(mbPcpAcctDetail);
   }

   public RbPcpAcctDetail selectByAcctAll(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setBaseAcctNo(baseAcctNo);
      mbPcpAcctDetail.setProdType(prodType);
      mbPcpAcctDetail.setAcctCcy(ccy);
      mbPcpAcctDetail.setAcctSeqNo(acctSeqNo);
      mbPcpAcctDetail.setClientNo(clientNo);
      mbPcpAcctDetail.setPcpAcctStatus("A");
      return (RbPcpAcctDetail)this.daoSupport.selectOne(mbPcpAcctDetail);
   }

   public List<RbPcpAcctDetail> selectListByInternalkey(Long internalkey, String clientNo) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setInternalKey(internalkey);
      mbPcpAcctDetail.setClientNo(clientNo);
      mbPcpAcctDetail.setPcpAcctStatus("A");
      return this.daoSupport.selectList(mbPcpAcctDetail);
   }

   public RbPcpAcctDetail selectByInternalkey(Long internalkey, String clientNo) {
      RbPcpAcctDetail mbPcpAcctDetail = new RbPcpAcctDetail();
      mbPcpAcctDetail.setInternalKey(internalkey);
      mbPcpAcctDetail.setClientNo(clientNo);
      mbPcpAcctDetail.setPcpAcctStatus("A");
      return (RbPcpAcctDetail)this.daoSupport.selectOne(mbPcpAcctDetail);
   }

   public List<RbPcpAcctDetail> selectEodPcpAcctDetail(Long startKay, Long endKay) {
      Map<String, Object> param = new HashMap(16);
      param.put("START_KEY", startKay);
      param.put("END_KEY", endKay);
      return this.daoSupport.selectList(RbPcpAcctDetail.class.getName() + ".selectEodPcpAcctDetail", param);
   }

   public RbPcpAcctDetail selectByinternalKeyAndStatus(String agreementId, Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("agreementId", agreementId);
      RbPcpAcctDetail mbPcpAcctDetail1 = (RbPcpAcctDetail)this.daoSupport.selectOne(RbPcpAcctDetail.class.getName() + ".getMbPcpAcctDetalbyIKeyAndAgId", param);
      return mbPcpAcctDetail1;
   }
}
