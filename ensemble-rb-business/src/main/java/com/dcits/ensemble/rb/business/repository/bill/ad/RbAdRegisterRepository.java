package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAdRegisterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAdRegisterRepository.class);

   public RbAdRegister queryAdRegisterByCrReference(String crReference, String reference) {
      new RbAdRegister();
      Map<String, Object> param = new HashMap(16);
      param.put("crReference", crReference);
      param.put("reference", reference);
      RbAdRegister rbAdRegister1 = (RbAdRegister)this.daoSupport.selectOne(RbAdRegister.class.getName() + ".queryAdRegisterByCrReference", param);
      if (BusiUtil.isNull(rbAdRegister1)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegister1;
      }
   }

   public RbAdRegister checkNullAdRegisterByCrReference(String crReference, String reference) {
      Map<String, Object> param = new HashMap(16);
      param.put("crReference", crReference);
      param.put("reference", reference);
      RbAdRegister rbAdRegister = (RbAdRegister)this.daoSupport.selectOne(RbAdRegister.class.getName() + ".queryAdRegisterByCrReference", param);
      return rbAdRegister;
   }

   public List<RbAdRegister> queryAdRegisterParam(String tranType, String billType, String billNo, String billMediumType, String acceptBranch) {
      Map<String, Object> param = new HashMap(16);
      param.put("tranType", tranType);
      param.put("billType", billType);
      param.put("billNo", billNo);
      param.put("billMediumType", billMediumType);
      param.put("acceptBranch", acceptBranch);
      List<RbAdRegister> rbAdRegisters = this.daoSupport.selectList(RbAdRegister.class.getName() + ".queryAdRegisterParam", param);
      if (BusiUtil.isNull(rbAdRegisters)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegisters;
      }
   }

   public RbAdRegister queryAdRegisterByBillInfo(String crReference, String billType, String billNo, String billMediumType) {
      Map<String, Object> param = new HashMap(16);
      param.put("crReference", crReference);
      param.put("billType", billType);
      param.put("billNo", billNo);
      param.put("billMediumType", billMediumType);
      RbAdRegister rbAdRegister = (RbAdRegister)this.daoSupport.selectOne(RbAdRegister.class.getName() + ".queryAdRegisterByBillInfo", param);
      if (BusiUtil.isNull(rbAdRegister)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegister;
      }
   }

   public List<RbAdRegister> getMbAdRegisterList(String branch, String acceptAcct, String billType, String billPrefix, String billNo, String status, String billStatus, String billMediumType, String payerClientNo, String payerName, String clientName) {
      Map<String, Object> param = new HashMap(16);
      param.put("agentBranch", branch);
      param.put("payerAcctNo", acceptAcct);
      param.put("billNo", billNo);
      param.put("billType", billType);
      param.put("billPrefix", billPrefix);
      param.put("acceptStatus", status);
      param.put("billStatus", billStatus);
      param.put("billMediumType", billMediumType);
      param.put("clientNo", payerClientNo);
      param.put("payerName", payerName);
      param.put("clientName", clientName);
      List<RbAdRegister> rbAdRegisters = this.daoSupport.selectList(RbAdRegister.class.getName() + ".selectAdRegisterList", param);
      if (BusiUtil.isNull(rbAdRegisters)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegisters;
      }
   }

   public List<RbAdRegister> getMbAdRegisterListByPage(String branch, String acceptAcct, String billType, String billPrefix, String billNo, String status, String billStatus, String billMediumType, String payerClientNo, String payerName, String clientName, String dateType, Date startDate, Date endDate) {
      Map<String, Object> param = new HashMap(16);
      String branchId = Context.getInstance().getBranchId();
      param.put("agentBranch", branch);
      param.put("payerAcctNo", acceptAcct);
      param.put("billNo", billNo);
      param.put("billType", billType);
      param.put("billPrefix", billPrefix);
      param.put("acceptStatus", status);
      param.put("billStatus", billStatus);
      param.put("billMediumType", billMediumType);
      param.put("clientNo", payerClientNo);
      param.put("payerName", payerName);
      param.put("clientName", clientName);
      param.put("userBranch", branchId);
      List<RbAdRegister> rbAdRegisters = this.daoSupport.selectList(RbAdRegister.class.getName() + ".selectAdRegisterListByPage", param);
      if (BusiUtil.isNull(rbAdRegisters)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegisters;
      }
   }

   public List<RbAdRegister> queryAdRegisterList(String billNo, String agentBranch, String status) {
      Map<String, Object> param = new HashMap(16);
      param.put("billNo", billNo);
      param.put("agentBranch", agentBranch);
      param.put("status", status);
      List<RbAdRegister> rbAdRegisters = this.daoSupport.selectList(RbAdRegister.class.getName() + ".queryAdRegisterList", param);
      if (BusiUtil.isNull(rbAdRegisters)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegisters;
      }
   }

   public RbAdRegister queryAdRegister(String tranType, String billType, String billNo, String billMediumType) {
      Map<String, Object> param = new HashMap(16);
      param.put("tranType", tranType);
      param.put("billType", billType);
      param.put("billNo", billNo);
      param.put("billMediumType", billMediumType);
      RbAdRegister rbAdRegister = (RbAdRegister)this.daoSupport.selectOne(RbAdRegister.class.getName() + ".queryAdRegisterOne", param);
      if (BusiUtil.isNull(rbAdRegister)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegister;
      }
   }

   public RbAdRegister queryAdRegisterByPrimaryKey(Long internalKey) {
      RbAdRegister rbAdRegister = new RbAdRegister();
      rbAdRegister.setInternalKey(internalKey);
      RbAdRegister rbAdRegister1 = (RbAdRegister)this.daoSupport.selectOne(rbAdRegister);
      if (BusiUtil.isNull(rbAdRegister1)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdRegister1;
      }
   }

   public RbAdRegister getMbAdRegisterOne(String reference, String billMediumType, String acceptStatus) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("billMediumType", billMediumType);
      param.put("acceptStatus", acceptStatus);
      return (RbAdRegister)this.daoSupport.selectOne(RbAdRegister.class.getName() + ".getMbAdRegister", param);
   }

   public RbAdRegister getByBillNoAndType(String billType, String billNo) {
      Map<String, Object> param = new HashMap();
      param.put("billType", billType);
      param.put("billNo", billNo);
      return (RbAdRegister)this.daoSupport.selectOne(RbAdRegister.class.getName() + ".getByBillNoAndType", param);
   }

   public RbAdRegister queryAdRegisterByLostNo(String lostNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostNo", lostNo);
      List<RbAdRegister> rbAdRegisters = this.daoSupport.selectList(RbAdRegister.class.getName() + ".selectByLostNo", param);
      if (BusiUtil.isNull(rbAdRegisters)) {
         throw BusiUtil.createBusinessException("AD4147", new String[]{lostNo});
      } else {
         RbAdRegister rbAdRegister = (RbAdRegister)rbAdRegisters.get(0);
         return rbAdRegister;
      }
   }

   public List<RbAdSettle> getMbAdRegisterResByParam(String billNo, String branch, String acceptStatus, String acceptContractNo, String acceptAcct, String billType, Date billMaturityDate, Date runDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("billNo", billNo);
      param.put("branch", branch);
      param.put("acceptStatus", acceptStatus);
      param.put("runDate", runDate);
      param.put("acceptContractNo", acceptContractNo);
      param.put("payerAcctNo", acceptAcct);
      param.put("billType", billType);
      param.put("billMaturityDate", billMaturityDate);
      return this.daoSupport.selectList(RbAdRegister.class.getName() + ".getAdRegisterParam", param);
   }

   public List<RbAdSettle> getMbAdRegisterResByPage(String billNo, String branch, String acceptStatus, String acceptContractNo, String acceptAcct, String billType, Date billMaturityDate, Date runDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("billNo", billNo);
      param.put("branch", branch);
      param.put("acceptStatus", acceptStatus);
      param.put("runDate", runDate);
      param.put("acceptContractNo", acceptContractNo);
      param.put("payerAcctNo", acceptAcct);
      param.put("billType", billType);
      param.put("billMaturityDate", billMaturityDate);
      return this.daoSupport.selectList(RbAdRegister.class.getName() + ".getAdRegisterPage", param);
   }

   public List<String> queryAdRegisterForKeys(Map param) {
      return this.daoSupport.selectList(RbAdRegister.class.getName() + ".queryAdRegisterForKeys", param);
   }

   public List<RbAdRegister> queryAdRegisiterForDetail(Map param) {
      return this.daoSupport.selectList(RbAdRegister.class.getName() + ".queryAdRegisiterForDetail", param);
   }

   public List<RbAdRegister> queryAdRegisterByTranDate(Date tranDate, String branch, String userId) {
      Map<String, Object> param = new HashMap(16);
      param.put("tranDate", tranDate);
      param.put("branch", branch);
      param.put("userId", userId);
      return this.daoSupport.selectList(RbAdRegister.class.getName() + ".queryAdRegisterByTranDate", param);
   }

   public List<String> queryMbAdRegisterByDateForKeys(Map param) {
      return this.daoSupport.selectList(RbAdRegister.class.getName() + ".queryMbAdRegisterByDateForKeys", param);
   }

   public List<RbAdRegister> queryMbAdRegisterByDateDetails(Map param) {
      return this.daoSupport.selectList(RbAdRegister.class.getName() + ".queryMbAdRegisterByDateDetails", param);
   }

   public void adRegisterInsert(RbAdRegister rbAdRegister) {
      if (BusiUtil.isNotNull(rbAdRegister)) {
         super.insert(rbAdRegister);
      }

   }

   public void updateMbAdRegister(Long internalKey, String acceptStatus, String advanceFlag) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("acceptStatus", acceptStatus);
      param.put("advanceFlag", advanceFlag);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAdRegister.class.getName() + ".updateAcceptStatus", param);
   }

   public void updateBookAcceptStatus(RbAdRegister rbAdRegister) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", rbAdRegister.getInternalKey());
      param.put("acceptStatus", rbAdRegister.getAcceptStatus());
      param.put("billType", rbAdRegister.getBillType());
      param.put("billNo", rbAdRegister.getBillNo());
      param.put("agentBranch", rbAdRegister.getAcceptBranch());
      param.put("acceptAcct", rbAdRegister.getAcceptBaseAcctNo());
      param.put("acceptAcctSeqNo", rbAdRegister.getAcceptAcctSeqNo());
      param.put("acceptAcctCcy", rbAdRegister.getAcceptAcctCcy());
      param.put("acceptProdeType", rbAdRegister.getAcceptProdType());
      param.put("acceptName", rbAdRegister.getAcceptName());
      param.put("acceptBankCode", rbAdRegister.getAcceptBankCode());
      param.put("acceptBankName", rbAdRegister.getAcceptBankName());
      param.put("acceptBankAddr", rbAdRegister.getAcceptBankAddr());
      param.put("billAcceptDate", rbAdRegister.getBillAcceptDate());
      param.put("billAcceptBank", rbAdRegister.getBillAcceptBank());
      param.put("billStatus", rbAdRegister.getBillStatus());
      param.put("operUserId", rbAdRegister.getOperUserId());
      param.put("acceptUser", rbAdRegister.getAcceptUserId());
      param.put("payeeBankCode", rbAdRegister.getPayeeBankCode());
      param.put("payeeBankName", rbAdRegister.getPayeeBankName());
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAdRegister.class.getName() + ".updateBookAcceptStatus", param);
   }

   public void updateMbAdRegisterDb(RbAdRegister rbAdRegister) {
      super.update(rbAdRegister);
   }

   public void updateBillStatus(Long internalKey, String billStatus, String lostNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("billStatus", billStatus);
      param.put("internalKey", internalKey);
      param.put("lostNo", lostNo);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAdRegister.class.getName() + ".updateBillStatus", param);
   }

   public void updateAcceptStatus(Long internalKey, String acceptStatus, String isOwnHolder, String advanceFlag, Date billCashDate, String billCashBank, String billCashUser) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("acceptStatus", acceptStatus);
      param.put("isOwnHolder", isOwnHolder);
      param.put("advanceFlag", advanceFlag);
      param.put("billCashDate", billCashDate);
      param.put("billCashBank", billCashBank);
      param.put("billCashUser", billCashUser);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAdRegister.class.getName() + ".updateAcceptStatus", param);
   }

   public void updateByPrimaryKey(RbAdRegister rbAdRegister) {
      super.update(rbAdRegister);
   }
}
