package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureRestraints;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAcctNatureRestraintsRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctNatureRestraintsRepository.class);

   public List<RbAcctNatureRestraints> getMbAcctNatResByAcctNa(String acctNature, String clientNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("acctNature", acctNature);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctNatureRestraints.class.getName() + ".getMbAcctNatResByAcctNa", param);
   }

   public List<RbAcctNatureRestraints> getMbAcctNatureRestraints(String acctNature) {
      Map<String, Object> param = new HashMap(1);
      param.put("acctNature", acctNature);
      return this.daoSupport.selectList(RbAcctNatureRestraints.class.getName() + ".getMbAcctNatResByAcctNa", param);
   }

   public RbAcctNatureRestraints selectByPrimaryKey(String acctNature, String restraintType) {
      Map<String, Object> param = new HashMap(2);
      param.put("acctNature", acctNature);
      param.put("restraintType", restraintType);
      return (RbAcctNatureRestraints)this.daoSupport.selectOne(RbAcctNatureRestraints.class.getName() + ".selectByPrimaryKey", param);
   }
}
