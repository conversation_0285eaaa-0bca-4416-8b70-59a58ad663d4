package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpLimitCumulative;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpLimitCumulativeRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpLimitCumulativeRepository.class);

   public void updateMbPcpLimit(RbPcpLimitCumulative mbPcpLimitCumulative) {
      if (log.isInfoEnabled()) {
         log.info("mbPcpLimitCumulative : [" + mbPcpLimitCumulative + "]");
      }

      if (BusiUtil.isNotNull(mbPcpLimitCumulative)) {
         Map<String, Object> para = new HashMap();
         para.put("internalKey", mbPcpLimitCumulative.getInternalKey().toString());
         para.put("limitType", mbPcpLimitCumulative.getLimitType());
         para.put("clientNo", mbPcpLimitCumulative.getClientNo());
         this.daoSupport.delete(RbPcpLimitCumulative.class.getName() + ".deleteMbPcpLimitCumulative", para);
         super.insert(mbPcpLimitCumulative);
      }

   }

   public void insertMbPcpLimitCumulative(RbPcpLimitCumulative mbPcpLimitCumulative) {
      if (log.isInfoEnabled()) {
         log.info("mbPcpLimitCumulative : [" + mbPcpLimitCumulative + "]");
      }

      if (BusiUtil.isNotNull(mbPcpLimitCumulative)) {
         super.insert(mbPcpLimitCumulative);
      }

   }

   public List<RbPcpLimitCumulative> getMbPcpLimitCumulative(Long internalKey) {
      if (log.isInfoEnabled()) {
         log.info("internalKey : [" + internalKey + "]");
      }

      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      List<RbPcpLimitCumulative> mbPcpLimitCumulatives = this.daoSupport.selectList(RbPcpLimitCumulative.class.getName() + ".getMbPcpLimitCumulative", map);
      return mbPcpLimitCumulatives;
   }

   public RbPcpLimitCumulative getMbPcpLimitCumulativeByLimitType(RbPcpLimitCumulative mbPcpLimitCumulative) {
      if (log.isInfoEnabled()) {
         log.info("mbPcpLimitCumulative : [" + mbPcpLimitCumulative + "]");
      }

      RbPcpLimitCumulative mbPcpLimitCumulativeInfo = (RbPcpLimitCumulative)this.daoSupport.selectOne(RbPcpLimitCumulative.class.getName() + ".getMbPcpLimitCumulativeByLimitType", mbPcpLimitCumulative);
      return mbPcpLimitCumulativeInfo;
   }

   public void deleteMbPcpLimitCumulative(RbPcpLimitCumulative mbPcpLimitCumulative) {
      if (BusiUtil.isNotNull(mbPcpLimitCumulative)) {
         Map<String, Object> para = new HashMap();
         para.put("internalKey", mbPcpLimitCumulative.getInternalKey().toString());
         para.put("limitType", mbPcpLimitCumulative.getLimitType());
         para.put("clientNo", mbPcpLimitCumulative.getClientNo());
         this.daoSupport.delete(RbPcpLimitCumulative.class.getName() + ".deleteMbPcpLimitCumulative", para);
      }

   }
}
