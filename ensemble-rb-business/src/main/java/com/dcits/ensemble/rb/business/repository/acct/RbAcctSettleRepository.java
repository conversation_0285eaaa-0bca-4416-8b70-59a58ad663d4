package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.bc.unit.acct.settle.AbstractRbSettleOperate;
import com.dcits.ensemble.rb.business.common.constant.BaseEvent;
import com.dcits.ensemble.rb.business.common.constant.SettleMethodEnum;
import com.dcits.ensemble.rb.business.common.constant.interest.AmtTypeEnum;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.common.util.SetUtil;
import com.dcits.ensemble.rb.business.convert.acct.RbAcctSettleConvert;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle;
import com.dcits.ensemble.rb.business.factory.settle.RbSettleOperateFactory;
import com.dcits.ensemble.rb.business.model.acct.settle.MbAcctSettleModel;
import com.dcits.ensemble.rb.business.model.acct.settle.SettleClassEnum;
import com.dcits.ensemble.rb.business.model.acct.settle.SettlePayRecIndEnum;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

@Repository
public class RbAcctSettleRepository extends BusinessRepository {
   @Resource
   private RbAcctRepository rbAcctRepository;

   public RbAcctSettle createAcctSettle(MbAcctSettleModel acctSettleModel) {
      AbstractRbSettleOperate rbSettleOperator = RbSettleOperateFactory.getRbSettleOperator(SettleClassEnum.valueOf(acctSettleModel.getSettleAcctClass()));
      rbSettleOperator.assembleRbSettleInfo(acctSettleModel);
      rbSettleOperator.assembleRbSettleAcctInfo(acctSettleModel);
      RbAcctSettle rbAcctSettle = RbAcctSettleConvert.INSTANCE.to(acctSettleModel);
      rbAcctSettle.setSettleNo((String)SequenceGenerator.nextValue(SequenceEnum.settleSeqNo, new String[]{Context.getInstance().getRunDate()}));
      SetUtil.nullSetDefault(rbAcctSettle, rbAcctSettle::getLastChangeDate, RbAcctSettle::setLastChangeDate, DateUtil.parseDate(Context.getInstance().getRunDate()));
      SetUtil.nullSetDefault(rbAcctSettle, rbAcctSettle::getLastChangeUserId, RbAcctSettle::setLastChangeUserId, Context.getInstance().getUserId());
      SetUtil.nullSetDefault(rbAcctSettle, rbAcctSettle::getCreateDate, RbAcctSettle::setCreateDate, DateUtil.parseDate(Context.getInstance().getRunDate()));
      return rbAcctSettle;
   }

   public void createRbAcctSettleDb(RbAcctSettle rbAcctSettle) {
      Context context = Context.getInstance();
      rbAcctSettle.setUserId(context.getUserId());
      rbAcctSettle.setLastChangeUserId(context.getUserId());
      rbAcctSettle.setLastChangeDate(DateUtil.parseDate(context.getRunDate()));
      rbAcctSettle.setCreateDate(DateUtil.parseDate(context.getRunDate()));
      RbAcctSettle acctSettle = new RbAcctSettle();
      acctSettle.setClientNo(rbAcctSettle.getClientNo());
      acctSettle.setInternalKey(rbAcctSettle.getInternalKey());
      acctSettle.setSettleAcctClass(rbAcctSettle.getSettleAcctClass());
      acctSettle.setAmtType(rbAcctSettle.getAmtType());
      acctSettle.setSettleMethod(rbAcctSettle.getSettleMethod());
      acctSettle.setPayRecInd(rbAcctSettle.getPayRecInd());
      acctSettle.setEventType(rbAcctSettle.getEventType());
      List<RbAcctSettle> rbAcctSettles = super.selectList(acctSettle);
      if (BusiUtil.isNotNull(rbAcctSettles)) {
         List<RbAcctSettle> settleList = (List)rbAcctSettles.stream().filter((e) -> {
            return BusiUtil.isEquals(e.getSettleAcctClass(), SettleClassEnum.REL.getCode());
         }).collect(Collectors.toList());
         if (BusiUtil.isNotNull(settleList)) {
            throw BusiUtil.createBusinessException("RB6821");
         }
      } else {
         RbAcct rbAcct = this.rbAcctRepository.getNotCloseRbAcctByPk(rbAcctSettle.getInternalKey(), rbAcctSettle.getClientNo());
         SetUtil.nullSetDefault(rbAcctSettle, rbAcctSettle::getSettleNo, RbAcctSettle::setSettleNo, SequenceGenerator.nextValue(SequenceEnum.settleSeqNo, new String[]{context.getRunDate()}));
         SetUtil.notNullSetByValue(rbAcctSettle, RbAcctSettle::setCompany, rbAcct.getCompany());
         super.insert(rbAcctSettle);
      }

   }

   public void createRbAcctSettleDbList(List<RbAcctSettle> rbAcctSettles) {
      if (!BusiUtil.isNull(rbAcctSettles)) {
         Iterator var2 = rbAcctSettles.iterator();

         while(var2.hasNext()) {
            RbAcctSettle rbAcctSettle = (RbAcctSettle)var2.next();
            SetUtil.nullSetDefault(rbAcctSettle, rbAcctSettle::getSettleNo, RbAcctSettle::setSettleNo, SequenceGenerator.nextValue(SequenceEnum.settleSeqNo, new String[]{Context.getInstance().getRunDate()}));
            super.insert(rbAcctSettle);
         }

      }
   }

   public void addAcctSettle(List<MbAcctSettleModel> rbAcctSettles, long internalKey) {
      if (BusiUtil.isNotNull(rbAcctSettles)) {
         Iterator var4 = rbAcctSettles.iterator();

         while(var4.hasNext()) {
            MbAcctSettleModel rbAcctSettle = (MbAcctSettleModel)var4.next();
            rbAcctSettle.setInternalKey(internalKey);
            RbAcctSettle acctSettle = this.createAcctSettle(rbAcctSettle);
            super.insert(acctSettle);
         }
      }

   }

   public RbAcctSettle deleteRbAcctSettle(MbAcctSettleModel rbAcctSettleModel) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(rbAcctSettleModel.getInternalKey());
      rbAcctSettle.setSettleNo(rbAcctSettleModel.getSettleNo());
      rbAcctSettle.setClientNo(rbAcctSettleModel.getClientNo());
      return rbAcctSettle;
   }

   public void deleteRbAcctSettleDbList(List<RbAcctSettle> rbAcctSettles) {
      Iterator var2 = rbAcctSettles.iterator();

      while(var2.hasNext()) {
         RbAcctSettle rbAcctSettle = (RbAcctSettle)var2.next();
         super.delete(rbAcctSettle);
      }

   }

   public void deleteRbAcctSettleDbListBySettleClass(List<RbAcctSettle> rbAcctSettles) {
      if (!BusiUtil.isNull(rbAcctSettles)) {
         Iterator var2 = rbAcctSettles.iterator();

         while(var2.hasNext()) {
            RbAcctSettle rbAcctSettle = (RbAcctSettle)var2.next();
            RbAcctSettle rbAcctSettleW = new RbAcctSettle();
            rbAcctSettleW.setClientNo(rbAcctSettle.getClientNo());
            rbAcctSettleW.setInternalKey(rbAcctSettle.getInternalKey());
            rbAcctSettleW.setSettleAcctClass(rbAcctSettle.getSettleAcctClass());
            super.deleteByCondition(RbAcctSettle.class.getName().concat(".deleteRbAcctSettleDbListBySettleClass"), rbAcctSettleW);
         }

      }
   }

   public void updateRbAcctSettle(RbAcctSettle rbAcctSettle) {
      super.update(rbAcctSettle);
   }

   public void updateRbAcctSettleDbList(List<RbAcctSettle> rbAcctSettles) {
      Iterator var2 = rbAcctSettles.iterator();

      while(var2.hasNext()) {
         RbAcctSettle rbAcctSettle = (RbAcctSettle)var2.next();
         super.update(rbAcctSettle);
      }

   }

   public void updateRbAcctSettleDbListBySettleClass(List<RbAcctSettle> rbAcctSettles) {
      if (!BusiUtil.isNull(rbAcctSettles)) {
         super.updateAddBatch(RbAcctSettle.class.getName().concat(".updateRbAcctSettleDbListBySettleClass"), rbAcctSettles);
      }
   }

   public void updAcctSettle(MbAcctSettleModel rbAcctSettleModel) {
      Context context = Context.getInstance();
      RbAcctSettle rbAcctSettle = RbAcctSettleConvert.INSTANCE.to(rbAcctSettleModel);
      rbAcctSettle.setLastChangeDate(DateUtil.parseDate(context.getRunDate()));
      rbAcctSettle.setLastChangeUserId(context.getUserId());
      super.update(rbAcctSettle);
   }

   public void updateRbAcctSettleIdep(RbAcctSettle rbAcctSettle) {
      rbAcctSettle.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctSettle.setCompany(Context.getInstance().getCompany());
      super.updateByCondition(".updateIdep", rbAcctSettle);
   }

   public void updateRbAcctSettleTpp(RbAcctSettle rbAcctSettle) {
      rbAcctSettle.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctSettle.setCompany(Context.getInstance().getCompany());
      super.updateByCondition(".updateTpp", rbAcctSettle);
   }

   public List<RbAcctSettle> getRbAcctSettleByPk(Long internalKey, String clientNo) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setClientNo(clientNo);
      return super.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> getRbAcctSettleInfo(RbAcctSettle rbAcctSettle) {
      return super.selectList(rbAcctSettle);
   }

   public RbAcctSettle getRbAcctSettleByPkAndAcctClass(long internalKey, String settleAcctClass, String clientNo, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setSettleAcctClass(settleAcctClass);
      rbAcctSettle.setClientNo(clientNo);
      rbAcctSettle.setCompany(company);
      return (RbAcctSettle)super.selectOne(rbAcctSettle);
   }

   public List<RbAcctSettle> getRbAcctSettlesByPkAndAcctClass(long internalKey, String settleAcctClass, String clientNo, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setSettleAcctClass(settleAcctClass);
      rbAcctSettle.setClientNo(clientNo);
      rbAcctSettle.setCompany(company);
      return super.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> selectBySettleInternalKey(long settleAcctInternalKey, String settleClient, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setSettleAcctInternalKey(settleAcctInternalKey);
      rbAcctSettle.setSettleClient(settleClient);
      rbAcctSettle.setCompany(company);
      return super.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> selectBySettleAcctInternalKey(long settleAcctInternalKey, String settleAcctClass, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setSettleAcctInternalKey(settleAcctInternalKey);
      rbAcctSettle.setSettleAcctClass(settleAcctClass);
      rbAcctSettle.setCompany(company);
      return super.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> getRbAcctSettleBySettleAcct4Keys(String settleBaseAcctNo, String settleAcctSeqNo, String settleAcctCcy, String settleProdType, String clientNo, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setSettleBaseAcctNo(settleBaseAcctNo);
      rbAcctSettle.setSettleAcctSeqNo(settleAcctSeqNo);
      rbAcctSettle.setSettleAcctCcy(settleAcctCcy);
      rbAcctSettle.setSettleAcctCcy(settleProdType);
      rbAcctSettle.setClientNo(clientNo);
      rbAcctSettle.setCompany(company);
      return super.selectList(RbAcctSettle.class.getName() + ".selectByMbSettleFourElement", rbAcctSettle);
   }

   public List<RbAcctSettle> selectBySettleBaseAcctOrCardNo(long internalKey, String settleBaseAcctNo, String settleCardNo, String settleAcctSeqNo, String settleCcy, String settleAcctClass) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleBaseAcctNo", settleBaseAcctNo);
      param.put("settleCardNo", settleCardNo);
      param.put("settleAcctSeqNo", settleAcctSeqNo);
      param.put("settleCcy", settleCcy);
      param.put("settleAcctClass", settleAcctClass);
      return super.selectList(RbAcctSettle.class.getName() + ".selectBySettleBaseAcctOrCardNo", param);
   }

   public RbAcctSettle getAcctAutoSettle(long internalKey, String clientNo, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setSettleAcctClass(SettleClassEnum.AUS.getCode());
      rbAcctSettle.setSettleMethod(SettleMethodEnum.SETTLE_METHOD_R.getCode());
      rbAcctSettle.setPayRecInd(SettlePayRecIndEnum.REC.getCode());
      rbAcctSettle.setClientNo(clientNo);
      rbAcctSettle.setCompany(company);
      List<RbAcctSettle> rbAcctSettles = super.selectList(rbAcctSettle);
      if (BusiUtil.isNull(rbAcctSettles)) {
         throw BusiUtil.createBusinessException("RB3045");
      } else {
         return (RbAcctSettle)rbAcctSettles.get(0);
      }
   }

   public List<RbAcctSettle> selectAcctSettleBySettleAcctClass(long internalKey, String settleAcctClass, String clientNo, String company, String settleBaseAcctNo) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setSettleAcctClass(settleAcctClass);
      rbAcctSettle.setSettleBaseAcctNo(settleBaseAcctNo);
      rbAcctSettle.setClientNo(clientNo);
      rbAcctSettle.setCompany(company);
      return super.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> getTimeDepositAcctSettle(Long settleAcctInternalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleAcctInternalKey", settleAcctInternalKey);
      return super.selectList(RbAcctSettle.class.getName() + ".getFixedAcctSettle", param);
   }

   public List<RbAcctSettle> selectAcctSettleByEventType(long internalKey, String eventType, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setEventType(eventType);
      rbAcctSettle.setCompany(eventType);
      return super.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> selectSettleBySettleAcctClasses(long internalKey, String[] settleAcctClasses, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleAcctClasses", settleAcctClasses);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return super.selectList(RbAcctSettle.class.getName() + ".selectSettleBySettleAcctClasses", param);
   }

   public RbAcctSettle getAcctSettleBySettleAmt(Map<String, Object> param) {
      RbAcctSettle rbAcctSettle = null;
      if (BusiUtil.isNotNull(param)) {
         List<RbAcctSettle> list = super.selectList(RbAcctSettle.class.getName() + ".getAcctSettleBySettleClass", param);
         if (BusiUtil.isNotNull(list)) {
            rbAcctSettle = (RbAcctSettle)list.get(0);
         }
      }

      return rbAcctSettle;
   }

   public RbAcctSettle getAutoDepositSettle(long internalKey, String clientNo, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setAmtType(AmtTypeEnum.PRI.getCode());
      rbAcctSettle.setSettleMethod(SettleMethodEnum.SETTLE_METHOD_R.getCode());
      rbAcctSettle.setEventType(BaseEvent.ARO.name());
      rbAcctSettle.setPayRecInd(SettlePayRecIndEnum.PAY.getCode());
      rbAcctSettle.setSettleAcctClass(SettleClassEnum.AUT.getCode());
      rbAcctSettle.setClientNo(clientNo);
      rbAcctSettle.setCompany(company);
      return (RbAcctSettle)super.selectOne(rbAcctSettle);
   }

   public RbAcctSettle selectByInternalKeyAndAmtType(long internalKey, String amtType, String clientNo, String company) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setAmtType(amtType);
      rbAcctSettle.setClientNo(clientNo);
      rbAcctSettle.setCompany(company);
      List<RbAcctSettle> rbAcctSettles = super.selectList(rbAcctSettle);
      return BusiUtil.isNull(rbAcctSettles) ? null : (RbAcctSettle)rbAcctSettles.get(0);
   }

   public Boolean getIsBindSettleAcct(RbAcctStandardModel acctInfo, RbAcctStandardModel othAcctInfo) {
      Boolean isBindSettleAcct = false;
      if (BusiUtil.isNotNullAll(new Object[]{acctInfo.getAcctClass(), othAcctInfo.getAcctClass()})) {
         List<RbAcctSettle> rbAcctSettles = this.getRbAcctSettlesByPkAndAcctClass(acctInfo.getInternalKey(), SettleClassEnum.REL.getCode(), acctInfo.getClientNo(), acctInfo.getCompany());
         Iterator var5 = rbAcctSettles.iterator();

         while(var5.hasNext()) {
            RbAcctSettle rbAcctSettle = (RbAcctSettle)var5.next();
            if (BusiUtil.isIn(rbAcctSettle.getSettleBaseAcctNo(), new String[]{othAcctInfo.getBaseAcctNo(), othAcctInfo.getCardNo()})) {
               isBindSettleAcct = true;
               break;
            }
         }
      }

      return isBindSettleAcct;
   }
}
