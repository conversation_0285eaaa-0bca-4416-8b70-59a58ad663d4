package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.PtSettleAcct;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class PtSettleAcctRepository extends BusinessRepository {
   public List<PtSettleAcct> selectInfoMsg(String channelFlag, String branch, String clientNo, String glCode, String ccy, String profitCenter, Long seqNo) {
      Map<String, Object> param = new HashMap();
      param.put("channelFlag", channelFlag);
      param.put("branch", branch);
      param.put("glCode", glCode);
      param.put("ccy", ccy);
      param.put("profitCenter", profitCenter);
      param.put("seqNo", seqNo);
      return this.daoSupport.selectList(PtSettleAcct.class.getName() + ".selectInfoMsg", param);
   }

   public int updateAcctNo(PtSettleAcct ptSettleAcct) {
      return this.daoSupport.update(PtSettleAcct.class.getName() + ".updateAcctNo", ptSettleAcct);
   }
}
