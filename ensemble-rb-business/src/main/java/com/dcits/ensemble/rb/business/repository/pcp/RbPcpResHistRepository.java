package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpResHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpResHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpResHistRepository.class);

   public void insert(RbPcpResHist rbPcpResHist) {
      super.insert(rbPcpResHist);
   }

   public List<RbPcpResHist> selectBySeq(String baseAcctNo, String clientNo) {
      RbPcpResHist rbPcpResHist = new RbPcpResHist();
      rbPcpResHist.setBaseAcctNo(baseAcctNo);
      rbPcpResHist.setClientNo(clientNo);
      return this.daoSupport.selectList(rbPcpResHist);
   }

   public void updatePcpResHist(RbPcpResHist mbPcpResHistList) {
      this.daoSupport.update(mbPcpResHistList);
   }
}
