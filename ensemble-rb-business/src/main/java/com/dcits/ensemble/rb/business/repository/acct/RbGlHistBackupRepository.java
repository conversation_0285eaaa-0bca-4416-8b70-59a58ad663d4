package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHistBackup;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbGlHistBackupRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbGlHistBackupRepository.class);

   public List<RbGlHistBackup> getRbGlHistBackupInfo(RbGlHistBackup rbGlHistBackup) {
      return this.daoSupport.selectList(rbGlHistBackup);
   }

   public void insertBatchRbGlHistBackup(List<RbGlHistBackup> rbGlHistBackupList) {
      if (BusiUtil.isNotNull(rbGlHistBackupList)) {
         this.daoSupport.insertAddBatch(rbGlHistBackupList);
      }

   }
}
