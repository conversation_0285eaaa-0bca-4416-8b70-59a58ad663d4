package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalanceHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Iterator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpAcctBalanceHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpAcctBalanceHistRepository.class);

   public void insert(RbPcpAcctBalanceHist mbPcpAcctBalanceHist) {
      mbPcpAcctBalanceHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbPcpABHSeqNo));
      super.insert(mbPcpAcctBalanceHist);
   }

   public void insertList(List<RbPcpAcctBalanceHist> mbPcpAcctBalanceHistList) {
      Iterator var2 = mbPcpAcctBalanceHistList.iterator();

      while(var2.hasNext()) {
         RbPcpAcctBalanceHist mbPcpAcctBalanceHist = (RbPcpAcctBalanceHist)var2.next();
         super.insert(mbPcpAcctBalanceHist);
      }

   }
}
