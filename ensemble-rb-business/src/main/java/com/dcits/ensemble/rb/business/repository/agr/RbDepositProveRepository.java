package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositProve;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbDepositProveRepository extends BusinessRepository {
   public List<RbDepositProve> getRbDepositProve(String internalKey, String clientNo, String baseAcctNo, String channel, String certEndDate, String tranDate, String branch, String officerId) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("channel", channel);
      param.put("certEndDate", BusiUtil.string2Date(certEndDate));
      param.put("tranDate", BusiUtil.string2Date(tranDate));
      param.put("branch", branch);
      param.put("officerId", officerId);
      return this.daoSupport.selectList(RbDepositProve.class.getName() + ".getRbDepositProve", param);
   }

   public void createRbDepositProve(List rbDepositProveList) {
      super.insertAddBatch(rbDepositProveList);
   }
}
