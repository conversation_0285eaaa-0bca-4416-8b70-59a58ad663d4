package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.bc.component.pcp.sequence.MbPcpCaptHistSequenceSeqNo;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpCaptHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpCaptHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpCaptHistRepository.class);
   @Resource
   private MbPcpCaptHistSequenceSeqNo mbPcpCaptHistSequenceSeqNo;

   public List<RbPcpCaptHist> getCaptHistForDate(String internalKey, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      return this.daoSupport.selectList(RbPcpCaptHist.class.getName() + ".getCaptHistForDate", param);
   }

   public List<RbPcpCaptHist> getCaptHist(Long internalKey, String pcpGroupId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("pcpGroupId", pcpGroupId);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbPcpCaptHist.class.getName() + ".getCaptHist", param);
   }

   public RbPcpCaptHist createPcpCaptHist(RbPcpCaptHist mbPcpCaptHist) {
      mbPcpCaptHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbPcpCaptHistSeqNo));
      super.insert(mbPcpCaptHist);
      return mbPcpCaptHist;
   }

   public int updatePcpCaptHist(RbPcpCaptHist mbPcpCaptHist) {
      return super.update(mbPcpCaptHist);
   }

   public RbPcpCaptHist selectBySeqNo(String seqNo) {
      Map<String, Object> param = new HashMap();
      param.put("seqNo", seqNo);
      return (RbPcpCaptHist)this.daoSupport.selectOne(RbPcpCaptHist.class.getName() + ".selectBySeqNo", param);
   }
}
