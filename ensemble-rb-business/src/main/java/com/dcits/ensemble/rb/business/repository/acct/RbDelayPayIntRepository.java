package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayPayInt;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbDelayPayIntHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbDelayPayIntRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbDelayPayIntRepository.class);

   public int insert(RbDelayPayInt rbDelayPayInt) {
      int count = this.daoSupport.insert(rbDelayPayInt);
      RbDelayPayIntHist rbDelayPayIntHist = new RbDelayPayIntHist();
      BeanUtils.copyProperties(rbDelayPayInt, rbDelayPayIntHist);
      rbDelayPayIntHist.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.insert(rbDelayPayIntHist);
      return count;
   }

   public int update(RbDelayPayInt rbDelayPayInt) {
      if (BusiUtil.isNotNull(rbDelayPayInt)) {
         if (BusiUtil.isNotNull(rbDelayPayInt.getDelayPayIntAmt())) {
            rbDelayPayInt.setDelayPayIntAmt(rbDelayPayInt.getDelayPayIntAmt().setScale(10, 4));
         }

         if (BusiUtil.isNotNull(rbDelayPayInt.getDelayIntAmtDiff())) {
            rbDelayPayInt.setDelayIntAmtDiff(rbDelayPayInt.getDelayIntAmtDiff().setScale(10, 4));
         }
      }

      int count = this.daoSupport.update(rbDelayPayInt);
      RbDelayPayIntHist rbDelayPayIntHist = new RbDelayPayIntHist();
      BeanUtils.copyProperties(rbDelayPayInt, rbDelayPayIntHist);
      rbDelayPayIntHist.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.insert(rbDelayPayIntHist);
      return count;
   }

   public RbDelayPayInt selectByInternalKey(Long internalKey, String clientNo) {
      RbDelayPayInt rbDelayPayInt = new RbDelayPayInt();
      rbDelayPayInt.setInternalKey(internalKey);
      rbDelayPayInt.setClientNo(clientNo);
      return (RbDelayPayInt)this.daoSupport.selectOne(rbDelayPayInt);
   }

   public RbDelayPayInt selectByInfo(Long internalKey, String clientNo) {
      RbDelayPayInt rbDelayPayInt = new RbDelayPayInt();
      rbDelayPayInt.setInternalKey(internalKey);
      rbDelayPayInt.setClientNo(clientNo);
      rbDelayPayInt.setStatus("A");
      return (RbDelayPayInt)this.daoSupport.selectOne(rbDelayPayInt);
   }

   public int count(Long othInternalKey) {
      RbDelayPayInt rbDelayPayInt = new RbDelayPayInt();
      rbDelayPayInt.setOthInternalKey(othInternalKey);
      rbDelayPayInt.setStatus("A");
      return this.daoSupport.count(rbDelayPayInt);
   }

   public List<RbDelayPayInt> queryByRefAndInternalKey(Long internalKey, String reference) {
      RbDelayPayInt rbDelayPayInt = new RbDelayPayInt();
      rbDelayPayInt.setInternalKey(internalKey);
      rbDelayPayInt.setReference(reference);
      return this.daoSupport.selectList(rbDelayPayInt);
   }

   public List<RbDelayPayInt> queryRbDelayPayIntList(String baseAcctNo, String acctSeqNo, @NotNull String approvalNo, String clientNo) {
      RbDelayPayInt param = new RbDelayPayInt();
      param.setBaseAcctNo(baseAcctNo);
      param.setAcctSeqNo(acctSeqNo);
      param.setApprovalNo(approvalNo);
      param.setClientNo(clientNo);
      return this.daoSupport.selectList(param);
   }
}
