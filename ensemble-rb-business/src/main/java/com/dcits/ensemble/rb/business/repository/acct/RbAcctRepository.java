package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.BasePo;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.comet.rpc.api.model.head.AppHead;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.bc.component.acct.AcctBusiUtil;
import com.dcits.ensemble.rb.business.bc.component.cm.common.RbBaseAcctInfoImpl;
import com.dcits.ensemble.rb.business.bc.component.cm.operate.util.PageQueryUtil;
import com.dcits.ensemble.rb.business.common.util.AppHeadUtil;
import com.dcits.ensemble.rb.business.common.util.MqUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.restful.tb.TbBranchChangeOut;
import com.dcits.ensemble.rb.business.model.res.restraints.StatusFlagEnum;
import com.dcits.ensemble.rb.business.repository.cd.RbCardNoSecretaryRestraintsRepository;
import com.dcits.ensemble.rb.business.shareapi.acct.model.AcctQueryControlModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAcctRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctRepository.class);
   private static final String MAX_SEQ_NO = "MAX_SEQ_NO";
   public static final String MULTI_CORP_QUERY_ALLOW = "MULTI_CORP_QUERY_ALLOW";
   public static final String ACCT_STATUS_C = "C";
   public static final String STATUS_FLAG_2 = "2";
   public static final int PAGE_SIZE = 999;
   public static final String IS_INDIVIDUAL_N = "N";
   public static final String NATURECLASS_1 = "11001";
   public static final String NATURECLASS_2 = "11002";
   @Resource
   private PageQueryUtil pageQueryUtil;
   @Resource
   private RbAcctClientRelationRepository rbAcctClientRelationRepository;
   @Resource
   private RbBaseAcctInfoImpl rbBaseAcctInfo;
   @Resource
   private RbCardNoSecretaryRestraintsRepository rbCardNoSecretaryRestraintsRepository;
   @Resource
   private FmBaseStor fmBaseStor;

   public RbAcct selectSingle(RbAcct rbAcct) {
      return (RbAcct)this.daoSupport.selectOne(rbAcct);
   }

   public void updMbAcctDb(List<RbAcct> rbAccts) {
      if (BusiUtil.isNotNull(rbAccts)) {
         for(int i = 0; i < rbAccts.size(); ++i) {
            super.update((EnsBaseDbBean)rbAccts.get(i));
         }
      }

   }

   public void updMbAcctDbWithCloseDate(List<RbAcct> rbAccts) {
      if (BusiUtil.isNotNull(rbAccts)) {
         for(int i = 0; i < rbAccts.size(); ++i) {
            this.daoSupport.update(RbAcct.class.getName() + ".updateAcctCloseDate", (BasePo)rbAccts.get(i));
         }
      }

   }

   public void createMbAcctDb(List<RbAcct> rbAccts) {
      if (BusiUtil.isNotNull(rbAccts)) {
         for(int i = 0; i < rbAccts.size(); ++i) {
            super.insert((EnsBaseDbBean)rbAccts.get(i));
            if (BusiUtil.isNotNull(((RbAcct)rbAccts.get(i)).getCardNo())) {
               this.rbCardNoSecretaryRestraintsRepository.createMsg(((RbAcct)rbAccts.get(i)).getCardNo(), ((RbAcct)rbAccts.get(i)).getInternalKey(), ((RbAcct)rbAccts.get(i)).getClientNo(), ((RbAcct)rbAccts.get(i)).getCompany());
            }

            String operate = "INSERT";
            MqUtil.sendMqToGateWay(((RbAcct)rbAccts.get(i)).getClientNo(), ((RbAcct)rbAccts.get(i)).getBaseAcctNo(), operate);
         }
      }

   }

   public RbAcct getNotCloseRbAcctByPk(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      return (RbAcct)super.selectOne(RbAcct.class.getName() + ".getNotCloseRbAcctByPk", param);
   }

   public void updateMbAcctStatus(String internalKey, String status, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("status", status);
      param.put("clientNo", clientNo);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctStatus", param);
   }

   public void updateMbAcctVoucherStatus(String internalKey, String voucherStatus, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("voucherStatus", voucherStatus);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctVoucherStatus", param);
   }

   public void updateMbAcctStatusPrev(String internalKey, String statusPrev, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("statusPrev", statusPrev);
      param.put("clientNo", clientNo);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctStatusPrev", param);
   }

   public void updMbAcctDb(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updateByClientNo", rbAcct);
      }

   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updMbAcctDb1(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updateByClientNo", rbAcct);
      }

   }

   public void updRbAcctVoucherStatusByBaseAcctNo(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updRbAcctVoucherStatusByBaseAcctNo", rbAcct);
      }

   }

   public void updRbAcctStopPayByInternalKey(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updRbAcctStopPayByInternalKey", rbAcct);
      }

   }

   public void updRbAcctNewVoucherByCardNo(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updRbAcctNewVoucherByCardNo", rbAcct);
      }

   }

   public Integer selectCountByRunDate(Date runDate) {
      Map<String, Object> map = new HashMap(16);
      map.put("runDate", runDate);
      return (Integer)this.daoSupport.selectObject(RbAcct.class.getName() + ".selectCountByRunDate", map);
   }

   public List<RbAcct> getTwoAndThreeClassByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      return this.pageQueryUtil.selectByPage(RbAcct.class.getName() + ".getTwoAndThreeClassByClientNo", param);
   }

   public void updMbAcctInOutDb(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         rbAcct.setTranTimestamp(BusiUtil.getTranTimestamp26());
         this.daoSupport.update(RbAcct.class.getName() + ".updateByClientNoInOut", rbAcct);
      }

   }

   public void updSomeMbAcctDb(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updSomeMbAcctDb", rbAcct);
      }

   }

   public void updMbAcctsDb(List<RbAcct> rbAccts) {
      if (BusiUtil.isNotNull(rbAccts)) {
         super.updateAddBatch(rbAccts);
      }

   }

   public void updByBaseAcctNo(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updateByBaseAcctNo", rbAcct);
      }

   }

   public void updateParentInternalKey(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updateParentInternalKey", rbAcct);
      }

   }

   public void updateClientNo(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updateClientNo", rbAcct);
      }

   }

   public void updateByPrimaryKeyForClientChange(RbAcct rbAcct) {
      this.daoSupport.update(RbAcct.class.getName() + ".updateByPrimaryKeyForClientChange", rbAcct);
   }

   public void updateByPrimaryKeyForAcctNature(RbAcct rbAcct) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", rbAcct.getInternalKey());
      param.put("acctNature", rbAcct.getAcctNature());
      param.put("clientNo", rbAcct.getClientNo());
      this.daoSupport.update(RbAcct.class.getName() + ".updateByPrimaryKeyForAcctNature", param);
   }

   public List<RbAcct> getMbAcctByCardNo(String cardNo, String acctSeqNo, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("cardNo", cardNo);
      map.put("acctSeqNo", acctSeqNo);
      map.put("company", company);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByCardNo", map);
      if (BusiUtil.isNull(rbAccts)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{cardNo});
      } else {
         return rbAccts;
      }
   }

   public RbAcct getMbAcct(Long internalKey, String clientNo) {
      RbAcct rbAcct1 = new RbAcct();
      rbAcct1.setInternalKey(internalKey);
      rbAcct1.setClientNo(clientNo);
      RbAcct rbAcct = (RbAcct)this.daoSupport.selectOne(rbAcct1);
      if (null == rbAcct) {
         throw BusiUtil.createBusinessException("RB8216", new String[]{internalKey.toString()});
      } else {
         return rbAcct;
      }
   }

   public RbAcct getMbAcctByMbAcctDoss(RbAcctDossReg mbAcctDoss) {
      RbAcct rbAcct1 = new RbAcct();
      rbAcct1.setInternalKey(mbAcctDoss.getInternalKey());
      rbAcct1.setClientNo(mbAcctDoss.getClientNo());
      RbAcct rbAcct = (RbAcct)this.daoSupport.selectOne(rbAcct1);
      if (null == rbAcct) {
         throw BusiUtil.createBusinessException("RB8216", new String[]{mbAcctDoss.getBaseAcctNo()});
      } else {
         return rbAcct;
      }
   }

   public RbAcct getMbAcctForUpdate(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      String company = "";
      boolean batch = Context.getInstance().isBatch();
      if (!batch && BusiUtil.isEqualN(String.valueOf(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW")))) {
         company = String.valueOf(Context.getInstance().getProperty("COMPANY"));
      }

      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctForUpdate", param);
   }

   public RbAcct getMbAcctNoWarning(Long internalKey, String clientNo) {
      RbAcct rbAcct1 = new RbAcct();
      rbAcct1.setInternalKey(internalKey);
      rbAcct1.setClientNo(clientNo);
      return (RbAcct)this.daoSupport.selectOne(rbAcct1);
   }

   public RbAcct getMbAcct(Long internalKey, boolean flag, String clientNo) {
      RbAcct rbAcct1 = new RbAcct();
      rbAcct1.setInternalKey(internalKey);
      rbAcct1.setClientNo(clientNo);
      RbAcct rbAcct = (RbAcct)this.daoSupport.selectOne(rbAcct1);
      if (flag && null == rbAcct) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{internalKey.toString()});
      } else {
         return rbAcct;
      }
   }

   public List<RbAcct> queryMbAcctNum(String clientNo) {
      String company = "";
      boolean batch = Context.getInstance().isBatch();
      if (!batch && BusiUtil.isEqualN(String.valueOf(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW")))) {
         company = String.valueOf(Context.getInstance().getProperty("COMPANY"));
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryMbAcctNum", param);
   }

   public List<RbAcct> selectListByClientNo(String clientNo) {
      String company = "";
      boolean batch = Context.getInstance().isBatch();
      if (!batch && BusiUtil.isEqualN(String.valueOf(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW")))) {
         company = String.valueOf(Context.getInstance().getProperty("COMPANY"));
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".selectListByClientNo", param);
   }

   public RbAcct getRbAcctByClientNoBaseNO(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getRbAcctByClientNoBaseNO", param);
   }

   public RbAcct getRbAcctByBaseNOAcctSeqNo(String baseAcctNo, String clientNo, String acctSeqNo, String acctCcy) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("acctCcy", acctCcy);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getRbAcctByClientNoBaseNO", param);
   }

   public List<RbAcct> getRbAcctByClientNoBaseNOAndStatusNotC(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctByClientNoBaseNOAndStatusNotC", param);
   }

   public RbAcct getRbAcctByCardNoBaseNO(String cardNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("cardNo", cardNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getRbAcctByCardNoBaseNO", param);
   }

   public RbAcct getRbAcctByCardNoOpen(String clientNo, Long internalKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getRbAcctByCardNoOpen", param);
   }

   public List<RbAcct> getMbAcctByClientNo(String clientNo, String leadAcctFlag) {
      return this.getMbAcctByClientNoAnAcctStatus(clientNo, leadAcctFlag, (String)null, (String)null);
   }

   public List<RbAcct> getMbAcctByClientNoAnAcctStatus(String clientNo, String leadAcctFlag, String acctStatus, String acctClass) {
      String company = "";
      boolean isBatch = Context.getInstance().isBatch();
      if (!isBatch && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW")) && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_INFO_SHARE"))) {
         company = Context.getInstance().getProperty("COMPANY");
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("leadAcctFlag", leadAcctFlag);
      param.put("acctStatus", acctStatus);
      param.put("acctClass", acctClass);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByClientNo", param);
   }

   public List<RbAcct> getMbAcctByClientNoByStatusFlag1(String clientNo, String leadAcctFlag, String acctClass) {
      String company = "";
      boolean isBatch = Context.getInstance().isBatch();
      if (!isBatch && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW")) && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_INFO_SHARE"))) {
         company = Context.getInstance().getProperty("COMPANY");
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("leadAcctFlag", leadAcctFlag);
      param.put("acctClass", acctClass);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByClientNoByStatusFlag1", param);
   }

   public List<RbAcct> getMbAcctByClientNoByStatusFlag2(String clientNo, String leadAcctFlag, String acctClass) {
      String company = "";
      boolean isBatch = Context.getInstance().isBatch();
      if (!isBatch && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW")) && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_INFO_SHARE"))) {
         company = Context.getInstance().getProperty("COMPANY");
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("leadAcctFlag", leadAcctFlag);
      param.put("acctClass", acctClass);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByClientNoByStatusFlag2", param);
   }

   public List<RbAcct> getRbAcctByClientNo(RbAcct rbAcct) {
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctByClientNo", rbAcct);
   }

   public List<RbAcct> getRbAcctByClientNo(String clientNo, String acctStatus, String acctRealFlag, String company) {
      Map<String, Object> param = new HashMap(3);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      param.put("acctRealFlag", acctRealFlag);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctByClientNo", param);
   }

   public List<RbAcct> getRbAcctByClientNo(String clientNo, String acctStatus, String acctRealFlag, String prodType, String isShowClosedAcct, String company) {
      Map<String, Object> param = new HashMap(3);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      param.put("acctRealFlag", acctRealFlag);
      param.put("prodType", prodType);
      param.put("isShowClosedAcct", isShowClosedAcct);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctByClientNoShowClosed", param);
   }

   public List<RbAcct> getMbAcctByClientNoWithoutException(String clientNo) {
      String company = "";
      boolean isBatch = Context.getInstance().isBatch();
      if (!isBatch && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW"))) {
         company = Context.getInstance().getProperty("COMPANY");
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctAllByClientNo", param);
   }

   public List<RbAcct> getMbAcctListByGlCode(String baseAcctNo, String glCode, String acctBranch, String company) {
      List<RbAcct> lAcctInfo = null;
      Map<String, Object> param = new HashMap(16);
      param.put("glCode", glCode);
      param.put("acctBranch", acctBranch);
      param.put("company", company);
      lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByBaseAcctNoAndGlCode", param);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else {
         return lAcctInfo;
      }
   }

   public List<RbAcct> getMbAcctListByGlCode(String baseAcctNo, String glCode, String acctBranch, String statusFlag, String company) {
      List lAcctInfo = null;

      try {
         Map<String, Object> param = new HashMap(16);
         param.put("glCode", glCode);
         param.put("acctBranch", acctBranch);
         param.put("statusFlag", statusFlag);
         param.put("company", company);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByBaseAcctNoAndGlCode", param);
      } catch (Exception var8) {
         log.warn("errorStackTrace:", var8);
      }

      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else {
         return lAcctInfo;
      }
   }

   public List<RbAcct> getAcctInfo(String clientNo) {
      String company = "";
      Boolean isBatch = Context.getInstance().isBatch();
      if (!isBatch && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW"))) {
         company = Context.getInstance().getProperty("COMPANY");
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctAllByClientNo", param);
      if (BusiUtil.isNotNull(rbAccts) && !rbAccts.isEmpty()) {
         return rbAccts;
      } else {
         throw BusiUtil.createBusinessException("RB4005");
      }
   }

   public List<RbAcct> getCancelMbAcctListByGlCode(String baseAcctNo, String glCode, String acctBranch, String company) {
      List lAcctInfo = null;

      try {
         Map<String, Object> param = new HashMap(16);
         param.put("glCode", glCode);
         param.put("acctBranch", acctBranch);
         param.put("runDate", Context.getInstance().getRunDate().replaceAll("-", ""));
         param.put("company", company);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getCancelMbAcctByBaseAcctNoAndGlCode", param);
      } catch (Exception var7) {
         log.warn("errorStackTrace:", var7);
      }

      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else {
         return lAcctInfo;
      }
   }

   public List<RbAcct> getMbAcctByClientTypeAndBranch(String clientType, String categoryType, String prodType, String acctBranch) {
      Map<String, Object> param = new HashMap(16);
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcct.class.getName() + ".getMbAcctByClientTypeAndBranch";
      param.put("clientType", clientType);
      param.put("categoryType", categoryType);
      param.put("prodType", prodType);
      param.put("acctBranch", acctBranch);
      List acctInfo;
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         acctInfo = queryResult.getResultlist();
      } else {
         acctInfo = this.daoSupport.selectList(statementPostfix, param);
      }

      if (BusiUtil.isNull(acctInfo)) {
         throw BusiUtil.createBusinessException("RB4068");
      } else {
         return acctInfo;
      }
   }

   public List<RbAcct> getMbAcctByClientTypeAndBranch2(String clientType, String categoryType, String prodType, String acctBranch) {
      Map<String, Object> param = new HashMap(16);
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("clientType", clientType);
      param.put("categoryType", categoryType);
      param.put("prodType", prodType);
      param.put("acctBranch", acctBranch);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByClientTypeAndBranch", param);
   }

   public String getChClientName(String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (String)this.daoSupport.selectObject(RbAcct.class.getName() + ".getChClientName", param);
   }

   public RbAcct getAcctSched(Long internalKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getAcctSched", param);
   }

   public void updateBranch(long internalKey, String acctBranch) {
      String lastChangeDate = Context.getInstance().getRunDate();
      String userId = Context.getInstance().getUserId();
      RbAcct rbAcct = new RbAcct();
      rbAcct.setInternalKey(internalKey);
      rbAcct.setAcctBranch(acctBranch);
      rbAcct.setApplyBranch(acctBranch);
      rbAcct.setHomeBranch(acctBranch);
      rbAcct.setLastChangeDate(DateUtil.parseDate(lastChangeDate));
      rbAcct.setLastChangeUserId(userId);
      super.update(rbAcct);
   }

   public List<RbAcct> getAllMbAcctNoCondition(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getListNoCondition", param);
   }

   public List<RbAcct> getListNoCondition(String baseAcctNo, String prodType, String acctCcy, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getListNoCondition", param);
   }

   public RbAcct getMbAcctByAcctSeqNoNoCondition(String baseAcctNo, String seqNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", seqNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByAcctSeqNoNoCondition", param);
   }

   public RbAcct getMbAcctBybaseAcctNoClientNo(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctBybaseAcctNoClientNo", param);
   }

   public List<RbAcct> getMbAcctAllByClientNo(String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctAllByClientNo", param);
   }

   public RbAcct getRbAcctByPkAndStatus(Long internalKey, String clientNo, StatusFlagEnum statusFlag) {
      if (BusiUtil.existNull(new Object[]{internalKey, clientNo})) {
         throw BusiUtil.createBusinessException("RB4919");
      } else {
         HashMap<String, Object> param = new HashMap();
         param.put("internalKey", internalKey);
         param.put("clientNo", clientNo);
         param.put("statusFlag", BusiUtil.nvl(statusFlag, StatusFlagEnum.INCLUDE_CLOSE.getCode()));
         return (RbAcct)super.selectOne(RbAcct.class.getName().concat(".getRbAcctByPkAndStatus"), param);
      }
   }

   public List<RbAcct> getRbAcctAllByClientNo(String clientNo, String prodType, String acctType, String acctNature) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("prodType", prodType);
      param.put("acctType", acctType);
      param.put("acctNature", acctNature);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctAllByClientNo", param);
   }

   public RbAcct getRbAcctByPk(Long internalKey, String clientNo) {
      RbAcct rbAcct1 = new RbAcct();
      rbAcct1.setInternalKey(internalKey);
      rbAcct1.setClientNo(clientNo);
      RbAcct rbAcct = (RbAcct)super.selectOne(rbAcct1);
      if (null == rbAcct) {
         if (BusiUtil.isNotNull(internalKey)) {
            throw BusiUtil.createBusinessException("RB8216", new String[]{internalKey.toString()});
         } else {
            throw BusiUtil.createBusinessException("RB8201");
         }
      } else {
         return rbAcct;
      }
   }

   public List<RbAcct> getAllRbAccts(String baseAcctNo, String clientNo, String acctType, AcctQueryControlModel controlModel) {
      if (BusiUtil.isNullAll(new Object[]{baseAcctNo, clientNo})) {
         throw BusiUtil.createBusinessException("RB4918");
      } else {
         HashMap<String, Object> param = new HashMap();
         param.put("baseAcctNo", baseAcctNo);
         param.put("clientNo", clientNo);
         param.put("acctType", acctType);
         if (BusiUtil.isNotNull(controlModel)) {
            param.put("statusFlag", controlModel.getStatusFlag().getCode());
            if (controlModel.isPageFlag()) {
               return this.pageQueryUtil.selectByPage(RbAcct.class.getName().concat(".getAllRbAccts"), param);
            }

            param.put("isIncludeSingleMainAcct", controlModel.isIncludeSingleMainAcct() ? "Y" : "N");
         } else {
            param.put("isIncludeSingleMainAcct", "N");
         }

         return super.selectList(RbAcct.class.getName().concat(".getAllRbAccts"), param);
      }
   }

   public List<RbAcct> getRbRbAcctAllStatus(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return super.selectList(RbAcct.class.getName() + ".getRbRbAcctAllStatus", param);
   }

   public List<RbAcct> getMbAcctAllByClientNoJS(String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctAllByClientNoJS", param);
   }

   public List<RbAcct> getRbAcctAllByClientNoJS(String clientNo, String prodType, String acctType, String acctNature) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("prodType", prodType);
      param.put("acctType", acctType);
      param.put("acctNature", acctNature);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctAllByClientNoJS", param);
   }

   public List<RbAcct> getMbAcctAllByClientNoB(String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctAllByClientNoB", param);
   }

   public int getApprLetterNoCount(String apprLetterNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      return (Integer)this.daoSupport.selectObject(RbAcct.class.getName() + ".getApprLetterNoCount", param);
   }

   public RbAcct getAutoRenewRollover(Long internalKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getAutoRenewRollover", param);
   }

   public RbAcct getRbAcctByInternalKey(Long internalKey) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setInternalKey(internalKey);
      return (RbAcct)this.daoSupport.selectOne(rbAcct);
   }

   public List<RbAcct> getRbAcctByInternalKeys(@NonNull List<Long> internalKeys) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKeys", internalKeys);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctByInternalKeys", param);
   }

   public List<RbAcct> getActiveAcct(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      company = AcctBusiUtil.convertCompany(company);
      if (BusiUtil.isNotNull(company)) {
         param.put("company", company);
      }

      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getMbAcctByBaseAcctNoByStatus1(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByBaseAcctNoByStatus1", param);
   }

   public List<RbAcct> getMbAcctByBaseAcctNoByStatus2(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByBaseAcctNoByStatus2", param);
   }

   public List<RbAcct> getActiveAcctByProdType(String prodType, String acctCcy, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getActiveAcctByProdType", param);
   }

   public List<RbAcct> getMbAcctByAcctNature(String acctNature, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctNature", acctNature);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryMbAcctByAcctNature", param);
   }

   public List<RbAcct> getAllRbAcctInfoList(Long internalKey, String startDate, String endDate, String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return super.selectList(RbAcct.class.getName() + ".getAllIMbAcctInfoList", param);
   }

   public RbAcct queryAcctNoticeSingle(Long internalKey, String prodType, String fixedCall, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("prodType", prodType);
      param.put("fixedCall", fixedCall);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".queryAcctNoticeSingle", param);
   }

   public List<RbAcct> getTwoThreeByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getTwoThreeByClientNo", param);
   }

   public RbAcct queryYhtBydate(Long internalKey, String startDate, String endDate) {
      String startDa = startDate;
      String endDa = endDate;
      if ("".equals(startDate)) {
         startDa = null;
      }

      if ("".equals(endDate)) {
         endDa = null;
      }

      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey.toString());
      param.put("startDate", BusiUtil.string2Date(startDa));
      param.put("endDate", BusiUtil.string2Date(endDa));
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".queryYhtBydate", param);
   }

   public List<RbAcct> queryAllYhtList(String agreementId, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryAllYhtList", param);
   }

   public RbAcct getSettleAcct(String acctNo, String acctCcy, String company) {
      RbAcct rbAcct = null;

      try {
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", acctNo);
         param.put("acctCcy", acctCcy);
         param.put("comapny", company);
         rbAcct = (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbSettleAcct", param);
      } catch (Exception var6) {
         log.warn("errorStackTrace:", var6);
      }

      if (null == rbAcct) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{acctNo});
      } else {
         return rbAcct;
      }
   }

   public List<RbAcct> getMbAcctNoWarning(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType) {
      return this.getMbAcctNoWarning(baseAcctNo, acctSeqNo, acctCcy, prodType, (String)null, (String)null, (String)null);
   }

   public List<RbAcct> getMbAcctNoWarningByPage(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, List<String> notInProdTypes, String acctType, String balType, boolean isQueryTAcctType) {
      return this.getMbAcctNoWarningByPage(baseAcctNo, acctSeqNo, acctCcy, prodType, balType, (String)null, (String)null, notInProdTypes, acctType, isQueryTAcctType);
   }

   public List<RbAcct> getMbAcctNoWarning(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String balType, String term, String termType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("balType", balType);
      param.put("term", term);
      param.put("termType", termType);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
   }

   public List<RbAcct> getMbAcctNoWarningByCard(String cardNo, String acctSeqNo, String acctCcy, String prodType) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", cardNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
   }

   public List<RbAcct> getMbAcctNoWarningByPage(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String balType, String term, String termType, List<String> notInProdTypes, String acctType, boolean isQueryTAcctType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("balType", balType);
      param.put("term", term);
      param.put("termType", termType);
      param.put("notInProdTypes", notInProdTypes);
      param.put("acctType", acctType);
      param.put("isQueryTAcctType", isQueryTAcctType);
      RowArgs rowArgs = AppHeadUtil.getRowArgs();
      String statementPostfix = RbAcct.class.getName() + ".getMbAcctSelectiveByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcct> getMbAcctNoWarningAllStatusByPage(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String balType, String term, String termType, String acctType, String statusFlag) {
      return this.getMbAcctNoWarningAllStatusByPage(baseAcctNo, acctSeqNo, acctCcy, prodType, balType, term, termType, acctType, statusFlag, (List)null);
   }

   public List<RbAcct> getMbAcctNoWarningAllStatusByPage(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String balType, String term, String termType, String acctType, String statusFlag, List<String> notInProdTypes) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("balType", balType);
      param.put("term", term);
      param.put("termType", termType);
      param.put("acctType", acctType);
      param.put("statusFlag", statusFlag);
      param.put("notInProdTypes", notInProdTypes);
      RowArgs rowArgs = AppHeadUtil.getRowArgs();
      String statementPostfix = RbAcct.class.getName() + ".getMbAcctNoWarningAllStatus";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcct> getMbAcct1(String baseAcctNo, String prodType, String acctCcy, String seqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective1", param);
   }

   public List<RbAcct> getOthSubAcct(String baseAcctNo, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getOthSubAcct", param);
   }

   public List<RbAcct> getOthSubAcctByCardNo(String cardNo, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", cardNo);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getOthSubAcctByCardNo", param);
   }

   public List<RbAcct> getMbAcct(String baseAcctNo, String prodType, String acctCcy, String seqNo, String statusFlag) {
      String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("statusFlag", statusFlag);
      param.put("clientNo", clientNo);
      if (BusiUtil.isEquals(statusFlag, "2")) {
         param.put("acctStatus", "C");
      }

      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
   }

   public List<RbAcct> getMbAcctByclientNo(String baseAcctNo, String prodType, String acctCcy, String seqNo, String statusFlag, String clientNo) {
      String clientNoNew;
      if (BusiUtil.isNull(baseAcctNo) && BusiUtil.isNotNull(clientNo)) {
         clientNoNew = clientNo;
      } else {
         clientNoNew = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
      }

      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("statusFlag", statusFlag);
      param.put("clientNo", clientNoNew);
      if (BusiUtil.isEquals(statusFlag, "2")) {
         param.put("acctStatus", "C");
      }

      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
   }

   public List<RbAcct> getMbAcctQueryHist(String baseAcctNo, String prodType, String acctCcy, String seqNo, String statusFlag) {
      String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("statusFlag", statusFlag);
      param.put("clientNo", clientNo);
      if (BusiUtil.isEquals(statusFlag, "2")) {
         param.put("acctStatus", "C");
      }

      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelectiveQueryHist", param);
   }

   public List<RbAcct> getMbAcctInBranch(String baseAcctNo, String prodType, String acctCcy, String seqNo, String statusFlag, List<String> branchList) {
      this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("statusFlag", statusFlag);
      param.put("branchList", branchList);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctListForStage", param);
   }

   public List<RbAcct> getMbAcctByPage(String baseAcctNo, String prodType, String acctCcy, String seqNo, String statusFlag, List<String> notInProdTypes, String acctType, String balType, boolean isQueryTAcctType) {
      String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("statusFlag", statusFlag);
      param.put("clientNo", clientNo);
      param.put("notInProdTypes", notInProdTypes);
      param.put("isQueryTAcctType", isQueryTAcctType);
      if (BusiUtil.isEquals(statusFlag, "2")) {
         param.put("acctStatus", "C");
      }

      param.put("acctType", acctType);
      param.put("balType", balType);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcct.class.getName() + ".getMbAcctSelective";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcct> getMbAcct(String baseAcctNo, String prodType, String acctCcy, String seqNo, String statusFlag, String clientNo) {
      if (BusiUtil.isNotNull(baseAcctNo) && BusiUtil.isNull(clientNo)) {
         String var7 = (String)BusiUtil.nvl(clientNo, this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null));
      }

      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("statusFlag", statusFlag);
      param.put("clientNo", clientNo);
      if (BusiUtil.isEquals(statusFlag, "2")) {
         param.put("acctStatus", "C");
      }

      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
   }

   public List<RbAcct> getMbAcctListForStage(String baseAcctNo, String prodType, String acctCcy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      RbAcct rbAcct = new RbAcct();
      BeanUtils.copyProperties(param, rbAcct);
      Integer count = this.daoSupport.count(rbAcct);
      if (count > 1000) {
         this.getContextAppHead();
         return this.pageQueryUtil.selectByPage(RbAcct.class.getName() + ".getMbAcctListActiveByPage", param);
      } else {
         return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctListForStage", param);
      }
   }

   private AppHead getContextAppHead() {
      AppHead appHead = Context.getInstance().getAppHead();
      if (BusiUtil.isNull(appHead)) {
         appHead = new AppHead();
         appHead.setPgupOrPgdn("1");
         appHead.setTotalNum("1000");
         appHead.setCurrentNum("0");
         appHead.setPageStart("0");
         appHead.setPageEnd("0");
      }

      if (BusiUtil.isNull(appHead.getTotalRows())) {
         appHead.setPgupOrPgdn("1");
         appHead.setTotalNum("1000");
         appHead.setCurrentNum("0");
      }

      return appHead;
   }

   public List<RbAcct> getMbAcctByName(String baseAcctNo, String cardNo, String acctName) {
      Map<String, Object> param = new HashMap(16);
      if (BusiUtil.isNotNull(baseAcctNo) || BusiUtil.isNotNull(cardNo)) {
         param.put("baseAcctNo", baseAcctNo);
         param.put("cardNo", cardNo);
         String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, cardNo, (String)null);
         param.put("clientNo", clientNo);
      }

      if (BusiUtil.isNotNull(acctName)) {
         param.put("acctName", "%" + acctName + "%");
      }

      param.put("statusFlag", 1);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
   }

   public RbAcct getMbAcctByStatus(String cardNo, String baseAcctNo, String prodType, String acctCcy, String seqNo, String statusFlag) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", cardNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("statusFlag", statusFlag);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctSelective", param);
   }

   public List<RbAcct> getCancelMbAcct(String baseAcctNo, String prodType, String acctCcy, String seqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("runDate", Context.getInstance().getRunDate().replaceAll("-", ""));
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getCancelMbAcct", param);
   }

   public List<RbAcct> getCancelMbAcctByPage(String baseAcctNo, String prodType, String acctCcy, String seqNo, List<String> notInProdTypes, String acctType, String balType, boolean isQueryTAcctType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("runDate", Context.getInstance().getRunDate().replaceAll("-", ""));
      param.put("notInProdTypes", notInProdTypes);
      param.put("acctType", acctType);
      param.put("balType", balType);
      param.put("isQueryTAcctType", isQueryTAcctType);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcct.class.getName() + ".getCancelMbAcct";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcct> getCancelMbAcctInfos(String baseAcctNo, String prodType, String acctCcy, String seqNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getCancelMbAcctInfos", param);
   }

   public String getClientNo(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return (String)this.daoSupport.selectObject(RbAcct.class.getName() + ".getClientNo", param);
   }

   public RbAcct getMbAcctByAcctSeqNo(String baseAcctNo, String seqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", seqNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByAcctSeqNo", param);
   }

   public RbAcct getMbAcctByAcctSeqNoClient(String baseAcctNo, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByAcctSeqNoClient", param);
   }

   public int getAcctMaxSeqNo(String baseAcctNo) {
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);
      return this.rbAcctClientRelationRepository.getMaxSeqNoByBaseAcctNo(cardBaseAcctNo);
   }

   public int getAcctMaxSeqNoByClientNo(String baseAcctNo, String clientNo, String company) {
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);
      return this.getMaxSeqNoByClientNo(cardBaseAcctNo, clientNo, company);
   }

   public List<RbAcct> getMbAcctList(String baseAcctNo, String contractNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("contractNo", contractNo);
      param.put("clientNo", clientNo);
      return (List)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctList", param);
   }

   public int getMaxSeqNo(String baseAcctNo) {
      String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      HashMap<String, Object> ret = (HashMap)this.daoSupport.selectObject(RbAcct.class.getName() + ".getMaxSeqNo", param);
      if (BusiUtil.isNotNull(ret)) {
         if (ret.get("MAX_SEQ_NO") instanceof Double) {
            Double seqNo = (Double)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }

         if (ret.get("MAX_SEQ_NO") instanceof BigDecimal) {
            BigDecimal seqNo = (BigDecimal)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }
      }

      return 0;
   }

   public int getMaxSeqNoByClientNo(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      HashMap<String, Object> ret = (HashMap)this.daoSupport.selectObject(RbAcct.class.getName() + ".getMaxSeqNo", param);
      if (BusiUtil.isNotNull(ret)) {
         if (ret.get("MAX_SEQ_NO") instanceof Double) {
            Double seqNo = (Double)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }

         if (ret.get("MAX_SEQ_NO") instanceof BigDecimal) {
            BigDecimal seqNo = (BigDecimal)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }
      }

      return 0;
   }

   public RbAcct getMbAcctByAccountStatus(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByAccountStatus", param);
   }

   public List<RbAcct> getMbAcctByBaseAcctNoorCardNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByBaseAcctNoorCardNo", param);
   }

   public List<RbAcct> getAllMbAccts(String baseAcctNo, String company) {
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);
      String lastRunDate = Context.getInstance().getLastRunDate();
      List acctInfo = null;

      try {
         Map<String, Object> param = new HashMap(16);
         param.put("lastRunDate", lastRunDate);
         param.put("baseAcctNo", baseAcctNo);
         param.put("company", company);
         acctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByAccountStatus", param);
      } catch (Exception var7) {
         log.error("errorStackTrace:", var7);
      }

      if (BusiUtil.isNull(acctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{cardBaseAcctNo});
      } else {
         return acctInfo;
      }
   }

   public String getActualBaseAcctNo(String baseAcctNo) {
      return BusiUtil.isNull(baseAcctNo) ? null : baseAcctNo;
   }

   public String getAcctBranch(String baseAcctNo, String prodType, String acctCcy, String seqNo, String clientNo, String company) {
      String acctBranch = null;
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctInfoByPrimaryKey", param);
      if (BusiUtil.isNotNull(rbAccts)) {
         acctBranch = ((RbAcct)rbAccts.get(0)).getAcctBranch();
      } else {
         List<RbBaseAcct> rbBaseAccts = this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getMbBaseAcctInfoByPrimaryKey", param);
         if (!BusiUtil.isNotNull(rbBaseAccts)) {
            throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
         }

         acctBranch = ((RbBaseAcct)rbBaseAccts.get(0)).getAcctBranch();
      }

      return acctBranch;
   }

   public RbAcct getSingleAcctNoCondition(String baseAcctNo, String prodType, String acctCcy, String seqNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getSingleAcctNoCondition", param);
   }

   public RbAcct getCurrentAcctByCardNo(String baseAcctNo, String acctCcy) {
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);
      List<RbAcct> rbAccts = null;
      RbAcct rbAcct = null;

      try {
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("acctCcy", acctCcy);
         rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getCurrentAcctByCardNo", param);
      } catch (Exception var7) {
         log.error("errorStackTrace:", var7);
      }

      if (BusiUtil.isNull(rbAccts)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{cardBaseAcctNo});
      } else {
         rbAcct = (RbAcct)rbAccts.get(0);
         return rbAcct;
      }
   }

   public List<RbAcct> getAllMbAcctInfo(String baseAcctNo, String cardNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("cardNo", cardNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctSelective", param);
   }

   public List<RbAcct> getAllMbAcctInfoByPage(String baseAcctNo, List<String> notInProdTypes, String acctType, String balType, boolean isQueryTAcctType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("notInProdTypes", notInProdTypes);
      param.put("acctType", acctType);
      param.put("balType", balType);
      param.put("isQueryTAcctType", isQueryTAcctType);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcct.class.getName() + ".getAllMbAcctSelectiveByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public RbAcct getMbAcctInfo(String baseAcctNo, String prodType, String acctCcy, String seqNo) {
      String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
      if (BusiUtil.isNotNull(rbAccts)) {
         return (RbAcct)rbAccts.get(0);
      } else {
         throw BusiUtil.createBusinessException("RB4005");
      }
   }

   public RbAcct getMbAcctInfoByPrimaryKey(String baseAcctNo, String prodType, String acctCcy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctInfoByPrimaryKey", param);
      if (BusiUtil.isNotNull(rbAccts)) {
         return (RbAcct)rbAccts.get(0);
      } else {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      }
   }

   public RbAcct getAllMbAcctInfoByPrimaryKey(String baseAcctNo, String prodType, String acctCcy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctInfoByPrimaryKey", param);
      if (BusiUtil.isNotNull(rbAccts)) {
         return (RbAcct)rbAccts.get(0);
      } else {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      }
   }

   public RbAcct getMbAcctInfoByCardNo(String baseAcctNo, String prodType, String acctCcy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctInfoByPrimaryKey", param);
      if (BusiUtil.isNotNull(rbAccts)) {
         return (RbAcct)rbAccts.get(0);
      } else {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      }
   }

   public RbAcct getRbAcctInfoByCondition(RbAcct rbAcct) {
      return (RbAcct)this.daoSupport.selectOne(rbAcct);
   }

   public List<RbAcct> getNvAcct(String acctBranch, String acctCcy, String clientNo, String profitCenter) {
      Map<String, Object> map = new HashMap(16);
      map.put("acctBranch", acctBranch);
      map.put("acctCcy", acctCcy);
      map.put("clientNo", clientNo);
      map.put("profitCenter", profitCenter);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getNVAcct", map);
   }

   public List<RbAcct> getAllMbAcctByBaseAcctNoReversal(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctByBaseAcctNoReversal", param);
   }

   public List<RbAcct> getAllMbAcctByBaseAcctAcctSeqNo(String baseAcctNo, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctByBaseAcctNoAcctSeqNo", param);
   }

   public List<RbAcct> getAllMbAcctByBaseAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getMbAcctByBaseAcctNoAcctSeqNo(String baseAcctNo, String acctSeqNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByBaseAcctNoAcctSeqNo", param);
   }

   public List<RbAcct> getAllMbAcctByBaseAcctNo(String baseAcctNo, String statusFlag, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("statusFlag", statusFlag);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getAllAcctInfo(RbAcct rbAcct) {
      return this.daoSupport.selectList(rbAcct);
   }

   public RbAcct getMbAcctForMinAcctSeqNo(String baseAcctNo, String prodType, String acctCcy, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctForMinAcctSeqNo", param);
   }

   public List<RbAcct> getMbAcctForMinAcctSeqNo2(String baseAcctNo, String prodType, String acctCcy, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctForMinAcctSeqNo2", param);
   }

   public List<RbAcct> getMbAcctInfoByPrimaryKeyList(String baseAcctNo, String prodType, String acctCcy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctInfoByPrimaryKey", param);
   }

   public List<RbAcct> getMbCardAcctForMinAcctSeqNo(String cardNo, String prodType, String acctCcy, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", cardNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbCardAcctForMinAcctSeqNo", param);
   }

   public RbAcct getMbRbAcct(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbRbAcctByBaseAcctNo", param);
   }

   public RbAcct getMbRbAcctCard(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbRbAcctCardByBaseAcctNo", param);
   }

   public List<RbAcct> getAllMbSubAcctByBaseAcctNo(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbSubAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getTMbSubAcctByBaseAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getTMbSubAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getMbSubAcct(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbSubAcct", param);
   }

   public List<RbAcct> getCMbSubAcct(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getCMbSubAcct", param);
   }

   public List<RbAcct> getNewMbSubAcct(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getNewMbSubAcct", param);
   }

   public List<RbAcct> getMbSubAcctForX(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbSubAcctForX", param);
   }

   public List<RbAcct> getMbSubAcctNotSelfCard(String baseAcctNo, String cardNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("cardNo", cardNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbSubAcctNotSelfCard", param);
   }

   public RbAcct getCancelMbLeadAcct(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("runDate", Context.getInstance().getRunDateParse());
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getCancelMbLeadAcctByBaseAcctNo", param);
   }

   public RbAcct getMbLeadAcct(String baseAcctNo, String statusFlag) {
      RbAcctStandardModel rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(baseAcctNo);
      RbAcct rbAcct = new RbAcct();
      BeanUtil.copy(rbAcctStandardModel, rbAcct);
      return rbAcct;
   }

   public RbAcct getMbLeadAcctByStatus(String baseAcctNo, String statusFlag) {
      RbAcctStandardModel rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcctByStatus(baseAcctNo, statusFlag);
      RbAcct rbAcct = new RbAcct();
      BeanUtil.copy(rbAcctStandardModel, rbAcct);
      return rbAcct;
   }

   public RbAcct getMbLeadAcct(String baseAcctNo, boolean flag) {
      RbAcctStandardModel rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(baseAcctNo);
      RbAcct rbAcct = new RbAcct();
      BeanUtil.copy(rbAcctStandardModel, rbAcct);
      return rbAcct;
   }

   public RbAcct getMbLeadAcct(String baseAcctNo) {
      RbAcctStandardModel rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(baseAcctNo);
      RbAcct rbAcct = new RbAcct();
      BeanUtil.copy(rbAcctStandardModel, rbAcct);
      return rbAcct;
   }

   public RbAcct getCloseAcctInfo(String baseAcctNo) {
      String acctStatus = "C";
      RbAcctStandardModel rbAcctStandardModel = this.rbBaseAcctInfo.getCloseAcctInfo(baseAcctNo);
      RbAcct rbAcct = new RbAcct();
      BeanUtil.copy(rbAcctStandardModel, rbAcct);
      return rbAcct;
   }

   public List<RbAcct> getMbAcctByClientNoAndFixedCall(String clientNo, String fixedCall) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("fixedCall", fixedCall);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByClientNoAndFixedCall", param);
   }

   public List<RbAcct> getMbAcctByClientNoAndFixedCallAllStatus(String clientNo, String fixedCall) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("fixedCall", fixedCall);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByClientNoAndFixedCallAllStatus", param);
   }

   public RbAcct getMbSingleAcct(String baseAcctNo, String acctType, String acctCcy, String seqNo) {
      RbAcct acctInfo;
      if (acctType == null && acctCcy == null && seqNo == null) {
         acctInfo = this.getMbLeadAcct(baseAcctNo);
      } else {
         acctInfo = this.getMbAcct(baseAcctNo, acctType, acctCcy, seqNo);
      }

      if (acctInfo == null) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else {
         return acctInfo;
      }
   }

   public RbAcct getMbAcct(String baseAcctNo, String acctType, String acctCcy, String seqNo) {
      String seq = seqNo;
      if (seqNo == null || seqNo.length() == 0) {
         seq = "1";
      }

      List<RbAcct> lAcctInfo = this.getMbAcctList(baseAcctNo, acctType, acctCcy, seq);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("MB4016", new String[]{baseAcctNo});
      } else if (lAcctInfo.size() > 1) {
         throw BusiUtil.createBusinessException("RB4006", new String[]{baseAcctNo, acctType, acctCcy, seq});
      } else {
         return (RbAcct)lAcctInfo.get(0);
      }
   }

   public RbAcct getMbAcctForAgreement(String baseAcctNo, String acctType, String acctCcy, String seqNo) {
      String seq = seqNo;
      if (seqNo == null || seqNo.length() == 0) {
         seq = "1";
      }

      List<RbAcct> lAcctInfo = this.getMbAcctListForAgreement(baseAcctNo, acctType, acctCcy, seq);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("MB4016", new String[]{baseAcctNo});
      } else if (lAcctInfo.size() > 1) {
         throw BusiUtil.createBusinessException("RB4006", new String[]{baseAcctNo, acctType, acctCcy, seq});
      } else {
         return (RbAcct)lAcctInfo.get(0);
      }
   }

   public RbAcct getMbAcctByStatusFlag(String baseAcctNo, String prodType, String acctType, String acctCcy, String seqNo, String statusFlag) {
      String seq = seqNo;
      if (seqNo == null || seqNo.length() == 0) {
         seq = "1";
      }

      List<RbAcct> lAcctInfo = this.getMbAcctListByStatusFlag(baseAcctNo, prodType, acctType, acctCcy, seq, statusFlag);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("MB4016", new String[]{baseAcctNo});
      } else if (lAcctInfo.size() > 1) {
         throw BusiUtil.createBusinessException("RB4006", new String[]{baseAcctNo, acctType, acctCcy, seq});
      } else {
         return (RbAcct)lAcctInfo.get(0);
      }
   }

   public RbAcct getRbAcct(String baseAcctNo, String acctType, String acctCcy, String seqNo, String clientNo) {
      List<RbAcct> lAcctInfo = this.getMbAcctList(baseAcctNo, acctType, acctCcy, seqNo);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else {
         return (RbAcct)lAcctInfo.get(0);
      }
   }

   public List<RbAcct> getMbAcctList(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo) {
      List lAcctInfo = null;

      try {
         String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("prodType", prodType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         param.put("clientNo", clientNo);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
      } catch (Exception var8) {
         log.error(var8.getMessage(), var8);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctListQueryHist(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo) {
      List lAcctInfo = null;

      try {
         String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("prodType", prodType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         param.put("clientNo", clientNo);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelectiveQueryHist", param);
      } catch (Exception var8) {
         log.error(var8.getMessage(), var8);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctListForAgreement(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo) {
      List lAcctInfo = null;

      try {
         String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("prodType", prodType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         param.put("clientNo", clientNo);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelectiveForAgreement", param);
      } catch (Exception var8) {
         log.error(var8.getMessage(), var8);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getAcct(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo) {
      List<RbAcct> lAcctInfo = null;
      RbAcctClientRelation rbAcctClientRelation = this.rbAcctClientRelationRepository.getClientRelationByBaseAcctNoOrCardNo(baseAcctNo);

      try {
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", rbAcctClientRelation.getBaseAcctNo());
         param.put("prodType", prodType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         param.put("clientNo", rbAcctClientRelation.getClientNo());
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective", param);
      } catch (Exception var8) {
         log.error(var8.getMessage(), var8);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctListByStatusFlag(String baseAcctNo, String prodType, String acctType, String acctCcy, String acctSeqNo, String statusFlag) {
      List lAcctInfo = null;

      try {
         String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("prodType", prodType);
         param.put("acctType", acctType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         param.put("clientNo", clientNo);
         param.put("statusFlag", statusFlag);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelectiveByStatusFlag", param);
      } catch (Exception var10) {
         log.error(var10.getMessage(), var10);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctList2(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo) {
      List lAcctInfo = null;

      try {
         String clientNo = this.rbAcctClientRelationRepository.getClientNo(baseAcctNo, (Long)null, (String)null, (String)null);
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("prodType", prodType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         param.put("clientNo", clientNo);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelective2", param);
      } catch (Exception var8) {
         log.error(var8.getMessage(), var8);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctByCarNoOrBaseAcctNo(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String clientNo, String company) {
      List lAcctInfo = null;

      try {
         Map<String, Object> param = new HashMap(5);
         param.put("baseAcctNo", baseAcctNo);
         param.put("prodType", prodType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         param.put("clientNo", clientNo);
         param.put("company", company);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByCarNoOrBaseAcctNoByFour", param);
      } catch (Exception var9) {
         log.error(var9.getMessage(), var9);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctList(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String statusFlag) {
      List<RbAcct> lAcctInfo = null;
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);

      try {
         lAcctInfo = this.getMbAcct(cardBaseAcctNo, prodType, acctCcy, acctSeqNo, statusFlag);
      } catch (Exception var9) {
         log.error("NoShardFoundException:", var9);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctListbyClientNo(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String statusFlag, String clientNo) {
      List<RbAcct> lAcctInfo = null;
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);

      try {
         lAcctInfo = this.getMbAcctByclientNo(cardBaseAcctNo, prodType, acctCcy, acctSeqNo, statusFlag, clientNo);
      } catch (Exception var10) {
         log.error("NoShardFoundException:", var10);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctQueryHistList(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String statusFlag) {
      List<RbAcct> lAcctInfo = null;
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);

      try {
         lAcctInfo = this.getMbAcctQueryHist(cardBaseAcctNo, prodType, acctCcy, acctSeqNo, statusFlag);
      } catch (Exception var9) {
         log.error("NoShardFoundException:", var9);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctList(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String statusFlag, List<String> branchList) {
      List<RbAcct> lAcctInfo = null;
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);

      try {
         lAcctInfo = this.getMbAcctInBranch(cardBaseAcctNo, prodType, acctCcy, acctSeqNo, statusFlag, branchList);
      } catch (Exception var10) {
         log.error("NoShardFoundException:", var10);
      }

      return lAcctInfo;
   }

   public List<RbAcct> getMbAcctListByPage(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String statusFlag, List<String> notInProdTypes, String acctType, String balType, boolean isQueryTAcctType) {
      List<RbAcct> lAcctInfo = null;
      String cardBaseAcctNo = this.getActualBaseAcctNo(baseAcctNo);

      try {
         lAcctInfo = this.getMbAcctByPage(cardBaseAcctNo, prodType, acctCcy, acctSeqNo, statusFlag, notInProdTypes, acctType, balType, isQueryTAcctType);
      } catch (Exception var13) {
         log.error("NoShardFoundException:", var13);
      }

      return lAcctInfo;
   }

   public void updateAcctClass(Long internalKey, String acctClass, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("acctClass", acctClass);
      param.put("clientNo", clientNo);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctClass", param);
   }

   public void updateAcctVoucherStatus(String baseAcctNo, String docType, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("docType", docType);
      param.put("acctSeqNo", acctSeqNo);
      this.daoSupport.update(RbAcct.class.getName() + ".updateAcctVoucherStatus", param);
   }

   public List<RbAcct> getApprLetterAcctInfo(String internalKey, String clientNo, String apprLetterNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("apprLetterNo", apprLetterNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getApprLetterAcctInfo", param);
   }

   /** @deprecated */
   @Deprecated
   public RbAcct getMbAcctByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByInternalKey", param);
   }

   public RbAcct getMbAcctByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByInternalKey", param);
   }

   public RbAcct getInnerMbAcctByInternalKey(Long internalKey, String clientNo, String sourceModule, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("sourceModule", sourceModule);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getInnerMbAcctByInternalKey", param);
   }

   public RbAcct getMbAccts(String baseAcctNo, String acctType, String acctCcy, String seqNo, String statusFlag, List<String> branchList) {
      String seq = seqNo;
      if (seqNo == null || seqNo.length() == 0) {
         seq = "0";
      }

      List<RbAcct> lAcctInfo = this.getMbAcctList(baseAcctNo, acctType, acctCcy, seq, statusFlag, branchList);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else if (lAcctInfo.size() > 1) {
         throw BusiUtil.createBusinessException("RB4006", new String[]{baseAcctNo, acctType, acctCcy, seq});
      } else {
         return (RbAcct)lAcctInfo.get(0);
      }
   }

   public List<RbAcct> getDossMbAcctList(String baseAcctNo, String acctStatus, String acctBranch, String dossDate, String[] clientNos, String isIndividual) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctStatus", acctStatus.split(","));
      param.put("acctBranch", acctBranch);
      param.put("dossDate", DateUtil.parseDate(dossDate));
      param.put("clientNos", clientNos);
      param.put("isIndividual", isIndividual);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getDossAcct", param);
   }

   public List<RbAcct> getDossMbAcctByPage(String baseAcctNo, String acctStatus, String acctBranch, String dossDate, Long[] acctkeyNos, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctStatus", acctStatus.split(","));
      param.put("acctBranch", acctBranch);
      param.put("dossDate", DateUtil.parseDate(dossDate));
      param.put("acctkeys", acctkeyNos);
      param.put("company", company);
      AppHead appHead = Context.getInstance().getAppHead();
      RowArgs rowArgs = PageUtil.convertAppHead(appHead);
      if (BusiUtil.isNotNull(appHead) && BusiUtil.isNotNull(appHead.getTotalNum()) && Integer.parseInt(appHead.getTotalNum()) >= 1) {
         String statementPostfix = RbAcct.class.getName() + ".getDossAcctByPage";
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.getDossMbAcct(param);
      }
   }

   public List<RbAcct> getDossMbAcct(Map param) {
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getDossAcctByPage", param);
   }

   public List<RbAcct> queryMbAcctFordcprecontract(List<RbDcPrecontract> rbDcPrecontracts, String minDate, String maxDate, String isIndividual) {
      List<RbAcct> rbAccts = new ArrayList();
      Iterator var8 = rbDcPrecontracts.iterator();

      while(var8.hasNext()) {
         RbDcPrecontract rbDcPrecontract = (RbDcPrecontract)var8.next();
         String clientNo = rbDcPrecontract.getClientNo();
         Long internalKey = rbDcPrecontract.getInternalKey();
         if (!BusiUtil.isNull(internalKey)) {
            Map<String, Object> param = new HashMap(16);
            param.put("internalKey", String.valueOf(internalKey));
            param.put("minDate", BusiUtil.string2Date(minDate));
            param.put("maxDate", BusiUtil.string2Date(maxDate));
            param.put("clientNo", clientNo);
            param.put("individualFlag", isIndividual);
            RbAcct rbAcct = (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".queryMbAcctFordcprecontract", param);
            if (BusiUtil.isNotNull(rbAcct)) {
               rbAccts.add(rbAcct);
            }
         }
      }

      return rbAccts;
   }

   public List<RbAcct> getMbAcctByBaseAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getMbAcctByCardNoAndClientNo(String cardNo, String clientNo, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", cardNo);
      param.put("clientNo", clientNo);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctByCardNoAndClientNo", param);
   }

   public RbAcct getMbAcctByPage(String baseAcctNo, String acctType, String acctCcy, String seqNo, String clientNo) {
      String seq = seqNo;
      if (seqNo == null || seqNo.length() == 0) {
         seq = "0";
      }

      List<RbAcct> lAcctInfo = this.getMbAcctByPageList(baseAcctNo, acctType, acctCcy, seq, clientNo);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else if (lAcctInfo.size() > 1) {
         throw BusiUtil.createBusinessException("RB4006", new String[]{baseAcctNo, acctType, acctCcy, seq});
      } else {
         return (RbAcct)lAcctInfo.get(0);
      }
   }

   public List<RbAcct> getMbAcctByPageList(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String clientNo) {
      List lAcctInfo = null;

      try {
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("prodType", prodType);
         param.put("acctCcy", acctCcy);
         param.put("acctSeqNo", acctSeqNo);
         lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelectByPage", param);
      } catch (Exception var8) {
         log.info("NoShardFound");
      }

      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else {
         return lAcctInfo;
      }
   }

   public List<RbAcct> queryTotalMbAcct(String clientNo) {
      String company = "";
      boolean isBatch = Context.getInstance().isBatch();
      if (!isBatch && BusiUtil.isEqualN(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW"))) {
         company = Context.getInstance().getProperty("COMPANY");
      }

      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryMbAcctNum", param);
   }

   public RbAcct getMbLeadAcctByBaseAcctNoByCommssion(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbLeadAcctByBaseAcctNoByCommssion", param);
   }

   public List<RbAcct> getThreeByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getThreeByClientNo", param);
   }

   public void updateMaturityDate(Long internalKey, String maturityDate, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("maturityDate", DateUtil.parseDate(maturityDate));
      param.put("clientNo", clientNo);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMaturityDate", param);
   }

   public List<RbAcct> getInternalMbAcctByBranch(String acctBranch, List<String> glTypeList, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctBranch", acctBranch);
      param.put("glTypeList", glTypeList);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getInternalMbAcctByBranch", param);
   }

   public RbAcct getActiveMbAcct(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getActiveMbAcct", param);
   }

   public RbAcct getActiveMbAcctByStatus1(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getActiveMbAcctByStatus1", param);
   }

   public RbAcct getActiveMbAcctByStatus2(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getActiveMbAcctByStatus2", param);
   }

   public RbAcct getCloseMbAcct(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getCloseMbAcct", param);
   }

   public RbAcct getSettleMbAcctByBaseAcctNo(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getSettleMbAcctByBaseAcctNo", param);
   }

   public RbAcct getOthBaseAcctNoMessage(String othBaseAcctNo, String othAcctCcy, String othProdType, String othAcctSeqNo) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setBaseAcctNo(othBaseAcctNo);
      rbAcct.setAcctCcy(othAcctCcy);
      rbAcct.setProdType(othProdType);
      rbAcct.setAcctSeqNo(othAcctSeqNo);
      return (RbAcct)this.daoSupport.selectOne(rbAcct);
   }

   public List<RbAcct> getRbAcctListByBaseAcctNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctListByBaseAcctNo", param);
   }

   public List<RbAcct> getNoTranFlag(String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getNoTranFlag", param);
   }

   public void updateNoTranFlag(Long internalKey, String clientNo, String flag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("noTranFlag", flag);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateNoTranFlag", param);
   }

   public String getOneClassBaseAcc(String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (String)this.daoSupport.selectObject(RbAcct.class.getName() + ".getOneClassBaseAcc", param);
   }

   public List<RbAcct> getSettleAcctByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getSettleAcctByClientNo", param);
   }

   public List<RbAcct> getTAcctByBaseAcctNo(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getTAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getAcctByBaseAcctNoOrCardNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByBaseAcctNoOrCardNo", param);
   }

   public List<RbAcct> getAcctByBaseAcctNoOrCardNos(List<String> baseAcctNos, List<String> agreementIds) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNos", baseAcctNos);
      param.put("agreementIds", agreementIds);
      if (BusiUtil.isNull(baseAcctNos)) {
         throw BusiUtil.createBusinessException("RB3022");
      } else {
         return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByBaseAcctNoOrCardNos", param);
      }
   }

   public List<RbAcct> getAllAcctByBaseAcctNoOrCardNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllAcctByBaseAcctNoOrCardNo", param);
   }

   public List<RbAcct> getFCcyAcctByBaseAcctNo(String baseAcctNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getFCcyAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getAcctByBranchAndIsIndividual(String acctBranch, String isIndividual, List<String> acctNatures) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctBranch", acctBranch);
      param.put("isIndividual", isIndividual);
      param.put("acctNatures", acctNatures);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByBranchAndIsIndividual", param);
   }

   public List<RbAcct> getAcctByBranchAndIsIndividualSplit(String acctBranch, String isIndividual, List<String> acctNatures, String clientNo, List<String> clientNos) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctBranch", acctBranch);
      param.put("isIndividual", isIndividual);
      param.put("acctNatures", acctNatures);
      param.put("clientNo", clientNo);
      param.put("clientNos", clientNos);
      int startRowNum = 1;
      int endRowNum = startRowNum + 999;
      param.put("startRowNum", Integer.valueOf(startRowNum));
      param.put("endRowNum", endRowNum);
      List<RbAcct> result = new ArrayList();
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByBranchAndIsIndividualPage", param);
      if (BusiUtil.isNotNull(rbAccts)) {
         result.addAll(rbAccts);
      }

      while(BusiUtil.isNotNull(rbAccts) && rbAccts.size() > 999) {
         endRowNum = startRowNum + 999;
         param.put("startRowNum", startRowNum);
         param.put("endRowNum", endRowNum);
         rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByBranchAndIsIndividualPage", param);
         if (BusiUtil.isNotNull(rbAccts)) {
            result.addAll(rbAccts);
         }
      }

      return result;
   }

   public void mergeBranchCheckAllClient(String acctBranch, String isIndividual, String toBranch) {
      List<String> natureList = new ArrayList();
      natureList.add("11001");
      natureList.add("11002");
      Map<String, Object> param = new HashMap(16);
      param.put("acctBranch", acctBranch);
      param.put("isIndividual", isIndividual);
      param.put("acctNatures", natureList);
      int startRowNum = 1;
      int endRowNum = startRowNum + 999;
      param.put("startRowNum", Integer.valueOf(startRowNum));
      param.put("endRowNum", endRowNum);
      List<String> clientNos = this.daoSupport.selectList(RbAcct.class.getName() + ".getBranchAllClientNo", param);
      List<RbAcct> fromRbAcctList = this.getAcctByBranchAndIsIndividualSplit(acctBranch, "N", natureList, (String)null, clientNos);
      Map<String, List<RbAcct>> fromMap = this.groupByClientNo(fromRbAcctList);
      List<RbAcct> toRbAcctList = this.getAcctByBranchAndIsIndividualSplit(toBranch, "N", natureList, (String)null, clientNos);
      Map<String, List<RbAcct>> toMap = this.groupByClientNo(toRbAcctList);
      Iterator var13;
      String clientNo;
      if (BusiUtil.isNotNull(clientNos)) {
         var13 = clientNos.iterator();

         while(var13.hasNext()) {
            clientNo = (String)var13.next();
            this.checkAcctNatrueByClientNo(acctBranch, toBranch, (List)fromMap.get(clientNo), (List)toMap.get(clientNo));
         }
      }

      while(BusiUtil.isNotNull(clientNos) && clientNos.size() > 999) {
         startRowNum = endRowNum + 1;
         endRowNum = startRowNum + 999;
         param.put("startRowNum", startRowNum);
         param.put("endRowNum", endRowNum);
         clientNos = this.daoSupport.selectList(RbAcct.class.getName() + ".getBranchAllClientNo", param);
         if (BusiUtil.isNotNull(clientNos)) {
            fromRbAcctList = this.getAcctByBranchAndIsIndividualSplit(acctBranch, "N", natureList, (String)null, clientNos);
            fromMap = this.groupByClientNo(fromRbAcctList);
            toRbAcctList = this.getAcctByBranchAndIsIndividualSplit(toBranch, "N", natureList, (String)null, clientNos);
            toMap = this.groupByClientNo(toRbAcctList);
            var13 = clientNos.iterator();

            while(var13.hasNext()) {
               clientNo = (String)var13.next();
               this.checkAcctNatrueByClientNo(acctBranch, toBranch, (List)fromMap.get(clientNo), (List)toMap.get(clientNo));
            }
         }
      }

   }

   private Map<String, List<RbAcct>> groupByClientNo(List<RbAcct> rbAcctList) {
      if (BusiUtil.isNull(rbAcctList)) {
         return new HashMap();
      } else {
         Map<String, List<RbAcct>> result = (Map)rbAcctList.stream().collect(Collectors.groupingBy(RbAcct::getClientNo, Collectors.toList()));
         return result;
      }
   }

   private void checkAcctNatrueByClientNo(String fromBranch, String toBranch, List<RbAcct> fromRbAcctList, List<RbAcct> toRbAcctList) {
      if (BusiUtil.isNotNull(fromRbAcctList) && BusiUtil.isNotNull(toRbAcctList)) {
         RbAcct fromAcctNature1 = null;
         RbAcct fromAcctNature2 = null;
         RbAcct toAcctNature1 = null;
         RbAcct toAcctNature2 = null;
         Iterator var9 = fromRbAcctList.iterator();

         RbAcct rbAcct;
         while(var9.hasNext()) {
            rbAcct = (RbAcct)var9.next();
            if (BusiUtil.isEquals(rbAcct.getAcctNature(), "11001")) {
               fromAcctNature1 = rbAcct;
            } else if (BusiUtil.isEquals(rbAcct.getAcctNature(), "11002")) {
               fromAcctNature2 = rbAcct;
            }
         }

         var9 = toRbAcctList.iterator();

         while(var9.hasNext()) {
            rbAcct = (RbAcct)var9.next();
            if (BusiUtil.isEquals(rbAcct.getAcctNature(), "11001")) {
               toAcctNature1 = rbAcct;
            } else if (BusiUtil.isEquals(rbAcct.getAcctNature(), "11002")) {
               toAcctNature2 = rbAcct;
            }
         }

         if (BusiUtil.isNotNull(fromAcctNature1) && BusiUtil.isNotNull(toAcctNature1)) {
            throw new BusinessException("RB4510", new Object[]{fromBranch, fromAcctNature1.getBaseAcctNo(), fromAcctNature1.getAcctNature(), toBranch, toAcctNature1.getBaseAcctNo(), toAcctNature1.getAcctNature()});
         }

         if (BusiUtil.isNotNull(fromAcctNature1) && BusiUtil.isNotNull(toAcctNature2)) {
            throw new BusinessException("RB4510", new Object[]{fromBranch, fromAcctNature1.getBaseAcctNo(), fromAcctNature1.getAcctNature(), toBranch, toAcctNature2.getBaseAcctNo(), toAcctNature2.getAcctNature()});
         }

         if (BusiUtil.isNotNull(fromAcctNature2) && BusiUtil.isNotNull(toAcctNature1)) {
            throw new BusinessException("RB4510", new Object[]{fromBranch, fromAcctNature2.getBaseAcctNo(), fromAcctNature2.getAcctNature(), toBranch, toAcctNature1.getBaseAcctNo(), toAcctNature1.getAcctNature()});
         }

         if (BusiUtil.isNotNull(fromAcctNature2) && BusiUtil.isNotNull(toAcctNature2)) {
            throw new BusinessException("RB4510", new Object[]{fromBranch, fromAcctNature2.getBaseAcctNo(), fromAcctNature2.getAcctNature(), toBranch, toAcctNature2.getBaseAcctNo(), toAcctNature2.getAcctNature()});
         }
      } else {
         log.info("There are no general or basic households in the demolished institution or the transferred institution");
      }

   }

   public List<RbAcct> getAcctByDocumentIdAndDocumentType(String documentType, String documentId, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("documentType", documentType);
      param.put("documentId", documentId);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByDocumentIdAndDocumentType", param);
   }

   public List<RbAcct> getAcctByDocumentIdAndDocumentTypeAndBranch(String documentType, String documentId, List<String> branchList, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("documentType", documentType);
      param.put("documentId", documentId);
      param.put("branchList", branchList);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByDocumentIdAndDocumentTypeAndBranch", param);
   }

   public RbAcct getAllMbAcctBlackCondition(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByBaseAcctNoAndClientNo", param);
   }

   public RbAcct getMbAcctByCarNoOrBaseAcctNo4(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String clientNo) {
      return this.getMbAcctByCarNoOrBaseAcctNo4(baseAcctNo, prodType, acctCcy, acctSeqNo, clientNo, (String)null);
   }

   public RbAcct getMbAcctByCarNoOrBaseAcctNo4(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String clientNo, String balType) {
      Map<String, Object> param = new HashMap(5);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      param.put("balType", balType);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByCarNoOrBaseAcctNo4", param);
   }

   public RbAcct getRbAcctByBaseAcctNo(String baseAcctNo, String acctSeqNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getRbAcctByBaseAcctNo", param);
   }

   public List<RbAcct> getRbAcctByBaseAcctNo2(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctByBaseAcctNo2", param);
   }

   public List<RbAcct> getRbAcctByBaseAcctNoClose(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctByBaseAcctNoClose", param);
   }

   public void updateMbAcctNames(String clientNo, String clientName, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctName", clientName);
      param.put("acctDesc", clientName);
      param.put("clientNo", clientNo);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctNames", param);
   }

   public RbAcct getMbAcctFilter(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctFilter", param);
   }

   public List<RbAcct> getRbAcctByProdType(String acctBranch, String prodType) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setAcctBranch(acctBranch);
      rbAcct.setProdType(prodType);
      return this.daoSupport.selectList(rbAcct);
   }

   public List<RbAcct> getOtherCcyAcct(String baseAcctNo, String clientNo, String acctCcy, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("acctCcy", acctCcy);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getOtherCcyAcct", param);
   }

   public List<RbAcct> getMbAcctStatusByClientNo(String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctStatusByClientNo", param);
   }

   public List<RbAcct> selectByCheck(RbAcct rbAcct) {
      Map<String, Object> param = new HashMap();
      param.put("branch", rbAcct.getAcctBranch());
      param.put("ccy", rbAcct.getAcctCcy());
      param.put("prodType", rbAcct.getProdType());
      param.put("runDate", Context.getInstance().getRunDateParse());
      param.put("lastRunDateStr", Context.getInstance().getLastRunDate());
      param.put("lastRunDate", Context.getInstance().getLastRunDateParse());
      param.put("yesterday", Context.getInstance().getYesterdayParse());
      param.put("company", rbAcct.getCompany());
      return this.daoSupport.selectList(RbAcct.class.getName() + ".selectByCheck", param);
   }

   public List<RbAcct> getListDocCondition(String clientNo, String acctCcy, String seqNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("acctSeqNo", seqNo);
      param.put("acctCcy", acctCcy);
      param.put("acctType", "T");
      param.put("acctType", "T");
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getListDocCondition", param);
   }

   public void updateMbAcctNamesByInternalKey(String clientNo, String acctName, String altAcctName, Long internalKey, String documentId, String documentType, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctName", acctName);
      param.put("altAcctName", altAcctName);
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctNamesByInternalKey", param);
   }

   public RbAcct getAcctInfoNoStatus(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getAcctInfoNoStatus", param);
   }

   public List<RbAcct> getBaseAcctNoByAcctStatus(String acctStatus, String individualFlag, String acctBranch) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctStatus", acctStatus);
      param.put("individualFlag", individualFlag);
      param.put("acctBranch", acctBranch);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getBaseAcctNoByAcctStatus", param);
   }

   public List<RbAcct> getAcctByAcctStatusAndAcctOpenDate(String acctStatus, String individualFlag, String acctBranch, Date acctOpenDate, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctStatus", acctStatus);
      param.put("individualFlag", individualFlag);
      param.put("acctBranch", acctBranch);
      param.put("acctOpenDate", acctOpenDate);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByAcctStatusAndAcctOpenDate", param);
   }

   public void updateMbAcctDocumentMsg(String clientNo, String documentId, String documentType, String issCountry, String clientName, String enClientName, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctName", clientName);
      param.put("acctNameEn", enClientName);
      param.put("altAcctName", enClientName);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("issCountry", issCountry);
      param.put("clientNo", clientNo);
      param.put("company", company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctDocumentMsg", param);
   }

   public void updateMbAcctDocumentMsg(String clientNo, String documentId, String documentType, String clientName) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctName", clientName);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("clientNo", clientNo);
      param.put("lastChangeDate", Context.getInstance().getRunDateParse());
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctDocumentMsg", param);
   }

   public int getAcctCountByItems(String acctClass, String clientNo, String acctNature, String acctStopPay, String individualFlag) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctClass", acctClass);
      param.put("clientNo", clientNo);
      param.put("acctNature", acctNature);
      param.put("acctStopPay", acctStopPay);
      param.put("individualFlag", individualFlag);
      return (Integer)this.daoSupport.selectObject(RbAcct.class.getName() + ".getAcctCountByItems", param);
   }

   public RbAcct getAcctByClassClient(String acctClass, String clientNo, String acctNature, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctClass", acctClass);
      param.put("clientNo", clientNo);
      param.put("acctNature", acctNature);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectObject(RbAcct.class.getName() + ".getAcctByClassClient", param);
   }

   public int getRbAcctCountByAcctStatus(List<String> acctStatusList, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctStatusList", acctStatusList);
      param.put("clientNo", clientNo);
      return (Integer)this.daoSupport.selectObject(RbAcct.class.getName() + ".getRbAcctCountByAcctStatus", param);
   }

   public RbAcct getRbAcctByClientNoBaseNOccy(String baseAcctNo, String clientNo, String ccy) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("ccy", ccy);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getRbAcctByClientNoBaseNO", param);
   }

   public RbAcct getMbAcctInfoForIntRateForm(String baseAcctNo, String acctCcy, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("statusFlag", acctCcy);
      param.put("clientNo", clientNo);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctInfoByPrimaryKey", param);
      return BusiUtil.isNotNull(rbAccts) ? (RbAcct)rbAccts.get(0) : null;
   }

   public List<RbAcct> getMbAcctInfoForIntRateFormList(String baseAcctNo, String acctCcy, String clientNo, String company) {
      Map<String, Object> param = new HashMap(3);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctInfoByPrimaryKey", param);
   }

   public void updateRbAcctForReOpen(RbAcct rbAcct) {
      if (BusiUtil.isNotNull(rbAcct)) {
         this.daoSupport.update(RbAcct.class.getName() + ".updateRbAcctForReOpen", rbAcct);
      }

   }

   public List<RbAcct> getListByParentKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getListByParentKey", param);
   }

   public List<RbAcct> getrbAcctByParams(String baseAcctNo, String clientNo, String acctType, String prodType, String branch) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("branch", branch);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getrbAcctByParams", param);
   }

   public List<RbAcct> getAllMbAcctByStageCode(String stageCode, String company) {
      Map<String, Object> param = new HashMap(1);
      param.put("stageCode", stageCode);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctByStageCode", param);
   }

   public List<RbAcct> getMbAcctInfoForInner(String baseAcctNo, String prodType, String clientNo, String branchId, String acctOpenStartDate, String acctOpenEndDate, String acctCloseStartDate, String acctCloseEndDate, String acctStatus, String ccy, String acctCloseUserId, String userId) {
      Map<String, Object> param = new HashMap(8);
      param.put("branchId", branchId);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("prodType", prodType);
      param.put("acctOpenStartDate", BusiUtil.string2Date(acctOpenStartDate));
      param.put("acctOpenEndDate", BusiUtil.string2Date(acctOpenEndDate));
      param.put("acctCloseStartDate", BusiUtil.string2Date(acctCloseStartDate));
      param.put("acctCloseEndDate", BusiUtil.string2Date(acctCloseEndDate));
      param.put("acctStatus", acctStatus);
      param.put("ccy", ccy);
      param.put("acctCloseUserId", acctCloseUserId);
      param.put("userId", userId);
      if (BusiUtil.isNull(baseAcctNo) && BusiUtil.isNull(prodType) && BusiUtil.isNull(clientNo)) {
         param.put("glType", "I");
      }

      return this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctInfoForInner", param);
   }

   public RbAcct getMbAcctInfoForInnerByOth(Long internalKey, String clientNo, String acctOpenStartDate, String acctOpenEndDate, String acctCloseStartDate, String acctCloseEndDate, String acctStatus, String ccy, String company) {
      Map<String, Object> param = new HashMap(8);
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      param.put("acctOpenStartDate", BusiUtil.string2Date(acctOpenStartDate));
      param.put("acctOpenEndDate", BusiUtil.string2Date(acctOpenEndDate));
      param.put("acctCloseStartDate", BusiUtil.string2Date(acctCloseStartDate));
      param.put("acctCloseEndDate", BusiUtil.string2Date(acctCloseEndDate));
      param.put("acctStatus", acctStatus);
      param.put("ccy", ccy);
      param.put("glType", "I");
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctInfoForInnerByOth", param);
   }

   public Map<Long, RbAcct> getAllRbSubAcctByBaseAcctNo(String baseAcctNo, String clientNo, String statusFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("statusFlag", statusFlag);
      param.put("company", company);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctByBaseAcctNo", param);
      Map<Long, RbAcct> acctMap = new HashMap();
      if (BusiUtil.isNotNull(rbAccts)) {
         Iterator var8 = rbAccts.iterator();

         while(var8.hasNext()) {
            RbAcct rbAcct = (RbAcct)var8.next();
            acctMap.put(rbAcct.getInternalKey(), rbAcct);
         }
      }

      return acctMap;
   }

   public List<RbAcct> getRbAcctInfoForInternalKeyL(String startKey, String endKey, TbBranchChangeOut.BranchChangeArray branchChange) {
      Map<String, Object> param = new HashMap(3);
      param.put("acctBranch", branchChange.getOldBranch());
      param.put("startKey", startKey);
      param.put("endKey", endKey);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctInfoForInternalKeyL", param);
   }

   public List<RbAcct> getRbAcctInfoForInternalKeyL1(String startKey, String endKey, TbBranchChangeOut.BranchChangeArray branchChange) {
      Map<String, Object> param = new HashMap(3);
      param.put("acctBranch", branchChange.getNewBranch());
      param.put("startKey", startKey);
      param.put("endKey", endKey);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctInfoForInternalKeyL", param);
   }

   public RbAcct getRbProdAcctInfoForInternalKeyL(Long internalKey) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getRbProdAcctInfoForInternalKeyL", param);
   }

   public List<RbAcct> queryActiveInnerSonAcct(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryActiveInnerSonAcct", param);
   }

   public List<RbAcct> getMbAcct(Map param) {
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryAcctBy4KeysClientNoStatusAndOpenDate", param);
   }

   public List<RbAcct> getAllMbAcctInfoExceptCurrent(String baseAcctNo, String acctSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctInfoExceptCurrent", param);
   }

   public List<RbAcct> getAcctByAgreementId(String agreementId, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByAgreementId", param);
   }

   public List<RbAcct> getAcctByAgreementId(String baseAcctNo, String agreementId, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByFinAgreementId", param);
   }

   public List<RbAcct> getAcctByAgreementId(String baseAcctNo, String agreementId, String clientNo, String statusFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("statusFlag", statusFlag);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByFinAgreementId", param);
   }

   public Long getInternalKeyBySeqNo(String baseAcctNo, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      return (Long)this.daoSupport.selectObject(RbAcct.class.getName() + ".getInternalKeyBySeqNo", param);
   }

   public List<RbAcct> getAcctByFinAgreementId(String agreementId, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByFinAgreementId", param);
   }

   public List<RbAcct> getListByParams(String baseAcctNo, String acctType, String prodType, String branch) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("branch", branch);
      param.put("clientNo", branch);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAcctByParams", param);
   }

   public List<RbAcct> queryCounterDepWtdAcct(String branch, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("branch", branch);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryCounterDepWtdAcct", param);
   }

   public void updateAgreProdType(String agreProdType, long internalKey, String clientNo, String company) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setAgreProdType(agreProdType);
      rbAcct.setInternalKey(internalKey);
      rbAcct.setClientNo(clientNo);
      rbAcct.setCompany(company);
      this.daoSupport.update(RbAcct.class.getName() + ".updateAgreProdType", rbAcct);
   }

   public RbAcct getRbAcctForBalance(String baseAcctNo, String prodType, String acctCcy, String clientNo, String acctSeqNo, String statusFlag) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("clientNo", clientNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("statusFlag", statusFlag);
      List<RbAcct> lAcctInfo = this.daoSupport.selectList(RbAcct.class.getName() + ".getMbAcctSelectiveByStatusFlag", param);
      if (BusiUtil.isNull(lAcctInfo)) {
         throw BusiUtil.createBusinessException("RB4005", new String[]{baseAcctNo});
      } else if (lAcctInfo.size() > 1) {
         throw BusiUtil.createBusinessException("RB4006", new String[]{baseAcctNo, prodType, acctCcy});
      } else {
         return (RbAcct)lAcctInfo.get(0);
      }
   }

   public RbAcct getRbAcctInfo(String baseAcctNo, String prodType, String acctCcy, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", seqNo);
      param.put("clientNo", clientNo);
      List<RbAcct> rbAccts = this.daoSupport.selectList(RbAcct.class.getName() + ".getRbAcctInfo", param);
      if (BusiUtil.isNotNull(rbAccts)) {
         return (RbAcct)rbAccts.get(0);
      } else {
         throw BusiUtil.createBusinessException("RB8216", new String[]{baseAcctNo});
      }
   }

   public void updateCardNo(String cardNo, String baseAcctNo, String clientNo, String InputBaseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", cardNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("InputBaseAcctNo", InputBaseAcctNo);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAcct.class.getName() + ".updateCardNo", param);
   }

   public void updateBaseAcctNo(String cardNo, String baseAcctNo, String clientNo, String voucherNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardNo", cardNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("voucherNo", voucherNo);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAcct.class.getName() + ".updateBaseAcctNo", param);
   }

   public void updateAcctVoucherNo(String baseAcctNo, String acctSeqNo, String voucherNo, String docType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("voucherNo", voucherNo);
      param.put("docType", docType);
      this.daoSupport.update(RbAcct.class.getName() + ".updateAcctVoucherNo", param);
   }

   public List<RbAcct> queryAllAcctByPage(Map<String, Object> param) {
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcct.class.getName() + ".queryAllAcctByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcct> getInnerAcctInfoByConditionByPage(String baseAcctNo, String prodType, String acctSeqNo, String ccy, String glCode, String acctName, String clientNo, String branch, String acctOpenStartDate, String acctOpenEndDate, String acctCloseStartDate, String acctCloseEndDate, String acctStatus, String acctCloseUserId, String creationUserId, String statusFlag) {
      Map<String, Object> param = new HashMap(8);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctSeqNo", acctSeqNo);
      param.put("glCode", glCode);
      param.put("acctName", acctName);
      param.put("branch", branch);
      param.put("ccy", ccy);
      param.put("acctOpenStartDate", acctOpenStartDate);
      param.put("acctOpenEndDate", acctOpenEndDate);
      param.put("acctCloseStartDate", acctCloseStartDate);
      param.put("acctCloseEndDate", acctCloseEndDate);
      param.put("acctStatus", acctStatus);
      param.put("acctCloseUserId", acctCloseUserId);
      param.put("creationUserId", creationUserId);
      param.put("statusFlag", statusFlag);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcct.class.getName() + ".getInnerAcctInfoByConditionByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcct> getAcctListByAcctBranch(String acctBranch, String prodType, String ccy, String accountingStatus, String company) {
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("acctBranch", acctBranch);
      paramMap.put("prodType", prodType);
      paramMap.put("ccy", ccy);
      paramMap.put("accountingStatus", accountingStatus);
      paramMap.put("company", company);
      paramMap.put("runDate", Context.getInstance().getRunDateParse());
      return this.daoSupport.selectList(RbAcct.class.getName().concat(".getAcctListByAcctBranch"), paramMap);
   }

   public List<String> getCountByToBranch(String fromBranch, String toBranch) {
      Map<String, Object> param = new HashMap(16);
      param.put("fromBranch", fromBranch);
      param.put("toBranch", toBranch);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getCountByToBranch", param);
   }

   public List<String> getCountByFromBranch(String fromBranch, String toBranch) {
      Map<String, Object> param = new HashMap(16);
      param.put("fromBranch", fromBranch);
      param.put("toBranch", toBranch);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getCountByFromBranch", param);
   }

   public List<String> getBasicAccount(String fromBranch, String toBranch) {
      Map<String, Object> param = new HashMap(16);
      param.put("fromBranch", fromBranch);
      param.put("toBranch", toBranch);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getBasicAccount", param);
   }

   public List<RbAcct> getDcMbAcctByStageCodes(String[] stageCodes, String clientNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("stageCodes", stageCodes);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getDcMbAcctByStageCodes", param);
   }

   public List<RbAcct> getDcMbAcctByStageCode(String stageCode, String clientNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("stageCode", stageCode);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getDcMbAcctByStageCode", param);
   }

   public List<RbAcct> queryAllStatusMbAcctFordcprecontract(String branch, String prodType, String stageCode, Date minDate, Date maxDate, String isIndividual) {
      Map<String, Object> param = new HashMap(16);
      param.put("branch", branch);
      param.put("prodType", prodType);
      param.put("stageCode", stageCode);
      param.put("minDate", minDate);
      param.put("maxDate", maxDate);
      param.put("individualFlag", isIndividual);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".queryAllStatusMbAcctFordcprecontract", param);
   }

   public void deleteAcct(RbAcct rbAcct) {
      this.daoSupport.delete(rbAcct);
   }

   public void updateMbAcctStatusPrevClose(String internalKey, String statusPrev, String status, String clientNo, Date acctCloseDate, String company, String acctStopPay) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("statusPrev", statusPrev);
      param.put("status", status);
      param.put("acctCloseDate", acctCloseDate);
      param.put("clientNo", clientNo);
      param.put("acctStopPay", acctStopPay);
      this.daoSupport.update(RbAcct.class.getName() + ".updateMbAcctStatusPrevClose", param);
   }

   public List<RbAcct> selectMbAcctByCardNo(String cardNo, String acctSeqNo, String clientNo, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("cardNo", cardNo);
      map.put("acctSeqNo", acctSeqNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".selectMbAcctByCardNo", map);
   }

   public List<RbAcct> selectMbAcctByCardNo(String cardNo, String clientNo) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setCardNo(cardNo);
      rbAcct.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcct);
   }

   public RbAcct selectByCardNoAnd4Keys(String cardNo, String acctSeqNo, String baseAcctNo, String acctCcy, String prodType) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setCardNo(cardNo);
      rbAcct.setAcctSeqNo(acctSeqNo);
      rbAcct.setBaseAcctNo(baseAcctNo);
      rbAcct.setAcctCcy(acctCcy);
      rbAcct.setProdType(prodType);
      return (RbAcct)this.daoSupport.selectOne(rbAcct);
   }

   public List<RbAcct> selectMbAcctByCardNo1(String cardNo, String acctSeqNo, String clientNo, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("cardNo", cardNo);
      map.put("acctSeqNo", acctSeqNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".selectMbAcctByCardNo1", map);
   }

   public List<RbAcct> getAllMbAcctNoConditionWTCK(String baseAcctNo, String company, String clientNo, String[] prodType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("prodType", prodType);
      company = AcctBusiUtil.convertCompany(company);
      if (BusiUtil.isNotNull(company)) {
         param.put("company", company);
      }

      return this.daoSupport.selectList(RbAcct.class.getName() + ".getAllMbAcctNoConditionWTCK", param);
   }

   public List<RbAcct> queryAgreementByPage(String acctCcy, String baseAcctNo, String acctSeqNo, String prodType, String agreementId, String agreementType, String clientNo, String statusFlag, List<String> branchList) {
      Map<String, Object> param = new HashMap();
      param.put("acctCcy", acctCcy);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("prodType", prodType);
      param.put("agreementId", agreementId);
      param.put("agreementType", agreementType);
      param.put("clientNo", clientNo);
      param.put("statusFlag", statusFlag);
      param.put("branchList", branchList);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcct.class.getName() + ".queryAgreementByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcct> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcct> getRecoverRbacct() {
      Map<String, Object> param = new HashMap(16);
      param.put("recoverFlag", "Y");
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getRecoverRbacct", param);
   }

   public void updateRecoverFlag(String baseAcctNo, String acctSeqNo, String recoverFlag) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("recoverFlag", recoverFlag);
      this.daoSupport.update(RbAcct.class.getName() + ".updateRecoverFlag", param);
   }

   public List<RbAcct> getAcctListByAcctBranch(String acctBranch, String prodType, String ccy, String accountingStatus, String company, Date runDate) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setAcctBranch(acctBranch);
      rbAcct.setCompany(company);
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("acctBranch", acctBranch);
      paramMap.put("prodType", prodType);
      paramMap.put("ccy", ccy);
      paramMap.put("accountingStatus", accountingStatus);
      paramMap.put("company", company);
      paramMap.put("runDate", runDate);
      return this.daoSupport.selectList(RbAcct.class.getName().concat(".getAcctListByAcctBranch"), paramMap);
   }

   public List<RbAcct> getRbAcctByJoinAcctFlag(String clientNo, String joinAcctFlag) {
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("clientNo", clientNo);
      paramMap.put("joinAcctFlag", joinAcctFlag);
      return this.daoSupport.selectList(RbAcct.class.getName().concat(".getRbAcctByJoinAcctFlag"), paramMap);
   }

   public RbAcct getTwoClassAcctByCard1(String cardNo, String acctSeqNo, String acctCcy, String prodType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("cardNo", cardNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("acctCcy", acctCcy);
      param.put("prodType", prodType);
      param.put("clientNo", clientNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getTwoClassAcctByCard1", param);
   }

   public RbAcct getTwoClassAcctByBase(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("acctCcy", acctCcy);
      param.put("prodType", prodType);
      param.put("clientNo", clientNo);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getTwoClassAcctByBase", param);
   }

   public void updMbAcctDate(RbAcct rbAccts) {
      super.update(rbAccts);
   }

   public RbAcct getMbAcctByInternalKeyAndCompany(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".getMbAcctByInternalKey", param);
   }

   public List<RbAcct> getOdAccount(String baseAcctNo, String branchId, String acctCcy) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("branchId", branchId);
      param.put("acctCcy", acctCcy);
      return this.daoSupport.selectList(RbAcct.class.getName() + ".getOdAccount", param);
   }

   public RbAcct getRbAcct(Long internalKey, String clientNo) {
      String company = "";
      boolean batch = Context.getInstance().isBatch();
      if (!batch && BusiUtil.isEqualN(String.valueOf(Context.getInstance().getProperty("MULTI_CORP_QUERY_ALLOW")))) {
         company = Context.getInstance().getProperty("COMPANY");
      }

      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcct)this.daoSupport.selectOne(RbAcct.class.getName() + ".selectByPrimaryKeyExt", param);
   }

   public void updateEmpty(RbAcct rbAcct) {
      Map<String, Object> param = new HashMap();
      param.put("acctExec", rbAcct.getAcctExec());
      param.put("autoSettleFlag", rbAcct.getAutoSettleFlag());
      param.put("clientNo", rbAcct.getClientNo());
      param.put("internalKey", rbAcct.getInternalKey());
      this.daoSupport.update(RbAcct.class.getName() + ".updateAcctExec", param);
   }

   public RbAcct getRbAcctBranch(Long internalKey, String clientNo, String tranBranch) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setInternalKey(internalKey);
      rbAcct.setClientNo(clientNo);
      rbAcct.setAcctBranch(tranBranch);
      return (RbAcct)this.daoSupport.selectOne(rbAcct);
   }

   public void updAcctInfoSettleFlag(Long internalKey, String clientNo) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setInternalKey(internalKey);
      rbAcct.setClientNo(clientNo);
      rbAcct.setAutoSettleFlag("N");
      this.daoSupport.update(RbAcct.class.getName() + ".updAcctInfoSettleFlag", rbAcct);
   }

   public Integer getRbAcctInfoStepCount(Date runDate) {
      Map<String, Object> param = new HashMap(2);
      param.put("runDate", runDate);
      int rowCount = this.daoSupport.count(RbAcct.class.getName() + ".getRbAcctInfoStepCount", param);
      return rowCount;
   }

   public RbAcct getRbAcctInfo(String baseAcctNo, String acctSeqNo) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setBaseAcctNo(baseAcctNo);
      rbAcct.setAcctSeqNo(acctSeqNo);
      return (RbAcct)this.daoSupport.selectOne(rbAcct);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateJointAcctFlag(Long internalKey, String clientNo, String jointAcctFlag) {
      RbAcct rbAcct = new RbAcct();
      rbAcct.setJointAcctFlag(jointAcctFlag);
      rbAcct.setInternalKey(internalKey);
      rbAcct.setClientNo(clientNo);
      super.update(rbAcct);
   }
}
