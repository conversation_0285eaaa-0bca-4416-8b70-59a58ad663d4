package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.entity.dbmodel.PtBranch;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class PtBranchRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(PtBranchRepository.class);

   public PtBranch selectBankCodeByBranch(String branch) {
      Map<String, Object> param = new HashMap();
      param.put("branch", branch);
      return (PtBranch)this.daoSupport.selectOne(PtBranch.class.getName() + ".selectBankCodeByBranch", param);
   }
}
