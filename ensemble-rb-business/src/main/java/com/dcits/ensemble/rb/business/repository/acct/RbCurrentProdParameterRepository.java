package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbCurrentProdParameter;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbCurrentProdParameterRepository extends BusinessRepository {
   public RbCurrentProdParameter getRbCurrentProdParameter(String dqProdType) {
      RbCurrentProdParameter rbCurrentProdParameter = new RbCurrentProdParameter();
      rbCurrentProdParameter.setDqProdType(dqProdType);
      return (RbCurrentProdParameter)this.daoSupport.selectOne(rbCurrentProdParameter);
   }
}
