package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenFundSource;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Iterator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbOpenFundSourceRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbOpenFundSourceRepository.class);

   public void createRbOpenFundSourceList(List<RbOpenFundSource> rbOpenFundSourceList) {
      Iterator var2 = rbOpenFundSourceList.iterator();

      while(var2.hasNext()) {
         RbOpenFundSource rbOpenFundSource = (RbOpenFundSource)var2.next();
         if (BusiUtil.isNotNull(rbOpenFundSource)) {
            super.insert(rbOpenFundSource);
         }
      }

   }
}
