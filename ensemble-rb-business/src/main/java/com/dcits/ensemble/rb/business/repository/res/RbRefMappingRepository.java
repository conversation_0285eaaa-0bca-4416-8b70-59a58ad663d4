package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbRefMapping;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbRefMappingRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbRefMappingRepository.class);

   public RbRefMapping getMbResSeqNo(String reference, long internalKey, String eventType) {
      Map<String, Object> param = new HashMap(5);
      param.put("reference", reference);
      param.put("internalKey", internalKey);
      param.put("eventType", eventType);
      return (RbRefMapping)this.daoSupport.selectOne(RbRefMapping.class.getName() + ".selectOne", param);
   }

   public int createMbRefMap(RbRefMapping mbRefMapping) {
      int res = 0;
      if (BusiUtil.isNotNull(mbRefMapping)) {
         res = super.insert(mbRefMapping);
      }

      return res;
   }

   public int deleteByResSeqNo(String resSeqNo, String limitRef, String reference, String eventType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("resSeqNo", resSeqNo);
      param.put("limitRef", limitRef);
      param.put("reference", reference);
      param.put("eventType", eventType);
      param.put("clientNo", clientNo);
      return this.daoSupport.delete(RbRefMapping.class.getName() + ".delete", param);
   }

   public int update(RbRefMapping mbRefMapping) {
      return this.daoSupport.update(RbRefMapping.class.getName() + ".updateByInternalKey", mbRefMapping);
   }
}
