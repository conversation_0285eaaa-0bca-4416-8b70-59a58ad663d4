package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherChangeInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbVoucherChangeInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherChangeInfoRepository.class);

   public void createMbVoucherChangeInfo(RbVoucherChangeInfo mbVoucherChangeInfo) {
      if (BusiUtil.isNotNull(mbVoucherChangeInfo)) {
         super.insert(mbVoucherChangeInfo);
      }

   }

   public List<RbVoucherChangeInfo> getMbVocherChangeInfos(String baseAcctNo, String acctSeqNo, String docType, Date startDate, Date endDate, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("docType", docType);
      param.put("acctSeqNo", acctSeqNo);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      return this.daoSupport.selectList(RbVoucherChangeInfo.class.getName() + ".getMbVocherChangeInfos", param);
   }

   public List<RbVoucherChangeInfo> getMbVocherChangeInfosByOption(String baseAcctNo, String acctSeqNo, String docType, Date startDate, Date endDate, String voucherChangeType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("docType", docType);
      param.put("startDate", startDate);
      param.put("acctSeqNo", acctSeqNo);
      param.put("endDate", endDate);
      param.put("voucherChangeType", voucherChangeType);
      return this.daoSupport.selectList(RbVoucherChangeInfo.class.getName() + ".getMbVocherChangeInfos", param);
   }

   public RbVoucherChangeInfo getMbVocherChangeReturnInfosByOption(String baseAcctNo, String docType, String voucherChangeType, String voucherNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      param.put("voucherChangeType", voucherChangeType);
      return (RbVoucherChangeInfo)this.daoSupport.selectOne(RbVoucherChangeInfo.class.getName() + ".getMbVocherChangeReturnInfos", param);
   }

   public RbVoucherChangeInfo getMbVocherChangeReturnInfosByOption2(String baseAcctNo, String docType, String voucherChangeType, String voucherNo, String newVoucherNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      param.put("voucherChangeType", voucherChangeType);
      param.put("newVoucherNo", newVoucherNo);
      return (RbVoucherChangeInfo)this.daoSupport.selectOne(RbVoucherChangeInfo.class.getName() + ".getMbVocherChangeReturnInfos", param);
   }

   public RbVoucherChangeInfo getChangeInfoByNewDoc(String baseAcctNo, String newDocType, String newVoucherNo, String voucherChangeType) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("newDocType", newDocType);
      param.put("newVoucherNo", newVoucherNo);
      param.put("voucherChangeType", voucherChangeType);
      return (RbVoucherChangeInfo)this.daoSupport.selectOne(RbVoucherChangeInfo.class.getName() + ".getChangeInfoByNewDoc", param);
   }
}
