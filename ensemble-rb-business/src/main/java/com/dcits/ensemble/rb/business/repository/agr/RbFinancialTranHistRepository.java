package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialTranHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFinancialTranHistRepository extends BusinessRepository {
   public void createRbFinancialTranHist(RbFinancialTranHist rbFinancialTranHist) {
      super.insert(rbFinancialTranHist);
   }

   public List<RbFinancialTranHist> getFinTranHistByInternalKey(Long internalKey, Date startDate, Date endDate) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      return this.daoSupport.selectList(RbFinancialTranHist.class.getName() + ".getFinTranHistByInternalKey", param);
   }

   public List<RbFinancialTranHist> getFinTranHistByInternalKeyClientNo(Long internalKey, Date startDate, Date endDate, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbFinancialTranHist.class.getName() + ".getFinTranHistByInternalKeyClientNo", param);
   }
}
