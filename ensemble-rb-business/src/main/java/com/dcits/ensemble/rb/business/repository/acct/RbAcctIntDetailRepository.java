package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BigDecimalUtil;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbAcctIntDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctIntDetailRepository.class);

   public RbAcctIntDetail getMbAcctIntDetail(Long internalKey, String intClass, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setIntClass(intClass);
      rbAcctIntDetail.setClientNo(clientNo);
      return (RbAcctIntDetail)this.daoSupport.selectOne(rbAcctIntDetail);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailAll(Long internalKey, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcctIntDetail);
   }

   public RbAcctIntDetail getMbAcctIntDetail(Long internalKey, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      return (RbAcctIntDetail)this.daoSupport.selectOne(rbAcctIntDetail);
   }

   public RbAcctIntDetail getMbAcctIntDetailByIntClass(Long internalKey, String clientNo, String intClass) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      rbAcctIntDetail.setIntClass(intClass);
      return (RbAcctIntDetail)this.daoSupport.selectOne(rbAcctIntDetail);
   }

   public RbAcctIntDetail getMbAcctIntDetailByInt(Long internalKey, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      rbAcctIntDetail.setIntClass("INT");
      return (RbAcctIntDetail)this.daoSupport.selectOne(rbAcctIntDetail);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailAll(RbAcctIntDetail rbAcctIntDetail) {
      return this.daoSupport.selectList(rbAcctIntDetail);
   }

   public void updateByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      super.update(rbAcctIntDetail);
   }

   public void deleteByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      super.delete(rbAcctIntDetail);
   }

   public void updateIntDetailByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntDetailByPrimaryKey", rbAcctIntDetail);
   }

   public void updateDacByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      String runDate = Context.getInstance().getRunDate();
      rbAcctIntDetail.setLastChangeDate(BusiUtil.getDate(runDate));
      super.update(rbAcctIntDetail);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateWithNullByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateRbAcctIntDetailWithNull", rbAcctIntDetail);
   }

   public void updateNextCycleDateByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateNextCycleDateByPrimaryKey", rbAcctIntDetail);
   }

   public void updateCycleFreqByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateCycleFreqByPrimaryKey", rbAcctIntDetail);
   }

   public void uodateRealRateByInternalKetAndIntClass(RbAcctIntDetail rbAcctIntDetail) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", rbAcctIntDetail.getInternalKey());
      map.put("clientNo", rbAcctIntDetail.getClientNo());
      map.put("intClass", rbAcctIntDetail.getIntClass());
      map.put("realRate", rbAcctIntDetail.getRealRate());
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".uodateRealRateByInternalKetAndIntClass", map);
   }

   public Map<String, RbAcctIntDetail> getMbAcctIntDetailMap(Long internalKey, String clientNo) {
      Map<String, RbAcctIntDetail> intDetailMap = null;
      RbAcctIntDetail acctIntDetail = new RbAcctIntDetail();
      acctIntDetail.setInternalKey(internalKey);
      acctIntDetail.setClientNo(clientNo);
      List<RbAcctIntDetail> rbAcctIntDetails = this.daoSupport.selectList(acctIntDetail);
      if (BusiUtil.isNotNull(rbAcctIntDetails)) {
         intDetailMap = new HashMap();
         Iterator var6 = rbAcctIntDetails.iterator();

         while(var6.hasNext()) {
            RbAcctIntDetail rbAcctIntDetail = (RbAcctIntDetail)var6.next();
            intDetailMap.put(rbAcctIntDetail.getIntClass(), rbAcctIntDetail);
         }
      }

      return intDetailMap;
   }

   public void updMbAcctIntDtlBatch(List<RbAcctIntDetail> intDetails) {
      super.updateAddBatch(intDetails);
   }

   public void createMbAcctIntDb(List<RbAcctIntDetail> mbAcctInts) {
      super.insertAddBatch(mbAcctInts);
   }

   public RbAcctIntDetail getIntDetailByPrimaryKey(Map<String, Object> map) {
      map.put("company", Context.getInstance().getCompany());
      return (RbAcctIntDetail)this.daoSupport.selectOne(RbAcctIntDetail.class.getName() + ".selectIntDetailForUpdate", map);
   }

   public List<RbAcctIntDetail> selectRbIntSummary(Long internalKey, String clientNo, String company) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      map.put("clientNo", clientNo);
      map.put("company", company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".selectRbIntSummary", map);
   }

   public List<RbAcctIntDetail> getRbAcctIntDetails(Long internalKey, String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      map.put("clientNo", clientNo);
      map.put("runDate", Context.getInstance().getRunDateParse());
      map.put("lastRunDate", Context.getInstance().getLastRunDateParse());
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".rbPcpAcctCycleStep", map);
   }

   public void updatePrevDetailForEod(Map<String, Object> map, String company) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", company);
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updatePrevDetailForEod", map);
   }

   public void updateDriPrevDetailForEod(Map<String, Object> map) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateDriPrevDetailForEod", map);
   }

   public void updateAccruedPrevDetailForEod(Map<String, Object> map) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAccruedPrevDetailForEod", map);
   }

   public void updateIntLayerAgreement(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntLayerAgreement", map);
   }

   public List<RbAcctIntDetail> getRbIntDetailByActive(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getRbIntDetailByActive", param);
   }

   public List<RbAcctIntDetail> getRbAcctIntDetailInt(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getRbAcctIntDetailInt", param);
   }

   public void updateAcctRate(Long internalKey, String clientNo, String intClass, BigDecimal acctSpreadRate, BigDecimal acctPercentRate, BigDecimal realRate, String company) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      map.put("clientNo", clientNo);
      map.put("intClass", intClass);
      map.put("acctSpreadRate", acctSpreadRate);
      map.put("acctPercentRate", acctPercentRate);
      map.put("realRate", realRate);
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", company);
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAcctRate", map);
   }

   public void updateAggIntAdj(RbAcctIntDetail rbAcctIntDetail) {
      rbAcctIntDetail.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctIntDetail.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAggIntAdj", rbAcctIntDetail);
   }

   public void updateIntDetailByRenew(RbAcctIntDetail rbAcctIntDetail) {
      rbAcctIntDetail.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctIntDetail.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntDetailByRenew", rbAcctIntDetail);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateIntDetailByDrawAmt(RbAcctIntDetail rbAcctIntDetail) {
      rbAcctIntDetail.setCompany(Context.getInstance().getCompany());
      rbAcctIntDetail.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntDetailByRenew", rbAcctIntDetail);
   }

   public void updateAdjPrevDetailForEod(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAdjPrevDetailForEod", map);
   }

   public void updateAdvPrevDetailForEod(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAdvPrevDetailForEod", map);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailAllForLock(Long internalKey, String clientNo, String company) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      rbAcctIntDetail.setCompany(company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getMbAcctIntDetailAllForLock", rbAcctIntDetail);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailAllForLockByIntClass(Long internalKey, String clientNo, String company, String intClass) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      rbAcctIntDetail.setCompany(company);
      rbAcctIntDetail.setIntClass(intClass);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getMbAcctIntDetailAllForLock", rbAcctIntDetail);
   }

   public RbAcctIntDetail getMbAcctIntDetailIntForLock(Long internalKey, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      rbAcctIntDetail.setIntClass("INT");
      return (RbAcctIntDetail)this.daoSupport.selectOne(RbAcctIntDetail.class.getName() + ".getMbAcctIntDetailIntForLock", rbAcctIntDetail);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailList(List<Long> longs) {
      Map<String, Object> param = new HashMap();
      param.put("longs", longs);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getMbAcctIntDetailList", param);
   }

   public Map<Long, RbAcctIntDetail> selectLastIntByInternalKeys(Map<String, Object> param) {
      List<RbAcctIntDetail> rbAcctIntDetails = this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".selectLastIntByInternalKeys", param);
      Map<Long, RbAcctIntDetail> intDetailMap = new HashMap();
      rbAcctIntDetails.forEach((a) -> {
         RbAcctIntDetail rbAcctIntDetail = (RbAcctIntDetail)intDetailMap.get(a.getInternalKey());
         if (BusiUtil.isNull(rbAcctIntDetail)) {
            intDetailMap.put(a.getInternalKey(), a);
         } else {
            rbAcctIntDetail.setIntAccruedPrev(BigDecimalUtil.nullToZero(a.getIntAccruedPrev()).add(BigDecimalUtil.nullToZero(rbAcctIntDetail.getIntAccruedPrev())));
            rbAcctIntDetail.setIntAccruedLastPrev(BigDecimalUtil.nullToZero(a.getIntAccruedLastPrev()).add(BigDecimalUtil.nullToZero(rbAcctIntDetail.getIntAccruedLastPrev())));
            rbAcctIntDetail.setIntAdjPrev(BigDecimalUtil.nullToZero(a.getIntAdjPrev()).add(BigDecimalUtil.nullToZero(rbAcctIntDetail.getIntAdjPrev())));
            rbAcctIntDetail.setIntAdjLastPrev(BigDecimalUtil.nullToZero(a.getIntAdjLastPrev()).add(BigDecimalUtil.nullToZero(rbAcctIntDetail.getIntAdjLastPrev())));
            rbAcctIntDetail.setDiscntIntPrev(BigDecimalUtil.nullToZero(a.getDiscntIntPrev()).add(BigDecimalUtil.nullToZero(rbAcctIntDetail.getDiscntIntPrev())));
            rbAcctIntDetail.setDiscntIntLastPrev(BigDecimalUtil.nullToZero(a.getDiscntIntLastPrev()).add(BigDecimalUtil.nullToZero(rbAcctIntDetail.getDiscntIntLastPrev())));
         }

      });
      return intDetailMap;
   }

   public void updateLastPrevDetailForEod(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateLastPrevDetailForEod", map);
   }

   public void updateClientNo(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateClientNo", map);
   }
}
