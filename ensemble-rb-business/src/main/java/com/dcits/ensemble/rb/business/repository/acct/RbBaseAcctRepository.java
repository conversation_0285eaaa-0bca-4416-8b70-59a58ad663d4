package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct;
import com.dcits.ensemble.rb.business.model.acct.SourceModuleEnum;
import com.dcits.ensemble.rb.business.model.cm.restful.tb.TbBranchChangeOut;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbBaseAcctRepository extends BusinessRepository {
   public int updateBaseAcctByClientNo(RbBaseAcct rbBaseAcct) {
      rbBaseAcct.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbBaseAcct.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.update(RbBaseAcct.class.getName() + ".updateBaseAcctByClientNo", rbBaseAcct);
   }

   public void updateBaseAcctForReOpen(RbBaseAcct rbBaseAcct) {
      if (BusiUtil.isNotNull(rbBaseAcct)) {
         rbBaseAcct.setTranTimestamp(BusiUtil.getTranTimestamp26());
         this.daoSupport.update(RbBaseAcct.class.getName() + ".updateBaseAcctForReOpen", rbBaseAcct);
      }

   }

   public RbBaseAcct getBaseAcct(RbBaseAcct rbBaseAcct) {
      return (RbBaseAcct)this.daoSupport.selectOne(rbBaseAcct);
   }

   public int createRbBaseAcct(RbBaseAcct rbBaseAcct) {
      return super.insert(rbBaseAcct);
   }

   public RbBaseAcct getCancelRbBaseAcct(String baseAcctNo, String acctCloseDate) {
      RbBaseAcct rbBaseAcct = new RbBaseAcct();
      rbBaseAcct.setBaseAcctNo(baseAcctNo);
      rbBaseAcct.setAcctStatus("C");
      return (RbBaseAcct)this.daoSupport.selectOne(rbBaseAcct);
   }

   public void updateBaseAcctByClientNoForReversal(RbBaseAcct rbBaseAcct) {
      if (BusiUtil.isNotNull(rbBaseAcct)) {
         rbBaseAcct.setTranTimestamp(BusiUtil.getTranTimestamp26());
         this.daoSupport.update(RbBaseAcct.class.getName() + ".updateBaseAcctByClientNoForReversal", rbBaseAcct);
      }

   }

   public List<RbBaseAcct> getMainAcctList(RbBaseAcct rbBaseAcct) {
      if (BusiUtil.isNull(rbBaseAcct.getBaseAcctNo()) && BusiUtil.isNull(rbBaseAcct.getCardNo()) && BusiUtil.isNull(rbBaseAcct.getClientNo())) {
         throw new BusinessException("RB5183");
      } else {
         return this.daoSupport.selectList(rbBaseAcct);
      }
   }

   public List<RbBaseAcct> getBaseAcctByClientNo(String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByClientNo", param);
   }

   public List<RbBaseAcct> getBaseAcctByClientNoAndAcctStatus(String clientNo, String acctStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByClientNo", param);
   }

   public List<RbBaseAcct> getBaseAcctByClientNoByStatusFlag1(String clientNo, String acctStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByClientNoByStatusFlag1", param);
   }

   public List<RbBaseAcct> getBaseAcctByClientNoByStatusFlag2(String clientNo, String acctStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByClientNoByStatusFlag2", param);
   }

   public List<RbBaseAcct> getBaseAcctByClientNo(String clientNo) {
      return this.getBaseAcctByClientNoAndAcctStatus(clientNo, (String)null);
   }

   public RbBaseAcct getBaseAcctByBaseAcct(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(2);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("company", company);
      return (RbBaseAcct)this.daoSupport.selectOne(RbBaseAcct.class.getName() + ".getBaseAcctByClientNo", param);
   }

   public void updateMbBaseAcctNames(String clientNo, String clientName) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctName", clientName);
      param.put("acctDesc", clientName);
      param.put("clientNo", clientNo);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBaseAcct.class.getName() + ".updateMbBaseAcctNames", param);
   }

   public void updateMbBaseAcctDocumentMsg(String clientNo, String documentId, String documentType, String clientName) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctName", clientName);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("clientNo", clientNo);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBaseAcct.class.getName() + ".updateMbBaseAcctDocumentMsg", param);
   }

   public void updateMbBaseAcctDocumentMsg(String clientNo, String documentId, String documentType, String issCountry, String clientName, String enClientName, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctName", clientName);
      param.put("altAcctName", enClientName);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("issCountry", issCountry);
      param.put("clientNo", clientNo);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      param.put("company", company);
      this.daoSupport.update(RbBaseAcct.class.getName() + ".updateMbBaseAcctDocumentMsg", param);
   }

   public int updateByPrimaryKeyAndClientNo(RbBaseAcct rbBaseAcct) {
      rbBaseAcct.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbBaseAcct.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.update(RbBaseAcct.class.getName() + ".updateBaseAcctByInternalKeyAndClientNo", rbBaseAcct);
   }

   public List<RbBaseAcct> getMbBaseAcctByBaseAcctNoOrCardNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getMbBaseAcctByBaseAcctNoOrCardNo", param);
   }

   public RbBaseAcct getMapMbBaseAcctByBaseAcctNoOrCardNo(String baseAcctNo, String clienNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clienNo);
      return (RbBaseAcct)this.daoSupport.selectOne(RbBaseAcct.class.getName() + ".getMbBaseAcctByBaseAcctNoOrCardNo", param);
   }

   public RbBaseAcct getMbBaseAcctByBaseAcctNoAndSeqno(String baseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("baseAcctNo", baseAcctNo);
      return (RbBaseAcct)this.daoSupport.selectOne(RbBaseAcct.class.getName() + ".getMbBaseAcctByBaseAcctNoAndSeqno", param);
   }

   public RbBaseAcct getMapMbBaseAcctForInner(String baseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("baseAcctNo", baseAcctNo);
      param.put("sourceModule", SourceModuleEnum.GL.getCode());
      return (RbBaseAcct)this.daoSupport.selectOne(RbBaseAcct.class.getName() + ".getMbBaseAcctByBaseAcctNoOrCardNo", param);
   }

   public List<RbBaseAcct> getBaseAcctByAcctStatus() {
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByAcctStatus", new HashMap());
   }

   public RbBaseAcct getBaseAcctByInternalKey(Long parentInternalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", parentInternalKey);
      param.put("clientNo", clientNo);
      return (RbBaseAcct)this.daoSupport.selectOne(RbBaseAcct.class.getName() + ".getBaseAcctByInternalKey", param);
   }

   public RbBaseAcct getBaseAcctForInnerByInternalKey(Long parentInternalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", parentInternalKey);
      param.put("clientNo", clientNo);
      param.put("sourceModule", SourceModuleEnum.GL.getCode());
      return (RbBaseAcct)this.daoSupport.selectOne(RbBaseAcct.class.getName() + ".getBaseAcctByInternalKey", param);
   }

   public List<RbBaseAcct> getBaseAcctByInternalKeys(@NonNull List<Long> internalKeys) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKeys", internalKeys);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByInternalKeys", param);
   }

   public List<RbBaseAcct> getBaseAcctByProdType(String prodType, String company) {
      Map<String, Object> param = new HashMap(1);
      param.put("prodType", prodType);
      param.put("company", company);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByProdType", param);
   }

   public RbBaseAcct getBaseAcctByBaseAcctNo(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(2);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbBaseAcct)this.daoSupport.selectOne(RbBaseAcct.class.getName() + ".getBaseAcctByBaseAcctNo", param);
   }

   public List<RbBaseAcct> getBaseAcctByParams(String baseAcctNo, String acctType, String prodType, String branch) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("branch", branch);
      param.put("clientNo", branch);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByParams", param);
   }

   public void updateCardNoByInternalkey(String newCardNo, Long internalKey, String cardNo, String newProdType) {
      Map<String, Object> param = new HashMap(2);
      param.put("newCardNo", newCardNo);
      param.put("internalKey", internalKey);
      param.put("cardNo", cardNo);
      param.put("newProdType", newProdType);
      this.daoSupport.update(RbBaseAcct.class.getName() + ".updateCardNoByInternalkey", param);
   }

   public void updateCardNoByInternalkey(String newCardNo, Long internalKey, String cardNo, String newProdType, String clientNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("newCardNo", newCardNo);
      param.put("internalKey", internalKey);
      param.put("cardNo", cardNo);
      param.put("newProdType", newProdType);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbBaseAcct.class.getName() + ".updateCardNoByInternalkey1", param);
   }

   public void updateBaseAcctNoByInternalkey(String newCardNo, Long internalKey) {
      Map<String, Object> param = new HashMap(2);
      param.put("newCardNo", newCardNo);
      param.put("internalKey", internalKey);
      this.daoSupport.update(RbBaseAcct.class.getName() + ".updateBaseAcctNoByInternalkey", param);
   }

   public List<RbBaseAcct> getMbBaseAcctAllByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getMbBaseAcctAllByClientNo", param);
   }

   public List<RbBaseAcct> getRbBaseAcctInfoForInternalKeyL(String startKey, String endKey, TbBranchChangeOut.BranchChangeArray branchChange) {
      Map<String, Object> param = new HashMap(3);
      param.put("acctBranch", branchChange.getOldBranch());
      param.put("startKey", startKey);
      param.put("endKey", endKey);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getRbBaseAcctInfoForInternalKeyL", param);
   }

   public List<RbBaseAcct> getRbBaseAcctInfoForInternalKeyL1(String startKey, String endKey, TbBranchChangeOut.BranchChangeArray branchChange) {
      Map<String, Object> param = new HashMap(3);
      param.put("acctBranch", branchChange.getNewBranch());
      param.put("startKey", startKey);
      param.put("endKey", endKey);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getRbBaseAcctInfoForInternalKeyL", param);
   }

   public void updateRbBaseAcctClientNo(RbBaseAcct rbBaseAcct) {
      if (BusiUtil.isNotNull(rbBaseAcct)) {
         this.daoSupport.update(RbBaseAcct.class.getName() + ".updateRbBaseAcctClientNo", rbBaseAcct);
      }

   }

   public RbBaseAcct selectForUpdate(String baseAcctNo) {
      if (BusiUtil.isNull(baseAcctNo)) {
         return null;
      } else {
         RbBaseAcct result = null;
         RbBaseAcct rbBaseAcct = new RbBaseAcct();
         rbBaseAcct.setBaseAcctNo(baseAcctNo);
         List<RbBaseAcct> rbBaseAccts = this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getMbBaseAcctByAcctNoForUpdate", rbBaseAcct);
         if (BusiUtil.isNotNull(rbBaseAccts)) {
            result = (RbBaseAcct)rbBaseAccts.get(0);
         }

         return result;
      }
   }

   public int updateAcctStatusByBaseAcctNoAndClientNo(RbBaseAcct rbBaseAcct) {
      rbBaseAcct.setTranTimestamp(BusiUtil.getTranTimestamp26());
      return this.daoSupport.update(RbBaseAcct.class.getName() + ".updateAcctStatusByBaseAcctNoAndClientNo", rbBaseAcct);
   }

   public int updateAcctStatusByBaseAcctNoAndVoucher(RbBaseAcct rbBaseAcct) {
      rbBaseAcct.setTranTimestamp(BusiUtil.getTranTimestamp26());
      return this.daoSupport.update(RbBaseAcct.class.getName() + ".updateAcctStatusByBaseAcctNoAndVoucher", rbBaseAcct);
   }

   public List<RbBaseAcct> getBaseAcctByNoNew1(String baseAcctNo, String status, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctStatus", status);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbBaseAcct.class.getName() + ".getBaseAcctByNoNew1", param);
   }
}
