package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbTranHistAttach;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Iterator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbTranHistAttachRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbTranHistAttachRepository.class);

   public void createRbTranHistAttachList(List<RbTranHistAttach> rbTranHistAttachList) {
      Iterator var2 = rbTranHistAttachList.iterator();

      while(var2.hasNext()) {
         RbTranHistAttach rbTranHistAttach = (RbTranHistAttach)var2.next();
         if (BusiUtil.isNotNull(rbTranHistAttach)) {
            super.insert(rbTranHistAttach);
         }
      }

   }
}
