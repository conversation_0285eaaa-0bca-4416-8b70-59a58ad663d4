package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.rb.business.bc.component.cm.operate.util.PageQueryUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbMsaTransferDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class RbMsaTransferDetailRepository extends BusinessRepository {
   @Resource
   private PageQueryUtil pageQueryUtil;

   public RbMsaTransferDetail selectBy4keysTranDate(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, Date startDate, Date endDate, String transferStatus) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("transferStatus", transferStatus);
      return (RbMsaTransferDetail)this.daoSupport.selectOne(RbMsaTransferDetail.class.getName() + ".selectBy4keysTranDate", param);
   }

   public List<RbMsaTransferDetail> selectMsaTransferDetailByPage(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, Date startDate, Date endDate, String transferStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("transferStatus", transferStatus);
      param.put("clientNo", clientNo);
      return this.pageQueryUtil.selectByPage(RbMsaTransferDetail.class.getName() + ".selectBy4keysTranDate", param);
   }
}
