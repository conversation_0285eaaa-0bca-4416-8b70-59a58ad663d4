package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFinAgrtType;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFinAgrtTypeRepository extends BusinessRepository {
   public RbFinAgrtType selectByFinType(String finType) {
      RbFinAgrtType rbFinAgrtType = new RbFinAgrtType();
      rbFinAgrtType.setFinType(finType);
      return (RbFinAgrtType)this.daoSupport.selectOne(rbFinAgrtType);
   }

   public RbFinAgrtType getMbFinAgrtTypeByFinType(String finType) {
      Map<String, Object> param = new HashMap();
      param.put("finType", finType);
      return (RbFinAgrtType)this.daoSupport.selectOne(RbFinAgrtType.class.getName() + ".getMbFinAgrtTypeByFinType", param);
   }
}
