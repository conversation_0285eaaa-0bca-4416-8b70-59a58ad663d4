package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.comet.commons.tuple.TwoTuple;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.comet.flow.BusinessParamContext;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.common.constant.RbDict;
import com.dcits.ensemble.rb.business.common.util.AppHeadUtil;
import com.dcits.ensemble.rb.business.common.util.FmUtil;
import com.dcits.ensemble.rb.business.common.util.MqUtil;
import com.dcits.ensemble.rb.business.common.util.context.BusiContextUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientRelation;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct;
import com.dcits.ensemble.rb.business.model.cm.restful.cif.CifBaseInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAcctClientRelationRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctClientRelationRepository.class);
   private static final String Y = "Y";
   private static final String N = "N";
   private static final String MAX_SEQ_NO = "MAX_SEQ_NO";
   @Resource
   private FmBaseStor fmBaseStor;

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void createMAcctClientRelationDb(List<RbAcctClientRelation> rbAcctClientRelations) {
      if (BusiUtil.isNotNull(rbAcctClientRelations)) {
         for(int i = 0; i < rbAcctClientRelations.size(); ++i) {
            RbAcctClientRelation rbAcctClientRelationOld = this.isExist((RbAcctClientRelation)rbAcctClientRelations.get(i));
            if (BusiUtil.isNull(rbAcctClientRelationOld)) {
               super.insert((EnsBaseDbBean)rbAcctClientRelations.get(i));
               BusinessParamContext.getInstance();
               BusinessParamContext.put(RbDict.rbAcctClientRelation.toString(), rbAcctClientRelations);
               BusinessParamContext.getInstance();
               BusinessParamContext.put(RbDict.optUprightTableFlag.toString(), "I");
            }

            MqUtil.sendRooterMsg((RbAcctClientRelation)rbAcctClientRelations.get(i));
         }
      }

   }

   public int getMaxSeqNoByBaseAcctNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      HashMap<String, Object> ret = (HashMap)this.daoSupport.selectObject(RbAcctClientRelation.class.getName() + ".getMaxSeqNoByBaseAcctNo", param);
      if (BusiUtil.isNotNull(ret)) {
         if (ret.get("MAX_SEQ_NO") instanceof Double) {
            Double seqNo = (Double)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }

         if (ret.get("MAX_SEQ_NO") instanceof BigDecimal) {
            BigDecimal seqNo = (BigDecimal)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }
      }

      return 0;
   }

   public RbAcctClientRelation isExist(RbAcctClientRelation rbAcctClientRelation) {
      RbAcctClientRelation rbAcctClientRelation1 = new RbAcctClientRelation();
      rbAcctClientRelation1.setActualAcctNo(rbAcctClientRelation.getActualAcctNo());
      rbAcctClientRelation1.setCardNo(rbAcctClientRelation.getCardNo());
      rbAcctClientRelation1.setBaseAcctNo(rbAcctClientRelation.getBaseAcctNo());
      rbAcctClientRelation1.setProdType(rbAcctClientRelation.getProdType());
      rbAcctClientRelation1.setAcctCcy(rbAcctClientRelation.getAcctCcy());
      rbAcctClientRelation1.setAcctSeqNo(rbAcctClientRelation.getAcctSeqNo());
      RbAcctClientRelation rbAcctClientRelationReturn = this.selectOne(rbAcctClientRelation1);
      return rbAcctClientRelationReturn;
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertRbAcctClientRelation(RbAcctClientRelation rbAcctClientRelation) {
      this.daoSupport.insert(rbAcctClientRelation);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void deleteRbAcctClientRelation(RbAcctClientRelation rbAcctClientRelation) {
      this.daoSupport.delete(rbAcctClientRelation);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateRbAcctClientRelation(RbAcctClientRelation rbAcctClientRelationW, RbAcctClientRelation rbAcctClientRelationS) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", rbAcctClientRelationW.getInternalKey());
      param.put("cardNo", rbAcctClientRelationW.getCardNo());
      param.put("newCardNo", rbAcctClientRelationS.getCardNo());
      param.put("actualAcctNo", rbAcctClientRelationS.getActualAcctNo());
      param.put("acctStatus", rbAcctClientRelationS.getAcctStatus());
      param.put("prodType", rbAcctClientRelationS.getProdType());
      param.put("company", rbAcctClientRelationS.getCompany());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateCardNoAndActual", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateRbAcctClientRelation(RbAcctClientRelation rbAcctClientRelation) {
      this.daoSupport.update(rbAcctClientRelation);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateRbAcctClientRelationListByInternalKey(List<RbAcctClientRelation> rbAcctClientRelations) {
      Iterator var2 = rbAcctClientRelations.iterator();

      while(var2.hasNext()) {
         RbAcctClientRelation rbAcctClientRelation = (RbAcctClientRelation)var2.next();
         HashMap param = new HashMap();
         param.put("acctStatus", rbAcctClientRelation.getAcctStatus());
         param.put("clientNo", rbAcctClientRelation.getClientNo());
         param.put("internalKey", rbAcctClientRelation.getInternalKey());
         param.put("actualAcctNo", rbAcctClientRelation.getActualAcctNo());
         this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateClassByInternalKeyAndActualCardNo", param);
      }

   }

   public String getClientNo(String baseAcctNo, Long internalKey, String cardNo, String company) {
      if (BusiUtil.isNullAll(new Object[]{baseAcctNo, internalKey, cardNo})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", baseAcctNo);
         param.put("cardNo", cardNo);
         param.put("internalKey", internalKey);
         param.put("company", company);
         List<RbAcctClientRelation> rbAcctClientRelations = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getClientNo", param);
         return BusiUtil.isNotNull(rbAcctClientRelations) ? ((RbAcctClientRelation)rbAcctClientRelations.get(0)).getClientNo() : null;
      }
   }

   public RbAcctClientRelation getClientNoAndBaseAcctNo(RbAcctClientRelation rbAcctClientRelation) {
      if (BusiUtil.isNullAll(new Object[]{rbAcctClientRelation.getBaseAcctNo(), rbAcctClientRelation.getInternalKey(), rbAcctClientRelation.getCardNo()})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         Map<String, Object> param = new HashMap(16);
         param.put("baseAcctNo", rbAcctClientRelation.getBaseAcctNo());
         param.put("acctSeqNo", rbAcctClientRelation.getAcctSeqNo());
         param.put("acctCcy", rbAcctClientRelation.getAcctCcy());
         param.put("prodType", rbAcctClientRelation.getProdType());
         List<RbAcctClientRelation> rbAcctClientRelations = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getClientNoAndBaseAcctNo", param);
         return BusiUtil.isNotNull(rbAcctClientRelations) ? (RbAcctClientRelation)rbAcctClientRelations.get(0) : null;
      }
   }

   public RbAcctClientRelation findAll(RbAcctClientRelation rbAcctClientRelation) {
      if (BusiUtil.isNullAll(new Object[]{rbAcctClientRelation.getBaseAcctNo(), rbAcctClientRelation.getClientNo(), rbAcctClientRelation.getInternalKey(), rbAcctClientRelation.getCardNo()})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         return (RbAcctClientRelation)this.daoSupport.selectOne(rbAcctClientRelation);
      }
   }

   public RbAcctClientRelation selectOne(RbAcctClientRelation rbAcctClientRelation) {
      if (BusiUtil.isNullAll(new Object[]{rbAcctClientRelation.getBaseAcctNo(), rbAcctClientRelation.getClientNo(), rbAcctClientRelation.getInternalKey(), rbAcctClientRelation.getCardNo()})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         return (RbAcctClientRelation)this.daoSupport.selectOne(rbAcctClientRelation);
      }
   }

   public RbAcctClientRelation getLeadAcctStandard(String cardBaseAcctNo) {
      RbAcctClientRelation rbAcctClientRelation = new RbAcctClientRelation();
      rbAcctClientRelation.setLeadAcctFlag("Y");
      rbAcctClientRelation.setBaseAcctNo(cardBaseAcctNo);
      return (RbAcctClientRelation)this.daoSupport.selectOne(rbAcctClientRelation);
   }

   public void updateFmAcctClientRelation(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      if (BusiUtil.isNullAll(new Object[]{internalKey, clientNo})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         param.put("internalKey", internalKey);
         param.put("clientNo", clientNo);
         param.put("company", company);
         this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateFmAcctClientRelation", param);
      }
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateFmAcctClientRelationAll(Long internalkey, RbAcctClientRelation rbAcctClientRelation) {
      if (BusiUtil.isNullAll(new Object[]{internalkey})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         Map<String, Object> param = new HashMap(16);
         param.put("internalKey", internalkey);
         param.put("clientNo", rbAcctClientRelation.getClientNo());
         param.put("acctSeqNo", rbAcctClientRelation.getAcctSeqNo());
         param.put("parentInternalKey", rbAcctClientRelation.getParentInternalKey());
         param.put("baseAcctNo", rbAcctClientRelation.getBaseAcctNo());
         param.put("cardNo", rbAcctClientRelation.getCardNo());
         param.put("actualAcctNo", rbAcctClientRelation.getActualAcctNo());
         param.put("leadAcctFlag", rbAcctClientRelation.getLeadAcctFlag());
         param.put("acctStatus", rbAcctClientRelation.getAcctStatus());
         param.put("isCard", rbAcctClientRelation.getIsCard());
         param.put("prodType", rbAcctClientRelation.getProdType());
         param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
         param.put("company", rbAcctClientRelation.getCompany());
         this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateFmAcctClientRelationAll", param);
      }
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateRbAcctRelationName(RbAcctClientRelation rbAcctClientRelation) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", rbAcctClientRelation.getClientNo());
      param.put("acctName", rbAcctClientRelation.getAcctName());
      param.put("documentType", rbAcctClientRelation.getDocumentType());
      param.put("documentId", rbAcctClientRelation.getDocumentId());
      param.put("clientType", rbAcctClientRelation.getClientType());
      param.put("acctNature", rbAcctClientRelation.getAcctNature());
      param.put("baseAcctNo", rbAcctClientRelation.getBaseAcctNo());
      param.put("acctSeqNo", rbAcctClientRelation.getAcctSeqNo());
      param.put("company", rbAcctClientRelation.getCompany());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateRbAcctRelationName", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateFmAcctClientRelationAll1(RbAcctClientRelation rbAcctClientRelationW, RbAcctClientRelation rbAcctClientRelationS) {
      TwoTuple<RbAcctClientRelation, RbAcctClientRelation> rbAcctClientRelations = new TwoTuple(rbAcctClientRelationW, rbAcctClientRelationS);
      BusinessParamContext.getInstance();
      BusinessParamContext.put(RbDict.rbAcctClientRelationMap.toString(), rbAcctClientRelations);
      BusinessParamContext.getInstance();
      BusinessParamContext.put(RbDict.optUprightTableFlag.toString(), "U");
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", rbAcctClientRelationW.getInternalKey());
      param.put("cardNo", rbAcctClientRelationW.getCardNo());
      param.put("newCardNo", rbAcctClientRelationS.getCardNo());
      param.put("actualAcctNo", rbAcctClientRelationS.getActualAcctNo());
      param.put("acctStatus", rbAcctClientRelationS.getAcctStatus());
      param.put("prodType", rbAcctClientRelationS.getProdType());
      param.put("company", rbAcctClientRelationS.getCompany());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateCardNoAndActual", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateFmAcctClientRelationAll2(RbAcctClientRelation rbAcctClientRelationW, RbAcctClientRelation rbAcctClientRelationS) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", rbAcctClientRelationW.getInternalKey());
      param.put("cardNo", rbAcctClientRelationW.getCardNo());
      param.put("newCardNo", rbAcctClientRelationS.getCardNo());
      param.put("actualAcctNo", rbAcctClientRelationS.getActualAcctNo());
      param.put("acctStatus", rbAcctClientRelationS.getAcctStatus());
      param.put("prodType", rbAcctClientRelationS.getProdType());
      param.put("company", rbAcctClientRelationS.getCompany());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateCardNoAndActual", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void deleteByCardNo(RbAcctClientRelation rbAcctClientRelation) {
      List<RbAcctClientRelation> rbAcctClientRelations = this.settleCardGetClientNo(rbAcctClientRelation);
      BusinessParamContext.getInstance();
      BusinessParamContext.put(RbDict.rbAcctClientRelation.toString(), rbAcctClientRelations);
      BusinessParamContext.getInstance();
      BusinessParamContext.put(RbDict.optUprightTableFlag.toString(), "D");
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", rbAcctClientRelation.getClientNo());
      param.put("cardNo", rbAcctClientRelation.getCardNo());
      param.put("company", rbAcctClientRelation.getCompany());
      this.daoSupport.delete(RbAcctClientRelation.class.getName() + ".deleteByCardNo", param);
   }

   public List<RbAcctClientRelation> settleCardGetClientNo(RbAcctClientRelation rbAcctClientRelation) {
      Map<String, Object> param = new HashMap(16);
      if (BusiUtil.isNullAll(new Object[]{rbAcctClientRelation.getCardNo()})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         param.put("cardNo", rbAcctClientRelation.getCardNo());
         param.put("company", rbAcctClientRelation.getCompany());
         return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".settleCardGetClientNo", param);
      }
   }

   public List<RbAcctClientRelation> getClientRelationByClientNoAndC(String clientNo) {
      Map<String, Object> param = new HashMap(16);
      if (BusiUtil.isNullAll(new Object[]{clientNo})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         param.put("clientNo", clientNo);
         return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getClientRelationByClientNoAndC", param);
      }
   }

   public RbAcctClientRelation settleCardGetBaseAcctNo(RbAcctClientRelation rbAcctClientRelation) {
      Map<String, Object> param = new HashMap(16);
      if (BusiUtil.isNullAll(new Object[]{rbAcctClientRelation.getCardNo()})) {
         throw BusiUtil.createBusinessException("CD3018");
      } else {
         param.put("cardNo", rbAcctClientRelation.getCardNo());
         param.put("company", rbAcctClientRelation.getCompany());
         return (RbAcctClientRelation)this.daoSupport.selectOne(RbAcctClientRelation.class.getName() + ".settleCardGetBaseAcctNo", param);
      }
   }

   public List<RbAcctClientRelation> selectListAcct(RbAcctClientRelation rbAcctClientRelation) {
      return this.daoSupport.selectList(rbAcctClientRelation);
   }

   public List<RbAcctClientRelation> selectListAcctAll(RbAcctClientRelation rbAcctClientRelation, String cardFlag) {
      Map<String, Object> param = new HashMap(16);
      if (BusiUtil.isEquals("Y", cardFlag)) {
         param.put("cardNo", rbAcctClientRelation.getCardNo());
      } else {
         param.put("baseAcctNo", rbAcctClientRelation.getBaseAcctNo());
      }

      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".selectListAcctAll", param);
   }

   public List<RbAcctClientRelation> selectAcctInfoByPage(String baseAcctNo, String cardNo, String outFlag, List<String> branchList) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("cardNo", cardNo);
      param.put("outFlag", outFlag);
      param.put("branchList", branchList);
      String statementPostfix = RbAcctClientRelation.class.getName() + ".selectAcctInfoByPage";
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctClientRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctClientRelation> selectListAcctStatusFlagByPage(String baseAcctNo, String clientNo, String acctStatus, String acctClass, String statusFlag, List<String> notInProdTypes, String acctCcy, List<String> branchList) {
      HashMap<String, Object> param = new HashMap(5);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctStatus", acctStatus);
      param.put("acctClass", acctClass);
      param.put("statusFlag", statusFlag);
      param.put("notInProdTypes", notInProdTypes);
      param.put("acctCcy", acctCcy);
      param.put("branchList", branchList);
      String statementPostfix = RbAcctClientRelation.class.getName() + ".selectListAcctStatusFlagByPage";
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctClientRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctClientRelation> selectListAcctStatusFlagByPage2(String baseAcctNo, String clientNo, String acctStatus, String acctClass, String statusFlag, List<String> notInProdTypes, String acctCcy, String branchId, List<String> branchList) {
      HashMap<String, Object> param = new HashMap(5);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctStatus", acctStatus);
      param.put("acctClass", acctClass);
      param.put("statusFlag", statusFlag);
      param.put("notInProdTypes", notInProdTypes);
      param.put("acctCcy", acctCcy);
      param.put("branchId", branchId);
      param.put("branchList", branchList);
      String statementPostfix = RbAcctClientRelation.class.getName() + ".selectListAcctStatusFlagByPage2";
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctClientRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctClientRelation> selectListAcctStatusFlagByPage(String baseAcctNo, String clientNo, String acctStatus, String acctClass, String statusFlag, List<String> notInProdTypes, String acctCcy, String acctType, List<String> branchList) {
      HashMap<String, Object> param = new HashMap(5);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctStatus", acctStatus);
      param.put("acctClass", acctClass);
      param.put("statusFlag", statusFlag);
      param.put("notInProdTypes", notInProdTypes);
      param.put("acctCcy", acctCcy);
      param.put("acctType", acctType);
      param.put("branchList", branchList);
      String statementPostfix = RbAcctClientRelation.class.getName() + ".selectListAcctStatusFlagAcctTypeByPage";
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctClientRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctClientRelation> selectListAcctStatusFlagByPage2(String baseAcctNo, String clientNo, String acctStatus, String acctClass, String statusFlag, List<String> notInProdTypes, String acctCcy, String acctType, String branchId, List<String> branchList) {
      HashMap<String, Object> param = new HashMap(5);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctStatus", acctStatus);
      param.put("acctClass", acctClass);
      param.put("statusFlag", statusFlag);
      param.put("notInProdTypes", notInProdTypes);
      param.put("acctCcy", acctCcy);
      param.put("acctType", acctType);
      param.put("branchId", branchId);
      param.put("branchList", branchList);
      String statementPostfix = RbAcctClientRelation.class.getName() + ".selectListAcctStatusFlagAcctTypeByPage2";
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctClientRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctClientRelation> selectListRbAcct(Long internalKey, String clientNo) {
      RbAcctClientRelation rbAcctClientRelation = new RbAcctClientRelation();
      rbAcctClientRelation.setInternalKey(internalKey);
      rbAcctClientRelation.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcctClientRelation);
   }

   public List<RbAcctClientRelation> selectBy4Keys(Map<String, Object> param) {
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".selectBy4Keys", param);
   }

   public List<RbAcctClientRelation> selectBy4KeysLimitLeadAcctFlag(Map<String, Object> param) {
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".selectBy4KeysLimitLeadAcctFlag", param);
   }

   public List<RbAcctClientRelation> selectBy4KeysLimitdefault(Map<String, Object> param) {
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".selectBy4KeysLimitdefault", param);
   }

   public List<RbAcctClientRelation> selectBy4KeysLimit1000(Map<String, Object> param) {
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".selectBy4KeysLimit1000", param);
   }

   public List<RbAcctClientRelation> selectBy3Keys(Map<String, Object> param) {
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".selectBy3Keys", param);
   }

   public Integer getRelationRowCount(Map<String, Object> param) {
      int rowCount = this.daoSupport.count(RbAcctClientRelation.class.getName() + ".getRelationRowCount", param);
      log.debug("FmAcctClientRelationRepository.getRelationRowCount rowCount - {}", rowCount);
      return rowCount;
   }

   public List<RbAcctClientRelation> getAllProdTypes(String prodType) {
      Map<String, Object> param = new HashMap(4);
      MultiCorpUtil.prepareCompanyQueryCondition(param, true);
      param.put("prodType", prodType);
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getAllProdTypes", param);
   }

   public RbAcctClientRelation selectDefaultSettleAcctByActualAcctNo(String actualAcctNo) {
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("actualAcctNo", actualAcctNo);
      RbAcctClientRelation fm = (RbAcctClientRelation)this.daoSupport.selectOne(RbAcctClientRelation.class.getName() + ".selectDefaultSettleAcctByActualAcctNo", paramMap);
      return fm;
   }

   public void updateClientNoByInternalKey(String oldClientNo, String newClientNo, String internalKey, String documentId, String documentType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("newClientNo", newClientNo);
      param.put("oldClientNo", oldClientNo);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("lastChangeDate", Context.getInstance().getRunDateParse());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateClientNoByInternalKey", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateFmAcctClientRelationDocumentMsg(String clientNo, String documentId, String documentType, String clientName) {
      Map<String, Object> param = new HashMap();
      param.put("acctName", clientName);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateFmAcctClientRelationDocumentMsg", param);
   }

   public void updateBySixKeys(RbAcctClientRelation rbAcctClientRelation) {
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateBySixKeys", rbAcctClientRelation);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void tranInsertRbAcctClientRelation(RbBaseAcct rbBaseAcct, CifBaseInfo cifBaseInfo) {
      String sharedId = "";
      RbAcctClientRelation rbAcctClientRelation = new RbAcctClientRelation();
      rbAcctClientRelation.setClientNo(rbBaseAcct.getClientNo());
      rbAcctClientRelation.setBaseAcctNo(rbBaseAcct.getBaseAcctNo());
      rbAcctClientRelation.setInternalKey(rbBaseAcct.getInternalKey());
      rbAcctClientRelation.setAcctBranch(rbBaseAcct.getAcctBranch());
      rbAcctClientRelation.setShardId(sharedId);
      rbAcctClientRelation.setCompany(rbBaseAcct.getCompany());
      rbAcctClientRelation.setProdType(rbBaseAcct.getProdType());
      rbAcctClientRelation.setAcctCcy(rbBaseAcct.getAcctCcy());
      rbAcctClientRelation.setAcctSeqNo(rbBaseAcct.getAcctSeqNo());
      rbAcctClientRelation.setLeadAcctFlag("Y");
      rbAcctClientRelation.setClientType(rbBaseAcct.getClientType());
      rbAcctClientRelation.setSourceType(rbBaseAcct.getSourceType());
      rbAcctClientRelation.setAcctStatus(rbBaseAcct.getAcctStatus());
      rbAcctClientRelation.setAcctName(rbBaseAcct.getAcctName());
      rbAcctClientRelation.setAcctNature("11001");
      if (BusiUtil.isNotNull(rbBaseAcct.getCardNo())) {
         rbAcctClientRelation.setCardNo(rbBaseAcct.getCardNo());
         rbAcctClientRelation.setIsCard("Y");
      } else {
         rbAcctClientRelation.setIsCard("N");
      }

      rbAcctClientRelation.setAppFlag("N");
      if (BusiUtil.isNotNull(cifBaseInfo)) {
         rbAcctClientRelation.setIndividualFlag(cifBaseInfo.getIsIndividual());
         rbAcctClientRelation.setDocumentId(cifBaseInfo.getDocumentId());
         rbAcctClientRelation.setDocumentType(cifBaseInfo.getDocumentType());
      }

      rbAcctClientRelation.setActualAcctNo((String)BusiUtil.nvl(rbBaseAcct.getCardNo(), rbBaseAcct.getBaseAcctNo()));
      this.insert(rbAcctClientRelation);
   }

   public RbAcctClientRelation getFmAcctClientRelationByBaseAcctNoOrCardNo(String baseAcctNo) {
      RbAcctClientRelation rbAcctClientRelation = BusiContextUtil.getRbAcctClientRelation(baseAcctNo);
      if (BusiUtil.isNull(rbAcctClientRelation)) {
         Map<String, Object> param = new HashMap(2);
         param.put("cardNo", baseAcctNo);
         param.put("baseAcctNo", baseAcctNo);
         List<RbAcctClientRelation> list = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getFmAcctClientRelationByBaseAcctNoOrCardNo", param);
         if (BusiUtil.isNotNull(list) && list.size() != 0) {
            BusiContextUtil.putRbAcctClientRelation((RbAcctClientRelation)list.get(0), baseAcctNo);
            return (RbAcctClientRelation)list.get(0);
         } else {
            throw BusiUtil.createBusinessException("RB0712", new String[]{baseAcctNo});
         }
      } else {
         return rbAcctClientRelation;
      }
   }

   public RbAcctClientRelation getFmAcctClientRelationByBaseAcctNoOrCardNo1(String baseAcctNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("cardNo", baseAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      List<RbAcctClientRelation> list = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getFmAcctClientRelationByBaseAcctNoOrCardNo1", param);
      if (BusiUtil.isNotNull(list) && list.size() != 0) {
         return (RbAcctClientRelation)list.get(0);
      } else {
         throw BusiUtil.createBusinessException("RB0712", new String[]{baseAcctNo});
      }
   }

   public List<String> getFmAcctClientRelationByBaseAcctNoOrCardNo2(String baseAcctNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("cardNo", baseAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      List<String> list = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getFmAcctClientRelationByBaseAcctNoOrCardNo2", param);
      if (BusiUtil.isNotNull(list) && list.size() != 0) {
         return list;
      } else {
         throw BusiUtil.createBusinessException("RB0712", new String[]{baseAcctNo});
      }
   }

   public RbAcctClientRelation selectByActualAcctNo(String actualAcctNo, String ccy) {
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("actualAcctNo", actualAcctNo);
      List<RbAcctClientRelation> relations = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".selectDefaultSettleAcctByActualAcctNo", paramMap);
      if (BusiUtil.isNotNull(relations) && relations.size() > 0) {
         if (BusiUtil.isNull(ccy) && relations.size() > 1) {
            RbAcctClientRelation relation = new RbAcctClientRelation();
            Iterator var9 = relations.iterator();

            while(var9.hasNext()) {
               RbAcctClientRelation relation1 = (RbAcctClientRelation)var9.next();
               if (BusiUtil.isEquals(FmUtil.getFmSystem().getLocalCcy(), relation1.getAcctCcy())) {
                  relation = relation1;
                  break;
               }
            }

            if (BusiUtil.isNull(relation)) {
               throw BusiUtil.createBusinessException("RB7529");
            }

            return relation;
         }

         if (relations.size() == 1) {
            return (RbAcctClientRelation)relations.get(0);
         }

         Iterator var5 = relations.iterator();

         while(var5.hasNext()) {
            RbAcctClientRelation relation = (RbAcctClientRelation)var5.next();
            if (BusiUtil.isEquals(ccy, relation.getAcctCcy())) {
               return relation;
            }
         }
      }

      return null;
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateProdByFourKeys(RbAcctClientRelation rbAcctClientRelation, String newProdType) {
      Map<String, Object> param = new HashMap(16);
      param.put("prodType", rbAcctClientRelation.getProdType());
      param.put("baseAcctNo", rbAcctClientRelation.getBaseAcctNo());
      param.put("acctSeqNo", rbAcctClientRelation.getAcctSeqNo());
      param.put("acctCcy", rbAcctClientRelation.getAcctCcy());
      param.put("clientNo", rbAcctClientRelation.getClientNo());
      param.put("newProdType", newProdType);
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateByFourKeys", param);
   }

   public List<RbAcctClientRelation> getListByAcctStatusByPage(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String acctType, String statusFlag, String leadAcctFlag) {
      return this.getListByAcctStatusByPage(baseAcctNo, acctSeqNo, acctCcy, prodType, acctType, statusFlag, (List)null, leadAcctFlag);
   }

   public List<RbAcctClientRelation> getListByAcctStatusByPage(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String acctType, String statusFlag, List<String> notInProdTypes, String leadAcctFlag) {
      return this.getListByAcctStatusByPage(baseAcctNo, acctSeqNo, acctCcy, prodType, acctType, statusFlag, (String)null, (String)null, notInProdTypes, leadAcctFlag);
   }

   public List<RbAcctClientRelation> getListByAcctStatusByPage(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String acctType, String statusFlag, String term, String termType, List<String> notInProdTypes, String leadAcctFlag) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("acctType", acctType);
      param.put("statusFlag", statusFlag);
      param.put("notInProdTypes", notInProdTypes);
      param.put("term", term);
      param.put("termType", termType);
      param.put("leadAcctFlag", leadAcctFlag);
      RowArgs rowArgs = AppHeadUtil.getRowArgs();
      String statementPostfix = RbAcctClientRelation.class.getName() + ".getListByAcctStatusByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctClientRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctClientRelation> getListByInternalKeyByPage(List<Long> internalKeys) {
      Map<String, Object> param = new HashMap(1);
      param.put("internalKeys", internalKeys);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAcctClientRelation.class.getName() + ".getListByInternalKeyByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctClientRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateClassByInternalKey(String acctClass, String ClientNo, Long internalKey, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("ClientNo", ClientNo);
      param.put("acctClass", acctClass);
      param.put("acctSeqNo", acctSeqNo);
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateClassByInternalKey", param);
   }

   public RbAcctClientRelation getClientRelationByBaseAcctNoOrCardNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("cardNo", baseAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      List<RbAcctClientRelation> list = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getClientRelationByBaseAcctNoOrCardNo", param);
      if (BusiUtil.isNotNull(list) && list.size() != 0) {
         return (RbAcctClientRelation)list.get(0);
      } else {
         throw BusiUtil.createBusinessException("RB0712", new String[]{baseAcctNo});
      }
   }

   public List<RbAcctClientRelation> getClientRelationByCardNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("cardNo", baseAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getClientRelationByCardNo", param);
   }

   public List<RbAcctClientRelation> getUseClientRelationByClientNo(String clientNo) {
      List<RbAcctClientRelation> rbAcctClientRelations = null;
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("clientNo", clientNo);
      rbAcctClientRelations = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getUseClientRelationByClientNo", paramMap);
      return rbAcctClientRelations;
   }

   public List<RbAcctClientRelation> getClientRelationByBaseAcctNoAndClientNo(String baseAcctNo, String clientNo) {
      List<RbAcctClientRelation> rbAcctClientRelations = null;
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("baseAcctNo", baseAcctNo);
      paramMap.put("clientNo", clientNo);
      rbAcctClientRelations = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getClientRelationByBaseAcctNoAndClientNo", paramMap);
      return rbAcctClientRelations;
   }

   public List<RbAcctClientRelation> getClientRelationByClientNo(String clientNo) {
      List<RbAcctClientRelation> rbAcctClientRelations = null;
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("clientNo", clientNo);
      rbAcctClientRelations = this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getClientRelationByClientNo", paramMap);
      return rbAcctClientRelations;
   }

   public void updateClientNo(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateClientNo", map);
   }

   public List<RbAcctClientRelation> queryListByInternalKey(List<Long> internalKeys) {
      Map<String, Object> param = new HashMap(2);
      param.put("internalKeys", internalKeys);
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".queryListByInternalKey", param);
   }

   public List<RbAcctClientRelation> getDefaultSettleFlag(String baseAcctNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcctClientRelation.class.getName() + ".getDefaultSettleFlag", param);
   }

   public void updateActualAcctNoByInternalKey(RbAcctClientRelation rbAcctClientRelation) {
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateActualAcctNoByInternalKey", rbAcctClientRelation);
   }

   public int getMaxSeqNoByClientNo(String baseAcctNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      HashMap<String, Object> ret = (HashMap)this.daoSupport.selectObject(RbAcctClientRelation.class.getName() + ".getMaxSeqNo", param);
      if (BusiUtil.isNotNull(ret)) {
         if (ret.get("MAX_SEQ_NO") instanceof Double) {
            Double seqNo = (Double)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }

         if (ret.get("MAX_SEQ_NO") instanceof BigDecimal) {
            BigDecimal seqNo = (BigDecimal)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }
      }

      return 0;
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateRbAcctClientRelationSingle(RbAcctClientRelation rbAcctClientRelation) {
      HashMap param = new HashMap();
      param.put("acctStatus", rbAcctClientRelation.getAcctStatus());
      param.put("clientNo", rbAcctClientRelation.getClientNo());
      param.put("internalKey", rbAcctClientRelation.getInternalKey());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateRbAcctClientRelationSingle", param);
   }

   public void updateRelationStatus(RbAcctClientRelation rbAcctClientRelation) {
      HashMap param = new HashMap();
      param.put("acctStatus", rbAcctClientRelation.getAcctStatus());
      param.put("clientNo", rbAcctClientRelation.getClientNo());
      param.put("internalKey", rbAcctClientRelation.getInternalKey());
      this.daoSupport.update(RbAcctClientRelation.class.getName() + ".updateRbAcctClientRelationSingle", param);
   }
}
