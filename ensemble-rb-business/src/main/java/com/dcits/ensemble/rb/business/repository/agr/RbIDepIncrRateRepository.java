package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrRate;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbIDepIncrRateRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbIDepIncrRateRepository.class);

   public void createMbIDepIncrRateDbList(List<RbIdepIncrRate> mbIDepIncrRates) {
      if (BusiUtil.isNotNull(mbIDepIncrRates)) {
         Iterator var2 = mbIDepIncrRates.iterator();

         while(var2.hasNext()) {
            RbIdepIncrRate mbIDepIncrRate = (RbIdepIncrRate)var2.next();
            super.insert(mbIDepIncrRate);
         }
      }

   }

   public void updateByPrimaryKey(RbIdepIncrRate mbIDepIncrRate) {
      super.update(mbIDepIncrRate);
   }

   public List<RbIdepIncrRate> getAccordInfo(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return this.daoSupport.selectList(RbIdepIncrRate.class.getName() + ".selectByPrimaryKey", param);
   }

   public List<RbIdepIncrRate> getAccordInfoByFreq(Map<String, Object> param) {
      return this.daoSupport.selectList(RbIdepIncrRate.class.getName() + ".getAccordInfoByFreq", param);
   }

   public List<RbIdepIncrRate> getIncrRatesByDayNum(Map<String, Object> param) {
      return this.daoSupport.selectList(RbIdepIncrRate.class.getName() + ".getIncrRatesByDayNum", param);
   }

   public List<RbIdepIncrRate> getIncrRatesByFreq(String agreementId, String periodFreq) {
      Map<String, Object> cyclePeriodMap = new HashMap();
      cyclePeriodMap.put("agreementId", agreementId);
      cyclePeriodMap.put("periodFreq", periodFreq);
      return this.daoSupport.selectList(RbIdepIncrRate.class.getName() + ".getAccordInfoByFreq", cyclePeriodMap);
   }
}
