package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDebtAsynRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbAcctDebtAsynRegisterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctDebtAsynRegisterRepository.class);

   public RbAcctIntDetail getMbAcctIntDetail(Long internalKey, String intClass, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setIntClass(intClass);
      rbAcctIntDetail.setClientNo(clientNo);
      return (RbAcctIntDetail)this.daoSupport.selectOne(rbAcctIntDetail);
   }

   public List<RbAcctDebtAsynRegister> selectListByReference(String reference) {
      RbAcctDebtAsynRegister rbAcctDebtAsynRegister = new RbAcctDebtAsynRegister();
      rbAcctDebtAsynRegister.setReference(reference);
      return this.selectList(rbAcctDebtAsynRegister);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailAll(Long internalKey, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcctIntDetail);
   }

   public RbAcctIntDetail getMbAcctIntDetail(Long internalKey, String clientNo) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      return (RbAcctIntDetail)this.daoSupport.selectOne(rbAcctIntDetail);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailAll(RbAcctIntDetail rbAcctIntDetail) {
      return this.daoSupport.selectList(rbAcctIntDetail);
   }

   public void updateByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      super.update(rbAcctIntDetail);
   }

   public void updateIntDetailByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntDetailByPrimaryKey", rbAcctIntDetail);
   }

   public void updateDacByPrimaryKey(RbAcctIntDetail rbAcctIntDetail) {
      String runDate = Context.getInstance().getRunDate();
      rbAcctIntDetail.setLastChangeDate(BusiUtil.getDate(runDate));
      super.update(rbAcctIntDetail);
   }

   public Map<String, RbAcctIntDetail> getMbAcctIntDetailMap(Long internalKey, String clientNo) {
      Map<String, RbAcctIntDetail> intDetailMap = null;
      RbAcctIntDetail acctIntDetail = new RbAcctIntDetail();
      acctIntDetail.setInternalKey(internalKey);
      acctIntDetail.setClientNo(clientNo);
      List<RbAcctIntDetail> rbAcctIntDetails = this.daoSupport.selectList(acctIntDetail);
      if (BusiUtil.isNotNull(rbAcctIntDetails)) {
         intDetailMap = new HashMap();
         Iterator var6 = rbAcctIntDetails.iterator();

         while(var6.hasNext()) {
            RbAcctIntDetail rbAcctIntDetail = (RbAcctIntDetail)var6.next();
            intDetailMap.put(rbAcctIntDetail.getIntClass(), rbAcctIntDetail);
         }
      }

      return intDetailMap;
   }

   public void updMbAcctIntDtlBatch(List<RbAcctIntDetail> intDetails) {
      super.updateAddBatch(intDetails);
   }

   public void createMbAcctIntDb(List<RbAcctIntDetail> mbAcctInts) {
      super.insertAddBatch(mbAcctInts);
   }

   public RbAcctIntDetail getIntDetailByPrimaryKey(Map<String, Object> map, String company) {
      map.put("company", company);
      return (RbAcctIntDetail)this.daoSupport.selectOne(RbAcctIntDetail.class.getName() + ".selectIntDetailForUpdate", map);
   }

   public List<RbAcctIntDetail> selectRbIntSummary(Long internalKey, String clientNo, String company) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      map.put("clientNo", clientNo);
      map.put("company", company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".selectRbIntSummary", map);
   }

   public void updatePrevDetailForEod(Map<String, Object> map, String company) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", company);
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updatePrevDetailForEod", map);
   }

   public void updateDriPrevDetailForEod(Map<String, Object> map) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateDriPrevDetailForEod", map);
   }

   public void updateAccruedPrevDetailForEod(Map<String, Object> map) {
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAccruedPrevDetailForEod", map);
   }

   public void updateIntLayerAgreement(Map<String, Object> map, String company) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", company);
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntLayerAgreement", map);
   }

   public List<RbAcctIntDetail> getRbIntDetailByActive(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getRbIntDetailByActive", param);
   }

   public List<RbAcctIntDetail> getRbAcctIntDetailInt(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getRbAcctIntDetailInt", param);
   }

   public void updateAcctRate(Long internalKey, String clientNo, String intClass, BigDecimal acctSpreadRate, BigDecimal acctPercentRate, BigDecimal realRate, String company) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      map.put("clientNo", clientNo);
      map.put("intClass", intClass);
      map.put("acctSpreadRate", acctSpreadRate);
      map.put("acctPercentRate", acctPercentRate);
      map.put("realRate", realRate);
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("conmany", company);
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAcctRate", map);
   }

   public void updateAggIntAdj(RbAcctIntDetail rbAcctIntDetail) {
      rbAcctIntDetail.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctIntDetail.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAggIntAdj", rbAcctIntDetail);
   }

   public void updateIntDetailByRenew(RbAcctIntDetail rbAcctIntDetail) {
      rbAcctIntDetail.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctIntDetail.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntDetailByRenew", rbAcctIntDetail);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateIntDetailByDrawAmt(RbAcctIntDetail rbAcctIntDetail) {
      rbAcctIntDetail.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctIntDetail.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateIntDetailByRenew", rbAcctIntDetail);
   }

   public void updateAdjPrevDetailForEod(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAdjPrevDetailForEod", map);
   }

   public void updateAdvPrevDetailForEod(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctIntDetail.class.getName() + ".updateAdvPrevDetailForEod", map);
   }

   public List<RbAcctIntDetail> getMbAcctIntDetailAllForLock(Long internalKey, String clientNo, String company) {
      RbAcctIntDetail rbAcctIntDetail = new RbAcctIntDetail();
      rbAcctIntDetail.setInternalKey(internalKey);
      rbAcctIntDetail.setClientNo(clientNo);
      rbAcctIntDetail.setCompany(company);
      return this.daoSupport.selectList(RbAcctIntDetail.class.getName() + ".getMbAcctIntDetailAllForLock", rbAcctIntDetail);
   }
}
