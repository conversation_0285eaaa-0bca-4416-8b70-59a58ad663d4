package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCheckResult;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbProdCheckResultRepository extends BusinessRepository<RbProdCheckResult> {
   public void insertProdCheckResultList(List<RbProdCheckResult> rbProdCheckResults) {
      super.insertAddBatch(rbProdCheckResults);
   }

   public void deleteProdCheckResult(RbProdCheckResult prodCheckResult) {
      super.delete(prodCheckResult);
   }

   public List<RbProdCheckResult> selectProdCheckResult(RbProdCheckResult rbProdCheckResult) {
      return super.selectList(rbProdCheckResult);
   }

   public List<RbProdCheckResult> selectProdCheckResult(String startDate, String endDate, String branch, String ccy, String prodType, String amtType, String checkResult, String company) throws ParseException {
      Map<String, Object> param = new HashMap();
      if (BusiUtil.isNotNull(startDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
      }

      if (BusiUtil.isNotNull(startDate)) {
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      param.put("branch", branch);
      param.put("ccy", ccy);
      param.put("prodType", prodType);
      param.put("amtType", amtType);
      param.put("checkResult", checkResult);
      param.put("company", company);
      return this.daoSupport.selectList(RbProdCheckResult.class.getName() + ".selectCheckResultByPage", param);
   }

   public List<RbProdCheckResult> getProdResultByBranch(Map<String, Object> param) {
      return this.daoSupport.selectList(RbProdCheckResult.class.getName() + ".getProdResultByBranch", param);
   }
}
