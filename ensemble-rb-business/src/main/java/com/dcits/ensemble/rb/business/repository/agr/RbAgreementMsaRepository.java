package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementMsa;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementMsaRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementMsaRepository.class);

   public void createMbAgreementMsaDb(RbAgreementMsa agreementMsa) {
      if (BusiUtil.isNotNull(agreementMsa)) {
         super.insert(agreementMsa);
      }

   }

   public void updateAgreementMsa(RbAgreementMsa agreementMsa) {
      super.update(agreementMsa);
   }

   public RbAgreementMsa selectByCondition(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String status) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementStatus", status);
      return (RbAgreementMsa)this.daoSupport.selectOne(RbAgreementMsa.class.getName() + ".selectByCondition", param);
   }
}
