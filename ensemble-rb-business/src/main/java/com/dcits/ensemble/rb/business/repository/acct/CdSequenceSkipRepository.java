package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.rb.business.entity.dbmodel.CdSeqSkip;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public class CdSequenceSkipRepository extends BusinessRepository {
   public List<CdSeqSkip> getCdSequenceSkip(String cardBin, String company, String seqNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("cardBin", cardBin);
      param.put("company", company);
      param.put("seqNo", seqNo);
      return this.daoSupport.selectList(CdSeqSkip.class.getName() + ".getCdSequenceSkip", param);
   }
}
