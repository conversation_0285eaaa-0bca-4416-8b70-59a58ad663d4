package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementNorthbound;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementNorthboundRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementNorthboundRepository.class);

   public void createRbAgreementNorthBound(RbAgreementNorthbound rbAgreementNorthbound) {
      if (BusiUtil.isNotNull(rbAgreementNorthbound)) {
         super.insert(rbAgreementNorthbound);
      }

   }

   public RbAgreementNorthbound getEntityByClientAndBaseAcctNo(String baseAcctNo, String client) {
      RbAgreementNorthbound rbAgreementNorthbound1 = new RbAgreementNorthbound();
      rbAgreementNorthbound1.setBaseAcctNo(baseAcctNo);
      rbAgreementNorthbound1.setClientNo(client);
      rbAgreementNorthbound1.setNorthboundStatus("1");
      return (RbAgreementNorthbound)this.daoSupport.selectOne(rbAgreementNorthbound1);
   }

   public RbAgreementNorthbound getIsNorthboundSign(String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      return (RbAgreementNorthbound)this.daoSupport.selectOne(RbAgreementNorthbound.class.getName() + ".getIsNorthboundSign", param);
   }

   public RbAgreementNorthbound getIsNorthboundSignS(String baseAcctNo, String cardNo, String seqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("cardNo", cardNo);
      param.put("seqNo", seqNo);
      return (RbAgreementNorthbound)this.daoSupport.selectOne(RbAgreementNorthbound.class.getName() + ".getIsNorthboundSignS", param);
   }

   public RbAgreementNorthbound getRbAgreementNorthbound(String agreementId) {
      RbAgreementNorthbound rbAgreementNorthbound1 = new RbAgreementNorthbound();
      rbAgreementNorthbound1.setAgreementId(agreementId);
      return (RbAgreementNorthbound)this.daoSupport.selectOne(rbAgreementNorthbound1);
   }
}
