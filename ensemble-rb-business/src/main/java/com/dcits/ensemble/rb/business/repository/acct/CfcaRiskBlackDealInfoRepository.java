package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.CfcaRiskBlackDealInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import com.dcits.ensemble.util.DateUtil;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class CfcaRiskBlackDealInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(CfcaRiskBlackDealInfoRepository.class);

   public CfcaRiskBlackDealInfo selectOneCfcaRiskBlackDealInfo(CfcaRiskBlackDealInfo cfcaRiskBlackDealInfo) {
      Map<String, Object> param = new HashMap(16);
      param.put("dataTyp", cfcaRiskBlackDealInfo.getDataTyp());
      param.put("vchrNum", cfcaRiskBlackDealInfo.getVchrNum());
      param.put("blklTyp", cfcaRiskBlackDealInfo.getBlklTyp());
      return (CfcaRiskBlackDealInfo)this.daoSupport.selectOne(CfcaRiskBlackDealInfo.class.getName() + ".selectOneCfcaRiskBlackDealInfo", param);
   }

   public void updateCfcaRiskBlackDealInfo(CfcaRiskBlackDealInfo cfcaRiskBlackDealInfo) {
      Context context = Context.getInstance();
      cfcaRiskBlackDealInfo.setTranTimestamp(BusiUtil.getTranTimestamp26());

      try {
         cfcaRiskBlackDealInfo.setTranDate(DateUtil.parseDate(context.getRunDate()));
      } catch (Exception var4) {
         log.error(Arrays.toString(var4.getStackTrace()));
      }

      this.daoSupport.update(CfcaRiskBlackDealInfo.class.getName() + ".updateCfcaRiskBlackDealInfo", cfcaRiskBlackDealInfo);
   }

   public void dealCfcaRiskBlackDealInfoNoToday(Date runDate) {
      Map<String, Object> param = new HashMap();
      param.put("tranDate", runDate);
      this.daoSupport.update(CfcaRiskBlackDealInfo.class.getName() + ".dealCfcaRiskBlackDealInfoNoToday", param);
   }
}
