package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementAgencyModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementAgencyRepository extends BusinessRepository {
   public MbAgreementAgencyModel getAgencyAgrtByAgreementId(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      MbAgreementAgencyModel model = null;
      if (null == model) {
         throw BusiUtil.createBusinessException("RB4046");
      } else {
         return (MbAgreementAgencyModel)model;
      }
   }
}
