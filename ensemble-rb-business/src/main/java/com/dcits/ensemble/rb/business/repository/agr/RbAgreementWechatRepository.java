package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWechat;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementWechatRepository extends BusinessRepository {
   public RbAgreementWechat selectByAgreementId(String agreementId, String clientNo) {
      RbAgreementWechat rbAgreementWechat = new RbAgreementWechat();
      rbAgreementWechat.setAgreementId(agreementId);
      rbAgreementWechat.setClientNo(clientNo);
      return (RbAgreementWechat)this.daoSupport.selectOne(rbAgreementWechat);
   }

   public void deleteByAgreementId(String agreementId, String clientNo) {
      RbAgreementWechat rbAgreementWechat = new RbAgreementWechat();
      rbAgreementWechat.setAgreementId(agreementId);
      rbAgreementWechat.setClientNo(clientNo);
      this.daoSupport.delete(rbAgreementWechat);
   }

   public List<RbAgreementWechat> selectByClientNo(String clientNo) {
      RbAgreementWechat rbAgreementWechat = new RbAgreementWechat();
      rbAgreementWechat.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAgreementWechat);
   }

   public void updateByAgreementId(String agreementId, String clientNo, RbAgreementWechat rbAgreementWechat) {
      RbAgreementWechat rbAgreementWechat1 = new RbAgreementWechat();
      rbAgreementWechat1.setAgreementId(agreementId);
      rbAgreementWechat1.setClientNo(clientNo);
      super.delete(rbAgreementWechat1);
      super.insert(rbAgreementWechat);
   }

   public List<RbAgreementWechat> selectCosWechatSignInfo(Date tranDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("tranTimestamp", tranDate);
      return BusiUtil.isNull(tranDate) ? null : this.daoSupport.selectList(RbAgreementWechat.class.getName() + ".selectCosWechatSignInfo", param);
   }
}
