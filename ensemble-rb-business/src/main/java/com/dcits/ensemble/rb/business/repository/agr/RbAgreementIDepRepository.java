package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.model.dict.BaseDict;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementIdep;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementIDepModel;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAgreementIDepRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementIDepRepository.class);

   public RbAgreementIdep getIDAgreementDetail(String agreementId) {
      RbAgreementIdep rbAgreementIDep = new RbAgreementIdep();
      rbAgreementIDep.setAgreementId(agreementId);
      return (RbAgreementIdep)this.daoSupport.selectOne(rbAgreementIDep);
   }

   public void updateMbAgreementIDep(RbAgreementIdep rbAgreementIDep) {
      super.update(rbAgreementIDep);
   }

   public MbAgreementIDepModel dbConvert2Model(RbAgreementIdep rbAgreementIDep) {
      MbAgreementIDepModel mbAgreementIDepModel = new MbAgreementIDepModel();
      Context context = Context.getInstance();
      mbAgreementIDepModel.setAcctGroupId(rbAgreementIDep.getIdepGroupId());
      mbAgreementIDepModel.setIdepProdType(rbAgreementIDep.getIdepProdType());
      mbAgreementIDepModel.setCycleFreq(rbAgreementIDep.getCycleFreq());
      mbAgreementIDepModel.setSecondFlag(rbAgreementIDep.getSecondFlag());
      mbAgreementIDepModel.setSecondCycleFreq(rbAgreementIDep.getSecondCycleFreq());
      mbAgreementIDepModel.setSecondNextCycleDate(rbAgreementIDep.getSecondNextCycleDate());
      mbAgreementIDepModel.setEffectDate(rbAgreementIDep.getEffectDate());
      mbAgreementIDepModel.setLastCycleDate(rbAgreementIDep.getLastCycleDate());
      mbAgreementIDepModel.setNextCycleDate(rbAgreementIDep.getNextCycleDate());
      mbAgreementIDepModel.setRemainType(rbAgreementIDep.getRemainType());
      mbAgreementIDepModel.setRemainAmt(rbAgreementIDep.getRemainAmt());
      mbAgreementIDepModel.setUserId(context.getUserId());
      mbAgreementIDepModel.setBranch(context.getBranchId());
      mbAgreementIDepModel.setAutoSign(rbAgreementIDep.getAutoSign());
      mbAgreementIDepModel.setSchedMode(rbAgreementIDep.getSchedMode());
      mbAgreementIDepModel.setMaturityCycleType(rbAgreementIDep.getMaturityCycleType());
      mbAgreementIDepModel.setAgreementId(rbAgreementIDep.getAgreementId());
      mbAgreementIDepModel.setIntSettleType(rbAgreementIDep.getIntSettleType());
      mbAgreementIDepModel.setIntDay(rbAgreementIDep.getIntDay());
      mbAgreementIDepModel.setSecondIntDay(rbAgreementIDep.getSecondIntDay());
      mbAgreementIDepModel.setIntCountWay(rbAgreementIDep.getIntCountWay());
      mbAgreementIDepModel.setIdepSubType(rbAgreementIDep.getIdepSubType());
      return mbAgreementIDepModel;
   }

   public List<RbAgreementIdep> fuzzyQueryByAgreementId(List<String> agreementIdList) {
      List<RbAgreementIdep> rbAgreementIDeps = new ArrayList();
      RbAgreementIdep rbAgreementIDep = new RbAgreementIdep();
      Iterator var4 = agreementIdList.iterator();

      while(var4.hasNext()) {
         String agreementId = (String)var4.next();
         rbAgreementIDep.setAgreementId(agreementId);
         RbAgreementIdep rbAgreementIDep1 = (RbAgreementIdep)this.daoSupport.selectOne(rbAgreementIDep);
         rbAgreementIDeps.add(rbAgreementIDep1);
      }

      return rbAgreementIDeps;
   }

   public RbAgreementIdep getMbAgreementIDep(String baseAcctNo, String acctSeqNo, String ccy, String prodType, String clientNo) {
      Map<String, Object> param = new HashMap(1);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.acctSeqNo.toString(), acctSeqNo);
      param.put(BaseDict.acctCcy.toString(), ccy);
      param.put(BaseDict.prodType.toString(), prodType);
      param.put(BaseDict.clientNo.toString(), clientNo);
      return (RbAgreementIdep)this.daoSupport.selectOne(RbAgreementIdep.class.getName() + ".selectMbAgreementByfive", param);
   }
}
