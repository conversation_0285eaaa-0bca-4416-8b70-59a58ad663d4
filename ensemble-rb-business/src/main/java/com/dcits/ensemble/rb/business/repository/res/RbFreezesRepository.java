package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbFreeze;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public class RbFreezesRepository extends BusinessRepository {
   public List<RbFreeze> selectByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(1);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList("selectByInternalKey", param);
   }

   public List<RbFreeze> selectActiveResByInternalKey(Long internalKey, String restraintType, String seqNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("restraintType", restraintType);
      param.put("resSeqNo", seqNo);
      return this.daoSupport.selectList("selectByPrimaryKey", param);
   }

   public RbFreeze getMbFreeze(String resSeqNo, String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("resSeqNo", resSeqNo);
      map.put("clientNo", clientNo);
      return (RbFreeze)this.daoSupport.selectOne("getMbFreeze", map);
   }
}
