package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctPayment;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctPaymentRepository extends BusinessRepository {
   public int insert(RbAcctPayment rbAcctPayment) {
      return super.insert(rbAcctPayment);
   }

   public void update(RbAcctPayment rbAcctPayment) {
      super.update(rbAcctPayment);
   }

   public void delete(RbAcctPayment rbAcctPayment) {
      super.delete(rbAcctPayment);
   }

   public List<RbAcctPayment> selectRbAcctPaymentInfo(String clientNo, Long internalKey, String baseAcctNo, String channel, String blacklistIndFlag) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      param.put("baseAcctNo", baseAcctNo);
      param.put("channel", channel);
      param.put("blacklistIndFlag", blacklistIndFlag);
      return this.daoSupport.selectList(RbAcctPayment.class.getName() + ".selectRbAcctPaymentInfo", param);
   }

   public RbAcctPayment selectRbAcctPayment(Long internalKey, Long otherInternalKey, String channel, String clientNo) {
      RbAcctPayment rbAcctPayment = new RbAcctPayment();
      rbAcctPayment.setInternalKey(internalKey);
      rbAcctPayment.setClientNo(clientNo);
      rbAcctPayment.setOthInternalKey(otherInternalKey);
      rbAcctPayment.setChannel(channel);
      return (RbAcctPayment)this.daoSupport.selectOne(rbAcctPayment);
   }
}
