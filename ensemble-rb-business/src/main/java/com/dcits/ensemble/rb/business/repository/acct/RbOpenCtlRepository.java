package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCtl;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
public class RbOpenCtlRepository extends BusinessRepository {
   public List<RbOpenCtl> getMbOpenCtlListByClientType(String clientType) {
      Map<String, Object> param = new HashMap();
      param.put("clientType", clientType);
      return this.daoSupport.selectList(RbOpenCtl.class.getName() + ".selectByClientType", param);
   }

   public List<RbOpenCtl> getRbOenCtrlList(RbOpenCtl rbOpenCtl) {
      return this.daoSupport.selectList(RbOpenCtl.class.getName() + ".selectList", rbOpenCtl);
   }
}
