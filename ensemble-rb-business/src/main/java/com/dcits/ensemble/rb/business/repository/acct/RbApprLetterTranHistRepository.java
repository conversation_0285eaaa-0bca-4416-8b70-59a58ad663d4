package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterTranHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbApprLetterTranHistRepository extends BusinessRepository {
   public void updateByPrimaryKey(RbApprLetterTranHist rbapprLetterTranHist) {
      super.update(rbapprLetterTranHist);
   }

   public void insert(RbApprLetterTranHist rbapprLetterTranHist) {
      rbapprLetterTranHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.schedNo, new String[]{Context.getInstance().getRunDate()}));
      super.insert(rbapprLetterTranHist);
   }
}
