package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.model.entity.dbmodel.EgTranHistSource;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class EgTranHistResourceRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(EgTranHistResourceRepository.class);

   public void insertBranchToEg(List<EgTranHistSource> list) {
      if (BusiUtil.isNotNull(list)) {
         this.daoSupport.insertAddBatch(list);
      }

   }
}
