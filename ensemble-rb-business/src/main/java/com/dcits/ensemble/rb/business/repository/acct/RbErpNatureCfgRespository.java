package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbErpNatureCfg;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbErpNatureCfgRespository extends BusinessRepository {
   public RbErpNatureCfg getMbErpNatureCfgInfo(String inlandOffshore, String corporation, String acctNature, String acctOperateType, String reasonCode, String natureProperty) {
      Map<String, Object> map = new HashMap(16);
      map.put("reasonCode", reasonCode);
      map.put("inlandOffshore", inlandOffshore);
      map.put("corporation", corporation);
      map.put("acctNature", acctNature);
      map.put("acctOperateType", acctOperateType);
      map.put("natureProperty", natureProperty);
      List<RbErpNatureCfg> result = this.daoSupport.selectList(RbErpNatureCfg.class.getName() + ".getMbErpNatureCfgInfo", map);
      return BusiUtil.isNull(result) ? null : (RbErpNatureCfg)result.get(0);
   }

   public List<RbErpNatureCfg> selectForAddRes(String inlandOffshore, String corporation, String acctNature, String acctOperateType, String reasonCode, String natureProperty) {
      Map<String, Object> map = new HashMap(16);
      map.put("acctNature", acctNature);
      map.put("acctOperateType", acctOperateType);
      List<RbErpNatureCfg> result = this.daoSupport.selectList(RbErpNatureCfg.class.getName() + ".selectForAddRes", map);
      return result;
   }

   public List<RbErpNatureCfg> getMbErpNatureCfgInfo(String acctNature, String restranintType) {
      RbErpNatureCfg rbErpNatureCfg = new RbErpNatureCfg();
      rbErpNatureCfg.setAcctNature(acctNature);
      rbErpNatureCfg.setAcctOperateType("O");
      rbErpNatureCfg.setRestraintType(restranintType);
      return this.daoSupport.selectList(rbErpNatureCfg);
   }

   public List<RbErpNatureCfg> getMbErpNatureCfgInfoS(String acctNature, String restranintType) {
      RbErpNatureCfg rbErpNatureCfg = new RbErpNatureCfg();
      rbErpNatureCfg.setAcctNature(acctNature);
      rbErpNatureCfg.setRestraintType(restranintType);
      return this.daoSupport.selectList(rbErpNatureCfg);
   }

   public List<RbErpNatureCfg> getMbErpNatureCfgbyReasonCodeInfo(String acctNature) {
      Map<String, Object> map = new HashMap(16);
      map.put("acctNature", acctNature);
      List<RbErpNatureCfg> result = this.daoSupport.selectList(RbErpNatureCfg.class.getName() + ".getMbErpNatureCfgbyReasonCodeInfo", map);
      return result;
   }
}
