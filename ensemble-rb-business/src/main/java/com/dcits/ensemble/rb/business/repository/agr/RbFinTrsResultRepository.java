package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFinTrsResult;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbFinTrsResultRepository extends BusinessRepository {
   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void addRbFinTrsResultDb(RbFinTrsResult rbFinTrsResult) {
      super.insert(rbFinTrsResult);
   }
}
