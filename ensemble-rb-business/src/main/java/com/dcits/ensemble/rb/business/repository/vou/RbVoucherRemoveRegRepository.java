package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherRemoveReg;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbVoucherRemoveRegRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherRemoveRegRepository.class);

   public List<RbVoucherRemoveReg> selectByCondtion(RbVoucherRemoveReg rbVoucherRemoveReg) {
      return this.daoSupport.selectList(RbVoucherRemoveReg.class.getName() + ".selectByCondtion", rbVoucherRemoveReg);
   }

   public void updateRemoveFlag(RbVoucherRemoveReg rbVoucherRemoveReg) {
      this.daoSupport.update(RbVoucherRemoveReg.class.getName() + ".updateRemoveFlag", rbVoucherRemoveReg);
   }
}
