package com.dcits.ensemble.rb.business.repository.channel;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdRecAcct;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RbCmCdRecAcctRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbCmCdRecAcctRepository.class);

   public RbCmCdRecAcct selectByPrimaryKey(String subAcctNo, String baseAcctNo, String recBaseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("recBaseAcctNo", recBaseAcctNo);
      return (RbCmCdRecAcct)this.daoSupport.selectOne(RbCmCdRecAcct.class.getName() + ".selectByPrimaryKey", param);
   }

   public List<RbCmCdRecAcct> selectListBySubAcctNo(String subAcctNo) {
      Map<String, Object> param = new HashMap();
      param.put("subAcctNo", subAcctNo);
      return this.daoSupport.selectList(RbCmCdRecAcct.class.getName() + ".selectListBySubAcctNo", param);
   }

   public void deleteBySubAcctNoAndBaseAcctNo(String subAcctNo, String baseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      this.daoSupport.selectOne(RbCmCdRecAcct.class.getName() + ".deleteBySubAcctNoAndBaseAcctNo", param);
   }

   public void createRbCmCdRecAcct(RbCmCdRecAcct rbCmCdRecAcct) {
      super.insert(rbCmCdRecAcct);
   }
}
