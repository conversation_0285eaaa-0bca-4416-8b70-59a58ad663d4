package com.dcits.ensemble.rb.business.repository.res.control;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.model.FmLangTranslation;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class FmLangTranslationRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(FmLangTranslationRepository.class);

   public int insertFmLangTranslation(FmLangTranslation translation) {
      return this.daoSupport.insert(translation);
   }

   public void updateFmLangTranslation(FmLangTranslation translation) {
      this.daoSupport.update(translation);
   }
}
