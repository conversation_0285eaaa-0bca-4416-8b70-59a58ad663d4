package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalanceTemp;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAcctProdBalanceTempRepository extends BusinessRepository<RbAcctProdBalanceTemp> {
   private static final Logger log = LoggerFactory.getLogger(RbAcctProdBalanceTempRepository.class);

   public void insertRbAcctProdBalanceList(List<RbAcctProdBalanceTemp> rbAcctProdBalances) {
      super.insertAddBatch(rbAcctProdBalances);
   }

   public void updateRbAcctProdBalanceList(List<RbAcctProdBalanceTemp> rbAcctProdBalances) {
      super.updateAddBatch(rbAcctProdBalances);
   }

   public void deleteAll() {
      RbAcctProdBalanceTemp rbAcctProdBalance = new RbAcctProdBalanceTemp();
      rbAcctProdBalance.setCompany(Context.getInstance().getCompany());
      this.daoSupport.delete(RbAcctProdBalanceTemp.class.getName() + ".delete", new RbAcctProdBalanceTemp());
   }

   public List<RbAcctProdBalance> selectAllBalanceTempInfo(Date lastRunDate, String company) {
      Map<String, Object> param = new HashMap();
      param.put("lastRunDate", lastRunDate);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctProdBalance.class.getName() + ".selectAllBalanceTempInfo", param);
   }
}
