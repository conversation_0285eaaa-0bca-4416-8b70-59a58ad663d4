package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIntSplit;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbIntSplitRepository extends BusinessRepository {
   public List<RbIntSplit> getIntSplitList(Long internalKey, String intClass, String clientNo) {
      Map<String, Object> paramMap = new HashMap(16);
      paramMap.put("internalKey", internalKey);
      paramMap.put("intClass", intClass);
      paramMap.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getIntSplitList", paramMap);
   }

   public RbIntSplit getSplitInDate(RbIntSplit rbIntSplit, String company) {
      rbIntSplit.setCompany(company);
      return (RbIntSplit)this.daoSupport.selectOne(RbIntSplit.class.getName() + ".getSplitInDate", rbIntSplit);
   }

   public int deleteStartDate(Map<String, Object> map, String company) {
      map.put("company", company);
      return this.daoSupport.delete(RbIntSplit.class.getName() + ".deleteStartDate", map);
   }

   public void updateByInternalKey(RbIntSplit rbIntSplit) {
      rbIntSplit.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbIntSplit.class.getName() + ".updateByInternalKey", rbIntSplit);
   }

   public void updateIntSplitByInternalKey(RbIntSplit rbIntSplit) {
      this.daoSupport.update(RbIntSplit.class.getName() + ".updateIntSplitByInternalKey", rbIntSplit);
   }

   public RbIntSplit findSplitDate(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return (RbIntSplit)this.daoSupport.selectOne(RbIntSplit.class.getName() + ".findSplitDate", rbIntSplit);
   }

   public List<RbIntSplit> selectSplitBatchDate(Long startKey, Long endKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("START_KEY", startKey);
      param.put("END_KEY", endKey);
      param.put("company", company);
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".selectBatchDate", param);
   }

   public List<RbIntSplit> getSplitList(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getSplitList", rbIntSplit);
   }

   public List<RbIntSplit> getSplitListBefore(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getSplitListBefore", rbIntSplit);
   }

   public List<RbIntSplit> getHistList(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getHistList", rbIntSplit);
   }

   public RbIntSplit selectSplitInfo(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return (RbIntSplit)this.daoSupport.selectOne(RbIntSplit.class.getName() + ".selectSplitInfo", rbIntSplit);
   }

   public RbIntSplit selectLastSplitInfo(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return (RbIntSplit)this.daoSupport.selectOne(RbIntSplit.class.getName() + ".selectLastSplitInfo", rbIntSplit);
   }

   public List<RbIntSplit> findByConditionHistSplit(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".findByConditionHistSplit", rbIntSplit);
   }

   /** @deprecated */
   @Deprecated
   public List<RbIntSplit> getHistListByPage(RbIntSplit rbIntSplit) {
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getHistListByPage", rbIntSplit);
   }

   public RbIntSplit getSplitListEnd(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return (RbIntSplit)this.daoSupport.selectOne(RbIntSplit.class.getName() + ".getSplitListEnd", rbIntSplit);
   }

   public List<RbIntSplit> getSplitListByEndDate(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getSplitListByEndDate", rbIntSplit);
   }

   public int deleteAll(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.delete(RbIntSplit.class.getName() + ".deleteAll", rbIntSplit);
   }

   public List<RbIntSplit> selectSplitAndHist(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".selectSplitAndHist", rbIntSplit);
   }

   public boolean existSplitInfo(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      List list = this.daoSupport.selectList(RbIntSplit.class.getName() + ".existSplitInfo", rbIntSplit);
      return BusiUtil.isNotNull(list);
   }

   public List<RbIntSplit> selectSplitLists(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".selectSplitLists", rbIntSplit);
   }

   public RbIntSplit selectSplitByDate(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return (RbIntSplit)this.daoSupport.selectOne(RbIntSplit.class.getName() + ".selectSplitByDate", rbIntSplit);
   }

   public List<RbIntSplit> findByInternalKeyIntClass(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".findByInternalKeyIntClass", rbIntSplit);
   }

   public void updateForReverse(RbIntSplit rbIntSplit) {
      rbIntSplit.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbIntSplit.class.getName() + ".updateForReverse", rbIntSplit);
   }

   public void updateForAmt(RbIntSplit rbIntSplit) {
      rbIntSplit.setCompany(Context.getInstance().getCompany());
      rbIntSplit.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbIntSplit.class.getName() + ".updateForAmt", rbIntSplit);
   }

   public List<RbIntSplit> selectRbhqSplitBatchDate(Long startKey, Long endKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("START_KEY", startKey);
      param.put("END_KEY", endKey);
      param.put("company", company);
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".selectRbhqBatchDate", param);
   }

   public List<RbIntSplit> selectRbdqSplitBatchDate(Long startKey, Long endKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("START_KEY", startKey);
      param.put("END_KEY", endKey);
      param.put("company", company);
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".selectRbdqBatchDate", param);
   }

   public List<RbIntSplit> selectClSplitBatchDate(Long startKey, Long endKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("START_KEY", startKey);
      param.put("END_KEY", endKey);
      param.put("company", company);
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".selectClBatchDate", param);
   }

   public List<RbIntSplit> getSplitByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getSplitByInternalKey", param);
   }

   public List<RbIntSplit> getSplitByStarDate(String cycleRollDate, Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("cycleRollDate", cycleRollDate);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbIntSplit.class.getName() + ".getSplitByStarDate", param);
   }
}
