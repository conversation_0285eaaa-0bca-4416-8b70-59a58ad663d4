package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAutoTranInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAutoTranInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAutoTranInfoRepository.class);

   public void insertListRbAutoTranferInfo(List<RbAutoTranInfo> list) {
      super.insertAddBatch(list);
   }
}
