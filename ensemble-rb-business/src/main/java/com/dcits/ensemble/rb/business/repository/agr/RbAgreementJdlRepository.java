package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.model.dict.BaseDict;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementJdl;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementJdlRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementJdlRepository.class);

   public String insert(RbAgreementJdl rbAgreementjdl, Long internalKey) {
      String agreementId = SequenceGenerator.nextValue(SequenceEnum.agreementIdSeqNo, new String[]{Context.getInstance().getRunDate()}) + internalKey.toString();
      super.insert(rbAgreementjdl);
      return agreementId;
   }

   public void delAgreement(RbAgreementJdl rbAgreementjdl) {
      this.daoSupport.delete(rbAgreementjdl);
   }

   public void updAgreement(RbAgreementJdl rbAgreementjdl) {
      this.daoSupport.update(rbAgreementjdl);
   }

   public List<RbAgreementJdl> selectAgreementJdL(RbAgreementJdl rbAgreementjdl) {
      return this.daoSupport.selectList(rbAgreementjdl);
   }

   public List<RbAgreementJdl> getAgreementJdL(String baseAcctNo, Long internalKey, String ccy, String clientNo, String acctType, String status, String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.internalKey.toString(), internalKey);
      param.put(BaseDict.ccy.toString(), ccy);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put("acctType", acctType);
      param.put("status", status);
      param.put(BaseDict.agreementId.toString(), agreementId);
      return this.daoSupport.selectList(RbAgreementJdl.class.getName() + ".getAgreements", param);
   }

   public List<RbAgreementJdl> getAgreementById(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put(BaseDict.agreementId.toString(), agreementId);
      return this.daoSupport.selectList(RbAgreementJdl.class.getName() + ".getAgreementsByBranch", param);
   }

   public List<RbAgreementJdl> getActiveAgreementById(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put(BaseDict.agreementId.toString(), agreementId);
      return this.daoSupport.selectList(RbAgreementJdl.class.getName() + ".getActiveAgreementsByBranch", param);
   }

   public List<RbAgreementJdl> getAgreementsByBranch(String branch, String internalKey, String baseAcctNo, String ccy, String clientNo, String acctType, String status, String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put(BaseDict.branch.toString(), branch);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.internalKey.toString(), internalKey);
      param.put(BaseDict.ccy.toString(), ccy);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put("acctType", acctType);
      param.put("status", status);
      param.put(BaseDict.agreementId.toString(), agreementId);
      return this.daoSupport.selectList(RbAgreementJdl.class.getName() + ".getAgreementsByBranch", param);
   }

   public List<RbAgreementJdl> getAgreementsByBranch(String branch) {
      Map<String, Object> param = new HashMap();
      param.put(BaseDict.branch.toString(), branch);
      return this.daoSupport.selectList(RbAgreementJdl.class.getName() + ".getAgreementsByBranch", param);
   }

   public List<RbAgreementJdl> getAgreementsByInternalKey(String internalKey) {
      Map<String, Object> param = new HashMap();
      param.put(BaseDict.internalKey.toString(), internalKey);
      return this.daoSupport.selectList(RbAgreementJdl.class.getName() + ".getAgreementsByBranch", param);
   }

   public List<RbAgreementJdl> getActiveAgreementsByInternalKey(String internalKey) {
      Map<String, Object> param = new HashMap();
      param.put(BaseDict.internalKey.toString(), internalKey);
      return this.daoSupport.selectList(RbAgreementJdl.class.getName() + ".getActiveAgreementsByBranch", param);
   }
}
