package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailSplit;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctIntDetailSplitRepository extends BusinessRepository {
   public RbAcctIntDetailSplit getRbAcctIntDetailSplit(Long internalKey, String intClass, String clientNo) {
      RbAcctIntDetailSplit rbAcctIntDetailSplit = new RbAcctIntDetailSplit();
      rbAcctIntDetailSplit.setInternalKey(internalKey);
      rbAcctIntDetailSplit.setIntClass(intClass);
      rbAcctIntDetailSplit.setClientNo(clientNo);
      List<RbAcctIntDetailSplit> rbAcctIntDetailSplits = this.daoSupport.selectList(rbAcctIntDetailSplit);
      return BusiUtil.isNotNull(rbAcctIntDetailSplits) ? (RbAcctIntDetailSplit)rbAcctIntDetailSplits.get(0) : null;
   }

   public List<RbAcctIntDetailSplit> getRbAcctIntDetailSplitList(Long internalKey, String intClass, String clientNo) {
      RbAcctIntDetailSplit rbAcctIntDetailSplit = new RbAcctIntDetailSplit();
      rbAcctIntDetailSplit.setInternalKey(internalKey);
      rbAcctIntDetailSplit.setIntClass(intClass);
      rbAcctIntDetailSplit.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcctIntDetailSplit);
   }

   public List<RbAcctIntDetailSplit> getSplitList(RbAcctIntDetailSplit rbAcctIntDetailSplit) {
      rbAcctIntDetailSplit.setCompany(Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbAcctIntDetailSplit.class.getName() + ".getSplitList", rbAcctIntDetailSplit);
   }
}
