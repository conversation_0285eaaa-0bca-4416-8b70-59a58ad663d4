package com.dcits.ensemble.rb.business.repository.channel;

import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.bc.unit.channel.msg.business.MsgSetTypeEnum;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelMsg;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbChannelMsgRepository extends BusinessRepository<RbChannelMsg> {
   private static final Logger log = LoggerFactory.getLogger(RbChannelMsgRepository.class);

   public int updateByChannelMsgModel(RbChannelMsg rbChannelMsg) {
      new RbChannelMsg();
      Map<String, Object> map = new HashMap();
      map.put("clientNo", rbChannelMsg.getClientNo());
      map.put("msgSetType", rbChannelMsg.getMsgSetType());
      RbChannelMsg oldRbChannelMsg = (RbChannelMsg)this.daoSupport.selectOne(RbChannelMsg.class.getName() + ".selectOneByClientNo", map);
      if (BusiUtil.isNull(oldRbChannelMsg)) {
         rbChannelMsg.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.channelMsgSeqNo));
         return this.insert(rbChannelMsg);
      } else {
         rbChannelMsg.setSeqNo(oldRbChannelMsg.getSeqNo());
         return this.updateByPrimaryKey(rbChannelMsg);
      }
   }

   public List<RbChannelMsg> selectByClientNo(String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbChannelMsg.class.getName() + ".selectMsgListByClientNo", map);
   }

   public RbChannelMsg selectByClientNoAndType(String clientNo, MsgSetTypeEnum msgSetTypeEnum) {
      Map<String, Object> map = new HashMap();
      map.put("clientNo", clientNo);
      map.put("msgSetType", msgSetTypeEnum.getSetType());
      return (RbChannelMsg)this.daoSupport.selectOne(RbChannelMsg.class.getName() + ".selectByClientNoAndType", map);
   }
}
