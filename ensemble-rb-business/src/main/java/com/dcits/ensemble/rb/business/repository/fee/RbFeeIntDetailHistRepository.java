package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeIntDetailHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Repository;

@Repository
public class RbFeeIntDetailHistRepository extends BusinessRepository<RbFeeIntDetailHist> {
   public RbFeeIntDetailHist selectOneByFeeIntNo(String feeIntNo, String reference, String clientNo) {
      RbFeeIntDetailHist rbFeeIntDetailHist = new RbFeeIntDetailHist();
      rbFeeIntDetailHist.setFeeIntNo(feeIntNo);
      rbFeeIntDetailHist.setReference(reference);
      rbFeeIntDetailHist.setClientNo(clientNo);
      return (RbFeeIntDetailHist)this.selectOne(rbFeeIntDetailHist);
   }
}
