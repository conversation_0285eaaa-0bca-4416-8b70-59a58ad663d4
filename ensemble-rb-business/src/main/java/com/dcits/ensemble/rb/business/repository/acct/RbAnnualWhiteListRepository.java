package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAnnualWhiteList;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAnnualWhiteListRepository extends BusinessRepository {
   public List<RbAnnualWhiteList> selectByBranch(String acctBranch) {
      Map<String, Object> map = new HashMap();
      map.put("acctBranch", acctBranch);
      return this.daoSupport.selectList("selectByAcctBranch", map);
   }

   public RbAnnualWhiteList selectByInternalKey(Long internalKey) {
      RbAnnualWhiteList rbAnnualWhiteList = new RbAnnualWhiteList();
      rbAnnualWhiteList.setInternalKey(internalKey);
      return (RbAnnualWhiteList)this.daoSupport.selectOne(rbAnnualWhiteList);
   }

   public int createRbAnnualWhiteList(RbAnnualWhiteList rbAnnualWhiteList) {
      return super.insert(rbAnnualWhiteList);
   }

   public int update(RbAnnualWhiteList rbAnnualWhiteList) {
      return super.update(rbAnnualWhiteList);
   }

   public int delete(RbAnnualWhiteList rbAnnualWhiteList) {
      return this.daoSupport.delete(rbAnnualWhiteList);
   }
}
