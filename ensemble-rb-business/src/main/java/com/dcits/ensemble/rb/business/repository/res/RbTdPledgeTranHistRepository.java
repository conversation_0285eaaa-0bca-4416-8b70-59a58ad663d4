package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbTdPledgeTranHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbTdPledgeTranHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbTdPledgeTranHistRepository.class);

   public RbTdPledgeTranHist getRbTdPledgeTranHist(RbTdPledgeTranHist rbTdPledgeTranHist) {
      return (RbTdPledgeTranHist)this.daoSupport.selectOne(RbTdPledgeTranHist.class.getName() + "getRbTdPledgeTranHist", rbTdPledgeTranHist);
   }

   public int updateByUnqual(RbTdPledgeTranHist rbTdPledgeTranHist) {
      return this.daoSupport.update(RbTdPledgeTranHist.class.getName() + "updateByUnqual", rbTdPledgeTranHist);
   }
}
