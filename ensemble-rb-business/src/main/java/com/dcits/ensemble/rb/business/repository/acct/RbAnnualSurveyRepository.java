package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAnnualSurvey;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAnnualSurveyRepository extends BusinessRepository {
   public List<RbAnnualSurvey> getMbCleanParameterList() {
      return this.daoSupport.selectList(RbAnnualSurvey.class.getName() + ".queryAllAnnualServey", new RbAnnualSurvey());
   }
}
