package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.bc.component.vou.sequence.MbPnTranDetailSeq;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnPayment;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbPnPaymentRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPnPaymentRepository.class);

   public void mbPnPaymentInsert(RbPnPayment mbPnPayment) {
      if (BusiUtil.isNull(mbPnPayment.getSerialNo())) {
         MbPnTranDetailSeq seq = new MbPnTranDetailSeq();
         mbPnPayment.setSerialNo(seq.getMbPnTranDetailSeqId());
      }

      if (BusiUtil.isNotNull(mbPnPayment)) {
         super.insert(mbPnPayment);
      }

   }
}
