package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.bc.unit.acct.sequences.MbAcctUpDownInfoSeqNo;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctUpDownInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctUpDownInfoRepository extends BusinessRepository {
   public void createMbAcctUpDownInfoDb(RbAcctUpDownInfo rbAcctUpDownInfo) {
      new MbAcctUpDownInfoSeqNo();
      rbAcctUpDownInfo.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbAcctUpDownInfoSeqNo));
      super.insert(rbAcctUpDownInfo);
   }

   public List<RbAcctUpDownInfo> getMbAcctUpDownInfo(String baseAcctNo, String acctSeqNo, String ccy, String prodType) {
      RbAcctUpDownInfo rbAcctUpDownInfo = new RbAcctUpDownInfo();
      rbAcctUpDownInfo.setBaseAcctNo(baseAcctNo);
      rbAcctUpDownInfo.setAcctSeqNo(acctSeqNo);
      rbAcctUpDownInfo.setAcctCcy(ccy);
      rbAcctUpDownInfo.setProdType(prodType);
      return this.daoSupport.selectList(rbAcctUpDownInfo);
   }
}
