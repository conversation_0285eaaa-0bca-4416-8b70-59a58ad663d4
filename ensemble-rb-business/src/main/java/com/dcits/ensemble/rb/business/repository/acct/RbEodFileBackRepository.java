package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbEodFileBack;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbEodFileBackRepository extends BusinessRepository {
   public RbEodFileBack getEodFileBackByTranDate(Date tranDate, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("tranDate", tranDate);
      map.put("company", company);
      RbEodFileBack rbEodFileBack = null;
      rbEodFileBack = (RbEodFileBack)this.daoSupport.selectOne(RbEodFileBack.class.getName() + ".getFileBackByTranDate", map);
      return rbEodFileBack;
   }

   public void setEodFileBackByTranDate(Date tranDate, String newCount, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("tranDate", tranDate);
      map.put("newCount", newCount);
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", company);
      this.daoSupport.update(RbEodFileBack.class.getName() + ".setEodFileBackByTranDate", map);
   }

   public void setInpFileBackByTranDate(Date tranDate, String newCount, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("tranDate", tranDate);
      map.put("newCount", newCount);
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      map.put("company", company);
      this.daoSupport.update(RbEodFileBack.class.getName() + ".setInpFileBackByTranDate", map);
   }
}
