package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.bc.unit.acct.sequences.SequenceSeqNo;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbListChangeHist;
import com.dcits.ensemble.rb.business.entity.dbmodel.RcAllList;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@Repository
@BusiUnit
public class RbListChangeHisRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbListChangeHisRepository.class);

   public void createRbListChangeHistory(RbListChangeHist rbListChangeHistory) {
      if (BusiUtil.isNotNull(rbListChangeHistory)) {
         super.insert(rbListChangeHistory);
      }

   }

   public void updateHistory(RbListChangeHist rbListChangeHistory) {
      if (BusiUtil.isNotNull(rbListChangeHistory)) {
         super.update(rbListChangeHistory);
      }

   }

   public void deleteHistory(RbListChangeHist rbListChangeHistory) {
      if (BusiUtil.isNotNull(rbListChangeHistory)) {
         super.delete(rbListChangeHistory);
      }

   }

   public List<RbListChangeHist> selectHistoryList(RbListChangeHist rbListChangeHistory) {
      return BusiUtil.isNotNull(rbListChangeHistory) ? super.selectList(rbListChangeHistory) : null;
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertHist(RcAllList rcAllList, String status, String operate) {
      RbListChangeHist rbListChangeHistory = new RbListChangeHist();
      if (com.dcits.comet.commons.utils.BusiUtil.isNotNull(rcAllList)) {
         BeanUtil.copy(rcAllList, rbListChangeHistory);
         rbListChangeHistory.setStatus(status);
         rbListChangeHistory.setDataType(rcAllList.getDataType());
         rbListChangeHistory.setDataValue(rcAllList.getDataValue());
         rbListChangeHistory.setSeqNo((new SequenceSeqNo()).getNextVal());
         rbListChangeHistory.setClientNo(rcAllList.getClientNo());
         if (com.dcits.comet.commons.utils.BusiUtil.isEquals("U", operate)) {
            this.createRbListChangeHistory(rbListChangeHistory);
         }

         if (com.dcits.comet.commons.utils.BusiUtil.isEquals("N", operate)) {
            this.createRbListChangeHistory(rbListChangeHistory);
         }

         if (com.dcits.comet.commons.utils.BusiUtil.isEquals("D", operate)) {
            this.createRbListChangeHistory(rbListChangeHistory);
         }
      }

   }

   public boolean selectByData(String dataType, String dataValue, String listType) {
      boolean res = false;
      RbListChangeHist rbListChangeHist = new RbListChangeHist();
      rbListChangeHist.setDataType(dataType);
      rbListChangeHist.setDataValue(dataValue);
      rbListChangeHist.setListType(listType);
      rbListChangeHist.setStatus("A");
      RbListChangeHist rbListChangeHist1 = (RbListChangeHist)this.daoSupport.selectOne(rbListChangeHist);
      if (BusiUtil.isNotNull(rbListChangeHist1)) {
         res = true;
      }

      return res;
   }

   public List<RbListChangeHist> getRbListChangeHist(String startDate, String endDate, String clientNo, String baseAcctNo, String status, String listType) {
      Map<String, Object> param = new HashMap(5);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      param.put("status", status);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("listType", listType);
      return this.daoSupport.selectList(RbListChangeHist.class.getName() + ".selectRbListChangeHist", param);
   }

   public List<RbListChangeHist> getRbListChangeHist(String startDate, String endDate, String clientNo, String dataType, String dataValue, String status, String listType, String company) {
      Map<String, Object> param = new HashMap(8);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      param.put("status", status);
      param.put("clientNo", clientNo);
      param.put("dataType", dataType);
      param.put("dataValue", dataValue);
      param.put("listType", listType);
      param.put("company", company);
      return this.daoSupport.selectList(RbListChangeHist.class.getName() + ".selectRbListChangeHist", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void deleteRbListChangeHistDb(String dataType, String dataValue, String listType, String clientNo, String company) {
      RbListChangeHist ral = new RbListChangeHist();
      ral.setDataType(dataType);
      ral.setDataValue(dataValue);
      ral.setListType(listType);
      ral.setClientNo(clientNo);
      ral.setCompany(company);
      this.daoSupport.delete(RbListChangeHist.class.getName().concat(".deleteByDataTypeAndValue"), ral);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void createRbListChangeHistDb(RbListChangeHist RbListChangeHist) {
      super.insert(RbListChangeHist);
   }
}
