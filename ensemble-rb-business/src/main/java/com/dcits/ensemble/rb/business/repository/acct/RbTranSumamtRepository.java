package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranSumamt;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbTranSumamtRepository extends BusinessRepository {
   public void insertForEod(List<RbTranSumamt> rbTranSumamtList) {
      super.insertAddBatch(rbTranSumamtList);
   }

   public RbTranSumamt getSumAmtInfo(Date tranDate, String company) {
      Map<String, Object> map = new HashMap();
      map.put("tranDate", tranDate);
      map.put("company", company);
      return (RbTranSumamt)this.daoSupport.selectOne(RbTranSumamt.class.getName() + ".getSumAmtInfo", map);
   }
}
