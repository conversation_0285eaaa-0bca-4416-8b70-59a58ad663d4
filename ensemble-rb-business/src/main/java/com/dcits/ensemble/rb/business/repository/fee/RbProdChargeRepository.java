package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbProdCharge;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public class RbProdChargeRepository extends BusinessRepository {
   public List<RbProdCharge> selectNextChargeDateMature(String runDate) {
      Map<String, Object> param = new HashMap();
      param.put("runDate", DateUtil.parseDate(runDate, new String[]{"yyyyMMdd"}));
      return this.daoSupport.selectList(RbProdCharge.class.getName() + ".selectNextChargeDateMature", param);
   }

   public List<RbProdCharge> getProdChargeByProdType(String prodType) {
      Map<String, Object> param = new HashMap();
      param.put("prodType", prodType);
      return this.daoSupport.selectList("selectProdChargeByProdType", param);
   }

   public List<RbProdCharge> getProdChargeByProdAndFee(String prodType, String feeType) {
      Map<String, Object> param = new HashMap(2);
      List<String> feeTypeList = new ArrayList();
      feeTypeList.add(feeType);
      param.put("prodType", prodType);
      param.put("feeTypeList", feeTypeList);
      return this.daoSupport.selectList(RbProdCharge.class.getName() + ".getProdChargeByProdAndFee", param);
   }
}
