package com.dcits.ensemble.rb.business.repository.acct.schedule;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.dao.model.BasePo;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctScheduleHist;
import com.dcits.ensemble.rb.business.model.acct.schedule.MbAcctScheduleModel;
import com.dcits.ensemble.rb.business.repository.acct.RbAcctRepository;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.NonNull;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctScheduleRepository extends BusinessRepository {
   @Resource
   private RbAcctRepository mbAcctRepository;
   @Resource
   private RbAcctScheduleHistRepository mbAcctScheduleHistRepository;
   @Resource
   private RbAcctCompositeScheduleRepository mbAcctCompositeScheduleRepository;
   @Resource
   private RbAcctCompositeScheduleRepository rbAcctCompositeScheduleDao;

   public void addAcctSchedule(MbAcctScheduleModel mbAcctSchedule) {
      String baseAcctNo = mbAcctSchedule.getBaseAcctNo();
      String prodType = mbAcctSchedule.getProdType();
      String ccy = mbAcctSchedule.getCcy();
      String seqNo = mbAcctSchedule.getSeqNo();
      Long internalKey = mbAcctSchedule.getInternalKey();
      if (internalKey == null) {
         if (baseAcctNo == null) {
            throw BusiUtil.createBusinessException("RB2233");
         }

         RbAcct rbAcct;
         if (seqNo != null && prodType != null && ccy != null) {
            rbAcct = this.mbAcctRepository.getMbAcctInfo(baseAcctNo, prodType, ccy, seqNo);
         } else {
            rbAcct = this.mbAcctRepository.getMbLeadAcct(baseAcctNo);
         }

         if (BusiUtil.isNull(rbAcct)) {
            throw BusiUtil.createBusinessException("RB3105", new String[]{"账户明细", "rbAcct"});
         }

         internalKey = rbAcct.getInternalKey();
      }

      RbAcctSchedule mbSchedule = new RbAcctSchedule();
      BeanUtil.copy(mbAcctSchedule, mbSchedule);
      mbSchedule.setInternalKey(internalKey);
      mbSchedule.setSchedNo((String)SequenceGenerator.nextValue(SequenceEnum.schedNo, new String[]{Context.getInstance().getRunDate()}));
      super.insert(mbSchedule);
   }

   public RbAcctSchedule createMbAcctSchedule(@NonNull Long internalKey) {
      if (internalKey == null) {
         throw new NullPointerException("internalKey");
      } else {
         RbAcctSchedule rbAcctSchedule = new RbAcctSchedule();
         rbAcctSchedule.setSchedNo((String)SequenceGenerator.nextValue(SequenceEnum.schedNo, new String[]{Context.getInstance().getRunDate()}));
         rbAcctSchedule.setInternalKey(internalKey);
         return rbAcctSchedule;
      }
   }

   public void addAcctSchedule(List<MbAcctScheduleModel> mbAcctSchedules, Long internalKey) {
      if (BusiUtil.isNotNull(mbAcctSchedules)) {
         int i = 0;

         for(int len = mbAcctSchedules.size(); i < len; ++i) {
            MbAcctScheduleModel mbAcctSchedule = (MbAcctScheduleModel)mbAcctSchedules.get(i);
            mbAcctSchedule.setInternalKey(internalKey);
            this.addAcctSchedule(mbAcctSchedule);
         }
      }

   }

   public List<RbAcctSchedule> getSchedule(Long internalKey, String company, String clientNo) {
      RbAcctSchedule rbAcctSchedule = new RbAcctSchedule();
      rbAcctSchedule.setInternalKey(internalKey);
      rbAcctSchedule.setCompany(company);
      rbAcctSchedule.setClientNo(clientNo);
      return this.daoSupport.selectList(RbAcctSchedule.class.getName() + ".selectScheduleByInternalKey", rbAcctSchedule);
   }

   public RbAcctSchedule getSchedule(String schedNo, Long internalKey, String clientNo) {
      RbAcctSchedule rbAcctSchedule = new RbAcctSchedule();
      rbAcctSchedule.setInternalKey(internalKey);
      rbAcctSchedule.setClientNo(clientNo);
      rbAcctSchedule.setSchedNo(schedNo);
      return (RbAcctSchedule)this.daoSupport.selectOne(rbAcctSchedule);
   }

   public RbAcctSchedule getScheduleByEventType(Long internalKey, String eventType, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("eventType", eventType);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcctSchedule)this.daoSupport.selectOne(RbAcctSchedule.class.getName() + ".selectScheduleByEventType", param);
   }

   public List<RbAcctSchedule> getSchedulesByEventType(Long internalKey, String eventType, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("eventType", eventType);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSchedule.class.getName() + ".selectScheduleByEventType", param);
   }

   public List<RbAcctSchedule> getSchedulesBySchedMode(Long internalKey, String schedMode, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("schedMode", schedMode);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctSchedule.class.getName() + ".selectSchedulesBySchedMode", param);
   }

   public void updAcctSchedule(MbAcctScheduleModel mbAcctSchedule) {
      Long internalKey = mbAcctSchedule.getInternalKey();
      String baseAcctNo = mbAcctSchedule.getBaseAcctNo();
      String prodType = mbAcctSchedule.getProdType();
      String ccy = mbAcctSchedule.getCcy();
      String seqNo = mbAcctSchedule.getSeqNo();
      String scheNo = mbAcctSchedule.getSchedNo();
      if (internalKey == null) {
         if (baseAcctNo == null) {
            throw BusiUtil.createBusinessException("RB3018");
         }

         RbAcct rbAcct;
         if (seqNo != null && prodType != null && ccy != null) {
            rbAcct = this.mbAcctRepository.getMbAcctInfo(baseAcctNo, prodType, ccy, seqNo);
         } else {
            rbAcct = this.mbAcctRepository.getMbLeadAcct(baseAcctNo);
         }

         if (BusiUtil.isNull(rbAcct)) {
            throw BusiUtil.createBusinessException("RB3105", new String[]{BusiUtil.getMessageByKey("MG0231"), "rbAcct"});
         }

         internalKey = rbAcct.getInternalKey();
      }

      if (scheNo == null) {
         throw BusiUtil.createBusinessException("RB3040");
      } else {
         RbAcctSchedule mbSchedule = new RbAcctSchedule();
         BeanUtil.copy(mbAcctSchedule, mbSchedule);
         mbSchedule.setInternalKey(internalKey);
         super.update(mbSchedule);
      }
   }

   public void delAcctSchedule(MbAcctScheduleModel mbAcctScheduleModel) {
      String baseAcctNo = mbAcctScheduleModel.getBaseAcctNo();
      String ccy = mbAcctScheduleModel.getCcy();
      String prodType = mbAcctScheduleModel.getProdType();
      String seqNo = mbAcctScheduleModel.getSeqNo();
      Long internalKey = mbAcctScheduleModel.getInternalKey();
      String schedNo = mbAcctScheduleModel.getSchedNo();
      String clientNo = mbAcctScheduleModel.getClientNo();
      if (BusiUtil.isNull(schedNo)) {
         throw BusiUtil.createBusinessException("RB3040");
      } else {
         if (BusiUtil.isNull(internalKey)) {
            if (BusiUtil.isNull(baseAcctNo)) {
               throw BusiUtil.createBusinessException("RB3018");
            }

            RbAcct rbAcct;
            if (BusiUtil.isNotNull(ccy) && BusiUtil.isNotNull(prodType) && BusiUtil.isNotNull(seqNo)) {
               rbAcct = this.mbAcctRepository.getMbAcctInfo(baseAcctNo, prodType, ccy, seqNo);
            } else {
               rbAcct = this.mbAcctRepository.getMbLeadAcct(baseAcctNo);
            }

            if (BusiUtil.isNull(rbAcct)) {
               throw BusiUtil.createBusinessException("RB3105", new String[]{BusiUtil.getMessageByKey("MG0231"), "rbAcct"});
            }

            internalKey = rbAcct.getInternalKey();
         }

         RbAcctScheduleHist rbAcctScheduleHist = new RbAcctScheduleHist();
         RbAcctSchedule rbAcctScheduleold = this.getSchedule(schedNo, internalKey, clientNo);
         BeanUtil.copy(rbAcctScheduleold, rbAcctScheduleHist);
         rbAcctScheduleHist.setLastChangeDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
         super.insert(rbAcctScheduleHist);
         RbAcctSchedule rbAcctSchedule = new RbAcctSchedule();
         rbAcctSchedule.setInternalKey(internalKey);
         rbAcctSchedule.setSchedNo(schedNo);
         this.daoSupport.delete(rbAcctSchedule);
      }
   }

   public RbAcctSchedule createMbAcctSchedule(MbAcctScheduleModel mbAcctScheduleModel) {
      RbAcctSchedule mbSchedule = new RbAcctSchedule();
      BeanUtil.copy(mbAcctScheduleModel, mbSchedule);
      mbSchedule.setSchedNo((String)SequenceGenerator.nextValue(SequenceEnum.schedNo, new String[]{Context.getInstance().getRunDate()}));
      if (!"null".equals(mbAcctScheduleModel.getTotalTimes())) {
         mbSchedule.setTotalTimes(Integer.valueOf(mbAcctScheduleModel.getTotalTimes()));
      }

      super.insert(mbSchedule);
      return mbSchedule;
   }

   public void createMbAcctScheduleDb(RbAcctSchedule mbSchedule) {
      mbSchedule.setSchedNo((String)SequenceGenerator.nextValue(SequenceEnum.schedNo, new String[]{Context.getInstance().getRunDate()}));
      super.insert(mbSchedule);
   }

   public void createMbAcctScheduleDbList(List<RbAcctSchedule> rbAcctSchedules) {
      for(int i = 0; i < rbAcctSchedules.size(); ++i) {
         ((RbAcctSchedule)rbAcctSchedules.get(i)).setSchedNo((String)SequenceGenerator.nextValue(SequenceEnum.schedNo, new String[]{Context.getInstance().getRunDate()}));
         super.insert((EnsBaseDbBean)rbAcctSchedules.get(i));
      }

   }

   public void createFromHist(RbAcctSchedule rbAcctSchedules) {
      super.insert(rbAcctSchedules);
   }

   public RbAcctSchedule deleteMbAcctSchedule(MbAcctScheduleModel mbAcctScheduleModel) {
      Long internalKey = mbAcctScheduleModel.getInternalKey();
      String schedNo = mbAcctScheduleModel.getSchedNo();
      String clientNo = mbAcctScheduleModel.getClientNo();
      RbAcctSchedule rbAcctSchedule = new RbAcctSchedule();
      rbAcctSchedule.setInternalKey(internalKey);
      rbAcctSchedule.setSchedNo(schedNo);
      rbAcctSchedule.setClientNo(clientNo);
      this.daoSupport.delete(rbAcctSchedule);
      return rbAcctSchedule;
   }

   public void deleteMbAcctScheduleDb(RbAcctSchedule mbSchedule) {
      this.daoSupport.delete(mbSchedule);
   }

   public void deleteMbAcctScheduleDbList(List<RbAcctSchedule> rbAcctSchedules) {
      for(int i = 0; i < rbAcctSchedules.size(); ++i) {
         this.daoSupport.delete((BasePo)rbAcctSchedules.get(i));
      }

   }

   public RbAcctSchedule updateMbAcctSchedule(MbAcctScheduleModel mbAcctScheduleModel) {
      RbAcctSchedule rbAcctSchedule = new RbAcctSchedule();
      BeanUtil.copy(mbAcctScheduleModel, rbAcctSchedule);
      if (!"null".equals(mbAcctScheduleModel.getTotalTimes())) {
         rbAcctSchedule.setTotalTimes(Integer.valueOf(mbAcctScheduleModel.getTotalTimes()));
      }

      return rbAcctSchedule;
   }

   public void updateMbAcctScheduleDb(RbAcctSchedule mbSchedule) {
      super.update(mbSchedule);
   }

   public void updateMbAcctScheduleDbList(List<RbAcctSchedule> rbAcctSchedules) {
      for(int i = 0; i < rbAcctSchedules.size(); ++i) {
         super.update((EnsBaseDbBean)rbAcctSchedules.get(i));
      }

   }

   public List<RbAcctSchedule> selectScheduleByDealDate(String internalKey, String dealDate, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSchedule.class.getName() + ".selectScheduleByDealDate", param);
   }
}
