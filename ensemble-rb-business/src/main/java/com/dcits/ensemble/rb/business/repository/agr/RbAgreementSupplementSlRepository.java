package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplementSl;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWdl;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementSupplementSlRepository extends BusinessRepository {
   public List<RbAgreementSupplementSl> getAgreementSupplementSlList(String agreementId) {
      RbAgreementSupplementSl rbAgreementSupplementSl = new RbAgreementSupplementSl();
      rbAgreementSupplementSl.setAgreementId(agreementId);
      return this.daoSupport.selectList(rbAgreementSupplementSl);
   }

   public RbAgreementSupplementSl getAgreementSupplementSlByChannel(String agreementId, String channelType) {
      RbAgreementSupplementSl rbAgreementSupplementSl = new RbAgreementSupplementSl();
      rbAgreementSupplementSl.setAgreementId(agreementId);
      rbAgreementSupplementSl.setChannel(channelType);
      return (RbAgreementSupplementSl)this.daoSupport.selectOne(rbAgreementSupplementSl);
   }

   public void createRbAgreementSupplementSlBatch(List<RbAgreementSupplementSl> rbAgreementSupplementSlList) {
      if (BusiUtil.isNotNull(rbAgreementSupplementSlList)) {
         super.insertAddBatch(rbAgreementSupplementSlList);
      }

   }

   public void updateByPrimaryKey(RbAgreementWdl rbAgreementWdl) {
      super.update(rbAgreementWdl);
   }
}
