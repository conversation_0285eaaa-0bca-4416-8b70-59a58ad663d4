package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPbContent;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPbContentRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPbContentRepository.class);

   public List<RbPbContent> selectByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbPbContent.class.getName() + ".selectByInternalKey", param);
   }

   public RbPbContent selectOneByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (RbPbContent)this.daoSupport.selectOne(RbPbContent.class.getName() + ".selectByInternalKey", param);
   }

   public RbPbContent deleteByInternalKey(Long internalKey, String clientNo) {
      RbPbContent rbPbContent = new RbPbContent();
      rbPbContent.setInternalKey(internalKey);
      rbPbContent.setClientNo(clientNo);
      this.daoSupport.delete(rbPbContent);
      return null;
   }

   public RbPbContent deleteByInternalKeyAndPageNoLineNo(Long internalKey, String clientNo, String pageNo, String lineNo) {
      RbPbContent rbPbContent = new RbPbContent();
      rbPbContent.setInternalKey(internalKey);
      rbPbContent.setClientNo(clientNo);
      rbPbContent.setPrintPageNo(Integer.valueOf(pageNo));
      rbPbContent.setPrintLineNo(Integer.valueOf(lineNo));
      this.daoSupport.delete(rbPbContent);
      return null;
   }

   public RbPbContent deleteByInternalKey1(String clientNo) {
      RbPbContent rbPbContent = new RbPbContent();
      rbPbContent.setClientNo(clientNo);
      this.daoSupport.delete(rbPbContent);
      return null;
   }

   public RbPbContent selectPageNoLineNo(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbPbContent)this.daoSupport.selectOne(RbPbContent.class.getName() + ".selectPageNoLineNo", param);
   }

   public RbPbContent selectPageNoLineNoUnLimit(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      List<RbPbContent> list = this.daoSupport.selectList(RbPbContent.class.getName() + ".selectPageNoLineNoXXX", param);
      if (BusiUtil.isNotNull(list)) {
         return list.size() > 0 ? (RbPbContent)list.get(0) : null;
      } else {
         return null;
      }
   }

   public int updatePageNoLineNo(Long internalKey, String newPageNo, String newLineNo, String clientNo) {
      RbPbContent mbPbContent = new RbPbContent();
      mbPbContent.setInternalKey(internalKey);
      mbPbContent.setPrintPageNo(Integer.valueOf(newPageNo));
      mbPbContent.setPrintLineNo(Integer.valueOf(newLineNo));
      mbPbContent.setClientNo(clientNo);
      return this.daoSupport.update(RbPbContent.class.getName() + ".updatePageNoLineNo", mbPbContent);
   }

   public RbPbContent selectBySeqNo(String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("seqNo", seqNo);
      param.put("clientNo", clientNo);
      return (RbPbContent)this.daoSupport.selectOne(RbPbContent.class.getName() + ".selectBySeqNo", param);
   }

   public List<RbPbContent> getPrintedInfo(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbPbContent.class.getName() + ".getPrintedInfo", param);
   }

   public RbPbContent queryTdNextPrintLine(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbPbContent)this.daoSupport.selectOne(RbPbContent.class.getName() + ".queryTdNextPrintLine", param);
   }

   public RbPbContent queryTdBlankPrintLine(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbPbContent)this.daoSupport.selectOne(RbPbContent.class.getName() + ".queryTdBlankPrintLine", param);
   }

   public int updateTdNullLine(Long internalKey, String newPageNo, String newLineNo, String clientNo) {
      RbPbContent mbPbContent = new RbPbContent();
      mbPbContent.setInternalKey(internalKey);
      mbPbContent.setPrintPageNo(Integer.valueOf(newPageNo));
      mbPbContent.setPrintLineNo(Integer.valueOf(newLineNo));
      mbPbContent.setClientNo(clientNo);
      return this.daoSupport.update(RbPbContent.class.getName() + ".updateTdNullLine", mbPbContent);
   }

   public int updateTdNullLine1(Long internalKey, String pageNo, String lineNo, String clientNo, String tranTimestamp) {
      RbPbContent mbPbContent = new RbPbContent();
      mbPbContent.setInternalKey(internalKey);
      mbPbContent.setPrintPageNo(Integer.valueOf(pageNo));
      mbPbContent.setPrintLineNo(Integer.valueOf(lineNo));
      mbPbContent.setClientNo(clientNo);
      mbPbContent.setTranTimestamp(tranTimestamp);
      return this.daoSupport.update(RbPbContent.class.getName() + ".updateTdNullLine1", mbPbContent);
   }

   public BigDecimal selectPrintSum(Long internalKey, String clientNo) {
      RbPbContent mbPbContent = new RbPbContent();
      mbPbContent.setInternalKey(internalKey);
      mbPbContent.setClientNo(clientNo);
      return (BigDecimal)this.daoSupport.selectObject(RbPbContent.class.getName() + ".selectPrintSum", mbPbContent);
   }

   public String getPrintPageNo(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (String)this.daoSupport.selectObject(RbPbContent.class.getName() + ".getPrintPageNo", param);
   }

   public int deleteBySeqNo(String seqNo, String clientNo) {
      RbPbContent mbPbContent = new RbPbContent();
      mbPbContent.setSeqNo(seqNo);
      mbPbContent.setClientNo(clientNo);
      return this.daoSupport.delete(RbPbContent.class.getName() + ".deleteBySeqNo", mbPbContent);
   }
}
