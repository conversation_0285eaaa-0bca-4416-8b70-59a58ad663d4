package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctJointInfo;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctJointMain;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctJointRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctJointRepository.class);

   public List<RbAcctJointMain> getJointMainByClientNo(String clientNo) {
      RbAcctJointMain rbAcctJointMain = new RbAcctJointMain();
      rbAcctJointMain.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcctJointMain);
   }

   public List<RbAcctJointInfo> getJointInfoByJointNo(String jointNo, String clientNo) {
      RbAcctJointInfo rbAcctJointInfo = new RbAcctJointInfo();
      rbAcctJointInfo.setJointNo(jointNo);
      rbAcctJointInfo.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcctJointInfo);
   }

   public RbAcctJointInfo getJointInfoByJointClientNo(String jointNo, String jointClientNo) {
      RbAcctJointInfo rbAcctJointInfo = new RbAcctJointInfo();
      rbAcctJointInfo.setJointNo(jointNo);
      rbAcctJointInfo.setJointClientNo(jointClientNo);
      return (RbAcctJointInfo)this.daoSupport.selectOne(rbAcctJointInfo);
   }

   public List<RbAcctJointInfo> getJointInfoByjointClientNo(String jointClientNo) {
      RbAcctJointInfo rbAcctJointInfo = new RbAcctJointInfo();
      rbAcctJointInfo.setJointClientNo(jointClientNo);
      return this.daoSupport.selectList(rbAcctJointInfo);
   }

   public RbAcctJointMain getJointMainByJointNo(String jointNo, String clientNo) {
      RbAcctJointMain rbAcctJointMain = new RbAcctJointMain();
      rbAcctJointMain.setJointNo(jointNo);
      rbAcctJointMain.setClientNo(clientNo);
      return (RbAcctJointMain)this.daoSupport.selectOne(rbAcctJointMain);
   }

   public RbAcctJointMain getJointMain(String clientNo, Long internalKey, String baseAcctNo, String prodType, String ccy) {
      RbAcctJointMain rbAcctJointMain = new RbAcctJointMain();
      rbAcctJointMain.setClientNo(clientNo);
      rbAcctJointMain.setInternalKey(internalKey);
      rbAcctJointMain.setBaseAcctNo(baseAcctNo);
      rbAcctJointMain.setProdType(prodType);
      rbAcctJointMain.setAcctCcy(ccy);
      return (RbAcctJointMain)this.daoSupport.selectOne(rbAcctJointMain);
   }
}
