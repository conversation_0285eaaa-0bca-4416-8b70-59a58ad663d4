package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawTypeHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctWithdrawTypeHistRepository extends BusinessRepository {
   public void createMbAcctWithdrawType(RbAcctWithdrawType rbAcctWithdrawType, String oldWithdrawalType, String withdrawalType, String optionType) {
      RbAcctWithdrawTypeHist rbAcctWithdrawTypeHist = new RbAcctWithdrawTypeHist();
      String seqNo = (String)SequenceGenerator.nextValue(SequenceEnum.withdrawTypeHistSeqNo);
      rbAcctWithdrawTypeHist.setSeqNo(seqNo);
      rbAcctWithdrawTypeHist.setClientNo(rbAcctWithdrawType.getClientNo());
      if (!BusiUtil.isEquals(optionType, "A")) {
         BigDecimal count = (BigDecimal)BusiUtil.nvl(this.getCountByWithdrawKey(rbAcctWithdrawType.getWithdrawKey(), rbAcctWithdrawType.getClientNo(), rbAcctWithdrawType.getCompany()), BigDecimal.ZERO);
         rbAcctWithdrawTypeHist.setDefendNum(count.add(BigDecimal.ONE).intValue());
      } else {
         rbAcctWithdrawTypeHist.setDefendNum(BigDecimal.ONE.intValue());
      }

      rbAcctWithdrawTypeHist.setWithdrawKey(rbAcctWithdrawType.getWithdrawKey());
      rbAcctWithdrawTypeHist.setWithdrawalTypeNew(withdrawalType);
      rbAcctWithdrawTypeHist.setWithdrawalTypeOld(oldWithdrawalType);
      rbAcctWithdrawTypeHist.setTranBranch(Context.getInstance().getBranchId());
      rbAcctWithdrawTypeHist.setUserId(Context.getInstance().getUserId());
      rbAcctWithdrawTypeHist.setAuthUserId(Context.getInstance().getAuthUserId());
      rbAcctWithdrawTypeHist.setReference(Context.getInstance().getReference());
      rbAcctWithdrawTypeHist.setWithdrawOperateType(optionType);
      rbAcctWithdrawTypeHist.setTranDate(DateUtil.parseDate(Context.getInstance().getTranDate()));
      rbAcctWithdrawTypeHist.setCompany(rbAcctWithdrawType.getCompany());
      super.insert(rbAcctWithdrawTypeHist);
   }

   public BigDecimal getCountByWithdrawKey(String withdrawKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("withdrawKey", withdrawKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (BigDecimal)this.daoSupport.selectObject(RbAcctWithdrawTypeHist.class.getName() + ".getCountByWithdrawKey", param);
   }

   public List<RbAcctWithdrawTypeHist> getListByWithdrawKey(String withdrawKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("withdrawKey", withdrawKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctWithdrawTypeHist.class.getName() + ".getListByWithdrawKey", param);
   }
}
