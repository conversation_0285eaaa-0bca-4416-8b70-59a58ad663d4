package com.dcits.ensemble.rb.business.repository.channel;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbJudiciaryAssistantInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RbJudiciaryAssistantInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbJudiciaryAssistantInfoRepository.class);

   public void createRbJudiciaryAssistantIn(RbJudiciaryAssistantInfo rbJudiciaryAssistantInfo) {
      super.insert(rbJudiciaryAssistantInfo);
   }
}
