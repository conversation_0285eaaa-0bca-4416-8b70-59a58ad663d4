package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreementHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Iterator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpAgreementHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpAgreementHistRepository.class);

   public void insert(RbPcpAgreementHist mbPcpAgreementHist) {
      mbPcpAgreementHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbPcpAHSeqNo));
      super.insert(mbPcpAgreementHist);
   }

   public void insertList(List<RbPcpAgreementHist> mbPcpAgreementHistList) {
      Iterator var2 = mbPcpAgreementHistList.iterator();

      while(var2.hasNext()) {
         RbPcpAgreementHist mbPcpAgreementHist = (RbPcpAgreementHist)var2.next();
         mbPcpAgreementHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbPcpAHSeqNo));
         super.insert(mbPcpAgreementHist);
      }

   }
}
