package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.dao.model.BasePo;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementGroup;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementGroupRepository extends BusinessRepository {
   @Resource
   private FmBaseStor fmBaseStor;

   public void createRbAgreementGroupDb(RbAgreementGroup rbAgreementGroup) {
      super.insert(rbAgreementGroup);
   }

   public void updateRbAgreementGroupDb(RbAgreementGroup RbAgreementGroup) {
      super.update(RbAgreementGroup);
   }

   public void deleteRbAgreementGroupDb(RbAgreementGroup rbAgreementGroup) {
      this.daoSupport.delete(rbAgreementGroup);
   }

   public void createRbAgreementGroupDbList(List<RbAgreementGroup> rbAgreementGroups) {
      for(int i = 0; i < rbAgreementGroups.size(); ++i) {
         super.insert((EnsBaseDbBean)rbAgreementGroups.get(i));
      }

   }

   public void updateRbAgreementGroupDbList(List<RbAgreementGroup> rbAgreementGroups) {
      for(int i = 0; i < rbAgreementGroups.size(); ++i) {
         super.update((EnsBaseDbBean)rbAgreementGroups.get(i));
      }

   }

   public void deleteRbAgreementGroupDbList(List<RbAgreementGroup> rbAgreementGroups) {
      for(int i = 0; i < rbAgreementGroups.size(); ++i) {
         this.daoSupport.delete((BasePo)rbAgreementGroups.get(i));
      }

   }

   public List<RbAgreementGroup> getRbAgreementGroupRegByAcct(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementGroup.class.getName() + ".selectByInternalKey", param);
   }

   public List<RbAgreementGroup> selectStatusAOrF(Long internalKey, String clientNo, String effectiveStatus) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("effectiveStatus", effectiveStatus);
      param.put("unEffectiveStatus", "F");
      return this.daoSupport.selectList(RbAgreementGroup.class.getName() + ".selectStatusAOrF", param);
   }

   public RbAgreementGroup selectByAgreementId(String agreementId, String clientNo) {
      RbAgreementGroup RbAgreementGroup = new RbAgreementGroup();
      RbAgreementGroup.setAgreementId(agreementId);
      RbAgreementGroup.setClientNo(clientNo);
      return (RbAgreementGroup)this.daoSupport.selectOne(RbAgreementGroup.class.getName() + ".selectByAgreementId", RbAgreementGroup);
   }

   public List<RbAgreementGroup> selectByReference(String reference) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      return this.daoSupport.selectList(RbAgreementGroup.class.getName() + ".selectByReference", param);
   }

   public List<RbAgreementGroup> selectByAgreementDate(String runDate) {
      Map<String, Object> param = new HashMap();
      param.put("runDate", DateUtil.parseDate(runDate));
      return this.daoSupport.selectList(RbAgreementGroup.class.getName() + ".selectByAgreementDate", param);
   }

   public List<RbAgreementGroup> selectByAgreementRunDate(String runDate) {
      Map<String, Object> param = new HashMap();
      param.put("runDate", DateUtil.parseDate(runDate));
      return this.daoSupport.selectList(RbAgreementGroup.class.getName() + ".selectByAgreementRunDate", param);
   }

   public List<RbAgreementGroup> getAllAgreementGroup(Map<String, Object> map) {
      return this.daoSupport.selectList(RbAgreementGroup.class.getName() + ".getAllAgreementGroup", map);
   }
}
