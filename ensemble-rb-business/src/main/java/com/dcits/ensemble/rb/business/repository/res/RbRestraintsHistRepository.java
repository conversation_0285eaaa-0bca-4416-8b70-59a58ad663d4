package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraintsHist;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTdLien;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbRestraintsHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbRestraintsHistRepository.class);

   public int createMbRestraintsHist(RbRestraintsHist mbRestraintsHist) {
      int res = 0;
      if (BusiUtil.isNotNull(mbRestraintsHist)) {
         res = super.insert(mbRestraintsHist);
      }

      return res;
   }

   public RbRestraintsHist createMbOrder() {
      RbRestraintsHist mbRestraintsHist = new RbRestraintsHist();
      mbRestraintsHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.restraintsHistSeqNo));
      return mbRestraintsHist;
   }

   public List<RbRestraintsHist> selectByReference(String reference, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraintsHist.class.getName() + ".selectByReference", param);
   }

   public List<RbRestraintsHist> selectByReferenceForReversal(String reference, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraintsHist.class.getName() + ".selectByReferenceForReversal", param);
   }

   public List<RbRestraintsHist> selectByResType(String baseAcctNo, String restraintType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("restraintType", restraintType);
      return this.daoSupport.selectList(RbRestraintsHist.class.getName() + ".selectByResType", param);
   }

   public List<RbRestraintsHist> selectByResSeqNo(String resSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("resSeqNo", resSeqNo);
      return this.daoSupport.selectList(RbRestraintsHist.class.getName() + ".selectByResSeqNo", param);
   }

   public int createRbTdLien(RbTdLien rbTdLien) {
      int res = 0;
      if (BusiUtil.isNotNull(rbTdLien)) {
         res = super.insert(rbTdLien);
      }

      return res;
   }

   public List<RbTdLien> selectRbTdLienList(String reference, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbTdLien.class.getName() + ".selectByReference", param);
   }

   public RbTdLien getRbTdLien(RbTdLien rbTdLien) {
      log.debug("rbTdLien===" + rbTdLien);
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", rbTdLien.getInternalKey());
      param.put("startDate", DateUtil.parseDate(Context.getInstance().getRunDate()));
      param.put("endDate", DateUtil.parseDate(Context.getInstance().getRunDate()));
      param.put("clientNo", rbTdLien.getClientNo());
      return (RbTdLien)this.daoSupport.selectOne(RbTdLien.class.getName() + ".selectByDate", param);
   }

   public RbTdLien getRbTdLienByAcctInternalKey(Long acctInternalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", acctInternalKey);
      param.put("clientNo", clientNo);
      return (RbTdLien)this.daoSupport.selectOne(RbTdLien.class.getName() + ".getRbTdLienByAcctInternalKey", param);
   }

   public RbTdLien getRbTdLienByAcctInternalKeyAndReference(Long acctInternalKey, String reference, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", acctInternalKey);
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return (RbTdLien)this.daoSupport.selectOne(RbTdLien.class.getName() + ".getRbTdLienByAcctInternalKeyAndReference", param);
   }

   public int deleteRbTdLien(RbTdLien rbTdLien) {
      log.debug("rbTdLien===" + rbTdLien);
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", rbTdLien.getInternalKey());
      param.put("clientNo", rbTdLien.getClientNo());
      return this.daoSupport.delete(RbTdLien.class.getName() + ".deleteByInternalKey", param);
   }

   public List<RbRestraintsHist> selectByChannelSeqNo(String channelSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("channelSeqNo", channelSeqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraintsHist.class.getName() + ".selectByChannelSeqNo", param);
   }

   public List<RbRestraintsHist> selectByResSeqNoAndClientNo(String resSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("resSeqNo", resSeqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraintsHist.class.getName() + ".selectByResSeqNoAndClientNo", param);
   }

   public List<RbRestraintsHist> selectByResSeqAndChannelSeqNo(String resSeqNo, String channelSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("resSeqNo", resSeqNo);
      param.put("channelSeqNo", channelSeqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraintsHist.class.getName() + ".selectByResSeqAndChannelSeqNo", param);
   }

   public void updateBySeqNo(String seqNo, String reserve, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("seqNo", seqNo);
      param.put("reserve", reserve);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbRestraintsHist.class.getName() + ".updateBySeqNo", param);
   }
}
