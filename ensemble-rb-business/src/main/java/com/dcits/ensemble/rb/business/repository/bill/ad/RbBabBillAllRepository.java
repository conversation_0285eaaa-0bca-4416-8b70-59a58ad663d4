package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillAll;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbBabBillAllRepository extends BusinessRepository {
   public void updateByVoucherNo(String acceptContractNo, String clientNo, String billDocType, String billVoucherNo) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("clientNo", clientNo);
      para.put("billDocType", billDocType);
      para.put("billVoucherNo", billVoucherNo);
      para.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBillAll.class.getName() + ".updateByVoucherNo", para);
   }

   public List<RbBabBillAll> getRbBabBillAllCount(Map map) {
      return this.daoSupport.selectList(RbBabBillAll.class.getName() + ".getRbBabBillAllCount", map);
   }
}
