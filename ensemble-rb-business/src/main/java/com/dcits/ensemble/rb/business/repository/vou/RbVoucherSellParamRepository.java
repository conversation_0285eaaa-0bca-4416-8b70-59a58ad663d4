package com.dcits.ensemble.rb.business.repository.vou;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherSellParam;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbVoucherSellParamRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherSellParamRepository.class);

   @Cached(
      area = "longArea",
      name = "rb:param:rb_voucher_sell:all:",
      key = "'all_rb_voucher_sell'",
      cacheType = CacheType.REMOTE
   )
   public List<RbVoucherSellParam> selectVoucherSellParam() {
      return this.daoSupport.selectAll(new RbVoucherSellParam());
   }
}
