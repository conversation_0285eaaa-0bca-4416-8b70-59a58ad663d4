package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctWithdrawType;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctWithdrawTypeRepository extends BusinessRepository<RbAcctWithdrawType> {
   @Resource
   private RbAcctWithdrawTypeHistRepository rbAcctWithdrawTypeHistRepository;

   public void deleteMbAcctWithdrawType(Long withdrawKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("withdrawKey", withdrawKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      RbAcctWithdrawType rbAcctWithdrawType = this.getMbAcctWithdrawType(withdrawKey, clientNo);
      if (BusiUtil.isNotNull(rbAcctWithdrawType)) {
         this.rbAcctWithdrawTypeHistRepository.createMbAcctWithdrawType(rbAcctWithdrawType, rbAcctWithdrawType.getWithdrawalType(), (String)null, "D");
         this.daoSupport.delete(RbAcctWithdrawType.class.getName() + ".deleteByInternalKey", param);
      }

   }

   public String deleteMbAcctWithdrawTypeNoHist(Long withdrawKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("withdrawKey", withdrawKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      RbAcctWithdrawType rbAcctWithdrawType = this.getMbAcctWithdrawType(withdrawKey, clientNo);
      if (BusiUtil.isNotNull(rbAcctWithdrawType)) {
         this.daoSupport.delete(RbAcctWithdrawType.class.getName() + ".deleteByInternalKey", param);
         return rbAcctWithdrawType.getWithdrawalType();
      } else {
         return null;
      }
   }

   public List<RbAcctWithdrawType> selectByPartPrimaryKey(Long withdrawKey, String withdrawalType, String clientNo) {
      RbAcctWithdrawType rbAcctWithdrawType = new RbAcctWithdrawType();
      rbAcctWithdrawType.setWithdrawKey(String.valueOf(withdrawKey));
      rbAcctWithdrawType.setClientNo(clientNo);
      rbAcctWithdrawType.setWithdrawalType(withdrawalType);
      return this.daoSupport.selectList(rbAcctWithdrawType);
   }

   public List<RbAcctWithdrawType> selectWithdrawTypeByCardNo(String withdrawKey, String withdrawalType, String clientNo) {
      RbAcctWithdrawType rbAcctWithdrawType = new RbAcctWithdrawType();
      rbAcctWithdrawType.setWithdrawKey(withdrawKey);
      rbAcctWithdrawType.setClientNo(clientNo);
      rbAcctWithdrawType.setWithdrawalType(withdrawalType);
      return this.daoSupport.selectList(rbAcctWithdrawType);
   }

   public RbAcctWithdrawType getMbAcctWithdrawType(Long withdrawKey, String clientNo) {
      List<RbAcctWithdrawType> rbAcctWithdrawTypes = this.selectByPartPrimaryKey(withdrawKey, (String)null, clientNo);
      return BusiUtil.isNotNull(rbAcctWithdrawTypes) ? (RbAcctWithdrawType)rbAcctWithdrawTypes.get(0) : null;
   }

   public RbAcctWithdrawType getMbAcctWithdrawType(String withdrawKey, String clientNo) {
      RbAcctWithdrawType rbAcctWithdrawType = new RbAcctWithdrawType();
      rbAcctWithdrawType.setWithdrawKey(withdrawKey);
      rbAcctWithdrawType.setClientNo(clientNo);
      List<RbAcctWithdrawType> rbAcctWithdrawTypes = this.daoSupport.selectList(rbAcctWithdrawType);
      return BusiUtil.isNotNull(rbAcctWithdrawTypes) ? (RbAcctWithdrawType)rbAcctWithdrawTypes.get(0) : null;
   }

   public void updateByRbAcctWithdrawType(RbAcctWithdrawType rbAcctWithdrawType) {
      rbAcctWithdrawType.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctWithdrawType.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctWithdrawType.class.getName() + ".updateByRbAcctWithdrawType", rbAcctWithdrawType);
   }

   public void createMbAcctWithdrawType(RbAcctWithdrawType rbAcctWithdrawType) {
      RbAcctWithdrawType dbAcctWithdrawType = this.getMbAcctWithdrawType(rbAcctWithdrawType.getWithdrawKey(), rbAcctWithdrawType.getWithdrawalType(), rbAcctWithdrawType.getClientNo());
      if (BusiUtil.isNull(dbAcctWithdrawType)) {
         super.insert(rbAcctWithdrawType);
      } else {
         super.update(rbAcctWithdrawType);
      }

      this.rbAcctWithdrawTypeHistRepository.createMbAcctWithdrawType(rbAcctWithdrawType, (String)null, rbAcctWithdrawType.getWithdrawalType(), "A");
   }

   public void createMbAcctWithdrawTypeNoHist(RbAcctWithdrawType rbAcctWithdrawType, String withdrawTypeOld) {
      super.insert(rbAcctWithdrawType);
      this.rbAcctWithdrawTypeHistRepository.createMbAcctWithdrawType(rbAcctWithdrawType, withdrawTypeOld, rbAcctWithdrawType.getWithdrawalType(), "U");
   }

   public RbAcctWithdrawType getMbAcctWithdrawType(String withdrawKey, String withdrawalType, String clientNo) {
      RbAcctWithdrawType rbAcctWithdrawType = new RbAcctWithdrawType();
      rbAcctWithdrawType.setWithdrawKey(withdrawKey);
      rbAcctWithdrawType.setClientNo(clientNo);
      rbAcctWithdrawType.setWithdrawalType(withdrawalType);
      return (RbAcctWithdrawType)super.selectOne(rbAcctWithdrawType);
   }
}
