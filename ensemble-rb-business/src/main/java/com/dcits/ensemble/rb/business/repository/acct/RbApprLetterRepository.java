package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetter;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbApprLetterRepository extends BusinessRepository {
   @Resource
   private FmBaseStor fmBaseStor;

   public RbApprLetter selectMbApprLetterInfo1(String apprLetterNo, String clientNo) {
      RbApprLetter rbApprLetter = new RbApprLetter();
      rbApprLetter.setApprLetterNo(apprLetterNo);
      rbApprLetter.setClientNo(clientNo);
      return (RbApprLetter)this.daoSupport.selectOne(rbApprLetter);
   }

   public RbApprLetter selectByPrimaryKey(RbApprLetter rbApprLetter) {
      return (RbApprLetter)this.daoSupport.selectOne(rbApprLetter);
   }

   public String selectMbApprLetterNo(String apprLetterNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("apprLetterNo", apprLetterNo);
      param.put("company", company);
      return (String)this.daoSupport.selectObject(RbApprLetter.class.getName() + ".selectByInternalKey", param);
   }

   public RbApprLetter selectApprByNoAndCompany(String apprLetterNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      if (BusiUtil.isNotNull(clientNo)) {
         MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      }

      param.put("clientNo", clientNo);
      param.put("apprLetterNo", apprLetterNo);
      return (RbApprLetter)this.daoSupport.selectObject(RbApprLetter.class.getName() + ".selectApprByNoAndCompany", param);
   }

   public List<RbApprLetter> queryByClientNo(String clientNo) {
      RbApprLetter rbApprLetter = new RbApprLetter();
      rbApprLetter.setClientNo(clientNo);
      return this.daoSupport.selectList(rbApprLetter);
   }

   public int deleteMbApprLetter(String apprLetterNo, String clientNo) {
      RbApprLetter rbApprLetter = new RbApprLetter();
      rbApprLetter.setClientNo(clientNo);
      rbApprLetter.setApprLetterNo(apprLetterNo);
      return this.daoSupport.delete(rbApprLetter);
   }

   public List<RbApprLetter> selectByApprLetterInfo(String clientNo, String startDate, String endDate, String company) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("company", company);
      return this.daoSupport.selectList(RbApprLetter.class.getName() + ".selectByApprLetterInfo", param);
   }

   public List<RbApprLetter> selectByApprLetterInfo1(String apprLetterNo, String startDate, String endDate, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("apprLetterNo", apprLetterNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbApprLetter.class.getName() + ".selectByApprLetterInfo", param);
   }
}
