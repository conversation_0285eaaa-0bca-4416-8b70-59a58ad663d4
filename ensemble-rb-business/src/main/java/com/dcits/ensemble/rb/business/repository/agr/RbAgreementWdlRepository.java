package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWdl;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementWdlRepository extends BusinessRepository {
   public void createMbAgreementWdl(RbAgreementWdl rbAgreementWdl) {
      if (BusiUtil.isNotNull(rbAgreementWdl)) {
         super.insert(rbAgreementWdl);
      }

   }

   public void updateByPrimaryKey(RbAgreementWdl rbAgreementWdl) {
      super.update(rbAgreementWdl);
   }

   /** @deprecated */
   @Deprecated
   public RbAgreementWdl getMbAgreementWdl(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return (RbAgreementWdl)this.daoSupport.selectOne(RbAgreementWdl.class.getName() + ".getMbAgreementWdl", param);
   }

   public void updateMbAgreementWdl(RbAgreementWdl rbAgreementWdl) {
      super.update(rbAgreementWdl);
   }

   public void deleteMbAgreementWdlDb(String agreementId, String clientNo) {
      RbAgreementWdl rbAgreementWdl = new RbAgreementWdl();
      rbAgreementWdl.setAgreementId(agreementId);
      rbAgreementWdl.setClientNo(clientNo);
      this.daoSupport.delete(rbAgreementWdl);
   }

   public List<RbAgreementWdl> getWdlInfo(String agreementId) {
      RbAgreementWdl rbAgreementWdl = new RbAgreementWdl();
      rbAgreementWdl.setAgreementId(agreementId);
      return this.daoSupport.selectList(rbAgreementWdl);
   }

   public List<RbAgreementWdl> getMbAgreementWdl(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementType", agreementType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementWdl.class.getName() + ".selectAgrByType", param);
   }

   public RbAgreementWdl getRbAgreementWdl(Long internalKey, String clientNo, String agreementType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("agreementType", agreementType);
      return (RbAgreementWdl)this.daoSupport.selectOne(RbAgreementWdl.class.getName() + ".selectAgreementWdlInfo", param);
   }

   public RbAgreementWdl getUnsignRbAgreementWdl(Long internalKey, String clientNo, String agreementType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbAgreementWdl)this.daoSupport.selectOne(RbAgreementWdl.class.getName() + ".selectUnsignAgreementWdlInfo", param);
   }

   public RbAgreementWdl getRbAgreementWdl(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      return (RbAgreementWdl)this.daoSupport.selectOne(RbAgreementWdl.class.getName() + ".selectOne", param);
   }

   public RbAgreementWdl selectByAcctNo(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String financeType, String agreementStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("financeType", financeType);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", agreementStatus);
      return (RbAgreementWdl)this.daoSupport.selectOne(RbAgreementWdl.class.getName() + ".selectByAcctNo", param);
   }
}
