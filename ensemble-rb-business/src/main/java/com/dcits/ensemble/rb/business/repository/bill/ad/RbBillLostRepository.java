package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.fm.model.FmBranch;
import com.dcits.ensemble.rb.business.bc.component.bill.sequence.RbBillLostSeq;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBillLost;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbBillLostRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbBillLostRepository.class);

   public RbBillLost getMbPnLostBylossNo(String lossNo, String validFlag) {
      Map<String, Object> param = new HashMap();
      param.put("lossNo", lossNo);
      param.put("validFlag", validFlag);
      return (RbBillLost)this.daoSupport.selectOne(RbBillLost.class.getName() + ".getMbPnLostBylossNo", param);
   }

   public RbBillLost getMbPnLostByBillNo(String billNo, String validFlag) {
      Map<String, Object> param = new HashMap();
      param.put("billNo", billNo);
      param.put("validFlag", validFlag);
      return (RbBillLost)this.daoSupport.selectOne(RbBillLost.class.getName() + ".getMbPnLostByBillNo", param);
   }

   public void updateMbPnLostStatus(RbBillLost rbBillLost) {
      super.update(rbBillLost);
   }

   public String mbPnLostInsert(RbBillLost rbBillLost) {
      String lossNo = null;
      if (BusiUtil.isNull(rbBillLost.getLostNo())) {
         RbBillLostSeq mbPnLostSeq = new RbBillLostSeq();
         String seqNo = mbPnLostSeq.getMbPnLostSeq();
         rbBillLost.setLostNo(seqNo);
         lossNo = seqNo + rbBillLost.getLossNo();
         rbBillLost.setLossNo(lossNo);
         this.daoSupport.insert(rbBillLost);
      }

      return lossNo;
   }

   public List<RbBillLost> getRbPnLostList(String branch, String docType, String billType, String billNo, BigDecimal billAmt, Date tranBeginDate, Date tranEndDate, String lossNo) {
      Map<String, Object> param = new HashMap();
      param.put("branchId", branch);
      param.put("docType", docType);
      param.put("billType", billType);
      param.put("billNo", billNo);
      param.put("tranBeginDate", tranBeginDate);
      param.put("tranEndDate", tranEndDate);
      param.put("billAmt", billAmt);
      param.put("lossNo", lossNo);
      return this.daoSupport.selectList(RbBillLost.class.getName() + ".selectRbPnLostList", param);
   }

   public List<RbBillLost> getRbPnLostListByPage(RbBillLost rbBillLostParm, Date tranBeginDate, Date tranEndDate, String[] tranValidFlag, List<FmBranch> branchList, BigDecimal minSignAmt, BigDecimal maxSignAmt) {
      Map<String, Object> param = new HashMap();
      param.put("branchId", rbBillLostParm.getBranchId());
      param.put("docType", rbBillLostParm.getDocType());
      param.put("docClass", rbBillLostParm.getDocClass());
      param.put("billType", rbBillLostParm.getBillType());
      param.put("billNo", rbBillLostParm.getBillNo());
      param.put("tranBeginDate", tranBeginDate);
      param.put("tranEndDate", tranEndDate);
      param.put("billAmt", rbBillLostParm.getBillAmt());
      param.put("lossNo", rbBillLostParm.getLossNo());
      param.put("lostStatus", rbBillLostParm.getLostStatus());
      param.put("tranValidFlagArry", tranValidFlag);
      param.put("branches", branchList);
      param.put("minSignAmt", minSignAmt);
      param.put("maxSignAmt", maxSignAmt);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbBillLost.class.getName() + ".selectRbPnLostListByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbBillLost> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(RbBillLost.class.getName() + ".selectRbPnLostListByPage", param);
      }
   }

   public List<RbBillLost> getRbPnLostList(String billType, String billNo, String lossNo) {
      Map<String, Object> param = new HashMap();
      param.put("billType", billType);
      param.put("billNo", billNo);
      param.put("lossNo", lossNo);
      return this.daoSupport.selectList(RbBillLost.class.getName() + ".selectRbPnLostList", param);
   }

   public List<RbBillLost> getMbPnLostByLossBillNo(String lossNo, String billNo) {
      Map<String, Object> param = new HashMap();
      param.put("lossNo", lossNo);
      param.put("billNo", billNo);
      param.put("validFlag", "A");
      return this.daoSupport.selectList(RbBillLost.class.getName() + ".getMbPnLostByLossBillNo", param);
   }

   public void updateRbBillLost(RbBillLost rbBillLost) {
      this.daoSupport.update(RbBillLost.class.getName() + ".updateRbBillLost", rbBillLost);
   }

   public List<RbBillLost> getBillLostByStatus(String... statusArr) {
      Map<String, Object> param = new HashMap();
      param.put("statusArr", statusArr);
      param.put("tranValidFlag", "A");
      return this.daoSupport.selectList(RbBillLost.class.getName() + ".getBillLostByStatus", param);
   }

   public List<RbBillLost> getLostByBillNo(String billNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("billNo", billNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbBillLost.class.getName() + ".getLostByBillNo", param);
   }
}
