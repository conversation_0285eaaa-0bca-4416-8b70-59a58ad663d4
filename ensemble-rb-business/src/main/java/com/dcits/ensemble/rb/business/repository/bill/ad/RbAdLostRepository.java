package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdLost;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAdLostRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAdLostRepository.class);

   public int creatLost(RbAdLost rbAdLost) {
      return super.insert(rbAdLost);
   }

   public int updateLost(RbAdLost rbAdLost) {
      return super.update(rbAdLost);
   }

   public List<RbAdLost> queryAdLostParam(long internalKey, Date startDate, Date endDate, String lostStatus, String lostNo) {
      Map<String, Object> param = new HashMap();
      if (internalKey != 0L) {
         param.put("internalKey", internalKey);
      }

      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("lostStatus", lostStatus);
      param.put("lostNo", lostNo);
      return this.daoSupport.selectList(RbAdLost.class.getName() + ".queryAdLostParam", param);
   }

   public List<RbAdLost> queryByAdLostNoAndStatus(String lostStatus, long lostNo) {
      Map<String, Object> param = new HashMap();
      param.put("lostStatus", lostStatus);
      param.put("lostNo", lostNo);
      List<RbAdLost> rbAdLosts = this.daoSupport.selectList(RbAdLost.class.getName() + ".queryByAdLostNoAndStatus", param);
      if (BusiUtil.isNull(rbAdLosts)) {
         throw BusiUtil.createBusinessException("AD4002");
      } else {
         return rbAdLosts;
      }
   }

   public List<RbAdLost> queryByReferenceAndStatus(String reference, String status) {
      if (!BusiUtil.isNotNull(reference) && !BusiUtil.isNotNull(status)) {
         throw BusiUtil.createBusinessException("RB9847");
      } else {
         Map<String, Object> param = new HashMap();
         param.put("reference", reference);
         param.put("status", status);
         List<RbAdLost> rbAdLosts = this.daoSupport.selectList(RbAdLost.class.getName() + ".queryByReferenceAndStatus", param);
         return rbAdLosts;
      }
   }
}
