package com.dcits.ensemble.rb.business.repository.acct.schedule;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSchedule;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctScheduleHist;
import com.dcits.ensemble.rb.business.model.acct.schedule.MbAcctScheduleModel;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctScheduleHistRepository extends BusinessRepository {
   @Resource
   private RbAcctScheduleRepository mbAcctScheduleRepository;

   public RbAcctScheduleHist createMbAcctScheduleHist(MbAcctScheduleModel mbAcctScheduleModel) {
      RbAcctScheduleHist rbAcctScheduleHist = new RbAcctScheduleHist();
      Long internalKey = mbAcctScheduleModel.getInternalKey();
      String schedNo = mbAcctScheduleModel.getSchedNo();
      String clientNo = mbAcctScheduleModel.getClientNo();
      RbAcctSchedule rbAcctScheduleold = this.mbAcctScheduleRepository.getSchedule(schedNo, internalKey, clientNo);
      BeanUtil.copy(rbAcctScheduleold, rbAcctScheduleHist);
      rbAcctScheduleHist.setLastChangeDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      return rbAcctScheduleHist;
   }

   public void createMbAcctScheduleHistDb(RbAcctScheduleHist rbAcctScheduleHist) {
      rbAcctScheduleHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.eventSeqNo));
      super.insert(rbAcctScheduleHist);
   }

   public RbAcctScheduleHist selectByPrimaryKey(Long internalKey, String clientNo, String eventType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("eventType", eventType);
      return (RbAcctScheduleHist)this.daoSupport.selectOne(RbAcctScheduleHist.class.getName() + ".selectByPrimaryKey", param);
   }

   public List<RbAcctScheduleHist> selectByPrimaryKeyForList(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctScheduleHist.class.getName() + ".selectByPrimaryKey", param);
   }

   public RbAcctScheduleHist selectOne(Long internalKey, String clientNo, String eventType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("eventType", eventType);
      return (RbAcctScheduleHist)this.daoSupport.selectOne(RbAcctScheduleHist.class.getName() + ".selectOne", param);
   }
}
