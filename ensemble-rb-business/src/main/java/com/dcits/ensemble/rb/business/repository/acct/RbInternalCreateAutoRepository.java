package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalCreateAuto;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbInternalCreateAutoRepository extends BusinessRepository {
   public List<RbInternalCreateAuto> getList() {
      new HashMap(16);
      return this.daoSupport.selectAll(new RbInternalCreateAuto());
   }
}
