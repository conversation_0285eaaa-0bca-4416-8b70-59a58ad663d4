package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterSub;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbApprLetterSubRepository extends BusinessRepository {
   @Resource
   private FmBaseStor fmBaseStor;

   public String selectByInternalKey(String apprLetterNo, String mainSubInd, String ccy, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      param.put("mainSubInd", mainSubInd);
      param.put("ccy", ccy);
      param.put("company", company);
      return (String)this.daoSupport.selectObject(RbApprLetterSub.class.getName() + ".selectByInternalKey", param);
   }

   public List<RbApprLetterSub> quiryMbApprLetterSub(String apprLetterNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbApprLetterSub.class.getName() + ".selectByAPPRSubLetterNo", param);
   }

   public List<RbApprLetterSub> getApprLetterSubByNoAndCompany(String apprLetterNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      if (BusiUtil.isNotNull(clientNo)) {
         MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      }

      param.put("apprLetterNo", apprLetterNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbApprLetterSub.class.getName() + ".getApprLetterSubByNoAndCompany", param);
   }

   public RbApprLetterSub selectByPrimaryKey(String apprLetterNo, String ccy, String clientNo) {
      RbApprLetterSub rbApprLetterSub = new RbApprLetterSub();
      rbApprLetterSub.setApprLetterNo(apprLetterNo);
      rbApprLetterSub.setMainSubInd("M");
      rbApprLetterSub.setCcy(ccy);
      rbApprLetterSub.setClientNo(clientNo);
      return (RbApprLetterSub)this.daoSupport.selectOne(rbApprLetterSub);
   }

   public int deleteMbApprLetterSub(String apprLetterNo, String mainSubInd, String ccy, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      param.put("mainSubInd", mainSubInd);
      param.put("ccy", ccy);
      param.put("clientNo", clientNo);
      return this.daoSupport.delete(RbApprLetterSub.class.getName() + ".delete", param);
   }

   public int deleteMbSubLimitAmt(String apprLetterNo, String mainSubInd, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      param.put("mainSubInd", mainSubInd);
      param.put("clientNo", clientNo);
      return this.daoSupport.delete(RbApprLetterSub.class.getName() + ".delete", param);
   }

   public List<RbApprLetterSub> selectByMainSubInd(String apprLetterNo, String mainSubInd, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      param.put("mainSubInd", mainSubInd);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbApprLetterSub.class.getName() + ".selectByMainSubInd", param);
   }

   public List<RbApprLetterSub> selectByMainSub(String apprLetterNo, String mainSubInd, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      param.put("mainSubInd", mainSubInd);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbApprLetterSub.class.getName() + ".selectByMainSub", param);
   }

   public RbApprLetterSub qryApprInfo(String apprLetterNo, String mainSubInd, String ccy, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("apprLetterNo", apprLetterNo);
      param.put("mainSubInd", mainSubInd);
      param.put("ccy", ccy);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbApprLetterSub)this.daoSupport.selectOne(RbApprLetterSub.class.getName() + ".qryApprInfo", param);
   }
}
