package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchSlSignCloseDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
public class RbBatchSlSignCloseDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbBatchSlSignCloseDetailRepository.class);

   public void batchInsert(List<RbBatchSlSignCloseDetail> list) {
      super.insertAddBatch(list);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateBatchStatus(RbBatchSlSignCloseDetail detail) {
      super.update(detail);
   }
}
