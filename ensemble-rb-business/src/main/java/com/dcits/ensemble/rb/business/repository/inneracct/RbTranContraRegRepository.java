package com.dcits.ensemble.rb.business.repository.inneracct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranContraReg;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbTranContraRegRepository extends BusinessRepository {
   public void insertToRbTranContraReg(RbTranContraReg rbTranContraReg) {
      super.insert(rbTranContraReg);
   }

   public void deleteInfoByReference(RbTranContraReg rbTranContraReg) {
      super.delete(rbTranContraReg);
   }

   public List<RbTranContraReg> getTranContraInfoList(String reference, String channelSeqNo, String subSeqNo, String seqNo) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("channelSeqNo", channelSeqNo);
      param.put("subSeqNo", subSeqNo);
      param.put("seqNo", seqNo);
      String statementPostfix = RbTranContraReg.class.getName() + ".getTranContraInfoList";
      return this.daoSupport.selectList(statementPostfix, param);
   }

   public Integer getTranContraInfoCount(String seqNo, String reference) {
      Map<String, Object> param = new HashMap();
      param.put("seqNo", seqNo);
      param.put("reference", reference);
      return (Integer)this.daoSupport.selectObject(RbTranContraReg.class.getName() + ".getTranContraInfoCount", param);
   }
}
