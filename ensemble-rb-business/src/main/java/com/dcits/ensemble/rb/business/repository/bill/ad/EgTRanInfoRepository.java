package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.model.entity.dbmodel.EgTranInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class EgTRanInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(EgTRanInfoRepository.class);

   public void insertBranchToEgTranInfo(List<EgTranInfo> list) {
      if (BusiUtil.isNotNull(list)) {
         this.daoSupport.insertAddBatch(list);
      }

   }
}
