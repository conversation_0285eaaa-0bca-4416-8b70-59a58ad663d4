package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAgreement;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctAgreementRepository extends BusinessRepository {
   public int insert(RbAcctAgreement rbAcctAgreement) {
      return super.insert(rbAcctAgreement);
   }

   public List<RbAcctAgreement> selectByInternalkey(Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbAcctAgreement.class.getName() + ".selectByInternalKey", param);
   }
}
