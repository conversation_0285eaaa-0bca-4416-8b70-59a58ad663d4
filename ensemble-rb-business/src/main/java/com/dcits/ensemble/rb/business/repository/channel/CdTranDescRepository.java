package com.dcits.ensemble.rb.business.repository.channel;

import com.dcits.ensemble.rb.business.bc.component.channel.model.CdTranDesc;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class CdTranDescRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(CdTranDescRepository.class);

   public CdTranDesc selectByPrimarykey(String messageCode, String messageType, String channel) {
      Map<String, Object> param = new HashMap();
      param.put("messageCode", messageCode);
      param.put("messageType", messageType);
      param.put("channel", channel);
      return (CdTranDesc)this.daoSupport.selectOne(CdTranDesc.class.getName() + ".selectByPrimarykey", param);
   }
}
