package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctNatureDef;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Component;

@Component
public class RbAcctNatureDefRepository extends BusinessRepository {
   public RbAcctNatureDef getMbAcctNatureDefInfo(String acctNature) {
      RbAcctNatureDef query = new RbAcctNatureDef();
      query.setAcctNature(acctNature);
      return (RbAcctNatureDef)this.daoSupport.selectOne(query);
   }
}
