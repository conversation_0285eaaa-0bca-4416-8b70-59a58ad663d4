package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.Context;
import com.dcits.comet.rpc.api.model.head.SysHead;
import com.dcits.ensemble.rb.business.bc.component.bill.sequence.RbBillTranDetailSeq;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBillTranDetail;
import com.dcits.ensemble.rb.business.model.vou.cheque.MbPnTranDetailModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbBillTranDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbBillTranDetailRepository.class);

   public RbBillTranDetail getTranDetailBySerialNo(String serialNo) {
      RbBillTranDetail rbBillTranDetail = new RbBillTranDetail();
      rbBillTranDetail.setSerialNo(serialNo);
      RbBillTranDetail tranDetail = (RbBillTranDetail)this.daoSupport.selectOne(rbBillTranDetail);
      if (BusiUtil.isNull(tranDetail)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return tranDetail;
      }
   }

   public RbBillTranDetail getMbPnTranDetailOne(String origSerialNo, String operType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("origSerialNo", origSerialNo);
      param.put("operType", operType);
      param.put("clientNo", clientNo);
      RbBillTranDetail rbBillTranDetail = (RbBillTranDetail)this.daoSupport.selectOne(RbBillTranDetail.class.getName() + ".selectMbPnTranDetailOne", param);
      return rbBillTranDetail;
   }

   public List<RbBillTranDetail> getMbPnTranDetailList(RbBillTranDetail paramDetail, Date tranBeginDate, Date tranEndDate) {
      Map<String, Object> param = new HashMap();
      param.put("branch", paramDetail.getTranBranch());
      param.put("billType", paramDetail.getBillType());
      param.put("billNo", paramDetail.getBillNo());
      param.put("tranBeginDate", tranBeginDate);
      param.put("tranEndDate", tranEndDate);
      param.put("operateType", paramDetail.getOperateType());
      param.put("docType", paramDetail.getDocType());
      param.put("docClass", paramDetail.getDocClass());
      return this.daoSupport.selectList(RbBillTranDetail.class.getName() + ".selectMbPnTranDetailList", param);
   }

   public List<RbBillTranDetail> getMbPnTranDetailListBySerialNo(String startDate, String endDate, String billStatus, String serialNo, String billNo, List<String> branchs, String operType, String origSerialNo) {
      Map<String, Object> param = new HashMap();
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("billStatus", billStatus);
      param.put("serialNo", serialNo);
      param.put("billNo", billNo);
      param.put("branchs", branchs.toArray());
      param.put("operateType", operType);
      param.put("origSerialNo", origSerialNo);
      List<RbBillTranDetail> rbBillTranDetails = this.daoSupport.selectList(RbBillTranDetail.class.getName() + ".selectMbPnTranDetailListBySerialNo", param);
      return rbBillTranDetails;
   }

   public RbBillTranDetail createMbPnTranDetail(MbPnTranDetailModel mbPnTranDetailModel) {
      RbBillTranDetail rbBillTranDetail = new RbBillTranDetail();
      rbBillTranDetail.setSignDate(mbPnTranDetailModel.getSignDate());
      rbBillTranDetail.setDocType(mbPnTranDetailModel.getDocType());
      rbBillTranDetail.setDocClass(mbPnTranDetailModel.getDocClass());
      rbBillTranDetail.setIssueBankNo(mbPnTranDetailModel.getIssueBankNo());
      rbBillTranDetail.setIssueBankName(mbPnTranDetailModel.getIssueBankName());
      rbBillTranDetail.setSerialNo(mbPnTranDetailModel.getSerialNo());
      rbBillTranDetail.setOrigSerialNo(mbPnTranDetailModel.getOrigSerialNo());
      rbBillTranDetail.setTranType(mbPnTranDetailModel.getTranType());
      rbBillTranDetail.setOperateType(mbPnTranDetailModel.getOperType());
      rbBillTranDetail.setBillType(mbPnTranDetailModel.getBillType());
      rbBillTranDetail.setBillNo(mbPnTranDetailModel.getBillNo());
      rbBillTranDetail.setEncrypKey(mbPnTranDetailModel.getEncryptKey());
      rbBillTranDetail.setCcySign(mbPnTranDetailModel.getCcySign());
      rbBillTranDetail.setBillAmt(mbPnTranDetailModel.getBillAmt());
      rbBillTranDetail.setTranferCashFlag(mbPnTranDetailModel.getTranferCashFlag());
      rbBillTranDetail.setPayerBaseAcctNo(mbPnTranDetailModel.getPayerAcctNo());
      rbBillTranDetail.setPayerAcctName(mbPnTranDetailModel.getPayerName());
      rbBillTranDetail.setPayeeAcctNo(mbPnTranDetailModel.getPayeeAcctNo());
      rbBillTranDetail.setPayeeAcctName(mbPnTranDetailModel.getPayeeAcctName());
      rbBillTranDetail.setIssueBankNo(mbPnTranDetailModel.getIssueBankNo());
      rbBillTranDetail.setIssueBankName(mbPnTranDetailModel.getIssueBankName());
      rbBillTranDetail.setReturnBaseAcctNo(mbPnTranDetailModel.getReturnAcctNo());
      rbBillTranDetail.setReturnAcctName(mbPnTranDetailModel.getReturnAcctName());
      rbBillTranDetail.setLossNo(mbPnTranDetailModel.getLossNo());
      rbBillTranDetail.setFeeChargeType(mbPnTranDetailModel.getFeeChargeType());
      rbBillTranDetail.setFeeOsdAmt(mbPnTranDetailModel.getFeeOsdAmt());
      rbBillTranDetail.setFeeRealAmt(mbPnTranDetailModel.getFeeRealAmt());
      rbBillTranDetail.setTranDate(mbPnTranDetailModel.getTranDate());
      rbBillTranDetail.setTranBranch(mbPnTranDetailModel.getTranBranch());
      rbBillTranDetail.setUserId(mbPnTranDetailModel.getUserId());
      rbBillTranDetail.setApprUserId(mbPnTranDetailModel.getApproUserId());
      rbBillTranDetail.setAuthUserId(mbPnTranDetailModel.getAuthUserId());
      rbBillTranDetail.setDealResult(mbPnTranDetailModel.getDealResult());
      rbBillTranDetail.setReference((String)BusiUtil.nvl(mbPnTranDetailModel.getReference(), Context.getInstance().getReference()));
      rbBillTranDetail.setBillStatus(mbPnTranDetailModel.getBillStatus());
      rbBillTranDetail.setPrintType(mbPnTranDetailModel.getPrintType());
      rbBillTranDetail.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbBillTranDetail.setHangSeqNo(mbPnTranDetailModel.getHangSeqNo());
      rbBillTranDetail.setCashItem(mbPnTranDetailModel.getCashItem());
      rbBillTranDetail.setSourceType(Context.getInstance().getSourceType());
      rbBillTranDetail.setChannelSeqNo(Context.getInstance().getSeqNo());
      rbBillTranDetail.setSubSeqNo(((SysHead)Context.getInstance().getSysHead()).getSubSeqNo());
      rbBillTranDetail.setProgramId(Context.getInstance().getProgramId());
      if (BusiUtil.isNull(rbBillTranDetail.getSerialNo())) {
         RbBillTranDetailSeq rbBillTranDetailSeq = new RbBillTranDetailSeq();
         rbBillTranDetail.setSerialNo(rbBillTranDetailSeq.getMbPnTranDetailSeqId());
      }

      rbBillTranDetail.setClientNo(mbPnTranDetailModel.getClientNo());
      return rbBillTranDetail;
   }

   public void mbPnTranDetailInsert(RbBillTranDetail rbBillTranDetail) {
      if (BusiUtil.isNotNull(rbBillTranDetail)) {
         super.insert(rbBillTranDetail);
      }

   }

   public void updMbPnTranDetailDb(RbBillTranDetail rbBillTranDetail) {
      if (BusiUtil.isNotNull(rbBillTranDetail)) {
         super.update(rbBillTranDetail);
      }

   }

   public RbBillTranDetail getBillTranDetailByChannelSeqNo(String channelSeqNo, String subSeqNo, String reference) {
      RbBillTranDetail rbBillTranDetail = new RbBillTranDetail();
      rbBillTranDetail.setChannelSeqNo(channelSeqNo);
      rbBillTranDetail.setSubSeqNo(subSeqNo);
      rbBillTranDetail.setReference(reference);
      return (RbBillTranDetail)this.daoSupport.selectOne(rbBillTranDetail);
   }
}
