package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalance;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalanceTemp;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbGlHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbAcctProdBalanceRepository extends BusinessRepository<RbAcctProdBalance> {
   private static final Logger log = LoggerFactory.getLogger(RbAcctProdBalanceRepository.class);

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertRbAcctProdBalanceList(List<RbAcctProdBalance> rbAcctProdBalances) {
      this.daoSupport.insertAddBatch(rbAcctProdBalances);
   }

   public void updateRbAcctProdBalanceList(List<RbAcctProdBalance> rbAcctProdBalances) {
      this.daoSupport.insertAddBatch(rbAcctProdBalances);
   }

   public RbAcctProdBalance selectOneProdBalance(String branch, String ccy, String prodType, String accountingStatus, String amtType, String company) {
      RbAcctProdBalance queryParam = new RbAcctProdBalance();
      queryParam.setBranch(branch);
      queryParam.setCcy(ccy);
      queryParam.setProdType(prodType);
      queryParam.setAccountingStatus(accountingStatus);
      queryParam.setAmtType(amtType);
      queryParam.setCompany(company);
      return (RbAcctProdBalance)super.selectOne(queryParam);
   }

   public RbAcctProdBalance selectOneProdBalancegroupBy(String branch, String ccy, String prodType, String accountingStatus, String amtType, String company, Date tranDate) {
      Map<String, Object> queryParam = new HashMap();
      queryParam.put("branch", branch);
      queryParam.put("ccy", ccy);
      queryParam.put("prodType", prodType);
      queryParam.put("accountingStatus", accountingStatus);
      queryParam.put("amtType", amtType);
      queryParam.put("company", company);
      queryParam.put("tranDate", tranDate);
      return (RbAcctProdBalance)this.daoSupport.selectOne(RbAcctProdBalance.class.getName() + ".getOneProdBalancegroupBy", queryParam);
   }

   public List<RbAcctProdBalance> selectProdBalances(RbAcctProdBalance rbAcctProdBalance) {
      return super.selectList(rbAcctProdBalance);
   }

   public void deleteAll() {
      RbAcctProdBalance rbAcctProdBalance = new RbAcctProdBalance();
      rbAcctProdBalance.setCompany(Context.getInstance().getCompany());
      this.daoSupport.delete(RbAcctProdBalance.class.getName() + ".deleteAll", new RbAcctProdBalance());
   }

   public List<RbAcctProdBalanceTemp> sumAcctBalance(Map<String, Object> map) {
      return this.daoSupport.selectList(RbAcctProdBalance.class.getName() + ".getTranSumAmtInfo", map);
   }

   public List<RbAcctProdBalance> sumTempBalanceTmp(Map<String, Object> map) {
      return this.daoSupport.selectList(RbAcctProdBalance.class.getName() + ".getTmpSumAmt", map);
   }

   public List<RbAcctProdBalanceTemp> sumAcctCycleAmount(Map<String, Object> map) {
      return this.daoSupport.selectList(RbAcctProdBalance.class.getName() + ".sumAcctCycleAmount", map);
   }

   public void deleteByDate(Date tranDate, Comparable start, Comparable end) {
      Map<String, Object> map = new HashMap();
      map.put("tranDate", tranDate);
      map.put("start", start);
      map.put("end", end);
      this.daoSupport.delete(RbAcctProdBalance.class.getName() + ".deleteByDate", map);
   }

   public List<RbAcctProdBalance> getBranchAcctBalance(Map<String, Object> map) {
      return this.daoSupport.selectList(RbAcctProdBalance.class.getName() + ".getBranchAcctBalance", map);
   }

   public RbGlHist sumGlHistBalByInternalKey(Map<String, Object> map) {
      return (RbGlHist)this.daoSupport.selectOne(RbAcctProdBalance.class.getName() + ".sumGlHistBalByInternalKey", map);
   }

   public RbGlHist sumGlHistIntByInternalKey(Map<String, Object> map) {
      return (RbGlHist)this.daoSupport.selectOne(RbAcctProdBalance.class.getName() + ".sumGlHistIntByInternalKey", map);
   }
}
