package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegisterHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAdRegisterHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAdRegisterHistRepository.class);

   public List<RbAdRegisterHist> getMbAdRegisterHist(String branch, String dateType, Date startDate, Date endDate, String acceptAcct, String billType, String billPrefix, String billNo, String status, Date tranDate) {
      String date = "18991231";
      if (BusiUtil.isNotNull(dateType)) {
         if (BusiUtil.isNull(endDate)) {
            endDate = tranDate;
         }

         if (BusiUtil.isNull(startDate)) {
            startDate = DateUtil.parseDate(date);
         }

         tranDate = null;
      }

      if (BusiUtil.isNull(dateType)) {
         if (BusiUtil.isNull(endDate)) {
            endDate = tranDate;
         }

         if (BusiUtil.isNull(startDate)) {
            startDate = DateUtil.parseDate(date);
         }
      }

      Map<String, Object> map = new HashMap();
      map.put("branch", branch);
      map.put("dateType", dateType);
      map.put("startDate", startDate);
      map.put("endDate", endDate);
      map.put("acceptAcct", acceptAcct);
      map.put("billType", billType);
      map.put("billPrefix", billPrefix);
      map.put("billNo", billNo);
      map.put("status", status);
      map.put("tranDate", tranDate);
      List<RbAdRegisterHist> rbAdRegisterHist = this.daoSupport.selectList(RbAdRegisterHist.class.getName() + ".getMbAdRegisterHist", map);
      return rbAdRegisterHist;
   }

   public RbAdRegisterHist getMbAdHistOne(String internalKey, String status, String origReference) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", internalKey);
      map.put("status", status);
      map.put("origReference", origReference);
      RbAdRegisterHist rbAdRegisterHist = (RbAdRegisterHist)this.daoSupport.selectOne(RbAdRegisterHist.class.getName() + ".getMbAdHistOne", map);
      return rbAdRegisterHist;
   }

   public void adRegisterHistInsert(RbAdRegisterHist rbAdRegisterHist) {
      if (BusiUtil.isNotNull(rbAdRegisterHist)) {
         super.insert(rbAdRegisterHist);
      }

   }

   public void updateByPrimary(RbAdRegisterHist rbAdRegisterHist) {
      if (BusiUtil.isNotNull(rbAdRegisterHist)) {
         super.update(rbAdRegisterHist);
      }

   }

   public String updHistCancle(RbAdRegister rbAdRegister1, String oldStatus, String status) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", String.valueOf(rbAdRegister1.getInternalKey()));
      map.put("status", status);
      RbAdRegisterHist rbAdRegisterHistOld = (RbAdRegisterHist)this.daoSupport.selectOne(RbAdRegister.class.getName() + ".getMbAdHistOne", map);
      if (BusiUtil.isNull(rbAdRegisterHistOld)) {
         throw BusiUtil.createBusinessException("CL7146");
      } else {
         String reference = rbAdRegisterHistOld.getOrigReference();
         rbAdRegisterHistOld.setOrigReference(rbAdRegisterHistOld.getOrigReference());
         rbAdRegisterHistOld.setOrigReference(Context.getInstance().getReference());
         rbAdRegisterHistOld.setLastChangeUserId(Context.getInstance().getUserId());
         rbAdRegisterHistOld.setLastChangeDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
         super.update(rbAdRegisterHistOld);
         return reference;
      }
   }
}
