package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.model.entity.dbmodel.TbGlHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class TbGlHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(TbGlHistRepository.class);

   public List<TbGlHist> getObGLHistByReference(List<String> seqNos, List<String> subSeqNos) {
      HashMap<String, Object> param = new HashMap();
      param.put("seqNos", seqNos);
      param.put("subSeqNos", subSeqNos);
      return this.daoSupport.selectList(TbGlHist.class.getName() + ".getObGLHistByReference", param);
   }

   public void updateTbGlHistBySeqNo(List<String> tradeNos) {
      HashMap<String, Object> param = new HashMap();
      param.put("list", tradeNos);
      this.daoSupport.update(TbGlHist.class.getName() + ".updateTbGlHistBySeqNo", param);
   }

   public void updateGlPostedYByTradeNo(List<TbGlHist> updTbGlHistList) {
      this.daoSupport.updateAddBatch(TbGlHist.class.getName() + ".updateGlPostedYByTradeNo", updTbGlHistList);
   }
}
