package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.model.dict.BaseDict;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraftCard;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementOverdraftCardModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementOverdraftCardRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementOverdraftCardRepository.class);

   public RbAgreementOverdraftCard getAgreementByCardNo(String cardNo, String clientNo, String agreementStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.cardNo.toString(), cardNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementStatus.toString(), agreementStatus);
      return (RbAgreementOverdraftCard)this.daoSupport.selectOne(RbAgreementOverdraftCard.class.getName() + ".selectByCondition", param);
   }

   public void createMbAgreementOverDraftCard(MbAgreementOverdraftCardModel mbAgreementModel) {
      if (BusiUtil.isNotNull(mbAgreementModel)) {
         RbAgreementOverdraftCard rbAgreement = new RbAgreementOverdraftCard();
         BeanUtil.copy(mbAgreementModel, rbAgreement);
         this.createMbAgreementDb(rbAgreement);
      }

   }

   public RbAgreementOverdraftCard getAgreementBy4Items(String cardNo, String clientNo, String loanNo, String agreementId) {
      RbAgreementOverdraftCard rbAgreementOverdraftCard = new RbAgreementOverdraftCard();
      rbAgreementOverdraftCard.setAgreementId(agreementId);
      rbAgreementOverdraftCard.setCardNo(cardNo);
      rbAgreementOverdraftCard.setAgreementStatus("A");
      rbAgreementOverdraftCard.setClientNo(clientNo);
      rbAgreementOverdraftCard.setLoanNo(loanNo);
      return (RbAgreementOverdraftCard)this.daoSupport.selectOne(rbAgreementOverdraftCard);
   }

   public void createMbAgreementDb(RbAgreementOverdraftCard rbAgreement) {
      if (BusiUtil.isNotNull(rbAgreement)) {
         super.insert(rbAgreement);
      }

   }
}
