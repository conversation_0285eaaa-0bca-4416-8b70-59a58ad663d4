package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbUnboundDepositHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbUnboundDepositHistRepository extends BusinessRepository {
   public void insert(RbUnboundDepositHist mbUnboundDepositHist) {
      mbUnboundDepositHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.unboundDepositHistSeqNo));
      super.insert(mbUnboundDepositHist);
   }
}
