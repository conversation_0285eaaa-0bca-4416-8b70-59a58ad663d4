package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementZeroBalance;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementZeroBalanceRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementZeroBalanceRepository.class);

   public void createRbAgreementZeroBalance(RbAgreementZeroBalance rbAgreementZeroBalance) {
      if (BusiUtil.isNotNull(rbAgreementZeroBalance)) {
         super.insert(rbAgreementZeroBalance);
      }

   }

   public RbAgreementZeroBalance getRbAgreementZeroBalanceByAgreementId(String agreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      return (RbAgreementZeroBalance)this.daoSupport.selectOne(RbAgreementZeroBalance.class.getName() + ".getRbAgreementZeroBalanceByAgreementId", param);
   }
}
