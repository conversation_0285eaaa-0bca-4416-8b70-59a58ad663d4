package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpTranHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbPcpTranHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpTranHistRepository.class);

   public void creatPcpTranHist(List<RbPcpTranHist> list) {
      super.insertAddBatch(list);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void creatPcpTranHist(RbPcpTranHist rbPcpTranHist) {
      super.insert(rbPcpTranHist);
   }
}
