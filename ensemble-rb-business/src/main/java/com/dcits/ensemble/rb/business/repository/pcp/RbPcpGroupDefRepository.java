package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGroupDef;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbPcpGroupDefRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpGroupDefRepository.class);

   public List<RbPcpGroupDef> getMbPcpGroupDefs(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String internalKey, String upperGroupId) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("prodType", prodType);
      map.put("ccy", ccy);
      map.put("acctSeqNo", acctSeqNo);
      map.put("internalKey", internalKey);
      map.put("upperGroupId", upperGroupId);
      return this.daoSupport.selectList(RbPcpGroupDef.class.getName() + ".getMbPcpGroupDefs", map);
   }

   public List<RbPcpGroupDef> selectAllInfo() {
      return this.daoSupport.selectList(new RbPcpGroupDef());
   }

   public RbPcpGroupDef getMbPcpGroupDefByGroupId(String groupId) {
      RbPcpGroupDef rbPcpGroupDef = new RbPcpGroupDef();
      rbPcpGroupDef.setPcpGroupId(groupId);
      return (RbPcpGroupDef)this.daoSupport.selectOne(rbPcpGroupDef);
   }

   public List<RbPcpGroupDef> selectByInternalkey(Long internalkey) {
      Map<String, Object> param = new HashMap();
      param.put("internalkey", internalkey + "");
      return this.daoSupport.selectList(RbPcpGroupDef.class.getName() + ".selectByInternalkay", param);
   }

   public RbPcpGroupDef selectByGroupId(String groupId) {
      Map<String, Object> param = new HashMap();
      param.put("pcpGroupId", groupId);
      return (RbPcpGroupDef)this.daoSupport.selectOne(RbPcpGroupDef.class.getName() + ".selectByGroupId", param);
   }

   public RbPcpGroupDef selectByPrimaryKey(String groupId) {
      RbPcpGroupDef rbPcpGroupDef = new RbPcpGroupDef();
      rbPcpGroupDef.setPcpGroupId(groupId);
      return (RbPcpGroupDef)this.daoSupport.selectOne(rbPcpGroupDef);
   }

   public List<RbPcpGroupDef> selectByBaseInter(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String groupId) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("prodType", prodType);
      map.put("ccy", ccy);
      map.put("acctSeqNo", acctSeqNo);
      map.put("pcpGroupId", groupId);
      return this.daoSupport.selectList(RbPcpGroupDef.class.getName() + ".selectByBaseInter", map);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void createRbPcpGroupDef(RbPcpGroupDef rbPcpGroupDef) {
      super.insert(rbPcpGroupDef);
   }

   public RbPcpGroupDef selectByinternalKeyAndMainAgreementId(Long internalkey, String mainAgreementId, String status) {
      RbPcpGroupDef rbPcpGroupDef = new RbPcpGroupDef();
      rbPcpGroupDef.setInternalKey(internalkey);
      rbPcpGroupDef.setMainAgreementId(mainAgreementId);
      rbPcpGroupDef.setAcctGroupStatus(status);
      return (RbPcpGroupDef)this.daoSupport.selectOne(rbPcpGroupDef);
   }

   public List<RbPcpGroupDef> selectByMainAgreementIdAndStstus(String mainAgreementId, String status) {
      RbPcpGroupDef rbPcpGroupDef = new RbPcpGroupDef();
      rbPcpGroupDef.setAcctGroupStatus(status);
      rbPcpGroupDef.setMainAgreementId(mainAgreementId);
      return this.daoSupport.selectList(rbPcpGroupDef);
   }

   public List<RbPcpGroupDef> selectActPcpGroup() {
      RbPcpGroupDef rbPcpGroupDef = new RbPcpGroupDef();
      rbPcpGroupDef.setAcctGroupStatus("A");
      return this.daoSupport.selectList(rbPcpGroupDef);
   }

   public List<RbPcpGroupDef> selectTopByClientNo(String clientNo, List<String> branchList) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("branchList", branchList);
      return this.daoSupport.selectList(RbPcpGroupDef.class.getName() + ".selectTopByClientNo", param);
   }

   public List<RbPcpGroupDef> selectTopByPcpGroupId(String pcpGroupId, List<String> branchList) {
      Map<String, Object> param = new HashMap();
      param.put("pcpGroupId", pcpGroupId);
      param.put("branchList", branchList);
      return this.daoSupport.selectList(RbPcpGroupDef.class.getName() + ".selectTopByPcpGroupId", param);
   }
}
