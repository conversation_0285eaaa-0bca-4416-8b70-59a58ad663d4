package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplement;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementWdl;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementSupplementRepository extends BusinessRepository {
   public void createMbAgreement(RbAgreementSupplement rbAgreementSupplement) {
      if (BusiUtil.isNotNull(rbAgreementSupplement)) {
         super.insert(rbAgreementSupplement);
      }

   }

   public void updateRbAgreementSupplement(RbAgreementSupplement rbAgreementSupplement) {
      super.update(rbAgreementSupplement);
   }

   public void deleteRbAgreementSupplement(RbAgreementSupplement rbAgreementSupplement) {
      super.delete(rbAgreementSupplement);
   }

   public void updateByPrimaryKey(RbAgreementWdl rbAgreementWdl) {
      super.update(rbAgreementWdl);
   }

   public RbAgreementSupplement geAgreementByAgreementID(String agreementID) {
      RbAgreementSupplement rbAgreementSupplement = new RbAgreementSupplement();
      rbAgreementSupplement.setAgreementId(agreementID);
      return (RbAgreementSupplement)this.daoSupport.selectOne(rbAgreementSupplement);
   }

   public RbAgreementSupplement getActiveAgreementBySlContractNo(String loanNo, String clientNo, String agreementStatus) {
      Map<String, Object> param = new HashMap();
      param.put("loanNo", loanNo);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", agreementStatus);
      return (RbAgreementSupplement)this.daoSupport.selectOne(RbAgreementSupplement.class.getName() + ".getActiveAgreementBySlContractNo", param);
   }

   public RbAgreementSupplement getLastAgreementBySlContractNo(String loanNo, String clientNo) {
      List<RbAgreementSupplement> agreementBySlContractNoList = this.getAgreementBySlContractNoList(loanNo, clientNo, (String)null);
      return BusiUtil.isNotNull(agreementBySlContractNoList) ? (RbAgreementSupplement)agreementBySlContractNoList.get(0) : null;
   }

   public List<RbAgreementSupplement> getAgreementBySlContractNoList(String loanNo, String clientNo, String agreementStatus) {
      Map<String, Object> param = new HashMap();
      param.put("loanNo", loanNo);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", agreementStatus);
      return this.daoSupport.selectList(RbAgreementSupplement.class.getName() + ".getAgreementBySlContractNoList", param);
   }

   public List<RbAgreementSupplement> selectByAcctNoList(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo, List<String> agreementStatus) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", agreementStatus);
      return this.daoSupport.selectList(RbAgreementSupplement.class.getName() + ".selectByAcctNoList", param);
   }

   public RbAgreementSupplement selectActiveByAcctNo(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo, String agreementStatus) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", agreementStatus);
      param.put("runDate", Context.getInstance().getRunDateParse());
      return (RbAgreementSupplement)this.daoSupport.selectOne(RbAgreementSupplement.class.getName() + ".selectActiveByAcctNo", param);
   }

   public RbAgreementSupplement selectByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      return (RbAgreementSupplement)this.daoSupport.selectOne(RbAgreementSupplement.class.getName() + ".selectByClientNo", param);
   }

   public RbAgreementSupplement selectActiveByCardNo(String cardNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", cardNo);
      return (RbAgreementSupplement)this.daoSupport.selectOne(RbAgreementSupplement.class.getName() + ".selectActiveByCardNo", param);
   }

   public RbAgreementSupplement selectAOrTStatusByCardNo(String cardNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", cardNo);
      return (RbAgreementSupplement)this.daoSupport.selectOne(RbAgreementSupplement.class.getName() + ".selectAOrTStatusByCardNo", param);
   }

   public RbAgreementSupplement selectExpiryByAcctNo(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      List<RbAgreementSupplement> selectList = this.daoSupport.selectList(RbAgreementSupplement.class.getName() + ".selectExpiryByAcctNo", param);
      return BusiUtil.isNotNull(selectList) ? (RbAgreementSupplement)selectList.get(0) : null;
   }
}
