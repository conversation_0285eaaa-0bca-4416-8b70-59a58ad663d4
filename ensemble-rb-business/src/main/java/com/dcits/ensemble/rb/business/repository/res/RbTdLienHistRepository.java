package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbTdLien;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbTdLienHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbTdLienHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbTdLienHistRepository.class);

   public void insetIntoHist(RbTdLien rbTdLien) {
      RbTdLienHist rbTdLienHist = new RbTdLienHist();
      rbTdLienHist.setAcctInternalKey(rbTdLien.getInternalKey());
      rbTdLienHist.setClientNo(rbTdLien.getClientNo());
      rbTdLienHist.setEndDate(rbTdLien.getEndDate());
      rbTdLienHist.setStartDate(rbTdLien.getStartDate());
      rbTdLienHist.setReference(rbTdLien.getReference());
      rbTdLienHist.setResSeqNo(rbTdLien.getResSeqNo());
      rbTdLienHist.setRestraintType(rbTdLien.getRestraintType());
      rbTdLienHist.setLastChangeDate(new Date());
      super.insert(rbTdLienHist);
   }
}
