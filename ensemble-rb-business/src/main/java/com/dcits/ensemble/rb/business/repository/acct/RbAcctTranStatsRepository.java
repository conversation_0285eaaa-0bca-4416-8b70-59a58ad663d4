package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTranStats;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctTranStatsRepository extends BusinessRepository {
   public RbAcctTranStats selectByPrimaryKey(Long internalKey) {
      RbAcctTranStats rbAcctTranStats = new RbAcctTranStats();
      rbAcctTranStats.setInternalKey(internalKey);
      return (RbAcctTranStats)this.daoSupport.selectOne(rbAcctTranStats);
   }
}
