package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundAgentMessgae;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbImpoundAgentMessgaeRepository extends BusinessRepository {
   public List<RbImpoundAgentMessgae> getRbImpoundAgentMessgaeList(String reference, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbImpoundAgentMessgae.class.getName() + ".getRbImpoundAgentMessgaeList", param);
   }
}
