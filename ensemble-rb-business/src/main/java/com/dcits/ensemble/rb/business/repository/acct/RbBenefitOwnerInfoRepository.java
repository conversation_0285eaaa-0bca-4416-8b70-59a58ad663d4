package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBenefitOwnerInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
@BusiUnit
public class RbBenefitOwnerInfoRepository extends BusinessRepository<RbBenefitOwnerInfo> {
   public List<RbBenefitOwnerInfo> getOwnerInfoByBeneficiaryNo(String beneficiaryClientNo) {
      RbBenefitOwnerInfo queryInfo = new RbBenefitOwnerInfo();
      queryInfo.setBeneficiaryClientNo(beneficiaryClientNo);
      return this.selectList(queryInfo);
   }

   public List<RbBenefitOwnerInfo> getInfoByInternalKey(Long internalKey, String clientNo) {
      RbBenefitOwnerInfo benefitOwnerInfo = new RbBenefitOwnerInfo();
      benefitOwnerInfo.setInternalKey(internalKey);
      benefitOwnerInfo.setClientNo(clientNo);
      return this.daoSupport.selectList(benefitOwnerInfo);
   }

   public RbBenefitOwnerInfo getInfoByBeneficiaryClientNo(Long internalKey, String beneficiaryClientNo) {
      RbBenefitOwnerInfo benefitOwnerInfo = new RbBenefitOwnerInfo();
      benefitOwnerInfo.setInternalKey(internalKey);
      benefitOwnerInfo.setBeneficiaryClientNo(beneficiaryClientNo);
      return (RbBenefitOwnerInfo)this.daoSupport.selectOne(benefitOwnerInfo);
   }

   public void updateBatch(List<RbBenefitOwnerInfo> infoList) {
      this.daoSupport.updateAddBatch(RbBenefitOwnerInfo.class.getName() + ".updateBatch", infoList);
   }
}
