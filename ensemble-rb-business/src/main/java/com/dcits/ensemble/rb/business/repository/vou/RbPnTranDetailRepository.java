package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.rb.business.bc.component.vou.sequence.MbPnTranDetailSeq;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail;
import com.dcits.ensemble.rb.business.model.vou.cheque.MbPnTranDetailModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import com.dcits.ensemble.util.DateUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPnTranDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPnTranDetailRepository.class);

   public RbPnTranDetail getMbPdRegisterBySerialNo(String serialNo) {
      RbPnTranDetail rbPnTranDetail = new RbPnTranDetail();
      rbPnTranDetail.setSerialNo(serialNo);
      RbPnTranDetail mbPnTranDetail = (RbPnTranDetail)this.daoSupport.selectOne(rbPnTranDetail);
      if (BusiUtil.isNull(mbPnTranDetail)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return mbPnTranDetail;
      }
   }

   public RbPnTranDetail getMbPnTranDetailOne(String serialNo, String operType) {
      Map<String, Object> param = new HashMap();
      param.put("origSerialNo", serialNo);
      param.put("operType", operType);
      RbPnTranDetail mbPnTranDetail = (RbPnTranDetail)this.daoSupport.selectOne(RbPnTranDetail.class.getName() + ".selectMbPnTranDetailOne", param);
      if (BusiUtil.isNull(mbPnTranDetail)) {
         throw BusiUtil.createBusinessException("RB4101");
      } else {
         return mbPnTranDetail;
      }
   }

   public List<RbPnTranDetail> getMbPnTranDetailList(String startDate, String endDate, String billStatus, String serialNo, String billNo, String branch, String operType) {
      Map<String, Object> param = new HashMap();
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("billStatus", billStatus);
      param.put("serialNo", serialNo);
      param.put("billNo", billNo);
      param.put("branch", branch);
      param.put("operType", operType);
      List<RbPnTranDetail> mbPnRegisterList = this.daoSupport.selectList(RbPnTranDetail.class.getName() + ".selectMbPnTranDetailList", param);
      return mbPnRegisterList;
   }

   public List<RbPnTranDetail> getMbPnTranDetailListBySerialNo(String startDate, String endDate, String billStatus, String serialNo, String billNo, List<String> branchs, String operType, String origSerialNo) {
      Map<String, Object> param = new HashMap();
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("billStatus", billStatus);
      param.put("serialNo", serialNo);
      param.put("billNo", billNo);
      param.put("branchs", branchs.toArray());
      param.put("operType", operType);
      param.put("origSerialNo", origSerialNo);
      List<RbPnTranDetail> mbPnRegisterList = this.daoSupport.selectList(RbPnTranDetail.class.getName() + ".selectMbPnTranDetailListBySerialNo", param);
      return mbPnRegisterList;
   }

   public RbPnTranDetail createMbPnTranDetail(MbPnTranDetailModel mbPnTranDetailModel) {
      RbPnTranDetail mbPnTranDetail = new RbPnTranDetail();
      mbPnTranDetail.setSignTime(DateUtil.formatDate(mbPnTranDetailModel.getSignDate(), "yyyy-MM-dd HH:mm:ss.SSSSSS"));
      mbPnTranDetail.setDocType(mbPnTranDetailModel.getDocType());
      mbPnTranDetail.setIssueBankNo(mbPnTranDetailModel.getIssueBankNo());
      mbPnTranDetail.setIssueBankName(mbPnTranDetailModel.getIssueBankName());
      mbPnTranDetail.setSerialNo(mbPnTranDetailModel.getSerialNo());
      mbPnTranDetail.setOrigSerialNo(mbPnTranDetailModel.getOrigSerialNo());
      mbPnTranDetail.setTranType(mbPnTranDetailModel.getTranType());
      mbPnTranDetail.setOperType(mbPnTranDetailModel.getOperType());
      mbPnTranDetail.setBillType(mbPnTranDetailModel.getBillType());
      mbPnTranDetail.setBillNo(mbPnTranDetailModel.getBillNo());
      mbPnTranDetail.setBillPswd(mbPnTranDetailModel.getEncryptKey());
      mbPnTranDetail.setSignCcy(mbPnTranDetailModel.getCcySign());
      mbPnTranDetail.setBillAmt(mbPnTranDetailModel.getBillAmt());
      mbPnTranDetail.setTranferCashFlag(mbPnTranDetailModel.getTranferCashFlag());
      mbPnTranDetail.setPayeeBaseAcctNo(mbPnTranDetailModel.getPayerAcctNo());
      mbPnTranDetail.setPayerAcctName(mbPnTranDetailModel.getPayerName());
      mbPnTranDetail.setPayeeAcctNo(mbPnTranDetailModel.getPayeeAcctNo());
      mbPnTranDetail.setPayeeAcctName(mbPnTranDetailModel.getPayeeAcctName());
      mbPnTranDetail.setIssueBankNo(mbPnTranDetailModel.getIssueBankNo());
      mbPnTranDetail.setIssueBankName(mbPnTranDetailModel.getIssueBankName());
      mbPnTranDetail.setReturnBaseAcctNo(mbPnTranDetailModel.getReturnAcctNo());
      mbPnTranDetail.setReturnAcctName(mbPnTranDetailModel.getReturnAcctName());
      mbPnTranDetail.setLossNo(mbPnTranDetailModel.getLossNo());
      mbPnTranDetail.setFeeChargeType(mbPnTranDetailModel.getFeeChargeType());
      mbPnTranDetail.setFeeOsdAmt(mbPnTranDetailModel.getFeeOsdAmt());
      mbPnTranDetail.setFeeRealAmt(mbPnTranDetailModel.getFeeRealAmt());
      mbPnTranDetail.setTranDate(mbPnTranDetailModel.getTranDate());
      mbPnTranDetail.setTranBranch(mbPnTranDetailModel.getTranBranch());
      mbPnTranDetail.setUserId(mbPnTranDetailModel.getUserId());
      mbPnTranDetail.setApprUserId(mbPnTranDetailModel.getApproUserId());
      mbPnTranDetail.setAuthUserId(mbPnTranDetailModel.getAuthUserId());
      mbPnTranDetail.setDealResult(mbPnTranDetailModel.getDealResult());
      mbPnTranDetail.setReference(Context.getInstance().getReference());
      mbPnTranDetail.setBillStatus(mbPnTranDetailModel.getBillStatus());
      if (BusiUtil.isNull(mbPnTranDetail.getSerialNo())) {
         MbPnTranDetailSeq mbPnRegisterSeq = new MbPnTranDetailSeq();
         mbPnTranDetail.setSerialNo(mbPnRegisterSeq.getMbPnTranDetailSeqId());
      }

      return mbPnTranDetail;
   }

   public void mbPnTranDetailInsert(RbPnTranDetail mbPnTranDetail) {
      if (BusiUtil.isNotNull(mbPnTranDetail)) {
         super.insert(mbPnTranDetail);
      }

   }

   public void updMbPnTranDetailDb(RbPnTranDetail mbPnTranDetail) {
      if (BusiUtil.isNotNull(mbPnTranDetail)) {
         super.update(mbPnTranDetail);
      }

   }
}
