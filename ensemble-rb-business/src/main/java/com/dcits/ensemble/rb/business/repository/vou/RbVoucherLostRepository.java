package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherLost;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherLostExt;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbVoucherLostRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherLostRepository.class);

   public List<RbVoucherLost> selectByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", String.valueOf(internalKey));
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".selectByInternalKey", param);
   }

   public List<RbVoucherLost> selectVoucherLostByEndDate(String startKey, String endKey, String runDate, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("runDate", runDate);
      param.put("startKey", startKey);
      param.put("endKey", endKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".selectVoucherLostByEndDate", param);
   }

   public List<RbVoucherLost> getLostByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", String.valueOf(internalKey));
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getLostByInternalKey", param);
   }

   public RbVoucherLost selectByPrimaryKey(String lostNo, Long internalKey) {
      RbVoucherLost rbVoucherLost = new RbVoucherLost();
      rbVoucherLost.setLostNo(lostNo);
      rbVoucherLost.setLostKey(String.valueOf(internalKey));
      return (RbVoucherLost)this.daoSupport.selectOne(rbVoucherLost);
   }

   public List<RbVoucherLost> selectMbVoucherLost(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", String.valueOf(internalKey));
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".selectMbVoucherLost", param);
   }

   public List<RbVoucherLost> selectMbVoucherLostByVoucherNo(Long internalKey, String docType, String voucherNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostKey", String.valueOf(internalKey));
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".selectMbVoucherLostByVoucherNo", param);
   }

   public RbVoucherLost getMbVoucherLostByLostNo(String lostNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostNo", lostNo);
      param.put("clientNo", clientNo);
      return (RbVoucherLost)this.daoSupport.selectOne(RbVoucherLost.class.getName() + ".getMbVoucherLostByLostNo", param);
   }

   public RbVoucherLost getMbVoucherLostByLostNoAndStatus(String lostNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostNo", lostNo);
      param.put("clientNo", clientNo);
      return (RbVoucherLost)this.daoSupport.selectOne(RbVoucherLost.class.getName() + ".getMbVoucherLostByLostNoAndStatus", param);
   }

   public void createMbVoucherLostsDb(List<RbVoucherLost> mbVoucherLosts) {
      if (BusiUtil.isNotNull(mbVoucherLosts)) {
         for(int i = 0; i < mbVoucherLosts.size(); ++i) {
            super.insert((EnsBaseDbBean)mbVoucherLosts.get(i));
         }
      }

   }

   public void createMbVoucherLostDb(RbVoucherLost mbVoucherLost) {
      if (BusiUtil.isNotNull(mbVoucherLost)) {
         super.insert(mbVoucherLost);
      }

   }

   public void updateMbVoucherLostDb(RbVoucherLost mbVoucherLost) {
      if (BusiUtil.isNotNull(mbVoucherLost)) {
         super.update(mbVoucherLost);
      }

   }

   public void deleteMbVoucherLostDb(RbVoucherLost mbVoucherLost) {
      if (BusiUtil.isNotNull(mbVoucherLost)) {
         this.daoSupport.delete(mbVoucherLost);
      }

   }

   public RbVoucherLost getLccInfo(String lostNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostNo", lostNo);
      param.put("clientNo", clientNo);
      return (RbVoucherLost)this.daoSupport.selectOne(RbVoucherLost.class.getName() + ".getLccInfo", param);
   }

   public RbVoucherLost getVerInfo(String lostNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("lostNo", lostNo);
      if (BusiUtil.isNotNull(clientNo) && BusiUtil.isNotEquals("", clientNo)) {
         param.put("clientNo", clientNo);
      }

      return (RbVoucherLost)this.daoSupport.selectOne(RbVoucherLost.class.getName() + ".getVerInfo", param);
   }

   public List<RbVoucherLost> getMbVoucherLostByMap(Map map, String clientNo) {
      if (BusiUtil.isNotNull(clientNo) && BusiUtil.isNotEquals("", clientNo)) {
         map.put("clientNo", clientNo);
      }

      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getMbVoucherLostByMap", map);
   }

   public List<RbVoucherLost> getMbVoucherLostNoLostKey(Map map, String clientNo) {
      if (BusiUtil.isNotNull(clientNo) && BusiUtil.isNotEquals("", clientNo)) {
         map.put("clientNo", clientNo);
      }

      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getMbVoucherLostNoLostKey", map);
   }

   public RbVoucherLost getVerInfoByCardNo(String lostKey) {
      Map<String, Object> param = new HashMap();
      param.put("lostKey", lostKey);
      return (RbVoucherLost)this.daoSupport.selectOne(RbVoucherLost.class.getName() + ".getVerInfoByCardNo", param);
   }

   public List<RbVoucherLost> getVerInfoByCardNoList(String lostKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("lostKey", lostKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getVerInfoByCardNo", param);
   }

   public List<RbVoucherLost> getVerInfoByCardNoLostKey(String acctNo, String lostKey, String acctSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("lostKey", lostKey);
      param.put("baseAcctNo", acctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getVerInfoByCardNo", param);
   }

   public List<RbVoucherLost> getSortedRbVoucherLosts(String clientNo, String lostNo, String baseAcctNo, String docType, String voucherNo, String startDate, String endDate) {
      Date starDateDate = DateUtil.parseDate(startDate, new String[]{"yyyyMMdd HH:mm:ss"});
      Date endDateDate = DateUtil.parseDate(endDate, new String[]{"yyyyMMdd HH:mm:ss"});
      RbVoucherLostExt rbVoucherLost = new RbVoucherLostExt();
      rbVoucherLost.setClientNo(clientNo);
      rbVoucherLost.setLostNo(lostNo);
      rbVoucherLost.setBaseAcctNo(baseAcctNo);
      rbVoucherLost.setDocType(docType);
      rbVoucherLost.setVoucherNo(voucherNo);
      rbVoucherLost.setStartDate(starDateDate);
      rbVoucherLost.setEndDate(endDateDate);
      rbVoucherLost.setVoucherLostStatus("USE");
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getSortedRbVoucherLosts", rbVoucherLost);
   }

   public List<RbVoucherLost> getSortedRbVoucherLostsAll(String clientNo, String lostNo, String baseAcctNo, String docType, String voucherNo, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("lostNo", lostNo);
      param.put("baseAcctNo", baseAcctNo);
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getSortedRbVoucherLosts", param);
   }

   public List<RbVoucherLost> getSortedRbVoucherLostsByTranBranch(String clientNo, String lostNo, String baseAcctNo, String docType, String voucherNo, String startDate, String endDate, String tranBranch) {
      Date starDateDate = DateUtil.parseDate(startDate, new String[]{"yyyyMMdd"});
      Date endDateDate = DateUtil.parseDate(endDate, new String[]{"yyyyMMdd"});
      RbVoucherLostExt rbVoucherLost = new RbVoucherLostExt();
      rbVoucherLost.setClientNo(clientNo);
      rbVoucherLost.setLostNo(lostNo);
      rbVoucherLost.setBaseAcctNo(baseAcctNo);
      rbVoucherLost.setDocType(docType);
      rbVoucherLost.setVoucherNo(voucherNo);
      rbVoucherLost.setStartDate(starDateDate);
      rbVoucherLost.setEndDate(endDateDate);
      rbVoucherLost.setTranBranch(tranBranch);
      return this.daoSupport.selectList(RbVoucherLost.class.getName() + ".getSortedRbVoucherLostsByTranBranch", rbVoucherLost);
   }

   public RbVoucherLost getRbVoucherLost(RbVoucherLost rbVoucherLost) {
      return (RbVoucherLost)this.daoSupport.selectOne(rbVoucherLost);
   }

   public RbVoucherLost getRbVoucherLostByBaseAcctNo(String baseAcctNo, String voucherNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("voucherNo", voucherNo);
      return (RbVoucherLost)this.daoSupport.selectOne(RbVoucherLost.class.getName() + ".getRbVoucherLostByBaseAcctNo", param);
   }

   public List<RbVoucherLost> getRbVoucherLosts(RbVoucherLost rbVoucherLost) {
      return this.daoSupport.selectList(rbVoucherLost);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void update1(RbVoucherLost rbVoucherLost) {
      super.update(rbVoucherLost);
   }

   public void updateAutoUnblockDate(String clientNo, String lostNo, Date autoUnLockDate) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("lostNo", lostNo);
      param.put("autoUnblockDate", autoUnLockDate);
      this.daoSupport.update(RbVoucherLost.class.getName() + ".updateAutoUnblockDate", param);
   }
}
