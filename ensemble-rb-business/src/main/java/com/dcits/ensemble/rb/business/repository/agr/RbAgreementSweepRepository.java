package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.dao.model.BasePo;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSweep;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementSweepModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementSweepRepository extends BusinessRepository {
   public void createMbAgreementSweepDb(RbAgreementSweep rbAgreementSweep) {
      super.insert(rbAgreementSweep);
   }

   public void updateMbAgreementSweepDb(RbAgreementSweep rbAgreementSweep) {
      super.update(rbAgreementSweep);
   }

   public void deleteMbAgreementSweepDb(RbAgreementSweep rbAgreementSweep) {
      this.daoSupport.delete(rbAgreementSweep);
   }

   public void createMbAgreementSweepDbList(List<RbAgreementSweep> rbAgreementSweeps) {
      for(int i = 0; i < rbAgreementSweeps.size(); ++i) {
         super.insert((EnsBaseDbBean)rbAgreementSweeps.get(i));
      }

   }

   public void updateMbAgreementSweepDbList(List<RbAgreementSweep> rbAgreementSweeps) {
      for(int i = 0; i < rbAgreementSweeps.size(); ++i) {
         super.update((EnsBaseDbBean)rbAgreementSweeps.get(i));
      }

   }

   public void deleteMbAgreementSweepDbList(List<RbAgreementSweep> rbAgreementSweeps) {
      for(int i = 0; i < rbAgreementSweeps.size(); ++i) {
         this.daoSupport.delete((BasePo)rbAgreementSweeps.get(i));
      }

   }

   public RbAgreementSweep createSweep(MbAgreementSweepModel mbAgreementSweepModel) {
      RbAgreementSweep rbAgreementSweep = new RbAgreementSweep();
      BeanUtil.copy(mbAgreementSweepModel, rbAgreementSweep);
      rbAgreementSweep.setSeqNo(mbAgreementSweepModel.getSchedNo());
      rbAgreementSweep.setBaseAcctNo(mbAgreementSweepModel.getBaseAcctNo());
      rbAgreementSweep.setAcctCcy(mbAgreementSweepModel.getCcy());
      rbAgreementSweep.setAcctSeqNo(mbAgreementSweepModel.getAcctSeqNo());
      rbAgreementSweep.setProdType(mbAgreementSweepModel.getProdType());
      rbAgreementSweep.setOthAcctSort(mbAgreementSweepModel.getOthAcctSort());
      rbAgreementSweep.setUserId(Context.getInstance().getUserId());
      rbAgreementSweep.setSignDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      rbAgreementSweep.setAgreementStatus("A");
      rbAgreementSweep.setClientNo(mbAgreementSweepModel.getClientNo());
      if (BusiUtil.isNotNull(mbAgreementSweepModel.getConTransferCount())) {
         rbAgreementSweep.setConTransferCount(Integer.valueOf(mbAgreementSweepModel.getConTransferCount()));
      }

      rbAgreementSweep.setRenewMinAmt(mbAgreementSweepModel.getMinAmt());
      if (BusiUtil.isNotNull(mbAgreementSweepModel.getRenewMultiple())) {
         rbAgreementSweep.setRenewMultiple(Integer.valueOf(mbAgreementSweepModel.getRenewMultiple()));
      }

      return rbAgreementSweep;
   }

   public RbAgreementSweep updateSweep(MbAgreementSweepModel mbAgreementSweepModel) {
      RbAgreementSweep rbAgreementSweep = new RbAgreementSweep();
      BeanUtil.copy(mbAgreementSweepModel, rbAgreementSweep);
      rbAgreementSweep.setRenewMinAmt(mbAgreementSweepModel.getMinAmt());
      if (BusiUtil.isNotNull(mbAgreementSweepModel.getRenewMultiple())) {
         rbAgreementSweep.setRenewMultiple(Integer.valueOf(mbAgreementSweepModel.getRenewMultiple()));
      }

      if (BusiUtil.isNotNull(mbAgreementSweepModel.getConTransferCount())) {
         rbAgreementSweep.setConTransferCount(Integer.valueOf(mbAgreementSweepModel.getConTransferCount()));
      }

      return rbAgreementSweep;
   }

   public void updateFinFixedAmt(RbAgreementSweep rbAgreementSweep) {
      this.daoSupport.update(RbAgreementSweep.class.getName() + ".updateFinFixedAmtByPrimaryKey", rbAgreementSweep);
   }

   public RbAgreementSweep orgupdateSweep(String agreementId, String clientNo) {
      RbAgreementSweep rbAgreementSweep = new RbAgreementSweep();
      String status = "C";
      rbAgreementSweep.setAgreementId(agreementId);
      rbAgreementSweep.setAgreementStatus(status);
      rbAgreementSweep.setClientNo(clientNo);
      return rbAgreementSweep;
   }

   public List<RbAgreementSweep> getMbAgreementSweepRegByAcct(Long internalkey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalkey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementSweep.class.getName() + ".selectByInternalKey", param);
   }

   public List<RbAgreementSweep> getByOthInternalKey(Long othInternalkey) {
      Map<String, Object> param = new HashMap();
      param.put("othInternalKey", othInternalkey);
      return this.daoSupport.selectList(RbAgreementSweep.class.getName() + ".selectByOthInternalKey", param);
   }

   public RbAgreementSweep selectByAgreementId(String agreementId, String clientNo) {
      RbAgreementSweep rbAgreementSweep = new RbAgreementSweep();
      rbAgreementSweep.setAgreementId(agreementId);
      rbAgreementSweep.setClientNo(clientNo);
      return (RbAgreementSweep)this.daoSupport.selectOne(RbAgreementSweep.class.getName() + ".selectByAgreementId", rbAgreementSweep);
   }

   public List<RbAgreementSweep> getMbAgreementSweepReg(Long internalkey, String schedNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalkey);
      param.put("schedNo", schedNo);
      return this.daoSupport.selectList(RbAgreementSweep.class.getName() + ".selectByInternalKeyAndSchedNo", param);
   }

   public List<RbAgreementSweep> getByOthInternalKeyRtt(Long othInternalkey) {
      Map<String, Object> param = new HashMap();
      param.put("othInternalkey", othInternalkey);
      return this.daoSupport.selectList(RbAgreementSweep.class.getName() + ".getByOthInternalKeyRtt", param);
   }
}
