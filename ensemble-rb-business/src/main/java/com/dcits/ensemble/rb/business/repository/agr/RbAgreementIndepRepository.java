package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.model.dict.BaseDict;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementIndep;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAgreementIndepRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementIndepRepository.class);

   public RbAgreementIndep getINDAgreementDetail(String agreementId) {
      RbAgreementIndep rbAgreementIndep = new RbAgreementIndep();
      rbAgreementIndep.setAgreementId(agreementId);
      return (RbAgreementIndep)this.daoSupport.selectOne(rbAgreementIndep);
   }

   public RbAgreementIndep getINDAgreementDetail2(String agreementId, String clientNo) {
      RbAgreementIndep rbAgreementIndep = new RbAgreementIndep();
      rbAgreementIndep.setAgreementId(agreementId);
      rbAgreementIndep.setClientNo(clientNo);
      return (RbAgreementIndep)this.daoSupport.selectOne(rbAgreementIndep);
   }

   public void updateMbAgreementINDep(RbAgreementIndep rbAgreementIndep) {
      super.update(rbAgreementIndep);
   }

   public RbAgreementIndep getMbAgreementINDep(String baseAcctNo, String acctSeqNo, String ccy, String prodType, String clientNo) {
      Map<String, Object> param = new HashMap(1);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.acctSeqNo.toString(), acctSeqNo);
      param.put(BaseDict.acctCcy.toString(), ccy);
      param.put(BaseDict.prodType.toString(), prodType);
      param.put(BaseDict.clientNo.toString(), clientNo);
      return (RbAgreementIndep)this.daoSupport.selectOne(RbAgreementIndep.class.getName() + ".selectMbAgreementByfive", param);
   }
}
