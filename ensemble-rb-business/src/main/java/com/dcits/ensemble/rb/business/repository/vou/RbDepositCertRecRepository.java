package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertRec;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbDepositCertRecRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbDepositCertRecRepository.class);

   public List<RbDepositCertRec> getDepositCertInfo(String depositCertNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("depositCertNo", depositCertNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbDepositCertRec.class.getName() + ".getDepositCertInfo", param);
   }

   public List<RbDepositCertRec> selectDueDepositCertByDepositCertNo(String depositCertNo, String runDate, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("runDate", runDate);
      param.put("depositCertNo", depositCertNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbDepositCertRec.class.getName() + ".selectDueDepositCertByDepositCertNo", param);
   }

   public List<RbDepositCertRec> selectDueDepositCertByInternalKey(String internalKey, String clientNo, String depositCerNo, String depositCertStatus) {
      Map<String, Object> param = new HashMap();
      param.put("mbInternalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("depositCerNo", depositCerNo);
      param.put("depositCertStatus", depositCertStatus);
      return this.daoSupport.selectList(RbDepositCertRec.class.getName() + ".selectDueDepositCertByInternalKey", param);
   }

   public List<RbDepositCertRec> getDepositCertInfoByUser(String userId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("userId", userId);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbDepositCertRec.class.getName() + ".getDepositCertInfoByUserID", param);
   }

   public List<RbDepositCertRec> getDepositCert() {
      Map<String, Object> param = new HashMap();
      return this.daoSupport.selectList(RbDepositCertRec.class.getName() + ".getDepositCert", param);
   }

   public int updateByDepositCertNo(RbDepositCertRec record) {
      return this.daoSupport.update(RbDepositCertRec.class.getName() + ".updateByDepositCertNo", record);
   }

   public void createMbDepositCertVoucher(List<RbDepositCertRec> rbDepositCertRecsList) {
      if (BusiUtil.isNotNull(rbDepositCertRecsList)) {
         Iterator var2 = rbDepositCertRecsList.iterator();

         while(var2.hasNext()) {
            RbDepositCertRec rbDepositCertRec = (RbDepositCertRec)var2.next();
            super.insert(rbDepositCertRec);
         }
      }

   }

   public void updateMbDepositCertRec(List<RbDepositCertRec> rbDepositCertRecsList) {
      if (BusiUtil.isNotNull(rbDepositCertRecsList)) {
         Iterator var2 = rbDepositCertRecsList.iterator();

         while(var2.hasNext()) {
            RbDepositCertRec rbDepositCertRec = (RbDepositCertRec)var2.next();
            this.daoSupport.update(RbDepositCertRec.class.getName() + ".updateByDepositCertNo", rbDepositCertRec);
         }
      }

   }

   public List<RbDepositCertRec> getDepositCertInfoByClientNo(String clientNo, String depositCerNo) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("depositCertNo", depositCerNo);
      return this.daoSupport.selectList(RbDepositCertRec.class.getName() + ".getDepositCertInfoByClientNo", param);
   }
}
