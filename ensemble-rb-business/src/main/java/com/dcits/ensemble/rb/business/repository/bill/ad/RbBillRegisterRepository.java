package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.fm.model.FmBranch;
import com.dcits.ensemble.rb.business.bc.component.bill.sequence.RbBillTranDetailSeq;
import com.dcits.ensemble.rb.business.common.util.FmUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBillRegister;
import com.dcits.ensemble.rb.business.model.vou.cheque.MbPnRegModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbBillRegisterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbBillRegisterRepository.class);

   public RbBillRegister createMbRegister(MbPnRegModel mbPnRegModel) {
      RbBillRegister rbBillRegister = new RbBillRegister();
      rbBillRegister.setPayerBankCode(mbPnRegModel.getPayerBankCode());
      rbBillRegister.setPayerBankName(mbPnRegModel.getPayerBankName());
      rbBillRegister.setSerialNo(mbPnRegModel.getSerialNo());
      rbBillRegister.setBillType(mbPnRegModel.getBillType());
      rbBillRegister.setSignType(mbPnRegModel.getTranferCashFlag());
      rbBillRegister.setPayerBaseAcctNo(mbPnRegModel.getPayerAcctNo());
      rbBillRegister.setMediumNo(mbPnRegModel.getMediumNo());
      rbBillRegister.setPayerAcctName(mbPnRegModel.getPayerName());
      rbBillRegister.setPayerDocumentType(mbPnRegModel.getPayerGlobalIdType());
      rbBillRegister.setPayerDocumentId(mbPnRegModel.getPayerGlobalId());
      rbBillRegister.setBillSignDate(mbPnRegModel.getBillSignDate());
      rbBillRegister.setBillApplyType(mbPnRegModel.getBillApplyType());
      rbBillRegister.setBillApplyPrefix(mbPnRegModel.getBillApplyPrefix());
      rbBillRegister.setBillApplyNo(mbPnRegModel.getBillApplyNo());
      rbBillRegister.setBillApplyDate(mbPnRegModel.getBillApplyDate());
      rbBillRegister.setCcySign(mbPnRegModel.getCcySign());
      rbBillRegister.setBillTranAmt(mbPnRegModel.getBillTranAmt());
      rbBillRegister.setPayeeBaseAcctNo(mbPnRegModel.getPayeeAcctNo());
      rbBillRegister.setPayeeAcctName(mbPnRegModel.getPayeeName());
      rbBillRegister.setPayeeAcctSeqNo(mbPnRegModel.getPayeeAcctSeqNo());
      rbBillRegister.setPayeeAcctCcy(mbPnRegModel.getPayeeAcctCcy());
      rbBillRegister.setPayeeProdType(mbPnRegModel.getPayeeProdeType());
      rbBillRegister.setRemark(mbPnRegModel.getRemark());
      rbBillRegister.setBillSignBranch(mbPnRegModel.getBillSignBranch());
      rbBillRegister.setBillSignUserId(mbPnRegModel.getBillSignUserId());
      rbBillRegister.setAuthUserId(mbPnRegModel.getAuthUser());
      rbBillRegister.setPayerAcctSeqNo(mbPnRegModel.getPayerAcctSeqNo());
      rbBillRegister.setPayerAcctCcy(mbPnRegModel.getPayerAcctCcy());
      rbBillRegister.setPayerAcctProdType(mbPnRegModel.getPayerProdeType());
      rbBillRegister.setBillStatus(mbPnRegModel.getBillStatus());
      rbBillRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
      rbBillRegister.setAgentFlag((String)BusiUtil.nvl(mbPnRegModel.getAgentFlag(), "N"));
      rbBillRegister.setAgentBranch(mbPnRegModel.getAgentbankNo());
      rbBillRegister.setAgentName(mbPnRegModel.getAgentName());
      rbBillRegister.setAgentDocumentType(mbPnRegModel.getAgentDocumentType());
      rbBillRegister.setAgentDocumentId(mbPnRegModel.getAgentDocumentNo());
      rbBillRegister.setDocType(mbPnRegModel.getDocType());
      rbBillRegister.setDocClass(mbPnRegModel.getDocClass());
      rbBillRegister.setSignCashItem(mbPnRegModel.getCashItem());
      rbBillRegister.setPromptDate(mbPnRegModel.getPromptDate());
      if (BusiUtil.isNotNull(mbPnRegModel.getClientNo())) {
         rbBillRegister.setClientNo(mbPnRegModel.getClientNo());
      } else {
         FmBranch fmBranch = FmUtil.getBranch(Context.getInstance().getBranchId());
         rbBillRegister.setClientNo(fmBranch.getInternalClient());
      }

      if (BusiUtil.isNull(rbBillRegister.getSerialNo())) {
         RbBillTranDetailSeq mbPnRegisterSeq = new RbBillTranDetailSeq();
         rbBillRegister.setSerialNo(mbPnRegisterSeq.getMbPnTranDetailSeqId());
      }

      rbBillRegister.setExpireFlag("N");
      return rbBillRegister;
   }

   public void mbPnRegisterInsert(RbBillRegister rbBillRegister) {
      if (BusiUtil.isNotNull(rbBillRegister)) {
         super.insert(rbBillRegister);
      }

   }

   public void updMbPnRegisterDb(RbBillRegister rbBillRegister) {
      super.update(rbBillRegister);
   }

   public void updMbPnRegisterByBillNo(RbBillRegister rbBillRegister) {
      this.daoSupport.update(RbBillRegister.class.getName() + ".updMbPnRegisterByBillNo", rbBillRegister);
   }

   public RbBillRegister getMbPdRegisterBySerialNo(String serialNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("serialNo", serialNo);
      RbBillRegister mbPnRegister = (RbBillRegister)this.daoSupport.selectOne(RbBillRegister.class.getName() + ".selectByPrimaryKey", param);
      if (BusiUtil.isNull(mbPnRegister)) {
         throw BusiUtil.createBusinessException("PN4007", new String[]{serialNo});
      } else {
         return mbPnRegister;
      }
   }

   public List<RbBillRegister> getMbPdRegisterList(String startDate, String endDate, String billStatus, String branch, String serialNo, String billNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("billStatus", billStatus);
      param.put("branch", branch);
      param.put("serialNo", serialNo);
      param.put("billNo", billNo);
      List<RbBillRegister> mbPnRegisterList = this.daoSupport.selectList(RbBillRegister.class.getName() + ".selectMbPdRegisterList", param);
      return mbPnRegisterList;
   }

   public RbBillRegister getMbPnRegisterByBillNo(String docType, String billNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("docType", docType);
      param.put("billNo", billNo);
      RbBillRegister mbPnRegister = (RbBillRegister)this.daoSupport.selectOne(RbBillRegister.class.getName() + ".selectByBillNo", param);
      if (BusiUtil.isNull(mbPnRegister)) {
         throw BusiUtil.createBusinessException("PN4010", new String[]{billNo});
      } else {
         return mbPnRegister;
      }
   }

   public RbBillRegister getMbPnRegisterByBillType(String billType, String docType, String billNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("billType", billType);
      param.put("docType", docType);
      param.put("billNo", billNo);
      RbBillRegister mbPnRegister = (RbBillRegister)this.daoSupport.selectOne(RbBillRegister.class.getName() + ".selectByBillType", param);
      return mbPnRegister;
   }

   public RbBillRegister getRegisterByBillNo(String billStatus, String billNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("billStatus", billStatus);
      param.put("billNo", billNo);
      RbBillRegister mbPnRegister = (RbBillRegister)this.daoSupport.selectOne(RbBillRegister.class.getName() + ".selectRegisterByBillNo", param);
      return mbPnRegister;
   }

   public List<RbBillRegister> getMbPdRegisterListByBillNo(String billNo, String docClass) {
      Map<String, Object> param = new HashMap(16);
      param.put("billNo", billNo);
      param.put("docClass", docClass);
      List<RbBillRegister> mbPnRegisterList = this.daoSupport.selectList(RbBillRegister.class.getName() + ".selectMbPdRegisterListByBillNo", param);
      return mbPnRegisterList;
   }

   public List<RbBillRegister> getMbPdRegisterListBySerialNo(String serialNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("serialNo", serialNo);
      List<RbBillRegister> mbPnRegisterList = this.daoSupport.selectList(RbBillRegister.class.getName() + ".selectMbPdRegisterListBySerialNo", param);
      return mbPnRegisterList;
   }

   public List<RbBillRegister> getMbPdRegisterList(Map map) {
      List<RbBillRegister> mbPnRegisterList = this.daoSupport.selectList(RbBillRegister.class.getName() + ".getMbPdRegisterList", map);
      return mbPnRegisterList;
   }

   public List<RbBillRegister> getMbPdRegisterListByPage(Map map) {
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbBillRegister.class.getName() + ".getMbPdRegisterList";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbBillRegister> queryResult = this.daoSupport.selectQueryResult(statementPostfix, map, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(RbBillRegister.class.getName() + ".getMbPdRegisterList", map);
      }
   }

   public List<RbBillRegister> getUnApprBillList(String branch, String userId, String tranDate, String billStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put("billSignBranch", branch);
      param.put("billSignUserId", userId);
      param.put("billSignDate", DateUtil.parseDate(tranDate));
      param.put("billStatus", billStatus);
      return this.daoSupport.selectList(RbBillRegister.class.getName() + ".getUnApprBillList", param);
   }

   public List<RbBillRegister> getUnpayPnList() {
      RbBillRegister billRegister = new RbBillRegister();
      billRegister.setBillStatus("01");
      billRegister.setDocClass("BNK");
      return this.daoSupport.selectList(billRegister);
   }

   public int getMbPdRegisterCount(Map map) {
      return this.daoSupport.count(RbBillRegister.class.getName() + ".getMbPdRegisterCount", map);
   }

   public List<RbBillRegister> queryOverdueBillInfo() {
      Map<String, Object> param = new HashMap(16);
      param.put("expireFlag", "N");
      return this.daoSupport.selectList(RbBillRegister.class.getName() + ".queryOverdueBillList", param);
   }

   public int checkBillStatusByBranch(String branch, String[] billstatus) {
      Map<String, Object> param = new HashMap(16);
      param.put("signbranch", branch);
      param.put("billstatuses", billstatus);
      return this.daoSupport.count(RbBillRegister.class.getName() + ".checkBillStatusByBranch", param);
   }
}
