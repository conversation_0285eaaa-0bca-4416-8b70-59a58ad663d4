package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.model.bill.ad.MbEntrustModel;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.MbEntrustCollect;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class RbEntrustCollectRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbEntrustCollectRepository.class);

   public List<String> getEntrustNoByRegisterDate(String tranDate) {
      HashMap param = new HashMap(16);
      param.put("registerDate", tranDate);
      return this.daoSupport.selectList(MbEntrustCollect.class.getName() + ".getByRegisterDate", param);
   }

   public List<MbEntrustCollect> getByEntrustNo(MbEntrustModel mb) {
      HashMap param = new HashMap(16);
      param.put("entrustNo", mb.getEntrustNo());
      param.put("eventType", mb.getEventType());
      param.put("entrustStatus", mb.getEntrustStatus());
      return this.daoSupport.selectList(MbEntrustCollect.class.getName() + ".getByEntrustNo", param);
   }

   public void updateByEntrustNo(MbEntrustCollect mbEntrustCollect) {
      mbEntrustCollect.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(MbEntrustCollect.class.getName() + ".updateByEntrustNo", mbEntrustCollect);
   }

   public List<MbEntrustCollect> getEntrustbyNumber(String paperNumber) {
      HashMap param = new HashMap(16);
      param.put("paperNumber", paperNumber);
      return this.daoSupport.selectList(MbEntrustCollect.class.getName() + ".getEntrustbyNumber", param);
   }

   public List<MbEntrustCollect> getMbEntrustCollect(MbEntrustCollect mbEntrustCollect, String startDate, String endDate) {
      if ("".equals(mbEntrustCollect.getEntrustNo())) {
         mbEntrustCollect.setEntrustNo((String)null);
      }

      if ("".equals(mbEntrustCollect.getPayAcctNo())) {
         mbEntrustCollect.setPayAcctNo((String)null);
      }

      if ("".equals(mbEntrustCollect.getPaperNumber())) {
         mbEntrustCollect.setPaperNumber((String)null);
      }

      if ("".equals(mbEntrustCollect.getSendReceiveFlag())) {
         mbEntrustCollect.setSendReceiveFlag((String)null);
      }

      if ("".equals(mbEntrustCollect.getEntrustStatus())) {
         mbEntrustCollect.setEntrustStatus((String)null);
      }

      if ("".equals(mbEntrustCollect.getEntrustAmt())) {
         mbEntrustCollect.setEntrustAmt((String)null);
      }

      if ("".equals(mbEntrustCollect.getAcceptAcctNo())) {
         mbEntrustCollect.setAcceptAcctNo((String)null);
      }

      if ("".equals(startDate)) {
         startDate = null;
      }

      if ("".equals(endDate)) {
         endDate = null;
      }

      Map<String, Object> map = new HashMap(16);
      map.put("branch", mbEntrustCollect.getBranch());
      map.put("payAcctNo", mbEntrustCollect.getPayAcctNo());
      map.put("paperNumber", mbEntrustCollect.getPaperNumber());
      map.put("sendReceiveFlag", mbEntrustCollect.getSendReceiveFlag());
      map.put("batch", mbEntrustCollect.getBatch());
      map.put("entrustStatus", mbEntrustCollect.getEntrustStatus());
      map.put("entrustNo", mbEntrustCollect.getEntrustNo());
      map.put("entrustAmt", mbEntrustCollect.getEntrustAmt());
      map.put("acceptAcctNo", mbEntrustCollect.getAcceptAcctNo());
      map.put("startDate", startDate);
      map.put("endDate", endDate);
      return this.daoSupport.selectList(MbEntrustCollect.class.getName() + ".getMbEntrustCollect", map);
   }

   public void insertBatch(List<MbEntrustCollect> mbEntrustCollects) {
      this.daoSupport.insertBatch(mbEntrustCollects);
   }

   public MbEntrustCollect getEntrustByEntrustNo(String entrustNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("entrustNo", entrustNo);
      return (MbEntrustCollect)this.daoSupport.selectOne(MbEntrustCollect.class.getName() + ".getEntrustByEntrustNo", param);
   }
}
