package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTaeAccountCheckDetail;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTaeAccountCheckSum;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbTaeAccountCheckRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbTaeAccountCheckRepository.class);

   public List<RbTaeAccountCheckDetail> selectBatchCheckDetailBySegment(Comparable segmentStart, Comparable segmentEnd) {
      Map<String, Object> map = new HashMap(2);
      map.put("segmentStart", segmentStart);
      map.put("segmentEnd", segmentEnd);
      return this.daoSupport.selectList(RbTaeAccountCheckDetail.class.getName().concat(".selectBatchCheckDetailBySegment"), map);
   }

   public void deleteBatchCheckDetailBySegment(Comparable segmentStart, Comparable segmentEnd) {
      Map<String, Object> map = new HashMap(2);
      map.put("segmentStart", segmentStart);
      map.put("segmentEnd", segmentEnd);
      this.daoSupport.delete(RbTaeAccountCheckDetail.class.getName().concat(".deleteBatchCheckDetailBySegment"), map);
   }

   public RbTaeAccountCheckSum getRbTaeAccountCheckSumBySessionId(String sessionId) {
      Map<String, Object> param = new HashMap();
      param.put("sessionId", sessionId);
      return (RbTaeAccountCheckSum)this.daoSupport.selectOne(RbTaeAccountCheckSum.class.getName() + ".getRbTaeAccountCheckSum", param);
   }

   public List<RbTaeAccountCheckDetail> getDetailBySessionIdAndFlag(String sessionId, String flag) {
      Map<String, Object> param = new HashMap();
      param.put("sessionId", sessionId);
      param.put("flag", flag);
      return this.daoSupport.selectList(RbTaeAccountCheckDetail.class.getName() + ".getDetailBySessionIdAndFlag", param);
   }
}
