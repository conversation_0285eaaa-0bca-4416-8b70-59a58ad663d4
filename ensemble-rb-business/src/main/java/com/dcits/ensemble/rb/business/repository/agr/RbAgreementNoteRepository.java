package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementNote;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementNoteRepository extends BusinessRepository {
   public void createMbAgreementNote(RbAgreementNote rbAgreementNote) {
      if (BusiUtil.isNotNull(rbAgreementNote)) {
         super.insert(rbAgreementNote);
      }

   }

   public void updateByPrimaryKey(RbAgreementNote rbAgreementNote) {
      super.update(rbAgreementNote);
   }

   /** @deprecated */
   @Deprecated
   public RbAgreementNote getMbAgreementNote(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return (RbAgreementNote)this.daoSupport.selectOne(RbAgreementNote.class.getName() + ".getMbAgreementNote", param);
   }

   public void updateMbAgreementNote(RbAgreementNote rbAgreementNote) {
      super.update(rbAgreementNote);
   }

   public void deleteMbAgreementNoteDb(String agreementId, String clientNo) {
      RbAgreementNote rbAgreementNote = new RbAgreementNote();
      rbAgreementNote.setAgreementId(agreementId);
      rbAgreementNote.setClientNo(clientNo);
      this.daoSupport.delete(rbAgreementNote);
   }

   public List<RbAgreementNote> getNoteInfo(String agreementId) {
      RbAgreementNote rbAgreementNote = new RbAgreementNote();
      rbAgreementNote.setAgreementId(agreementId);
      return this.daoSupport.selectList(rbAgreementNote);
   }

   public List<RbAgreementNote> getMbAgreementNote(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementType", agreementType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementNote.class.getName() + ".selectAgrByType", param);
   }

   public RbAgreementNote getRbAgreementNote(Long internalKey, String clientNo, String agreementType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("agreementType", agreementType);
      return (RbAgreementNote)this.daoSupport.selectOne(RbAgreementNote.class.getName() + ".selectAgreementNoteInfo", param);
   }

   public RbAgreementNote getUnsignRbAgreementNote(Long internalKey, String clientNo, String agreementType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("agreementType", agreementType);
      return (RbAgreementNote)this.daoSupport.selectOne(RbAgreementNote.class.getName() + ".selectUnsignAgreementNoteInfo", param);
   }

   public RbAgreementNote getRbAgreementNote(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      return (RbAgreementNote)this.daoSupport.selectOne(RbAgreementNote.class.getName() + ".selectOne", param);
   }

   public RbAgreementNote selectByAcctNo(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String financeType, String agreementStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("financeType", financeType);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", agreementStatus);
      return (RbAgreementNote)this.daoSupport.selectOne(RbAgreementNote.class.getName() + ".selectByAcctNo", param);
   }

   public List<RbAgreementNote> getAgreementNoteByInternalKey(Long internalKey, String agreementType, String clientNo) {
      RbAgreementNote rbAgreementNote = new RbAgreementNote();
      rbAgreementNote.setInternalKey(internalKey);
      rbAgreementNote.setAgreementType(agreementType);
      rbAgreementNote.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAgreementNote);
   }

   public RbAgreementNote getActiveRbAgreementNote(Long internalKey, String clientNo, String agreementType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("agreementType", agreementType);
      return (RbAgreementNote)this.daoSupport.selectOne(RbAgreementNote.class.getName() + ".getActiveRbAgreementNote", param);
   }

   public RbAgreementNote getRbAgreementNoteByAgreementId(String agreementId, String clientNo, String agreementType) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("agreementType", agreementType);
      return (RbAgreementNote)this.daoSupport.selectOne(RbAgreementNote.class.getName() + ".getActiveRbAgreementNote", param);
   }
}
