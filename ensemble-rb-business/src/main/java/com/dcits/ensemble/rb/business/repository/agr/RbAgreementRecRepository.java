package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementRec;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementRecRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementRecRepository.class);

   public RbAgreementRec selectByPrimaryKey(String agreementId) {
      RbAgreementRec rbAgreementRec = new RbAgreementRec();
      rbAgreementRec.setAgreementId(agreementId);
      return (RbAgreementRec)this.daoSupport.selectOne(rbAgreementRec);
   }

   public List<RbAgreementRec> getActiveAgreementByClientNoAndId(String clientNo, String documentId) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("documentId", documentId);
      return this.daoSupport.selectList(RbAgreementRec.class.getName() + ".getActiveAgreementByClientNoAndId", param);
   }

   public List<RbAgreementRec> getActiveAgreementByPhoneNo(String phoneNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("phoneNo", phoneNo);
      return this.daoSupport.selectList(RbAgreementRec.class.getName() + ".getActiveAgreementByPhoneNo", param);
   }
}
