package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbReversedDelayTransferMsg;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbReversedDelayTransferMsgRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbReversedDelayTransferMsgRepository.class);
   private static final String RB_STATUS_O = "O";
   private static final int SEND_NO = 0;

   public void createRbReversedDelayTransferMsg(String baseAcctNo, String clientNo) {
      RbReversedDelayTransferMsg rbReversedDelayTransferMsg = new RbReversedDelayTransferMsg();
      rbReversedDelayTransferMsg.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.reversedDelayTransferMsgSeqNo));
      rbReversedDelayTransferMsg.setBaseAcctNo(baseAcctNo);
      rbReversedDelayTransferMsg.setRbStatus("O");
      rbReversedDelayTransferMsg.setSendNo(0);
      rbReversedDelayTransferMsg.setCreateDate(new Date());
      rbReversedDelayTransferMsg.setClientNo(clientNo);
      this.daoSupport.insert(rbReversedDelayTransferMsg);
   }
}
