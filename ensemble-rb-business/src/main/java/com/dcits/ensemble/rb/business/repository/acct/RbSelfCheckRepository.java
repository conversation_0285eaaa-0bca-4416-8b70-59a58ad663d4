package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbSelfCheck;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbSelfCheckRepository extends BusinessRepository<RbSelfCheck> {
   public RbSelfCheck selectSelfCheck(Date lastRunDate) {
      RbSelfCheck rbSelfCheck = new RbSelfCheck();
      rbSelfCheck.setCheckDate(lastRunDate);
      return (RbSelfCheck)super.selectOne(rbSelfCheck);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertRbSelfCheck(RbSelfCheck rbSelfCheck) {
      super.insert(rbSelfCheck);
   }

   public void updateSelfCheck(RbSelfCheck rbSelfCheck) {
      super.update(rbSelfCheck);
   }

   public Date selectLastLastRunDate(Date lastRunDate) {
      RbSelfCheck queryParam = new RbSelfCheck();
      queryParam.setCheckDate(lastRunDate);
      return (Date)this.daoSupport.selectObject(RbSelfCheck.class.getName().concat(".selectLastLastRunDate"), queryParam);
   }
}
