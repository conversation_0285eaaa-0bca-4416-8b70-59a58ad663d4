package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFinancialAcctRegisterRepository extends BusinessRepository {
   public List<RbFinancialAcctRegister> getFinRegisterByAgreementId(String agreementId, String clientNo, String acctStatus) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".getFinRegisterByAgreementId", param);
   }

   public List<RbFinancialAcctRegister> getFinRegByIdAndDate(String agreementId, String clientNo, String acctStatus, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".getFinRegByIdAndDate", param);
   }

   public List<RbFinancialAcctRegister> getFinRegByAcctAndDate(String baseAcctNo, Long parentInternalKey, String acctStatus, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("parentInternalKey", parentInternalKey);
      param.put("acctStatus", acctStatus);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".getFinRegByAcctAndDate", param);
   }

   public void updatePrevFinBalForEod(Map<String, Object> map) {
      this.daoSupport.update(RbFinancialAcctRegister.class.getName() + ".updatePrevFinBalForEod", map);
   }

   public List<RbFinancialAcctRegister> getFinRegByAcctAndDateType(String baseAcctNo, Long parentInternalKey, String acctStatus, String intType, String clientNo, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("parentInternalKey", parentInternalKey);
      param.put("acctStatus", acctStatus);
      param.put("intType", intType);
      param.put("clientNo", clientNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".getFinRegByAcctAndDateType", param);
   }

   public List<RbFinancialAcctRegister> selectByCheck(RbFinancialAcctRegister rbFinancialAcctRegister) {
      Map<String, Object> param = new HashMap();
      param.put("branch", rbFinancialAcctRegister.getTranBranch());
      param.put("ccy", rbFinancialAcctRegister.getAcctCcy());
      param.put("prodType", rbFinancialAcctRegister.getProdType());
      param.put("runDate", Context.getInstance().getRunDateParse());
      param.put("lastRunDateStr", Context.getInstance().getLastRunDate());
      param.put("lastRunDate", Context.getInstance().getLastRunDateParse());
      param.put("yesterday", Context.getInstance().getYesterdayParse());
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".selectByCheck", param);
   }
}
