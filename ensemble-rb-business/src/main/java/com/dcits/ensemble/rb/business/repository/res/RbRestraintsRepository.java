package com.dcits.ensemble.rb.business.repository.res;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.api.unit.res.IRestraintsOperate;
import com.dcits.ensemble.rb.business.api.unit.res.RestraintsOperateFactory;
import com.dcits.ensemble.rb.business.bc.component.cm.operate.util.PageQueryUtil;
import com.dcits.ensemble.rb.business.bc.unit.res.restraints.model.RestraintTypeEnum;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.res.restraints.CreatRestraintsModel;
import com.dcits.ensemble.rb.business.model.res.restraints.DeleteRestraintsModel;
import com.dcits.ensemble.rb.business.model.res.restraints.OperateRestraintsOutModel;
import com.dcits.ensemble.rb.business.model.res.restraints.ResOperateControlModel;
import com.dcits.ensemble.rb.business.model.res.restraints.RestraintsTypeEnum;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbRestraintsRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbRestraintsRepository.class);
   @Resource
   private PageQueryUtil pageQueryUtil;

   public List<RbRestraints> selectNotEndRestraints(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectNotEndRestraints", param);
   }

   public int createMbRestraints(RbRestraints mbRestraints) {
      log.debug("RestraintsHandle -----");
      int res = 0;
      if (BusiUtil.isNotNull(mbRestraints)) {
         res = super.insert(mbRestraints);
      }

      return res;
   }

   public int updateMbRestraints(RbRestraints mbRestraint) {
      int res = 0;
      if (BusiUtil.isNotNull(mbRestraint)) {
         res = super.update(mbRestraint);
      }

      return res;
   }

   public int updateOthMbRestraints(RbRestraints mbRestraint) {
      int res = 0;
      if (BusiUtil.isNotNull(mbRestraint)) {
         res = this.daoSupport.update(RbRestraints.class.getName() + ".updateByPrimaryKeyExt", mbRestraint);
      }

      return res;
   }

   public RbRestraints getMbRestraintsInfo(Long internalKey, String restraintType, String seqNo, String clientNo) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setInternalKey(internalKey);
      rbRestraints.setClientNo(clientNo);
      rbRestraints.setRestraintType(restraintType);
      rbRestraints.setResSeqNo(seqNo);
      return (RbRestraints)this.daoSupport.selectOne(rbRestraints);
   }

   public List<RbRestraints> getMbRestraintsInfoByStatus(Long internalKey, String restraintsStatus, String clientNo) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setInternalKey(internalKey);
      rbRestraints.setClientNo(clientNo);
      rbRestraints.setRestraintsStatus(restraintsStatus);
      return this.daoSupport.selectList(rbRestraints);
   }

   public RbRestraints selectMbRestraints(Long internalKey, String restraintType, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("restraintType", restraintType);
      param.put("resSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectRestraintByClientNo", param);
   }

   public RbRestraints selectMbRestraintsOld(Long internalKey, String restraintType, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("restraintType", restraintType);
      param.put("resSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectMbRestraintsOld", param);
   }

   public RbRestraints getRestraints(Long internalKey, String restraintType, String seqNo, String clientNo) {
      log.debug("MbResInquiry -----");
      RbRestraints rbRes = new RbRestraints();
      Map<String, Object> param = new HashMap();
      param.put("internalKey", new BigDecimal(internalKey));
      param.put("restraintType", restraintType);
      param.put("seqNo", new BigDecimal(seqNo));
      param.put("clientNo", clientNo);
      rbRes.setInternalKey(internalKey);
      rbRes.setRestraintType(restraintType);
      rbRes.setResSeqNo(seqNo);
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setInternalKey(internalKey);
      rbRestraints.setRestraintType(restraintType);
      rbRestraints.setResSeqNo(seqNo);
      return (RbRestraints)this.daoSupport.selectOne(rbRestraints);
   }

   public List<RbRestraints> selectRestraintsByStatus(Long internalKey, String[] resTypes, String[] restraintsStatus, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resTypes", resTypes);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintsByStatus", param);
   }

   public RbRestraints selectByPrimaryKeyForUpdate(Long internalKey, String resSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resSeqNo", resSeqNo);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectByPrimaryKeyForUpdate", param);
   }

   public RbRestraints selectByPrimaryKeyByE(Long internalKey, String resSeqNo, String restraintType, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resSeqNo", resSeqNo);
      param.put("restraintType", restraintType);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectByPrimaryKeyByE", param);
   }

   public List<RbRestraints> selectRestraintsByType(Long internalKey, String[] resTypes, String[] restraintsStatus, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resTypes", resTypes);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintsByType", param);
   }

   public List<RbRestraints> selectRestraintsByStatusButNature(Long internalKey, String[] resTypes, String[] restraintsStatus, String clientNo, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resTypes", resTypes);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintsByStatusButNature", param);
   }

   public List<RbRestraints> getRestraintsByInternalKey(Long internalKey, String[] resTypes, String[] restraintsStatus, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resTypes", resTypes);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getRestraintsByInternalKey", param);
   }

   public List<RbRestraints> getRestraintsByInternalKeyByPage(Long internalKey, String[] resTypes, String[] restraintsStatus, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resTypes", resTypes);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbRestraints.class.getName() + ".getRestraintsByInternalKeyByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbRestraints> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbRestraints> getRestraintsByInternalKeysByPage(Long[] internalKeys, String[] resTypes, String[] restraintsStatus, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKeys", internalKeys);
      param.put("resTypes", resTypes);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbRestraints.class.getName() + ".getRestraintsByInternalKeysByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbRestraints> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbRestraints> getRestraints(Long internalKey, String clientNo) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setInternalKey(internalKey);
      rbRestraints.setClientNo(clientNo);
      return this.daoSupport.selectList(rbRestraints);
   }

   public List<RbRestraints> getRestraintsByStatus(Long internalKey, String clientNo, String status) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setInternalKey(internalKey);
      rbRestraints.setClientNo(clientNo);
      rbRestraints.setRestraintsStatus(status);
      return this.daoSupport.selectList(rbRestraints);
   }

   public List<RbRestraints> getRestraintsForPage(Long internalKey, String resSeqNo, String restraintsStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("resSeqNo", resSeqNo);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      return this.pageQueryUtil.selectByPage(RbRestraints.class.getName() + ".getRestraintsForPage", param);
   }

   public List<RbRestraints> getAllAcctRestraints(Long internalKey, String subResClass, String clientNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("internalKey", internalKey);
      param.put("subResClass", subResClass);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectAcctRestraintByInternalKey", param);
   }

   public List<RbRestraints> getActiveRestraints(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraint", param);
   }

   public List<RbRestraints> getRestraintForA(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintForA", param);
   }

   public List<RbRestraints> getRestraintForAAndNotrestraintType(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getRestraintForAAndNotrestraintType", param);
   }

   public List<RbRestraints> getActiveRestraints(Long internalKey, String restraintType, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("restraintType", restraintType);
      param.put("resSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintByClientNo", param);
   }

   public List<RbRestraints> selectExpirtResForDeal() {
      String runDate = Context.getInstance().getRunDate();
      String lastRunDate = Context.getInstance().getLastRunDate();
      Map<String, Object> param = new HashMap(5);
      param.put("runDate", runDate);
      param.put("lastRunDate", lastRunDate);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectExpirtResForDeal", param);
   }

   public List<RbRestraints> getNotEndRestraints(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectNotEndRestraints", param);
   }

   public List<RbRestraints> getJudicialFreezeRestraints(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getJudicialFreezeRestraints", param);
   }

   public List<RbRestraints> getAcctNatureRes(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectNotEndRestraints", param);
   }

   public List<RbRestraints> getMbAcctNatureRes(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectNotEndNatureRestraints", param);
   }

   public RbRestraints getRestraint(String resSeqNo, String clientNo) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setResSeqNo(resSeqNo);
      rbRestraints.setClientNo(clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".getRestraint", rbRestraints);
   }

   public RbRestraints getRestraintLock(String resSeqNo, String clientNo) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setResSeqNo(resSeqNo);
      rbRestraints.setClientNo(clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".getRestraintLock", rbRestraints);
   }

   public RbRestraints getRestraintE(String resSeqNo, String clientNo) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setResSeqNo(resSeqNo);
      rbRestraints.setClientNo(clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".getRestraintE", rbRestraints);
   }

   public RbRestraints selectRestraint(Long internalKey, String restraintType, String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("restraintType", restraintType);
      param.put("resSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectRestraint", param);
   }

   public List<RbRestraints> getMbRestraints(String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getRbRestraints", param);
   }

   public List<RbRestraints> selectHighPriAmt(String resSeqNo, String[] resType, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("resSeqNo", resSeqNo);
      param.put("resType", resType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectHighPriAmtBySeqNo", param);
   }

   public List<RbRestraints> selectRestraintList(String seqNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("resSeqNo", seqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintList", param);
   }

   public List<RbRestraints> selectBefSeqAmt(String resSeqNo, String[] resType, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("resSeqNo", resSeqNo);
      param.put("resType", resType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectBefSeqAmtBySeqNo", param);
   }

   public List<RbRestraints> selectBefSeqAmtBySeqNoA(String resSeqNo, String[] resType, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("resSeqNo", resSeqNo);
      param.put("resType", resType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectBefSeqAmtBySeqNoA", param);
   }

   public RbRestraints queryRestraintsByResSeqNo(String resSeqNo, String resType, String restraintsStatus, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("resSeqNo", resSeqNo);
      map.put("restraintType", resType);
      map.put("restraintsStatus", restraintsStatus);
      map.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".queryRestraintsByResSeqNo", map);
   }

   public List<RbRestraints> queryRestraintsByInternalKey(Long internalKey, String resType, String restraintsStatus, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("internalKey", internalKey);
      map.put("restraintType", resType);
      map.put("restraintsStatus", restraintsStatus);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".queryRestraintsByInternalKey", map);
   }

   public List<RbRestraints> queryRestraintsByInternalKeyForPage(Long internalKey, String resType, String restraintsStatus, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("internalKey", internalKey);
      map.put("restraintType", resType);
      map.put("restraintsStatus", restraintsStatus);
      map.put("clientNo", clientNo);
      return this.pageQueryUtil.selectByPage(RbRestraints.class.getName() + ".queryRestraintsByInternalKey", map);
   }

   public RbRestraints getMbRestraintsByThreeParam(Long internalKey, String restraintType, String restraintsStatus, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("internalKey", internalKey);
      map.put("restraintType", restraintType);
      map.put("restraintsStatus", restraintsStatus);
      map.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".getRbRestraintsByThreeParam", map);
   }

   public RbRestraints getRestraintAll(String resSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("resSeqNo", resSeqNo);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".getRestraintAll", param);
   }

   public List<RbRestraints> selectManualNotEndRestraints(Long internalKey, String[] resTypes, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("resTypes", resTypes);
      param.put("clientNo", clientNo);
      List<RbRestraints> restraintsModelList = this.daoSupport.selectList(RbRestraints.class.getName() + ".selectManualNotEndRestraints", param);
      if (BusiUtil.isNotNull(restraintsModelList)) {
         restraintsModelList.sort(Comparator.comparing(RbRestraints::getStartDate).thenComparing(RbRestraints::getResSeqNo));
      }

      return restraintsModelList;
   }

   public List<RbRestraints> selectRestraintsOrderPriority(Long internalKey, String[] resTypes, String clientNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("resTypes", resTypes);
      param.put("clientNo", clientNo);
      List<RbRestraints> restraintsModelList = this.daoSupport.selectList(RbRestraints.class.getName() + ".selectManualNotEndRestraints", param);
      return restraintsModelList;
   }

   public List<RbRestraints> selectRestraintsAll(Map<String, Object> map) {
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".queryRestraintsByInternalKey", map);
   }

   public List<RbRestraints> querySFRestraintsByBranchInfo(String tranBranch, String[] resTypes, String[] restraintsStatus, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("tranBranch", tranBranch);
      param.put("restraintType", resTypes);
      param.put("restraintsStatus", restraintsStatus);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".querySFRestraintsByBranch", param);
   }

   public List<RbRestraints> selectSfRestraintsStart(String[] resType, Date startDate) {
      Map<String, Object> param = new HashMap(5);
      param.put("resType", resType);
      param.put("startDate", startDate);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectSfRestraintsStart", param);
   }

   public List<RbRestraints> selectSfRestraintsStart(String[] resType) {
      Map<String, Object> param = new HashMap(5);
      param.put("resType", resType);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectSfRestraintsStart", param);
   }

   public List<RbRestraints> selectSfRestraintsStartAndInternalKey(String[] resType, Long internalKey) {
      Map<String, Object> param = new HashMap(5);
      param.put("resType", resType);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectSfRestraintsStartAndInternalKey", param);
   }

   public List<RbRestraints> selectSfRestraintsEnd(String[] resType, Date endDate) {
      Map<String, Object> param = new HashMap(5);
      param.put("resType", resType);
      param.put("endDate", endDate);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectSfRestraintsEnd", param);
   }

   public List<RbRestraints> selectRestraintsByReference(String reference) {
      Map<String, Object> param = new HashMap(5);
      param.put("reference", reference);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintsByReference", param);
   }

   public List<RbRestraints> getNumRestraints(String date, String channelType) {
      Map<String, Object> param = new HashMap(5);
      param.put("channelDate", DateUtil.parseDate(date));
      param.put("programId", channelType);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getNumRestraints", param);
   }

   public RbRestraints selectRestraintsByChannelSeqNo(String channelSeqNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("channelSeqNo", channelSeqNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectNotEndResByChannelSeqNo", param);
   }

   public RbRestraints selectNotEndResJustByChannelSeqNo(String channelSeqNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("channelSeqNo", channelSeqNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectNotEndResJustByChannelSeqNo", param);
   }

   public List<RbRestraints> selectBabRestraints(String internalKey, List<String> resSeqNoList, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      String[] resSeqNo = null;
      if (BusiUtil.isNotNull(resSeqNoList) && resSeqNoList.size() > 0) {
         resSeqNo = new String[resSeqNoList.size()];

         for(int i = 0; i < resSeqNoList.size(); ++i) {
            resSeqNo[i] = (String)resSeqNoList.get(i);
         }
      }

      param.put("resSeqNo", resSeqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectBabRestraints", param);
   }

   public List<RbRestraints> getAllAcctRestraints(Map<String, Object> map) {
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectAcctRestraintByKey", map);
   }

   public List<RbRestraints> getAllAcctRestraints1(Map<String, Object> map) {
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectAcctRestraintByKey1", map);
   }

   public List<RbRestraints> getResByChannelSeqNo(String channelSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("channelSeqNo", channelSeqNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getResByChannelSeqNo", param);
   }

   public int updateMbRestraintsRealRestraintAmt(Long internalKey, String restraintType, String resSeqNo, String clientNo, BigDecimal realRestraintAmt) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("restraintType", restraintType);
      param.put("resSeqNo", resSeqNo);
      param.put("clientNo", clientNo);
      param.put("realRestraintAmt", realRestraintAmt);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      return BusiUtil.isNotNull(realRestraintAmt) ? this.daoSupport.update(RbRestraints.class.getName() + ".updateMbRestraintsRealRestraintAmt", param) : 0;
   }

   public void updResStraintsStatusByInternalKey(RbRestraints rbRestraints) {
      if (BusiUtil.isNotNull(rbRestraints)) {
         this.daoSupport.update(RbRestraints.class.getName() + ".updResStraintsStatusByInternalKey", rbRestraints);
      }

   }

   public RbRestraints getSeqInfoByMainSeq(Long internalKey, String stlSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("stlSeqNo", stlSeqNo);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".getSeqInfoByMainSeq", param);
   }

   public int getAhBuAcctCountByClient(String clientNo, List<String> ahBuList) {
      Map<String, Object> param = new HashMap(5);
      param.put("ahBuList", ahBuList);
      param.put("clientNo", clientNo);
      return (Integer)this.daoSupport.selectObject(RbRestraints.class.getName() + ".getAhBuAcctCountByClient", param);
   }

   public int getRestraintsCountByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (Integer)this.daoSupport.selectObject(RbRestraints.class.getName() + ".getRestraintsCountByInternalKey", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public String createRestraints(BigDecimal pledgedAmt, RbAcctStandardModel rbAcctStandardModel) {
      String restraintType = RestraintTypeEnum.PART_STOP_PAY.getCode();
      CreatRestraintsModel creatRestraintsModel = new CreatRestraintsModel(rbAcctStandardModel, restraintType);
      creatRestraintsModel.setStartDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      creatRestraintsModel.setEndDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      creatRestraintsModel.setPledgedAmt(pledgedAmt);
      creatRestraintsModel.setResSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.restraintsSeqNo));
      IRestraintsOperate restraintsBusiness = RestraintsOperateFactory.getRestraintsBusiness(RestraintsTypeEnum.ACCT);
      OperateRestraintsOutModel operateRestraintsOutModel = restraintsBusiness.create(creatRestraintsModel);
      String resSeqNo = operateRestraintsOutModel.getResSeqNo();
      return resSeqNo;
   }

   public List<RbRestraints> getRestraintsA(Long internalKey, String clientNo) {
      RbRestraints rbRestraints = new RbRestraints();
      rbRestraints.setInternalKey(internalKey);
      rbRestraints.setClientNo(clientNo);
      rbRestraints.setRestraintsStatus("A");
      return this.daoSupport.selectList(rbRestraints);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void delRestraints(String resSeqNo, BigDecimal pledgedAmt) {
      IRestraintsOperate restraintsBusiness = RestraintsOperateFactory.getRestraintsBusiness(RestraintsTypeEnum.ACCT);
      DeleteRestraintsModel deleteRestraintsModel = new DeleteRestraintsModel(resSeqNo);
      deleteRestraintsModel.setResSeqNo((String)BusiUtil.nvl(resSeqNo, ""));
      deleteRestraintsModel.setPledgedAmt(pledgedAmt);
      ResOperateControlModel resOperateControlModel = new ResOperateControlModel("DELETE");
      restraintsBusiness.delete(deleteRestraintsModel, resOperateControlModel);
   }

   public List<RbRestraints> getRestraintsByYc(Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("runDate", Context.getInstance().getRunDateParse());
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getRestraintsByYc", param);
   }

   public List<RbRestraints> getRestraintsByDoc(String documentId, String documentType) {
      Map<String, Object> param = new HashMap();
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getRestraintsByDoc", param);
   }

   public List<RbRestraints> getRestraintsNotE(Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("runDate", Context.getInstance().getRunDateParse());
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".getRestraintsNotE", param);
   }

   public List<RbRestraints> selectRestraintListByStlSeqNo(String resSeqNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("stlSeqNo", resSeqNo);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintListByStlSeqNo", param);
   }

   public List<RbRestraints> selectYCByAllAcct(Map<String, Object> param) {
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectYCByAllAcct", param);
   }

   public List<RbRestraints> selectRestraintsByReferenceClientNo(Long internalKey, String reference, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("reference", reference);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintsByReferenceClientNo", param);
   }

   public RbRestraints selectRestraintsByApplicationId(Long internalKey, String businessApplicationId, String restraintType, String restraintsStatus, String clientNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("businessApplicationId", businessApplicationId);
      param.put("restraintType", restraintType);
      param.put("restraintsStatus", restraintsStatus);
      param.put("clientNo", clientNo);
      return (RbRestraints)this.daoSupport.selectOne(RbRestraints.class.getName() + ".selectRestraintsByApplicationId", param);
   }

   public RbRestraints getTotalPldAmount(Long internalKey, String restraintType, String restraintsStatus) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("restraintType", restraintType);
      param.put("restraintsStatus", restraintsStatus);
      return (RbRestraints)this.daoSupport.selectObject(RbRestraints.class.getName() + ".getTotalPldAmount", param);
   }

   public void updateResNarrativeByResSeqNo(Long internalKey, String resSeqNo, String clientNo, String narrative, String narrative1, String company) {
      Map<String, Object> param = new HashMap(5);
      param.put("internalKey", internalKey);
      param.put("resSeqNo", resSeqNo);
      param.put("clientNo", clientNo);
      param.put("narrative", narrative);
      param.put("narrative1", narrative1);
      param.put("company", company);
      this.daoSupport.update(RbRestraints.class.getName() + ".updateResNarrativeByResSeqNo", param);
   }

   public List<RbRestraints> selectRestraintsAhInfo(Map<String, Object> param) {
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectRestraintsAhInfo", param);
   }

   public List<RbRestraints> getJudicialFreeze(RbAcctStandardModel rbAcct) {
      Map<String, Object> map = new HashMap();
      map.put("internalKey", rbAcct.getInternalKey());
      map.put("restraintType", RestraintTypeEnum.JUDI_RESTRAIN_TYPE.getCode());
      map.put("status", "A");
      map.put("clientNo", rbAcct.getClientNo());
      List<RbRestraints> mbRestraints = this.selectRestraintsAll(map);
      return mbRestraints;
   }

   public List<RbRestraints> getNotEndRestraints(Long internalKey, String clientNo, String[] resTypes) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("resTypes", resTypes);
      return this.daoSupport.selectList(RbRestraints.class.getName() + ".selectNoResRestraints", param);
   }

   public void updateByWaitSeq(RbRestraints rbRestraints) {
      if (BusiUtil.isNotNull(rbRestraints)) {
         super.updateByCondition(".updateByWaitSeq", rbRestraints);
      }

   }
}
