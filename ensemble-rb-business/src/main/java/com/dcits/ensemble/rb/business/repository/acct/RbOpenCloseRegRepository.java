package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.bc.component.cm.common.MbAcctInfoServiceImpl;
import com.dcits.ensemble.rb.business.bc.component.cm.operate.util.PageQueryUtil;
import com.dcits.ensemble.rb.business.bc.unit.card.business.IsCardBusiness;
import com.dcits.ensemble.rb.business.bc.unit.cm.sequences.MbOpenCloseRegSeq;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseReg;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseRegHist;
import com.dcits.ensemble.rb.business.model.acct.MbOpenCloseModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbOpenCloseRegRepository extends BusinessRepository<RbOpenCloseReg> {
   private static final Logger log = LoggerFactory.getLogger(RbOpenCloseRegRepository.class);
   public static final String OPEN_ACCT = "1";
   public static final String CLOSE_ACCT = "2";
   public static final String PRE_OPEN_ACCT = "3";
   @Resource
   private RbOpenCloseRegHistRepository rbOpenCloseRegHistRepository;
   @Resource
   private MbAcctInfoServiceImpl mbAcctInfoService;
   @Resource
   private IsCardBusiness isCardBusiness;
   @Resource
   private FmBaseStor fmBaseStor;
   @Resource
   private PageQueryUtil pageQueryUtil;

   public RbOpenCloseReg createMbOpenCloseReg(MbOpenCloseModel mbOpenCloseModel) {
      RbOpenCloseReg mbOpenCloseReg = new RbOpenCloseReg();
      mbOpenCloseReg.setTranDate(mbOpenCloseModel.getRunDate());
      mbOpenCloseReg.setUserId(mbOpenCloseModel.getOfficerId());
      mbOpenCloseReg.setInternalKey(mbOpenCloseModel.getInternalKey());
      mbOpenCloseReg.setProdType(mbOpenCloseModel.getProdType());
      mbOpenCloseReg.setAcctCcy(mbOpenCloseModel.getCcy());
      mbOpenCloseReg.setClientNo(mbOpenCloseModel.getClientNo());
      mbOpenCloseReg.setAcctSeqNo(mbOpenCloseModel.getAcctSeqNo());
      BeanUtil.copy(mbOpenCloseModel, mbOpenCloseReg);
      return mbOpenCloseReg;
   }

   public void createCdCardArchDb(RbOpenCloseReg cdCardArch) {
      super.insert(cdCardArch);
      RbOpenCloseRegHist rbOpenCloseRegHist = new RbOpenCloseRegHist();
      BeanUtils.copyProperties(cdCardArch, rbOpenCloseRegHist);
      this.rbOpenCloseRegHistRepository.insert(rbOpenCloseRegHist);
   }

   public void insertOne(RbOpenCloseReg rbOpenCloseReg) {
      this.daoSupport.insert(rbOpenCloseReg);
   }

   public void createMbOpenCloseRegDb(List<RbOpenCloseReg> mbOpenCloseReges) {
      if (BusiUtil.isNotNull(mbOpenCloseReges)) {
         for(int i = 0; i < mbOpenCloseReges.size(); ++i) {
            new MbOpenCloseRegSeq();
            ((RbOpenCloseReg)mbOpenCloseReges.get(i)).setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.openCloseRegSeqNo));
            super.insert(mbOpenCloseReges.get(i));
            RbOpenCloseRegHist rbOpenCloseRegHist = new RbOpenCloseRegHist();
            BeanUtils.copyProperties(mbOpenCloseReges.get(i), rbOpenCloseRegHist);
            this.rbOpenCloseRegHistRepository.insert(rbOpenCloseRegHist);
         }
      }

   }

   public void createMbOpenCloseRegDb(RbOpenCloseReg mbOpenCloseRege) {
      if (BusiUtil.isNotNull(mbOpenCloseRege)) {
         new MbOpenCloseRegSeq();
         mbOpenCloseRege.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.openCloseRegSeqNo));
         super.insert(mbOpenCloseRege);
         RbOpenCloseRegHist rbOpenCloseRegHist = new RbOpenCloseRegHist();
         BeanUtils.copyProperties(mbOpenCloseRege, rbOpenCloseRegHist);
         this.rbOpenCloseRegHistRepository.insert(rbOpenCloseRegHist);
      }

   }

   public boolean selectForReversal(String baseAcctNo, String acctSeqNo, String ccy, String reference, String orgType) {
      boolean res = false;
      RbOpenCloseReg mbOpenCloseReg = new RbOpenCloseReg();
      if (this.isCardBusiness.isCardByCardBin(baseAcctNo)) {
         mbOpenCloseReg.setCardNo(baseAcctNo);
      } else {
         mbOpenCloseReg.setBaseAcctNo(baseAcctNo);
      }

      mbOpenCloseReg.setReference(reference);
      mbOpenCloseReg.setAcctSeqNo(acctSeqNo);
      mbOpenCloseReg.setAcctCcy(ccy);
      mbOpenCloseReg.setRegType(orgType);
      RbOpenCloseReg rbOpenCloseReg = (RbOpenCloseReg)this.daoSupport.selectOne(mbOpenCloseReg);
      if (BusiUtil.isNotNull(rbOpenCloseReg) && BusiUtil.isNotNull(rbOpenCloseReg.getRegType())) {
         res = true;
      }

      return res;
   }

   public List<RbOpenCloseReg> selectReversalInfo(String acctSeqNo, String reference, String clientNo) {
      RbOpenCloseReg rbOpenCloseReg = new RbOpenCloseReg();
      rbOpenCloseReg.setReference(reference);
      rbOpenCloseReg.setAcctSeqNo(acctSeqNo);
      rbOpenCloseReg.setClientNo(clientNo);
      return this.daoSupport.selectList(rbOpenCloseReg);
   }

   public void updateByReferenceAndInternalKey(RbOpenCloseReg rbOpenCloseReg) {
      this.daoSupport.update(RbOpenCloseReg.class.getName() + ".updateByReferenceAndInternalKey", rbOpenCloseReg);
   }

   public List<RbOpenCloseReg> getOpenCloseAcctRegList(String isIndividual, String baseAcctNo, String acctType, String prodType, String ccy, String regType, String startDate, String endDate, String branch) {
      Map param = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("branch", branch);
      param.put("regType", regType);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      if (BusiUtil.isNotNull(startDate) && BusiUtil.isNotNull(endDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      return this.pageQueryUtil.selectByPage(RbOpenCloseReg.class.getName() + ".getOpenCloseAcctListByGLSQL", param);
   }

   public List<RbOpenCloseReg> getPreOpenCloseAcctRegList(String isIndividual, String baseAcctNo, String acctType, String prodType, String ccy, String regType, String startDate, String endDate, String branch) {
      Map param = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("branch", branch);
      param.put("acctStatus", "I");
      param.put("regType", "1");
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("ccy", ccy);
      param.put("prodType", prodType);
      if (BusiUtil.isNotNull(startDate) && BusiUtil.isNotNull(endDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbOpenCloseReg.class.getName() + ".getPreOpenCloseAcctListSQL";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbOpenCloseReg> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         List<RbOpenCloseReg> rbOpenCloseRegList = this.daoSupport.selectList(statementPostfix, param);
         return rbOpenCloseRegList;
      }
   }

   public List<RbOpenCloseReg> getOpenCloseAcctRegListByPage(String isIndividual, String baseAcctNo, String acctType, String prodType, String ccy, String regType, String startDate, String endDate, String branch) {
      Map param = new HashMap();
      param.put("branch", branch);
      param.put("regType", regType);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      if (BusiUtil.isNotNull(startDate) && BusiUtil.isNotNull(endDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbOpenCloseReg.class.getName() + ".getOpenCloseAcctListSQL";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbOpenCloseReg> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbOpenCloseReg> getPreOpenCloseAcctRegListByPage(String isIndividual, String baseAcctNo, String acctType, String prodType, String ccy, String regType, String startDate, String endDate, String branch) {
      Map param = new HashMap();
      param.put("branch", branch);
      param.put("acctStatus", "I");
      param.put("regType", "1");
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("ccy", ccy);
      param.put("prodType", prodType);
      if (BusiUtil.isNotNull(startDate) && BusiUtil.isNotNull(endDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbOpenCloseReg.class.getName() + ".getPreOpenCloseAcctListSQL";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbOpenCloseReg> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbOpenCloseReg> getOpenCloseAcctRegTwo(Long internalKey, String clientNo, String regType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("regType", regType);
      return this.daoSupport.selectList(RbOpenCloseReg.class.getName() + ".getOpenCloseAcctRegTwo", param);
   }

   public RbOpenCloseReg getOpenCloseAcctReg(Long internalKey, String clientNo, String regType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("regType", regType);
      return (RbOpenCloseReg)this.daoSupport.selectOne(RbOpenCloseReg.class.getName() + ".getOpenCloseAcctReg", param);
   }

   public RbOpenCloseReg getOpenCloseAcctRegByAcct(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return (RbOpenCloseReg)this.daoSupport.selectOne(RbOpenCloseReg.class.getName() + ".getOpenCloseAcctRegByAcct", param);
   }

   public void updateBaseAcctNoForChangeVoucher(Long internalKey, Date tranDate, String reference, String cardNo, String clientNo, String userId, String tranTimestamp, String baseAcctNo, String prodType, String acctSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("tranDate", tranDate);
      param.put("reference", reference);
      param.put("cardNo", cardNo);
      param.put("userId", userId);
      param.put("tranTimestamp", tranTimestamp);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctSeqNo", acctSeqNo);
      this.daoSupport.update(RbOpenCloseReg.class.getName() + ".updateBaseAcctNoForChangeVoucher", param);
   }

   public void updateRegBySeq1(RbOpenCloseReg rbOpenCloseReg) {
      new HashMap();
      this.daoSupport.update(RbOpenCloseReg.class.getName() + ".updateRegBySeq1", rbOpenCloseReg);
   }

   public void updateCardForChangeVoucher(Long internalKey, Date tranDate, String branch, String reference, String narrative, String cardNo, String clientNo, String userId, String tranTimestamp, String newProdType, String baseAcctNo, String prodType, String acctSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("tranDate", tranDate);
      param.put("reference", reference);
      param.put("branch", branch);
      param.put("cardNo", cardNo);
      param.put("userId", userId);
      param.put("tranTimestamp", tranTimestamp);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("newProdType", newProdType);
      param.put("acctSeqNo", acctSeqNo);
      param.put("narrative", narrative);
      this.daoSupport.update(RbOpenCloseReg.class.getName() + ".updateCardForChangeVoucher", param);
   }

   public void updateActiveDateByInternalKey(Long internalKey, Date activeDate, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("activeDate", activeDate);
      param.put("tranTimestamp", activeDate);
      this.daoSupport.update(RbOpenCloseReg.class.getName() + ".updateActiveDateByInternalKey", param);
   }

   public void updateAcctStatusByInternalKey(RbOpenCloseReg rbOpenCloseReg) {
      rbOpenCloseReg.setTranTimestamp(BusiUtil.getTranTimestamp26());
      RbOpenCloseRegHist rbOpenCloseRegHist = new RbOpenCloseRegHist();
      BeanUtils.copyProperties(rbOpenCloseReg, rbOpenCloseRegHist);
      this.daoSupport.update(RbOpenCloseReg.class.getName() + ".updateAcctStatusByInternalKey", rbOpenCloseReg);
      this.rbOpenCloseRegHistRepository.insert(rbOpenCloseRegHist);
   }

   public List<RbOpenCloseReg> getCloseAcctRegList(Date tranDate) {
      Map<String, Object> param = new HashMap();
      param.put("tranDate", tranDate);
      return this.daoSupport.selectList(RbOpenCloseReg.class.getName() + ".getCloseAcctRegList", param);
   }

   public List<RbOpenCloseReg> getAcctOpenCloseSendBankList(Date tranDate) {
      Map<String, Object> param = new HashMap();
      param.put("tranDate", tranDate);
      return this.daoSupport.selectList(RbOpenCloseReg.class.getName() + ".getAcctOpenCloseSendBankList", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public List<RbOpenCloseReg> updateAcctOpenCloseSendBankList(RbOpenCloseReg rbOpenCloseReg) {
      Map<String, Object> param = new HashMap();
      param.put("isInformBank", "Y");
      param.put("seqNo", rbOpenCloseReg.getSeqNo());
      param.put("clientNo", rbOpenCloseReg.getClientNo());
      return this.daoSupport.selectList(RbOpenCloseReg.class.getName() + ".updateAcctOpenCloseSendBankList", param);
   }

   public List<RbOpenCloseReg> getCloseAcctRegList(String isIndividual, String baseAcctNo, String acctType, String prodType, String ccy, String regType, String startDate, String endDate, String openBranch) {
      Map param = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("openBranch", openBranch);
      param.put("regType", regType);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      if (BusiUtil.isNotNull(startDate) && BusiUtil.isNotNull(endDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      return this.pageQueryUtil.selectByPage(RbOpenCloseReg.class.getName() + ".getCloseAcctListSQL", param);
   }

   public void deleteOne(RbOpenCloseReg rbOpenCloseReg) {
      this.daoSupport.delete(rbOpenCloseReg);
      List<RbOpenCloseReg> openCloseAcctRegList = this.getOpenCloseAcctRegList(rbOpenCloseReg.getInternalKey(), rbOpenCloseReg.getClientNo(), "1");
      List<RbOpenCloseReg> list = (List)openCloseAcctRegList.stream().sorted(new Comparator<RbOpenCloseReg>() {
         public int compare(RbOpenCloseReg o1, RbOpenCloseReg o2) {
            return o2.getTranDate().compareTo(o1.getTranDate());
         }
      }).collect(Collectors.toList());
      RbOpenCloseRegHist rbOpenCloseRegHist = new RbOpenCloseRegHist();
      BeanUtils.copyProperties(list.get(0), rbOpenCloseRegHist);
      this.rbOpenCloseRegHistRepository.insert(rbOpenCloseRegHist);
   }

   public List<RbOpenCloseReg> getCloseAcctRegListByPage(String isIndividual, String baseAcctNo, String acctType, String prodType, String ccy, String regType, String startDate, String endDate, String openBranch) {
      Map param = new HashMap();
      param.put("openBranch", openBranch);
      param.put("regType", regType);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      if (BusiUtil.isNotNull(startDate) && BusiUtil.isNotNull(endDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbOpenCloseReg.class.getName() + ".getCloseAcctListSQL";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbOpenCloseReg> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbOpenCloseReg> getCloseDateReg(Long internalKey, String clientNo, String regType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("regType", regType);
      return this.daoSupport.selectList(RbOpenCloseReg.class.getName() + ".getCloseDateReg", param);
   }

   public List<RbOpenCloseReg> getRbOpenCloseRegList(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbOpenCloseReg.class.getName() + ".getRbOpenCloseRegList", param);
   }

   public List<RbOpenCloseReg> getOpenCloseAcctRegList(Long internalKey, String clientNo, String regType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("regType", regType);
      return this.daoSupport.selectList(RbOpenCloseReg.class.getName() + ".getOpenCloseAcctRegList", param);
   }

   public int insert(RbOpenCloseReg rbOpenCloseReg) {
      RbOpenCloseRegHist rbOpenCloseRegHist = new RbOpenCloseRegHist();
      BeanUtils.copyProperties(rbOpenCloseReg, rbOpenCloseRegHist);
      int a = super.insert(rbOpenCloseReg);
      this.rbOpenCloseRegHistRepository.insert(rbOpenCloseRegHist);
      return a;
   }

   public List<RbOpenCloseReg> selectByStatus(String reference, String acctStatus) {
      RbOpenCloseReg rbOpenCloseReg = new RbOpenCloseReg();
      rbOpenCloseReg.setReference(reference);
      rbOpenCloseReg.setAcctStatus(acctStatus);
      return this.daoSupport.selectList(rbOpenCloseReg);
   }

   public List<RbOpenCloseReg> getRegByRefNo(String reference, String clientNo) {
      RbOpenCloseReg rbOpenCloseReg = new RbOpenCloseReg();
      rbOpenCloseReg.setReference(reference);
      rbOpenCloseReg.setClientNo(clientNo);
      return this.daoSupport.selectList(rbOpenCloseReg);
   }

   public void updateRegBySeq(RbOpenCloseReg rbOpenCloseReg) {
      this.daoSupport.update(rbOpenCloseReg);
   }

   public List<RbOpenCloseReg> getOpenCloseAcctRegListRb(String isIndividual, String baseAcctNo, String acctType, String prodType, String ccy, String regType, String startDate, String endDate, String branch) {
      Map param = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("branch", branch);
      param.put("regType", regType);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctType", acctType);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      if (BusiUtil.isNotNull(startDate) && BusiUtil.isNotNull(endDate)) {
         param.put("startDate", DateUtil.parseDate(startDate));
         param.put("endDate", DateUtil.parseDate(endDate));
      }

      return this.pageQueryUtil.selectByPage(RbOpenCloseReg.class.getName() + ".getOpenCloseAcctListSQL", param);
   }
}
