package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctProdBalanceHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAcctProdBalanceHistRepository extends BusinessRepository<RbAcctProdBalanceHist> {
   private static final Logger log = LoggerFactory.getLogger(RbAcctProdBalanceHistRepository.class);

   public void deleteByDate(Date tranDate) {
      RbAcctProdBalanceHist param = new RbAcctProdBalanceHist();
      param.setTranDate(tranDate);
      super.delete(param);
   }
}
