package com.dcits.ensemble.rb.business.repository.interest.intrateform;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIntRateFormMsg;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbIntRateFormMsgRepository extends BusinessRepository {
   public RbIntRateFormMsg queryByIntRateFormNo(String intRateFormNo, String clientNo) {
      RbIntRateFormMsg formMsg = new RbIntRateFormMsg();
      formMsg.setIntRateFormNo(intRateFormNo);
      formMsg.setClientNo(clientNo);
      return (RbIntRateFormMsg)this.daoSupport.selectOne(RbIntRateFormMsg.class.getName() + ".queryByIntRateFormNo", formMsg);
   }

   public RbIntRateFormMsg queryByIntRateFormNoForAll(String intRateFormNo, String clientNo) {
      return this.queryByIntRateFormNoForAll(intRateFormNo, clientNo, (String)null);
   }

   public RbIntRateFormMsg queryByIntRateFormNoForAll(String intRateFormNo, String clientNo, String baseAcctNo) {
      RbIntRateFormMsg formMsg = new RbIntRateFormMsg();
      formMsg.setIntRateFormNo(intRateFormNo);
      formMsg.setClientNo(clientNo);
      formMsg.setBaseAcctNo(baseAcctNo);
      return (RbIntRateFormMsg)this.daoSupport.selectOne(RbIntRateFormMsg.class.getName() + ".queryByIntRateFormNoForAll", formMsg);
   }

   public List<RbIntRateFormMsg> queryByIntRateFormNoForAllByPage(String intRateFormNo, String clientNo, String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("intRateFormNo", intRateFormNo);
      param.put("clientNo", clientNo);
      param.put("baseAcctNo", baseAcctNo);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbIntRateFormMsg.class.getName() + ".queryByIntRateFormNoForAllByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbIntRateFormMsg> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbIntRateFormMsg> queryByBaseAcctNoAndStatusNoIsN(String baseAcctNo, String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbIntRateFormMsg.class.getName() + ".queryByBaseAcctNoAndStatusNoIsN", map);
   }

   public List<RbIntRateFormMsg> queryByBaseAcctNoAndStatusNoIsN(String baseAcctNo, String clientNo, long internalKey) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      map.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbIntRateFormMsg.class.getName() + ".queryByBaseAcctNoAndStatusNoIsN", map);
   }

   public List<RbIntRateFormMsg> queryByBaseAcctAndAcctSeqNoNoAndStatusNoIsN(String baseAcctNo, String clientNo, String acctSeqNo) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      map.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbIntRateFormMsg.class.getName() + ".queryByBaseAcctNoAndStatusNoIsN", map);
   }

   public List<RbIntRateFormMsg> queryByBaseAcctNoAndStatusByTypeNoIsN(String baseAcctNo, String clientNo, String intRateRbProdType) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      map.put("intRateRbProdType", intRateRbProdType);
      return this.daoSupport.selectList(RbIntRateFormMsg.class.getName() + ".queryByBaseAcctNoAndStatusNoIsN", map);
   }

   public List<RbIntRateFormMsg> queryByBaseAcctNoForAll(String baseAcctNo, String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbIntRateFormMsg.class.getName() + ".queryByBaseAcctNoForAll", map);
   }

   public List<RbIntRateFormMsg> queryByBaseAcctCcyNotClose(String baseAcctNo, String acctSeqNo, String clientNo, String ccy, String intAgreementStatus) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("acctSeqNo", acctSeqNo);
      map.put("clientNo", clientNo);
      map.put("ccy", ccy);
      map.put("intAgreementStatus", intAgreementStatus);
      return this.daoSupport.selectList(RbIntRateFormMsg.class.getName() + ".queryByBaseAcctNotClose", map);
   }

   public List<RbIntRateFormMsg> queryByBaseAcctCcyAndStatus(String baseAcctNo, String clientNo, String ccy, String status) {
      RbIntRateFormMsg formMsg = new RbIntRateFormMsg();
      formMsg.setBaseAcctNo(baseAcctNo);
      formMsg.setClientNo(clientNo);
      formMsg.setCcy(ccy);
      formMsg.setIntAgreementStatus(status);
      return this.daoSupport.selectList(formMsg);
   }

   public void updateStatus(RbIntRateFormMsg rbIntRateFormMsg, int num) {
      if (num == 1) {
         rbIntRateFormMsg.setIntAgreementStatus("P");
         rbIntRateFormMsg.setReason("success！");
      }

      if (num == 2) {
         rbIntRateFormMsg.setIntAgreementStatus("N");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0200"));
      }

      if (num == 3) {
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0201"));
      }

      if (num == 4) {
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0202"));
      }

      if (num == 5) {
         rbIntRateFormMsg.setIntAgreementStatus("N");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0203"));
      }

      if (num == 6) {
         rbIntRateFormMsg.setIntAgreementStatus("Y");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0204"));
      }

      if (num == 7) {
         rbIntRateFormMsg.setIntAgreementStatus("R");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0205"));
      }

      if (num == 8) {
         rbIntRateFormMsg.setIntAgreementStatus("N");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0200"));
      }

      if (num == 9) {
         rbIntRateFormMsg.setIntAgreementStatus("R");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0203"));
      }

      if (num == 10) {
         rbIntRateFormMsg.setIntAgreementStatus("Y");
      }

      if (num == 11) {
         rbIntRateFormMsg.setIntAgreementStatus("R");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0206"));
      }

      if (num == 12) {
         rbIntRateFormMsg.setIntAgreementStatus("N");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0206"));
      }

      if (num == 13) {
         rbIntRateFormMsg.setIntAgreementStatus("T");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0207"));
      }

      if (num == 14) {
         rbIntRateFormMsg.setIntAgreementStatus("F");
         rbIntRateFormMsg.setReason(BusiUtil.getMessageByKey("MG0208"));
      }

      rbIntRateFormMsg.setLastChangeDate(Context.getInstance().getRunDateParse());
      this.updateForAll(rbIntRateFormMsg);
   }

   public void updateForAll(RbIntRateFormMsg rbIntRateFormMsg) {
      this.daoSupport.update(RbIntRateFormMsg.class.getName() + ".updateForAll", rbIntRateFormMsg);
   }

   public void updateStatusByInfo(RbIntRateFormMsg rbIntRateFormMsg) {
      this.daoSupport.update(RbIntRateFormMsg.class.getName() + ".updateStatusByInfo", rbIntRateFormMsg);
   }

   public void updateForByIntRateFormNo(RbIntRateFormMsg rbIntRateFormMsg) {
      this.daoSupport.update(RbIntRateFormMsg.class.getName() + ".updateForByIntRateFormNo", rbIntRateFormMsg);
   }
}
