package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbCashCtlSign;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbCashCtlSignRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbCashCtlSignRepository.class);

   public RbCashCtlSign getRbCashCtlSignActive(String baseAcctNo) {
      RbCashCtlSign rbCashCtlSign = new RbCashCtlSign();
      rbCashCtlSign.setBaseAcctNo(baseAcctNo);
      rbCashCtlSign.setStatus("A");
      return (RbCashCtlSign)this.daoSupport.selectOne(rbCashCtlSign);
   }

   public RbCashCtlSign getRbCashCtlSignForDel(String baseAcctNo, String subBaseAcctNo) {
      RbCashCtlSign rbCashCtlSign = new RbCashCtlSign();
      rbCashCtlSign.setBaseAcctNo(baseAcctNo);
      rbCashCtlSign.setSubBaseAcctNo(subBaseAcctNo);
      rbCashCtlSign.setStatus("A");
      return (RbCashCtlSign)this.daoSupport.selectOne(rbCashCtlSign);
   }

   public void updateSubBaseAcctNo(String baseAcctNo, String subBaseAcctNo) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("subBaseAcctNo", subBaseAcctNo);
      map.put("status", "A");
      this.daoSupport.update("updateSubBaseAcctNo", map);
   }
}
