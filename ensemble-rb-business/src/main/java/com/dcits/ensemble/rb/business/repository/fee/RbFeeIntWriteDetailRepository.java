package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeIntWriteDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public class RbFeeIntWriteDetailRepository extends BusinessRepository<RbFeeIntWriteDetail> {
   public RbFeeIntWriteDetail selectLastReference(String feeIntNo, String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("feeIntNo", feeIntNo);
      map.put("clientNo", clientNo);
      return (RbFeeIntWriteDetail)this.daoSupport.selectOne(RbFeeIntWriteDetail.class.getName() + ".selectLastReference", map);
   }

   public List<RbFeeIntWriteDetail> selectListByReference(String reference) {
      RbFeeIntWriteDetail rbFeeIntWriteDetail = new RbFeeIntWriteDetail();
      rbFeeIntWriteDetail.setReference(reference);
      return this.daoSupport.selectList(rbFeeIntWriteDetail);
   }
}
