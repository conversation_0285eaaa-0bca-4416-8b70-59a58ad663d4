package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIndepStandardSplit;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbIndepStandardSplitRepository extends BusinessRepository {
   public int insert(RbIndepStandardSplit split) {
      return super.insert(split);
   }

   public RbIndepStandardSplit getSplitForToday(String agreementId, String clientNo, Date tranDate) {
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("agreementId", agreementId);
      paramMap.put("clientNo", clientNo);
      paramMap.put("tranDate", tranDate);
      return (RbIndepStandardSplit)this.daoSupport.selectOne(RbIndepStandardSplit.class.getName() + ".getSplitForToday", paramMap);
   }
}
