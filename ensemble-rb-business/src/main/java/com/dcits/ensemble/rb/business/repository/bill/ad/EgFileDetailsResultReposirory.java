package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.model.cm.reg.EgFileDetailsResult;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class EgFileDetailsResultReposirory extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(EgFileDetailsResultReposirory.class);

   public void insertEgFileDetailsResult(EgFileDetailsResult egFileDetailsResult) {
      if (BusiUtil.isNotNull(egFileDetailsResult)) {
         HashMap<String, Object> param = new HashMap();
         param.put("egFileDetailsResult", egFileDetailsResult);
         this.daoSupport.insert(EgFileDetailsResult.class.getName() + ".insertEgFileDetailsResult", param);
      }

   }
}
