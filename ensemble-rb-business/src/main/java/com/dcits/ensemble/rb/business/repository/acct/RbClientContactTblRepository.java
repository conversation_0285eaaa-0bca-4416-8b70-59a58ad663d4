package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbClientContactTbl;
import com.dcits.ensemble.rb.business.model.acct.open.ContactInfoModel;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbClientContactTblRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbClientContactTblRepository.class);

   public List<RbClientContactTbl> queryRbClientContactTbls(List<Long> internalKeys, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKeys", internalKeys);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbClientContactTbl.class.getName().concat(".getRbClientContactTblByInternalKeys"), param);
   }

   public void updateRbClientContactTbls(List<Long> internalKeys, ContactInfoModel contactInfoModel, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKeys", internalKeys);
      param.put("clientNo", clientNo);
      param.put("address", contactInfoModel.getAddress());
      param.put("country", contactInfoModel.getCountry());
      param.put("city", contactInfoModel.getCity());
      param.put("cityDist", contactInfoModel.getCityDist());
      param.put("state", contactInfoModel.getState());
      param.put("postalCode", contactInfoModel.getPostalCode());
      param.put("contactType", contactInfoModel.getContactType());
      param.put("mobilePhone", contactInfoModel.getMobilePhone());
      param.put("email", contactInfoModel.getEmail());
      this.daoSupport.update(RbClientContactTbl.class.getName().concat(".updateRbClientContactTblByInternalKeys"), param);
   }

   public RbClientContactTbl queryAddressInformationByInternalKeys(Long internalKey) {
      RbClientContactTbl rbClientContactTbl = new RbClientContactTbl();
      rbClientContactTbl.setInternalKey(internalKey);
      return (RbClientContactTbl)this.daoSupport.selectOne(rbClientContactTbl);
   }
}
