package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctDiscount;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctDiscountRepository extends BusinessRepository {
   public List<RbAcctDiscount> getDiscByPayBranch(String billNo, String payBranch) {
      Map<String, Object> param = new HashMap();
      param.put("billNo", billNo);
      param.put("payBranch", payBranch);
      List<RbAcctDiscount> rbAcctDiscounts = this.daoSupport.selectList(RbAcctDiscount.class.getName() + ".getDiscByPayBranch", param);
      if (null == rbAcctDiscounts) {
         throw BusiUtil.createBusinessException("RB4029");
      } else {
         return rbAcctDiscounts;
      }
   }

   public RbAcctDiscount getDiscByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      RbAcctDiscount rbAcctDiscount = (RbAcctDiscount)this.daoSupport.selectOne(RbAcctDiscount.class.getName() + ".getDiscByInternalKey", param);
      if (null == rbAcctDiscount) {
         throw BusiUtil.createBusinessException("RB4029");
      } else {
         return rbAcctDiscount;
      }
   }

   public List<RbAcctDiscount> getMbDiscount(String baseAcctNo, String clientNo, String billNo, String payBranch, Long[] internalKeys) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("billNo", billNo);
      param.put("payBranch", payBranch);
      param.put("internalKeys", internalKeys);
      List<RbAcctDiscount> rbAcctDiscounts = this.daoSupport.selectList(RbAcctDiscount.class.getName() + ".getMbDiscount", param);
      if (BusiUtil.isNull(rbAcctDiscounts)) {
         throw BusiUtil.createBusinessException("RB4029");
      } else {
         return rbAcctDiscounts;
      }
   }

   public void creatMbDiscount(RbAcctDiscount rbAcctDiscount) {
      super.insert(rbAcctDiscount);
   }

   public void updateMbDiscount(RbAcctDiscount rbAcctDiscount) {
      super.update(rbAcctDiscount);
   }

   public List<RbAcctDiscount> getMbDisCountByBillNo(String billNo) {
      Map<String, Object> param = new HashMap();
      param.put("billNo", billNo);
      return this.daoSupport.selectList(RbAcctDiscount.class.getName() + ".getMbDisCountByBillNo", param);
   }
}
