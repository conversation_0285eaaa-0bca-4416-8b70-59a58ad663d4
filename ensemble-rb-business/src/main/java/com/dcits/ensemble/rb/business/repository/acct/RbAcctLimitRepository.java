package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctLimit;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctLimitRepository extends BusinessRepository {
   public void updMbAcctLimit(List<RbAcctLimit> rbAcctLimits) {
      if (BusiUtil.isNotNull(rbAcctLimits)) {
         for(int i = 0; i < rbAcctLimits.size(); ++i) {
            super.update((EnsBaseDbBean)rbAcctLimits.get(i));
         }
      }

   }

   public List<RbAcctLimit> getMbAcctLimit(RbAcctLimit rbAcctLimit) {
      List<RbAcctLimit> rbAcctLimits = this.daoSupport.selectList(rbAcctLimit);
      if (!BusiUtil.isNull(rbAcctLimit) && !BusiUtil.isNull(rbAcctLimits)) {
         return rbAcctLimits;
      } else {
         throw BusiUtil.createBusinessException("CL3019");
      }
   }

   public RbAcctLimit getMbAcctLimitone(RbAcctLimit rbAcctLimit) {
      RbAcctLimit rbAcctLimits = (RbAcctLimit)this.daoSupport.selectOne(rbAcctLimit);
      if (null == rbAcctLimit) {
         throw BusiUtil.createBusinessException("CL3019");
      } else {
         return rbAcctLimits;
      }
   }
}
