package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctClientChange;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAcctClientChangeRepository extends BusinessRepository {
   public void createMbAcctClientChange(RbAcctClientChange rbAcctClientChange) {
      super.insert(rbAcctClientChange);
   }

   public RbAcctClientChange createMbAcctClientChange() {
      RbAcctClientChange rbAcctClientChange = new RbAcctClientChange();
      rbAcctClientChange.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.eventSeqNo));
      return rbAcctClientChange;
   }

   public RbAcctClientChange selectOneInfo(String seqNo, String internalKey, String company) {
      Map<String, Object> param = new HashMap();
      param.put("seqNo", seqNo);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcctClientChange)this.daoSupport.selectOne(RbAcctClientChange.class.getName() + ".selectByPrimaryKeyExt", param);
   }

   public List<RbAcctClientChange> selectNoSuccess(String seqNo, String company) {
      RbAcctClientChange clientChange = new RbAcctClientChange();
      clientChange.setSeqNo(seqNo);
      clientChange.setCompany(company);
      return this.daoSupport.selectList(RbAcctClientChange.class.getName() + ".selectNoSuccess", clientChange);
   }

   public List<RbAcctClientChange> getRbAcctClientChangeByStatus(String baseAcctNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("baseAcctNo", baseAcctNo);
      return this.daoSupport.selectList(RbAcctClientChange.class.getName() + ".getRbAcctClientChangeByStatus", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateClientChangeBySeqNo(long internalKey, String prodType, String seqNo, String clientNo, String oldClientNo, String newClientNo, String company) {
      Map<String, Object> param = new HashMap(3);
      param.put("internalKey", internalKey);
      param.put("prodType", prodType);
      param.put("seqNo", seqNo);
      param.put("clientNo", clientNo);
      param.put("oldClientNo", oldClientNo);
      param.put("newClientNo", newClientNo);
      param.put("company", company);
      this.daoSupport.update(RbAcctClientChange.class.getName() + ".updateClientChangeBySeqNO", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void createClientChange(RbAcctClientChange rbAcctClientChange) {
      super.insert(rbAcctClientChange);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateClientChange(RbAcctClientChange rbAcctClientChange) {
      rbAcctClientChange.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctClientChange.class.getName() + ".updateClientChangeBySeqNO", rbAcctClientChange);
   }
}
