package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.DaoSupport;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.comet.rpc.api.model.head.AppHead;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAdjustHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctAdjustHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctAdjustHistRepository.class);
   @Resource
   DaoSupport daoSupport;

   public List<RbAcctAdjustHist> selectByInternalKey(Long internalKey, String clientNo) {
      RbAcctAdjustHist rbAcctAdjustHist = new RbAcctAdjustHist();
      rbAcctAdjustHist.setInternalKey(internalKey);
      rbAcctAdjustHist.setClientNo(clientNo);
      rbAcctAdjustHist.setProcessFlag("N");
      return this.daoSupport.selectList(rbAcctAdjustHist);
   }

   public Integer selectCountByInternalKey(Long internalKey, String clientNo) {
      RbAcctAdjustHist rbAcctAdjustHist = new RbAcctAdjustHist();
      rbAcctAdjustHist.setInternalKey(internalKey);
      rbAcctAdjustHist.setClientNo(clientNo);
      rbAcctAdjustHist.setProcessFlag("N");
      return (Integer)this.daoSupport.selectObject(RbAcctAdjustHist.class.getName() + ".selectCountByInternalKey", rbAcctAdjustHist);
   }

   public List<RbAcctAdjustHist> selectByInternalKeyLimit(Long internalKey, String clientNo, BigDecimal currentNum) {
      RbAcctAdjustHist rbAcctAdjustHist = new RbAcctAdjustHist();
      rbAcctAdjustHist.setInternalKey(internalKey);
      rbAcctAdjustHist.setClientNo(clientNo);
      rbAcctAdjustHist.setProcessFlag("N");
      AppHead appHead = Context.getInstance().getAppHead();
      appHead.setPgupOrPgdn("1");
      appHead.setTotalNum("1000");
      appHead.setCurrentNum(String.valueOf(currentNum));
      RowArgs rowArgs = PageUtil.convertAppHead(appHead);
      String statementPostfix = RbAcctAdjustHist.class.getName() + ".selectList";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctAdjustHist> queryResult = this.daoSupport.selectQueryResult(statementPostfix, rbAcctAdjustHist, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, rbAcctAdjustHist);
      }
   }

   public RbAcctAdjustHist selectByChannelSeqNo(String channelSeqNo, String clientNo, String tranStatus, String adjustFrom) {
      RbAcctAdjustHist rbAcctAdjustHist = new RbAcctAdjustHist();
      rbAcctAdjustHist.setClientNo(clientNo);
      rbAcctAdjustHist.setOrigChannelSeqNo(channelSeqNo);
      rbAcctAdjustHist.setTranStatus(tranStatus);
      rbAcctAdjustHist.setAdjustFrom(adjustFrom);
      return (RbAcctAdjustHist)this.daoSupport.selectOne(rbAcctAdjustHist);
   }

   public List<RbAcctAdjustHist> selectByChannelSeqNo(Long internalKey, String channelSeqNo) {
      RbAcctAdjustHist rbAcctAdjustHist = new RbAcctAdjustHist();
      rbAcctAdjustHist.setInternalKey(internalKey);
      rbAcctAdjustHist.setOrigChannelSeqNo(channelSeqNo);
      return this.daoSupport.selectList(rbAcctAdjustHist);
   }

   public void addRbAcctAdjustHist(RbAcctAdjustHist rbAcctAdjustHist) {
      super.insert(rbAcctAdjustHist);
   }

   public List<RbAcctAdjustHist> getRbAcctAjustHists(Long internalKey, String adjustDate, String intAdjType) {
      RbAcctAdjustHist rbAcctAdjustHist = new RbAcctAdjustHist();
      rbAcctAdjustHist.setInternalKey(internalKey);
      rbAcctAdjustHist.setAdjustDate(BusiUtil.string2Date(adjustDate));
      rbAcctAdjustHist.setIntAdjType(intAdjType);
      return this.daoSupport.selectList(rbAcctAdjustHist);
   }

   public List<RbAcctAdjustHist> getRbAcctAjustHistsByStartEndDate(Long internalKey, String startDate, String endDate, String intAdjType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("intAdjType", intAdjType);
      param.put("startDate", BusiUtil.getDate(startDate));
      param.put("endDate", BusiUtil.getDate(endDate));
      return this.daoSupport.selectList(RbAcctAdjustHist.class.getName() + ".getAccrInfoMainListByStartEndDate", param);
   }

   public List<RbAcctAdjustHist> getRbAcctAdjustHistsByNegativeAgg(RbAcctAdjustHist rbAcctAdjustHist) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", rbAcctAdjustHist.getInternalKey());
      param.put("clientNo", rbAcctAdjustHist.getClientNo());
      param.put("processFlag", rbAcctAdjustHist.getProcessFlag());
      return this.daoSupport.selectList(RbAcctAdjustHist.class.getName() + ".getRbAcctAdjustHistsByNegativeAgg", param);
   }

   public int restoreRbAcctAdjustHist(List<RbAcctAdjustHist> rbAcctAdjustHists) {
      ((RbAcctAdjustHist)rbAcctAdjustHists.get(0)).setCompany(Context.getInstance().getCompany());
      return this.daoSupport.updateAddBatch(RbAcctAdjustHist.class.getName() + ".restoreRbAcctAdjustHists", rbAcctAdjustHists);
   }

   public int updRbAcctAdjHistProcessFlag(RbAcctAdjustHist rbAcctAdjustHist) {
      return this.daoSupport.update(RbAcctAdjustHist.class.getName() + ".updRbAcctAdjHistProcessFlag", rbAcctAdjustHist);
   }
}
