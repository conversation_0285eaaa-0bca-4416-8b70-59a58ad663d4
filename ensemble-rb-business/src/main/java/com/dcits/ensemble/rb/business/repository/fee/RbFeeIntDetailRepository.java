package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeIntDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public class RbFeeIntDetailRepository extends BusinessRepository<RbFeeIntDetail> {
   public RbFeeIntDetail selectOneByFeeIntNo(String feeIntNo) {
      RbFeeIntDetail rbFeeIntDetail = new RbFeeIntDetail();
      rbFeeIntDetail.setFeeIntNo(feeIntNo);
      return (RbFeeIntDetail)this.selectOne(rbFeeIntDetail);
   }

   public List<RbFeeIntDetail> selectListByParam(String feeIntNo, String tranBranch, String userId, Date startDate, Date endDate) {
      Map<String, Object> param = new HashMap();
      param.put("feeIntNo", feeIntNo);
      param.put("tranBranch", tranBranch);
      param.put("userId", userId);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbFeeIntDetail.class.getName() + ".selectListByParam";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbFeeIntDetail> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }
}
