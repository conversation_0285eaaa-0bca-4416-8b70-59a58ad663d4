package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.bc.unit.acct.sequences.SettleNo;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettle;
import com.dcits.ensemble.rb.business.model.acct.settle.MbAcctSettleModel;
import com.dcits.ensemble.rb.business.model.acct.settle.SettlePayRecIndEnum;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctSettleRepsitory extends BusinessRepository {
   @Resource
   private RbAcctRepository mbAcctRepository;
   private static final String REL = "REL";
   private static final String SETTLE_BANK_FLAG_O = "O";

   public RbAcctSettle createMbAcctSettle(MbAcctSettleModel mbAcctSettleModel) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      BeanUtil.copy(mbAcctSettleModel, rbAcctSettle);
      if (BusiUtil.isNull(rbAcctSettle.getSettleNo())) {
         rbAcctSettle.setSettleNo((new SettleNo()).getNextVal());
      }

      return rbAcctSettle;
   }

   public List<RbAcctSettle> selectByInternalKey(Long internalKey, String clientNo) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> selectByFourElement(String baseAcctNo, String acctSeqNo, String acctCcy, String prodType, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleBaseAcctNo", baseAcctNo);
      param.put("settleAcctSeqNo", acctSeqNo);
      param.put("settleAcctCcy", acctCcy);
      param.put("settleProdType", prodType);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectByMbSettleFourElement", param);
   }

   public RbAcctSettle deleteMbAcctSettle(MbAcctSettleModel mbAcctSettleModel) {
      Long internalKey = mbAcctSettleModel.getInternalKey();
      String settleNo = mbAcctSettleModel.getSettleNo();
      String clientNo = mbAcctSettleModel.getClientNo();
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setSettleNo(settleNo);
      rbAcctSettle.setClientNo(clientNo);
      return rbAcctSettle;
   }

   public void createMbAcctSettleDb(RbAcctSettle rbAcctSettle) {
      rbAcctSettle.setUserId(Context.getInstance().getUserId());
      rbAcctSettle.setLastChangeDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      rbAcctSettle.setLastChangeUserId(Context.getInstance().getUserId());
      rbAcctSettle.setCreateDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      RbAcctSettle acctSettle = new RbAcctSettle();
      acctSettle.setInternalKey(rbAcctSettle.getInternalKey());
      acctSettle.setAmtType(rbAcctSettle.getAmtType());
      acctSettle.setSettleMethod(rbAcctSettle.getSettleMethod());
      acctSettle.setPayRecInd(rbAcctSettle.getPayRecInd());
      acctSettle.setEventType(rbAcctSettle.getEventType());
      acctSettle.setSettleAcctClass(rbAcctSettle.getSettleAcctClass());
      acctSettle.setClientNo(rbAcctSettle.getClientNo());
      List<RbAcctSettle> rbAcctSettles = this.daoSupport.selectList(acctSettle);
      if (BusiUtil.isNull(rbAcctSettles)) {
         RbAcct rbAcct = this.mbAcctRepository.getAcctSched(rbAcctSettle.getInternalKey(), rbAcctSettle.getCompany());
         if (BusiUtil.isNotNull(rbAcct)) {
            rbAcctSettle.setCompany(rbAcct.getCompany());
         }

         if (BusiUtil.isNull(rbAcctSettle.getSettleNo())) {
            rbAcctSettle.setSettleNo((new SettleNo()).getNextVal());
         }

         rbAcctSettle.setClientNo(rbAcct.getClientNo());
         super.insert(rbAcctSettle);
      } else {
         List<RbAcctSettle> settleList = (List)rbAcctSettles.stream().filter((rbAcctSettle1) -> {
            return BusiUtil.isEquals(rbAcctSettle1.getSettleAcctClass(), "REL");
         }).collect(Collectors.toList());
         if (settleList.size() > 0) {
            throw BusiUtil.createBusinessException("RB6821");
         }
      }

   }

   public void updateMbAcctSettleDb(RbAcctSettle rbAcctSettle) {
      rbAcctSettle.setLastChangeDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      rbAcctSettle.setLastChangeUserId(Context.getInstance().getUserId());
      super.update(rbAcctSettle);
   }

   public void updateMbAcctSettle(RbAcctSettle rbAcctSettle) {
      super.update(rbAcctSettle);
   }

   public void deleteMbAcctSettleDb(RbAcctSettle rbAcctSettle) {
      super.delete(rbAcctSettle);
   }

   public void createMbAcctSettleDbList(List<RbAcctSettle> rbAcctSettles) {
      RbAcct rbAcct = this.mbAcctRepository.getAcctSched(((RbAcctSettle)rbAcctSettles.get(0)).getInternalKey(), ((RbAcctSettle)rbAcctSettles.get(0)).getCompany());

      for(int i = 0; i < rbAcctSettles.size(); ++i) {
         if (BusiUtil.isNull(((RbAcctSettle)rbAcctSettles.get(i)).getSettleNo())) {
            ((RbAcctSettle)rbAcctSettles.get(i)).setSettleNo((new SettleNo()).getNextVal());
         }

         if (BusiUtil.isNotNull(rbAcct)) {
            ((RbAcctSettle)rbAcctSettles.get(i)).setCompany(rbAcct.getCompany());
         }

         super.insert((EnsBaseDbBean)rbAcctSettles.get(i));
      }

   }

   public void updateMbAcctSettleDbList(List<RbAcctSettle> rbAcctSettles) {
      for(int i = 0; i < rbAcctSettles.size(); ++i) {
         super.update((EnsBaseDbBean)rbAcctSettles.get(i));
      }

   }

   public void deleteMbAcctSettleDbList(List<RbAcctSettle> rbAcctSettles) {
      for(int i = 0; i < rbAcctSettles.size(); ++i) {
         super.delete((EnsBaseDbBean)rbAcctSettles.get(i));
      }

   }

   public RbAcctSettle createAcctSettle(MbAcctSettleModel mbAcctSettle) {
      String baseAcctNo = mbAcctSettle.getSettleBaseAcctNo();
      String prodType = mbAcctSettle.getSettleProdType();
      String ccy = mbAcctSettle.getSettleAcctCcy();
      String seqNo = mbAcctSettle.getSettleAcctSeqNo();
      Long internalKey = mbAcctSettle.getInternalKey();
      String clientNo = mbAcctSettle.getClientNo();
      String amtType = mbAcctSettle.getAmtType();
      mbAcctSettle.setSettleMethod("R");
      if (BusiUtil.isNull(mbAcctSettle.getSettleMethod())) {
         throw BusiUtil.createBusinessException("RB3014");
      } else {
         if (BusiUtil.isNull(internalKey)) {
            if (BusiUtil.isNull(baseAcctNo)) {
               throw BusiUtil.createBusinessException("RB3013");
            }

            RbAcct rbAcct;
            if (BusiUtil.isNotNull(seqNo) && BusiUtil.isNotNull(prodType) && BusiUtil.isNotNull(ccy)) {
               rbAcct = this.mbAcctRepository.getMbAcctInfo(baseAcctNo, prodType, ccy, seqNo);
            } else {
               rbAcct = this.mbAcctRepository.getMbLeadAcct(baseAcctNo);
            }

            internalKey = rbAcct.getInternalKey();
            clientNo = rbAcct.getClientNo();
         }

         RbAcct mbSubAcct;
         if (BusiUtil.isEquals(mbAcctSettle.getSettleBankFlag(), "O")) {
            mbSubAcct = null;
         } else if (BusiUtil.isNull(mbAcctSettle.getSettleAcctInternalKey())) {
            mbSubAcct = this.mbAcctRepository.getMbAcctInfo(mbAcctSettle.getSettleBaseAcctNo(), mbAcctSettle.getSettleProdType(), mbAcctSettle.getSettleAcctCcy(), mbAcctSettle.getSettleAcctSeqNo());
         } else {
            mbSubAcct = this.mbAcctRepository.getMbAcct(mbAcctSettle.getSettleAcctInternalKey(), mbAcctSettle.getSettleClient());
         }

         RbAcctSettle mbsettle = new RbAcctSettle();
         BeanUtil.copy(mbAcctSettle, mbsettle);
         if (BusiUtil.isNull(amtType)) {
            mbsettle.setAmtType("INT");
         } else {
            mbsettle.setAmtType(amtType);
         }

         if (BusiUtil.isEquals(mbsettle.getSettleAcctClass(), "AUS")) {
            mbsettle.setAmtType("INT");
            mbsettle.setPayRecInd(SettlePayRecIndEnum.REC.toString());
         }

         mbsettle.setInternalKey(internalKey);
         mbsettle.setSettleNo((new SettleNo()).getNextVal());
         if (BusiUtil.isNotNull(mbSubAcct)) {
            mbsettle.setSettleAcctInternalKey(mbSubAcct.getInternalKey());
            mbsettle.setSettleBranch(mbSubAcct.getAcctBranch());
            mbsettle.setSettleClient(mbSubAcct.getClientNo());
            mbsettle.setSettleBaseAcctNo((String)BusiUtil.nvl(mbsettle.getSettleBaseAcctNo(), mbSubAcct.getBaseAcctNo()));
            mbsettle.setSettleAcctCcy(mbSubAcct.getAcctCcy());
            mbsettle.setSettleAcctSeqNo(mbSubAcct.getAcctSeqNo());
            mbsettle.setSettleProdType(mbSubAcct.getProdType());
         }

         if (BusiUtil.isNull(mbsettle.getSettleAcctClass())) {
            String settleAcctClass = "INT";
            mbsettle.setSettleAcctClass(settleAcctClass);
         }

         if (BusiUtil.isNull(mbsettle.getCompany()) && BusiUtil.isNotNull(mbSubAcct)) {
            mbsettle.setCompany(mbSubAcct.getCompany());
         }

         mbsettle.setClientNo(clientNo);
         mbsettle.setLastChangeDate(new Date());
         mbsettle.setLastChangeUserId(Context.getInstance().getUserId());
         mbsettle.setCreateDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
         return mbsettle;
      }
   }

   public void addAcctSettle(List<MbAcctSettleModel> mbAcctSettles, Long internalKey) {
      if (BusiUtil.isNotNull(mbAcctSettles)) {
         for(int i = 0; i < mbAcctSettles.size(); ++i) {
            MbAcctSettleModel mbAcctSettle = (MbAcctSettleModel)mbAcctSettles.get(i);
            mbAcctSettle.setInternalKey(internalKey);
            RbAcctSettle mbsettle = this.createAcctSettle(mbAcctSettle);
            super.insert(mbsettle);
         }
      }

   }

   public void updAcctSettle(MbAcctSettleModel mbAcctSettle) {
      Long internalKey = mbAcctSettle.getInternalKey();
      String baseAcctNo = mbAcctSettle.getBaseAcctNo();
      String prodType = mbAcctSettle.getProdType();
      String ccy = mbAcctSettle.getCcy();
      String seqNo = mbAcctSettle.getAcctSeqNo();
      if (internalKey == null) {
         if (baseAcctNo == null) {
            throw BusiUtil.createBusinessException("RB2233");
         }

         RbAcct rbAcct;
         if (seqNo != null && prodType != null && ccy != null) {
            rbAcct = this.mbAcctRepository.getMbAcctInfo(baseAcctNo, prodType, ccy, seqNo);
         } else {
            rbAcct = this.mbAcctRepository.getMbLeadAcct(baseAcctNo);
         }

         if (BusiUtil.isNull(rbAcct)) {
            throw BusiUtil.createBusinessException("RB3105", new String[]{BusiUtil.getMessageByKey("MG0231"), "rbAcct"});
         }

         internalKey = rbAcct.getInternalKey();
      }

      RbAcctSettle mbsettle = new RbAcctSettle();
      BeanUtil.copy(mbAcctSettle, mbsettle);
      mbsettle.setInternalKey(internalKey);
      mbsettle.setLastChangeDate(new Date());
      mbsettle.setLastChangeUserId(Context.getInstance().getUserId());
      super.update(mbsettle);
   }

   public void delAcctSettle(MbAcctSettleModel mbAcctSettle, Long internalKey) {
      Long internal = internalKey;
      String baseAcctNo = mbAcctSettle.getBaseAcctNo();
      String prodType = mbAcctSettle.getProdType();
      String ccy = mbAcctSettle.getCcy();
      String seqNo = mbAcctSettle.getAcctSeqNo();
      String settleNo = mbAcctSettle.getSettleNo();
      if (internalKey == null) {
         if (baseAcctNo == null) {
            throw BusiUtil.createBusinessException("RB2233");
         }

         RbAcct rbAcct;
         if (seqNo != null && prodType != null && ccy != null) {
            rbAcct = this.mbAcctRepository.getMbAcctInfo(baseAcctNo, prodType, ccy, seqNo);
         } else {
            rbAcct = this.mbAcctRepository.getMbLeadAcct(baseAcctNo);
         }

         if (BusiUtil.isNull(rbAcct)) {
            throw BusiUtil.createBusinessException("RB3105", new String[]{BusiUtil.getMessageByKey("MG0231"), "rbAcct"});
         }

         internal = rbAcct.getInternalKey();
      }

      if (settleNo == null) {
         throw BusiUtil.createBusinessException("RB2233");
      } else {
         RbAcctSettle mbsettle = new RbAcctSettle();
         BeanUtil.copy(mbAcctSettle, mbsettle);
         mbsettle.setInternalKey(internal);
         super.delete(mbsettle);
      }
   }

   public List<RbAcctSettle> getRbAcctSettleOrderSettleAcct(long internalKey, String settleBaseAcctNo, String settleAcctSeqNo, String settleCcy, String settleAcctClass) {
      RbAcctSettle param = new RbAcctSettle();
      param.setInternalKey(internalKey);
      param.setSettleBaseAcctNo(settleBaseAcctNo);
      param.setSettleAcctSeqNo(settleAcctSeqNo);
      param.setSettleAcctCcy(settleCcy);
      param.setSettleAcctClass(settleAcctClass);
      return this.daoSupport.selectList(param);
   }

   public List<RbAcctSettle> selectBySettleBaseAcctOrCardNo(long internalKey, String settleBaseAcctNo, String settleCardNo, String settleAcctSeqNo, String settleCcy, String settleAcctClass) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleBaseAcctNo", settleBaseAcctNo);
      param.put("settleCardNo", settleCardNo);
      param.put("settleAcctSeqNo", settleAcctSeqNo);
      param.put("settleCcy", settleCcy);
      param.put("settleAcctClass", settleAcctClass);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectBySettleBaseAcctOrCardNo", param);
   }

   public List<RbAcctSettle> getRbAcctSettleInfo(RbAcctSettle rbAcctSettle) {
      return this.daoSupport.selectList(rbAcctSettle);
   }

   public List<RbAcctSettle> getAcctSettleBySettleAcctInternalKey(Long internalKey) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setSettleAcctInternalKey(internalKey);
      return this.daoSupport.selectList(rbAcctSettle);
   }

   public RbAcctSettle getAcctSettle(Long internalKey, String settleNo) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setSettleNo(settleNo);
      return (RbAcctSettle)this.daoSupport.selectOne(rbAcctSettle);
   }

   public RbAcctSettle getAcctSettleByInternalKeyAndClientNo(Long internalKey, String clientNo) {
      RbAcctSettle rbAcctSettle = new RbAcctSettle();
      rbAcctSettle.setInternalKey(internalKey);
      rbAcctSettle.setClientNo(clientNo);
      return (RbAcctSettle)this.daoSupport.selectOne(rbAcctSettle);
   }

   public RbAcctSettle getAcctAutoSettle(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      List<RbAcctSettle> rbAcctSettles = this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getAcctAutoSettle", param);
      if (BusiUtil.isNull(rbAcctSettles)) {
         throw BusiUtil.createBusinessException("RB3045");
      } else {
         return (RbAcctSettle)rbAcctSettles.get(0);
      }
   }

   public RbAcctSettle getAcctAutoSettlePRI(String settleBaseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleBaseAcctNo", settleBaseAcctNo);
      List<RbAcctSettle> rbAcctSettles = this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getAcctAutoSettlePRI", param);
      return BusiUtil.isNull(rbAcctSettles) ? null : (RbAcctSettle)rbAcctSettles.get(0);
   }

   public RbAcctSettle getDcAcctAutoSettle(Long internalKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcctSettle)this.daoSupport.selectOne(RbAcctSettle.class.getName() + ".getDcAcctAutoSettle", param);
   }

   public RbAcctSettle getAcctAutoSettlePF(Long internalKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("company", company);
      return (RbAcctSettle)this.daoSupport.selectOne(RbAcctSettle.class.getName() + ".getAcctAutoSettlePF", param);
   }

   public List<RbAcctSettle> selectAcctSettleBySettleAcctType(Long internalKey, String settleAcctType, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleAcctClass", settleAcctType);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectAcctSettleBySettleAcctType", param);
   }

   public List<RbAcctSettle> selectAcctSettleBySettleAcctType1(Long internalKey, String settleAcctType, String clientNo, String company, String settleBaseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleAcctClass", settleAcctType);
      param.put("clientNo", clientNo);
      param.put("company", company);
      param.put("settleBaseAcctNo", settleBaseAcctNo);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectAcctSettleBySettleAcctType1", param);
   }

   public List<RbAcctSettle> getAcctSettleBySettleAcctType(Long settleInternalKey, String settleAcctType) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleInternalKey", settleInternalKey);
      param.put("settleAcctClass", settleAcctType);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getAcctSettleBySettleAcctType", param);
   }

   public List<RbAcctSettle> getAcctSettleBySettleAcctType(Long settleInternalKey, String settleClient, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleInternalKey", settleInternalKey);
      param.put("settleClient", settleClient);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getAcctSettleBySettleAcctType", param);
   }

   public List<RbAcctSettle> getTimeDepositAcctSettle(Long settleAcctInternalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleAcctInternalKey", settleAcctInternalKey);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getFixedAcctSettle", param);
   }

   public List<RbAcctSettle> selectAcctSettleByEventType(Long internalKey, String eventType, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("eventType", eventType);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectAcctSettleByEventType", param);
   }

   public List<RbAcctSettle> selectBySettleInternalKey(Long settleAcctInternalKey, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleAcctInternalKey", settleAcctInternalKey);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectBySettleInternalKey", param);
   }

   public List<RbAcctSettle> selectBySettleInternalKey(Long settleAcctInternalKey, String settleClient, String company) {
      Map<String, Object> param = new HashMap(2);
      param.put("settleAcctInternalKey", settleAcctInternalKey);
      param.put("settleClient", settleClient);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectBySettleInternalKey2", param);
   }

   public List<RbAcctSettle> selectSettleBySettleAcctClasses(Long internalKey, String[] settleAcctClasses, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleAcctClasses", settleAcctClasses);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectSettleBySettleAcctClasses", param);
   }

   public List<RbAcctSettle> selectBySettleAcctInternalKey(Long settleAcctInternalKey, String settleAcctClass, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("settleAcctInternalKey", settleAcctInternalKey);
      param.put("settleAcctClass", settleAcctClass);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectBySettleAcctInternalKey2", param);
   }

   public RbAcctSettle getAcctSettleBySettleAmt(Map<String, Object> param) {
      RbAcctSettle rbAcctSettle = null;
      if (BusiUtil.isNotNull(param)) {
         List<RbAcctSettle> list = this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getAcctSettleBySettleClass", param);
         if (BusiUtil.isNotNull(list)) {
            rbAcctSettle = (RbAcctSettle)list.get(0);
         }
      }

      return rbAcctSettle;
   }

   public List<RbAcctSettle> mbAcctSettleListUpdate(RbAcct rbAcct, RbAcct settleAcct) {
      List<RbAcctSettle> rbAcctSettleList1 = new ArrayList();
      if (BusiUtil.isNotNull(rbAcct)) {
         List<RbAcctSettle> rbAcctSettleList = this.selectAcctSettleByEventType(rbAcct.getInternalKey(), "QFINT", rbAcct.getCompany());
         RbAcctSettle rbAcctSettle;
         if (BusiUtil.isNotNull(rbAcctSettleList)) {
            rbAcctSettle = (RbAcctSettle)rbAcctSettleList.get(0);
            rbAcctSettle.setEventType("QFINT");
            rbAcctSettle.setSettleAcctClass("INT");
            rbAcctSettle.setSettleMethod("R");
            rbAcctSettle.setPayRecInd("REC");
            rbAcctSettle.setAmtType("INT");
            rbAcctSettle.setSettleClient(rbAcct.getClientNo());
            rbAcctSettle.setSettleBranch(rbAcct.getAcctBranch());
            rbAcctSettle.setSettleAcctInternalKey(settleAcct.getInternalKey());
            rbAcctSettle.setSettleBaseAcctNo(settleAcct.getBaseAcctNo());
            rbAcctSettle.setSettleAcctName(settleAcct.getAcctName());
            rbAcctSettle.setSettleProdType(settleAcct.getProdType());
            rbAcctSettle.setSettleAcctCcy(settleAcct.getAcctCcy());
            rbAcctSettle.setSettleAcctSeqNo(settleAcct.getAcctSeqNo());
            rbAcctSettleList1.add(rbAcctSettle);
         } else {
            rbAcctSettle = new RbAcctSettle();
            rbAcctSettle.setInternalKey(rbAcct.getInternalKey());
            rbAcctSettle.setSettleNo((new SettleNo()).getNextVal());
            rbAcctSettle.setEventType("QFINT");
            rbAcctSettle.setSettleAcctClass("INT");
            rbAcctSettle.setSettleMethod("R");
            rbAcctSettle.setPayRecInd("REC");
            rbAcctSettle.setAmtType("INT");
            rbAcctSettle.setSettleClient(rbAcct.getClientNo());
            rbAcctSettle.setSettleBranch(rbAcct.getAcctBranch());
            rbAcctSettle.setSettleAcctInternalKey(settleAcct.getInternalKey());
            rbAcctSettle.setSettleBaseAcctNo(settleAcct.getBaseAcctNo());
            rbAcctSettle.setSettleAcctName(settleAcct.getAcctName());
            rbAcctSettle.setSettleProdType(settleAcct.getProdType());
            rbAcctSettle.setSettleAcctCcy(settleAcct.getAcctCcy());
            rbAcctSettle.setSettleAcctSeqNo(settleAcct.getAcctSeqNo());
            rbAcctSettleList1.add(rbAcctSettle);
         }
      }

      return rbAcctSettleList1;
   }

   public RbAcctSettle selectByRelSettleInternalKey(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      List<RbAcctSettle> rbAcctSettles = this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getRelSettleByInternalKey", param);
      return BusiUtil.isNull(rbAcctSettles) ? null : (RbAcctSettle)rbAcctSettles.get(0);
   }

   public RbAcctSettle selectAcctSettleByIntSettleAcctClass(Long internalKey, String settleAcctClass, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleAcctClass", settleAcctClass);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcctSettle)this.daoSupport.selectOne(RbAcctSettle.class.getName() + ".selectAcctSettleBySettleAcctType", param);
   }

   public RbAcctSettle getcAcctAutoDepositSettle(Long internalKey, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbAcctSettle)this.daoSupport.selectOne(RbAcctSettle.class.getName() + ".getcAcctAutoDepositSettle", param);
   }

   public void updateMbAcctSettleIdep(RbAcctSettle rbAcctSettle) {
      rbAcctSettle.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctSettle.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctSettle.class.getName() + ".updateIdep", rbAcctSettle);
   }

   public void updateMbAcctSettleTpp(RbAcctSettle rbAcctSettle) {
      rbAcctSettle.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctSettle.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctSettle.class.getName() + ".updateTpp", rbAcctSettle);
   }

   public RbAcctSettle selectByInternalKeyAndAmttype(Long internalKey, String settleAmtType, String clientNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleAmtType", settleAmtType);
      param.put("clientNo", clientNo);
      param.put("company", company);
      List<RbAcctSettle> rbAcctSettles = this.daoSupport.selectList(RbAcctSettle.class.getName() + ".selectByInternalKeyAndAmttype", param);
      return BusiUtil.isNull(rbAcctSettles) ? null : (RbAcctSettle)rbAcctSettles.get(0);
   }

   public List<RbAcctSettle> getRelBindInfoByBaseAcct(String baseAcctNo, String cardNo, String settleAcctSeqNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("cardNo", cardNo);
      param.put("settleAcctSeqNo", settleAcctSeqNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctSettle.class.getName() + ".getRelBindInfoByBaseAcct", param);
   }

   public RbAcctSettle getAcctSettle(RbAcctSettle rbAcctSettle) {
      return (RbAcctSettle)this.daoSupport.selectOne(rbAcctSettle);
   }

   public Boolean getIsBindSettleAcct(RbAcctStandardModel acctStandardModel, RbAcctStandardModel othAcctStandardModel) {
      Boolean isBindSettleAcct = null;
      if (BusiUtil.isNotNull(acctStandardModel.getAcctClass()) && BusiUtil.isNotNull(othAcctStandardModel.getAcctClass())) {
         List<RbAcctSettle> rbAcctSettles = this.selectAcctSettleBySettleAcctType(acctStandardModel.getInternalKey(), "REL", acctStandardModel.getClientNo(), acctStandardModel.getCompany());
         Iterator var5 = rbAcctSettles.iterator();

         RbAcctSettle rbAcctSettle;
         do {
            if (!var5.hasNext()) {
               return isBindSettleAcct;
            }

            rbAcctSettle = (RbAcctSettle)var5.next();
         } while(!BusiUtil.isEquals(rbAcctSettle.getSettleBaseAcctNo(), othAcctStandardModel.getBaseAcctNo()) && !BusiUtil.isEquals(rbAcctSettle.getSettleBaseAcctNo(), othAcctStandardModel.getCardNo()));

         isBindSettleAcct = true;
      }

      return isBindSettleAcct;
   }
}
