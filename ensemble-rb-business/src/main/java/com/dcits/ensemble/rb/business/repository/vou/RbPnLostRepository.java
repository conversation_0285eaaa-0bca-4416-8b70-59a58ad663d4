package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.bc.component.vou.sequence.MbPnLostSeq;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnLost;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPnLostRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPnLostRepository.class);

   public RbPnLost getMbPnLostBylossNo(String lossNo, String validFlag) {
      Map<String, Object> param = new HashMap();
      param.put("lossNo", lossNo);
      param.put("validFlag", validFlag);
      return (RbPnLost)this.daoSupport.selectOne(RbPnLost.class.getName() + ".getMbPnLostBylossNo", param);
   }

   public RbPnLost getMbPnLostByBillNo(String billNo, String validFlag) {
      Map<String, Object> param = new HashMap();
      param.put("billNo", billNo);
      param.put("validFlag", validFlag);
      return (RbPnLost)this.daoSupport.selectOne(RbPnLost.class.getName() + ".getMbPnLostByBillNo", param);
   }

   public void updateMbPnLostStatus(RbPnLost mbPnLost) {
      super.update(mbPnLost);
   }

   public void mbPnLostInsert(RbPnLost mbPnLost) {
      if (BusiUtil.isNull(mbPnLost.getLostNo())) {
         MbPnLostSeq mbPnLostSeq = new MbPnLostSeq();
         String seqNo = mbPnLostSeq.getMbPnLostSeq();
         mbPnLost.setLostNo(seqNo);
         String lossNo = seqNo + mbPnLost.getLossNo();
         mbPnLost.setLossNo(lossNo);
         super.insert(mbPnLost);
      }

   }
}
