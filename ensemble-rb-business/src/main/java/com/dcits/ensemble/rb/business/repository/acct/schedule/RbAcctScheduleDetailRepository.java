package com.dcits.ensemble.rb.business.repository.acct.schedule;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctScheduleDetailRepository extends BusinessRepository {
   public void addAcctScheduleDetail(List<RbAcctScheduleDetail> rbAcctScheduleDetails) {
      if (BusiUtil.isNotNull(rbAcctScheduleDetails)) {
         for(int i = 0; i < rbAcctScheduleDetails.size(); ++i) {
            this.addAcctScheduleDetail((RbAcctScheduleDetail)rbAcctScheduleDetails.get(i));
         }
      }

   }

   public void addAcctScheduleDetail(RbAcctScheduleDetail rbAcctScheduleDetail) {
      Long internalKey = rbAcctScheduleDetail.getInternalKey();
      String stageNo = rbAcctScheduleDetail.getStageNo();
      if (BusiUtil.isNull(internalKey)) {
         throw BusiUtil.createBusinessException("CL0422");
      } else if (BusiUtil.isNull(stageNo)) {
         throw BusiUtil.createBusinessException("CL0423");
      } else {
         super.insert(rbAcctScheduleDetail);
      }
   }
}
