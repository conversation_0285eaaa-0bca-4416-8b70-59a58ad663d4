package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIdepIncrInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbIDepIncrInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbIDepIncrInfoRepository.class);
   @Resource
   private FmBaseStor fmBaseStor;

   public List<RbIdepIncrInfo> getEffectIncrInfo(String agreementId) {
      return this.getAllIncrInfo(agreementId, "A", (String)null);
   }

   public List<RbIdepIncrInfo> queryIncrInfo(String agreementId) {
      Map map = new HashMap();
      map.put("agreementId", agreementId);
      return this.daoSupport.selectList(RbIdepIncrInfo.class.getName() + ".queryIncrInfo", map);
   }

   public List<RbIdepIncrInfo> getAllIncrInfo(String agreementId, String incrStatus, String tranDate) {
      Map map = new HashMap();
      map.put("agreementId", agreementId);
      map.put("incrStatus", incrStatus);
      map.put("tranDate", tranDate);
      return this.daoSupport.selectList(RbIdepIncrInfo.class.getName() + ".getAllIncrInfo", map);
   }

   public RbIdepIncrInfo getTranDateEffectInfo(String agreementId, String tranDate) {
      List<RbIdepIncrInfo> list = this.getAllIncrInfo(agreementId, "A", tranDate);
      return BusiUtil.isNotNull(list) ? (RbIdepIncrInfo)list.get(0) : null;
   }

   public RbIdepIncrInfo getMbIDepIncrInfo() {
      RbIdepIncrInfo mbIDepIncrInfo = new RbIdepIncrInfo();
      mbIDepIncrInfo.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.iDepIncrInfoSeqNo));
      return mbIDepIncrInfo;
   }

   public List<RbIdepIncrInfo> getMbIDepIncrInfoByDate(String agreementId, Date startDate, Date endDate) {
      Map map = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(map, false);
      map.put("agreementId", agreementId);
      map.put("startDate", startDate);
      map.put("endDate", endDate);
      return this.daoSupport.selectList(RbIdepIncrInfo.class.getName() + ".getMbIDepIncrInfoByDate", map);
   }
}
