package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.bc.component.bill.sequence.RbBillTranDetailSeq;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBillPayment;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbBillPaymentRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbBillPaymentRepository.class);

   public void mbPnPaymentInsert(RbBillPayment rbBillPayment) {
      if (BusiUtil.isNull(rbBillPayment.getSerialNo())) {
         RbBillTranDetailSeq seq = new RbBillTranDetailSeq();
         rbBillPayment.setSerialNo(seq.getMbPnTranDetailSeqId());
      }

      if (BusiUtil.isNotNull(rbBillPayment)) {
         super.insert(rbBillPayment);
      }

   }

   public RbBillPayment getBillPayment(String origSerialNo, String billNo, String clientNo) {
      RbBillPayment rbBillPayment = new RbBillPayment();
      rbBillPayment.setOrigSerialNo(origSerialNo);
      rbBillPayment.setBillNo(billNo);
      rbBillPayment.setClientNo(clientNo);
      return (RbBillPayment)this.daoSupport.selectOne(rbBillPayment);
   }
}
