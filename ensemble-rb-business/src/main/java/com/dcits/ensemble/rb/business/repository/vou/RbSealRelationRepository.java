package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbSealRelation;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbSealRelationRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbSealRelationRepository.class);

   public RbSealRelation selectByInternalKey(String internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbSealRelation)this.daoSupport.selectOne(RbSealRelation.class.getName() + ".selectByInternalKey", param);
   }

   public List<RbSealRelation> getMbSealRelationList(String docType, String voucherNo, String prefix, String internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      param.put("prefix", prefix);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbSealRelation.class.getName() + ".getMbSealRelationList", param);
   }

   public List<RbSealRelation> getMbSealRelationList1(String docType, String voucherNo, String prefix, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      param.put("prefix", prefix);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbSealRelation.class.getName() + ".getMbSealRelationList1", param);
   }

   public RbSealRelation selectMbSealRelation1(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbSealRelation)this.daoSupport.selectOne(RbSealRelation.class.getName() + ".selectByInternalKey1", param);
   }

   public void insert(RbSealRelation mbSealRelation) {
      super.insert(mbSealRelation);
   }

   public void deleteByPrimaryKey(RbSealRelation mbSealRelation) {
      this.daoSupport.delete(mbSealRelation);
   }

   public void updateByPrimaryKey(RbSealRelation mbSealRelation) {
      super.update(mbSealRelation);
   }

   public List<RbSealRelation> selectMbSealRelations(String docType, String voucherNo, String prefix, String internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      param.put("prefix", prefix);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbSealRelation.class.getName() + ".getMbSealRelationList", param);
   }

   public List<RbSealRelation> selectMbSealRelations(String docType, String voucherNo, String prefix, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("docType", docType);
      param.put("voucherNo", voucherNo);
      param.put("prefix", prefix);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbSealRelation.class.getName() + ".getMbSealRelationList1", param);
   }
}
