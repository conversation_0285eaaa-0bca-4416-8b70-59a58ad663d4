package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister;
import com.dcits.ensemble.rb.business.model.vou.cheque.MbPnRegModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPnRegisterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPnRegisterRepository.class);

   public RbPnRegister createMbRegister(MbPnRegModel mbPnRegModel) {
      RbPnRegister mbPnRegister = new RbPnRegister();
      mbPnRegister.setPayerBankCode(mbPnRegModel.getPayerBankCode());
      mbPnRegister.setPayerBankName(mbPnRegModel.getPayerBankName());
      mbPnRegister.setSerialNo(mbPnRegModel.getSerialNo());
      mbPnRegister.setBillType(mbPnRegModel.getBillType());
      mbPnRegister.setTranferCashFlag(mbPnRegModel.getTranferCashFlag());
      mbPnRegister.setPayeeBaseAcctNo(mbPnRegModel.getPayeeAcctNo());
      mbPnRegister.setMediumNo(mbPnRegModel.getMediumNo());
      mbPnRegister.setPayeeAcctName(mbPnRegModel.getPayerName());
      mbPnRegister.setPayerTele(mbPnRegModel.getPayerTele());
      mbPnRegister.setPayerDocumentType(mbPnRegModel.getPayerGlobalIdType());
      mbPnRegister.setPayerDocumentId(mbPnRegModel.getPayerGlobalId());
      mbPnRegister.setBillSignDate(mbPnRegModel.getBillSignDate());
      mbPnRegister.setBillApplyType(mbPnRegModel.getBillApplyType());
      mbPnRegister.setBillApplyPrefix(mbPnRegModel.getBillApplyPrefix());
      mbPnRegister.setBillApplyNo(mbPnRegModel.getBillApplyNo());
      mbPnRegister.setBillApplyDate(mbPnRegModel.getBillApplyDate());
      mbPnRegister.setSignCcy(mbPnRegModel.getCcySign());
      mbPnRegister.setBillTranAmt(mbPnRegModel.getBillTranAmt());
      mbPnRegister.setPayerBaseAcctNo(mbPnRegModel.getPayerAcctNo());
      mbPnRegister.setPayeeAcctName(mbPnRegModel.getPayeeName());
      mbPnRegister.setPayeeAcctSeqNo(mbPnRegModel.getPayeeAcctSeqNo());
      mbPnRegister.setPayeeAcctCcy(mbPnRegModel.getPayeeAcctCcy());
      mbPnRegister.setPayeeProdType(mbPnRegModel.getPayeeProdeType());
      mbPnRegister.setRemark(mbPnRegModel.getRemark());
      mbPnRegister.setBillSignBranch(mbPnRegModel.getBillSignBranch());
      mbPnRegister.setBillSignUserId(mbPnRegModel.getBillSignUserId());
      mbPnRegister.setAuthUserId(mbPnRegModel.getAuthUser());
      mbPnRegister.setPayerAcctSeqNo(mbPnRegModel.getPayerAcctSeqNo());
      mbPnRegister.setPayeeAcctCcy(mbPnRegModel.getPayerAcctCcy());
      mbPnRegister.setPayeeProdType(mbPnRegModel.getPayerProdeType());
      mbPnRegister.setBillStatus(mbPnRegModel.getBillStatus());
      mbPnRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
      return mbPnRegister;
   }

   public void mbPnRegisterInsert(RbPnRegister mbPnRegister) {
      if (BusiUtil.isNotNull(mbPnRegister)) {
         super.insert(mbPnRegister);
      }

   }

   public void updMbPnRegisterDb(RbPnRegister mbPnRegister) {
      super.update(mbPnRegister);
   }

   public RbPnRegister getMbPdRegisterBySerialNo(String serialNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("serialNo", serialNo);
      RbPnRegister mbPnRegister = (RbPnRegister)this.daoSupport.selectOne(RbPnRegister.class.getName() + ".selectByPrimaryKey", param);
      if (BusiUtil.isNull(mbPnRegister)) {
         throw BusiUtil.createBusinessException("RB4101");
      } else {
         return mbPnRegister;
      }
   }

   public List<RbPnRegister> getMbPdRegisterList(String startDate, String endDate, String billStatus, String branch, String serialNo, String billNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("billStatus", billStatus);
      param.put("branch", branch);
      param.put("serialNo", serialNo);
      param.put("billNo", billNo);
      List<RbPnRegister> mbPnRegisterList = this.daoSupport.selectList(RbPnRegister.class.getName() + ".selectMbPdRegisterList", param);
      return mbPnRegisterList;
   }

   public RbPnRegister getMbPnRegisterByBillNo(String docType, String billNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("docType", docType);
      param.put("billNo", billNo);
      RbPnRegister mbPnRegister = (RbPnRegister)this.daoSupport.selectOne(RbPnRegister.class.getName() + ".selectByBillNo", param);
      if (BusiUtil.isNull(mbPnRegister)) {
         throw BusiUtil.createBusinessException("RB4101");
      } else {
         return mbPnRegister;
      }
   }

   public RbPnRegister getMbPnRegisterByBillType(String billType, String docType, String billNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("billType", billType);
      param.put("docType", docType);
      param.put("billNo", billNo);
      RbPnRegister mbPnRegister = (RbPnRegister)this.daoSupport.selectOne(RbPnRegister.class.getName() + ".selectByBillType", param);
      if (BusiUtil.isNull(mbPnRegister)) {
         throw BusiUtil.createBusinessException("RB4101");
      } else {
         return mbPnRegister;
      }
   }

   public RbPnRegister getRegisterByBillNo(String billStatus, String billNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("billStatus", billStatus);
      param.put("billNo", billNo);
      RbPnRegister mbPnRegister = (RbPnRegister)this.daoSupport.selectOne(RbPnRegister.class.getName() + ".selectRegisterByBillNo", param);
      return mbPnRegister;
   }
}
