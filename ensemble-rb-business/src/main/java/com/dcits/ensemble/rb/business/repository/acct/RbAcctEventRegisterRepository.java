package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.common.constant.EnumTranStatus;
import com.dcits.ensemble.rb.business.common.util.AppHeadUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctEventRegisterRepository extends BusinessRepository {
   @Resource
   private FmBaseStor fmBaseStor;

   public void creatMbAcctRegisterDb(List<RbAcctEventRegister> rbAcctEventRegisters) {
      if (BusiUtil.isNotNull(rbAcctEventRegisters)) {
         for(int i = 0; i < rbAcctEventRegisters.size(); ++i) {
            super.insert((EnsBaseDbBean)rbAcctEventRegisters.get(i));
         }
      }

   }

   public String getMaxSeqNo(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (String)this.daoSupport.selectObject(RbAcctEventRegister.class.getName() + ".selectMaxSeqNoByinternalKey", param);
   }

   public BigDecimal getPreDrawedNumByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (BigDecimal)this.daoSupport.selectObject(RbAcctEventRegister.class.getName() + ".getPreDrawedNumByInternalKey", param);
   }

   public BigDecimal getPartDrawedNumByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (BigDecimal)this.daoSupport.selectObject(RbAcctEventRegister.class.getName() + ".getPartDrawedNumByInternalKey", param);
   }

   public BigDecimal getTranAmtByCycle(Long internalKey, String tranAmt) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("tranAmt", tranAmt);
      return (BigDecimal)this.daoSupport.selectObject(RbAcctEventRegister.class.getName() + ".getTranAmtByCycle", param);
   }

   public RbAcctEventRegister getRegisterByDate(Long internalKey, String tranDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      if (BusiUtil.isNotNull(tranDate)) {
         param.put("tranDate", BusiUtil.convertStr2Date(tranDate));
      }

      List<RbAcctEventRegister> rbAcctEventRegisters = this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getRegisterByDate", param);
      if (BusiUtil.isNotNull(rbAcctEventRegisters)) {
         if (rbAcctEventRegisters.size() > 1) {
            BigDecimal intAmt = (BigDecimal)rbAcctEventRegisters.stream().map(RbAcctEventRegister::getNetInterestAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            RbAcctEventRegister rbAcctEventRegister = (RbAcctEventRegister)rbAcctEventRegisters.get(0);
            rbAcctEventRegister.setNetInterestAmt(intAmt);
            return rbAcctEventRegister;
         }

         if (rbAcctEventRegisters.size() == 1) {
            return (RbAcctEventRegister)rbAcctEventRegisters.get(0);
         }
      }

      return null;
   }

   public List<RbAcctEventRegister> getRegisterByDates(Long internalKey, String tranDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      if (BusiUtil.isNotNull(tranDate)) {
         param.put("tranDate", BusiUtil.convertStr2Date(tranDate));
      }

      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getRegisterByDates", param);
   }

   public List<RbAcctEventRegister> getEventRegisterByDate(Long internalKey, Date tranDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("tranDate", tranDate);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getRegisterByDate", param);
   }

   public List<RbAcctEventRegister> getEventByRefNoAndClientNo(String refNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", refNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getEventByRefNoAndClientNo", param);
   }

   public List<RbAcctEventRegister> getEventByRefNoAndClientNoInternalKey(String refNo, String clientNo, Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", refNo);
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getEventByRefNoAndClientNoInternalKey", param);
   }

   public List<HashMap> getEventByRefNoGroupbyIntClass(String refNo, Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", refNo);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getEventByRefNoGroupbyIntClass", param);
   }

   public List<HashMap> getEventByRefNoAndClientNoGroupbyIntClass(String refNo, Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", refNo);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getEventByRefNoAndClientNoGroupbyIntClass", param);
   }

   public int updateByRefNo(RbAcctEventRegister record) {
      record.setTranTimestamp(BusiUtil.getTranTimestamp26());
      return this.daoSupport.update(RbAcctEventRegister.class.getName() + ".updateByReference", record);
   }

   public String getAddAmtMaxDate(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (String)this.daoSupport.selectObject(RbAcctEventRegister.class.getName() + ".getAddAmtMaxDate", param);
   }

   public List<RbAcctEventRegister> selectByInterbalKey(Long internalKey, String tranDate, String clientNo) {
      Date tranDateD = DateUtil.parseDate(tranDate);
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("tranDate", tranDateD);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".selectByInterbalKey", param);
   }

   public RbAcctEventRegister selectByPrimaryKey(Long internalKey, String seqNo) {
      RbAcctEventRegister rbAcctEventRegister = new RbAcctEventRegister();
      rbAcctEventRegister.setInternalKey(internalKey);
      rbAcctEventRegister.setSeqNo(seqNo);
      return (RbAcctEventRegister)this.daoSupport.selectOne(rbAcctEventRegister);
   }

   public List<RbAcctEventRegister> getByInternalAndCalcTime(long internalKey, Date beginStartDate, Date endStartDate, Date beginEndDate, Date finalEndDate) {
      HashMap<String, Object> map = new HashMap(5);
      map.put("internalKey", internalKey);
      map.put("beginStartDate", beginStartDate);
      map.put("endStartDate", endStartDate);
      map.put("beginEndDate", beginEndDate);
      map.put("finalEndDate", finalEndDate);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".selectByInternalAndCalcTime", map);
   }

   public List<RbAcctEventRegister> queryAcctCaptForEod(Map param) {
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".queryAcctCaptForEod", param);
   }

   public RbAcctEventRegister getRegisterByRefIntClass(Long internalKey, String reference, String intClass) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      param.put("internalKey", internalKey);
      param.put("intClass", intClass);
      return (RbAcctEventRegister)this.daoSupport.selectOne(RbAcctEventRegister.class.getName() + ".getRegisterByRefIntClass", param);
   }

   public List<RbAcctEventRegister> getRbAcctCycleHist(Long internalKey, String clientNo, String userId, String captDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("userId", userId);
      param.put("captDate", BusiUtil.string2Date(captDate));
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getRbAcctCycleHist", param);
   }

   public List<RbAcctEventRegister> getListByAcct(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo, String glPosted, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("cardBaseAcctNo", cardBaseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("glPosted", glPosted);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getListByAcct", param);
   }

   public List<String> getReFerence(String clientNo, String startDate, String endDate, String internalKey) {
      Map<String, Object> param = new HashMap(16);
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("clientNo", clientNo);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getListRefence", param);
   }

   public List<RbAcctEventRegister> getZxqylist(String actualBaseAcctNo, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("actualBaseAcctNo", actualBaseAcctNo);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getZxqylist", param);
   }

   public List<RbAcctEventRegister> getByInternalKeyAndTranDate(String baseAcctNo, String prodType, String internalKey, String startDate, String endDate, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("internalKey", Long.valueOf(internalKey));
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getByInternalKeyAndTranDate", param);
   }

   public List<RbAcctEventRegister> getByInternalKeyAndTranDateByPage(String baseAcctNo, String startDate, String endDate, String clientNo, String prodType, String term, String termType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("clientNo", clientNo);
      param.put("term", term);
      param.put("termType", termType);
      RowArgs rowArgs = AppHeadUtil.getRowArgs();
      String statementPostfix = RbAcctEventRegister.class.getName() + ".getByInternalKeyAndTranDate";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctEventRegister> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public List<RbAcctEventRegister> getByInternalKeyAndTranDateInt(String clientNo, String startDate, String endDate, String internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getByInternalKeyAndTranDateInt", param);
   }

   public List<RbAcctEventRegister> getByInternalKeyAndTranDateIntN(String clientNo, String startDate, String endDate, String internalKey, String reference) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("internalKey", Long.valueOf(internalKey));
      param.put("reference", reference);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getByInternalKeyAndTranDateIntN", param);
   }

   public List<RbAcctEventRegister> getByInternalKeyAndTranDateInt(String clientNo, String startDate, String endDate, Long internalKey, String reference) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("internalKey", internalKey);
      param.put("reference", reference);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getByInternalKeyAndTranDateIntN", param);
   }

   public List<RbAcctEventRegister> getRegisterByReference(String reference, String tranDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      if (BusiUtil.isNotNull(tranDate)) {
         param.put("tranDate", BusiUtil.convertStr2Date(tranDate));
      }

      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getRegisterByReference", param);
   }

   public List<RbAcctEventRegister> getRegisterByInternalKey(String internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getRegisterByInternalKey", param);
   }

   public List<RbAcctEventRegister> getRbAcctEventRegister(String clientNo, String internalKey, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbAcctEventRegister.class.getName() + ".getRbAcctEventRegister", param);
   }

   public RbAcctEventRegister selectByReference(String clientNo, String reference) {
      RbAcctEventRegister rbAcctEventRegister = new RbAcctEventRegister();
      rbAcctEventRegister.setClientNo(clientNo);
      rbAcctEventRegister.setReference(reference);
      return (RbAcctEventRegister)this.daoSupport.selectOne(rbAcctEventRegister);
   }

   public List<RbAcctEventRegister> selectByReferenceList(String clientNo, String reference) {
      RbAcctEventRegister rbAcctEventRegister = new RbAcctEventRegister();
      rbAcctEventRegister.setClientNo(clientNo);
      rbAcctEventRegister.setReference(reference);
      return this.daoSupport.selectList(rbAcctEventRegister);
   }

   public List<RbAcctEventRegister> selectByReferenceList(String clientNo, String reference, String tranStatus) {
      RbAcctEventRegister rbAcctEventRegister = new RbAcctEventRegister();
      rbAcctEventRegister.setClientNo(clientNo);
      rbAcctEventRegister.setReference(reference);
      rbAcctEventRegister.setTranStatus(tranStatus);
      return this.daoSupport.selectList(rbAcctEventRegister);
   }

   public RbAcctEventRegister getLastEventRegister(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (RbAcctEventRegister)this.daoSupport.selectOne(RbAcctEventRegister.class.getName() + ".getLastEventRegister", param);
   }

   public RbAcctEventRegister getLastEventRegister(Long internalKey, String intClass) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("intClass", intClass);
      return (RbAcctEventRegister)this.daoSupport.selectOne(RbAcctEventRegister.class.getName() + ".getLastEventRegister", param);
   }

   public void updateTranStatusByReference(String reference, String clientNo, Long internalKey) {
      RbAcctEventRegister rbAcctEventRegister = new RbAcctEventRegister();
      rbAcctEventRegister.setReference(reference);
      rbAcctEventRegister.setClientNo(clientNo);
      rbAcctEventRegister.setInternalKey(internalKey);
      rbAcctEventRegister.setTranStatus(EnumTranStatus.TRAN_REV.toString());
      this.daoSupport.update(RbAcctEventRegister.class.getName() + ".updateTranStatusByReference", rbAcctEventRegister);
   }

   public RbAcctEventRegister selectByBaseAcctNo(String baseAcctNo) {
      RbAcctEventRegister rbAcctEventRegister = new RbAcctEventRegister();
      rbAcctEventRegister.setBaseAcctNo(baseAcctNo);
      return (RbAcctEventRegister)this.daoSupport.selectOne(rbAcctEventRegister);
   }
}
