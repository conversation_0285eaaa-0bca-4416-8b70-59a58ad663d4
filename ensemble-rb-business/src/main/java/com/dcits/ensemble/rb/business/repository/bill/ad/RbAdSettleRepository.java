package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.bc.component.ad.sequence.MbSettleSeq;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdSettle;
import com.dcits.ensemble.rb.business.model.bill.ad.BondArray;
import com.dcits.ensemble.rb.business.repository.acct.RbAcctRepository;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAdSettleRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAdSettleRepository.class);
   @Resource
   private RbAcctRepository mbAcctRepository;

   public void updateByPrimaryKey(RbAdSettle rbAdSettle) {
      super.update(rbAdSettle);
   }

   public void adSettleInsert(List<RbAdSettle> rbAdSettleList) {
      RbAdSettle rbAdSettle;
      if (BusiUtil.isNotNull(rbAdSettleList)) {
         for(Iterator var2 = rbAdSettleList.iterator(); var2.hasNext(); super.insert(rbAdSettle)) {
            rbAdSettle = (RbAdSettle)var2.next();
            if (BusiUtil.isNull(rbAdSettle.getSerialNo())) {
               new MbSettleSeq();
               rbAdSettle.setSerialNo((String)SequenceGenerator.nextValue(SequenceEnum.settleSeqNo));
            }
         }
      }

   }

   public void adSettleInsertOne(RbAdSettle rbAdSettle) {
      if (BusiUtil.isNull(rbAdSettle.getSerialNo())) {
         rbAdSettle.setSerialNo((String)SequenceGenerator.nextValue(SequenceEnum.settleSeqNo));
      }

      super.insert(rbAdSettle);
   }

   public void deleteMbAdSettle(String reference) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      this.daoSupport.delete(RbAdSettle.class.getName() + ".deleteByReference", param);
   }

   public void deleteBond(String baseAcctNo, String acctType, String ccy, String seqNo, Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("acctNo", baseAcctNo);
      param.put("prodType", acctType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", seqNo);
      param.put("internalKey", internalKey);
      this.daoSupport.delete(RbAdSettle.class.getName() + ".deleteByAcct", param);
   }

   public List<RbAdSettle> queryMbAdSettle(String reference, String acceptContractNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      param.put("acceptContractNo", acceptContractNo);
      List<RbAdSettle> mbAdRegisters = this.daoSupport.selectList(RbAdSettle.class.getName() + ".queryMbAdSettle", param);
      if (BusiUtil.isNull(mbAdRegisters)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return mbAdRegisters;
      }
   }

   public RbAdSettle selectMbAdSettle(String acceptContractNo, String baseAcctNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acceptContractNo", acceptContractNo);
      RbAdSettle rbAdSettle = (RbAdSettle)this.daoSupport.selectOne(RbAdSettle.class.getName() + ".selectMbAdSettle", param);
      if (BusiUtil.isNull(rbAdSettle)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdSettle;
      }
   }

   public RbAdSettle selectMbAdSettleBySettleAcctNo(Long internalKey, String baseAcctNo, String settleAcctType) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("internalKey", internalKey);
      param.put("settleAcctType", settleAcctType);
      RbAdSettle rbAdSettle = (RbAdSettle)this.daoSupport.selectOne(RbAdSettle.class.getName() + ".selectMbAdSettleBySettleAcctNo", param);
      if (BusiUtil.isNull(rbAdSettle)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdSettle;
      }
   }

   public List<RbAdSettle> selectMbAdSettleByInternalKey(Long internalKey, String settleAcctType) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("settleAcctType", settleAcctType);
      List<RbAdSettle> rbAdSettles = this.daoSupport.selectList(RbAdSettle.class.getName() + ".selectMbAdSettleByInternalKey", param);
      if (BusiUtil.isNull(rbAdSettles)) {
         throw BusiUtil.createBusinessException("AD4101");
      } else {
         return rbAdSettles;
      }
   }

   public List<RbAdSettle> getMbAdSettle(String reference, String settleAcctType) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      param.put("settleAcctType", settleAcctType);
      return this.daoSupport.selectList(RbAdSettle.class.getName() + ".getMbAdSettle", param);
   }

   public RbAdSettle setSettleInfo1(BondArray bondArray) {
      RbAdSettle rbAdSettleModel = new RbAdSettle();
      RbAcct rbAcct = this.mbAcctRepository.getMbSingleAcct(bondArray.getBondAcctNo(), bondArray.getBondProdType(), bondArray.getBondAcctCcy(), bondArray.getBondAcctSeqNo());
      long serial_no = (new MbSettleSeq()).generateSeqNo();
      rbAdSettleModel.setSerialNo((String)SequenceGenerator.nextValue(SequenceEnum.settleSeqNo));
      rbAdSettleModel.setBaseAcctNo(bondArray.getBondAcctNo());
      rbAdSettleModel.setInternalKey(rbAcct.getInternalKey());
      rbAdSettleModel.setAcctSeqNo(bondArray.getBondAcctSeqNo());
      rbAdSettleModel.setProdType(bondArray.getBondProdType());
      rbAdSettleModel.setAcctCcy(bondArray.getBondAcctCcy());
      rbAdSettleModel.setClientNo(rbAcct.getClientNo());
      rbAdSettleModel.setClientName(rbAcct.getAcctName());
      rbAdSettleModel.setSettleAcctType("BOND");
      rbAdSettleModel.setTranAmt(bondArray.getBailAmount());
      rbAdSettleModel.setNeedSettleAmt(bondArray.getBailAmount());
      rbAdSettleModel.setSettleAmt(BigDecimal.ZERO);
      rbAdSettleModel.setTotalAmt(bondArray.getBailAmount());
      rbAdSettleModel.setTranDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      rbAdSettleModel.setLastProcStamp(BusiUtil.getTranTimestamp26());
      rbAdSettleModel.setTranBranch(Context.getInstance().getBranchId());
      return rbAdSettleModel;
   }
}
