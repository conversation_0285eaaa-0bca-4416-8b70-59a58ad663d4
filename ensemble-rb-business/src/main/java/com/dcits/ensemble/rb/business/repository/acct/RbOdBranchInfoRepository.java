package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOdBranchInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbOdBranchInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbOdBranchInfoRepository.class);

   public void updateByBranch(RbOdBranchInfo rbOdBranchInfo) {
      Map<String, Object> param = new HashMap();
      param.put("branch", rbOdBranchInfo.getBranch());
      param.put("userdAmt", rbOdBranchInfo.getUsedAmt());
      this.daoSupport.update(RbOdBranchInfo.class.getName() + ".updateByBranch", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateByBranchTransaction(RbOdBranchInfo rbOdBranchInfo) {
      Map<String, Object> param = new HashMap();
      param.put("branch", rbOdBranchInfo.getBranch());
      param.put("userdAmt", rbOdBranchInfo.getUsedAmt());
      this.daoSupport.update(RbOdBranchInfo.class.getName() + ".updateByBranch", param);
   }

   public RbOdBranchInfo getMsg(String branchId) {
      RbOdBranchInfo rbOdBranchInfo = new RbOdBranchInfo();
      rbOdBranchInfo.setBranch(branchId);
      return (RbOdBranchInfo)this.daoSupport.selectOne(rbOdBranchInfo);
   }
}
