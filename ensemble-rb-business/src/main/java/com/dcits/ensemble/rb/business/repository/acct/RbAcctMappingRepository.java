package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctMapping;
import com.dcits.ensemble.repository.BusinessRepository;
import javax.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctMappingRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctMappingRepository.class);

   public RbAcctMapping getRbAcctMapping(@NotNull String baseAcctNo, @NotNull String clientNo) {
      RbAcctMapping query = new RbAcctMapping();
      query.setBaseAcctNo(baseAcctNo);
      query.setClientNo(clientNo);
      return (RbAcctMapping)this.daoSupport.selectOne(query);
   }
}
