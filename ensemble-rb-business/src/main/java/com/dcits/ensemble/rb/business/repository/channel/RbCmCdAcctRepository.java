package com.dcits.ensemble.rb.business.repository.channel;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbCmCdAcct;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RbCmCdAcctRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbCmCdAcctRepository.class);

   public RbCmCdAcct selectByPrimaryKey(String subAcctNo, String baseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      return null;
   }

   public void createRbCmCdAcct(RbCmCdAcct rbCmCdAcct) {
      super.insert(rbCmCdAcct);
   }

   public void updateRbCmCdAcct(RbCmCdAcct rbCmCdAcct) {
      if (BusiUtil.isNotNull(rbCmCdAcct)) {
         super.update(rbCmCdAcct);
      }

   }

   public void deleteBySubAcctNoAndBaseAcctNo(String subAcctNo, String baseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      param.put("baseAcctNo", baseAcctNo);
      this.daoSupport.selectOne(RbCmCdAcct.class.getName() + ".deleteBySubAcctNoAndBaseAcctNo", param);
   }

   public RbCmCdAcct selectRbCmCdAcctBySubAcctNo(String subAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      return null;
   }

   public RbCmCdAcct selectRbCmCdAcctByStatusFlag1(String subAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      return null;
   }

   public RbCmCdAcct selectRbCmCdAcctByStatusFlag2(String subAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      return null;
   }

   public RbCmCdAcct selectRbCmCdAcctByStatusFlag3(String subAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("subAcctNo", subAcctNo);
      return null;
   }

   public RbCmCdAcct selectRbCmCdAcctByBaseAcctNo(String baseAcctNo) {
      Map<String, Object> param = new HashMap(5);
      param.put("baseAcctNo", baseAcctNo);
      return null;
   }
}
