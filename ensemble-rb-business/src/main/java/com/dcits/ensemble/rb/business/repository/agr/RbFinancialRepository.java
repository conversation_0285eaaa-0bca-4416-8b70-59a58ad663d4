package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcctRegister;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFinancialRepository extends BusinessRepository {
   private static final String MAX_SEQ_NO = "MAX_SEQ_NO";

   public void createRbFinancialAcctRegister(RbFinancialAcctRegister rbFinancialAcctRegister) {
      super.insert(rbFinancialAcctRegister);
   }

   public int getMaxSeqNo(String baseAcctNo, String prodType, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("clientNo", clientNo);
      HashMap<String, Object> ret = (HashMap)this.daoSupport.selectObject(RbFinancialAcctRegister.class.getName() + ".getMaxSeqNo", param);
      if (BusiUtil.isNotNull(ret)) {
         if (ret.get("MAX_SEQ_NO") instanceof Double) {
            Double seqNo = (Double)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }

         if (ret.get("MAX_SEQ_NO") instanceof BigDecimal) {
            BigDecimal seqNo = (BigDecimal)ret.get("MAX_SEQ_NO");
            return seqNo.intValue();
         }
      }

      return 0;
   }

   public void updateRbFinancialAcctRegister(RbFinancialAcctRegister rbFinancialAcctRegister) {
      super.update(rbFinancialAcctRegister);
   }

   public RbFinancialAcctRegister getFinRegisterByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbFinancialAcctRegister)this.daoSupport.selectOne(RbFinancialAcctRegister.class.getName() + ".getFinRegisterByInternalKey", param);
   }

   public List<RbFinancialAcctRegister> selectListByParentInternalKeyAndAgreementId(Long parentInternalKey, String agreementId, String acctStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("parentInternalKey", parentInternalKey);
      param.put("agreementId", agreementId);
      param.put("acctStatus", acctStatus);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".selectListByParentInternalKeyAndAgreementId", param);
   }

   public List<RbFinancialAcctRegister> selectListByParentInternalKeyAndAgreementIdLiFo(Long parentInternalKey, String agreementId, String acctStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("parentInternalKey", parentInternalKey);
      param.put("agreementId", agreementId);
      param.put("acctStatus", acctStatus);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".selectListByParentInternalKeyAndAgreementId", param);
   }

   public RbFinancialAcctRegister getFinRegisterByParentKeyAndSeqNo(Long internalKey, String clientNo, String seqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("seqNo", seqNo);
      return (RbFinancialAcctRegister)this.daoSupport.selectOne(RbFinancialAcctRegister.class.getName() + ".getFinRegisterByParentKeyAndSeqNo", param);
   }

   public List<RbFinancialAcctRegister> getFinRegisterByParentKeyAndStatus(Long parentInternalKey, String clientNo, String acctStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put("parentInternalKey", parentInternalKey);
      param.put("clientNo", clientNo);
      param.put("acctStatus", acctStatus);
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".getFinRegisterByParentKeyAndStatus", param);
   }

   public BigDecimal getCountTotalAmountByParentInternalKeyAndAgreementId(Long parentInternalKey, String agreementId, String acctStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("parentInternalKey", parentInternalKey);
      param.put("agreementId", agreementId);
      param.put("acctStatus", acctStatus);
      param.put("clientNo", clientNo);
      return (BigDecimal)this.daoSupport.selectObject(RbFinancialAcctRegister.class.getName() + ".getCountTotalAmountByParentInternalKeyAndAgreementId", param);
   }

   public BigDecimal getTotalIntByParentInternalKeyAndAgreementId(Long parentInternalKey, String agreementId, String acctStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("parentInternalKey", parentInternalKey);
      param.put("agreementId", agreementId);
      param.put("acctStatus", acctStatus);
      param.put("clientNo", clientNo);
      return (BigDecimal)this.daoSupport.selectObject(RbFinancialAcctRegister.class.getName() + ".getTotalIntByParentInternalKeyAndAgreementId", param);
   }

   public RbFinancialAcctRegister getFinRegisterByInternalKeyForLock(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (RbFinancialAcctRegister)this.daoSupport.selectOne(RbFinancialAcctRegister.class.getName() + ".getFinRegisterByInternalKeyForLock", param);
   }

   public List<RbFinancialAcctRegister> getRbNoteList(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("acctStatus", "A");
      return this.daoSupport.selectList(RbFinancialAcctRegister.class.getName() + ".getRbNoteList", param);
   }
}
