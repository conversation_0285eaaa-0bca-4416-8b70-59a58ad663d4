package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortUpdateHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFeeAmortUpdateHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbFeeAmortUpdateHistRepository.class);

   public List<RbFeeAmortUpdateHist> selectByChangeDate(String scSeqNo, String startDate, String endDate) {
      Map<String, Object> map = new HashMap();
      map.put("scSeqNo", scSeqNo);
      map.put("startDate", DateUtil.parseDate(startDate));
      map.put("endDate", DateUtil.parseDate(endDate));
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbFeeAmortUpdateHist.class.getName() + ".selectByChangeDate";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbFeeAmortUpdateHist> queryResult = this.daoSupport.selectQueryResult(statementPostfix, map, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, map);
      }
   }
}
