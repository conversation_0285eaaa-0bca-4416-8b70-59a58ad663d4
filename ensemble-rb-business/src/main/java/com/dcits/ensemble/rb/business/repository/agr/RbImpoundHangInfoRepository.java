package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundHangInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbImpoundHangInfoRepository extends BusinessRepository {
   public List<RbImpoundHangInfo> getMbImpoundHangInfoByCondition(String baseAcctNo, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("payAcctNo", baseAcctNo);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      return this.daoSupport.selectList(RbImpoundHangInfo.class.getName() + ".getMbImpoundHangInfoByCondition", param);
   }

   public RbImpoundHangInfo getMbImpoundHangInfoByReference(String reference) {
      RbImpoundHangInfo rbImpoundHangInfo = new RbImpoundHangInfo();
      rbImpoundHangInfo.setReference(reference);
      return (RbImpoundHangInfo)this.daoSupport.selectOne(rbImpoundHangInfo);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void createImpoundInfo(RbImpoundHangInfo rbImpoundHangInfo) {
      super.insert(rbImpoundHangInfo);
   }
}
