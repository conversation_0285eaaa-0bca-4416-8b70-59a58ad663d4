package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContrast;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import com.dcits.ensemble.util.DateUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctContrastRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctContrastRepository.class);

   public RbAcctContrast getMbAcctContrast(String oldAcctNo) {
      RbAcctContrast rbAcctContrast = new RbAcctContrast();
      rbAcctContrast.setOldAcctNo(oldAcctNo);
      return (RbAcctContrast)this.daoSupport.selectOne(rbAcctContrast);
   }

   public RbAcctContrast selectByBaseAcctNo(String baseAcctNo) {
      RbAcctContrast rbAcctContrast = new RbAcctContrast();
      rbAcctContrast.setBaseAcctNo(baseAcctNo);
      return (RbAcctContrast)this.daoSupport.selectOne(rbAcctContrast);
   }

   public void updateQueryTime(String oldAcctNo, Date queryTime) {
      RbAcctContrast rbAcctContrast = new RbAcctContrast();
      rbAcctContrast.setOldAcctNo(oldAcctNo);
      rbAcctContrast.setQueryTime(DateUtil.formatDate(queryTime, "yyyy-MM-dd HH:mm:ss.SSSSSS"));
      super.update(rbAcctContrast);
   }

   public void updateQueryTimeByBaseAcctNo(String baseAcctNo, Date queryTime, String company) {
      Map<String, Object> param = new HashMap();
      param.put("queryTime", queryTime);
      param.put("baseAcctNo", baseAcctNo);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      param.put("company", company);
      this.daoSupport.update(RbAcctContrast.class.getName() + ".updateByBaseAcctNo", param);
   }
}
