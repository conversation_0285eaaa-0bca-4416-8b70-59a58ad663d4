package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.CdSafeLock;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class CdSafeLockRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(CdSafeLockRepository.class);

   public CdSafeLock getByCardNo(String cardNo) {
      CdSafeLock cdSafeLock = new CdSafeLock();
      cdSafeLock.setCardNo(cardNo);
      return (CdSafeLock)this.daoSupport.selectOne(cdSafeLock);
   }

   public int insertByCardNo(String cardNo) {
      CdSafeLock cdSafeLock = new CdSafeLock();
      cdSafeLock.setCardNo(cardNo);
      return this.daoSupport.insert(cdSafeLock);
   }
}
