package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.router.RouterCalc;
import com.dcits.ensemble.dbmanage.router.RouterFields;
import com.dcits.ensemble.dbmanage.router.RouterKeys;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementImpound;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementImpoundRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementImpoundRepository.class);

   public void createMbAgreementImpoundDb(RbAgreementImpound rbAgreementImpound) {
      if (BusiUtil.isNotNull(rbAgreementImpound)) {
         RouterFields routerFields = RouterCalc.getRouterField(RouterKeys.internalKey, rbAgreementImpound.getInternalKey());
         super.insert(rbAgreementImpound);
      }

   }

   public void updateByPrimaryKey(RbAgreementImpound rbAgreementImpound) {
      super.update(rbAgreementImpound);
   }

   public void updateMbAgreementImpoundByAgreementId(RbAgreementImpound rbAgreementImpound) {
      rbAgreementImpound.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAgreementImpound.class.getName() + ".updateMbAgreementImpoundByAgreementId", rbAgreementImpound);
   }

   public List<RbAgreementImpound> getMbAgreementImpoundByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementImpound.class.getName() + ".getMbAgreementImpoundList", param);
   }

   public List<RbAgreementImpound> getMbAgreementImpoundByBenefitSide(String benefitBaseAcctNo, String benefitProdType, String benefnitSeqNo, String benenfitCCY) {
      Map<String, Object> param = new HashMap();
      param.put("benefitBaseAcctNo", benefitBaseAcctNo);
      param.put("benefitProdType", benefitProdType);
      param.put("benenfitSeqNo", benefnitSeqNo);
      param.put("benenfitCCY", benenfitCCY);
      return this.daoSupport.selectList(RbAgreementImpound.class.getName() + ".getMbAgreementImpoundByBenefitSide", param);
   }

   public List<RbAgreementImpound> selectPCDAgreement() {
      String runDate = Context.getInstance().getRunDate();
      log.debug("RUNDATEEEEEEEE Start..." + runDate);
      Map<String, Object> param = new HashMap();
      param.put("runDate", runDate);
      return this.daoSupport.selectList(RbAgreementImpound.class.getName() + ".selectPCDAgreementForDetail", param);
   }

   public List<String> selectPCDAgreementForKeys(Map param) {
      return this.daoSupport.selectList(RbAgreementImpound.class.getName() + ".selectPCDAgreementForKeys", param);
   }
}
