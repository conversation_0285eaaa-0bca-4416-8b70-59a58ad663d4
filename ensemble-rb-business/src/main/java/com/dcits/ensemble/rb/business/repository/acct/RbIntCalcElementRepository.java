package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElement;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbIntCalcElementHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbIntCalcElementRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbIntCalcElementRepository.class);

   public void backInsert(RbIntCalcElement mbIntCalcElement) {
      RbIntCalcElementHist rbIntCalcElementHist = new RbIntCalcElementHist();
      BeanUtil.copy(mbIntCalcElement, rbIntCalcElementHist);
      super.insert(rbIntCalcElementHist);
   }

   public RbIntCalcElement selectByCalcClass(Long internalKey, String model, String scene, String clientNo) {
      RbIntCalcElement mbIntCalcElement = new RbIntCalcElement();
      mbIntCalcElement.setIntCalcClass(model + "_" + scene);
      mbIntCalcElement.setInternalKey(internalKey);
      mbIntCalcElement.setClientNo(clientNo);
      return (RbIntCalcElement)this.daoSupport.selectOne(mbIntCalcElement);
   }

   public RbIntCalcElement selectByCalcClass(Long internalKey, String intCalcClass) {
      RbIntCalcElement mbIntCalcElement = new RbIntCalcElement();
      mbIntCalcElement.setInternalKey(internalKey);
      mbIntCalcElement.setIntCalcClass(intCalcClass);
      return (RbIntCalcElement)this.daoSupport.selectOne(mbIntCalcElement);
   }

   public void updateMbIntCalcElement(RbIntCalcElement mbIntCalcElement) {
      this.daoSupport.update(mbIntCalcElement);
   }

   public void createMbIntCalcElement(RbIntCalcElement mbIntCalcElement) {
      super.insert(mbIntCalcElement);
   }

   public RbIntCalcElement selectElementHistByCalcClass(Long internalKey, String model, String scene, Date lastChangeDate, String company) {
      RbIntCalcElement mbIntCalcElement = new RbIntCalcElement();
      mbIntCalcElement.setIntCalcClass(model + "_" + scene);
      mbIntCalcElement.setInternalKey(internalKey);
      mbIntCalcElement.setLastChangeDate(lastChangeDate);
      mbIntCalcElement.setCompany(company);
      return (RbIntCalcElement)this.daoSupport.selectOne(RbIntCalcElement.class.getName() + ".selectElementHistByCalcClass", mbIntCalcElement);
   }
}
