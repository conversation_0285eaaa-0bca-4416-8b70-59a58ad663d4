package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBabBillDtl;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbBabBillDtlRepository extends BusinessRepository {
   public RbBabBillDtl selectDtlByAcceptContractNoTC(String acceptContractNo, String flag) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("babFlag", flag);
      return (RbBabBillDtl)this.daoSupport.selectOne(RbBabBillDtl.class.getName() + ".selectDtlByAcceptContractNoTC", para);
   }

   public RbBabBillDtl selectDtlByReferenceTC(String reference, String flag) {
      Map<String, Object> para = new HashMap(16);
      para.put("reference", reference);
      para.put("babFlag", flag);
      return (RbBabBillDtl)this.daoSupport.selectOne(RbBabBillDtl.class.getName() + ".selectDtlByReferenceTC", para);
   }

   public List<RbBabBillDtl> selectDtlByAcceptContractNoD(String acceptContractNo, String flag) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", acceptContractNo);
      para.put("babFlag", flag);
      return this.daoSupport.selectList(RbBabBillDtl.class.getName() + ".selectDtlByAcceptContractNoD", para);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertRbBabBillDtl(RbBabBillDtl rbBabBillDtl) {
      super.insert(rbBabBillDtl);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertRbBabBillDtlList(List<RbBabBillDtl> rbBabBillDtlList) {
      super.insert(rbBabBillDtlList);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void udpateRbBabBillDtl(RbBabBillDtl rbBabBillDtl) {
      Map<String, Object> para = new HashMap(16);
      para.put("acceptContractNo", rbBabBillDtl.getAcceptContractNo());
      para.put("babFlag", rbBabBillDtl.getBabFlag());
      para.put("clientNo", rbBabBillDtl.getClientNo());
      para.put("advanceReference", rbBabBillDtl.getAdvanceReference());
      para.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbBabBillDtl.class.getName() + ".updateByAcceptContractNoD", para);
   }
}
