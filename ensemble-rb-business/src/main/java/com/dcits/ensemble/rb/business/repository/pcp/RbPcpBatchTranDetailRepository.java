package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpBatchTranDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RbPcpBatchTranDetailRepository extends BusinessRepository<RbPcpBatchTranDetail> {
   private static final Logger log = LoggerFactory.getLogger(RbPcpBatchTranDetailRepository.class);

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateNewTransaction(RbPcpBatchTranDetail rbPcpBatchTranDetail) {
      this.update(rbPcpBatchTranDetail);
   }

   public List<RbPcpBatchTranDetail> selectByBaseAcctNo(String baseAcctNo, Date startDate, Date endDate, BigDecimal startAmt, BigDecimal endAmt, String upDownType) {
      Map<String, Object> map = new HashMap();
      map.put("baseAcctNo", baseAcctNo);
      map.put("startDate", startDate);
      map.put("endDate", endDate);
      map.put("startAmt", startAmt);
      map.put("endAmt", endAmt);
      map.put("upDownType", upDownType);
      return this.daoSupport.selectList(RbPcpBatchTranDetail.class.getName() + ".selectByBaseAcctNo", map);
   }

   public List<RbPcpBatchTranDetail> selectByBatchNo(String batchNo, Date startDate, Date endDate, BigDecimal startAmt, BigDecimal endAmt, String upDownType) {
      Map<String, Object> map = new HashMap();
      map.put("batchNo", batchNo);
      map.put("startDate", startDate);
      map.put("endDate", endDate);
      map.put("startAmt", startAmt);
      map.put("endAmt", endAmt);
      map.put("upDownType", upDownType);
      return this.daoSupport.selectList(RbPcpBatchTranDetail.class.getName() + ".selectByBatchNo", map);
   }
}
