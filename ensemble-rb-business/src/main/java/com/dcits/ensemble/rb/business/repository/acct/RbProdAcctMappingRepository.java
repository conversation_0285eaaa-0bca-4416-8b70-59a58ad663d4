package com.dcits.ensemble.rb.business.repository.acct;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctMapping;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbProdAcctMappingRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbProdAcctMappingRepository.class);

   @Cached(
      area = "middleArea",
      name = "rb:param:rb_prod_acct_mapping:prod_mapping:",
      key = "#prodType+'-'+#branch+'-'+#acctCcy+'-'+#acctSeqNo+'-'+#company",
      cacheType = CacheType.REMOTE
   )
   public RbProdAcctMapping getrbProdAcctMapping(@NotNull String prodType, String branch, @NotNull String acctCcy, String acctSeqNo, String company) {
      RbProdAcctMapping rbProdAcctMapping = new RbProdAcctMapping();
      rbProdAcctMapping.setProdType(prodType);
      rbProdAcctMapping.setBranch(branch);
      rbProdAcctMapping.setAcctCcy(acctCcy);
      rbProdAcctMapping.setAcctSeqNo(acctSeqNo);
      rbProdAcctMapping.setCompany(company);
      return (RbProdAcctMapping)this.daoSupport.selectOne(rbProdAcctMapping);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertInfo(RbProdAcctMapping rbProdAcctMapping) {
      super.insert(rbProdAcctMapping);
   }

   public void updateByPrimaryKey(RbProdAcctMapping rbProdAcctMapping) {
      super.update(rbProdAcctMapping);
   }

   public List<RbProdAcctMapping> getRbProdAcctMappingList(String prodType, String branch, String acctCcy) {
      RbProdAcctMapping rbProdAcctMapping = new RbProdAcctMapping();
      rbProdAcctMapping.setProdType(prodType);
      rbProdAcctMapping.setBranch(branch);
      rbProdAcctMapping.setAcctCcy(acctCcy);
      return this.daoSupport.selectList(rbProdAcctMapping);
   }
}
