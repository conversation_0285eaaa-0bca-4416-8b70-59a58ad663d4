package com.dcits.ensemble.rb.business.repository.acct;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.AcSubject;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class AcSubjectRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(AcSubjectRepository.class);

   @Cached(
      area = "longArea",
      name = "rb:param:ac_subject:subject_Code:",
      key = "#subjectCode",
      cacheType = CacheType.REMOTE
   )
   public AcSubject selectByPrimaryKey(String subjectCode) {
      AcSubject acSubject = new AcSubject();
      acSubject.setSubjectCode(subjectCode);
      return (AcSubject)this.daoSupport.selectOne(acSubject);
   }
}
