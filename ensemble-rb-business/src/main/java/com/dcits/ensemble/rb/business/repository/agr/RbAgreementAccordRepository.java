package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAccord;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementAccordRepository extends BusinessRepository {
   public void createMbAgreementAccord(RbAgreementAccord rbAgreementAccord) {
      if (BusiUtil.isNotNull(rbAgreementAccord)) {
         super.insert(rbAgreementAccord);
      }

   }

   public void updateByPrimaryKey(RbAgreementAccord rbAgreementAccord) {
      super.update(rbAgreementAccord);
   }

   /** @deprecated */
   @Deprecated
   public RbAgreementAccord getMbAgreementAccord(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return (RbAgreementAccord)this.daoSupport.selectOne(RbAgreementAccord.class.getName() + ".getMbAgreementAccord", param);
   }

   public void updateMbAgreementAccord(RbAgreementAccord rbAgreementAccord) {
      super.update(rbAgreementAccord);
   }

   public void deleteMbAgreementAccordDb(String agreementId, String clientNo) {
      RbAgreementAccord rbAgreementAccord = new RbAgreementAccord();
      rbAgreementAccord.setAgreementId(agreementId);
      rbAgreementAccord.setClientNo(clientNo);
      this.daoSupport.delete(rbAgreementAccord);
   }

   public List<RbAgreementAccord> getAccordInfo(String agreementId) {
      RbAgreementAccord rbAgreementAccord = new RbAgreementAccord();
      rbAgreementAccord.setAgreementId(agreementId);
      return this.daoSupport.selectList(rbAgreementAccord);
   }

   public List<RbAgreementAccord> getMbAgreementAcc(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementType", agreementType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementAccord.class.getName() + ".selectAgrByType", param);
   }

   public List<RbAgreementAccord> getRbAgreementAccordList(String internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementAccord.class.getName() + ".selectAgreementAccordInfo", param);
   }

   public RbAgreementAccord getRbAgreementAccord(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      return (RbAgreementAccord)this.daoSupport.selectOne(RbAgreementAccord.class.getName() + ".selectOne", param);
   }

   public RbAgreementAccord getMbAgreementAccOne(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      List<RbAgreementAccord> list = this.daoSupport.selectList(RbAgreementAccord.class.getName() + ".selectAgrByType", param);
      return BusiUtil.isNotNull(list) ? (RbAgreementAccord)list.get(0) : null;
   }

   public RbAgreementAccord getMbAgreementAccByIntRateFormNo(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String intRateFormNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("intRateFormNo", intRateFormNo);
      return (RbAgreementAccord)this.daoSupport.selectOne(RbAgreementAccord.class.getName() + ".selectAgrByFormNo", param);
   }

   public List<RbAgreementAccord> getIntRateForm(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementAccord.class.getName() + ".getIntRateForm", param);
   }

   public List<RbAgreementAccord> getDaysBefore(Date targetDate) {
      Map<String, Object> param = new HashMap();
      param.put("targetDate", targetDate);
      return this.daoSupport.selectList(RbAgreementAccord.class.getName() + ".getDaysBefore", param);
   }

   public RbAgreementAccord getAgreementAcordInfo(String baseAcctNo, String acctCcy, String acctSeqNo, String prodType) {
      RbAgreementAccord rbAgreementAccord = new RbAgreementAccord();
      rbAgreementAccord.setProdType(prodType);
      rbAgreementAccord.setAcctCcy(acctCcy);
      rbAgreementAccord.setBaseAcctNo(baseAcctNo);
      rbAgreementAccord.setAcctSeqNo(acctSeqNo);
      rbAgreementAccord.setAgreementStatus("A");
      return (RbAgreementAccord)this.daoSupport.selectOne(rbAgreementAccord);
   }
}
