package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.router.RouterCalc;
import com.dcits.ensemble.dbmanage.router.RouterFields;
import com.dcits.ensemble.dbmanage.router.RouterKeys;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbClientChargeAgrt;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbClientChargeAgrtRepository extends BusinessRepository {
   public void creatMbClientChargeAgrt(RbClientChargeAgrt rbClientChargeAgrt) {
      super.insert(rbClientChargeAgrt);
   }

   public void creatMbClientChargeAgrt(Long internalKey, RbClientChargeAgrt rbClientChargeAgrt) {
      RouterFields routerFields = RouterCalc.getRouterField(RouterKeys.internalKey, internalKey);
      super.insert(rbClientChargeAgrt);
   }

   public void updateMbClientChargeAgrt(RbClientChargeAgrt rbClientChargeAgrt) {
      super.update(rbClientChargeAgrt);
   }

   public void updateRbClientChargeAgrt(RbClientChargeAgrt rbClientChargeAgrt) {
      rbClientChargeAgrt.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbClientChargeAgrt.class.getName() + ".updateRbClientChargeAgrt", rbClientChargeAgrt);
   }

   /** @deprecated */
   @Deprecated
   public RbClientChargeAgrt getChargeAgrt(Long internalKey, String servType) {
      Map<String, Object> param = new HashMap();
      param.put("feeType", servType);
      param.put("internalKey", internalKey);
      return (RbClientChargeAgrt)this.daoSupport.selectOne(RbClientChargeAgrt.class.getName() + ".getChargeAgrt", param);
   }

   public RbClientChargeAgrt getChargeAgrt(String agreementId, String feeType, String clientNo) {
      RbClientChargeAgrt rbClientChargeAgrt = new RbClientChargeAgrt();
      rbClientChargeAgrt.setAgreementId(String.valueOf(agreementId));
      rbClientChargeAgrt.setFeeType(feeType);
      rbClientChargeAgrt.setClientNo(clientNo);
      return (RbClientChargeAgrt)this.daoSupport.selectOne(RbClientChargeAgrt.class.getName() + ".getChargeAgrtByKey", rbClientChargeAgrt);
   }

   public List<RbClientChargeAgrt> getChargeAgrt(String feeType, String clientNo) {
      RbClientChargeAgrt rbClientChargeAgrt = new RbClientChargeAgrt();
      rbClientChargeAgrt.setFeeType(feeType);
      rbClientChargeAgrt.setClientNo(clientNo);
      return this.daoSupport.selectList(RbClientChargeAgrt.class.getName() + ".getChargeAgrtByKeyMod", rbClientChargeAgrt);
   }

   public List<RbClientChargeAgrt> getMbClientChargeAgrt(String servType, String agrtNo) {
      Map<String, Object> param = new HashMap();
      param.put("feeType", servType);
      param.put("agreementId", agrtNo);
      List<RbClientChargeAgrt> rbClientChargeAgrts = this.daoSupport.selectList(RbClientChargeAgrt.class.getName() + ".selectByPrimaryKey", param);
      if (null == rbClientChargeAgrts) {
         throw BusiUtil.createBusinessException("RB4005");
      } else {
         return rbClientChargeAgrts;
      }
   }

   public List<RbClientChargeAgrt> getChargeAgrtByInternalKey(String internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbClientChargeAgrt.class.getName() + ".getChargeAgrtByInternalKey", param);
   }

   public RbClientChargeAgrt getMbClientChargeAgrtByAgreementIdAndFeeType(String agreementId, String feeType) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("feeType", feeType);
      return (RbClientChargeAgrt)this.daoSupport.selectOne(RbClientChargeAgrt.class.getName() + ".getMbClientChargeAgrtByAgreementIdAndFeeType", param);
   }

   public List<RbClientChargeAgrt> selectNextChargeDateMature(String runDate) {
      Map<String, Object> param = new HashMap();
      param.put("runDate", runDate);
      return this.daoSupport.selectList(RbClientChargeAgrt.class.getName() + ".selectNextChargeDateMature", param);
   }
}
