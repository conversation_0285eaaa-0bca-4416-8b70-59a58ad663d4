package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbBatchReserveDetailRepository extends BusinessRepository {
   public List<RbBatchReserveDetail> getReserveDetailByExtRefNo(String extRefNo, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("extRefNo", extRefNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbBatchReserveDetail.class.getName() + ".getReserveDetailByExtRefNo", param);
   }

   public RbBatchReserveDetail getReserveDetailByExtRefNoAndSeqNo(String extRefNo, String seqNo) {
      Map<String, Object> param = new HashMap(2);
      param.put("extRefNo", extRefNo);
      param.put("seqNo", seqNo);
      return (RbBatchReserveDetail)this.daoSupport.selectOne(RbBatchReserveDetail.class.getName() + ".getReserveDetailByExtRefNoAndSeqNo", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updRbBatchReserveDetail(RbBatchReserveDetail rbBatchReserveDetail) {
      super.update(rbBatchReserveDetail);
   }

   public void updRbBatchReserveDetailNotTransaction(List<RbBatchReserveDetail> rbBatchReserveDetailList) {
      rbBatchReserveDetailList.forEach((item) -> {
         super.update(item);
      });
   }
}
