package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherCourt;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherCourtHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbVoucherCourtRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherCourtRepository.class);

   public void add(RbVoucherCourt rbVoucherCourt) {
      this.daoSupport.insert(rbVoucherCourt);
      RbVoucherCourtHist voucherCourtHist = new RbVoucherCourtHist();
      BeanUtil.copy(rbVoucherCourt, voucherCourtHist);
      voucherCourtHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.voucherCourtHistSeqNo));
      this.daoSupport.insert(voucherCourtHist);
   }

   public void update(RbVoucherCourt rbVoucherCourt) {
      this.daoSupport.update(rbVoucherCourt);
      RbVoucherCourtHist voucherCourtHist = new RbVoucherCourtHist();
      voucherCourtHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.voucherCourtHistSeqNo));
      BeanUtil.copy(rbVoucherCourt, voucherCourtHist);
      this.daoSupport.insert(voucherCourtHist);
   }

   public RbVoucherCourt getRbVoucherCourtByLostNo(String clientNo, String lostNo) {
      RbVoucherCourt rbVoucherCourt = new RbVoucherCourt();
      rbVoucherCourt.setLostNo(lostNo);
      rbVoucherCourt.setClientNo(clientNo);
      return (RbVoucherCourt)this.daoSupport.selectOne(rbVoucherCourt);
   }

   public RbVoucherCourt getRbVoucherCourtByVoucherNo(String clientNo, String docType, String voucherNo) {
      RbVoucherCourt rbVoucherCourt = new RbVoucherCourt();
      rbVoucherCourt.setDocType(docType);
      rbVoucherCourt.setVoucherNo(voucherNo);
      rbVoucherCourt.setClientNo(clientNo);
      return (RbVoucherCourt)this.daoSupport.selectOne(rbVoucherCourt);
   }
}
