package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbBatchReserveInfoRepository extends BusinessRepository {
   public RbBatchReserveInfo getRbBatchReserveInfo(String extRefNo) {
      RbBatchReserveInfo rbBatchReserveInfo1 = new RbBatchReserveInfo();
      rbBatchReserveInfo1.setExtRefNo(extRefNo);
      RbBatchReserveInfo rbBatchReserveInfo = (RbBatchReserveInfo)this.daoSupport.selectOne(rbBatchReserveInfo1);
      return rbBatchReserveInfo;
   }

   public List<RbBatchReserveInfo> getRbBatchReserveInfoByDate(Date registerDate, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("registerDate", registerDate);
      param.put("company", company);
      return this.daoSupport.selectList(RbBatchReserveInfo.class.getName() + ".getReserveInfoByRDate", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public RbBatchReserveInfo updRbBatchReserveInfoCheckStatus(RbBatchReserveInfo rbBatchReserveInfo) {
      RbBatchReserveInfo info = this.selectForUpdate(rbBatchReserveInfo);
      if (!BusiUtil.isNull(info) && !BusiUtil.isNotEquals("0", info.getReserveStatus())) {
         super.update(rbBatchReserveInfo);
         info.setReserveStatus(rbBatchReserveInfo.getReserveStatus());
         return info;
      } else {
         return null;
      }
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updRbBatchReserveInfoStatus(RbBatchReserveInfo rbBatchReserveInfo) {
      super.update(rbBatchReserveInfo);
   }

   public void updateRbBatchReserveInfoNoTranction(RbBatchReserveInfo rbBatchReserveInfo) {
      super.update(rbBatchReserveInfo);
   }

   public RbBatchReserveInfo selectForUpdate(RbBatchReserveInfo rbBatchReserveInfo) {
      List<RbBatchReserveInfo> rbBatchReserveInfos = this.daoSupport.selectList(RbBatchReserveInfo.class.getName() + ".getReserveInfoForUpdate", rbBatchReserveInfo);
      return BusiUtil.isNull(rbBatchReserveInfos) ? null : (RbBatchReserveInfo)rbBatchReserveInfos.get(0);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updRbBatchReserveInfo(RbBatchReserveInfo rbBatchReserveInfo) {
      super.update(rbBatchReserveInfo);
   }
}
