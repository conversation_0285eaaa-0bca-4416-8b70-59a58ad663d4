package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSms;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementSmsRepository extends BusinessRepository {
   public void createMbAgreementSmsDb(RbAgreementSms rbAgreementSms) {
      if (BusiUtil.isNotNull(rbAgreementSms)) {
         super.insert(rbAgreementSms);
      }

   }

   public List<RbAgreementSms> selectListByTakeSignFlag(Map<String, Object> param) {
      List<String> takeSignFlag = new ArrayList();
      takeSignFlag.add("A");
      takeSignFlag.add("Y");
      param.put("takeSignFlag", takeSignFlag);
      return this.daoSupport.selectList(RbAgreementSms.class.getName() + ".selectListByTakeSignFlag", param);
   }

   public RbAgreementSms selectByPrimaryKey(String agreementId) {
      RbAgreementSms rbAgreementSms = new RbAgreementSms();
      rbAgreementSms.setAgreementId(agreementId);
      return (RbAgreementSms)this.daoSupport.selectOne(rbAgreementSms);
   }

   public List<RbAgreementSms> selectByAgreementId(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      RbAgreementSms rbAgreementSms = new RbAgreementSms();
      rbAgreementSms.setAgreementId(agreementId);
      rbAgreementSms.setClientNo(clientNo);
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      return this.selectListByTakeSignFlag(param);
   }

   public List<RbAgreementSms> selectByIntenalKey(Long internalKey, String clientNo) {
      RbAgreementSms rbAgreementSms = new RbAgreementSms();
      rbAgreementSms.setInternalKey(internalKey);
      rbAgreementSms.setClientNo(clientNo);
      return this.daoSupport.selectList(rbAgreementSms);
   }

   public List<RbAgreementSms> selectByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      List<RbAgreementSms> rbAgreementSmsList = this.selectListByTakeSignFlag(param);
      return (List)(BusiUtil.isNotNull(rbAgreementSmsList) ? this.selectListByTakeSignFlag(param) : new ArrayList());
   }

   public void updateByAgreementId(String agreementId, String clientNo, List<RbAgreementSms> rbAgreementSmsList) {
      RbAgreementSms rbAgreementSms = new RbAgreementSms();
      rbAgreementSms.setAgreementId(agreementId);
      rbAgreementSms.setClientNo(clientNo);
      super.delete(rbAgreementSms);
      super.insertAddBatch(rbAgreementSmsList);
   }

   public List<RbAgreementSms> selectByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap();
      RbAgreementSms rbAgreementSms = new RbAgreementSms();
      rbAgreementSms.setClientNo(clientNo);
      param.put("clientNo", clientNo);
      List<RbAgreementSms> rbAgreementSmsList = this.selectListByTakeSignFlag(param);
      return rbAgreementSmsList;
   }

   public List<RbAgreementSms> selectCosSmsSignInfo(Date tranDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("tranTimestamp", tranDate);
      return BusiUtil.isNull(tranDate) ? null : this.daoSupport.selectList(RbAgreementSms.class.getName() + ".selectCosSmsSignInfo", param);
   }

   public void updateRbAgreementName(RbAgreementSms rbAgreementSms) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", rbAgreementSms.getClientNo());
      param.put("chClientName", rbAgreementSms.getChClientName());
      param.put("documentType", rbAgreementSms.getDocumentType());
      param.put("documentId", rbAgreementSms.getDocumentId());
      param.put("genderFlag", rbAgreementSms.getGenderFlag());
      param.put("documentExpiryDate", rbAgreementSms.getDocumentExpiryDate());
      this.daoSupport.update(RbAgreementSms.class.getName() + ".updateRbAgreementName", param);
   }
}
