package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctFundHangInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class RbAcctFundHangInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctFundHangInfoRepository.class);

   public void createRbAcctFundHangInfoDb(List<RbAcctFundHangInfo> rbAcctFundHangInfoList) {
      super.insertAddBatch(rbAcctFundHangInfoList);
   }
}
