package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbCreditBseRestrictSign;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbCreditBseRestrictSignRepository extends BusinessRepository {
   public List<RbCreditBseRestrictSign> getRbCreditByCardNo(String cardNo, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("cardNo", cardNo);
      map.put("company", company);
      List<RbCreditBseRestrictSign> rbCredits = null;
      rbCredits = this.daoSupport.selectList(RbCreditBseRestrictSign.class.getName() + ".getRbCreditByCardNo", map);
      return rbCredits;
   }

   public RbCreditBseRestrictSign getRbCreditBystlSeqNo(String stlSeqNo, String company) {
      Map<String, Object> map = new HashMap(16);
      map.put("stlSeqNo", stlSeqNo);
      map.put("company", company);
      RbCreditBseRestrictSign rbCredits = null;
      rbCredits = (RbCreditBseRestrictSign)this.daoSupport.selectOne(RbCreditBseRestrictSign.class.getName() + ".getRbCreditBystlSeqNo", map);
      return rbCredits;
   }

   public void updateStatusAndDate(String status, Date date, String resSeqNo, String clientNo) {
      Map<String, Object> map = new HashMap(16);
      map.put("status", status);
      map.put("date", date);
      map.put("resSeqNo", resSeqNo);
      map.put("clientNo", clientNo);
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbCreditBseRestrictSign.class.getName() + ".updateStatusAndDate", map);
   }
}
