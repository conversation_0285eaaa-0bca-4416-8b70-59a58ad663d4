package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherSellInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbVoucherSellInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherSellInfoRepository.class);

   public RbVoucherSellInfo getSellInfoByVoucher(String voucherNo, String docType) {
      Map<String, Object> param = new HashMap(16);
      param.put("voucherNo", voucherNo);
      param.put("docType", docType);
      return (RbVoucherSellInfo)this.daoSupport.selectOne(RbVoucherSellInfo.class.getName() + ".getSellInfoByVoucher", param);
   }
}
