package com.dcits.ensemble.rb.business.repository.channel;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbJudicatureFileQuery;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RbJudicatureFileQueryRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbJudicatureFileQueryRepository.class);

   public void createRbJudicatureFileQuery(RbJudicatureFileQuery rbJudicatureFileQuery) {
      super.insert(rbJudicatureFileQuery);
   }

   public RbJudicatureFileQuery selectRbJudicatureFileByBatchNo(String batchNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("batchNo", batchNo);
      return (RbJudicatureFileQuery)this.daoSupport.selectOne(RbJudicatureFileQuery.class.getName() + ".selectRbJudicatureFileQueryByBatchNo", param);
   }

   public List<RbJudicatureFileQuery> selectRbJudicatureFileByBatchNoList(String batchNo) {
      Map<String, Object> param = new HashMap(1);
      param.put("batchNo", batchNo);
      return this.daoSupport.selectList(RbJudicatureFileQuery.class.getName() + ".selectRbJudicatureFileQueryByBatchNo", param);
   }

   public void updateRbJudicatureFileByBatchNo(String batchNo, String fileName, String tranFileResult, String fileErrorMsg, String channelSeqNo, String subSeqNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("batchNo", batchNo);
      map.put("fileName", fileName);
      map.put("tranFileResult", tranFileResult);
      map.put("fileErrorMsg", fileErrorMsg);
      map.put("channelSeqNo", channelSeqNo);
      map.put("subSeqNo", subSeqNo);
      this.daoSupport.update(RbJudicatureFileQuery.class.getName() + ".update", map);
   }
}
