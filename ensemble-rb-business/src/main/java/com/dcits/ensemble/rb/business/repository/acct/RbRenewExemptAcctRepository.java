package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbRenewExemptAcct;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbRenewExemptAcctRepository extends BusinessRepository {
   public RbRenewExemptAcct selectByAcct(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return (RbRenewExemptAcct)this.daoSupport.selectOne(RbRenewExemptAcct.class.getName() + ".selectByAcct", param);
   }

   public void deleteByAcct(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", acctCcy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("clientNo", clientNo);
      param.put("company", company);
      this.daoSupport.delete(RbRenewExemptAcct.class.getName() + ".deleteByAcct", param);
   }
}
