package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbChequeDud;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbChequeDudRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbChequeDudRepository.class);

   public List<RbChequeDud> quiryByClientNo(String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbChequeDud.class.getName() + ".quiryByClientNo", param);
   }

   public List<RbChequeDud> quiryByBaseAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbChequeDud.class.getName() + ".quiryByBaseAcctNo", param);
   }

   public List<RbChequeDud> quiryByVoucher(String baseAcctNo, String prodType, String acctSeqNo, String voucherNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctSeqNo", acctSeqNo);
      param.put("voucherNo", voucherNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbChequeDud.class.getName() + ".quiryByVoucher", param);
   }

   public void delete(String dudSeqNo, String clientNo) {
      RbChequeDud rbChequeDud = new RbChequeDud();
      rbChequeDud.setDudSeqNo(dudSeqNo);
      rbChequeDud.setClientNo(clientNo);
      this.daoSupport.delete(rbChequeDud);
   }

   public void createMbChequeDud(RbChequeDud rbChequeDud) {
      rbChequeDud.setDudSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbChequeDudNo, new String[]{Context.getInstance().getRunDate()}));
      super.insert(rbChequeDud);
   }

   public void updateChequeDud(String dudSeqNo, String clientNo, String chargeFlag) {
      RbChequeDud rbChequeDud = new RbChequeDud();
      rbChequeDud.setDudSeqNo(dudSeqNo);
      rbChequeDud.setClientNo(clientNo);
      rbChequeDud.setChargeFlag(chargeFlag);
      this.daoSupport.update(rbChequeDud);
   }
}
