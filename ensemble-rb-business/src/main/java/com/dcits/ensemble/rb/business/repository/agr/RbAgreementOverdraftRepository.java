package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.model.dict.BaseDict;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementOverdraftModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementOverdraftRepository extends BusinessRepository {
   public void createMbAgreementOverdraftDb(RbAgreementOverdraft rbAgreementOverdraft) {
      if (BusiUtil.isNotNull(rbAgreementOverdraft)) {
         super.insert(rbAgreementOverdraft);
      }

   }

   public void updateMbAgreementOverdraftDb(RbAgreementOverdraft rbAgreementOverdraft) {
      if (BusiUtil.isNotNull(rbAgreementOverdraft)) {
         super.update(rbAgreementOverdraft);
      }

   }

   public RbAgreementOverdraft selectActiveOverdraft(RbAgreementOverdraft rbAgreementOverdraft) {
      rbAgreementOverdraft.setAgreementStatus("A");
      return (RbAgreementOverdraft)this.daoSupport.selectOne(rbAgreementOverdraft);
   }

   public RbAgreementOverdraft selectOdInfo(RbAgreementOverdraft rbAgreementOverdraft) {
      return (RbAgreementOverdraft)this.daoSupport.selectOne(rbAgreementOverdraft);
   }

   public RbAgreementOverdraft getOverdraft(String agreementId, String clientNo) {
      RbAgreementOverdraft rbAgreementOverdraft = new RbAgreementOverdraft();
      rbAgreementOverdraft.setAgreementId(agreementId);
      rbAgreementOverdraft.setClientNo(clientNo);
      return (RbAgreementOverdraft)this.daoSupport.selectOne(rbAgreementOverdraft);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByBaseAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      param.put(BaseDict.agreementStatus.toString(), "A");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getAgreementsByAcctNo", param);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByBaseAcctNoAndStatus(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getOdfAgrtByBaseAcctNoAndStatus", param);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByBaseAcctNoByAgreementStatus(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      param.put(BaseDict.agreementStatus.toString(), "A");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getOdfAgrtByBaseAcctNoByAgreementStatus", param);
   }

   public List<RbAgreementOverdraft> getOverdraftByParams(MbAgreementOverdraftModel overdraftModel) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), overdraftModel.getBaseAcctNo());
      param.put(BaseDict.clientNo.toString(), overdraftModel.getClientNo());
      param.put(BaseDict.agreementType.toString(), "ODF");
      param.put(BaseDict.effectDate.toString(), overdraftModel.getStartDate());
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getOverdraftByParams", param);
   }

   public List<RbAgreementOverdraft> getAgreementsByInfo(String baseAcctNo, String clientNo, String agreementStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementStatus.toString(), agreementStatus);
      param.put(BaseDict.agreementType.toString(), "ODF");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getAgreementsByInfo", param);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByBaseAcctNoByStatus(String baseAcctNo, String clientNo, String agreementStatus) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      param.put(BaseDict.agreementStatus.toString(), agreementStatus);
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getOdfAgrtByBaseAcctNoByStatus", param);
   }

   public RbAgreementOverdraft getActiveOverdraft(String agreementId, String clientNo, String agreementStatus) {
      RbAgreementOverdraft rbAgreementOverdraft = new RbAgreementOverdraft();
      rbAgreementOverdraft.setAgreementStatus("A");
      rbAgreementOverdraft.setAgreementId(agreementId);
      rbAgreementOverdraft.setClientNo(clientNo);
      rbAgreementOverdraft.setAgreementStatus(agreementStatus);
      return (RbAgreementOverdraft)this.daoSupport.selectOne(rbAgreementOverdraft);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByAcctNo(String agreementId, String baseAcctNo, String clientNo, String agreementStatus, String agreementType) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.agreementId.toString(), agreementId);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementStatus.toString(), agreementStatus);
      param.put(BaseDict.agreementType.toString(), agreementType);
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getAgreementsByAcctNo", param);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByAgreementId(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.agreementId.toString(), agreementId);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getOdfAgrtByAgreementId", param);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getAgreementsByAcctNo", param);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByBaseAcctNoIncludeE(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getOdfAgrtByBaseAcctNoIncludeE", param);
   }

   public void updteStatus(RbAgreementOverdraft rbAgreementOverdraft1) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.agreementId.toString(), rbAgreementOverdraft1.getAgreementId());
      param.put(BaseDict.clientNo.toString(), rbAgreementOverdraft1.getClientNo());
      param.put(BaseDict.agreementStatus.toString(), rbAgreementOverdraft1.getAgreementStatus());
      param.put("remark", rbAgreementOverdraft1.getRemark());
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAgreementOverdraft.class.getName() + ".updteStatus", param);
   }

   public void updteStatusP(RbAgreementOverdraft rbAgreementOverdraft1) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.agreementId.toString(), rbAgreementOverdraft1.getAgreementId());
      param.put(BaseDict.clientNo.toString(), rbAgreementOverdraft1.getClientNo());
      param.put(BaseDict.agreementStatus.toString(), rbAgreementOverdraft1.getAgreementStatus());
      param.put("remark", rbAgreementOverdraft1.getRemark());
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAgreementOverdraft.class.getName() + ".updteStatusP", param);
   }

   public RbAgreementOverdraft getOdfByBaseAcctNoAndStatusForOne(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.clientNo.toString(), clientNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      return (RbAgreementOverdraft)this.daoSupport.selectOne(RbAgreementOverdraft.class.getName() + ".getOdfAgrtByBaseAcctNoAndStatus", param);
   }

   public RbAgreementOverdraft getAgreementsByAcctNo(RbAgreementOverdraft rbAgreementOverdraft1) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), rbAgreementOverdraft1.getBaseAcctNo());
      param.put(BaseDict.clientNo.toString(), rbAgreementOverdraft1.getClientNo());
      param.put(BaseDict.agreementType.toString(), "ODF");
      return (RbAgreementOverdraft)this.daoSupport.selectOne(RbAgreementOverdraft.class.getName() + ".getAgreementsByAcctNo", param);
   }

   public List<RbAgreementOverdraft> getOdfAgrtByBaesAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.agreementType.toString(), "ODF");
      return this.daoSupport.selectList(RbAgreementOverdraft.class.getName() + ".getOdfAgrtByBaesAcctNo", param);
   }
}
