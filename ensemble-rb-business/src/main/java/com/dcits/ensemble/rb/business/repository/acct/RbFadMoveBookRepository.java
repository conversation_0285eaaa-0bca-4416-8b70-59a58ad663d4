package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFadMoveBook;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFadMoveBookRepository extends BusinessRepository {
   public RbFadMoveBook selectByPrimaryKey(RbFadMoveBook rbFadMoveBook) {
      return (RbFadMoveBook)this.daoSupport.selectOne(rbFadMoveBook);
   }
}
