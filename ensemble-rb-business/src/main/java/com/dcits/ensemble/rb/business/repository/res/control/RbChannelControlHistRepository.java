package com.dcits.ensemble.rb.business.repository.res.control;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControlHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbChannelControlHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbChannelControlHistRepository.class);

   public RbChannelControlHist getNewHist() {
      RbChannelControlHist rbChannelControlHist = new RbChannelControlHist();
      rbChannelControlHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.controlHistSeqNo));
      return rbChannelControlHist;
   }
}
