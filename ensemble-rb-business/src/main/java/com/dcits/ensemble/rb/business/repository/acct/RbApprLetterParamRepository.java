package com.dcits.ensemble.rb.business.repository.acct;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbApprLetterParam;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbApprLetterParamRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbApprLetterParamRepository.class);

   @Cached(
      area = "longArea",
      name = "rb:param:rb_appr_letter_param:appr_letter:",
      key = "#paraKey",
      cacheType = CacheType.REMOTE
   )
   public List<RbApprLetterParam> selectParaInfo(String paraKey, String company) {
      Map<String, Object> param = new HashMap(1);
      param.put("paraKey", paraKey);
      param.put("company", company);
      return this.daoSupport.selectList(RbApprLetterParam.class.getName() + ".selectParaInfo", param);
   }
}
