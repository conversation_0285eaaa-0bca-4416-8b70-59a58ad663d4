package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBatchReserveLoan;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbBatchReserveLoanRepository extends BusinessRepository {
   public RbBatchReserveLoan getrbBatchReserveLoan(String contractNo, String billVoucherNo) {
      RbBatchReserveLoan rbBatchReserveLoan1 = new RbBatchReserveLoan();
      rbBatchReserveLoan1.setContractNo(contractNo);
      rbBatchReserveLoan1.setBillVoucherNo(billVoucherNo);
      return (RbBatchReserveLoan)this.daoSupport.selectOne(rbBatchReserveLoan1);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public RbBatchReserveLoan updRbBatchReserveLoanCheckStatus(RbBatchReserveLoan rbBatchReserveLoan) {
      RbBatchReserveLoan info = this.selectForUpdate(rbBatchReserveLoan);
      if (!BusiUtil.isNull(info) && !BusiUtil.isNotEquals("0", info.getReserveStatus())) {
         super.update(rbBatchReserveLoan);
         info.setReserveStatus(rbBatchReserveLoan.getReserveStatus());
         return info;
      } else {
         return null;
      }
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updRbBatchReserveLoanStatus(RbBatchReserveLoan rbBatchReserveLoan) {
      super.update(rbBatchReserveLoan);
   }

   public RbBatchReserveLoan selectForUpdate(RbBatchReserveLoan rbBatchReserveLoan) {
      List<RbBatchReserveLoan> rbBatchReserveLoans = this.daoSupport.selectList(RbBatchReserveLoan.class.getName() + ".getReserveLoanForUpdate", rbBatchReserveLoan);
      return BusiUtil.isNull(rbBatchReserveLoans) ? null : (RbBatchReserveLoan)rbBatchReserveLoans.get(0);
   }
}
