package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenInnerParam;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbOpenInnerParamRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbOpenInnerParamRepository.class);

   public RbOpenInnerParam selectOneByTranTypeAndBranch(String tranType, String branch) {
      Map<String, Object> param = new HashMap();
      param.put("tranType", tranType);
      param.put("branch", branch);
      return (RbOpenInnerParam)this.daoSupport.selectOne(RbOpenInnerParam.class.getName() + ".selectOneByTranTypeAndBranch", param);
   }

   public RbOpenInnerParam selectOneForUpdate(RbOpenInnerParam rbOpenInnerParam) {
      return (RbOpenInnerParam)this.daoSupport.selectOne(RbOpenInnerParam.class.getName() + ".selectOneForUpdate", rbOpenInnerParam);
   }

   public List<RbOpenInnerParam> selectOneByOpenBasis(String openBasis) {
      Map<String, Object> param = new HashMap();
      param.put("openBasis", openBasis);
      return this.daoSupport.selectList(RbOpenInnerParam.class.getName() + ".selectOneByOpenBasis", param);
   }

   public RbOpenInnerParam selectOneByTranTypeAndBranchAndOpenBasis(String tranType, String branch, String openBasis) {
      Map<String, Object> param = new HashMap();
      param.put("tranType", tranType);
      param.put("branch", branch);
      param.put("openBasis", openBasis);
      return (RbOpenInnerParam)this.daoSupport.selectOne(RbOpenInnerParam.class.getName() + ".selectOneByTranTypeAndBranchAndOpenBasis", param);
   }

   public void updateForOpenBasis(RbOpenInnerParam rbOpenInnerParam) {
      this.daoSupport.update(RbOpenInnerParam.class.getName() + ".updateForOpenBasis", rbOpenInnerParam);
   }
}
