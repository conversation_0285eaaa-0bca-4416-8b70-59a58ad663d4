package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbDelayPayIntHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbDelayPayIntHistRepository.class);
}
