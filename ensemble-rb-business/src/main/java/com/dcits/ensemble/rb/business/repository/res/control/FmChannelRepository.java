package com.dcits.ensemble.rb.business.repository.res.control;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.api.IMoreLanguage;
import com.dcits.ensemble.fm.model.FmChannel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class FmChannelRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(FmChannelRepository.class);
   @Resource
   private IMoreLanguage moreLanguage;

   public List<FmChannel> selectUncounterChannelList() {
      FmChannel fmChannel = new FmChannel();
      fmChannel.setCounterFlag("N");
      return this.daoSupport.selectList(fmChannel);
   }

   public List<FmChannel> selectChannelListByList(List<String> channels) {
      if (BusiUtil.isNotNull(channels)) {
         Map param = new HashMap();
         param.put("channels", channels);
         return this.daoSupport.selectList(FmChannel.class.getName() + ".selectChannelListByChannels", param);
      } else {
         return new ArrayList();
      }
   }

   public FmChannel selectFmChannelByChannel(String channel) {
      FmChannel fmChannel = new FmChannel();
      fmChannel.setChannel(channel);
      FmChannel fmChannel1 = (FmChannel)this.daoSupport.selectOne(fmChannel);
      String descUs = this.moreLanguage.getFmLangTranslation("FM_CHANNEL", "CHANNEL_DESC", channel);
      if (BusiUtil.isNotNull(descUs)) {
         fmChannel1.setChannelShort(descUs);
      }

      return fmChannel1;
   }

   public List<FmChannel> selectCounterChannels() {
      FmChannel fmChannel = new FmChannel();
      fmChannel.setCounterFlag("Y");
      return this.daoSupport.selectList(fmChannel);
   }
}
