package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBillTranDef;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbBillTranDefRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbBillTranDefRepository.class);

   @Cached(
      area = "longArea",
      name = "rb:bill:trandef:",
      key = "#docType+'-'+#billType+'-'+#cashFrom+'-'+#operType",
      cacheType = CacheType.REMOTE
   )
   public RbBillTranDef getRbBillTranDef(String docType, String billType, String cashFrom, String operType) {
      RbBillTranDef rbBillTranDef = new RbBillTranDef();
      rbBillTranDef.setDocType(docType);
      rbBillTranDef.setBillType(billType);
      rbBillTranDef.setCashFromTo(cashFrom);
      rbBillTranDef.setOperateType(operType);
      return (RbBillTranDef)this.daoSupport.selectOne(rbBillTranDef);
   }
}
