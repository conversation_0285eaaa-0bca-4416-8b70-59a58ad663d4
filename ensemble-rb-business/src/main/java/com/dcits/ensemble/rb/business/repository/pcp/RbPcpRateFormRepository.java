package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpRateForm;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpRateFormRepository extends BusinessRepository<RbPcpRateForm> {
   private static final Logger log = LoggerFactory.getLogger(RbPcpRateFormRepository.class);

   public RbPcpRateForm getRbRateFormByAgreementId(String agreementId) {
      Map<String, Object> map = new HashMap(5);
      map.put("agreementId", agreementId);
      return (RbPcpRateForm)this.daoSupport.selectOne(RbPcpRateForm.class.getName() + ".getRbRateFormByAgreementId", map);
   }

   public RbPcpRateForm getRbRateFormByApprovalNo(String approvalNo) {
      RbPcpRateForm rbPcpRateForm = new RbPcpRateForm();
      rbPcpRateForm.setApprovalNo(approvalNo);
      return (RbPcpRateForm)this.selectOne(rbPcpRateForm);
   }
}
