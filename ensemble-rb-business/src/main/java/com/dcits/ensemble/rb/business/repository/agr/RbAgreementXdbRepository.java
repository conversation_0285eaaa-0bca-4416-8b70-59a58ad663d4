package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementXdb;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementXdbRepository extends BusinessRepository {
   public void createMbAgreementXdb(RbAgreementXdb rbAgreementXdb) {
      if (BusiUtil.isNotNull(rbAgreementXdb)) {
         super.insert(rbAgreementXdb);
      }

   }

   public void updateByPrimaryKey(RbAgreementXdb rbAgreementXdb) {
      super.update(rbAgreementXdb);
   }

   /** @deprecated */
   @Deprecated
   public RbAgreementXdb getMbAgreementXdb(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return (RbAgreementXdb)this.daoSupport.selectOne(RbAgreementXdb.class.getName() + ".getMbAgreementXdb", param);
   }

   public void updateMbAgreementXdb(RbAgreementXdb rbAgreementXdb) {
      super.update(rbAgreementXdb);
   }

   public void deleteMbAgreementXdbDb(String agreementId, String clientNo) {
      RbAgreementXdb rbAgreementXdb = new RbAgreementXdb();
      rbAgreementXdb.setAgreementId(agreementId);
      rbAgreementXdb.setClientNo(clientNo);
      this.daoSupport.delete(rbAgreementXdb);
   }

   public List<RbAgreementXdb> getAccordInfo(String agreementId) {
      RbAgreementXdb rbAgreementXdb = new RbAgreementXdb();
      rbAgreementXdb.setAgreementId(agreementId);
      return this.daoSupport.selectList(rbAgreementXdb);
   }

   public RbAgreementXdb getMbAgreementXdb(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctCcy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementType", agreementType);
      param.put("clientNo", clientNo);
      return (RbAgreementXdb)this.daoSupport.selectOne(RbAgreementXdb.class.getName() + ".selectAgrByType", param);
   }

   public List<RbAgreementXdb> getRbAgreementXdb(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (List)this.daoSupport.selectOne(RbAgreementXdb.class.getName() + ".selectAgreementAccordInfo", param);
   }

   public RbAgreementXdb getRbAgreementXdb(String agreementId, String clientNo) {
      RbAgreementXdb rbAgreementXdb = new RbAgreementXdb();
      rbAgreementXdb.setAgreementId(agreementId);
      rbAgreementXdb.setClientNo(clientNo);
      return (RbAgreementXdb)this.daoSupport.selectOne(RbAgreementXdb.class.getName() + ".selectOne", rbAgreementXdb);
   }
}
