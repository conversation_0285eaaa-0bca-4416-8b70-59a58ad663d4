package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTdaChange;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class MbTdaChangeRepository extends BusinessRepository {
   public void addMbTdaChange(RbTdaChange mbTdaChange) {
      mbTdaChange.setChangeTdaSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.tdaChangeSeqNo));
      super.insert(mbTdaChange);
   }

   public void updateMbTdaChangeStatus(RbTdaChange mbTdaChange) {
      String seqNo = mbTdaChange.getChangeTdaSeqNo();
      Map<String, Object> param = new HashMap();
      param.put("changeTdaSeqNo", seqNo);
      param.put("changeTdaStatus", mbTdaChange.getChangeTdaStatus());
      param.put("clientNo", mbTdaChange.getClientNo());
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      param.put("company", mbTdaChange.getCompany());
      this.daoSupport.update(RbTdaChange.class.getName() + ".updateMbTdaChangeStatus", param);
   }

   /** @deprecated */
   @Deprecated
   public List<RbTdaChange> getMbTdaChangeList(String internalKey, String changeTdaStatus, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("changeTdaStatus", changeTdaStatus);
      param.put("company", company);
      return this.daoSupport.selectList(RbTdaChange.class.getName() + ".getMbTdaChangeList", param);
   }

   public List<RbTdaChange> getRbTdaChangeList(Long internalKey, String changeTdaStatus, String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("changeTdaStatus", changeTdaStatus);
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbTdaChange.class.getName() + ".getMbTdaChangeList", param);
   }
}
