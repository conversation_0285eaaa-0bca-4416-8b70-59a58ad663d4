package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctRiskChangeRegister;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAcctRiskChangeRegisterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctRiskChangeRegisterRepository.class);

   public void createRbAcctRiskChange(RbAcctRiskChangeRegister rbAcctRiskChangeRegister) {
      rbAcctRiskChangeRegister.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.eventSeqNo));
      if (BusiUtil.isNull(rbAcctRiskChangeRegister.getTranBranch())) {
         rbAcctRiskChangeRegister.setTranBranch(Context.getInstance().getBranchId());
      }

      if (BusiUtil.isNotNull(Context.getInstance().getTranDate())) {
         rbAcctRiskChangeRegister.setTranDate(BusiUtil.convertStr2Date(Context.getInstance().getTranDate()));
      }

      rbAcctRiskChangeRegister.setUserId(Context.getInstance().getUserId());
      rbAcctRiskChangeRegister.setReference(Context.getInstance().getReference());
      super.insert(rbAcctRiskChangeRegister);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateRbAcctRiskChange(RbAcctRiskChangeRegister rbAcctRiskChangeRegister) {
      super.update(rbAcctRiskChangeRegister);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateByBaseAcctNo(RbAcctRiskChangeRegister rbAcctRiskChangeRegister) {
      rbAcctRiskChangeRegister.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctRiskChangeRegister.class.getName() + ".updateByBaseAcctNo", rbAcctRiskChangeRegister);
   }

   public List<RbAcctRiskChangeRegister> getRbAcctRiskChange(String baseAcctNo, String prodType, String Ccy, String acctSeqNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("Ccy", Ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctRiskChangeRegister.class.getName() + ".getRbAcctRiskChange", param);
   }

   public List<RbAcctRiskChangeRegister> selectRbAcctRiskChange(String baseAcctNo, Date startDate, Date endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      return this.daoSupport.selectList(RbAcctRiskChangeRegister.class.getName() + ".selectRiskChangeRegister", param);
   }

   public void updateBaseAcctNo(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String cardNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("cardNo", cardNo);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbAcctRiskChangeRegister.class.getName() + ".updateBaseAcctNo", param);
   }

   public void insertRbAcctRiskChange(RbAcctRiskChangeRegister rbAcctRiskChangeRegister) {
      super.insert(rbAcctRiskChangeRegister);
   }
}
