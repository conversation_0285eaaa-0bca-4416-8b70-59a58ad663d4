package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbSealRelationHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbSealRelationHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbSealRelationHistRepository.class);

   public void insert(RbSealRelationHist mbSealRelationHist) {
      super.insert(mbSealRelationHist);
   }
}
