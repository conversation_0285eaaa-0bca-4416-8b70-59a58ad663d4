package com.dcits.ensemble.rb.business.repository.overdraft;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOverdraftInterestRecovery;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbOverdraftInterestRecoveryRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbOverdraftInterestRecoveryRepository.class);

   public List<RbOverdraftInterestRecovery> interestRecoveryAsc(String clientNo, Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbOverdraftInterestRecovery.class.getName() + ".getInterestRecoveryByCondition", param);
   }
}
