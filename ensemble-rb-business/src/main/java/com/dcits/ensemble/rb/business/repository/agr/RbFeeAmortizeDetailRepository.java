package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFeeAmortizeDetailRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbFeeAmortizeDetailRepository.class);

   public List<RbFeeAmortizeDetail> selectByCondition(Map<String, Object> para) {
      return this.daoSupport.selectList(RbFeeAmortizeDetail.class.getName() + ".selectByCondition", para);
   }

   public void insertBatch(List<RbFeeAmortizeDetail> records) {
      super.insertAddBatch(records);
   }

   public RbFeeAmortizeDetail createNewMbFeeAmortizeDetail(BigDecimal amortizeSeqNo) {
      RbFeeAmortizeDetail mbFeeAmortizeDetail = new RbFeeAmortizeDetail();
      if (BusiUtil.isNotNull(amortizeSeqNo)) {
         mbFeeAmortizeDetail.setAmortizeSeqNo(amortizeSeqNo.toString());
      } else {
         mbFeeAmortizeDetail.setAmortizeSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.feeAmortizeDetailSeqNo));
      }

      return mbFeeAmortizeDetail;
   }

   public int updateBatchDetailEod(List<RbFeeAmortizeDetail> mbFeeAmortizeDetails) {
      return super.updateAddBatch(mbFeeAmortizeDetails);
   }
}
