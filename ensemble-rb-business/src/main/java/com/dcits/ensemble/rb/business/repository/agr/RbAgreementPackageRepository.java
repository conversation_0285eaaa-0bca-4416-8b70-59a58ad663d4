package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackage;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementPackageModel;
import com.dcits.ensemble.rb.business.model.fee.MbFeePackageQueryModel;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementPackageRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementPackageRepository.class);

   public void creatMbAgreementPackage(MbAgreementPackageModel packageModel) {
      RbAgreementPackage rbAgreementPackage = new RbAgreementPackage();
      BeanUtil.copy(packageModel, rbAgreementPackage);
      rbAgreementPackage.setUserId(Context.getInstance().getUserId());
      rbAgreementPackage.setTranDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      rbAgreementPackage.setTranBranch(Context.getInstance().getBranchId());
      rbAgreementPackage.setChannelSeqNo(Context.getInstance().getSeqNo());
      super.insert(rbAgreementPackage);
   }

   public void updateMbAgreementPackage(MbAgreementPackageModel packageModel) {
      RbAgreementPackage rbAgreementPackage = new RbAgreementPackage();
      BeanUtil.copy(packageModel, rbAgreementPackage);
      this.daoSupport.update(rbAgreementPackage);
   }

   public MbAgreementPackageModel selectMbAgreementPackage(MbAgreementPackageModel packageModel) {
      RbAgreementPackage rbAgreementPackage = new RbAgreementPackage();
      rbAgreementPackage.setAgreementId(packageModel.getAgreementId());
      rbAgreementPackage.setClientNo(packageModel.getClientNo());
      MbAgreementPackageModel mbAgreementPackageModel = null;
      rbAgreementPackage = (RbAgreementPackage)this.daoSupport.selectOne(rbAgreementPackage);
      if (rbAgreementPackage != null) {
         mbAgreementPackageModel = new MbAgreementPackageModel();
         BeanUtil.copy(rbAgreementPackage, mbAgreementPackageModel);
      }

      return mbAgreementPackageModel;
   }

   public void updateAddBatchs(List<RbAgreementPackage> rbAgreementPackages) {
      this.daoSupport.updateAddBatch(rbAgreementPackages);
   }

   public RbAgreementPackage queryByAgreementId(String agreementId) {
      RbAgreementPackage rbAgreementPackage = new RbAgreementPackage();
      rbAgreementPackage.setAgreementId(agreementId);
      return (RbAgreementPackage)this.daoSupport.selectOne(rbAgreementPackage);
   }

   public List<RbAgreementPackage> queryByClientNo(String clientNo, String runDate) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("runDate", runDate);
      return this.daoSupport.selectList(RbAgreementPackage.class.getName() + ".selectByClientNo", param);
   }

   public RbAgreementPackage getArrearAgreementFeePackage(String agreementId, String runDate, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("runDate", runDate);
      param.put("clientNo", clientNo);
      return (RbAgreementPackage)this.daoSupport.selectOne(RbAgreementPackage.class.getName() + ".getArrearAgreementFeePackage", param);
   }

   public void updAgreementPackage(RbAgreementPackage rbAgreementPackage) {
      this.daoSupport.update(rbAgreementPackage);
   }

   public List<MbFeePackageQueryModel> selectFeePackageByPage(String packageId, String clientNo, String documentType, String documentId, String issCountry, String startDate, String endDate, String agreementStataus) {
      Map<String, Object> param = new HashMap();
      param.put("packageId", packageId);
      param.put("clientNo", clientNo);
      param.put("documentType", documentType);
      param.put("documentId", documentId);
      param.put("issCountry", issCountry);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("agreementStatus", agreementStataus);
      return this.daoSupport.selectList(RbAgreementPackage.class.getName() + ".selectFeePackageByPage", param);
   }
}
