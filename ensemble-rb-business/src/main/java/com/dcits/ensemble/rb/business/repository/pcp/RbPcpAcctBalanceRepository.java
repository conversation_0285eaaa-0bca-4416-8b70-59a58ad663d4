package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.common.constant.PcpDownEnum;
import com.dcits.ensemble.rb.business.common.constant.PcpUpEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAcctBalance;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbPcpAcctBalanceRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpAcctBalanceRepository.class);

   public RbPcpAcctBalance updMbAcctBal(String groupId, String clientNo, BigDecimal amount, BigDecimal prevAmount, BigDecimal preUpAmount, BigDecimal preDownAmount, BigDecimal tranAmt, String lastBalChangeDate, String tranDate, String flag, Long internalKey) {
      String runDate = Context.getInstance().getRunDate();
      RbPcpAcctBalance mbPcpAcctBalance = new RbPcpAcctBalance();
      mbPcpAcctBalance.setPcpGroupId(groupId);
      mbPcpAcctBalance.setClientNo(clientNo);
      mbPcpAcctBalance.setPcpBalance(amount);
      mbPcpAcctBalance.setInternalKey(internalKey);
      if (PcpUpEnum.UP.toString().equalsIgnoreCase(flag)) {
         mbPcpAcctBalance.setTotalUpAmt(tranAmt);
      }

      if (PcpDownEnum.DOWN.toString().equalsIgnoreCase(flag)) {
         mbPcpAcctBalance.setTotalDownAmt(tranAmt);
      }

      mbPcpAcctBalance.setLastChangeDate((Date)BusiUtil.nvl(DateUtil.parseDate(tranDate), DateUtil.parseDate(runDate)));
      if (BusiUtil.isNull(lastBalChangeDate) || BusiUtil.diffDays(lastBalChangeDate, (String)BusiUtil.nvl(tranDate, runDate)) < 0L) {
         mbPcpAcctBalance.setLastPcpBalance(prevAmount);
         mbPcpAcctBalance.setLastTotalDownAmt(preDownAmount);
         mbPcpAcctBalance.setLastTotalUpAmt(preUpAmount);
      }

      return mbPcpAcctBalance;
   }

   public void updMbAcctBalDb(List<RbPcpAcctBalance> mbPcpAcctBalances) {
      if (BusiUtil.isNotNull(mbPcpAcctBalances)) {
         for(int i = 0; i < mbPcpAcctBalances.size(); ++i) {
            super.update((EnsBaseDbBean)mbPcpAcctBalances.get(i));
         }
      }

   }

   public RbPcpAcctBalance getMbPcpAcctBal(String clientNo, String groupId) {
      RbPcpAcctBalance rbPcpAcctBalance = new RbPcpAcctBalance();
      rbPcpAcctBalance.setClientNo(clientNo);
      rbPcpAcctBalance.setPcpGroupId(groupId);
      return (RbPcpAcctBalance)this.daoSupport.selectOne(rbPcpAcctBalance);
   }

   public RbPcpAcctBalance getMbPcpAcctBalCompose(String clientNo, String groupId, Long internalKey) {
      RbPcpAcctBalance rbPcpAcctBalance = new RbPcpAcctBalance();
      rbPcpAcctBalance.setClientNo(clientNo);
      rbPcpAcctBalance.setPcpGroupId(groupId);
      rbPcpAcctBalance.setInternalKey(internalKey);
      return (RbPcpAcctBalance)this.daoSupport.selectOne(rbPcpAcctBalance);
   }

   public RbPcpAcctBalance getMbAcctBalAllForLock(String groupId, String clientNo, Long internalKey) {
      Map<String, Object> map = new HashMap();
      map.put("pcpGroupId", groupId);
      map.put("clientNo", clientNo);
      map.put("internal_key", internalKey);
      return (RbPcpAcctBalance)this.daoSupport.selectOne(RbPcpAcctBalance.class.getName() + ".getPcpAcctBalanceForUpdate", map);
   }

   public List<RbPcpAcctBalance> getAllMbAcctBalByGroupId(String groupId) {
      Map<String, Object> map = new HashMap();
      map.put("pcpGroupId", groupId);
      return this.daoSupport.selectList(RbPcpAcctBalance.class.getName() + ".getAllMbAcctBalByGroupId", map);
   }

   public void createMbAcctBalDb(List<RbPcpAcctBalance> mbPcpAcctBals) {
      if (BusiUtil.isNotNull(mbPcpAcctBals)) {
         for(int i = 0; i < mbPcpAcctBals.size(); ++i) {
            super.insert((EnsBaseDbBean)mbPcpAcctBals.get(i));
         }
      }

   }

   public void updateByPrimaryKey(RbPcpAcctBalance mbPcpAcctBalance) {
      String runDate = Context.getInstance().getRunDate();
      mbPcpAcctBalance.setLastChangeDate(DateUtil.parseDate(runDate));
      super.update(mbPcpAcctBalance);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateRbPcpAcctBalance(String groupId, String clientNo, Long internalKey, BigDecimal tranAmt) {
      RbPcpAcctBalance oldMbPcpAcctBalance = this.getMbAcctBalAllForLock(groupId, clientNo, internalKey);
      BigDecimal oldPcpBalance = oldMbPcpAcctBalance.getPcpBalance();
      BigDecimal oldTotalDownAmt = oldMbPcpAcctBalance.getTotalDownAmt();
      String lastChangeDate = DateUtil.formatDate(oldMbPcpAcctBalance.getLastChangeDate(), "yyyyMMdd");
      String tranDate = Context.getInstance().getTranDate();
      BigDecimal newPcpBalance = ((BigDecimal)BusiUtil.nvl(oldPcpBalance, BigDecimal.ZERO)).subtract(tranAmt);
      BigDecimal newTotalDownAmt = ((BigDecimal)BusiUtil.nvl(oldTotalDownAmt, BigDecimal.ZERO)).add(tranAmt);
      RbPcpAcctBalance newMbPcpAcctBalance = this.updMbAcctBal(groupId, clientNo, newPcpBalance, oldPcpBalance, (BigDecimal)null, oldTotalDownAmt, newTotalDownAmt, lastChangeDate, tranDate, "DOWN", oldMbPcpAcctBalance.getInternalKey());
      super.update(newMbPcpAcctBalance);
   }

   public void updateByPrimaryKeyByEod(RbPcpAcctBalance mbPcpAcctBalance) {
      super.update(mbPcpAcctBalance);
   }

   public void updatePrevBalForEod(Map map) {
      this.daoSupport.update(RbPcpAcctBalance.class.getName() + ".updatePrevBalForEod", map);
   }

   public RbPcpAcctBalance getMbAcctBalAllNotForLock(String groupId, String clientNo, Long internalKey) {
      Map<String, Object> map = new HashMap();
      map.put("pcpGroupId", groupId);
      map.put("clientNo", clientNo);
      map.put("internal_key", internalKey);
      return (RbPcpAcctBalance)this.daoSupport.selectOne(RbPcpAcctBalance.class.getName() + ".getMbAcctBalAllNotForLock", map);
   }

   public String getAcctBalByClientNo(String clientNo) {
      Map<String, Object> map = new HashMap();
      map.put("clientNo", clientNo);
      return (String)this.daoSupport.selectObject(RbPcpAcctBalance.class.getName() + ".getAcctBalByClientNo", map);
   }
}
