package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbServPreAccr;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbServPreAccrRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbServPreAccrRepository.class);

   public void insert(RbServPreAccr RbServPreAccr) {
      super.insert(RbServPreAccr);
   }

   public List<RbServPreAccr> selectBySeDate(String startDate, String endDate, String branch, String preAccrNo, String userId) {
      Map<String, Object> param = new HashMap();
      param.put("branch", branch);
      param.put("preAccrNo", preAccrNo);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("userId", userId);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbServPreAccr.class.getName() + ".selectBySeDate";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbServPreAccr> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public RbServPreAccr selectByPreAccrNo(String preAccrNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("preAccrNo", preAccrNo);
      return (RbServPreAccr)this.daoSupport.selectOne(RbServPreAccr.class.getName() + ".selectByPreAccrNo", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateByPreAccrNo(RbServPreAccr rbServPre) {
      super.update(rbServPre);
   }
}
