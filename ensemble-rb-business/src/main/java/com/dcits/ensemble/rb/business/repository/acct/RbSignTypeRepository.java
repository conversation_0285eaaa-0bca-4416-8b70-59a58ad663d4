package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbSignType;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbSignTypeRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbSignTypeRepository.class);

   public RbSignType getRbSignType(String signType, String company) {
      Map<String, Object> param = new HashMap();
      param.put("signType", signType);
      param.put("company", company);
      return (RbSignType)this.daoSupport.selectOne(RbSignType.class.getName() + ".getRbSignType", param);
   }
}
