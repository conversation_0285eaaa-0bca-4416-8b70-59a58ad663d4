package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.bc.component.td.certtd.sequence.DcPrecontractTrfSeqNo;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctSettleHist;
import com.dcits.ensemble.rb.business.model.acct.settle.MbAcctSettleModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctSettleHistRepsitory extends BusinessRepository {
   @Resource
   private RbAcctRepository mbAcctRepository;
   private static final String SETTLE_BANK_FLAG_O = "O";
   SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

   public RbAcctSettleHist createMbAcctSettle(RbAcctSettleHist mbAcctSettleModel) {
      RbAcctSettleHist rbAcctSettleHist = new RbAcctSettleHist();
      BeanUtil.copy(mbAcctSettleModel, rbAcctSettleHist);
      rbAcctSettleHist.setSettleNo((String)SequenceGenerator.nextValue(SequenceEnum.settleSeqNo, new String[]{Context.getInstance().getRunDate()}));
      return rbAcctSettleHist;
   }

   public List<RbAcctSettleHist> rbAcctSettleHists(Long internalKey) {
      RbAcctSettleHist rbAcctSettleHist = new RbAcctSettleHist();
      rbAcctSettleHist.setInternalKey(internalKey);
      return this.daoSupport.selectList(RbAcctSettleHist.class.getName() + ".selectByPrimaryKeyAndRel", rbAcctSettleHist);
   }

   public void addAcctSettleHist(List<MbAcctSettleModel> mbAcctSettles) {
      if (BusiUtil.isNotNull(mbAcctSettles)) {
         for(int i = 0; i < mbAcctSettles.size(); ++i) {
            MbAcctSettleModel mbAcctSettle = (MbAcctSettleModel)mbAcctSettles.get(i);
            this.insertRbAcctSettleHistsList(mbAcctSettle);
         }
      }

   }

   public void insertRbAcctSettleHistsList(MbAcctSettleModel mbAcctSettle) {
      new DcPrecontractTrfSeqNo();
      String dateStr = DateUtil.formatDate(new Date(), "yyyyMMdd");
      String trfSeqNo = dateStr + SequenceGenerator.nextValue(SequenceEnum.eventSeqNo);
      RbAcctSettleHist rbAcctSettleHist = new RbAcctSettleHist();
      rbAcctSettleHist.setSeqNo(trfSeqNo);
      rbAcctSettleHist.setSettleAcctClass(mbAcctSettle.getSettleAcctClass());
      RbAcct mbSubAcct;
      if (BusiUtil.isEquals(mbAcctSettle.getSettleBankFlag(), "O")) {
         mbSubAcct = null;
      } else if (BusiUtil.isNull(mbAcctSettle.getSettleAcctInternalKey())) {
         mbSubAcct = this.mbAcctRepository.getMbAcctInfo(mbAcctSettle.getSettleBaseAcctNo(), mbAcctSettle.getSettleProdType(), mbAcctSettle.getSettleAcctCcy(), mbAcctSettle.getSettleAcctSeqNo());
      } else {
         mbSubAcct = this.mbAcctRepository.getMbAcct(mbAcctSettle.getSettleAcctInternalKey(), mbAcctSettle.getSettleClient());
      }

      rbAcctSettleHist.setClientNo(mbAcctSettle.getClientNo());
      rbAcctSettleHist.setCompany(mbAcctSettle.getCompany());
      rbAcctSettleHist.setSettleAcctInternalKey(mbAcctSettle.getInternalKey());
      rbAcctSettleHist.setSettleAcctCcy(mbAcctSettle.getSettleAcctCcy());
      rbAcctSettleHist.setSettleAcctName(mbAcctSettle.getSettleAcctName());
      rbAcctSettleHist.setSettleAcctSeqNo(mbAcctSettle.getSettleAcctSeqNo());
      rbAcctSettleHist.setSettleBaseAcctNo(mbAcctSettle.getSettleBaseAcctNo());
      rbAcctSettleHist.setSettleProdType(mbAcctSettle.getSettleProdType());
      rbAcctSettleHist.setSettleBankFlag(mbAcctSettle.getSettleBankFlag());
      rbAcctSettleHist.setTranTimestamp(this.sdf.format(new Date()));
      rbAcctSettleHist.setSettleClient(mbAcctSettle.getClientNo());
      rbAcctSettleHist.setInternalKey(mbAcctSettle.getInternalKey());
      rbAcctSettleHist.setAcctSettleOperateType("01");
      rbAcctSettleHist.setSettleMobilePhone(mbAcctSettle.getSettleMobilePhone());
      rbAcctSettleHist.setLastChargeDate(new Date());
      rbAcctSettleHist.setLastChangeUserId(Context.getInstance().getUserId());
      if (BusiUtil.isNotNull(mbSubAcct)) {
         rbAcctSettleHist.setSettleBranch(mbSubAcct.getAcctBranch());
         rbAcctSettleHist.setCompany(mbSubAcct.getCompany());
      } else {
         rbAcctSettleHist.setCompany(mbAcctSettle.getCompany());
      }

      super.insert(rbAcctSettleHist);
   }

   public void insert(RbAcctSettleHist rbAcctSettleHist) {
      super.insert(rbAcctSettleHist);
   }
}
