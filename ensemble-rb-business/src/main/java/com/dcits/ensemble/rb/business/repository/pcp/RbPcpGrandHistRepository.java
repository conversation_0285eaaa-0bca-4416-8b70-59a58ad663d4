package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpGrandHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbPcpGrandHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpGrandHistRepository.class);

   public List<RbPcpGrandHist> selectLastHistByDate(Long internalkey, String groupId) {
      Map<String, Object> param = new HashMap();
      param.put("internalkey", internalkey);
      param.put("tranDate", DateUtil.parseDate(Context.getInstance().getLastRunDate()));
      param.put("pcpGroupId", groupId);
      return this.daoSupport.selectList(RbPcpGrandHist.class.getName() + ".selectLastHistByDate", param);
   }

   public int updatePcpGrandHist(RbPcpGrandHist mbPcpGrandHist) {
      return super.update(mbPcpGrandHist);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public int createPcpGrandHist(RbPcpGrandHist mbPcpGrandHist) {
      return super.insert(mbPcpGrandHist);
   }

   public RbPcpGrandHist selectByKeyAndDate(Long internalkey, String date) {
      Map<String, Object> param = new HashMap();
      param.put("internalkey", internalkey + "");
      param.put("tranDate", date);
      return (RbPcpGrandHist)this.daoSupport.selectOne(RbPcpGrandHist.class.getName() + ".selectByKeyAndDate", param);
   }
}
