package com.dcits.ensemble.rb.business.repository.acct;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbProdRelationDefine;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public class RbProdRelationDefineRepository extends BusinessRepository {
   @Cached(
      area = "middleArea",
      name = "rb:product:skipPart",
      key = "'getSkipPart'+'-'+#mainProdType+'-'+#subProdType+'-'+#eventType",
      cacheType = CacheType.REMOTE
   )
   public List<RbProdRelationDefine> getSkipPart(String mainProdType, String subProdType, String eventType, String company) {
      Map<String, Object> param = new HashMap();
      param.put("prodType", mainProdType);
      param.put("subProdType", subProdType);
      param.put("eventType", eventType);
      param.put("company", company);
      return this.daoSupport.selectList(RbProdRelationDefine.class.getName() + ".getSkipPart", param);
   }

   public EnsBaseDbBean getDBRecord() {
      return new RbProdRelationDefine();
   }
}
