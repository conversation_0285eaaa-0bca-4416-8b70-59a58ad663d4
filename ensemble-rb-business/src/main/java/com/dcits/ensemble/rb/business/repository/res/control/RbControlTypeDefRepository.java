package com.dcits.ensemble.rb.business.repository.res.control;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbControlTypeDef;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbControlTypeDefRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbControlTypeDefRepository.class);

   public List<RbControlTypeDef> selectListByControlType(String controlType) {
      RbControlTypeDef rbControlTypeDef = new RbControlTypeDef();
      rbControlTypeDef.setControlType(controlType);
      return this.daoSupport.selectList(rbControlTypeDef);
   }

   @Cached(
      area = "longArea",
      name = "rb:param:rb_control_type_def:",
      key = "#controlType",
      cacheType = CacheType.REMOTE
   )
   public List<RbControlTypeDef> selectListByControlTypeForCache(String controlType) {
      return this.selectListByControlType(controlType);
   }
}
