package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.bc.component.cm.operate.util.PageQueryUtil;
import com.dcits.ensemble.rb.business.common.util.context.BusiContextUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAttach;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbContactList;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAcctAttachRepository extends BusinessRepository<RbAcctAttach> {
   @Resource
   private PageQueryUtil pageQueryUtil;

   public String getContraAcctNo(Long internalKey) {
      if (internalKey == null) {
         return "";
      } else {
         Map<String, Object> map = new HashMap();
         map.put("internalKey", internalKey);
         return (String)this.daoSupport.selectObject("selectContraAcctNo", map);
      }
   }

   public List<RbAcctAttach> selectAttachList(List<Long> internalKeyList, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKeyList", internalKeyList);
      param.put("clientNo", clientNo);
      return super.selectList(RbAcctAttach.class.getName() + ".selectAttachList", param);
   }

   public RbAcctAttach getRbAcctAttach(Long internalKey, String clientNo) {
      RbAcctAttach rbAcctAttach = BusiContextUtil.getRbAcctAttach(internalKey);
      if (BusiUtil.isNull(rbAcctAttach)) {
         rbAcctAttach = new RbAcctAttach();
         rbAcctAttach.setInternalKey(internalKey);
         rbAcctAttach.setClientNo(clientNo);
         rbAcctAttach = (RbAcctAttach)super.selectOne(rbAcctAttach);
         if (BusiUtil.isNotNull(rbAcctAttach)) {
            BusiContextUtil.putRbAcctAttach(rbAcctAttach);
         }
      }

      return rbAcctAttach;
   }

   public List<RbAcctAttach> getBlackUncheckByInternalKeys(String clientNo, String company, List<Long> internalKeys) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("company", company);
      param.put("acctVerifyFlag", "N");
      param.put("internalKeys", internalKeys);
      return this.pageQueryUtil.selectByPage(RbAcctAttach.class.getName() + ".selectBlackUncheckByInternalKeys", param);
   }

   public Long getInternalKey(String contraAcctNo) {
      if (contraAcctNo != null && !contraAcctNo.equals("")) {
         Map<String, Object> map = new HashMap();
         map.put("contraBaseAcctNo", contraAcctNo);
         return (Long)this.daoSupport.selectObject("selectInternalKey", map);
      } else {
         return null;
      }
   }

   public int updateByPrimaryKey(RbAcctAttach rbAcctAttach) {
      return super.update(rbAcctAttach);
   }

   public void deleteByPrimaryKey(RbAcctAttach rbAcctAttach) {
      super.delete(rbAcctAttach);
   }

   public void updateByPrimaryKeyCommit(RbAcctAttach rbAcctAttach) {
      rbAcctAttach.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctAttach.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctAttach.class.getName() + ".updateByPrimaryKeyClinet", rbAcctAttach);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateByPrimaryKey1(RbAcctAttach rbAcctAttach) {
      super.update(rbAcctAttach);
   }

   public RbAcctAttach getMbAcctAttach(Long internalKey, String clientNo) {
      RbAcctAttach rbAcctAttach = BusiContextUtil.getRbAcctAttach(internalKey);
      if (BusiUtil.isNull(rbAcctAttach)) {
         rbAcctAttach = new RbAcctAttach();
         rbAcctAttach.setInternalKey(internalKey);
         rbAcctAttach.setClientNo(clientNo);
         rbAcctAttach = (RbAcctAttach)this.daoSupport.selectOne(rbAcctAttach);
         if (BusiUtil.isNotNull(rbAcctAttach)) {
            BusiContextUtil.putRbAcctAttach(rbAcctAttach);
         }
      }

      return rbAcctAttach;
   }

   public RbAcctAttach getAcctAttachData(Long internalKey) {
      RbAcctAttach rbAcctAttach = new RbAcctAttach();
      rbAcctAttach.setInternalKey(internalKey);
      return (RbAcctAttach)this.daoSupport.selectOne(rbAcctAttach);
   }

   public List<RbAcctAttach> getAcctVerifyResult(Long internalKey, String startDate, String endDate, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("comapnt", company);
      return this.daoSupport.selectList(RbAcctAttach.class.getName() + ".getAcctVerifyResult", param);
   }

   public void createMbAcctAttachDb(List<RbAcctAttach> rbAcctAttaches) {
      if (BusiUtil.isNotNull(rbAcctAttaches)) {
         for(int i = 0; i < rbAcctAttaches.size(); ++i) {
            super.insert(rbAcctAttaches.get(i));
         }
      }

   }

   public void updateMbAcctAttachDbList(List<RbAcctAttach> rbAcctAttaches) {
      for(int i = 0; i < rbAcctAttaches.size(); ++i) {
         super.update(rbAcctAttaches.get(i));
      }

   }

   public void updateMbAcctAttachDbList1(List<RbAcctAttach> rbAcctAttaches) {
      ((RbAcctAttach)rbAcctAttaches.get(0)).setCompany(Context.getInstance().getCompany());

      for(int i = 0; i < rbAcctAttaches.size(); ++i) {
         this.daoSupport.update(RbAcctAttach.class.getName() + ".updateMbAcctAttachDbList1", rbAcctAttaches);
      }

   }

   public List<RbAcctAttach> getRbAcctAttachList(RbAcctAttach rbAcctAttach) {
      return this.daoSupport.selectList(rbAcctAttach);
   }

   public List<RbAcctAttach> getBlackUncheck(String clientNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctAttach.class.getName() + ".selectBlackUncheck", param);
   }

   public void updateByInternalKey(RbAcctAttach rbAcctAttach) {
      rbAcctAttach.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAcctAttach.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAcctAttach.class.getName() + ".updateProofStatusByPrimaryKey", rbAcctAttach);
   }

   public List<RbAcctAttach> selectBalList(List<Long> internalKeyList) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKeyList);
      return this.daoSupport.selectList(RbAcctAttach.class.getName() + ".selectBalList", param);
   }

   public void updateMbAcctAttachByInternalKeyAndClientNo(RbAcctAttach rbAcctAttach) {
      rbAcctAttach.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctAttach.class.getName() + ".updateMbAcctAttachByInternalKeyAndClientNo", rbAcctAttach);
   }

   public void updateByReOpenDate(List<RbAcct> rbAccts) {
      Iterator var2 = rbAccts.iterator();

      while(var2.hasNext()) {
         RbAcct rbAcct = (RbAcct)var2.next();
         RbAcctAttach rbAcctAttach = this.getMbAcctAttach(rbAcct.getInternalKey(), rbAcct.getClientNo());
         rbAcctAttach.setReOpenDate(Context.getInstance().getRunDateParse());
         this.daoSupport.update(rbAcctAttach);
      }

   }

   public void deleteMbAcctAttachByInternalKeyDb(RbAcctAttach rbAcctAttach) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", rbAcctAttach.getInternalKey());
      param.put("clientNo", rbAcctAttach.getClientNo());
      this.daoSupport.delete(RbContactList.class.getName() + ".deleteByInternalKeyAndClientNo", param);
   }

   public List<RbAcctAttach> queryByContraBaseAcctNo(String contraBaseAcctNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("contraBaseAcctNo", contraBaseAcctNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctAttach.class.getName() + ".queryByContraBaseAcctNo", param);
   }

   public void updateClientNo(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.selectList(RbAcctAttach.class.getName() + ".updateClientNo", map);
   }

   public void updateRiskByInternalKey(RbAcctAttach rbAcctAttach) {
      rbAcctAttach.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctAttach.class.getName() + ".updateRiskByInternalKey", rbAcctAttach);
   }

   public RbAcctAttach getAttachInfo(Long internalKey, String clientNo) {
      RbAcctAttach rbAcctAttach = new RbAcctAttach();
      rbAcctAttach.setInternalKey(internalKey);
      rbAcctAttach.setClientNo(clientNo);
      return (RbAcctAttach)this.daoSupport.selectOne(rbAcctAttach);
   }

   public void updAttachInfoSettleFlag(Long internalKey, String clientNo) {
      RbAcctAttach rbAcctAttach = new RbAcctAttach();
      rbAcctAttach.setInternalKey(internalKey);
      rbAcctAttach.setClientNo(clientNo);
      rbAcctAttach.setAutoSettleFlag("N");
      this.daoSupport.update(RbAcctAttach.class.getName() + ".updAttachInfoSettleFlag", rbAcctAttach);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertRbAcctAttach(RbAcctAttach rbAcctAttach) {
      if (BusiUtil.isNull(rbAcctAttach)) {
         super.insert(rbAcctAttach);
      }

   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public int updateResult(RbAcctAttach rbAcctAttach) {
      return super.update(rbAcctAttach);
   }
}
