package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementSupplementHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementSupplementHistRepository extends BusinessRepository {
   public void create(RbAgreementSupplementHist rbAgreementSupplementHist) {
      if (BusiUtil.isNotNull(rbAgreementSupplementHist)) {
         super.insert(rbAgreementSupplementHist);
      }

   }
}
