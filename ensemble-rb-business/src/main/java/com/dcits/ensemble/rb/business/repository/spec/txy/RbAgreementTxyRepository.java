package com.dcits.ensemble.rb.business.repository.spec.txy;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementTxy;
import com.dcits.ensemble.rb.business.model.acct.RbAcctMasterModel;
import com.dcits.ensemble.rb.business.model.acct.RbAcctModel;
import com.dcits.ensemble.rb.business.repository.agr.RbAgreementRepository;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAgreementTxyRepository extends BusinessRepository<RbAgreementTxy> {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementTxyRepository.class);
   @Resource
   RbAgreementRepository rbAgreementRepository;

   public RbAgreementTxy getAgreementTxyBySignId(String signId) {
      Map<String, Object> map = new HashMap(5);
      map.put("signId", signId);
      return (RbAgreementTxy)this.daoSupport.selectOne(RbAgreementTxy.class.getName() + ".getAgreementTxyBySignId", map);
   }

   public List<RbAgreementTxy> getAgreementTxyByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("internalKey", internalKey);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementTxy.class.getName() + ".getAgreementTxyByInternalKey", map);
   }

   public RbAgreementTxy getAgreementTxyByAgreementId(String agreementId, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("agreementId", agreementId);
      map.put("clientNo", clientNo);
      return (RbAgreementTxy)this.daoSupport.selectOne(RbAgreementTxy.class.getName() + ".getAgreementTxyByAgreementId", map);
   }

   public List<RbAgreementTxy> getAgreementTxyByMainAgreementId(String mainAgreementId, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("mainAgreementId", mainAgreementId);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementTxy.class.getName() + ".getAgreementTxyByMainAgreementId", map);
   }

   public void createMbAgreementTxy(RbAgreementTxy rbAgreementTxy) {
      if (BusiUtil.isNotNull(rbAgreementTxy)) {
         super.insert(rbAgreementTxy);
      }

   }

   public void updateAgreementWithCycleFreqNullByPrimary(RbAgreementTxy rbAgreementTxy) {
      this.daoSupport.update(RbAgreementTxy.class.getName() + ".updateWithNullByPrimary", rbAgreementTxy);
   }

   public void updateIntAccrued(Map<String, BigDecimal> rbTxyAccrMap, RbAcctMasterModel rbAcctMasterModel) {
      RbAcctModel rbAcctModel = rbAcctMasterModel.getRbAcctModel();
      BigDecimal agg = (BigDecimal)rbTxyAccrMap.get("agg");
      BigDecimal intAccrued = (BigDecimal)rbTxyAccrMap.get("intAccrued");
      List<RbAgreementTxy> agreementTxys = this.getAgreementTxyByInternalKey(rbAcctModel.getInternalKey(), rbAcctModel.getClientNo());
      Iterator var7 = agreementTxys.iterator();

      while(var7.hasNext()) {
         RbAgreementTxy rbAgreementTxy = (RbAgreementTxy)var7.next();
         if (BusiUtil.isEquals(rbAgreementTxy.getMainFlag(), "M")) {
            RbAgreement activeAgreement2 = this.rbAgreementRepository.getActiveAgreement2(rbAgreementTxy.getAgreementId(), rbAgreementTxy.getClientNo());
            if (BusiUtil.isNotNull(activeAgreement2)) {
               rbAgreementTxy.setAgg(((BigDecimal)BusiUtil.nvl(rbAgreementTxy.getAgg(), BigDecimal.ZERO)).add(agg));
               rbAgreementTxy.setIntAccruedCalcCtd(((BigDecimal)BusiUtil.nvl(rbAgreementTxy.getIntAccruedCalcCtd(), BigDecimal.ZERO)).add(intAccrued));
               this.updateByPrimaryKey(rbAgreementTxy);
            }
         }
      }

   }
}
