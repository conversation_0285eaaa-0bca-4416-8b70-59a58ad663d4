package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctAppointment;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAcctAppointmentRepository extends BusinessRepository {
   @Resource
   private FmBaseStor fmBaseStor;

   public RbAcctAppointment selectByBbAcctAppointment(RbAcctAppointment rbAcctAppointment) {
      return (RbAcctAppointment)this.daoSupport.selectOne(rbAcctAppointment);
   }

   public List<RbAcctAppointment> selectByAcctAppointment(RbAcctAppointment rbAcctAppointment) {
      return this.daoSupport.selectList(rbAcctAppointment);
   }

   public List<RbAcctAppointment> selectByDueAndDate(String orderId, String baseAcctNo, String appointmentStatus, String clientName, Date startDate, Date endDate) {
      Map<String, Object> map = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(map, false);
      map.put("applyId", orderId);
      map.put("clientName", clientName);
      map.put("startDate", startDate);
      map.put("endDate", endDate);
      map.put("baseAcctNo", baseAcctNo);
      map.put("appointmentStatus", appointmentStatus);
      return this.daoSupport.selectList("selectByDueAndDate", map);
   }

   public int createRbAcctAppointment(RbAcctAppointment rbAcctAppointment) {
      return super.insert(rbAcctAppointment);
   }

   public int update(RbAcctAppointment rbAcctAppointment) {
      return super.update(rbAcctAppointment);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public int updateByEntity(RbAcctAppointment rbAcctAppointment) {
      return super.update(rbAcctAppointment);
   }

   public int delete(RbAcctAppointment rbAcctAppointment) {
      return this.daoSupport.delete(rbAcctAppointment);
   }
}
