package com.dcits.ensemble.rb.business.repository.res.control;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbChannelControlRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbChannelControlRepository.class);

   public List<RbChannelControl> selectEffectingControl(String clientNo, Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbChannelControl.class.getName() + ".selectChannelControlEffecting", param);
   }

   public List<RbChannelControl> selectChannelControlList(@NonNull RbChannelControl rbChannelControl) {
      if (rbChannelControl == null) {
         throw new NullPointerException("rbChannelControl");
      } else {
         return this.daoSupport.selectList(RbChannelControl.class.getName() + ".selectChannelControl", rbChannelControl);
      }
   }

   public List<RbChannelControl> selectChannelControlNotEnd(String clientNo, Long internalKey, String controlType) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("internalKey", internalKey);
      param.put("controlType", controlType);
      return this.daoSupport.selectList(RbChannelControl.class.getName() + ".selectChannelControlNotEnd", param);
   }

   public int createChannelControl(@NonNull RbChannelControl rbChannelControl) {
      if (rbChannelControl == null) {
         throw new NullPointerException("rbChannelControl");
      } else {
         rbChannelControl.setControlSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.controlSeqNo));
         return this.insert(rbChannelControl);
      }
   }

   public int updateChannelControl(@NonNull RbChannelControl rbChannelControl) {
      if (rbChannelControl == null) {
         throw new NullPointerException("rbChannelControl");
      } else {
         return this.update(rbChannelControl);
      }
   }

   public RbChannelControl selectOneByControlSeqNoClintNo(String ControlSeqNo, String ClientNo) {
      RbChannelControl rbChannelControl = new RbChannelControl();
      rbChannelControl.setControlSeqNo(ControlSeqNo);
      rbChannelControl.setClientNo(ClientNo);
      return (RbChannelControl)this.daoSupport.selectOne(rbChannelControl);
   }
}
