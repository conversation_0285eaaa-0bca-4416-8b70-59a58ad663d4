package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPrice;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpInnerPriceHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpInnerPriceHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpInnerPriceHistRepository.class);

   public void insert(RbPcpInnerPriceHist mbPcpInnerPriceHist) {
      mbPcpInnerPriceHist.setSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbPcpIPHSeqNo));
      super.insert(mbPcpInnerPriceHist);
   }

   public RbPcpInnerPriceHist creatInnerPriceHist(RbPcpInnerPrice mbPcpInnerPrice) {
      return (RbPcpInnerPriceHist)BeanUtil.copyByClass(mbPcpInnerPrice, RbPcpInnerPriceHist.class);
   }
}
