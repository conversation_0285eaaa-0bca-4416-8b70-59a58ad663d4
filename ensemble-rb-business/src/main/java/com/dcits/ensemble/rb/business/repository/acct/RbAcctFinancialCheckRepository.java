package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.dao.DaoSupport;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.cm.accounting.AccountingCheckModel;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctFinancialCheck;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctFinancialCheckRepository extends BusinessRepository {
   @Autowired
   private DaoSupport daoSupport;

   public void insertAll(List<RbAcctFinancialCheck> rbAcctFinancialCheckList) {
      super.insertAddBatch(rbAcctFinancialCheckList);
   }

   public List<AccountingCheckModel> selectBalList(RbAcctFinancialCheck rbAcctFinancialCheck, List<Long> internalKeyList) {
      Map<String, Object> map = new HashMap();
      map.put("branch", rbAcctFinancialCheck.getBranch());
      map.put("ccy", rbAcctFinancialCheck.getCcy());
      map.put("prodType", rbAcctFinancialCheck.getProdType());
      map.put("runDate", Context.getInstance().getRunDateParse());
      map.put("lastRunDateStr", Context.getInstance().getLastRunDate());
      map.put("lastRunDate", Context.getInstance().getLastRunDateParse());
      map.put("yesterday", Context.getInstance().getYesterdayParse());
      map.put("internalKey", internalKeyList);
      map.put("company", rbAcctFinancialCheck.getCompany());
      return this.daoSupport.selectList(RbAcctFinancialCheck.class.getName() + ".selectBalList", map);
   }

   public List<AccountingCheckModel> selectIntList(RbAcctFinancialCheck rbAcctFinancialCheck, List<Long> internalKeyList) {
      Map<String, Object> map = new HashMap();
      map.put("branch", rbAcctFinancialCheck.getBranch());
      map.put("ccy", rbAcctFinancialCheck.getCcy());
      map.put("prodType", rbAcctFinancialCheck.getProdType());
      map.put("runDate", Context.getInstance().getRunDateParse());
      map.put("lastRunDateStr", Context.getInstance().getLastRunDate());
      map.put("lastRunDate", Context.getInstance().getLastRunDateParse());
      map.put("yesterday", Context.getInstance().getYesterdayParse());
      map.put("internalKey", internalKeyList);
      map.put("company", rbAcctFinancialCheck.getCompany());
      return this.daoSupport.selectList(RbAcctFinancialCheck.class.getName() + ".selectIntList", map);
   }

   public List<AccountingCheckModel> selectFinBalList(RbAcctFinancialCheck rbAcctFinancialCheck, List<Long> internalKeyList) {
      Map<String, Object> map = new HashMap();
      map.put("branch", rbAcctFinancialCheck.getBranch());
      map.put("ccy", rbAcctFinancialCheck.getCcy());
      map.put("prodType", rbAcctFinancialCheck.getProdType());
      map.put("runDate", Context.getInstance().getRunDateParse());
      map.put("lastRunDateStr", Context.getInstance().getLastRunDate());
      map.put("lastRunDate", Context.getInstance().getLastRunDateParse());
      map.put("yesterday", Context.getInstance().getYesterdayParse());
      map.put("internalKey", internalKeyList);
      map.put("company", rbAcctFinancialCheck.getCompany());
      return this.daoSupport.selectList(RbAcctFinancialCheck.class.getName() + ".selectFinBalList", map);
   }

   public List<AccountingCheckModel> selectFinIntList(RbAcctFinancialCheck rbAcctFinancialCheck, List<Long> internalKeyList) {
      Map<String, Object> map = new HashMap();
      map.put("branch", rbAcctFinancialCheck.getBranch());
      map.put("ccy", rbAcctFinancialCheck.getCcy());
      map.put("prodType", rbAcctFinancialCheck.getProdType());
      map.put("runDate", Context.getInstance().getRunDateParse());
      map.put("lastRunDateStr", Context.getInstance().getLastRunDate());
      map.put("lastRunDate", Context.getInstance().getLastRunDateParse());
      map.put("yesterday", Context.getInstance().getYesterdayParse());
      map.put("internalKey", internalKeyList);
      map.put("company", rbAcctFinancialCheck.getCompany());
      return this.daoSupport.selectList(RbAcctFinancialCheck.class.getName() + ".selectFinIntList", map);
   }
}
