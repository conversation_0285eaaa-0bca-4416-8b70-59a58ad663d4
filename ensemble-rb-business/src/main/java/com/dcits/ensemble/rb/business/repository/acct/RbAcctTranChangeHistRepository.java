package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctTranChangeHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctTranChangeHistRepository extends BusinessRepository {
   public List<RbAcctTranChangeHist> getRbAcctTranChangeHistOrderByTranDate(Long internalKey) {
      RbAcctTranChangeHist rbAcctTranChangeHist = new RbAcctTranChangeHist();
      rbAcctTranChangeHist.setInternalKey(internalKey);
      return this.daoSupport.selectList(RbAcctTranChangeHist.class.getName() + ".getRbAcctTranChangeHistOrderByTranDate", rbAcctTranChangeHist);
   }

   public void updateRbAcctTranChangeHistForIncrease(Long internalKey, String tranDate, BigDecimal amount) {
   }

   public void updateRbAcctTranChangeHistForReduce(Long internalKey, String tranDate, BigDecimal amount) {
   }
}
