package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetailHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class RbAcctIntDetailHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctIntDetailHistRepository.class);

   public RbAcctIntDetailHist getRbAcctIntDetailHist(RbAcctIntDetailHist rbAcctIntDetailHist) {
      return (RbAcctIntDetailHist)this.daoSupport.selectOne(rbAcctIntDetailHist);
   }

   public List<RbAcctIntDetailHist> getRbAcctIntDetailHistAll(RbAcctIntDetailHist rbAcctIntDetailHist) {
      return this.daoSupport.selectList(rbAcctIntDetailHist);
   }
}
