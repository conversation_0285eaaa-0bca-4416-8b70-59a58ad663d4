package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.common.util.AppHeadUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbEbAcctHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
@BusiUnit
public class RbEbAcctHistRepository extends BusinessRepository<RbEbAcctHist> {
   private static final Logger log = LoggerFactory.getLogger(RbEbAcctHistRepository.class);

   public List<RbEbAcctHist> selectByParam(String baseAcctNo, String documentId, String documentType, String busiType, String tradeType, String acctType) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("documentId", documentId);
      param.put("documentType", documentType);
      param.put("busiType", busiType);
      param.put("tradeType", tradeType);
      param.put("acctType", acctType);
      RowArgs rowArgs = AppHeadUtil.getRowArgs();
      String statementPostfix = RbEbAcctHist.class.getName() + ".selectByParam";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbEbAcctHist> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }
}
