package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbElementValueTemp;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbElementValueTempReposity extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbElementValueTempReposity.class);

   public int deleteAll(RbElementValueTemp rbElementValueTemp) {
      return this.daoSupport.delete(RbElementValueTemp.class.getName() + ".deleteAll", rbElementValueTemp);
   }

   public int insertBatch(List<RbElementValueTemp> mbElementValueTemps) {
      return super.insertAddBatch(mbElementValueTemps);
   }

   public RbElementValueTemp createNewMbElementValueTemp(Long tempSeqNo) {
      RbElementValueTemp mbElementValueTemp = new RbElementValueTemp();
      if (BusiUtil.isNotNull(tempSeqNo)) {
         mbElementValueTemp.setElemSeqNo(String.valueOf(tempSeqNo));
      } else {
         mbElementValueTemp.setElemSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.elementValueTempSeqNo));
      }

      return mbElementValueTemp;
   }

   public List<RbElementValueTemp> selectByInternalKeyFeeType(Long internalKey, String feeType, String runDate) {
      RbElementValueTemp mbElementValueTemp = new RbElementValueTemp();
      mbElementValueTemp.setInternalKey(internalKey);
      mbElementValueTemp.setFeeType(feeType);
      mbElementValueTemp.setRunDate(DateUtil.parseDate(runDate));
      return this.daoSupport.selectList(RbElementValueTemp.class.getName() + ".selectByInternalKeyFeeType", mbElementValueTemp);
   }
}
