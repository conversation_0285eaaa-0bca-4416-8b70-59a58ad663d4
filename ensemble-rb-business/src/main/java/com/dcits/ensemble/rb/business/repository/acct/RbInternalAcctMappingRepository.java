package com.dcits.ensemble.rb.business.repository.acct;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.comet.boot.autoconfigure.common.mybatis.MaxSelectLimit;
import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.bc.unit.acct.business.dac.RbOpenInnerAcctDac;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbInternalAcctMapping;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbInternalAcctMappingRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbInternalAcctMappingRepository.class);
   @Resource
   private RbOpenInnerAcctDac rbOpenInnerAcctDac;
   @Resource
   private FmBaseStor fmBaseStor;

   public RbInternalAcctMapping getMappingByChannal(String channelType, String tranType, String branch, String ccy) {
      new RbInternalAcctMapping();
      Map<String, Object> param = new HashMap();
      param.put("channelTypeRule", channelType);
      param.put("tranType", tranType);
      param.put("acctBranch", branch);
      param.put("acctCcy", ccy);
      List<RbInternalAcctMapping> rbInternalAcctMapping = this.daoSupport.selectList(RbInternalAcctMapping.class.getName() + ".selectOneExt", param);
      if (rbInternalAcctMapping.size() > 1) {
         throw BusiUtil.createBusinessException("RB6835", new String[]{tranType, branch});
      } else if (!rbInternalAcctMapping.isEmpty()) {
         return (RbInternalAcctMapping)rbInternalAcctMapping.get(0);
      } else {
         throw new BusinessException("RB6863", BusiUtil.getMessageByKey("MG0197"));
      }
   }

   public RbInternalAcctMapping getMappingByChannelForOpen(String channelType, String tranType, String branch, String ccy) {
      Map<String, Object> param = new HashMap();
      param.put("channelTypeRule", channelType);
      param.put("tranType", tranType);
      param.put("acctBranch", branch);
      param.put("acctCcy", ccy);
      return (RbInternalAcctMapping)this.daoSupport.selectOne(RbInternalAcctMapping.class.getName() + ".selectOneExt", param);
   }

   @Cached(
      area = "longArea",
      name = "rb:param:rb_internal_acct_mapping:acct_mapping:",
      key = "#tranType+'-'+#branch+'-'+#ccy",
      cacheType = CacheType.REMOTE
   )
   public List<RbInternalAcctMapping> selectList(String tranType, String branch, String ccy) {
      RbInternalAcctMapping rbInternalAcctMapping = new RbInternalAcctMapping();
      rbInternalAcctMapping.setTranType(tranType);
      rbInternalAcctMapping.setAcctBranch(branch);
      rbInternalAcctMapping.setAcctCcy(ccy);
      return this.daoSupport.selectList(rbInternalAcctMapping);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void createRbInternalAcctMappingDb(RbInternalAcctMapping rbInternalAcctMapping) {
      super.insert(rbInternalAcctMapping);
   }

   public RbInternalAcctMapping getOneRecordByTranType(String tranType) {
      RbInternalAcctMapping rbInternalAcctMapping = new RbInternalAcctMapping();
      rbInternalAcctMapping.setTranType(tranType);
      return (RbInternalAcctMapping)this.daoSupport.selectOne(rbInternalAcctMapping);
   }

   public Integer getInnerAcctMappingFlag(String baseAcctNo) {
      Map<String, Object> param = new HashMap(3);
      param.put("baseAcctNo", baseAcctNo);
      return (Integer)this.daoSupport.selectObject(RbInternalAcctMapping.class.getName() + ".getInnerAcctMappingFlag", param);
   }

   public Map<String, Integer> getInnerAcctMappingList(List<String> baseAcctNos) {
      Map<String, Object> param = new HashMap(2);
      Map<String, Integer> resultMap = new HashMap();
      param.put("baseAcctNos", baseAcctNos);
      List<RbInternalAcctMapping> rbInternalAcctMappingList = this.daoSupport.selectList(RbInternalAcctMapping.class.getName() + ".getInnerAcctMappingListBefore", param);
      if (rbInternalAcctMappingList.size() > 0) {
         Map<String, List<RbInternalAcctMapping>> rbInternalAcctMappingListMap = (Map)rbInternalAcctMappingList.stream().collect(Collectors.groupingBy(RbInternalAcctMapping::getBaseAcctNo));
         Set<Entry<String, List<RbInternalAcctMapping>>> entries = rbInternalAcctMappingListMap.entrySet();
         Iterator var7 = entries.iterator();

         while(var7.hasNext()) {
            Entry<String, List<RbInternalAcctMapping>> map = (Entry)var7.next();
            resultMap.put(map.getKey(), ((List)map.getValue()).size());
         }
      }

      return resultMap;
   }

   public RbInternalAcctMapping getMapping(String tranType, String branchId, String acctCcy, String company) {
      Map<String, Object> param = new HashMap();
      param.put("tranType", tranType);
      param.put("branch", branchId);
      param.put("ccy", acctCcy);
      param.put("company", company);
      return (RbInternalAcctMapping)this.daoSupport.selectOne(RbInternalAcctMapping.class.getName() + ".getMapping", param);
   }

   @MaxSelectLimit(
      open = true,
      limitRows = 5000
   )
   public List<RbInternalAcctMapping> selectListByInterTranType(String interTranType) {
      RbInternalAcctMapping rbInternalAcctMapping = new RbInternalAcctMapping();
      rbInternalAcctMapping.setInterTranType(interTranType);
      return this.daoSupport.selectList(rbInternalAcctMapping);
   }

   public RbInternalAcctMapping getMappingByChannalAndCompany(String channelType, String tranType, String branch, String ccy) {
      Map<String, Object> param = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("channelTypeRule", channelType);
      param.put("tranType", tranType);
      param.put("acctBranch", branch);
      param.put("acctCcy", ccy);
      return (RbInternalAcctMapping)this.daoSupport.selectOne(RbInternalAcctMapping.class.getName() + ".getMappingByChannalAndCompany", param);
   }
}
