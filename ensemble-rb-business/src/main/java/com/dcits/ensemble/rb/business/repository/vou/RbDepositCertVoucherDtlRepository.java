package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbDepositCertVoucherDtl;
import com.dcits.ensemble.rb.business.model.vou.voucher.MbDepositCertModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbDepositCertVoucherDtlRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbDepositCertVoucherDtlRepository.class);

   public List<RbDepositCertVoucherDtl> getDepositCertVoucher(String mbDepositCertNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("mbDepositCertNo", mbDepositCertNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbDepositCertVoucherDtl.class.getName() + ".getDepositCertVoucher", param);
   }

   public void createMbDepositCertVoucher(List<RbDepositCertVoucherDtl> rbDepositCertVoucherDtlsList) {
      if (BusiUtil.isNotNull(rbDepositCertVoucherDtlsList)) {
         Iterator var2 = rbDepositCertVoucherDtlsList.iterator();

         while(var2.hasNext()) {
            RbDepositCertVoucherDtl rbDepositCertVoucherDtl = (RbDepositCertVoucherDtl)var2.next();
            super.insert(rbDepositCertVoucherDtl);
         }
      }

   }

   public void updateMbDepositCertVoucher(List<RbDepositCertVoucherDtl> rbDepositCertVoucherDtlsList) {
      if (BusiUtil.isNotNull(rbDepositCertVoucherDtlsList)) {
         Iterator var2 = rbDepositCertVoucherDtlsList.iterator();

         while(var2.hasNext()) {
            RbDepositCertVoucherDtl rbDepositCertVoucherDtl = (RbDepositCertVoucherDtl)var2.next();
            this.daoSupport.update(RbDepositCertVoucherDtl.class.getName() + ".updateByDepositCertNo", rbDepositCertVoucherDtl);
         }
      }

   }

   protected int updateByVoucherNo(MbDepositCertModel record) {
      return 0;
   }
}
