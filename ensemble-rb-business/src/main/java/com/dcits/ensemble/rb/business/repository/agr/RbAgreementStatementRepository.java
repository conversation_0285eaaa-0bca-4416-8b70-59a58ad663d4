package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementStatement;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementStatementRepository extends BusinessRepository {
   public RbAgreementStatement selectByPrimaryKey(String agreementId) {
      RbAgreementStatement rbAgreementStatement = new RbAgreementStatement();
      rbAgreementStatement.setAgreementId(agreementId);
      rbAgreementStatement.setAgreementStatus("A");
      return (RbAgreementStatement)this.daoSupport.selectOne(rbAgreementStatement);
   }

   public RbAgreementStatement getActiveAgreementByClientNoAndId(String clientNo, String agreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("agreementId", agreementId);
      return (RbAgreementStatement)this.daoSupport.selectOne(RbAgreementStatement.class.getName() + ".getActiveAgreementByClientNoAndId", param);
   }

   public void updateByCondition(RbAgreementStatement rbAgreementStatement) {
      rbAgreementStatement.setTranTimestamp(BusiUtil.getTranTimestamp26());
      rbAgreementStatement.setCompany(Context.getInstance().getCompany());
      this.daoSupport.update(RbAgreementStatement.class.getName() + ".updateByCondition", rbAgreementStatement);
   }
}
