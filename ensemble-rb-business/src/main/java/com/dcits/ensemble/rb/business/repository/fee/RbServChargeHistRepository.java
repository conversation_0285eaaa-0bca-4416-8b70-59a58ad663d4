package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.bc.component.fee.sequence.MbServChargeHistSeqNo;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.common.util.FmUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbServChargeHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbServChargeHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbServChargeHistRepository.class);

   public void createMbServChargeHistDb(RbServChargeHist mbServChargeHist) {
      if (BusiUtil.isNull(mbServChargeHist.getEffectDate())) {
         mbServChargeHist.setEffectDate(FmUtil.getFmSystem().getRunDate());
      }

      super.insert(mbServChargeHist);
   }

   public int insertBatch(List<RbServChargeHist> mbServChargeHists) {
      return super.insert(mbServChargeHists);
   }

   public int updateBatch(List<RbServChargeHist> mbServChargeHists) {
      return super.updateAddBatch(mbServChargeHists);
   }

   public List<RbServChargeHist> selectByReference(String reference, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByReference", param);
   }

   public List<RbServChargeHist> selectByReferenceExtract(String reference, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByReferenceExtract", param);
   }

   public List<RbServChargeHist> selectByCondition(Map<String, Object> param) {
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByCondition", param);
   }

   public List<RbServChargeHist> selectByConditionFree(Map<String, Object> param) {
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByConditionFree", param);
   }

   public List<RbServChargeHist> selectByConditionPage(Map<String, Object> param) {
      RowArgs rowArgs = null;

      try {
         rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      } catch (Exception var5) {
         throw BusiUtil.createBusinessException("RB5129");
      }

      String statementPostfix = RbServChargeHist.class.getName() + ".selectByCondition";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbServChargeHist> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.selectByCondition(param);
      }
   }

   public List<RbServChargeHist> selectByConditionPageFree(Map<String, Object> param) {
      RowArgs rowArgs = null;

      try {
         rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      } catch (Exception var5) {
         throw BusiUtil.createBusinessException("RB5129");
      }

      String statementPostfix = RbServChargeHist.class.getName() + ".selectByConditionFree";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbServChargeHist> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.selectByConditionFree(param);
      }
   }

   public RbServChargeHist createNewMbServChargeHist(String scSeqNo, String feeType) {
      RbServChargeHist mbServChargeHist = new RbServChargeHist();
      if (BusiUtil.isNotNull(scSeqNo)) {
         mbServChargeHist.setScSeqNo(scSeqNo);
      } else {
         new MbServChargeHistSeqNo(Context.getInstance().getRunDate());
         scSeqNo = (String)SequenceGenerator.nextValue(SequenceEnum.servChargeHistSeqNo.newInstance(new String[]{Context.getInstance().getRunDate()}), new String[]{Context.getInstance().getRunDate()});
         mbServChargeHist.setScSeqNo(scSeqNo);
      }

      return mbServChargeHist;
   }

   public List<RbServChargeHist> getRbServChargeHistReversalWipe(RbServChargeHist rbServChargeHist, String endDate, List<String> branchList) {
      Map<String, Object> param = new HashMap();
      param.put("chargeToBaseAcctNo", rbServChargeHist.getChargeToBaseAcctNo());
      param.put("feeCcy", rbServChargeHist.getFeeCcy());
      param.put("userId", rbServChargeHist.getUserId());
      param.put("startDate", rbServChargeHist.getTranDate());
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("branchList", branchList);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".getRbServChargEHistReversalWipe", param);
   }

   public List<RbServChargeHist> selectByReferenceSpecial(String reference, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByReferenceSpecial", param);
   }

   public List<RbServChargeHist> selectByClientTypeFeetype(String clientNo, String baseAcctNo, String clientType, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("clientNo", clientNo);
      param.put("clientType", clientType);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByClientTypeFeetype", param);
   }

   public List<RbServChargeHist> selectRbServChargeHistByTranDate(Date tranDate) {
      HashMap<String, Object> param = new HashMap();
      param.put("tranDate", tranDate);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectRbServChargeHistByTranDate", param);
   }

   public List<RbServChargeHist> selectByAgreementId(List<String> agreementIdList) {
      HashMap<String, Object> param = new HashMap();
      param.put("agreementIdList", agreementIdList);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByAgreementId", param);
   }

   public void updateByChange(String amortizeStatus, String agreementId) {
      HashMap<String, Object> param = new HashMap();
      param.put("amortizeStatus", amortizeStatus);
      param.put("agreementId", agreementId);
      this.daoSupport.update(RbServChargeHist.class.getName() + ".updateByChange", param);
   }

   public RbServChargeHist selectRbServChargeHistByScSeqNo(String scSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("scSeqNo", scSeqNo);
      return (RbServChargeHist)this.daoSupport.selectOne(RbServChargeHist.class.getName() + ".selectRbServChargeHistByScSeqNo", param);
   }

   public RbServChargeHist selectRbServChargeHist(String scSeqNo, String reference) {
      Map<String, Object> param = new HashMap();
      param.put("scSeqNo", scSeqNo);
      param.put("reference", reference);
      return (RbServChargeHist)this.daoSupport.selectOne(RbServChargeHist.class.getName() + ".selectRbServChargeHist", param);
   }

   public List<RbServChargeHist> selectRbServChargeHistByChannelSeqNo(String orgChannelSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("orgChannelSeqNo", orgChannelSeqNo);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectRbServChargeHistByChannelSeqNo", param);
   }

   public List<RbServChargeHist> selectRbServChargeHistByChannelSeqNoAndSubSeqNo(String orgChannelSeqNo, String origSubSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("orgChannelSeqNo", orgChannelSeqNo);
      param.put("origSubSeqNo", origSubSeqNo);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectRbServChargeHistByChannelSeqNoAndSubSeqNo", param);
   }

   public RbServChargeHist selectRbServChargeHistByReferenceAndTrantype(String scSeqNo, String reference, String trantype) {
      Map<String, Object> param = new HashMap();
      param.put("scSeqNo", scSeqNo);
      param.put("reference", reference);
      param.put("trantype", trantype);
      return (RbServChargeHist)this.daoSupport.selectOne(RbServChargeHist.class.getName() + ".selectRbServChargeHistByReferenceAndTrantype", param);
   }

   public List<RbServChargeHist> selectRbServChargeHistByReferenceAndTrantype1(String reference, String trantype) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("trantype", trantype);
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectRbServChargeHistByReferenceAndTrantype", param);
   }

   public void updateGlPostedY(List<RbServChargeHist> mbServChargeHists) {
      this.daoSupport.updateAddBatch(RbServChargeHist.class.getName() + ".updateGlPostedY", mbServChargeHists);
   }

   public void deleteByPrimaryKey(RbServChargeHist rbServChargeHist) {
      this.daoSupport.delete(RbServChargeHist.class.getName() + ".deleteByPrimaryKey", rbServChargeHist);
   }

   public List<RbServChargeHist> selectByReference(String reference, String startDate, String endDate) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByReferenceAndDate", param);
   }

   public List<RbServChargeHist> selectByFeeAmtForReversal(String reference, String startDate, String endDate, BigDecimal startAmt, BigDecimal endAmt, String userId, String chargeToBaseAcctNo, String feeCcy) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("chargeToBaseAcctNo", chargeToBaseAcctNo);
      param.put("feeCcy", feeCcy);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("startAmt", startAmt);
      param.put("endAmt", endAmt);
      param.put("userId", userId);
      param.put("sourceType", Context.getInstance().getSourceType());
      param.put("tranBranch", Context.getInstance().getBranchId());
      return this.daoSupport.selectList(RbServChargeHist.class.getName() + ".selectByReferenceAndDate", param);
   }
}
