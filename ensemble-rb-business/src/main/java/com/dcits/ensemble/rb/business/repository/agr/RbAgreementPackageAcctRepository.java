package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementPackageAcct;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.BenefitAcctModel;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementPackageAcctRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementPackageAcctRepository.class);

   public void creatMbAgreementPackageAcct(List<RbAgreementPackageAcct> rbAgreementPackageAccts) {
      super.insertAddBatch(rbAgreementPackageAccts);
   }

   public void deleteMbAgreementPackageAcct(RbAgreementPackageAcct rbAgreementPackageAcct) {
      this.daoSupport.delete(rbAgreementPackageAcct);
   }

   public List<RbAgreementPackageAcct> queryByAgreementId(String agreementId) {
      RbAgreementPackageAcct rbAgreementPackageAcct = new RbAgreementPackageAcct();
      rbAgreementPackageAcct.setAgreementId(agreementId);
      return this.daoSupport.selectList(RbAgreementPackageAcct.class.getName() + ".selectByAgreementId", rbAgreementPackageAcct);
   }

   public RbAgreementPackageAcct queryByAgreementIdAndInternalKey(String agreementId, Long internalKey, String clientNo) {
      RbAgreementPackageAcct rbAgreementPackageAcct = new RbAgreementPackageAcct();
      rbAgreementPackageAcct.setAgreementId(agreementId);
      rbAgreementPackageAcct.setInternalKey(internalKey);
      return (RbAgreementPackageAcct)this.daoSupport.selectOne(rbAgreementPackageAcct);
   }

   public int insertBatch(List<RbAgreementPackageAcct> packageAccts) {
      return this.daoSupport.insertAddBatch(packageAccts);
   }

   public List<RbAgreementPackageAcct> getMbAgreementPackageByBaseAcctNo(String baseAcctNo, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("base_acct_no", baseAcctNo);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementPackageAcct.class.getName() + ".getMbAgreementPackageByBaseAcctNo", param);
   }

   public List<BenefitAcctModel> selectBenefitAcctByPage(Map param) {
      return this.daoSupport.selectList(RbAgreementPackageAcct.class.getName() + ".selectBenefitAcctByPage", param);
   }
}
