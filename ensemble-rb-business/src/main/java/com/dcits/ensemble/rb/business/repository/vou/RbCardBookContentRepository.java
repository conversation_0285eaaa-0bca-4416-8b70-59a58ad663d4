package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbCardBookContent;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbCardBookContentRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbCardBookContentRepository.class);

   public List<RbCardBookContent> selectByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbCardBookContent.class.getName() + ".selectByInternalKey", param);
   }

   public RbCardBookContent deleteByInternalKey(Long internalKey, String clientNo) {
      RbCardBookContent rbPbContent = new RbCardBookContent();
      rbPbContent.setInternalKey(internalKey);
      rbPbContent.setClientNo(clientNo);
      this.daoSupport.delete(rbPbContent);
      return null;
   }

   public RbCardBookContent selectPageNoLineNo(Long internalKey, String clientNo, String regType) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("regType", regType);
      return (RbCardBookContent)this.daoSupport.selectOne(RbCardBookContent.class.getName() + ".selectPageNoLineNo", param);
   }

   public int updateNullLine(Long internalKey, String newPageNo, String newLineNo, String clientNo, String regType) {
      RbCardBookContent rbCardBookContent = new RbCardBookContent();
      rbCardBookContent.setInternalKey(internalKey);
      rbCardBookContent.setPrintPageNo(Integer.valueOf(newPageNo));
      rbCardBookContent.setPrintLineNo(Integer.valueOf(newLineNo));
      rbCardBookContent.setClientNo(clientNo);
      rbCardBookContent.setRegType(regType);
      return this.daoSupport.update(RbCardBookContent.class.getName() + ".updateNullLine", rbCardBookContent);
   }

   public List<RbCardBookContent> selectPageNoLineNo(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbCardBookContent.class.getName() + ".selectPageNoLineNo", param);
   }

   public RbCardBookContent deleteByBookNo(String bookNo) {
      RbCardBookContent rbPbContent = new RbCardBookContent();
      rbPbContent.setBookNo(bookNo);
      rbPbContent.setRegType("T");
      this.daoSupport.delete(rbPbContent);
      return null;
   }

   public List<RbCardBookContent> selectByBookNo(String bookNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("bookNo", bookNo);
      return this.daoSupport.selectList(RbCardBookContent.class.getName() + ".selectByBookNo", param);
   }
}
