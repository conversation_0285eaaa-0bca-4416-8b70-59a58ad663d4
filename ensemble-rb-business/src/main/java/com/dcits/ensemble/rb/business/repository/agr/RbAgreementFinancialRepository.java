package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.model.dict.BaseDict;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementFinancial;
import com.dcits.ensemble.rb.business.model.agr.AgreementTypeEnum;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementFinancialModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbAgreementFinancialRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementFinancialRepository.class);

   public void createMbAgreementFinancialDb(RbAgreementFinancial rbAgreementFinancial) {
      if (BusiUtil.isNotNull(rbAgreementFinancial)) {
         super.insert(rbAgreementFinancial);
      }

   }

   public void updateByPrimaryKey(RbAgreementFinancial rbAgreementFinancial) {
      super.update(rbAgreementFinancial);
   }

   public RbAgreementFinancial getMbAgreementFinancial(String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      return (RbAgreementFinancial)this.daoSupport.selectOne(RbAgreementFinancial.class.getName() + ".getMbAgreementFinancial", param);
   }

   public RbAgreementFinancial getMbAgreementFinancialByAll(String agreementId) {
      RbAgreementFinancial rbAgreementFinancial = new RbAgreementFinancial();
      rbAgreementFinancial.setAgreementId(agreementId);
      return (RbAgreementFinancial)this.daoSupport.selectOne(rbAgreementFinancial);
   }

   /** @deprecated */
   @Deprecated
   public RbAgreementFinancial getMbAgreementFin(String baseAcctNo, String acctSeqNo, String ccy, String prodType, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap(10);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.acctSeqNo.toString(), acctSeqNo);
      param.put(BaseDict.ccy.toString(), ccy);
      param.put(BaseDict.prodType.toString(), prodType);
      param.put(BaseDict.agreementType.toString(), agreementType);
      param.put(BaseDict.clientNo.toString(), clientNo);
      return (RbAgreementFinancial)this.daoSupport.selectOne(RbAgreementFinancial.class.getName() + ".getMbAgreementFin", param);
   }

   public List<RbAgreementFinancial> getMbAgreementFinAllStatus(String agreementId, String baseAcctNo, String acctSeqNo, String ccy, String prodType, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap(10);
      param.put(BaseDict.agreementId.toString(), agreementId);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.acctSeqNo.toString(), acctSeqNo);
      param.put(BaseDict.ccy.toString(), ccy);
      param.put(BaseDict.prodType.toString(), prodType);
      param.put(BaseDict.agreementType.toString(), agreementType);
      param.put(BaseDict.clientNo.toString(), clientNo);
      return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".getMbAgreementFinAllStatus", param);
   }

   public void updateAgreementFinancial(RbAgreementFinancial rbAgreementFinancial) {
      super.update(rbAgreementFinancial);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateAgreementFinancialNew(RbAgreementFinancial rbAgreementFinancial) {
      super.update(rbAgreementFinancial);
   }

   public List<RbAgreementFinancial> getMbFinAcctByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".getMbFinAcctByInternalKey", param);
   }

   public List<MbAgreementFinancialModel> selectFinAgrtForTransfer() {
      return this.daoSupport.selectList("selectActiveFinAgrement", new HashMap());
   }

   public List<RbAgreementFinancial> getZxqyAvaByInternalKey(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return (List)this.daoSupport.selectOne(RbAgreementFinancial.class.getName() + ".getZxqyAvaByInternalKey", param);
   }

   public List<RbAgreementFinancial> getZxqyAvaByInternalKeyList(Long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".getZxqyAvaByInternalKey", param);
   }

   public RbAgreementFinancial getFinAgreementInfo(Long internalKey, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("agreementType", agreementType);
      return (RbAgreementFinancial)this.daoSupport.selectOne(RbAgreementFinancial.class.getName() + ".getZxqyAvaByInternalKey", param);
   }

   public List<RbAgreementFinancial> getRbAgreementFinancialByPage(String baseAcctNo, String prodType, String acctSeqNo, String acctCcy, String agreementType, String agreementStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctSeqNo", acctSeqNo);
      param.put("acctCcy", acctCcy);
      param.put("agreementType", agreementType);
      param.put("agreementStatus", agreementStatus);
      param.put("clientNo", clientNo);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAgreementFinancial.class.getName() + ".getRbAgreementFinancialByPage";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAgreementFinancial> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".getRbAgreementFinancialByPage", param);
      }
   }

   public List<RbAgreementFinancial> getRbAgreementFinancial(String baseAcctNo, String prodType, String acctSeqNo, String acctCcy, String agreementType, String agreementStatus, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctSeqNo", acctSeqNo);
      param.put("acctCcy", acctCcy);
      param.put("agreementType", agreementType);
      param.put("agreementStatus", agreementStatus);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".getRbAgreementFinancialByPage", param);
   }

   public List<RbAgreementFinancial> getRbAgreementFinancialNoPage(String baseAcctNo, String prodType, String acctSeqNo, String acctCcy, String agreementId, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("acctSeqNo", acctSeqNo);
      param.put("acctCcy", acctCcy);
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("agreementStatus", "A");
      return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".getRbAgreementFinancialNoPage", param);
   }

   public List<RbAgreementFinancial> selectFinAgreementInfo(String acctCcy, String baseAcctNo, String acctSeqNo, String prodType, String agreementId, String agreementType, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("acctCcy", acctCcy);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("prodType", prodType);
      param.put("agreementId", agreementId);
      param.put("agreementType", agreementType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".selectFinAgreementInfo", param);
   }

   public List<RbAgreementFinancial> selectFinAgreementInfoByPage(String acctCcy, String baseAcctNo, String acctSeqNo, String prodType, String agreementId, String agreementType) {
      Map<String, Object> param = new HashMap();
      param.put("acctCcy", acctCcy);
      param.put("baseAcctNo", baseAcctNo);
      param.put("acctSeqNo", acctSeqNo);
      param.put("prodType", prodType);
      param.put("agreementId", agreementId);
      param.put("agreementType", agreementType);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbAgreementFinancial.class.getName() + ".selectFinAgreementInfo";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAgreementFinancial> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".selectFinAgreementInfo", param);
      }
   }

   public List<RbAgreementFinancial> getRbAgreementFinancialList(String baseAcctNo, String acctSeqNo, String ccy, String prodType, String clientNo) {
      Map<String, Object> param = new HashMap(10);
      param.put(BaseDict.baseAcctNo.toString(), baseAcctNo);
      param.put(BaseDict.acctSeqNo.toString(), acctSeqNo);
      param.put(BaseDict.ccy.toString(), ccy);
      param.put(BaseDict.prodType.toString(), prodType);
      param.put(BaseDict.clientNo.toString(), clientNo);
      return this.daoSupport.selectList(RbAgreementFinancial.class.getName() + ".getMbAgreementFin", param);
   }

   public RbAgreementFinancial getFinAgreementInfo1(Long internalKey, String agreementType, String clientNo, String finprodType) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("clientNo", clientNo);
      param.put("agreementType", agreementType);
      param.put("finprodType", finprodType);
      return (RbAgreementFinancial)this.daoSupport.selectOne(RbAgreementFinancial.class.getName() + ".getZxqyAvaByInternalKey1", param);
   }

   public boolean isFin(Long internalkey, String clientno) {
      RbAgreementFinancial mbAgreementFin = this.getFinAgreementInfo1(internalkey, AgreementTypeEnum.FIN.toString(), clientno, "12047");
      return BusiUtil.isNotNull(mbAgreementFin);
   }
}
