package com.dcits.ensemble.rb.business.repository.acct;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbBusinessParameter;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbParameterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbParameterRepository.class);

   @Cached(
      area = "longArea",
      name = "rb:param:rb_parameter:para_key:",
      key = "#paraKey",
      cacheType = CacheType.REMOTE
   )
   public RbBusinessParameter getParamterValue(String paraKey, String company) {
      RbBusinessParameter query = new RbBusinessParameter();
      query.setParaKey(paraKey);
      query.setCompany(company);
      return (RbBusinessParameter)this.daoSupport.selectOne(RbBusinessParameter.class.getName() + ".getParamterValue", query);
   }

   public String getRbParameterValue(String paraKey, String company) {
      RbBusinessParameter rbParameter = this.getParamterValue(paraKey, company);
      if (BusiUtil.isNotNull(rbParameter) && BusiUtil.isNotNull(rbParameter.getParaValue())) {
         return rbParameter.getParaValue();
      } else {
         throw BusiUtil.createBusinessException("RB7106", new String[]{paraKey});
      }
   }
}
