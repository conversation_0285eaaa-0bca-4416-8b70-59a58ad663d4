package com.dcits.ensemble.rb.business.repository.acct.schedule;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctScheduleAmendHist;
import com.dcits.ensemble.repository.BusinessRepository;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctScheduleAmendHistRepository extends BusinessRepository {
   public void createMbAcctScheduleAmendHist(RbAcctScheduleAmendHist rbAcctScheduleAmendHist) {
      super.insert(rbAcctScheduleAmendHist);
   }
}
