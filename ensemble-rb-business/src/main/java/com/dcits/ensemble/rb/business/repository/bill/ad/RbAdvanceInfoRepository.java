package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdvanceInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAdvanceInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAdvanceInfoRepository.class);

   public List<RbAdvanceInfo> getInternalAcctInfoByBrachNoRundate(String branchNo, Date date) {
      Map<String, Object> parm = new HashMap();
      parm.put("branchNo", branchNo);
      parm.put("runDate", date);
      return this.daoSupport.selectList(RbAdvanceInfo.class.getName().concat(".selectListByBranchNoRunDate"), parm);
   }
}
