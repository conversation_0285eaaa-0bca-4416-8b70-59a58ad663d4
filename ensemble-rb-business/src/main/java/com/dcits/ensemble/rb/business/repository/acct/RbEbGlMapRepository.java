package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbEbGlMap;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbEbGlMapRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbEbGlMapRepository.class);
   public static final String EB_Y = "Y";

   public List<RbEbGlMap> selectByProdType(String prodType, String eventType, String tranType, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("prodType", prodType);
      param.put("eventType", eventType);
      param.put("tranType", tranType);
      param.put("company", company);
      return this.daoSupport.selectList(RbEbGlMap.class.getName() + ".selectByProdType", param);
   }
}
