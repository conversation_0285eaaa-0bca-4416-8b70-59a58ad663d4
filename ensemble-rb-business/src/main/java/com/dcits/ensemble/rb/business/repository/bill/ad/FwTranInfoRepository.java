package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.flow.antirepeate.entity.FwTranInfoPo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class FwTranInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(FwTranInfoRepository.class);

   public void updateFwTranInfoPostedFlagByReference(List<String> seqNos, List<String> subSeqNos) {
      if (BusiUtil.isNotNull(seqNos) && BusiUtil.isNotNull(subSeqNos)) {
         Map<String, Object> param = new HashMap();
         param.put("seqNos", seqNos);
         param.put("subSeqNos", subSeqNos);
         this.daoSupport.update(FwTranInfoPo.class.getName() + ".updateFwTranInfoPostedFlagByReference", param);
      }

   }

   public List<FwTranInfoPo> selectListByCondition(FwTranInfoPo fwTranInfoPo) {
      Map<String, Object> param = new HashMap();
      param.put("seqNo", fwTranInfoPo.getSeqNo());
      return this.daoSupport.selectList(FwTranInfoPo.class.getName() + ".selectListByCondition", param);
   }

   public FwTranInfoPo selectOneByReference(String reference, String serviceId) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("serviceId", serviceId);
      return (FwTranInfoPo)this.daoSupport.selectOne(FwTranInfoPo.class.getName() + ".selectOneByReference", param);
   }

   public List<FwTranInfoPo> selectListByReference(FwTranInfoPo fwTranInfoPo) {
      return this.daoSupport.selectList(fwTranInfoPo);
   }
}
