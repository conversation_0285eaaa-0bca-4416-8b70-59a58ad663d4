package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctVerifyHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbAcctVerifyHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctVerifyHistRepository.class);

   public List<RbAcctVerifyHist> selectByBaseAcctNo(String baseAcctNo, String acctSeqNo) {
      RbAcctVerifyHist rbAcctVerifyHist = new RbAcctVerifyHist();
      rbAcctVerifyHist.setBaseAcctNo(baseAcctNo);
      rbAcctVerifyHist.setAcctSeqNo(acctSeqNo);
      return this.daoSupport.selectList(rbAcctVerifyHist);
   }

   public void updateBaseAcctByOldBase(String clientNo, String oldBaseAcctNo, String oldAcctSeqNo, String newBaseAcctNo, String newAcctSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("clientNo", clientNo);
      param.put("oldBaseAcctNo", oldBaseAcctNo);
      param.put("oldAcctSeqNo", oldAcctSeqNo);
      param.put("newBaseAcctNo", newBaseAcctNo);
      param.put("newAcctSeqNo", newAcctSeqNo);
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      param.put("lastChangeDate", Context.getInstance().getRunDateParse());
      this.daoSupport.update(RbAcctVerifyHist.class.getName() + ".updateBaseAcctByOldBase", param);
   }

   public List<RbAcctVerifyHist> selectByBaseAcctNoForPage(String baseAcctNo, String acctSeqNo) {
      RbAcctVerifyHist rbAcctVerifyHist = new RbAcctVerifyHist();
      rbAcctVerifyHist.setBaseAcctNo(baseAcctNo);
      rbAcctVerifyHist.setAcctSeqNo(acctSeqNo);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbAcctVerifyHist> queryResult = this.daoSupport.selectQueryResult(rbAcctVerifyHist, rowArgs.getPageIndex(), rowArgs.getLimit());
         if (queryResult != null) {
            Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
            return queryResult.getResultlist();
         } else {
            Context.getInstance().getAppHead().setTotalRows("0");
            return null;
         }
      } else {
         return this.daoSupport.selectList(rbAcctVerifyHist);
      }
   }

   public void insert(RbAcctVerifyHist rbAcctVerifyHist) {
      if (BusiUtil.isNotNull(rbAcctVerifyHist)) {
         super.insert(rbAcctVerifyHist);
      }

   }

   public void updateByCondition(RbAcctVerifyHist rbAcctVerifyHist, String baseAcctNoOld, String verifyStatusOld, String acctSeqNoOld) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", rbAcctVerifyHist.getClientNo());
      param.put("baseAcctNo", rbAcctVerifyHist.getBaseAcctNo());
      param.put("acctSeqNo", rbAcctVerifyHist.getAcctSeqNo());
      param.put("acctVerifyStatus", rbAcctVerifyHist.getAcctVerifyStatus());
      param.put("verificationDate", Context.getInstance().getRunDate());
      param.put("operUserId", Context.getInstance().getUserId());
      param.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      param.put("company", Context.getInstance().getCompany());
      param.put("baseAcctNoOld", baseAcctNoOld);
      param.put("verifyStatusOld", verifyStatusOld);
      param.put("acctSeqNoOld", acctSeqNoOld);
      this.daoSupport.update(RbAcctVerifyHist.class.getName() + ".updateByCondition", param);
   }
}
