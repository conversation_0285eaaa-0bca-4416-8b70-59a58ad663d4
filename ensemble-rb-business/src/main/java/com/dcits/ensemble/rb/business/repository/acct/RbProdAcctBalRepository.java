package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbProdAcctBal;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbProdAcctBalRepository extends BusinessRepository<RbProdAcctBal> {
   public void deleteAll() {
      Map map = new HashMap();
      map.put("company", Context.getInstance().getCompany());
      this.daoSupport.delete(RbProdAcctBal.class.getName() + ".deleteAll", map);
   }

   public List<RbProdAcctBal> sumProdAcctBal(Date lastRunDate, String company) {
      Map<String, Object> param = new HashMap();
      param.put("lastRunDate", lastRunDate);
      param.put("company", company);
      return this.daoSupport.selectList(RbProdAcctBal.class.getName() + ".sumProdAcctBal", param, new RbProdAcctBal());
   }

   public List<RbProdAcctBal> sumAcctBalance(Map<String, Object> map) {
      map.put("company", Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbProdAcctBal.class.getName() + ".sumAcctBalance", map);
   }

   public List<RbProdAcctBal> sumAcctBalanceNew(Map<String, Object> map) {
      return this.daoSupport.selectList(RbProdAcctBal.class.getName() + ".sumAcctBalanceNew", map);
   }

   public RbProdAcctBal sumOneAcctBalance(Map<String, Object> map) {
      return (RbProdAcctBal)this.daoSupport.selectOne(RbProdAcctBal.class.getName() + ".sumOneAcctBalance", map);
   }

   public List<RbProdAcctBal> sumAcctInt(Map<String, Object> map) {
      map.put("company", Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbProdAcctBal.class.getName() + ".sumAcctInt", map);
   }

   public List<RbProdAcctBal> sumAcctIntNew(Map<String, Object> map) {
      return this.daoSupport.selectList(RbProdAcctBal.class.getName() + ".sumAcctIntNew", map);
   }

   public RbProdAcctBal sumOneAcctInt(Map<String, Object> map) {
      return (RbProdAcctBal)this.daoSupport.selectOne(RbProdAcctBal.class.getName() + ".sumOneAcctInt", map);
   }

   public List<RbProdAcctBal> sumAcctBalanceFin(Map<String, Object> map) {
      map.put("company", Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbProdAcctBal.class.getName() + ".sumAcctBalanceFin", map);
   }

   public List<RbProdAcctBal> sumAcctIntFin(Map<String, Object> map) {
      map.put("company", Context.getInstance().getCompany());
      return this.daoSupport.selectList(RbProdAcctBal.class.getName() + ".sumAcctIntFin", map);
   }

   public RbProdAcctBal selectOneProdAcctBal(String branch, String ccy, String prodType, String accountingStatus, String amtType) {
      RbProdAcctBal queryParam = new RbProdAcctBal();
      queryParam.setBranch(branch);
      queryParam.setCcy(ccy);
      queryParam.setProdType(prodType);
      queryParam.setAccountingStatus(accountingStatus);
      queryParam.setAmtType(amtType);
      return (RbProdAcctBal)super.selectOne(queryParam);
   }

   public void updateProdAcctBals(List<RbProdAcctBal> rbProdAcctBals) {
      rbProdAcctBals.forEach((a) -> {
         this.daoSupport.update(RbProdAcctBal.class.getName().concat(".updateDealFlag2"), a);
      });
   }

   public List<RbProdAcctBal> queryNoDealProdAcctBals(Date lastRunDate) {
      RbProdAcctBal prodAcctBal = new RbProdAcctBal();
      prodAcctBal.setLastChangeDate(lastRunDate);
      prodAcctBal.setDealFlag("1");
      return this.daoSupport.selectList(RbProdAcctBal.class.getName().concat(".selectSumProdAcctBalNoDeal"), prodAcctBal);
   }

   public void updateProdAcctBalDealFlag(Date lastRunDate) {
      Map<String, Object> map = new HashMap(1);
      map.put("lastRunDate", lastRunDate);
      this.daoSupport.update(RbProdAcctBal.class.getName().concat(".updateProdAcctBalDealFlag"), map);
   }

   public RbProdAcctBal selectOneProdAcctBalSum(String branch, String ccy, String prodType, String accountingStatus, String amtType, Date lastRunDate) {
      RbProdAcctBal queryParam = new RbProdAcctBal();
      queryParam.setBranch(branch);
      queryParam.setCcy(ccy);
      queryParam.setProdType(prodType);
      queryParam.setAccountingStatus(accountingStatus);
      queryParam.setAmtType(amtType);
      queryParam.setLastChangeDate(lastRunDate);
      return (RbProdAcctBal)this.daoSupport.selectOne(RbProdAcctBal.class.getName().concat(".selectOneProdAcctBalSum"), queryParam);
   }

   public Date selectLastLastRunDate(Date yesterday) {
      RbProdAcctBal queryParam = new RbProdAcctBal();
      queryParam.setLastChangeDate(yesterday);
      return (Date)this.daoSupport.selectObject(RbProdAcctBal.class.getName().concat(".selectLastLastRunDate"), queryParam);
   }
}
