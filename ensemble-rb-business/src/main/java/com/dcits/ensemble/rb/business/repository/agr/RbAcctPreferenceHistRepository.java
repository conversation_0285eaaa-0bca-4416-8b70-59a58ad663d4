package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctPreferenceHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctPreferenceHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctPreferenceHistRepository.class);

   public List<RbAcctPreferenceHist> getAcctPreferenceHistByInternalKey(Long internalKey) {
      RbAcctPreferenceHist rbAcctPreferenceHist = new RbAcctPreferenceHist();
      rbAcctPreferenceHist.setInternalKey(internalKey);
      return this.daoSupport.selectList(RbAcctPreferenceHist.class.getName() + ".getAcctPreferenceHistByInternalKey", rbAcctPreferenceHist);
   }
}
