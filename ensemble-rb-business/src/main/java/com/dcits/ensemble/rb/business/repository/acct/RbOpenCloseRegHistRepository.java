package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOpenCloseRegHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbOpenCloseRegHistRepository extends BusinessRepository<RbOpenCloseRegHist> {
   private static final Logger log = LoggerFactory.getLogger(RbOpenCloseRegHistRepository.class);

   public int insert(RbOpenCloseRegHist rbOpenCloseRegHist) {
      rbOpenCloseRegHist.setHistSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.rbOpenCloseRegHistSeqNo));
      rbOpenCloseRegHist.setDealFlag("N");
      return super.insert(rbOpenCloseRegHist);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public int updateForNewTransaction(RbOpenCloseRegHist rbOpenCloseRegHist) {
      return super.update(rbOpenCloseRegHist);
   }

   public BigDecimal getNotDealCount() {
      Map<String, Object> param = new HashMap();
      param.put("dealFlag", "N");
      return (BigDecimal)this.daoSupport.selectObject(RbOpenCloseRegHist.class.getName() + ".getNotDealCount", param);
   }
}
