package com.dcits.ensemble.rb.business.repository.fee;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.bc.component.fee.sequence.AmortizeAgrIdSeq;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFeeAmortizeAgr;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFeeAmortizeAgrRepository extends BusinessRepository<RbFeeAmortizeAgr> {
   private static final Logger log = LoggerFactory.getLogger(RbFeeAmortizeAgrRepository.class);
   @Resource
   private FmBaseStor fmBaseStor;

   public RbFeeAmortizeAgr getMbFeeAmortizeAgr(String agreementId) {
      RbFeeAmortizeAgr rbFeeAmortizeAgr = new RbFeeAmortizeAgr();
      rbFeeAmortizeAgr.setAgreementId(agreementId);
      return (RbFeeAmortizeAgr)this.daoSupport.selectOne(rbFeeAmortizeAgr);
   }

   public RbFeeAmortizeAgr selectByPrimaryKey(String agreementId) {
      RbFeeAmortizeAgr rbFeeAmortizeAgr = new RbFeeAmortizeAgr();
      rbFeeAmortizeAgr.setAgreementId(agreementId);
      return (RbFeeAmortizeAgr)this.daoSupport.selectOne(rbFeeAmortizeAgr);
   }

   public List<RbFeeAmortizeAgr> selectByCondition(Map<String, Object> para) {
      return this.daoSupport.selectList(RbFeeAmortizeAgr.class.getName() + ".selectByCondition", para);
   }

   public List<RbFeeAmortizeAgr> selectMbFeeAmortizeAgr(String runDate, String lastRunDate) {
      Map<String, Object> param = new HashMap();
      param.put("runDate", runDate);
      param.put("lastRunDate", lastRunDate);
      return this.daoSupport.selectList(RbFeeAmortizeAgr.class.getName() + ".selectMbFeeAmortizeAgr", param);
   }

   public void insertBatchAgr(List<RbFeeAmortizeAgr> records) {
      super.insertAddBatch(records);
   }

   public List<RbFeeAmortizeAgr> selectFeeAmortizeInfo(Map<String, Object> para) {
      return this.daoSupport.selectList(RbFeeAmortizeAgr.class.getName() + ".selectFeeAmortizeInfo", para);
   }

   public void updBatch(List<RbFeeAmortizeAgr> mbFeeAmortizeAgrs) {
      if (BusiUtil.isNotNull(mbFeeAmortizeAgrs)) {
         Iterator var2 = mbFeeAmortizeAgrs.iterator();

         while(var2.hasNext()) {
            RbFeeAmortizeAgr mbFeeAmortizeAgr = (RbFeeAmortizeAgr)var2.next();
            super.update(mbFeeAmortizeAgr);
         }
      }

   }

   public RbFeeAmortizeAgr createNewMbFeeAmortizeAgr(String runDate) {
      RbFeeAmortizeAgr mbFeeAmortizeAgr = new RbFeeAmortizeAgr();
      AmortizeAgrIdSeq amortizeAgrIdSeq = new AmortizeAgrIdSeq();
      String amortizeId = BusiUtil.isNotNull(runDate) ? amortizeAgrIdSeq.getNextVal(runDate) : amortizeAgrIdSeq.getNextVal(Context.getInstance().getRunDate());
      mbFeeAmortizeAgr.setAgreementId(amortizeId);
      return mbFeeAmortizeAgr;
   }

   public RbFeeAmortizeAgr getMbFeeAmortizeAgrByReference(String reference) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      return (RbFeeAmortizeAgr)this.daoSupport.selectOne(RbFeeAmortizeAgr.class.getName() + ".selectOne", param);
   }

   public RbFeeAmortizeAgr getMbFeeAmortizeAgrByReferenceAndClientNo(String reference, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return (RbFeeAmortizeAgr)this.daoSupport.selectOne(RbFeeAmortizeAgr.class.getName() + ".getMbFeeAmortizeAgrByReferenceAndClientNo", param);
   }

   public List<RbFeeAmortizeAgr> selectAmoByPayModel(Long internalKey, Date startDate, Date endDate, String branch, String clientNo, String preAccrNo, String userId) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      param.put("branch", branch);
      param.put("clientNo", clientNo);
      param.put("preAccrNo", preAccrNo);
      param.put("userId", userId);
      return this.daoSupport.selectList(RbFeeAmortizeAgr.class.getName() + ".selectAmoByPayModel", param);
   }

   public RbFeeAmortizeAgr getMbFeeAmortizeAgrByAgreementId(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return (RbFeeAmortizeAgr)this.daoSupport.selectOne(RbFeeAmortizeAgr.class.getName() + ".getMbFeeAmortizeAgrByAgreementId", param);
   }

   public List<RbFeeAmortizeAgr> selectRbFeeAmortizeAgrByReference(List<String> references) {
      HashMap<String, Object> param = new HashMap();
      param.put("list", references);
      return this.daoSupport.selectList(RbFeeAmortizeAgr.class.getName() + ".selectRbFeeAmortizeAgrByReference", param);
   }

   public RbFeeAmortizeAgr getMbFeeAmortizeAgrByReferenceAndCompany(String reference) {
      Map<String, Object> param = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("reference", reference);
      return (RbFeeAmortizeAgr)this.daoSupport.selectOne(RbFeeAmortizeAgr.class.getName() + ".getMbFeeAmortizeAgrByReferenceAndCompany", param);
   }

   public Integer getRbFeeAmortizeAgrCount(Long internalKey, Date runDate) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("runDate", runDate);
      return this.daoSupport.count(RbFeeAmortizeAgr.class.getName() + ".getRbFeeAmortizeAgrCount", param);
   }
}
