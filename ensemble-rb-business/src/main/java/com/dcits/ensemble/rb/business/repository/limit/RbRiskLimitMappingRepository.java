package com.dcits.ensemble.rb.business.repository.limit;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbRiskLimitMapping;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbRiskLimitMappingRepository extends BusinessRepository {
   public List<RbRiskLimitMapping> getByRiskLimit(String acctClass, String acctRiskLevel, String type) {
      RbRiskLimitMapping rbRiskLimitMapping = new RbRiskLimitMapping();
      rbRiskLimitMapping.setAcctRiskLevel(acctRiskLevel);
      rbRiskLimitMapping.setAcctClass(acctClass);
      rbRiskLimitMapping.setLimitType(type);
      return this.daoSupport.selectList(rbRiskLimitMapping);
   }

   public RbRiskLimitMapping getByRiskLimitOne(String acctClass, String acctRiskLevel, String riskLimitRef, String type) {
      RbRiskLimitMapping rbRiskLimitMapping = new RbRiskLimitMapping();
      rbRiskLimitMapping.setAcctRiskLevel(acctRiskLevel);
      rbRiskLimitMapping.setAcctClass(acctClass);
      rbRiskLimitMapping.setRiskLimitRef(riskLimitRef);
      rbRiskLimitMapping.setLimitType(type);
      return (RbRiskLimitMapping)this.daoSupport.selectOne(rbRiskLimitMapping);
   }

   public List<RbRiskLimitMapping> getRiskLimits(RbRiskLimitMapping rbRiskLimitMapping) {
      return this.daoSupport.selectList(rbRiskLimitMapping);
   }
}
