package com.dcits.ensemble.rb.business.repository.res.control;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbControlTranRelation;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbControlTranRelationRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbControlTranRelationRepository.class);

   public List<RbControlTranRelation> selectListByClass(String controlClass) {
      Map<String, Object> param = new HashMap();
      param.put("controlClass", controlClass);
      return this.daoSupport.selectList(RbControlTranRelation.class.getName() + ".selectControlTranTypeEffecting", param);
   }
}
