package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFinEbMap;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFinEbMapRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbFinEbMapRepository.class);
   public static final String EB_Y = "Y";

   public List<RbFinEbMap> selectByOptionDistinctInfoN(String optionFlag, String entityFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("optionFlag", optionFlag);
      param.put("entityFlag", entityFlag);
      param.put("company", company);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByOptionDistinctInfoN", param);
   }

   public List<RbFinEbMap> selectByOptionDistinctInfoY(String optionFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("company", company);
      param.put("optionFlag", optionFlag);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByOptionDistinctInfoY", param);
   }

   public List<RbFinEbMap> selectByOptionInfoN(String optionFlag, String entityFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("company", company);
      param.put("optionFlag", optionFlag);
      param.put("entityFlag", entityFlag);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByOptionInfoN", param);
   }

   public List<RbFinEbMap> selectByOptionInfoY(String optionFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("company", company);
      param.put("optionFlag", optionFlag);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByOptionInfoY", param);
   }

   public List<RbFinEbMap> selectByAllInfoN(String optionFlag, String billCode, String billMediumType, String entityFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("company", company);
      param.put("optionFlag", optionFlag);
      param.put("billCode", billCode);
      param.put("billMediumType", billMediumType);
      param.put("entityFlag", entityFlag);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByAllInfoN", param);
   }

   public List<RbFinEbMap> selectByAllInfoY(String optionFlag, String billCode, String billMediumType, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("optionFlag", optionFlag);
      param.put("billCode", billCode);
      param.put("billMediumType", billMediumType);
      param.put("company", company);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByAllInfoY", param);
   }

   public List<RbFinEbMap> selectByOptionFrontInfoN(String optionFlag, String prodType, String frontProdType, String entityFlag, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("optionFlag", optionFlag);
      param.put("prodType", prodType);
      param.put("frontProdType", frontProdType);
      param.put("entityFlag", entityFlag);
      param.put("company", company);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByOptionFrontInfoN", param);
   }

   public List<RbFinEbMap> selectByOptionFrontInfoY(String optionFlag, String prodType, String frontProdType, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("optionFlag", optionFlag);
      param.put("prodType", prodType);
      param.put("frontProdType", frontProdType);
      param.put("company", company);
      return this.daoSupport.selectList(RbFinEbMap.class.getName() + ".selectByOptionFrontInfoY", param);
   }

   public RbFinEbMap selectEntityFlagByOptionFrontInfoY(String optionFlag, String prodType, String frontProdType, String tranType, String billCode, String billMediumType, String company) {
      Map<String, Object> param = new HashMap(16);
      param.put("optionFlag", optionFlag);
      param.put("prodType", prodType);
      param.put("frontProdType", frontProdType);
      param.put("tranType", tranType);
      param.put("billCode", billCode);
      param.put("billMediumType", billMediumType);
      param.put("company", company);
      return (RbFinEbMap)this.daoSupport.selectOne(RbFinEbMap.class.getName() + ".selectEntityFlagByOptionFrontInfoY", param);
   }
}
