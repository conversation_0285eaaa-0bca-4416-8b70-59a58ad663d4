package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbOsdServCharge;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@BusiUnit
public class RbOsdServChargeRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbOsdServChargeRepository.class);

   public List<RbOsdServCharge> getMbOsdServChargeByChargeInternalKey(Long internalKey, String feeType, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("feeType", feeType);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByChargeInternalKey", param);
   }

   public List<RbOsdServCharge> getMbOsdServChargeByChargeInternalKeyByPage(Long internalKey, String feeType, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("feeType", feeType);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByChargeInternalKeyByPage", param);
   }

   public List<RbOsdServCharge> getRbOsdServChargeByStatus(Long internalKey, String feeType, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("feeType", feeType);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByChargeInternalStatus", param);
   }

   public List<RbOsdServCharge> getRbOsdServCharge(Long internalKey, String[] feeTypes, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("feeTypes", feeTypes);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByChargeStatus", param);
   }

   public List<RbOsdServCharge> getMbOsdServChargeByInternalKey(Long internalKey, String feeType, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("feeType", feeType);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByInternalKey", param);
   }

   public List<RbOsdServCharge> getRbOsdServChargeByBaseAcctNo(String clientNo, String baseAcctNo) {
      RbOsdServCharge rbOsdServCharge = new RbOsdServCharge();
      rbOsdServCharge.setBaseAcctNo(baseAcctNo);
      rbOsdServCharge.setClientNo(clientNo);
      rbOsdServCharge.setOsdStatus("C");
      return this.daoSupport.selectList(rbOsdServCharge);
   }

   public List<RbOsdServCharge> getMbOsdServChargeByBaseAcctNo(String baseAcctNo, String feeType, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("feeType", feeType);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByBaseAcctNo", param);
   }

   public List<RbOsdServCharge> getMbOsdServChargeByClientNo(String clientNo, String feeType, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("feeType", feeType);
      param.put("startDate", BusiUtil.string2Date(startDate));
      param.put("endDate", BusiUtil.string2Date(endDate));
      List<RbOsdServCharge> mbOsdServCharges = this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByClientNo", param);
      if (null == mbOsdServCharges) {
         throw BusiUtil.createBusinessException("MB4018");
      } else {
         return mbOsdServCharges;
      }
   }

   public List<RbOsdServCharge> getMbOsdServChargeByClientNoByPage(String clientNo, String feeType, String startDate, String endDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("clientNo", clientNo);
      param.put("feeType", feeType);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      List<RbOsdServCharge> mbOsdServCharges = this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByClientNoByPage", param);
      if (null == mbOsdServCharges) {
         throw BusiUtil.createBusinessException("MB4018");
      } else {
         return mbOsdServCharges;
      }
   }

   public List<RbOsdServCharge> selectArrearByAgreementId(String agreementId, String feeType, String osdSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      param.put("feeType", feeType);
      param.put("osdSeqNo", osdSeqNo);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectArrearByAgreementId", param);
   }

   public List<RbOsdServCharge> selectByInternalKeyAndFeeType(long internalKey, String feeType, Date nextChargeDate, String tranBranch, String feeCcy) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("feeType", feeType);
      param.put("nextChargeDate", nextChargeDate);
      param.put("tranBranch", tranBranch);
      param.put("feeCcy", feeCcy);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByInternalKeyAndFeeType", param);
   }

   public List<RbOsdServCharge> selectByReference(String reference, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectByReference", param);
   }

   public List<RbOsdServCharge> selectNextChargeDateMature(String runDate, String lastRunDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("runDate", runDate);
      param.put("lastRunDate", lastRunDate);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectNextChargeDateMature", param);
   }

   public int insertBatch(List<RbOsdServCharge> mbOsdServCharges) {
      return super.insertAddBatch(mbOsdServCharges);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public int insertRbOsdServCharge(RbOsdServCharge mbOsdServCharge) {
      return super.insert(mbOsdServCharge);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public int udpateRbOsdServCharge(RbOsdServCharge mbOsdServCharge) {
      return super.update(mbOsdServCharge);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public int deleteRbOsdServCharge(RbOsdServCharge mbOsdServCharge) {
      return super.delete(mbOsdServCharge);
   }

   public RbOsdServCharge createNewMbOsdServCharge(String osdSeqNo) {
      RbOsdServCharge mbOsdServCharge = new RbOsdServCharge();
      if (BusiUtil.isNotNull(osdSeqNo)) {
         mbOsdServCharge.setOsdSeqNo(osdSeqNo);
      } else {
         mbOsdServCharge.setOsdSeqNo((String)SequenceGenerator.nextValue(SequenceEnum.osdChargeSeqNo));
      }

      return mbOsdServCharge;
   }

   public int updateBatch(List<RbOsdServCharge> mbOsdServCharges) {
      return super.updateAddBatch(mbOsdServCharges);
   }

   public int updateByClientNoAndKey(RbOsdServCharge mbOsdServCharge) {
      return this.daoSupport.update(RbOsdServCharge.class.getName() + ".updateByPrimaryKeyAndClientNo", mbOsdServCharge);
   }

   public RbOsdServCharge getOsdServChargeByClientNoAndKey(RbOsdServCharge mbOsdServCharge) {
      Map<String, Object> param = new HashMap(16);
      param.put("osdSeqNo", mbOsdServCharge.getOsdSeqNo());
      param.put("clientNo", mbOsdServCharge.getClientNo());
      return (RbOsdServCharge)this.daoSupport.selectOne(RbOsdServCharge.class.getName() + ".selectByClientNoAndKey", param);
   }

   public List<RbOsdServCharge> selectListByAgreementId(String agreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      return this.daoSupport.selectList(RbOsdServCharge.class.getName() + ".selectArrearByAgreementId", param);
   }

   public int deleteOsdServChargeByAgrt(RbOsdServCharge mbOsdServCharge) {
      return this.daoSupport.delete(RbOsdServCharge.class.getName() + ".deleteOsdServChargeByAgrt", mbOsdServCharge);
   }

   public int updateOsdServChargeByAgrt(RbOsdServCharge mbOsdServCharge) {
      return this.daoSupport.update(RbOsdServCharge.class.getName() + ".updateOsdServChargeByAgrt", mbOsdServCharge);
   }

   public void updateOsdProdByKey(String newProdType, long internalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("newProdType", newProdType);
      param.put("clientNo", clientNo);
      this.daoSupport.update(RbOsdServCharge.class.getName() + ".updateProdByKey", param);
   }

   public RbOsdServCharge getOsdServChargeByInternalKey(Long internalKey, String feeType, Date tranDate, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("feeType", feeType);
      param.put("clientNo", clientNo);
      return (RbOsdServCharge)this.daoSupport.selectOne(RbOsdServCharge.class.getName() + ".getOsdServChargeByInternalKey", param);
   }

   public RbOsdServCharge getOsdServChargeByOsdSeqNo(String osdSeqNo, String clientNo) {
      RbOsdServCharge mbOsdServCharge = new RbOsdServCharge();
      mbOsdServCharge.setOsdSeqNo(osdSeqNo);
      mbOsdServCharge.setClientNo(clientNo);
      return (RbOsdServCharge)this.daoSupport.selectOne(mbOsdServCharge);
   }

   public List<RbOsdServCharge> getOsdServChargeList(RbOsdServCharge mbOsdServCharge) {
      return this.daoSupport.selectList(mbOsdServCharge);
   }
}
