package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBailRegister;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAcctBailRegisterRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctBailRegisterRepository.class);

   public void insertRbAcctBailRegister(RbAcctBailRegister rbAcctBailRegister) {
      this.daoSupport.insert(rbAcctBailRegister);
   }

   public RbAcctBailRegister getRbAcctBailRegister(RbAcctBailRegister rbAcctBailRegister) {
      return (RbAcctBailRegister)this.daoSupport.selectOne(rbAcctBailRegister);
   }

   public RbAcctBailRegister getRbAcctBailRegisters(String baseAcctNo, String acctSeqNo, String clientNo) {
      RbAcctBailRegister rbAcctBailRegister = new RbAcctBailRegister();
      rbAcctBailRegister.setDcBaseAcctNo(baseAcctNo);
      rbAcctBailRegister.setDcAcctSeqNo(acctSeqNo);
      rbAcctBailRegister.setClientNo(clientNo);
      return (RbAcctBailRegister)this.daoSupport.selectOne(rbAcctBailRegister);
   }

   public List<RbAcctBailRegister> getRbAcctBailRegistersByDealStatus(String dealStatus, String fromChannel) {
      RbAcctBailRegister rbAcctBailRegister = new RbAcctBailRegister();
      rbAcctBailRegister.setDealStatus(dealStatus);
      rbAcctBailRegister.setFromChannel(fromChannel);
      return this.daoSupport.selectList(rbAcctBailRegister);
   }

   public RbAcctBailRegister selectRbAcctBailRegister(String baseAcctNo, String cardNo, String dcAcctSeqNo, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("cardNo", cardNo);
      param.put("dcAcctSeqNo", dcAcctSeqNo);
      param.put("clientNo", clientNo);
      return (RbAcctBailRegister)this.daoSupport.selectOne(RbAcctBailRegister.class.getName() + ".selectRbAcctBailRegister", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updRbAcctBailRegister(RbAcctBailRegister rbAcctBailRegister) {
      this.daoSupport.update(rbAcctBailRegister);
   }
}
