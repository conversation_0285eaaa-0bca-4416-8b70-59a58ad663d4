package com.dcits.ensemble.rb.business.repository.res.control;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbDepDraControl;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbDepDraControlRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbDepDraControlRepository.class);

   @Cached(
      area = "longArea",
      name = "rb:param:dep_dra_control:all:",
      key = "'all_dep_dra_control'",
      cacheType = CacheType.REMOTE
   )
   public List<RbDepDraControl> getAllDepDraControl() {
      return this.daoSupport.selectAll(new RbDepDraControl());
   }
}
