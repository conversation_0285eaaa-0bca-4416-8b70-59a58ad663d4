package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.ensemble.rb.business.model.entity.dbmodel.ClGlHist;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class ClGlHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(ClGlHistRepository.class);

   public List<ClGlHist> getClGLHistByReference(List<String> seqNos, List<String> subSeqNos) {
      Map<String, Object> param = new HashMap();
      param.put("seqNos", seqNos);
      param.put("subSeqNos", subSeqNos);
      return this.daoSupport.selectList(ClGlHist.class.getName() + ".getClGLHistByReference", param);
   }

   public void updateClGlHistBySeqNo(List<String> reference) {
      HashMap<String, Object> param = new HashMap();
      param.put("list", reference);
      this.daoSupport.update(ClGlHist.class.getName() + ".updateClGlHistBySeqNo", param);
   }
}
