package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFundDirectionDetails;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbFundDirectionDetailsRepository extends BusinessRepository {
   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void createRbFundDirectionDetails(RbFundDirectionDetails rbFundDirectionDetails) {
      if (BusiUtil.isNotNull(rbFundDirectionDetails)) {
         super.insert(rbFundDirectionDetails);
      }

   }

   public RbFundDirectionDetails getRbFundDirectionDetails(RbFundDirectionDetails rbFundDirectionDetails) {
      return (RbFundDirectionDetails)this.daoSupport.selectOne(rbFundDirectionDetails);
   }

   public void updateRbFundDirectionDetails(RbFundDirectionDetails rbFundDirectionDetails) {
      this.daoSupport.update(rbFundDirectionDetails);
   }
}
