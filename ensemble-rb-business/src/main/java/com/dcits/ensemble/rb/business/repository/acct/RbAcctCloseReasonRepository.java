package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctCloseReason;
import com.dcits.ensemble.repository.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctCloseReasonRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctCloseReasonRepository.class);

   public RbAcctCloseReason getCloseReason(String reasonCode) {
      RbAcctCloseReason closeReason = new RbAcctCloseReason();
      closeReason.setReasonCode(reasonCode);
      return (RbAcctCloseReason)this.daoSupport.selectOne(closeReason);
   }
}
