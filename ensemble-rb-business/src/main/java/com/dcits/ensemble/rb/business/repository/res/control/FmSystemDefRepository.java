package com.dcits.ensemble.rb.business.repository.res.control;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.fm.model.FmSystemDef;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class FmSystemDefRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(FmSystemDefRepository.class);

   public List<FmSystemDef> selectSystemListByIds(List<String> ids) {
      if (BusiUtil.isNotNull(ids)) {
         Map map = new HashMap();
         map.put("systemIds", ids);
         return this.daoSupport.selectList(FmSystemDef.class.getName() + ".selectSystemListByIds", map);
      } else {
         return new ArrayList();
      }
   }
}
