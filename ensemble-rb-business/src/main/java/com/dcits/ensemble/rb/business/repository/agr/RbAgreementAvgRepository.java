package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.bc.unit.agr.AgreementStatusEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementAvg;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementAvgRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAgreementAvgRepository.class);

   public RbAgreementAvg getActiveAgreement(String baseAcctNo, String acctSeqNo, String prodType, String ccy, String clientNo) {
      RbAgreementAvg query = new RbAgreementAvg();
      query.setBaseAcctNo(baseAcctNo);
      query.setAcctSeqNo(acctSeqNo);
      query.setProdType(prodType);
      query.setAcctCcy(ccy);
      query.setClientNo(clientNo);
      query.setAgreementStatus(AgreementStatusEnum.ACTIVE.getCode());
      return (RbAgreementAvg)this.daoSupport.selectOne(query);
   }

   public RbAgreementAvg getActiveAgreementByAgreementId(String agreementId, String clientNo) {
      RbAgreementAvg query = new RbAgreementAvg();
      query.setAgreementId(agreementId);
      query.setClientNo(clientNo);
      query.setAgreementStatus(AgreementStatusEnum.ACTIVE.getCode());
      return (RbAgreementAvg)this.daoSupport.selectOne(query);
   }

   public RbAgreementAvg getAgreementEffecting(String agreementId, String clientNo, Date runDate) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      param.put("clientNo", clientNo);
      param.put("runDate", runDate);
      return (RbAgreementAvg)this.daoSupport.selectOne(RbAgreementAvg.class.getName() + ".getAgreementEffecting", param);
   }

   public List<RbAgreementAvg> getAgreementList(String baseAcctNo, String acctSeqNo, String prodType, String ccy, String clientNo, String agreementStatus) {
      RbAgreementAvg query = new RbAgreementAvg();
      query.setBaseAcctNo(baseAcctNo);
      query.setAcctSeqNo(acctSeqNo);
      query.setProdType(prodType);
      query.setAcctCcy(ccy);
      query.setClientNo(clientNo);
      query.setAgreementStatus(agreementStatus);
      return this.daoSupport.selectList(query);
   }

   public List<RbAgreementAvg> getAgreementList(String agreementId, String clientNo, String agreementStatus) {
      RbAgreementAvg query = new RbAgreementAvg();
      query.setAgreementId(agreementId);
      query.setClientNo(clientNo);
      query.setAgreementStatus(agreementStatus);
      return this.daoSupport.selectList(query);
   }
}
