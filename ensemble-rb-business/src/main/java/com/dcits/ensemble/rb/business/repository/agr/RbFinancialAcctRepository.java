package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbFinancialAcct;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbFinancialAcctRepository extends BusinessRepository {
   public void creatMbFinancialDb(RbFinancialAcct mbFinancialAcct) {
      super.insert(mbFinancialAcct);
   }

   public void updateMbFinancialDb(RbFinancialAcct mbFinancialAcct) {
      super.update(mbFinancialAcct);
   }

   public void deleteMbFinancialDb(RbFinancialAcct mbFinancialAcct) {
      this.daoSupport.delete(mbFinancialAcct);
   }

   public RbFinancialAcct getMbFinAcctByInternalKey(Long finInternalKey, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("finInternalKey", finInternalKey);
      param.put("clientNo", clientNo);
      return (RbFinancialAcct)this.daoSupport.selectOne(RbFinancialAcct.class.getName() + ".getMbFinAcctByInternalKey", param);
   }

   public RbFinancialAcct getMbFinAcctByBaseNoFinType(Long internalKey, String finProdType, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("finProdType", finProdType);
      param.put("clientNo", finProdType);
      return (RbFinancialAcct)this.daoSupport.selectOne(RbFinancialAcct.class.getName() + ".getMbFinAcctByBaseNoFinType", param);
   }

   public List<RbFinancialAcct> getMbFinAcctsByInternalKey(Long finInternalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("finInternalKey", finInternalKey);
      return this.daoSupport.selectList(RbFinancialAcct.class.getName() + ".getMbFinAcctByInternalKey", param);
   }

   public List<RbFinancialAcct> getMbFinAcctByFinInternalKey(Long finInternalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("finInternalKey", finInternalKey);
      return this.daoSupport.selectList(RbFinancialAcct.class.getName() + ".getMbFinAcctByFinInternalKey", param);
   }

   public List<RbFinancialAcct> getMbFinAcctsBySignDate(Long internalKey, String clinetNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clinetNo);
      return this.daoSupport.selectList(RbFinancialAcct.class.getName() + ".getMbFinAcctsBySignDate", param);
   }

   public RbFinancialAcct getMbFinByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return (RbFinancialAcct)this.daoSupport.selectOne(RbFinancialAcct.class.getName() + ".getMbFinByInternalKey", param);
   }

   public List<RbFinancialAcct> getMbFinAccts(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbFinancialAcct.class.getName() + ".getMbFinAccts", param);
   }

   public List<RbFinancialAcct> getMbFinAcctsForEod() {
      String lastRunDate = Context.getInstance().getLastRunDate();
      Map<String, Object> param = new HashMap(16);
      param.put("lastRunDate", lastRunDate);
      return this.daoSupport.selectList(RbFinancialAcct.class.getName() + ".getMbFinAcctsForEod", param);
   }

   public List<RbFinancialAcct> getMbFinAcctsByInternalKeyAndClient(Long internalKey, String clinetNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clinetNo);
      return this.daoSupport.selectList(RbFinancialAcct.class.getName() + ".getMbFinAcctsByInternalKeyAndClient", param);
   }

   public List<RbFinancialAcct> getMbFinAcctsByInternalkeyAndClientNo(Long internalKey, String clinetNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      param.put("clientNo", clinetNo);
      return this.daoSupport.selectList(RbFinancialAcct.class.getName() + ".getMbFinAcctsByInternalkeyAndClientNo", param);
   }
}
