package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.AcctInfoWhiteList;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class AcctInfoWhiteListRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(AcctInfoWhiteListRepository.class);

   public void updAcctInfoWhiteListDb(List<AcctInfoWhiteList> acctInfoWhiteLists) {
      if (BusiUtil.isNotNull(acctInfoWhiteLists)) {
         acctInfoWhiteLists.stream().forEach((acctInfoWhiteList) -> {
            super.update(acctInfoWhiteList);
         });
      }

   }

   public void insertAcctInfoWhiteListDb(List<AcctInfoWhiteList> acctInfoWhiteLists) {
      if (BusiUtil.isNotNull(acctInfoWhiteLists)) {
         acctInfoWhiteLists.stream().forEach((acctInfoWhiteList) -> {
            super.insert(acctInfoWhiteList);
         });
      }

   }
}
