package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.rb.business.model.cm.reg.EgRbFeeAmortizeAgr;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class EgRbFeeAmortizeAgrRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(EgRbFeeAmortizeAgrRepository.class);

   public void insertBatch(List<EgRbFeeAmortizeAgr> list) {
      if (BusiUtil.isNotNull(list)) {
         this.daoSupport.insertAddBatch(list);
      }

   }
}
