package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAutoCreateDetail;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@BusiUnit
public class RbAutoCreateDetailRepository extends BusinessRepository {
   public RbAutoCreateDetail selectExistDetail(String branch, String prodType, String ccy, String acctName) {
      Map<String, Object> param = new HashMap(3);
      param.put("branch", branch);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctName", acctName);
      return (RbAutoCreateDetail)this.daoSupport.selectOne(RbAutoCreateDetail.class.getName() + ".selectExistDetail", param);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void insertNewDetail(RbAutoCreateDetail rbAutoCreateDetail) {
      super.insert(rbAutoCreateDetail);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updateDetailByPrimaryKey(RbAutoCreateDetail rbAutoCreateDetail) {
      super.updateByPrimaryKey(rbAutoCreateDetail);
   }
}
