package com.dcits.ensemble.rb.business.repository.bill.ad;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.fm.model.FmSystem;
import com.dcits.ensemble.model.base.ChannelTypeEnum;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.common.util.FmUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAdRegister;
import com.dcits.ensemble.rb.business.model.bill.ad.EnumBillType;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.MbLglcGlhist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbLglcGlhistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbLglcGlhistRepository.class);

   public void regLgLcHist(RbAdRegister rbAdRegister, String narrative, String eventType) {
      if (log.isDebugEnabled()) {
         log.debug("Bank acceptance bill credit table external account bookkeeping table begins!" + rbAdRegister);
      }

      MbLglcGlhist mbLglcGlhist = new MbLglcGlhist();
      mbLglcGlhist.setReference(Context.getInstance().getReference());
      if (BusiUtil.isEquals(rbAdRegister.getBillMediumType(), EnumBillType.P.toString())) {
         mbLglcGlhist.setProdType("11P");
      }

      if (BusiUtil.isEquals(rbAdRegister.getBillMediumType(), EnumBillType.E.toString())) {
         mbLglcGlhist.setProdType("11E");
      }

      mbLglcGlhist.setOptionType("03");
      mbLglcGlhist.setTradeNo(rbAdRegister.getBillNo());
      mbLglcGlhist.setReversel("N");
      mbLglcGlhist.setChannelSeqNo(ChannelTypeEnum.MT.getCode());
      mbLglcGlhist.setBranch(rbAdRegister.getTranBranch());
      mbLglcGlhist.setOperUserId(rbAdRegister.getOperUserId());
      mbLglcGlhist.setClientNo(rbAdRegister.getPayerBaseAcctNo());
      mbLglcGlhist.setNarrative(narrative);
      mbLglcGlhist.setAmtType("ALL");
      mbLglcGlhist.setAmount(rbAdRegister.getBillAmt());
      mbLglcGlhist.setTranBranch(Context.getInstance().getBranchId());
      mbLglcGlhist.setTranCcy(rbAdRegister.getCcy());
      mbLglcGlhist.setGlPosted("N");
      mbLglcGlhist.setSourceModule("CL");
      mbLglcGlhist.setSourceType(Context.getInstance().getSourceType());
      mbLglcGlhist.setEventType(eventType);
      mbLglcGlhist.setTranType("ALL");
      mbLglcGlhist.setTranDate(Context.getInstance().getRunDate());
      mbLglcGlhist.setTranNo((String)SequenceGenerator.nextValue(SequenceEnum.invoiceTranNo, new String[]{Context.getInstance().getRunDate()}));
      FmSystem fmSystems = FmUtil.getFmSystem();
      mbLglcGlhist.setTranProfitCenter(fmSystems.getDefaultProfitCenter());
      this.creatMbLglcGlhist(mbLglcGlhist);
      if (log.isDebugEnabled()) {
         log.debug("Bank acceptance bill credit statement external account bookkeeping statement is completed!" + rbAdRegister);
      }

   }

   public MbLglcGlhist selectByTradeNo(String tradeNo) {
      Map<String, Object> map = new HashMap();
      map.put("tradeNo", tradeNo);
      return (MbLglcGlhist)this.daoSupport.selectOne(MbLglcGlhist.class.getName() + ".selectByTradeNo", map);
   }

   public List<MbLglcGlhist> selectByReference(String reference) {
      Map<String, Object> map = new HashMap();
      map.put("reference", reference);
      return this.daoSupport.selectList(MbLglcGlhist.class.getName() + ".selectByReference", map);
   }

   public int update(MbLglcGlhist mbLglcGlhist) {
      return super.update(mbLglcGlhist);
   }

   public List<String> selectMbLgLcHistByDateForKeys(Map param) {
      return this.daoSupport.selectList(MbLglcGlhist.class.getName() + ".selectMbLgLcHistByDateForKeys", param);
   }

   public List<MbLglcGlhist> selectMbLgLcHistByDateDetails(Map param) {
      return this.daoSupport.selectList(MbLglcGlhist.class.getName() + ".selectMbLgLcHistByDateDetails", param);
   }

   public void creatMbLglcGlhist(MbLglcGlhist mbLglcGlhist) {
      super.insert(mbLglcGlhist);
   }
}
