package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctContraInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbAcctContraInfoRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbAcctContraInfoRepository.class);

   public List<RbAcctContraInfo> queryByContraBaseAcctNo(String contraBaseAcctNo, String company) {
      Map<String, Object> param = new HashMap();
      param.put("contraBaseAcctNo", contraBaseAcctNo);
      param.put("company", company);
      return this.daoSupport.selectList(RbAcctContraInfo.class.getName() + ".queryByContraBaseAcctNo", param);
   }

   public RbAcctContraInfo queryByInternalKeyAndClientNo(Long internalKey, String clientNo) {
      RbAcctContraInfo rbAcctContraInfo = new RbAcctContraInfo();
      rbAcctContraInfo.setInternalKey(internalKey);
      rbAcctContraInfo.setClientNo(clientNo);
      return (RbAcctContraInfo)this.daoSupport.selectOne(rbAcctContraInfo);
   }

   public void createRbAcctContraInfoDb(List<RbAcctContraInfo> RbAcctContraInfoList) {
      if (BusiUtil.isNotNull(RbAcctContraInfoList)) {
         for(int i = 0; i < RbAcctContraInfoList.size(); ++i) {
            super.insert((EnsBaseDbBean)RbAcctContraInfoList.get(i));
         }
      }

   }

   public void updateClientNo(Map<String, Object> map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbAcctContraInfo.class.getName() + ".updateClientNo", map);
   }

   public List<RbAcctContraInfo> queryListByInternalKey(List<Long> internalKeys) {
      Map<String, Object> param = new HashMap(2);
      param.put("internalKeys", internalKeys);
      return this.daoSupport.selectList(RbAcctContraInfo.class.getName() + ".queryListByInternalKey", param);
   }
}
