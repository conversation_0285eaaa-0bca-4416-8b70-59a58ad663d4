package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbPcpAgreementRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpAgreementRepository.class);

   public void creatMbPcpAgreement(RbPcpAgreement mbPcpAgreement) {
      super.insert(mbPcpAgreement);
   }

   public void updateMbPcpAgreement(RbPcpAgreement mbPcpAgreement) {
      super.update(mbPcpAgreement);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void updatePcpAgreement(RbPcpAgreement mbPcpAgreement) {
      this.daoSupport.update(mbPcpAgreement);
   }

   public List<RbPcpAgreement> selectByMainAcct(String baseAcctNo, String prodType, String ccy, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByMainAcct", param);
   }

   public List<RbPcpAgreement> selectEffectAgreementByAcct(String baseAcctNo, String prodType, String ccy, String acctSeqNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectEffectAgreementByAcct", param);
   }

   public RbPcpAgreement selectByAcctAndId(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String agreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementId", agreementId);
      return (RbPcpAgreement)this.daoSupport.selectOne(RbPcpAgreement.class.getName() + ".selectByAcctAndId", param);
   }

   public List<RbPcpAgreement> selectRbPcpAgreements(String baseAcctNo, String prodType, String ccy, String acctSeqNo, String agreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("baseAcctNo", baseAcctNo);
      param.put("prodType", prodType);
      param.put("ccy", ccy);
      param.put("acctSeqNo", acctSeqNo);
      param.put("agreementId", agreementId);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByAcctAndId", param);
   }

   public List<RbPcpAgreement> selectPcpInfoByApprovalNo(String approvalNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("approvalNo", approvalNo);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectPcpInfoByApprovalNo", param);
   }

   public List<RbPcpAgreement> selectByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap(16);
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByInternalKey", param);
   }

   public RbPcpAgreement selectByAgreementId(String agreenemtId) {
      RbPcpAgreement rbPcpAgreement = new RbPcpAgreement();
      rbPcpAgreement.setAgreementId(agreenemtId);
      return (RbPcpAgreement)this.daoSupport.selectOne(rbPcpAgreement);
   }

   public RbPcpAgreement selectByPrimaryKeyByStatusA(String agreenemtId) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreenemtId);
      return (RbPcpAgreement)this.daoSupport.selectOne(RbPcpAgreement.class.getName() + ".selectByPrimaryKeyByStatusA", param);
   }

   /** @deprecated */
   @Deprecated
   public List<RbPcpAgreement> selectByGroupId(String groupId) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", groupId);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByGroupId", param);
   }

   public RbPcpAgreement selectByGroupIdAndStatus(String groupId, String status) {
      RbPcpAgreement rbPcpAgreement = new RbPcpAgreement();
      rbPcpAgreement.setAgreementStatus(status);
      rbPcpAgreement.setPcpGroupId(groupId);
      return (RbPcpAgreement)this.daoSupport.selectOne(rbPcpAgreement);
   }

   public List<RbPcpAgreement> selectByGroupId(String groupId, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", groupId);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByGroupId", param);
   }

   public List<RbPcpAgreement> selectByGroupIdInStatusAPC(String groupId) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", groupId);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByGroupIdInStatusAPC", param);
   }

   public List<RbPcpAgreement> selectByGroupIdInStatusAPCE(String groupId) {
      Map<String, Object> param = new HashMap(16);
      param.put("pcpGroupId", groupId);
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByGroupIdInStatusAPCE", param);
   }

   public List<RbPcpAgreement> selectByStatus() {
      return this.daoSupport.selectList(RbPcpAgreement.class.getName() + ".selectByStatus", new RbPcpAgreement());
   }

   public void updatePcpAgreementRateForm(RbPcpAgreement mbPcpAgreement) {
      this.daoSupport.update(RbPcpAgreement.class.getName() + ".updatePcpAgreementRateForm", mbPcpAgreement);
   }

   public void updateRateFormInfo(String agreementId) {
      Map<String, Object> param = new HashMap(16);
      param.put("agreementId", agreementId);
      this.daoSupport.update(RbPcpAgreement.class.getName() + ".updateRateFormInfo", param);
   }
}
