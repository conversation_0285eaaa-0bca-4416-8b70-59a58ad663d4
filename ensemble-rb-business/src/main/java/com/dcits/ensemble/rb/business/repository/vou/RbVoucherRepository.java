package com.dcits.ensemble.rb.business.repository.vou;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.data.RowArgs;
import com.dcits.comet.commons.exception.BusinessException;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.PageUtil;
import com.dcits.comet.dao.model.QueryResult;
import com.dcits.ensemble.component.sequence.SequenceGenerator;
import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.bc.component.cm.operate.util.PageQueryUtil;
import com.dcits.ensemble.rb.business.common.sequence.SequenceEnum;
import com.dcits.ensemble.rb.business.common.util.MqUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelationHist;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherJournal;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherBusiModel;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbVoucherStatusModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class RbVoucherRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbVoucherRepository.class);
   private static final String[] INVALID_VOUCHER_STATUS = new String[]{"CAN", "USE"};
   private static final String[] LOST_VOUCHER_STATUS = new String[]{"VER", "LCC"};
   @Resource
   private PageQueryUtil pageQueryUtil;
   @Resource
   private FmBaseStor fmBaseStor;

   public void changeVoucherStatus(RbVoucherAcctRelation mbVoucherAcctRelation) {
      mbVoucherAcctRelation.setTranTimestamp(BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbVoucherAcctRelation.class.getName() + ".changeVoucherStatus", mbVoucherAcctRelation);
      if (BusiUtil.isNotNull(mbVoucherAcctRelation.getCardNo())) {
         String operate = "INSERT";
         MqUtil.sendMqToGateWay(mbVoucherAcctRelation.getClientNo(), mbVoucherAcctRelation.getCardNo(), operate);
      }

   }

   public void changeVoucherinfo(RbVoucherAcctRelation mbVoucherAcctRelation) {
      super.update(mbVoucherAcctRelation);
   }

   public void upadteDocTypeAndStatus(RbVoucherAcctRelation mbVoucherAcctRelation) {
      this.daoSupport.update(RbVoucherAcctRelation.class.getName() + ".upadteDocTypeAndStatus", mbVoucherAcctRelation);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationByAcctNoVouch(String baseAcctNo, String ccy, String prodType, String seqNo, String docType, String prefix, String voucherNo, Boolean isCard, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("ccy", ccy);
      map.put("prodType", prodType);
      map.put("acctSeqNo", seqNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      map.put("clientNo", clientNo);
      if (BusiUtil.isNotNull(isCard) && isCard) {
         map.put("isCard", "true");
      }

      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getMbVoucherAcctRelationByAcctNoVouch", map);
   }

   public RbVoucherAcctRelation getMbVoucherAcctRelationByAcctNoVouch2(String baseAcctNo, String ccy, String prodType, String seqNo, String docType, String prefix, String voucherNo, Boolean isCard, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("ccy", ccy);
      map.put("prodType", prodType);
      map.put("acctSeqNo", seqNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      map.put("clientNo", clientNo);
      if (BusiUtil.isNotNull(isCard)) {
         if (isCard) {
            map.put("isCard", "true");
         } else {
            map.put("isCard", "false");
         }
      }

      return (RbVoucherAcctRelation)this.daoSupport.selectOne(RbVoucherAcctRelation.class.getName() + ".getMbVoucherAcctRelationByAcctNoVouch2", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationInfo(String baseAcctNo, String ccy, String prodType, String seqNo, String docType, String prefix, String voucherNo, Boolean isCard, String clientNo, String voucherStatus) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("ccy", ccy);
      map.put("prodType", prodType);
      map.put("acctSeqNo", seqNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      map.put("clientNo", clientNo);
      map.put("voucherStatus", voucherStatus);
      if (BusiUtil.isNotNull(isCard) && isCard) {
         map.put("isCard", "true");
      }

      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getMbVoucherAcctRelationInfo", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationIsNotCan(String baseAcctNo, String ccy, String prodType, String seqNo, String docType, String prefix, String voucherNo, Boolean isCard, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("ccy", ccy);
      map.put("prodType", prodType);
      map.put("seqNo", seqNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      map.put("clientNo", clientNo);
      if (BusiUtil.isNotNull(isCard) && isCard) {
         map.put("isCard", "true");
      }

      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getMbVoucherAcctRelationIsNotCan", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherInfoByAcctNo(String baseAcctNo, String ccy, String prodType, String seqNo, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("acctCcy", ccy);
      map.put("prodType", prodType);
      map.put("acctSeqNo", seqNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getMbVoucherInfoByAcctNo", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherInfoByAcctNoDocCla(String cardNo, String baseAcctNo, String ccy, String prodType, String seqNo, String clientNo, String docClass) {
      Map<String, Object> map = new HashMap(5);
      map.put("cardNo", cardNo);
      map.put("baseAcctNo", baseAcctNo);
      map.put("acctCcy", ccy);
      map.put("prodType", prodType);
      map.put("acctSeqNo", seqNo);
      map.put("clientNo", clientNo);
      map.put("docClass", docClass);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getMbVoucherInfoByAcctNoCla", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherInfoByAcctNoByPage(String baseAcctNo, String ccy, String prodType, String seqNo, String clientNo) {
      Map<String, Object> map = new HashMap(6);
      map.put("baseAcctNo", baseAcctNo);
      map.put("acctCcy", ccy);
      map.put("prodType", prodType);
      map.put("acctSeqNo", seqNo);
      map.put("clientNo", clientNo);
      map.put("docTypeNeq", "748");
      return this.pageQueryUtil.selectByPage(RbVoucherAcctRelation.class.getName() + ".getMbVoucherInfoByAcctNo", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherInfoByAcctNoNotChk(String baseAcctNo, String ccy, String prodType, String seqNo, String clientNo, String filterDocType) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("acctCcy", ccy);
      map.put("prodType", prodType);
      map.put("acctSeqNo", seqNo);
      map.put("clientNo", clientNo);
      map.put("filterDocType", filterDocType);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getMbVoucherInfoByAcctNo", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationByVoucher14000325(String baseAcctNo, String acctSeqNo, String ccy, String prodType, String docType, String prefix, String startVoucherNo, String endVoucherNo, String voucherStatus, String clientNo, String tranDate) {
      Map<String, Object> map = new HashMap(15);
      map.put("baseAcctNo", baseAcctNo);
      map.put("acctSeqNo", acctSeqNo);
      map.put("ccy", ccy);
      map.put("prodType", prodType);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("startVoucherNo", startVoucherNo);
      map.put("endVoucherNo", endVoucherNo);
      map.put("voucherStatus", voucherStatus);
      map.put("clientNo", clientNo);
      if (BusiUtil.isNotNull(tranDate)) {
         map.put("tranDate", DateUtil.parseDate(tranDate, new String[]{"yyyyMMdd"}));
      }

      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getVouAcctMsgByVoucherOrDate", map);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationByVoucher14000330(String ccy, String docType, String prefix, long startVoucherNo, long endVoucherNo, String branchNo, String tranDate) {
      Map<String, Object> map = new HashMap(10);
      map.put("ccy", ccy);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("startVoucherNo", startVoucherNo);
      map.put("endVoucherNo", endVoucherNo);
      map.put("branchNo", branchNo);
      map.put("tranDate", tranDate);
      RowArgs rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      String statementPostfix = RbVoucherAcctRelation.class.getName() + ".getVouAcctMsgByVoucherOrDate";
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbVoucherAcctRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, map, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, map);
      }
   }

   public List<RbVoucherAcctRelation> getListByVoucherNo(String docType, String prefix, String voucherNo) {
      Map<String, Object> map = new HashMap(10);
      MultiCorpUtil.prepareCompanyQueryCondition(map, false);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getListByVoucherNo", map);
   }

   public void voucherAcctRelationChange(Map map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbVoucherAcctRelation.class.getName() + ".relationChange", map);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {Exception.class}
   )
   public void voucherAcctReversalChange(Map map) {
      map.put("tranTimestamp", BusiUtil.getTranTimestamp26());
      this.daoSupport.update(RbVoucherAcctRelation.class.getName() + ".relationChange", map);
   }

   public List<RbVoucherAcctRelation> getVoucherInfoByAcctVouchStatus(String baseAcctNo, String ccy, String prodType, String acctSeqNo, String tranDate, String voucherStatus, String oldStatus, String clientNo) {
      RbVoucherAcctRelation query = new RbVoucherAcctRelation();
      query.setBaseAcctNo(baseAcctNo);
      query.setAcctCcy(ccy);
      query.setProdType(prodType);
      query.setAcctSeqNo(acctSeqNo);
      query.setTranDate(BusiUtil.convertStr2Date(tranDate));
      query.setVoucherStatus(voucherStatus);
      query.setOldStatus(oldStatus);
      query.setClientNo(clientNo);
      return this.daoSupport.selectList(query);
   }

   public void createMbVoucherJournal(RbVoucherJournal mbVoucherJournal) {
      String journalId = (String)SequenceGenerator.nextValue(SequenceEnum.voucherJournalSeqNo, new String[]{Context.getInstance().getRunDate()});
      mbVoucherJournal.setVoucherJournalId(journalId);
      super.insert(mbVoucherJournal);
   }

   public List<RbVoucherAcctRelationHist> getHistByAcctVouchStatus(String baseAcctNo, String ccy, String prodType, String acctSeqNo, String tranDate, String voucherStatus, String oldStatus, String clientNo) {
      RbVoucherAcctRelationHist rbVoucherAcctRelationHist = new RbVoucherAcctRelationHist();
      rbVoucherAcctRelationHist.setBaseAcctNo(baseAcctNo);
      rbVoucherAcctRelationHist.setAcctCcy(ccy);
      rbVoucherAcctRelationHist.setProdType(prodType);
      rbVoucherAcctRelationHist.setAcctSeqNo(acctSeqNo);
      rbVoucherAcctRelationHist.setTranDate(DateUtil.parseDate(tranDate));
      rbVoucherAcctRelationHist.setVoucherStatus(voucherStatus);
      rbVoucherAcctRelationHist.setOldStatus(oldStatus);
      rbVoucherAcctRelationHist.setClientNo(clientNo);
      return this.daoSupport.selectList(rbVoucherAcctRelationHist);
   }

   public List<RbVoucherAcctRelationHist> getVoucherAcctRelationHistByAcctNoVoucherNo(String baseAcctNo, String ccy, String prodType, String acctSeqNo, String docType, String prefix, String voucherNo, String clientNo) {
      Map<String, Object> map = new HashMap(10);
      map.put("baseAcctNo", baseAcctNo);
      map.put("ccy", ccy);
      map.put("prodType", prodType);
      map.put("seqNo", acctSeqNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherAcctRelationHist.class.getName() + ".getMbVoucherAcctRelationByAcctNoVouch", map);
   }

   public List<RbVoucherAcctRelationHist> getVoucherAcctRelationHistByBaseAcctNo(String baseAcctNo, String ccy, String prodType, String acctSeqNo, String clientNo) {
      RbVoucherAcctRelationHist rbVoucherAcctRelationHist = new RbVoucherAcctRelationHist();
      rbVoucherAcctRelationHist.setBaseAcctNo(baseAcctNo);
      rbVoucherAcctRelationHist.setAcctCcy(ccy);
      rbVoucherAcctRelationHist.setProdType(prodType);
      rbVoucherAcctRelationHist.setAcctSeqNo(acctSeqNo);
      rbVoucherAcctRelationHist.setClientNo(clientNo);
      return this.daoSupport.selectList(rbVoucherAcctRelationHist);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationByCardNo(String cardNo, String clientNo) {
      RbVoucherAcctRelation rbVoucherAcctRelation = new RbVoucherAcctRelation();
      rbVoucherAcctRelation.setCardNo(cardNo);
      rbVoucherAcctRelation.setClientNo(clientNo);
      return this.daoSupport.selectList(rbVoucherAcctRelation);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationByVoucher(String baseAcctNo, String docType, String prefix, String voucherNo, String voucherStatus, String clientNo) {
      RbVoucherAcctRelation rbVoucherAcctRelation = new RbVoucherAcctRelation();
      rbVoucherAcctRelation.setBaseAcctNo(baseAcctNo);
      rbVoucherAcctRelation.setDocType(docType);
      rbVoucherAcctRelation.setPrefix(prefix);
      rbVoucherAcctRelation.setVoucherNo(voucherNo);
      rbVoucherAcctRelation.setVoucherStatus(voucherStatus);
      rbVoucherAcctRelation.setClientNo(clientNo);
      return this.daoSupport.selectList(rbVoucherAcctRelation);
   }

   public List<RbVoucherAcctRelation> getMbVoucherAcctRelationByVoucherForPage(String baseAcctNo, String docType, String prefix, String voucherStartNo, String voucherEndNo, String voucherStatus, String clientNo) {
      Map<String, Object> param = new HashMap(16);
      RowArgs rowArgs = null;
      if (BusiUtil.isNotNull(Context.getInstance().getAppHead().getTotalNum()) && Integer.parseInt(Context.getInstance().getAppHead().getTotalNum()) != -1) {
         rowArgs = PageUtil.convertAppHead(Context.getInstance().getAppHead());
      }

      String statementPostfix = RbVoucherAcctRelation.class.getName() + ".getMbVoucherAcctRelationByStartEndVoucherNo";
      param.put("baseAcctNo", baseAcctNo);
      param.put("docType", docType);
      param.put("prefix", prefix);
      param.put("voucherStartNo", voucherStartNo);
      param.put("voucherEndNo", voucherEndNo);
      param.put("voucherStatus", voucherStatus);
      param.put("clientNo", clientNo);
      if (BusiUtil.isNotNull(rowArgs)) {
         QueryResult<RbVoucherAcctRelation> queryResult = this.daoSupport.selectQueryResult(statementPostfix, param, rowArgs.getPageIndex(), rowArgs.getLimit());
         Context.getInstance().getAppHead().setTotalRows(String.valueOf(queryResult.getTotalrecord()));
         return queryResult.getResultlist();
      } else {
         return this.daoSupport.selectList(statementPostfix, param);
      }
   }

   public RbVoucherAcctRelation selectByPrimaryKey(String docType, String voucherNo, String baseAcctNo, String clientNo) {
      RbVoucherAcctRelation rbVoucherAcctRelation = new RbVoucherAcctRelation();
      rbVoucherAcctRelation.setBaseAcctNo(baseAcctNo);
      rbVoucherAcctRelation.setDocType(docType);
      rbVoucherAcctRelation.setVoucherNo(voucherNo);
      rbVoucherAcctRelation.setClientNo(clientNo);
      return (RbVoucherAcctRelation)this.daoSupport.selectOne(rbVoucherAcctRelation);
   }

   public RbVoucherAcctRelation selectByPrimaryKeyCardNo(String docType, String cardNo, String baseAcctNo, String clientNo) {
      RbVoucherAcctRelation rbVoucherAcctRelation = new RbVoucherAcctRelation();
      rbVoucherAcctRelation.setBaseAcctNo(baseAcctNo);
      rbVoucherAcctRelation.setDocType(docType);
      rbVoucherAcctRelation.setCardNo(cardNo);
      rbVoucherAcctRelation.setClientNo(clientNo);
      return (RbVoucherAcctRelation)this.daoSupport.selectOne(rbVoucherAcctRelation);
   }

   public List<RbVoucherAcctRelation> selectByBaseInfo(String baseAcctNo, String ccy, String prodType, String seqNo, String docType, String clientNo) {
      RbVoucherAcctRelation query = new RbVoucherAcctRelation();
      query.setBaseAcctNo(baseAcctNo);
      query.setAcctCcy(ccy);
      query.setProdType(prodType);
      query.setAcctSeqNo(seqNo);
      query.setDocType(docType);
      query.setClientNo(clientNo);
      return this.daoSupport.selectList(query);
   }

   public List<RbVoucherAcctRelation> getRbVoucherAcctRelationListByPage(RbVoucherAcctRelation query) {
      Map<String, Object> map = new HashMap(6);
      map.put("baseAcctNo", query.getBaseAcctNo());
      map.put("acctCcy", query.getAcctCcy());
      map.put("prodType", query.getProdType());
      map.put("acctSeqNo", query.getAcctSeqNo());
      map.put("docType", query.getDocType());
      map.put("clientNo", query.getClientNo());
      return this.pageQueryUtil.selectByPage(RbVoucherAcctRelation.class.getName() + ".getRbVoucherAcctRelationListByPage", map);
   }

   public int selectCount(RbVoucherAcctRelation query) {
      return this.daoSupport.count(query);
   }

   public List<RbVoucherAcctRelation> selectByBaseInfo(String baseAcctNo, String ccy, String prodType, String seqNo, String clientNo) {
      return this.selectByBaseInfo(baseAcctNo, ccy, prodType, seqNo, (String)null, clientNo);
   }

   public List<RbVoucherAcctRelation> selectByBaseInfoEffective(String baseAcctNo, String ccy, String prodType, String seqNo, String clientNo) {
      return this.selectByBaseInfoEffective(baseAcctNo, ccy, prodType, seqNo, (String)null, clientNo);
   }

   public List<RbVoucherAcctRelation> selectByBaseInfoEffective(String baseAcctNo, String ccy, String prodType, String seqNo, String docType, String clientNo) {
      log.debug("selectByBaseInfoEffective -- start");
      List<RbVoucherAcctRelation> list = this.selectByBaseInfo(baseAcctNo, ccy, prodType, seqNo, docType, clientNo);
      List<RbVoucherAcctRelation> result = new ArrayList();
      if (BusiUtil.isNotNull(list)) {
         result = (List)list.stream().filter((p) -> {
            return BusiUtil.strNotIn(p.getVoucherStatus(), INVALID_VOUCHER_STATUS);
         }).collect(Collectors.toList());
      }

      log.debug("selectByBaseInfoEffective -- result -- {}", result);
      return (List)result;
   }

   public List<RbVoucherAcctRelation> selectByBaseInfoEffectiveA(String baseAcctNo, String ccy, String prodType, String seqNo, String docClass, String clientNo) {
      log.debug("selectByBaseInfoEffectiveA -- start");
      List<RbVoucherAcctRelation> list = this.getByBaseInfoEffective(baseAcctNo, ccy, prodType, seqNo, docClass, clientNo);
      List<RbVoucherAcctRelation> result = new ArrayList();
      if (BusiUtil.isNotNull(list)) {
         result = (List)list.stream().filter((p) -> {
            return BusiUtil.strNotIn(p.getVoucherStatus(), INVALID_VOUCHER_STATUS);
         }).collect(Collectors.toList());
      }

      log.debug("selectByBaseInfoEffectiveA -- result -- {}", result);
      return (List)result;
   }

   public List<RbVoucherAcctRelation> getByBaseInfoEffective(String baseAcctNo, String ccy, String prodType, String seqNo, String docClass, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("ccy", ccy);
      map.put("prodType", prodType);
      map.put("seqNo", seqNo);
      map.put("docClass", docClass);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getByBaseInfoEffective", map);
   }

   public List<RbVoucherAcctRelation> selectByBaseInfoLost(String baseAcctNo, String ccy, String prodType, String seqNo, String docType, String clientNo) {
      log.debug("selectByBaseInfoLost -- start");
      List<RbVoucherAcctRelation> list = this.selectByBaseInfo(baseAcctNo, ccy, prodType, seqNo, docType, clientNo);
      List<RbVoucherAcctRelation> result = null;
      if (BusiUtil.isNotNull(list)) {
         result = (List)list.stream().filter((p) -> {
            return BusiUtil.strIn(p.getVoucherStatus(), LOST_VOUCHER_STATUS);
         }).collect(Collectors.toList());
      }

      log.debug("selectByBaseInfoLost -- result -- {}", result);
      return result;
   }

   public RbVoucherAcctRelation getVoucherAcctRelation(RbVoucherAcctRelation rbVoucherAcctRelation) {
      return (RbVoucherAcctRelation)this.daoSupport.selectOne(rbVoucherAcctRelation);
   }

   public List<RbVoucherAcctRelation> getRbVoucherClientNoList(String baseAcctNo, String docType, String prefix, String voucherNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getRbVoucherClientNoList", map);
   }

   public List<RbVoucherBusiModel> getRbVoucherBusiModelList(String baseAcctNo, String docType, String prefix, String voucherNo, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getRbVoucherBusiModelList", map);
   }

   public List<RbVoucherBusiModel> getRbVoucherJournalList(String baseAcctNo, String docType, String prefix, String voucherNo, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("docType", docType);
      map.put("prefix", prefix);
      map.put("voucherNo", voucherNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherJournal.class.getName() + ".getRbVoucherList", map);
   }

   public List<RbVoucherStatusModel> getRbVoucherStatusModelList(String baseAcctNo, String clientNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getRbVoucherStatusModelList", map);
   }

   public List<RbVoucherJournal> getRbVoucherJournalByRefAndSeq(String baseAcctNo, String cardNo, String clientNo, String reference, String channelSeqNo) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("cardNo", cardNo);
      map.put("clientNo", clientNo);
      map.put("reference", reference);
      map.put("channelSeqNo", channelSeqNo);
      return this.daoSupport.selectList(RbVoucherJournal.class.getName() + ".getRbVoucherByRefAndSeq", map);
   }

   public RbVoucherAcctRelation getRbVoucherByBaseAcctInfo(String baseAcctNo, String clientNo, String docClass) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("clientNo", clientNo);
      map.put("docClass", docClass);
      return (RbVoucherAcctRelation)this.daoSupport.selectOne(RbVoucherAcctRelation.class.getName() + ".getMbVoucherAcctRelationByBaseInfo", map);
   }

   @Transactional(
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = {BusinessException.class, Exception.class, RuntimeException.class}
   )
   public void updateMbVoucherByInfo(RbVoucherAcctRelation rbVoucherAcctRelation, String newProdType) {
      Map<String, Object> param = new HashMap(16);
      param.put("prodType", rbVoucherAcctRelation.getProdType());
      param.put("baseAcctNo", rbVoucherAcctRelation.getBaseAcctNo());
      param.put("acctSeqNo", rbVoucherAcctRelation.getAcctSeqNo());
      param.put("acctCcy", rbVoucherAcctRelation.getAcctCcy());
      param.put("clientNo", rbVoucherAcctRelation.getClientNo());
      param.put("newProdType", newProdType);
      this.daoSupport.update(RbVoucherAcctRelation.class.getName() + ".updateMbVoucherRelationByBaseInfo", param);
   }

   public void updateMbVoucherByCardNoAndVoucherNo(RbVoucherAcctRelation rbVoucherAcctRelation) {
      Map<String, Object> param = new HashMap(16);
      param.put("voucherStatus", rbVoucherAcctRelation.getVoucherStatus());
      param.put("docType", rbVoucherAcctRelation.getDocType());
      param.put("acctSeqNo", rbVoucherAcctRelation.getAcctSeqNo());
      param.put("cardNo", rbVoucherAcctRelation.getCardNo());
      param.put("voucherNo", rbVoucherAcctRelation.getVoucherNo());
      this.daoSupport.update(RbVoucherAcctRelation.class.getName() + ".updateMbVoucherByCardNoAndVoucherNo", param);
   }

   public void updateMbVoucherByInfoNoTransaction(RbVoucherAcctRelation rbVoucherAcctRelation, String newProdType) {
      Map<String, Object> param = new HashMap(16);
      param.put("prodType", rbVoucherAcctRelation.getProdType());
      param.put("baseAcctNo", rbVoucherAcctRelation.getBaseAcctNo());
      param.put("acctSeqNo", rbVoucherAcctRelation.getAcctSeqNo());
      param.put("acctCcy", rbVoucherAcctRelation.getAcctCcy());
      param.put("clientNo", rbVoucherAcctRelation.getClientNo());
      param.put("newProdType", newProdType);
      this.daoSupport.update(RbVoucherAcctRelation.class.getName() + ".updateMbVoucherRelationByBaseInfo", param);
   }

   public List<RbVoucherAcctRelation> selectByBaseInfoAndReference(String baseAcctNo, String seqNo, String clientNo, String reference) {
      RbVoucherAcctRelation query = new RbVoucherAcctRelation();
      query.setBaseAcctNo(baseAcctNo);
      query.setAcctSeqNo(seqNo);
      query.setClientNo(clientNo);
      query.setReference(reference);
      return this.daoSupport.selectList(query);
   }

   public List<RbVoucherAcctRelation> getRbVoucherByVoucherStatus(String baseAcctNo, String ccy, String prodtype, String acctseqno, String clientNo, String docType) {
      Map<String, Object> map = new HashMap(5);
      map.put("baseAcctNo", baseAcctNo);
      map.put("docType", docType);
      map.put("acctCcy", ccy);
      map.put("prodType", prodtype);
      map.put("acctSeqNo", acctseqno);
      map.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbVoucherAcctRelation.class.getName() + ".getRbVoucherByVoucherStatus", map);
   }

   public List<RbVoucherAcctRelation> selectBy4Key(String baseAcctNo, String prodType, String acctCcy, String acctSeqNo) {
      RbVoucherAcctRelation rbVoucherAcctRelation = new RbVoucherAcctRelation();
      rbVoucherAcctRelation.setBaseAcctNo(baseAcctNo);
      rbVoucherAcctRelation.setProdType(prodType);
      rbVoucherAcctRelation.setAcctCcy(acctCcy);
      rbVoucherAcctRelation.setAcctSeqNo(acctSeqNo);
      return this.daoSupport.selectList(rbVoucherAcctRelation);
   }
}
