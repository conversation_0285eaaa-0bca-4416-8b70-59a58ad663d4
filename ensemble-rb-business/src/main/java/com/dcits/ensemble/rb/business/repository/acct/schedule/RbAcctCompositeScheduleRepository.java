package com.dcits.ensemble.rb.business.repository.acct.schedule;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.RbAcctCompositeSchedule;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAcctCompositeScheduleRepository extends BusinessRepository {
   public List<RbAcctCompositeSchedule> selectScheduleByInternalKey(Long internalKey) {
      RbAcctCompositeSchedule rbAcctCompositeSchedule = new RbAcctCompositeSchedule();
      rbAcctCompositeSchedule.setInternalKey(internalKey);
      return this.daoSupport.selectList(rbAcctCompositeSchedule);
   }

   public RbAcctCompositeSchedule selectCompositeScheduleByStartDate(Long internalKey, Date startDate, String company) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("company", company);
      return (RbAcctCompositeSchedule)this.daoSupport.selectOne(RbAcctCompositeSchedule.class.getName() + ".selectCompositeScheduleByStartDate", param);
   }
}
