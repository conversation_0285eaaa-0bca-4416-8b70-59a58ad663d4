package com.dcits.ensemble.rb.business.repository.pcp;

import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpArrearageHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class RbPcpArrearageHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(RbPcpArrearageHistRepository.class);

   public List<String> queryPcpArrearage(Map param) {
      return (List)this.daoSupport.selectObject(RbPcpArrearageHist.class.getName() + ".queryPcpArrearage", param);
   }

   public List<RbPcpArrearageHist> queryPcpArrearageByInternalKey(long startKey, long endKey) {
      Map<String, Object> param = new HashMap();
      param.put("START_KEY", startKey);
      param.put("END_KEY", endKey);
      return this.daoSupport.selectList(RbPcpArrearageHist.class.getName() + ".queryPcpArrearageByInternalKey", param);
   }

   public List<RbPcpArrearageHist> queryPcpArrearageByInternalKey() {
      Map<String, Object> param = new HashMap();
      return this.daoSupport.selectList(RbPcpArrearageHist.class.getName() + ".queryPcpArrearageBystatus", param);
   }

   public List<RbPcpArrearageHist> getMbPcpArrearageHistByDate(String internalKey, Date startDate, Date endDate) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      param.put("startDate", startDate);
      param.put("endDate", endDate);
      return this.daoSupport.selectList(RbPcpArrearageHist.class.getName() + ".getMbPcpArrearageHistByDate", param);
   }

   public void insert(RbPcpArrearageHist mbPcpArrearageHist) {
      super.insert(mbPcpArrearageHist);
   }

   public void createMbAcctBalDb(List<RbPcpArrearageHist> mbPcpArrearageHists) {
      if (BusiUtil.isNotNull(mbPcpArrearageHists)) {
         for(int i = 0; i < mbPcpArrearageHists.size(); ++i) {
            super.insert((EnsBaseDbBean)mbPcpArrearageHists.get(i));
         }
      }

   }

   public void createByPrimaryKey(RbPcpArrearageHist mbPcpArrearageHist) {
      super.insert(mbPcpArrearageHist);
   }

   public void updateByPrimaryKey(RbPcpArrearageHist mbPcpArrearageHist) {
      super.update(mbPcpArrearageHist);
   }
}
