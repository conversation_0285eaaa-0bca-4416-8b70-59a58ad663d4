package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementYhtRule;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbAgreementYhtRuleRepository extends BusinessRepository {
   public RbAgreementYhtRule getDistributeIntRule(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return (RbAgreementYhtRule)this.daoSupport.selectOne(RbAgreementYhtRule.class.getName() + ".getByagreementId", param);
   }

   public RbAgreementYhtRule getYhtRule(String agreementId) {
      Map<String, Object> param = new HashMap();
      param.put("agreementId", agreementId);
      return (RbAgreementYhtRule)this.daoSupport.selectOne(RbAgreementYhtRule.class.getName() + ".getByagreementId", param);
   }

   public void createRule(RbAgreementYhtRule rbAgreementYhtRule) {
      super.insert(rbAgreementYhtRule);
   }

   public void updateRule(RbAgreementYhtRule rbAgreementYhtRule) {
      super.update(rbAgreementYhtRule);
   }
}
