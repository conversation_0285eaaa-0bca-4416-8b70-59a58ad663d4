package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbContactList;
import com.dcits.ensemble.rb.business.model.acct.MbContactModel;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import com.dcits.ensemble.util.ObjectUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
@BusiUnit
public class RbContactListRepository extends BusinessRepository {
   public List<RbContactList> getMbContactLists(Long internalkey, String clientNo, String company) {
      if (internalkey == null) {
         throw BusiUtil.createBusinessException("RB2333");
      } else {
         Map<String, Object> param = new HashMap();
         param.put("internalKey", internalkey);
         param.put("clientNo", clientNo);
         param.put("company", company);
         List<RbContactList> rbContactList = this.daoSupport.selectList(RbContactList.class.getName() + ".getContactByInternalKey", param);
         return rbContactList;
      }
   }

   public RbContactList createMbContactList(MbContactModel mbContactListModel) {
      RbContactList rbContactList = new RbContactList();
      ObjectUtil.copyPrint(mbContactListModel, "mbContactListModel", rbContactList, "rbContactList");
      rbContactList.setInternalKey(mbContactListModel.getInternalKey());
      rbContactList.setLinkmanName(mbContactListModel.getContactOfficer());
      rbContactList.setDocumentType(mbContactListModel.getGobalIdType());
      rbContactList.setDocumentId(mbContactListModel.getGobalId());
      rbContactList.setPhoneNo1(mbContactListModel.getPhoneNo1());
      rbContactList.setPhoneNo2(mbContactListModel.getPhoneNo2());
      rbContactList.setLinkmanType((String)BusiUtil.nvl(mbContactListModel.getLinkmanType(), mbContactListModel.getContactType()));
      rbContactList.setContactType(mbContactListModel.getContactType());
      rbContactList.setCompany(mbContactListModel.getCompany());
      rbContactList.setClientNo(mbContactListModel.getClientNo());
      rbContactList.setLinkmanDesc(mbContactListModel.getLinkmanDesc());
      rbContactList.setContactClass(mbContactListModel.getContactClass());
      rbContactList.setContactStatus("A");
      rbContactList.setCheckCertificateOrder(mbContactListModel.getCheckCertificateOrder());
      rbContactList.setCheckCertificateFlag(mbContactListModel.getCheckCertificateFlag());
      return rbContactList;
   }

   public void createMbContactListDb(RbContactList rbContactList) {
      super.insert(rbContactList);
   }

   public void deleteMbContactListByInternalKeyDb(RbContactList rbContactList) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", rbContactList.getInternalKey());
      param.put("clientNo", rbContactList.getClientNo());
      param.put("company", rbContactList.getCompany());
      this.daoSupport.delete(RbContactList.class.getName() + ".deleteByInternalKeyAndClientNo", param);
   }

   public void createMbContactListDbList(List<RbContactList> rbContactLists) {
      for(int i = 0; i < rbContactLists.size(); ++i) {
         super.insert((EnsBaseDbBean)rbContactLists.get(i));
      }

   }

   public void deleteMbContactListByInternalKeyDbList(List<RbContactList> rbContactLists) {
      if (BusiUtil.isNotNull(rbContactLists)) {
         Object internalKey = ((RbContactList)rbContactLists.get(0)).getInternalKey();
         Object clientNo = ((RbContactList)rbContactLists.get(0)).getClientNo();
         Map<String, Object> param = new HashMap();
         param.put("internalKey", internalKey);
         param.put("clientNo", clientNo);
         param.put("company", ((RbContactList)rbContactLists.get(0)).getCompany());
         this.daoSupport.delete(RbContactList.class.getName() + ".deleteByInternalKeyAndClientNo", param);
      }

   }
}
