package com.dcits.ensemble.rb.business.repository.agr;

import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbImpoundInfo;
import com.dcits.ensemble.repository.BusinessRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
@BusiUnit
public class RbImpoundInfoRepository extends BusinessRepository {
   public List<RbImpoundInfo> getMbImpoundInfoByInternalKey(Long internalKey) {
      Map<String, Object> param = new HashMap();
      param.put("internalKey", internalKey);
      return this.daoSupport.selectList(RbImpoundInfo.class.getName() + ".getMbImpoundInfoList", param);
   }

   public List<RbImpoundInfo> getMbImpoundInfoBySchedNo(String schedNo) {
      Map<String, Object> param = new HashMap();
      param.put("schedNo", schedNo);
      return this.daoSupport.selectList(RbImpoundInfo.class.getName() + ".getMbImpoundInfoBySchedNo", param);
   }

   public RbImpoundInfo getMbImpoundInfoByReference(String reference, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("reference", reference);
      param.put("clientNo", clientNo);
      return (RbImpoundInfo)this.daoSupport.selectOne(RbImpoundInfo.class.getName() + ".getMbImpoundInfoByReference", param);
   }

   public List<RbImpoundInfo> getMbImpoundInfoByBaseAcctNoOrDate(Long internalkey, String startDate, String endDate, String branch, String transferFlag, String clientNo) {
      Map<String, Object> param = new HashMap();
      param.put("internal_key", internalkey);
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("branch", branch);
      param.put("transferFlag", transferFlag);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbImpoundInfo.class.getName() + ".getMbImpoundInfoByBaseAcctNoOrDate", param);
   }

   public List<RbImpoundInfo> getMbImpoundInfoByBranch(String startDate, String endDate, String branch, String transferFlag) {
      Map<String, Object> param = new HashMap();
      param.put("startDate", DateUtil.parseDate(startDate));
      param.put("endDate", DateUtil.parseDate(endDate));
      param.put("branch", branch);
      param.put("transferFlag", transferFlag);
      return this.daoSupport.selectList(RbImpoundInfo.class.getName() + ".getMbImpoundInfoByBranch", param);
   }

   public List<String> selectFmBranchAttached(String attachId) {
      Map<String, Object> param = new HashMap();
      param.put("branch", attachId);
      return this.daoSupport.selectList(RbImpoundInfo.class.getName() + ".selectFmBranchAttached", param);
   }

   public List<RbImpoundInfo> getMbImpoundInfoforcheck(String clientNo, Long internalkey) {
      Map<String, Object> param = new HashMap();
      param.put("internal_key", internalkey);
      param.put("clientNo", clientNo);
      return this.daoSupport.selectList(RbImpoundInfo.class.getName() + ".getMbImpoundInfoByClientAndInternalkey", param);
   }
}
