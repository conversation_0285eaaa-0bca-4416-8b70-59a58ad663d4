package com.dcits.ensemble.rb.business.repository.channel;

import com.dcits.ensemble.fm.core.FmBaseStor;
import com.dcits.ensemble.fm.util.MultiCorpUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.PtPaymentTranHist;
import com.dcits.ensemble.repository.BusinessRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class PtPaymentTranHistRepository extends BusinessRepository {
   private static final Logger log = LoggerFactory.getLogger(PtPaymentTranHistRepository.class);
   @Resource
   private FmBaseStor fmBaseStor;

   public List<PtPaymentTranHist> selectByChannelTypeAndRefNo(String channelType, String refNo, String settleStep, String settleNo) {
      Map<String, Object> param = new HashMap();
      param.put("channelType", channelType);
      param.put("refNo", refNo);
      return this.daoSupport.selectList(PtPaymentTranHist.class.getName() + ".selectByChannelTypeAndRefNo", param);
   }

   public List<PtPaymentTranHist> selectByRefNoAndSubSeqNo(String channelType, String refNo, String channelSubSeqNo) {
      Map<String, Object> param = new HashMap();
      param.put("channelType", channelType);
      param.put("refNo", refNo);
      param.put("channelSubSeqNo", channelSubSeqNo);
      return this.daoSupport.selectList(PtPaymentTranHist.class.getName() + ".selectByRefNoAndSubSeqNo", param);
   }

   public List<PtPaymentTranHist> selectByRefNoAndStepNo(String channelFlag, String channelRefNo, String channelStepNo) {
      Map<String, Object> param = new HashMap();
      param.put("channelFlag", channelFlag);
      param.put("channelRefNo", channelRefNo);
      return this.daoSupport.selectList(PtPaymentTranHist.class.getName() + ".selectByRefNoAndStepNo", param);
   }

   public List<PtPaymentTranHist> selectByChannelRefNoAndStepNo(String channelFlag, String channelRefNo, String channelStepNo) {
      Map<String, Object> param = new HashMap();
      MultiCorpUtil.prepareCompanyQueryCondition(param, false);
      param.put("channelFlag", channelFlag);
      param.put("channelRefNo", channelRefNo);
      return this.daoSupport.selectList(PtPaymentTranHist.class.getName() + ".selectByChannelRefNoAndStepNo", param);
   }

   public void updateByRefNo(PtPaymentTranHist ptPaymentTranHist, String acctPaymentStatus) {
      Map<String, Object> map = new HashMap(16);
      map.put("bean", ptPaymentTranHist);
      if (BusiUtil.isNotNull(acctPaymentStatus)) {
         map.put("acctPaymentStatus", acctPaymentStatus);
      }

      this.daoSupport.update(PtPaymentTranHist.class.getName() + ".updateByRefNo", map);
   }

   public PtPaymentTranHist selectByOption(String channelType, String refNo, String ptOperateType) {
      Map<String, Object> param = new HashMap();
      param.put("channelType", channelType);
      param.put("refNo", refNo);
      param.put("ptOperateType", ptOperateType);
      return (PtPaymentTranHist)this.daoSupport.selectOne("selectByOption", param);
   }

   public List<PtPaymentTranHist> getPaymentTranHistCount(Map map) {
      return this.daoSupport.selectList(PtPaymentTranHist.class.getName() + ".getPaymentTranHistCount", map);
   }
}
