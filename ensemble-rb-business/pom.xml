<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dcits</groupId>
        <artifactId>ensemble-rb</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>ensemble-rb-business</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-dbsharding-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-dbsharding-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-dbsharding-galaxy-libra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-boot-autoconfigure-ig</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>dcfs.fts</groupId>
            <artifactId>dcfs-fts-common</artifactId>
        </dependency>
        <dependency>
            <groupId>dcfs.fts</groupId>
            <artifactId>dcfs-fts-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.util</groupId>
            <artifactId>taobao-express</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-rb-limit-online</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-rb-limit-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-rb-limit-business-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-rb-limit-business</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-rb-limit-business</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <!-- Spring Boot Core -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- MyBatis -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <!-- Apache Commons -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>


        <dependency>
            <groupId>com.dcits.libra</groupId>
            <artifactId>libra-client-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.libra</groupId>
            <artifactId>libra-client-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.libra</groupId>
            <artifactId>libra-client-encryption</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.libra</groupId>
            <artifactId>libra-client-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.libra</groupId>
            <artifactId>libra-transaction-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.sonic</groupId>
            <artifactId>sonic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.sonic</groupId>
            <artifactId>sonic-remoting</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.sonic</groupId>
            <artifactId>sonic-remoting-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.sonic</groupId>
            <artifactId>sonic-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.sonic</groupId>
            <artifactId>sonic-sdk-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.sonic</groupId>
            <artifactId>sonic-sdk-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.union</groupId>
            <artifactId>union-all</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-notice-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-notice-core</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-territory-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-territory-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-transaction-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-sequence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-sequence-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-sequence-galaxy-mars</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>ensemble-fm-business-repository</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-fm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-cache-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-cache-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-cache-redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-dao-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-dao-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-ig-stat</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-ig-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-ig-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-file-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-file-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-file-ftp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-flow-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-flow-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-flow-galaxy-gravity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-kit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>gravity-kit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-rpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-rpc-consumer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-rpc-consumer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-rpc-consumer-springcloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-rpc-provider-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-rpc-provider-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits</groupId>
            <artifactId>comet-rpc-provider-springcloud</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.ensemble</groupId>
            <artifactId>ensemble-component-sequence</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-components</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-components-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-luna-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-luna-resources</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-node-javassist</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-oas-endpoint</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-oas-modules</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-spring-web-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.gravity</groupId>
            <artifactId>gravity-web-spring-boot-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dcits.mars</groupId>
            <artifactId>mars-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.mars</groupId>
            <artifactId>mars-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.mars</groupId>
            <artifactId>mars-client-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcits.mars</groupId>
            <artifactId>mars-client-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

    </dependencies>

</project>